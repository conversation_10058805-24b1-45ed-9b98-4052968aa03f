
### backyard db
-- ----------------------------
-- Table structure for hcm_permission
-- ----------------------------
DROP TABLE IF EXISTS `hcm_permission`;
CREATE TABLE `hcm_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `key` varchar(100) DEFAULT NULL COMMENT '权限key',
  `name` varchar(100) DEFAULT NULL COMMENT '权限名称',
  `description` varchar(100) DEFAULT NULL COMMENT '权限描述',
  `t_key` varchar(100) DEFAULT '' COMMENT '翻译key',
  `ancestry` int(11) DEFAULT NULL COMMENT '所属上级ID',
  `is_iframe` tinyint(1) unsigned DEFAULT '0',
  `url` varchar(128) NOT NULL DEFAULT '' COMMENT '路由名称 前端使用',
  `type` tinyint(4) DEFAULT NULL COMMENT '权限类型，1菜单，2操作',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='系统权限';

-- ----------------------------
-- Records of hcm_permission
-- ----------------------------

INSERT INTO `hcm_permission` VALUES (1, 'user.permission.grant', '权限管理', '权限管理', '', 0, 0, '', 1, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (2, 'action.user.permission.grant', '角色管理', '角色管理', '', 1, 0, 'permission/hcm', 1, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (4, 'hrstaff', 'HRIS管理', 'HRIS管理', 'hris_manage', 0, 1, '', 1, 1, '2021-06-24 12:23:29');
INSERT INTO `hcm_permission` VALUES (5, 'hrstaff', '员工班次设置', '员工班次设置', 'staff_shift_setting', 4, 1, '/#/fbi/shuttle', 1, 1, '2021-06-24 12:22:48');
INSERT INTO `hcm_permission` VALUES (6, 'hrstaff', 'Flash员工管理', 'Flash员工管理', 'flash_staff_manage', 4, 1, '/#/fbi/flash', 1, 2, '2021-06-24 12:26:43');
INSERT INTO `hcm_permission` VALUES (7, 'hrstaff', 'Flash网点员工管理', 'Flash网点员工管理', 'flash_store_staff_manage', 4, 1, '/#/fbi/flash_dot', 1, 3, '2021-06-24 12:27:05');
INSERT INTO `hcm_permission` VALUES (8, 'hrstaff', 'Flash实习生员工管理', 'Flash实习生员工管理', 'flash_intern_staff_management', 4, 1, ' /#/fbi/staff/trainee', 1, 4, '2021-06-24 12:27:44');
INSERT INTO `hcm_permission` VALUES (9, 'hrstaff', '外协员工管理', '外协员工管理', 'outsourcing_staff_management', 4, 1, '/#/fbi/flash_out', 1, 5, '2021-06-24 12:28:11');
INSERT INTO `hcm_permission` VALUES (10, 'hrstaff', '合作商员工管理', '合作商员工管理', 'hr_staff_offical_partner', 4, 1, '/#/fbi/cooperative', 1, 6, '2021-06-24 12:28:40');
INSERT INTO `hcm_permission` VALUES (11, 'hrstaff', '部门职位角色管理', '部门职位角色管理', 'departmental_role_management', 4, 1, '/#/flash/conf', 1, 7, '2021-06-24 12:29:04');
INSERT INTO `hcm_permission` VALUES (12, 'hrstaff', 'Flash员工管理(下载)', 'Flash员工管理(下载)', 'flash_staff_management_download', 4, 1, '/#/fbi/flash_download', 1, 8, '2021-06-24 12:29:29');
INSERT INTO `hcm_permission` VALUES (13, 'hrstaff', 'Flash员工操作记录(薪资)', 'Flash员工操作记录(薪资)', 'hr_flash_operate_log_salary', 4, 1, '/#/fbi/logFile_Nowage', 1, 9, '2021-06-24 12:30:04');
INSERT INTO `hcm_permission` VALUES (14, 'hrstaff', '外协员工工作订单', '外协员工工作订单', 'hr_flash_flashout_config', 4, 1, '/#/fbi/flashout_config', 1, 10, '2021-06-24 12:30:33');
INSERT INTO `hcm_permission` VALUES (15, 'hrstaff', 'Flash网点员工薪资结构', 'Flash网点员工薪资结构', 'hr_flash_salary', 4, 1, '/#/fbi/dot_salary', 1, 11, '2021-06-24 12:31:41');
INSERT INTO `hcm_permission` VALUES (16, 'hrstaff', 'Flash总部员工薪资结构', 'Flash总部员工薪资结构', 'hr_flash_salary_headoffice', 4, 1, '/#/fbi/salary', 1, 12, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (17, 'hrstaff', 'Flash员工邮箱管理', 'Flash员工邮箱管理', 'hr_flash_emails', 4, 1, '/#/fbi/mailboxManagement', 1, 13, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (18, 'hrstaff', '招聘进度-按区域', '招聘进度-按区域', 'hr_staff_statistics_area', 4, 0, '/hrstaff/statistics_area', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (19, 'hrstaff', '招聘进度-部门', '招聘进度-部门', 'recruitment_progress_department', 4, 0, '/hrstaff/statistics_branch', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (20, 'hrstaff', '招聘进度-网点', '招聘进度-网点', 'recruitment_progress_store', 4, 0, '/hrstaff/statistics_store', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (21, 'hrstaff', 'Flash员工资产管理', 'Flash员工资产管理', 'flash_staff_assets_management', 4, 0, '/hrstaff/assetsmanager', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (22, 'hrstaff', 'Flash资产管理', 'Flash资产管理', 'flash_asset_management', 4, 0, '/hrstaff/assetmanager', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (23, 'hrstaff', '异常资产报表', '异常资产报表', 'abnormal_assets', 4, 0, '/hrstaff/abnormalassets', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (24, 'hrstaff', 'Flash员工购买记录', 'Flash员工购买记录', 'flash-purchase-records', 4, 0, '/hrstaff/interiorOrder', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (25, 'hrstaff', '网点包材管理', '网点包材管理', 'store_material_management', 4, 0, '/hrstaff/storeMaterialManagement', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (26, 'hrstaff', 'Flash员工离职管理', 'Flash员工离职管理', 'flash_staff_leave_assets_management', 4, 0, '/hrstaff/leaveassetsmanager', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (27, 'hrstaff', '资产清单管理', '资产清单管理', 'asset_list_management', 4, 0, '/hrstaff/assetslog', 0, 1, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (28, 'hrstaff', '可选班次维护', '可选班次维护', 'hr_shift_config', 4, 1, '/#/fbi/shuttle_maintain', 1, 14, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (29, 'hrstaff', 'Flash 员工操作记录', 'Flash 员工操作记录', 'flash_staff_operation_record', 4, 1, '/#/fbi/log_file', 1, 15, '2020-01-06 08:45:51');
INSERT INTO `hcm_permission` VALUES (31, 'menu.admin.settingenv.main', '设置中心', NULL, '', 0, 0, '', 1, NULL, '2021-06-28 03:57:31');
INSERT INTO `hcm_permission` VALUES (32, 'menu.admin.settingenv.list', '系统设置', NULL, '', 31, 0, 'sys/center-list', 1, NULL, '2021-06-30 02:46:50');
INSERT INTO `hcm_permission` VALUES (33, 'action.admin.settingenv.list', '设置中心列表', NULL, '', 32, 0, 'sys/center-list', 2, NULL, '2021-06-28 03:59:49');
INSERT INTO `hcm_permission` VALUES (34, 'action.admin.settingenv.save', '设置中心保存', NULL, '', 32, 0, '', 2, NULL, '2021-06-28 03:57:06');


CREATE TABLE `hcm_staff_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `permission_ids` varchar(2000) DEFAULT NULL COMMENT '用户权限列表',
  `is_granted` tinyint(4) DEFAULT NULL,
  `last_updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hcm用户权限表';