
### backyard db ###

CREATE TABLE `hcm_excel_task` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `staff_info_id` varchar(30) NOT NULL DEFAULT '' COMMENT '员工工号',
  `executable_path` varchar(200) NOT NULL DEFAULT '' COMMENT '执行路径',
  `action_name` varchar(255) DEFAULT NULL COMMENT '任务url，存 php  cli.php  之后的 main   action  ，字符串以逗号隔开',
  `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名',
  `path` varchar(255) DEFAULT NULL COMMENT '下载相对地址',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0:待处理 1:执行完成',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '1:hris导出',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `finish_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `args_json` text,
  PRIMARY KEY (`id`),
  KEY `idx_staff_info_id` (`staff_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1 COMMENT='HCM后台执行excel下载任务';