window.lang={"lang":"English","account":"Account ID","password":"Password","login":"Login","login_user":"Please input account","login_pwd":"Please input password","login_all":"Incorrect account ID or password","no_server":"The server is abnormal. Please try again later\u2026","dot":"Branch","details":"Details","this_week":"This Week ","last_week":"Last Week ","this_month":"This Month ","last_month":"Last Month ","date":"Date","time":"Time","houre":"Houre","operating_report":"Operation report","sales_rankings":"Sales report","already_opened":"Opened","not_open":"In process","unknown":"Null","personal":"Personal","enterprise":"Business","piece":"Piece","info":" Details","example":"example: 30 hours 56 mins will show as 30:56 in this report","500":"\u0e23\u0e2b\u0e31\u0e2a\u0e22\u0e37\u0e19\u0e22\u0e31\u0e19 {|code|} \u0e04\u0e38\u0e13\u0e01\u0e33\u0e25\u0e31\u0e07\u0e41\u0e01\u0e49\u0e44\u0e02\u0e02\u0e49\u0e2d\u0e21\u0e39\u0e25\u0e2a\u0e48\u0e27\u0e19\u0e15\u0e31\u0e27 \u0e2b\u0e32\u0e01\u0e04\u0e38\u0e13\u0e44\u0e21\u0e48\u0e44\u0e14\u0e49\u0e40\u0e1b\u0e47\u0e19\u0e1c\u0e39\u0e49\u0e14\u0e33\u0e40\u0e19\u0e34\u0e19\u0e01\u0e32\u0e23 \u0e42\u0e1b\u0e23\u0e14\u0e23\u0e30\u0e27\u0e31\u0e07\u0e04\u0e27\u0e32\u0e21\u0e1b\u0e25\u0e2d\u0e14\u0e20\u0e31\u0e22\u0e02\u0e2d\u0e07\u0e1a\u0e31\u0e0d\u0e0a\u0e35\u0e04\u0e38\u0e13","1001":"This employee does not exist!","1002":"There is no missing attendance need to make up for the selected time!","1003":"There is no missing attendance need to make up for the selected time!","1004":"There is a make-up request for the selected time already\uff0cplease do not submit repeatedly!!","1005":"There is a make-up request for the selected time already\uff0cplease do not submit repeatedly!!","1006":"Make-up failure!","1007":"You can only select dates within 3 days","1008":"You cannot submit the make-up request on the selected time!","1009":"Failed to submit leave!","1010":"The start time must be earlier than or equal to the end time!","1011":"There is a make-up request for A1:E87 selected time already\uff0cplease do not submit repeatedly!","1012":"The selected time has a leave record, please do not repeat the submission!!","1014":"Approval failure!","1015":"There is no such approval!","1016":"This order has been approved and cannot be approved again!","1017":"Sorry, you have no permission to audit!","1018":"Leave date must be at least the current date!","1019":"Enter a maximum of 500 words!","1020":"Enter a maximum of 500 words!","1021":"You cannot submit the make-up request on the selected time!","1022":"You cannot submit the make-up request on the selected time!","1023":"Please select start time","1024":"Please select end time","1025":"You can only apply to make up three times a month and this month the limit has been exceeded!","1026":"The sick leave optional date is nearly three days or greater than the current date!","1027":"Please upload your doctor's diagnosis!","1028":"Tips\uff1aYou still have |num| chances","1029":"Not a chance! If you need to continue, please report it to the department manager for approval","1030":"Reason for revocation should be between 1-500 characters","leave_for_five":"You can apply day off just one day a week","leave_for_half":"This type of leave can not be applied for 0.5 days, please apply for 1 day","rest_day":"The request date is already a weekly holiday. Can not request again.","ph_day":"Cannot apply for rest days during PH holidays","wk_flow_error":"Approvers are missing, please contact the HRIS","2001":"Punch in","2002":"Punch out","2003":"Annual leave","2004":"Personal leave(Paid)","2005":"Sick leave","2006":"Maternity leave","2007":"Paternity leave","2008":"National military training holiday","2009":"Funeral Leave","2010":"Leave for sterilization","2011":"Personal Trained Leave","2012":"Marriage leave","2013":"Pabbajja leave","2014":"Personal leave(Unpaid)","2015":"Days off","2016":"Other ","2017":"Rest day","2018":"Flash Trained Leave","2020":"antenatal care","2019":"Rest day","2021":"sick leave","2022":"International family leave","paid":"paid","un_paid":"Unpaid","leave_limit":"Insufficient holiday balance","leave_7":"The application should be applied within 7 days","leave_7_img":"please upload relevant proof","2101":"Please select the date","2102":"Please select the start time","2103":"Please enter the license plate number","2104":"Start time format is incorrect","2105":"License plate number format error","2106":"Image cannot be empty","2107":"You don't have permission!","2108":"You have already submitted an LH fee application today, please do not submit it again!!","2109":"Application failed!","2110":"LH starts at 18pm and before 9am!","advice":"Advice","salary":"Salary","incentive":"Incentive","complaint":"Complaint","other":"Other","string_len":"Enter only 10-2000 characters","already_reply":"Replied","wait_reply":"Wait for reply","has_read":"read","un_read":"Unread","miss_args":"missing required parameters","select_type":"Please select the problem type","3001":"The post name is not empty and is within 50 characters","3002":"Job description is not empty","3003":"The department is not empty","3004":"Reporting line cannot be empty","3005":"Repeat the post name, please re-add","3006":"Post addition failed","3007":"Incorrect position","3008":"No such position exists","3009":"There is no such department","3010":"Position cannot be empty","3011":"Reporting line is incorrect","3012":"Submitter name cannot be empty","3013":"The JD associated with HC can not be deleted","4001":"Approval","4002":"Recruitment","4003":"Completed","4004":"Obsoleted","4005":"Refused","4006":"Communicated","4007":"Not Communicating","4008":"Error","4009":"Insufficient permissions","4010":"There is no such branch!","4011":"There are * people in the interview, please contact the recruiter to finish the interview process.","4012":"The status has changed, please refresh the page","4013":"Insufficient permissions","4014":"The reason should be within 300 characters","4015":"Pending","4016":"Application","4017":"Agreed","4018":"Insufficient permissions","4019":"Work branch cannot be empty","4020":"The demand has to be greater than 0 and less than 999","4101":"Creation failed!","4102":"fail to edit!","4103":"failed to delete!","4104":"Query failed!","4105":"No such job!","4106":"No such entry!","4107":"The resume has been uploaded to the resume library, please do not upload it again!","4108":"Please fill in your resume!","4109":"Wrong email entry!","4110":"Wrong id card input!","4111":"Graduation school is entered error!","4112":"Major input error!","4113":"Wrong entry of work experience!","4114":"Expected salary entry error!","4115":"Wrong name entry!","4116":"Gender typing error!","4117":"Wrong number entry!","4118":"Expected working place input error!","4119":"Expected position entry error!","4120":"Input personnel error!","4121":"The maximum number of deletions at one time is","4201":"Not filled","4202":"Filled","4203":"Terminated","4204":"expired","4301":"Did not contact the interview candidate","4302":"The interview candidate has something to come","4303":"Candidate has found a new job","4304":"HC application needs to be cancelled","4305":"Jobs do not match after communication","4306":"other","4401":"The interviewer can't be empty!","4402":"The interview time cannot be empty!","4403":"The state cannot be empty","4404":"The state cannot be empty","4405":"Provinces cannot be empty","4406":"Provinces cannot be empty","4407":"Cities cannot be empty","4408":"Cities cannot be empty","4409":"The region cannot be empty","4410":"The region cannot be empty","4411":"The detailed address cannot be empty","4412":"HC cannot be empty","4413":"Your resume should not be empty","4414":"HC is not correct","4415":"An appointment interview does not exist","4416":"Failed to add interview appointment","4417":"An appointment interview already exists. You cannot add an interview","4418":"The reservation ID cannot be empty!","4419":"Cancel the failure","4420":"The interview information cannot be empty","4421":"The evaluation content is too long","4422":"Wrong choice of currency","4423":"Suggested salary is incorrect","4424":"This offer has been sent. Do not send it again","4425":"Sorry,this position is filled, offer sent failed ","4426":"The interviewer has been admitted","4501":"Suggested salary is incorrect","4502":"Feedback information","4601":"create","4602":"Modify the","4603":"delete","4603_1":"View","4604":"In","4605":"","4606":"cancel","4701":"Send failure","4702":"Available time is not available","4703":"Feedback failed!","4801":"Not communicating","4802":"Waiting for an interview","4803":"Interviewing","4804":"Pending Offer","4805":"Offered","4806":"rejected","4807":"Cancelled","4808":"Deleted","4809":"Already employed","4900":"male","4901":"Female","4902":"other","4903":"Already employed","4904":"Waiting for entry","5001":"Submitted successfully","5002":"Submitted successfully","5101":"End time must not be less than start time","5102":"Repeat selection","5103":"Only about 3 days","5104":"Approval Pending","5105":"Rejected","5106":"Agree","cancel":"Cancelled","closed":"over time and closed","5107":"Weekdays OT","5108":"Rest&PH attendance","5109":"Working nights\uff1aTime can only be selected after 19:00, before 8:00 the next day","5110":"Up to 1000 characters","5111":"Rest&PH OT","5112":"reason of revocation","6001":"has submitted the application for ","6002":"please examine and approve","6003":"Attendance Amending","6004":"Ask for Leave","6005":"Apply for LH fee","6006":"Backyard \u0e02\u0e49\u0e2d\u0e04\u0e27\u0e32\u0e21\u0e15\u0e23\u0e27\u0e08\u0e2a\u0e2d\u0e1a","6007":"You have 1 approval messages.","6008":"You have","6009":"bar unread message","6010":"OT","6011":"temporary vehicle","6012":"\u7533\u8bf7\u8fd0\u8d39\u6298\u6263","5200":"Application date is limited to: April 16th to April 24th","5204":"Application date is limited to: April 16th to May 10th","5205":"Application date is limited to: May 16th to June 15th","5206":"The LH time you apply for must be between 21:00 and 6:00 the next day","5201":"Cannot apply for LH and OT at the same time on the same date","5202":"Do not submit again","6200":"year","6201":"month","6202":"salary","6100":"Interview","6101":"offer","6102":"On-boarding","6103":"Interview feedback","6104":"go to next step","6105":"Not Pass","6106":"Interview Pass","6107":"Modify content","6108":"create","6109":"Modify the","6110":"delete","6111":"cancel","6112":"View","6113":"reservation","6114":"fill in","6115":"send","6116":"Handle","6117":"Feedback","6118":"Resume","7100":"user is not staff of the branch","7101":"the number is not correct","7102":"reason for application should be 10-500 words","7103":"material can not be the same","7104":"You can apply only once a week ","7105":"\u0e04\u0e38\u0e13\u0e43\u0e0a\u0e49\u0e2a\u0e34\u0e17\u0e18\u0e34\u0e4c\u0e43\u0e19\u0e01\u0e32\u0e23\u0e40\u0e1a\u0e34\u0e01\u0e2d\u0e38\u0e1b\u0e01\u0e23\u0e13\u0e4c\u0e2b\u0e21\u0e14\u0e41\u0e25\u0e49\u0e27 \u0e23\u0e1a\u0e01\u0e27\u0e19\u0e15\u0e34\u0e14\u0e15\u0e48\u0e2d Admin \u0e2a\u0e33\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e43\u0e2b\u0e0d\u0e48\u0e17\u0e35\u0e48\u0e14\u0e39\u0e41\u0e25\u0e1e\u0e37\u0e49\u0e19\u0e17\u0e35\u0e48\u0e02\u0e2d\u0e07\u0e17\u0e48\u0e32\u0e19","7111":"airport","7112":"train","7113":"car","7114":"other","7115":"single trip","7116":"round trip","7120":"please fill in the reason for business trip","7121":"please fill in the city ","7122":"please choose","7123":"10-500 words","7124":"please complete the information","7125":"Departure time should earlier than ending time","7126":"remark should between 10-500 words","7127":"reson should between 10-50 words","7128":"please reenter the city","reason_application_type_1":"Yellow license plate registration","reason_application_type_2":"Get driver's license","7130":"Please enter the correct license plate number","7131":"Cannot choose future dates","7132":"Please enter 16-20 digit fuel card number","7133":"\u0e1b\u0e15\u0e17. Oil is 16 digit card number, please enter again","7134":"Please upload pictures of vehicles","7135":"Please upload photos on the personal information page of your license","7136":" You can make up nearly 3 days' mileage only","7137":"{} oppotunity for making up mileage left","7138":"The opportunity for making up mileage has run out!\uff01","7139":"Off-work mileage cannot be less than on-work mileage","7140":"Making up the mileage cannot exceed 50KM a day","7141":"DIESEL B7","7142":"DIESEL B10","7146":"New mileage must be greater than or equal to last mileage","7147":"There are new miles after the currently selected time. The current mileage cannot be greater than the new mileage","2201":"No data\uff01","2202":"You can only cancel your application\uff01","2204":"There is no information about the employee's supervisor\uff01","2205":"Only the direct superior can undo\uff01","2206":"The current application cannot be cancelled","2207":"After 20th, you cannot cancel the application before 15th of this month","7001":"make\u00a0up","7002":"leave","7003":"apply\u00a0for\u00a0LH\u00a0fee","7004":"approval\u00a0OT","7005":"apply for materials","7006":"apply for business trip","7007":"apply for HC","7008":"Vehicle mileage","7009":"Temporary vehicle application","7010":"apply for resignation","7011":"Apply by outsourcing staff","7013":"apply asset(personal)","7015":"Request report","7016":"Apply for transfer","7017":"apply asset(public)","7018":"Apply for transfer","7021":"Marketing product application","7022":"salary approval","7023":"Business trip Punch-in approval","7024":"Claim amount application","7025":"Shop department application marketing product page","7026":"Sales\/PMD department application marketing product page","7033":"Message sending application","7028":"Yellow license plate business trip","7027":"Car application","organization_id":"Application Network","operator_id":"claimant","approval_at":"Application time","claims_amount":"Amount requested","client_mobile":"Customer Mobile Number","pno_no":"waybill number","customer_type_category":"Customer type","customer_type_category_0":"unknown","customer_type_category_1":"general user","customer_type_category_2":"Signed user","customer_type_category_3":"Franchisee","diff_marker_category":"Parcel problems","diff_marker_category_20":"Damaged cargo","diff_marker_category_21":"Cargo shortage","diff_marker_category_22":"cargo lost","instructions_image_list":"Annexes","1101":"The selected time does not match the actual clocking time, so you cannot apply for OT","overtime_store_allowed":"The OT time of branch staff shall not exceed 4h!","overtime_department_allowed":"The OT time of header office staff shall not exceed 8h!","overtime_limit":"According to your attendance time of the day, the OT duration is insufficient {selected duration}, please choose again!","overtime_act_last":"The actual OT time cannot be less than 2h!","overtime_attendance_limit_4":"If your attendance is less than 4 hours, you cannot apply for OT!","overtime_attendance_limit_6":"If your attendance is less than 6 hours, you cannot apply for OT!","overtime_attendance_limit_11":"If your attendance is less than 11 hours, you cannot apply for OT!","overtime_work_limit_4":"Due to less than 4 hours of attendance on that day, you cannot apply for holiday OT","overtime_invalid":"Since there is no matching attendance record, you will not receive OT fee","overtime_five_days":"You can choose only today, the last 2 days, or the next 2 days","overtime_two_days":"You can only apply for 1x OT on a rest day or 2 days before a holiday","overtime_one_days_1":"1.5 times OT can only be applied on the working day","overtime_one_days_2":"3 times OT can only be applied on the day of a holiday\/holidays ","overtime_weekend_forbidden":"Cannot apply for 1.5 times OT on holidays\/holidays","overtime_forbidden":"Cannot apply for OT at the current start time ","overtime_att_start":"Please sign up for work card first","overtime_leave_limit":"There is a leave record on the day, and no overtime application is allowed ","overtime_24":"Daily attendance time cannot exceed 24h","overtime_bi_notice":"This application can not be modified due to not verified yet.","repair_same_day":"Punch time and attendance date must be the same day","repair_early":"Punch in time must be earlier than the punch out time","repair_late":"Punch out time must be later than the punch in time","repair_tomorrow":"Punch out time must be earlier than the punch in the next day","repair_limit_16":"Make up for the punch out attendance must not over 22 hours after  punching in","repair_yesterday":"Punch in time shall not be earlier than punch out time the previous day","leave_16_notice":"You can choose only today, the last 8 days, or the next 2 days","leave_over_limit":"The maximum training leave is 8 days","leave_18_notice":"You can only apply within 7 days ago or apply in advance.","leave_18_over_year":"You can leave until December 31st, If you need to extend leave, please submit in the next year.","leave_2_notice":"The days left of sick leave is {left} days, if there's no remains you can apply unpaid sick leave.","over_7_days":"Applications over 5 days are automatically lapsed and cannot be approved","probation_limit":"Employees on probation can only apply for leave_type","limit_7":"You can edit data within 7 days","wrong_date":"please choose correct date","overtime_weekend":"date selected is not weekend or holidays","attendance_img_verify":"you can try again.\\n After many failed attempts, you can consult email:\\n <EMAIL>,\\n line: @flashsystem\\n to check whether the negatives uploaded are unqualified.","attendance_date":"date\u00a0of\u00a0attendance","reissue_card_time":"make-up\u00a0time","attendance_type":"type\u00a0of\u00a0making\u00a0up","audit_reason":"reason\u00a0for\u00a0absence","leave_type":"type\u00a0of\u00a0leave","start_time":"start\u00a0time","end_time":"end\u00a0time","days":"days","audit_leave_reason":"reason","photo":"photo","plate_number":"Plate No.","remark":"remark","OT_date":"date\u00a0of\u00a0over time","OT_type":"type\u00a0of\u00a0over time","duration":"duration","OT_reason":"type\u00a0of\u00a0over time","send_request":"apply","goods_name":"name of material","num":"number","employ_user":"user","reason_application":"reason for application","approver_is_not_exist":"status of approver is abnormal\uff0c please wait a moment and then try again.","traffic_tools":"transportation","oneway_or_roundtrip":"single or round trip","departure_city":"place of departure","destination_city":"destination","days_num":"days","selectJD":"selectJD","department":"department","store":"branch","expirationdate":"deadline","demandnumber":"HC in demand","other_requirement":"other requirement","hcreason":"reason of recruitment","hc_reason_type_1":"recruitment","hc_reason_type_2":"transfer","hc_reason_type_3":"Departure","has_wait":"You have waited","hours":"hours","minute":"minutes","please try again":"Please refresh or try again","1_times_salary":"1 times salary","1.5_times_salary":"1.5 times salary","3_times_salary":"3 times salary","could_change":"change for day off","branch_repertory":"remaining stock","branch_staff_number":"branch staff number","deliveryCount":"number of deliverin","pickupCount":"number of collecting","material_requisition_record":"material requisition record","scm_current_inventory":"SCM current inventory","mileage_date":"Date","start_kilometres":"On-work mileage","started_img":"On-work mileage photo","end_kilometres":"Off-work mileage","end_img":"Off-work mileage phone","car_type":"Vehicle Type","capacity":"Carrying capacity","arrive_time":"Expected arrival time","start_store":"Departure site","end_store":"Destination site","region":"Region","live_photo":"Photo","time_table":"Timetable","return_back_time_table":"Return Timetable","serial_number":"No.","site":"Site","planned_arrive":"Planned arrival time","planned_departure":"Planned departure time","reject_reason1":"Dismiss reason","reject_reason":"Reason for rejection","cancel_reason":"reason of revocation","you_submit":"Your application for ","please_check":" is processed, please check.","fleet_time_need_ge_current":"Expected arrival time should be later than current time.","vehicle_department":"LH Central control department","fleet_depture_can_no_same":"Departure branch can not be the same with the arrival branch.","company":"Supplier name","driver_name":"Driver's name","driver_phone":"Driver's phone number","ordinary_car":"Application type","fd_courier_car":"FD Courier","fd_driver_id":"Driver ID","app_type":"Application type","err_msg_driver_not_exist":"Driver ID does not exist in the system, please re-enter","err_msg_enter_driver_name":"Please enter driver\u2018s name","err_msg_enter_driver_phone":"Please enter driver\u2018s phone number","err_msg_enter_driver_plate":"Please enter driver\u2018s number plate","err_msg_wrong_job_title":"Driver must be Van Courier","invalid_for_apply":"remaining time\uff1a{hour}hours","sim_card":"sim card","huawei_mobile_7pro":"Huawei Y7 Pro","idata_mobile":"mobile phone IDATA","TSH_mobile":"mobile phone True Super Hero","bumbag":"waist bag","bluetooth_ptinter":"printer PTP","helmet":"safe hat","net_pocket":"net pocket","scales":"hook scale","raincoat":"rain coat","motorcycle_bag":"motocycle bag","motorcycle_boot":"motocycle box","jacket":"jacket","trousers":"trousers","t-shirt":"polo T-shirt","staff_card":"staff card","fuel_card":"fuel card","laptop":"laptop","pc":"PC","vehicle_sticker":"vehicle sticker","frame_box":"motorcycle iron frame box","none":"none","hire_date":"Employment Date","last_work_date":"Last working day","leave_date":"resignation date","work_handover":"job handover","goods_handover":"return materials","leave_reason":"Reasons for resignation","leave_remark":"remarks for resignation","not_returned_hro":"unreturned materials\uff08HRO\uff09","not_returned_stock":"unreturned materials (Purchaser)","arrears":"unreturned amount of money","staff_sign":"signature","resign_reason_1":"Personal reason","resign_reason_2":"Enterprise expulsion","resign_reason_3":"Not adapted to corporate culture","resign_reason_4":"Low salary","resign_reason_5":"Lack of space for development","resign_reason_6":"Incompatibility with superiors\/subordinate","resign_reason_7":"Incompatibility with colleagues","resign_reason_8":"Workplace environment","resign_reason_9":"Job description does not meet the requirements","resign_reason_10":"Not convenient to work according to company working days","resign_reason_11":"Heavy workload","resign_reason_12":"Poor welfare","resign_reason_13":"Family Factors","resign_reason_14":"Do personal business","resign_reason_15":"Traffic factor","resign_reason_16":"Health problems","resign_reason_17":"Continue study","resign_reason_99":"Other","err_msg_has_leave":"resigned staff can not apply","err_msg_should_on_hire":"the staff should be on the job","err_msg_50_500":"remarks for resignation should between 20 to 500 words.","err_msg_unvalid_leave_time":"date of resignation should be later than onboard date","auth_code_error":"auth code error","err_msg_no_self":"handover person cannot be staff himself.","highest":"Highest","high":"High","medium":"Medium","low":"Low","recruit_status":"Recruitment status","has_recruited":"Number of the Recruited","recruit_list":"List of the Recruited","audit_type":"Approval type","audit_no":"Approval number","submit_time":"Submission time","recruit_priority":"Recruitment Priority","You_have_a_new_form_not_submitted_please_submit_it_first":"You have a new form not submitted, please submit it first ","You_can,t_close_the_message_until_you_submit_the_form":"You can't close the message until you submit the form","You_have_a_new_message_that_is_not_read_please_read_the_clock_again":"You have a new message that is not read, please read the clock again","closed_after_reading":"closed after reading","4031":"Not effective","4032":"Recruiting","4033":"Filled","4034":"Abolished","4035":"Expired","shift_early":"Morning shift","shift_middle":"Afternoon shift","shift_night":"Night shift","os_staff_job_title":"Outsourcing position","employment_date":"Employment date","employment_days":"Days of employment","work_shift":"Work shift","demend_num":"Number of application","reason_app":"Reason of application","err_msg_invalid_date":"\"Employment date\" invalid input","last_4_days_parcel":"Workload in the past 4 days","parcel_count":"Number to be dispatched","parcel_avg":"Warehouse keeper efficacy","final_num":"Number of approval","undertake_count":"Pickup volume","parcel_per":"Per capita delivered","warning_notice":"please click link to sign name","ot_time_limit":"Start time can not later than 12\uff1a00 of the next day.","audit_store_prohibit":"You can not make up for attendance record, please remember to scan face when arrive or leave.","status":"status","staff_assigned":"Number of allocated","staff_list":"Personnel List","warning_more_3days":"The employees who received the warning has not signed for confirmation for more than 3 days, which requires superior communication and reading to the employee warning content.","abort_list_royalty":"Promotion incentive","8000":"You don't have permission","8001":"This month's Commission","abort_list_myinfo":"Personal information","abort_list_asset":"assets of branch","abort_list_myasset":"personal assets","abort_list_pubasset":"public assets","abort_list_sop":"Regulations&SOP","abort_list_mailbox":"Flash Box","abort_list_shareCenter":"Information Sharing Centre","abort_list_about":"About","abort_list_qa":"FAQ","abort_list_storeinfo":"Branch Info","abort_list_emergency":"Emergency hotline","jd_name":"JD\u540d\u79f0","basic_salary":"\u57fa\u672c\u5de5\u8d44","salary_title_1":"Salary details","salary_title_2":"\u5de5\u8d44\u660e\u7ec6","remittanceDialogMsg_1":"\u0e04\u0e38\u0e13\u0e22\u0e31\u0e07\u0e44\u0e21\u0e48\u0e19\u0e33\u0e2a\u0e48\u0e07\u0e04\u0e48\u0e32\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e43\u0e2b\u0e49\u0e01\u0e31\u0e1a\u0e1a\u0e23\u0e34\u0e29\u0e31\u0e17 \u0e22\u0e2d\u0e14\u0e40\u0e07\u0e34\u0e19\u0e04\u0e37\u0e2d {receivable_amount} \u0e3f \u0e42\u0e1b\u0e23\u0e14\u0e23\u0e35\u0e1a\u0e19\u0e33\u0e2a\u0e48\u0e07\u0e04\u0e48\u0e32\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23 \u0e21\u0e34\u0e40\u0e0a\u0e48\u0e19\u0e19\u0e31\u0e49\u0e19\u0e27\u0e31\u0e19\u0e16\u0e31\u0e14\u0e44\u0e1b\u0e23\u0e30\u0e1a\u0e1a\u0e08\u0e30\u0e1b\u0e23\u0e31\u0e1a\u0e40\u0e1b\u0e47\u0e19\u0e1e\u0e31\u0e01\u0e07\u0e32\u0e19\u0e2d\u0e31\u0e15\u0e42\u0e19\u0e21\u0e31\u0e15\u0e34 \u0e2d\u0e35\u0e01\u0e17\u0e31\u0e49\u0e07\u0e22\u0e31\u0e07\u0e21\u0e35\u0e04\u0e27\u0e32\u0e21\u0e40\u0e2a\u0e35\u0e48\u0e22\u0e07\u0e16\u0e39\u0e01\u0e44\u0e25\u0e48\u0e2d\u0e2d\u0e01","remittanceDialogMsg_2":"{\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e04\u0e39\u0e40\u0e23\u0e35\u0e22\u0e23\u0e4c\u0e04\u0e49\u0e32\u0e07\u0e0a\u0e33\u0e23\u0e30: {receivable_amount}\u0e1a\u0e32\u0e17}\u0e42\u0e1b\u0e23\u0e14\u0e41\u0e08\u0e49\u0e07\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e04\u0e39\u0e40\u0e23\u0e35\u0e22\u0e23\u0e4c\u0e0a\u0e33\u0e23\u0e30\u0e15\u0e32\u0e21\u0e40\u0e27\u0e25\u0e32\u0e17\u0e35\u0e48\u0e01\u0e33\u0e2b\u0e19\u0e14 \u0e2b\u0e32\u0e01\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e44\u0e21\u0e48\u0e0a\u0e33\u0e23\u0e30\u0e20\u0e32\u0e22\u0e43\u0e193\u0e27\u0e31\u0e19\u0e08\u0e30\u0e21\u0e35\u0e04\u0e27\u0e32\u0e21\u0e40\u0e2a\u0e35\u0e48\u0e22\u0e07\u0e17\u0e35\u0e48\u0e08\u0e30\u0e16\u0e39\u0e01\u0e1e\u0e31\u0e01\u0e07\u0e32\u0e19\u0e2b\u0e23\u0e37\u0e2d\u0e1e\u0e49\u0e19\u0e2a\u0e20\u0e32\u0e1e\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19","remittanceDialogMsg_3":"{\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e04\u0e39\u0e40\u0e23\u0e35\u0e22\u0e23\u0e4c\u0e17\u0e35\u0e48\u0e04\u0e49\u0e32\u0e07\u0e0a\u0e33\u0e23\u0e30:  {receivable_amount} \u0e1a\u0e32\u0e17 \u0e22\u0e2d\u0e14\u0e17\u0e35\u0e48\u0e04\u0e27\u0e23\u0e42\u0e2d\u0e19\u0e40\u0e07\u0e34\u0e19\u0e44\u0e1b\u0e22\u0e31\u0e07\u0e2a\u0e33\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e43\u0e2b\u0e0d\u0e48:  {amount} \u0e1a\u0e32\u0e17}\u0e42\u0e1b\u0e23\u0e14\u0e42\u0e2d\u0e19\u0e15\u0e32\u0e21\u0e40\u0e27\u0e25\u0e32\u0e17\u0e35\u0e48\u0e01\u0e33\u0e2b\u0e19\u0e14 \u0e2b\u0e32\u0e01\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e44\u0e21\u0e48\u0e42\u0e2d\u0e19\u0e20\u0e32\u0e22\u0e43\u0e193\u0e27\u0e31\u0e19\u0e08\u0e30\u0e21\u0e35\u0e04\u0e27\u0e32\u0e21\u0e40\u0e2a\u0e35\u0e48\u0e22\u0e07\u0e17\u0e35\u0e48\u0e08\u0e30\u0e16\u0e39\u0e01\u0e1e\u0e31\u0e01\u0e07\u0e32\u0e19\u0e2b\u0e23\u0e37\u0e2d\u0e1e\u0e49\u0e19\u0e2a\u0e20\u0e32\u0e1e\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19","remittanceDialogMsg_4":"\u0e08\u0e19\u0e16\u0e36\u0e07\u0e15\u0e2d\u0e19\u0e19\u0e35\u0e49 \u0e21\u0e35\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e14\u0e31\u0e07\u0e15\u0e48\u0e2d\u0e44\u0e1b\u0e19\u0e35\u0e49\u0e22\u0e31\u0e07\u0e44\u0e21\u0e48\u0e19\u0e33\u0e2a\u0e48\u0e07\u0e04\u0e48\u0e32\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\n    {staffamount}\n\u0e42\u0e1b\u0e23\u0e14\u0e40\u0e23\u0e48\u0e07\u0e43\u0e2b\u0e49\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e19\u0e33\u0e2a\u0e48\u0e07\u0e04\u0e48\u0e32\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e20\u0e32\u0e22\u0e43\u0e19\u0e27\u0e31\u0e19\u0e19\u0e35\u0e49 \u0e21\u0e34\u0e40\u0e0a\u0e48\u0e19\u0e19\u0e31\u0e49\u0e19\u0e27\u0e31\u0e19\u0e16\u0e31\u0e14\u0e44\u0e1b\u0e23\u0e30\u0e1a\u0e1a\u0e08\u0e30\u0e1b\u0e23\u0e31\u0e1a\u0e40\u0e1b\u0e47\u0e19 \"\u0e1e\u0e31\u0e01\u0e07\u0e32\u0e19\" \u0e2d\u0e31\u0e15\u0e42\u0e19\u0e21\u0e31\u0e15\u0e34","remittanceDialogMsg_5":"\u0e40\u0e15\u0e37\u0e2d\u0e19\u0e04\u0e23\u0e31\u0e49\u0e07\u0e2a\u0e38\u0e14\u0e17\u0e49\u0e32\u0e22\n    \u0e42\u0e1b\u0e23\u0e14\u0e40\u0e15\u0e37\u0e2d\u0e19\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e14\u0e31\u0e07\u0e15\u0e48\u0e2d\u0e44\u0e1b\u0e19\u0e35\u0e49\u0e43\u0e2b\u0e49\u0e19\u0e33\u0e2a\u0e48\u0e07\u0e04\u0e48\u0e32\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\n    {staffids}\n       \u0e15\u0e2d\u0e19\u0e19\u0e35\u0e49\u0e23\u0e30\u0e1a\u0e1a\u0e44\u0e14\u0e49\u0e1b\u0e23\u0e31\u0e1a\u0e2a\u0e16\u0e32\u0e19\u0e30\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e40\u0e1b\u0e47\u0e19\u0e1e\u0e31\u0e01\u0e07\u0e32\u0e19\u0e42\u0e14\u0e22\u0e2d\u0e31\u0e15\u0e42\u0e19\u0e21\u0e31\u0e15\u0e34 \u0e2b\u0e32\u0e01\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e19\u0e33\u0e2a\u0e48\u0e07\u0e04\u0e48\u0e32\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e41\u0e25\u0e49\u0e27 \u0e23\u0e1a\u0e01\u0e27\u0e19\u0e41\u0e19\u0e1a\u0e2b\u0e25\u0e31\u0e01\u0e10\u0e32\u0e19 \u0e41\u0e25\u0e49\u0e27\u0e2a\u0e48\u0e07\u0e2d\u0e35\u0e40\u0e21\u0e25\u0e44\u0e1b\u0e17\u0e35\<EMAIL> \u0e40\u0e1e\u0e37\u0e48\u0e2d\u0e41\u0e01\u0e49\u0e44\u0e02\u0e2a\u0e16\u0e32\u0e19\u0e30\u0e1e\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19","remittanceDialogMsg_6":"You have {receivable_count} record of owing money from company, the amount of  is {receivable_amount} B. If fail to pay back within 3 days, you will be facing the risk of suspending or dismission.","remittanceDialogMsg_7":"You have {receivable_count} record of owing money from company, the amount of  is {receivable_amount} B. If fail to pay back within 3 days, you will be facing the risk of suspending or dismission.\n    You cannot punch out until pay back. ","inventory_asset_1":"\u0e08\u0e2d\u0e20\u0e32\u0e1e LED","inventory_asset_2":"\u0e42\u0e17\u0e23\u0e28\u0e31\u0e1e\u0e17\u0e4c\u0e0b\u0e31\u0e21\u0e0b\u0e38\u0e07\u0e2e\u0e35\u0e42\u0e23\u0e48","inventory_asset_3":"\u0e15\u0e30\u0e41\u0e01\u0e23\u0e07\u0e40\u0e2b\u0e25\u0e47\u0e01\u0e17\u0e23\u0e07\u0e2a\u0e39\u0e07","inventory_asset_4":"\u0e1a\u0e2d\u0e23\u0e4c\u0e14\u0e15\u0e34\u0e14\u0e1b\u0e23\u0e30\u0e01\u0e32\u0e28","inventory_asset_5":"\u0e15\u0e39\u0e49\u0e40\u0e22\u0e47\u0e19","inventory_asset_6":"\u0e40\u0e04\u0e32\u0e17\u0e4c\u0e40\u0e15\u0e2d\u0e23\u0e4c\u0e41\u0e1e\u0e47\u0e04\u0e02\u0e2d\u0e07","inventory_asset_7":"\u0e42\u0e17\u0e23\u0e28\u0e31\u0e1e\u0e17\u0e4c  HuaWei \uff08Y7 Pro\uff09","inventory_asset_8":"\u0e04\u0e2d\u0e21\u0e1e\u0e34\u0e27\u0e40\u0e15\u0e2d\u0e23\u0e4c","inventory_asset_9":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e0a\u0e31\u0e48\u0e07\u0e15\u0e38\u0e4a\u0e01\u0e15\u0e32\u0e04\u0e39\u0e48  (Van)","inventory_asset_10":"\u0e40\u0e01\u0e49\u0e32\u0e2d\u0e35\u0e49\u0e1e\u0e25\u0e32\u0e2a\u0e15\u0e34\u0e01","inventory_asset_11":"\u0e0a\u0e31\u0e49\u0e19\u0e2a\u0e15\u0e47\u0e2d\u0e01\u0e2a\u0e34\u0e19\u0e04\u0e49\u0e32","inventory_asset_12":"\u0e15\u0e39\u0e49\u0e40\u0e0b\u0e1f","inventory_asset_13":"\u0e15\u0e23\u0e30\u0e41\u0e01\u0e23\u0e07\u0e43\u0e2a\u0e48\u0e02\u0e2d\u0e07","inventory_asset_14":"\u0e01\u0e23\u0e30\u0e40\u0e1b\u0e4b\u0e32\u0e02\u0e49\u0e32\u0e07\u0e21\u0e2d\u0e40\u0e15\u0e2d\u0e23\u0e4c\u0e44\u0e0b\u0e04\u0e4c","inventory_asset_15":"\u0e23\u0e16\u0e25\u0e32\u0e01\u0e1e\u0e32\u0e40\u0e25\u0e17","inventory_asset_16":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07IDATA","inventory_asset_17":"\u0e23\u0e16\u0e40\u0e02\u0e47\u0e19\u0e44\u0e1f\u0e40\u0e1a\u0e2d\u0e23\u0e4c","inventory_asset_18":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e1b\u0e23\u0e34\u0e49\u0e19\u0e17\u0e4c\u0e40\u0e25\u0e40\u0e1a\u0e25\u0e15\u0e31\u0e49\u0e07\u0e42\u0e15\u0e4a\u0e30","inventory_asset_19":"\u0e1e\u0e32\u0e40\u0e25\u0e17\u0e1e\u0e25\u0e32\u0e2a\u0e15\u0e34\u0e01","inventory_asset_20":"\u0e21\u0e37\u0e2d\u0e16\u0e37\u0e2d\u0e2a\u0e41\u0e01\u0e19\u0e40\u0e19\u0e2d\u0e23\u0e4c","inventory_asset_21":"\u0e15\u0e23\u0e30\u0e41\u0e01\u0e23\u0e07\u0e40\u0e2b\u0e25\u0e47\u0e01\u0e1e\u0e31\u0e1a\u0e44\u0e14\u0e49","inventory_asset_22":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e2a\u0e41\u0e01\u0e19\u0e25\u0e32\u0e22\u0e19\u0e34\u0e49\u0e27\u0e21\u0e37\u0e2d","inventory_asset_23":"\u0e41\u0e2e\u0e19\u0e25\u0e34\u0e1f","inventory_asset_24":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e2a\u0e41\u0e01\u0e19\u0e40\u0e19\u0e2d\u0e23\u0e4c","inventory_asset_25":"\u0e01\u0e25\u0e49\u0e2d\u0e07  CCTV","inventory_asset_26":"\u0e01\u0e25\u0e48\u0e2d\u0e07\u0e21\u0e2d\u0e44\u0e0b\u0e04\u0e4c","inventory_asset_27":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e40\u0e01\u0e47\u0e1a\u0e40\u0e07\u0e34\u0e19","inventory_asset_28":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e0a\u0e31\u0e48\u0e07\u0e14\u0e34\u0e08\u0e34\u0e15\u0e2d\u0e25  60 \u0e01\u0e34\u0e42\u0e25\u0e01\u0e23\u0e31\u0e21 (DC\/SP)","inventory_asset_29":"\u0e40\u0e0b\u0e2d\u0e23\u0e4c\u0e40\u0e27\u0e2d\u0e23\u0e4c","inventory_asset_30":"\u0e15\u0e39\u0e49\u0e40\u0e01\u0e47\u0e1a\u0e40\u0e2d\u0e01\u0e2a\u0e32\u0e23","inventory_asset_31":"\u0e42\u0e15\u0e4a\u0e30\u0e2b\u0e19\u0e49\u0e32\u0e02\u0e32\u0e27","inventory_asset_32":"\u0e40\u0e01\u0e49\u0e32\u0e2d\u0e35\u0e49\u0e2a\u0e33\u0e19\u0e31\u0e01\u0e07\u0e32\u0e19\u0e2a\u0e35\u0e14\u0e33","inventory_asset_33":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e0a\u0e31\u0e48\u0e07\u0e1e\u0e01\u0e1e\u0e32","inventory_asset_34":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e0a\u0e31\u0e48\u0e07 30 \u0e01\u0e34\u0e42\u0e25\u0e01\u0e23\u0e31\u0e21","inventory_asset_35":"\u0e2a\u0e32\u0e22\u0e40\u0e04\u0e40\u0e1a\u0e34\u0e25","inventory_asset_36":"\u0e1e\u0e31\u0e14\u0e25\u0e21\u0e2d\u0e38\u0e15\u0e2a\u0e32\u0e2b\u0e01\u0e23\u0e23\u0e21","inventory_asset_37":"\u0e01\u0e25\u0e49\u0e2d\u0e07","inventory_asset_38":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e0a\u0e31\u0e48\u0e07\u0e19\u0e49\u0e33\u0e2b\u0e19\u0e31\u0e01","inventory_asset_39":"\u0e41\u0e2d\u0e23\u0e4c","inventory_asset_40":"\u0e42\u0e19\u0e4a\u0e15\u0e1a\u0e38\u0e4a\u0e04","inventory_asset_41":"\u0e15\u0e23\u0e30\u0e01\u0e23\u0e49\u0e32","inventory_asset_42":"\u0e0a\u0e31\u0e49\u0e19\u0e15\u0e30\u0e41\u0e01\u0e23\u0e07","inventory_asset_43":"\u0e15\u0e39\u0e49\u0e40\u0e2b\u0e25\u0e47\u0e01","inventory_asset_44":"\u0e15\u0e23\u0e30\u0e41\u0e01\u0e23\u0e07\u0e40\u0e2b\u0e25\u0e47\u0e01\u0e2a\u0e48\u0e07\u0e02\u0e2d\u0e07","inventory_asset_45":"\u0e15\u0e39\u0e49\u0e01\u0e14\u0e19\u0e49\u0e33\u0e14\u0e37\u0e48\u0e21","inventory_asset_46":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e1b\u0e23\u0e34\u0e49\u0e19\u0e17\u0e4c  Brother","inventory_asset_47":"\u0e40\u0e04\u0e23\u0e37\u0e48\u0e2d\u0e07\u0e1b\u0e23\u0e34\u0e49\u0e19\u0e17\u0e4c\u0e40\u0e25\u0e40\u0e1a\u0e25\u0e1e\u0e01\u0e1e\u0e32","inventory_asset_48":"NVR  \u0e1a\u0e31\u0e19\u0e17\u0e36\u0e01\u0e20\u0e32\u0e1e","inventory_asset_49":"\u0e1e\u0e31\u0e14\u0e25\u0e21\u0e15\u0e34\u0e14\u0e1c\u0e19\u0e31\u0e07","inventory_asset_50":"\u0e1e\u0e31\u0e14\u0e25\u0e21  18 \u0e19\u0e34\u0e49\u0e27","err_msg_unvalid_input":"The number of valid assets and invalid assets should equal to the total","purchase":"Purchase","purchase_department":"Purchasing department","err_msg_do_not_have_department":"Your account does not have department information and cannot  apply. <NAME_EMAIL>","no_approver_in_the_branch":"no approver in the branch","recomment_num":"recommanded number","approval_num":"approved number","os_request_reason_faraway":"Upcountry outsourcing","os_request_reason_stopout":"Branch Overload","os_request_reason_resign":"Employee turnover","os_request_reason_nocar":"Insufficient vehicles","os_request_reason_highavg":"High efficiency","os_request_reason_other":"other reasons","err_msg_job_title_department_dismatch":"Your department does not have this position and cannot apply","os_request_mode_1":"Short term outsourcing application","os_request_mode_2":"Long term outsourcing application","os_request_mode_3":"FMS outsourcing application","os_mode":"Employment model","err_msg_more_days":"The number of long-term outsourcing application days must not be less than 90 days","mileage_modify_msg1":"You have replenished your mileage and cannot apply for modification of your mileage.","handover_type":"post","express_type_face":"face to dace","no_parcel_yet":"The waybill does not exist","please_entry_staff_no":"Please enter employee id","enter_stsff_number":"enter stsff number of branch supervisor or purchasing","no_have_public_assets":"This staff cannot have public assets","no_certificate_identity_verification":"This employee has not passed the certificate and identity verification","no_received_cannot_withdrawn":"The asset's status is not \"Refuse to receive\", that cannot be revoked","not_received":"The asset's status is not \"To be received\", that cannot be revoked","no_assets_to_headquarters":"The asset does not support transferring to the headquarters for the time being","type-1":"Investigate","type-2":"Issued a warning","type-3":"Dismissal","report_str_limit":"Enter a maximum of 1000 words!","reason-1":"About working time","reason-2":"Absent from work for 3 consecutive days","reason-3":"Cheat","reason-4":"Drinking alcohol on worktime or in the workplace","reason-5":"Possession or taking drugs","reason-6":"Disobeying rules or company regulations","reason-7":"Using social media to damage the company","reason-8":"Taking the company's equipment for personal use, downloading competitors apps e.g. Grab , Lalamove","reason-9":"Corruption\/abuse of power","reason-10":"Neglecting one's duty","reason-11":"Quarreling, fighting\/harming colleagues, outsiders, superiors or others","reason-12":"Quarrel without telling superiors or absent from work without reason","reason-13":"The superior did not agree to ask for leave, did not ask for leave through the system","reason-14":"Did not ask for leave through the system","reason-15":"Did not check in or out work on time","reason-16":"Did not cooperate with the company's drug use inspection","reason-17":"Forged documents","reason-18":"Carelessness caused major losses to the company (causing loss of money)","reason-19":"Did not to pay back according to the time specified by the branch","reason-20":"Misreporting mileage","reason-21":"Incite\/provoke\/damage company interests","reason-22":"Dereliction of duty","reason-23":"Causing the company to damage reputation","reason-24":"Not accept or cooperate with the company's investigation","reason-25":"Fake  Status","reason-26":"Fake  POD","reason-27":"Work efficiency does not meet the company's standard (KPI)","reason-28":"Corrupt money","reason-29":"Corrupt package","reason-30":"Steal company property","reason-qaqc-101":"Fake Pod over four times a month","reason-qaqc-102":"The package is lost after being scanned by the branch KIT (2 pieces and more)","reason-qaqc-103":"Input or scanned empty order to send out the warehouse","reason-qaqc-104":"Stolen package","reason-qaqc-105":"Smoking in the warehouse","reason-qaqc-106":"Abuse customers","reason-qaqc-107":"Litter parcels","reason-qaqc-108":"Two or more false cancellations of collection within a month-to be announced","reason-qaqc-109":"Intrude into the customer's home without the customer's consent","report_id":"Employee Code","report_name":"Name","report_job_name":"Position","report_store_name":"Branch","report_type":"Report type","report_reason":"Reason","event_date":"Date of violated regulations","report_remark":"Explanation","picture":"Image","report_break_rule_staff":"Employees who violated regulations","royalty_type_c":"C","royalty_type_ka":"KA","asset_00001":"not uploaded","asset_00002":"waiting for audit","asset_00003":"fail","asset_00004":"pass","asset_00005":"ID card or signature is not uploaded or not approved.","resign_arrears":"Courier payable(THB)","resign_other":"Other(THB)","resign_total":"Total(THB)","resign_cashier":"Cashier payable(THB)","operate_status_9":"Request for repair","operate_status_10":"Resignation pending","operate_status_11":"Excess to be received","emergency_type_1":"Injured in an accident","emergency_type_2":"About car\/forklift","emergency_type_3":"Fall from height","emergency_type_4":"Electric shock","emergency_type_5":"Being hit\/clamped\/pulled","emergency_type_6":"Harsh chemicals","emergency_type_7":"Others","personal_type_1":"No injured","personal_type_2":"There are injured","personal_type_3":"Sent to hospital","personal_type_4":"Not sent to hospital","personal_type_5":"Leave requested","personal_type_6":"No leave","ticket_status_1":"Pending","ticket_status_2":"Replied","ticket_status_3":"Closed","ticket_item_type_1":"mobile phone","ticket_item_type_2":"notebook","ticket_item_type_3":"PC","ticket_item_type_4":"others","ticket_repeat_msg":"Please do not submit repeatedly","phone_no":"Tel","costomer_id":"ID","costomer_name":"Customer Name","price_type":"Price Type","current_disc":"Current Discount","request_disc":"Apply Discount","valid_days":"Validity period (calculated as 30 days per month)","price_type_1":"Ordinary price","price_type_2":"Discount price","price_type_3":"Special price","price_type_4":"Distance gradient price","request_note":"Note","month_period_1":"1 month","month_period_2":"2 months","month_period_3":"3 months","month_period_4":"4 months","month_period_5":"5 months","month_period_6":"6 months","err_msg_cant_request":"Unable to apply, the customer is not shipping locally.","err_msg_no_costomer":"Unable to apply, no customer information found.","err_msg_panding":"Unable to apply, the customer has submitted the freight discount application information, the information is being reviewed, please be patient.","err_msg_has_taken_effect":"Unable to apply, the customer has submitted the freight discount application information, the application information has been reviewed, and the freight discount has taken effect.","err_msg_speacial_price":"Unable to apply, this customer uses special price.","err_msg_exist_coupon_padding":"Unable to apply, there is already such a coupon application","err_msg_exist_coupon":"Unable to apply, there are already such coupons","err_msg_exist_discount":"Cannot apply for fruit discounts in the current region","cod_rate":"COD fee rate","return_rate":"Return discount","credit_period":"Credit period","enter_valid_data":"Please enter effective date","enter_return_rate":"Please enter return discount","enter_return_cod_rate":"Please enter COD discount","enter_credit_period":"Please enter credit term","enter_return_discount_date":"Please enter Discount effective time discount","discount_is_about_expire":"Customer's application discount is about to expire","discount_msg_temp":"Customer: {customer_info} {discount_info} will expire at {expire_date},","order_status_warting_send":"to be delivered","order_status_pre_sale":"Scheduled","order_status_send":"To be received","order_status_customer_received":"completed","order_status_customer_cancel":"The order canceled","pay_method_deduction_of_wages":"Salary deduction","This_month_buy_num_has_exceeded_the_limit":"Each employee can only purchase 5 uniforms per month!","no_pc_code_found":"No PC_code found, please contact your manager.","fill_in_the_shipping_address":"Please fill in the shipping address.","incomplete_shipping_address":"Incomplete shipping address, please contact product department.","car_lights_list":"Car light list","front_lights":"Front lights","rear_lights":"Rear lights","double_flash":"Double flash","roof_lamp":"Roof lamp","left_turn_light":"Turn light (left)","right_turn_light":"Turn light (right)","container_list":"Cabinet list","left_container_wall":"Left cabinet wall","right_container_wall":"Right cabinet wall","wall_container_top":"Wall cabinet top","container_door":"Container door","door_lock":"Door lock","safety_equipment_list":"Safety equipment list","fire_extinguisher":"Fire extinguisher","flashlight":"Flashlight","cone_or_triangle_reflective_sign":"Cone or triangle reflective sign","wheel_baffle_plate":"Wheel baffle plate","reflective_coat":"Reflective coat","other_list":"Other","tire_condition":"Tire condition","wiper":"Wiper","brake":"Brake","intact":"Intact","damage":"Damage","yes":"Yes","no":"No","request_dismissed":"The resignation application has been rejected.","resign_content":"Due to the change of your immediate superior, the original resignation approval has been automatically rejected, please submit a new resignation application.","auto_dismiss_reason":"The employee's immediate superior changes, so the resignation approval has been invalid.","jobtransfer_0001":"The number of characters should be between 10-500","jobtransfer_0002":"yes","jobtransfer_0003":"no","jobtransfer_0004":"No permission to apply","jobtransfer_0005":"There is no branch person in charge at the transfer office, please select again","jobtransfer_0006":"Employee Code","jobtransfer_0007":"Current branch","jobtransfer_0008":"Branch after transfer","jobtransfer_0009":"Current company","jobtransfer_0010":"Current department","jobtransfer_0012":"Current job","jobtransfer_0013":"Transfer company","jobtransfer_0014":"Department after transfer","jobtransfer_0015":"Job after transfer","jobtransfer_0016":"Date of transfer","jobtransfer_0017":"Reason for transfer","jobtransfer_0018":"Invalid transfer application","jobtransfer_0019":"\u0e04\u0e38\u0e13\u0e21\u0e35\u0e04\u0e33\u0e22\u0e37\u0e48\u0e19\u0e02\u0e2d\u0e01\u0e32\u0e23\u0e42\u0e2d\u0e19\u0e22\u0e49\u0e32\u0e22\u0e43\u0e2b\u0e21\u0e48\u0e23\u0e2d\u0e01\u0e32\u0e23\u0e2d\u0e19\u0e38\u0e21\u0e31\u0e15\u0e34","jobtransfer_0020":"Job handover error, please fill in the staff in your branch","jobtransfer_0021":"on the job","jobtransfer_0022":"dimission","jobtransfer_0023":"Suspend","jobtransfer_0024":"No optional transfer HC","jobtransfer_0025":"no valid job transfer request","jobtransfer_0026":"Please re-enter staff ID","jobtransfer_0027":"There is a job transfer application, please do not submit it again","jobtransfer_0028":"Use of personal vehicle","jobtransfer_0030":"Rental of company vehicles","jobtransfer_0031":"Employee to be transferred","jobtransfer_0032":"Area-District","jobtransfer_0033":"Transfer HCID","jobtransfer_0034":"Please select the role after transfer","jobtransfer_0035":"Role after transfer","jobtransfer_0036":"Vehicle source","jobtransfer_0037":"Salary after transfer","jobtransfer_0038":"Basic salary before transfer","jobtransfer_0039":"The HRBP of the department in charge is empty and cannot be applied. Please contact the HRIS administrator","job_transfer_state.1":"Pending transfer","job_transfer_state.2":"Not transferred","job_transfer_state.3":"Transferred","job_transfer_state.4":"Transfer Failed","job_transfer_operate.1":"Transfer immediately","job_transfer_operate.2":"Automatic transfer by system","job_transfer_operate.3":"Edit transfer date","job_transfer_operate.4":"Edit transfer HCID","job_transfer_err":"The basic salary range in 1 ~ 99999","job_transfer_cod_err":"Cannot be transferred, please check whether the employee has COD","job_transfer_unfinish_task_err":"Cannot be transferred, please check whether the employee has unfinished tasks","job_transfer_notice":"Please review in \"OA-Transfer Management- Batch transfer approval\"","job_transfer_title":"Pending approval reminder: OA-batch transfer approval","job_transfer_batch":"Batch transfer","job_transfer_err_2":"HC quantity is insufficient, please select again","job_transfer.base_salary":"Basic salary after transfer","job_transfer.position_allowance":"Post\u00a0allowance\u00a0","job_transfer.exp_allowance":"Experience allowance","job_transfer.food_allowance":"Meal\u00a0allowance","job_transfer.dangerous_area":"Danger zone","job_transfer.notebook_rental":"Computer subsidy","job_transfer.house_rental":"Housing rental subsidy","job_transfer.car_rental":"Car rental allowance","job_transfer.trip_payment":"Oil supplement","job_transfer.recommended":"Recommended subsidy","job_transfer.island_allowance":"Island Allowance","job_transfer.gasoline_allowance":"Oil supplement","msg_qn_058":"Complete the questionnaire and submit it before closing","msg_qn_072":"Answer the questionnaire before closing","sign_msg_029":"Please click the link to sign","sign_msg_033":"Employee signature","sign_msg_027":"Accept & Sign","sign_msg_026":"Agree & Accept","sign_msg_025":"Agree","sign_msg_028":"You have a new message that is not read, please read the clock again","leave_add_undone_questionnaire_msg":"You have an unfinished questionnaire message, please ask for leave after completing it.","leave_add_undone_sign_msg":"You have an unfinished signature message, please ask for leave after completion.","store_name":"Name of branches","manage_region_name":"Region","manage_piece_name":"Area","wrong_pwd":"Incorrect password","no_need_salary_approval":"No need to apply for salary approval","candidate":"Candidate","cv_id":"cv_id","hc_id":"hc_id","salary_jd_name":"The name of JD","salary_department":"Department","salary_city":"Work city","salary_store":"Entry branch","salary_job":"Entry position","salary_money":"Probation period salary","trial_salary":"After probation period salary","please_submit_approval":"Probation period salary","renting":"House rental","wait_time":"Had been waiting for %s hours and %s mins","approval_push_msg":"The salary application is pending approval, please approve it.","auto_pandding_20":"Pass automatically","auto_pandding_21":"Pass automatically-Dimission","auto_pandding_22":"Pass automatically-Suspended","shipment_status":"Logistics status","shipment_be_delivered":"to be delivered","shipment_be_received":"To be received","shipment_number":"shipment number","salary_is_null":"Please fill in salary through trial period","disc_reason_1":"Customers with greater demand for corresponding value-added services","disc_reason_2":"The peers give more preferential customers in the price of value-added services","disc_reason_3":"Customers who have corresponding value-added service needs among the lost customers","disc_reason_4":"New customers who give value-added service experience","disc_reason_5":"Customers who send fruit pieces","disc_reason_6":"else","disc_reason_7":"Customers ask for discounts","disc_reason_8":"Peers in the price to give customers with greater discounts","disc_reason_9":"Customers who need price concessions among the lost customers","disc_reason_10":"Discount experience for new customers","disc_reason_11":"Competitor's customer","one_month":"A month","two_month":"Two months","thr_month":"Three months","discount_type_1":"Shipping discount","discount_type_2":"Fruit shipping discount","discount_type_3":"Return discount","discount_type_4":"Credit period","discount_detail_1":"Apply for marketing products","discount_detail_2":"Customer's average daily order quantity in the previous month","discount_detail_3":"Percentage of speed service, insured insurance service and freight insurance service in the previous month","discount_detail_4":"Customer registration time","discount_detail_5":"Estimated daily orders placed by customers","discount_detail_6":"Customer has applied for the offer","discount_detail_7":"insured insurance service","discount_detail_8":"speed service","discount_detail_9":"freight insurance service","discount_detail_10":"Settlement type","discount_detail_11":"credit_period","settlement_category_1":"On-site settlement","settlement_category_2":"Regular settlement","discount_effective_date":"Discount effective time","ot_detail_1":"Average work efficiency of all outlets last week","ot_detail_2":"Average work efficiency of outlets worked in the last week","ot_detail_3":"Cumulative OT1.5 duration this month","ot_detail_4":"Pieces\/hour","upload_no_more_than_10_pictures":"upload no more than 10 pictures","report_audit_warn":"You still have  %s reports that have not been processed for approval, please do so as soon as possible","counselor_option_cannot_null":"buddy option cannot be empty","counselor_not_exist":"buddy does not exist","apply_parson":"Applicant","apply_department":"Applicant Department","va_message_1":"The temporary vehicle you applied for (Application Number:","va_message_2":") has been approved. The vehicle will arrive at the branch on the time. Please get ready for loading.","va_message_3":"The temporary vehicle you approved for (Application Number:","va_message_4":") has been dismissed. Click here for details.","backyard_msg":"Backyard approval information","va_reject_1":"Branch vehicle loading rate not up to standard","va_reject_2":"Wrong application type of temporary vehicle","va_reject_3":"The photos taken are not up to standard","va_reject_4":"Branch Supervisor\/District Manager approved slowly","va_reject_5":"The CCD cannot find the vehicle","va_reject_6":"The branch applies for unreasonable vehicle type","va_reject_999":"Others","ot_detail_5":"District name","ot_detail_6":"Name of branches","ot_detail_7":"Number of in-service warehouse managers","ot_detail_8":"Cumulative OT1 duration this month","ot_detail_9":"Cumulative OT3 duration this month","ot_detail_10":"Average work efficiency of SHOP outlets last week","ot_detail_11":"Name of the employee's area ","attendance_num":"Number of attendance at the branch","parcel_num":"The total distribution volume of the branch","flow_audit_action.0":"Application","flow_audit_action.1":"Agree","flow_audit_action.2":"Reject","flow_audit_action.6":"Withdrawn","audit_status.1":"Pending","audit_status.2":"Agreed","audit_status.21":"Auto transferred","audit_status.22":"Auto Agreed","audit_status.3":"Rejected","audit_status.4":"Cancelled","audit_status.24":"Agreed","answer_msg_100":"After all answers are completed, you can proceed","audit_status.5":"Time Out","cc_message":"You have a new employee approval copy, please check it!","common_cc_message":"You have a new CC","hc_reasontype_1":"Add","hc_reasontype_2":"Transfer","hc_reasontype_3":"Departure","atb_place_start":"Check-in location for work","atb_place_end":"Check-in location after get off work","atb_reason_1":"Reasons of Work outside","atb_reason_2":"Reasons of Work outside","atb_start":"Clock in time at work","atb_end":"Check-in time after get off work","atb_title":"Business trip Punch-in approval","atb_date":"Punch in date","ceo_mail_001":"To be answered","ceo_mail_002":"Replied","ceo_mail_003":"Completed","ceo_mail_004":"timeout","ceo_mail_005":"solved","hc_budget_no_count":"HC application quota is insufficient, please submit HC budget in OA first","err_msg_network_operations_hc_budget_no_count":"Submission failed, please contact the superior to deal with","mail_code_title":"Backyard Personal information verification","mail_code_content":"Hello\uff0c<br\/>Your personal information verification code is\u3010{code}\u3011","please_fill_explanation":"Please fill explanation","please_insert_picture":"Please insert picture","content_desc_extend":"Explanation-supplement","img_extend":"Image-supplement","hc_budget":"HC budget (remaining \/total)","business_card_notice":"There is no missing attendance need to make up for the selected time","message_title":"Message title","message_set_time":"Estimated sending time","message_category":"Message type","message_send_type":"Sending range","message_content":"Message content","message_top_setting":"Sticky settings","message_by_show_type":"BY display type","message_feedback_setting":"Feedback settings","message_end_time":"Questionnaire deadline","message_category_0":"General message","message_category_6":"Questionnaire message","message_category_33":"Sign message","message_category_34":"Answer message","send_type_1":"Send to everyone","send_type_2":"Send to designated department","send_type_3":"Send to the designated network area","send_type_4":"Send to designated person","send_type_7":"Send to designated position","send_type_9":"Send to designated organization or employee","message_top_setting_1":"Top","message_top_setting_3":"Not sticky","message_by_show_type_0":"Agree","message_by_show_type_1":"Agree & Accept","message_feedback_setting_0":"Voluntary","message_feedback_setting_1":"Must fill in","message_content_hint":"Click to view content details","update_mobile_company_error_1":"The number is being used by employee {staff_info_id}","start_drive_time":"Start time","start_drive_place":"Starting location","mileage_num":"Odometer numbers","mileage_img":"Odometer photo","start_drive_place_img":"Departure scene photos","start_drive_mileage_img":"Start odometer photo","end_drive_time":"End of car time","end_drive_place":"End of car location","end_drive_place_img":"Destination photos","drive_reason":"Reason for using car","drive_push":"It is necessary to complete the \"end of car report\" within 16 hours, and the system will automatically submit, If you do not fill in the end-use car report to the superior, and will not receive subsidies.","mileage_notice":"The ending mileage must be greater than the starting mileage ","hire_type":"Type of employment","hire_time_days":"Period of Employment  (days)","hire_time_month":"Period of Employment (months)","hire_type_1":"Permanent Employee","hire_type_2":"Special contract workers (monthly)","hire_type_3":"Special contract employees (daily)","hire_type_4":"Special contract workers (hourly)","hire_type_5":"Intern","daily":"days","monthly":"monthly","contract_expiry_date":"Contract expiration date","hire_type_chose":"Please select employment type","vehicle_info_vehicle_source_1":"Use personal vehicle","vehicle_info_vehicle_source_2":"Rent company vehicle","vehicle_info_vehicle_source_3":"Borrowed car (not the owner)","vehicle_info_driver_license_type_1":"Private Car Driving Licence (Temporary)","vehicle_info_driver_license_type_2":"Private Motorcycle Driving Licence (Temporary)","vehicle_info_driver_license_type_3":"Private Car Driving Licence","vehicle_info_driver_license_type_4":"Private Vehicle Driving Licence Class 1","vehicle_info_driver_license_type_5":"Private Vehicle Driving Licence Class 2","vehicle_info_driver_license_type_6":"Private Vehicle Driving Licence Class 3","vehicle_info_driver_license_type_7":"Private Vehicle Driving Licence Class 4","vehicle_info_driver_license_type_8":"Commercial Vehicle Driver's License","vehicle_info_driver_license_type_9":"Public Vehicle Driving licence Class 1","vehicle_info_driver_license_type_10":"Public Vehicle Driving licence Class 2","vehicle_info_driver_license_type_11":"Public Vehicle Driving licence Class 3","vehicle_info_driver_license_type_12":"Public Vehicle Driving licence Class 4","vehicle_info_driver_license_type_13":"Private Motorcycle Driver's License","vehicle_info_driver_license_type_14":"Commercial Motorcycle Driver's License","vehicle_info_driver_license_type_100":"Other","vehicle_info_0001":"The same license plate number already exists, please re-enter","vehicle_info_0002":"Please enter the correct engine number: Please enter 10-16 characters","vehicle_info_0003":"The same engine number already exists, please re-enter","vehicle_info_0004":"Insurance end time must be later than start time","vehicle_info_0005":"Driver's license expiration date must be later than effective date","vehicle_info_0006":"Under review, temporarily uneditable","vehicle_info_0007":"start time of use vehicle must be later than employment date","vehicle_info_0008":"Please upload Front and Back pictures","re_field_amount":"Total price (Including VAT)","re_field_apply_center_code":"Applicant's center","re_field_apply_company_name":"Applicant's company","re_field_apply_department_name":"Applicant's department","re_field_apply_first_department_name":"\u7533\u8bf7\u4eba\u6240\u5c5e\u4e00\u7ea7\u90e8\u95e8","re_field_apply_id":"Applicant's Employee ID","re_field_apply_mobile":"Applicant's number","re_field_apply_name":"Applicant's name","re_field_apply_store_name":"Branch","re_field_approve":"Approver","re_field_auth_logs":"Approval Process","re_field_bank_account":"Beneficiary account number","re_field_bank_info":"Bank information","re_field_bank_name":"Beneficiary account name","re_field_bank_type":"Beneficiary bank name","re_field_base_info":"Basic Information","re_field_category_a":"Reimbursement classification","re_field_category_b":"Reimbursement details","re_field_cost_store_name":"Expense Network","re_field_created_company_name":"Sponsor\u2018s company","re_field_created_department_name":"Sponsor\u2018s department","re_field_created_id":"Sponsor\u2018s Employee ID","re_field_created_name":"Sponsor\u2018s name","re_field_currency_text":"Currency","re_field_date":"Reimbursement date","re_field_deal_id":"Process Labor Employee ID","re_field_deal_mark":"Handling opinions","re_field_deal_name":"Process Labor name","re_field_deal_res":"Process result","re_field_detail":"Reimbursement details","re_field_detail_amount":"Price (Including VAT)","re_field_detail_id":"NO.","re_field_finished_at":"Complete time","re_field_fuel_end":"Ending point","re_field_fuel_mileage":"Mileage","re_field_fuel_start":"Starting point","re_field_id":"Numbering","re_field_info":"Reimbursement instructions","re_field_invoices_ids":"Invoice number","re_field_is_pay":"Whether Paid","re_field_loan_amount":"Write-down loans","re_field_local":"Local","re_field_other_amount":"Other deductions","re_field_pay_at":"Payment date","re_field_pay_bank_account":"Payment account","re_field_pay_bank_name":"Payment bank","re_field_pay_info":"Payment Information","re_field_rate":"Vat","re_field_real_amount":"The amount actually paid","re_field_remark":"Remarks","re_field_sign_name":"Signatory","re_field_start_at":"Date of occurrence","re_field_stat":"Reimbursement classification statistics","re_field_step":"Step name","re_field_tax":"VAT","re_field_tax_not":"Price\uff08No VAT\uff09","re_field_title":"Personal Reimbursement","re_field_travel":"Travel","re_field_travel_end":"Destination city","re_field_travel_start":"Departure city","re_field_apply_cost_center":"\u8d39\u7528\u6240\u5c5e\u4e2d\u5fc3","re_field_apply_cost_department":"\u8d39\u7528\u6240\u5c5e\u90e8\u95e8","re_field_apply_cost_first_department":"\u8d39\u7528\u6240\u5c5e\u4e00\u7ea7\u90e8\u95e8","re_field_apply_cost_store":"\u8d39\u7528\u6240\u5c5e\u7f51\u70b9","re_field_fee_end_at":"Expense end date","re_field_fee_start_at":"Expense start date","country_code_project":"Expense item","cs_ticket_status_1":"Pending","cs_ticket_status_2":"Read","cs_ticket_status_3":"Replied","cs_ticket_status_4":"Closed","ticket_name":"Ticket","ticket_speed_1":"Urgent","ticket_speed_2":"General","updated_at":"Update time","created_at":"Submission time","submitter":"Submitter","cs_department":"Department","department_3":"CS Team","department_22":"KAM Customer Service","cs_ticket_type":"Ticket Type","cs_ticket_type_11":"Urge Parcel","cs_ticket_type_18":"Customer Complain","tracking_number":"Tracking Number","ticket_title":"Ticket Title","ticket_content":"Ticket Content","branch_reply":"Branch Reply","to_branch":"To Branch","ticket_push_notice":"You have a new ticket to process","click_to_process":"Click to process","time_should_response":"Time should response","timed_out":"Timed out","electronic_contract_clock_top_hint":"Your contract has not been signed. Please punch in after signing","electronic_contract_clock_foot_hint":"You can punch in all contracts","contract_empty_error":"Your contract has been deleted by HR, please contact HR or superior to deal with it as soon as possible","err_msg_no_valid_approver":"Unable to apply due to lack of approval person","err_msg_resign_vehicle_images":"\u8f66\u8f86\u7167\u7247:\u8bf7\u4e0a\u4f20\u8f66\u7684\u56db\u8fb9\u3001\u8f66\u5185\u3001\u76ae\u5361\u8f66\u53a2\u7167\u7247\uff0c\u81f3\u5c116\u4e2a","err_msg_resign_vehicle_test_images":"\u8f66\u8f86\u68c0\u9a8c\u6587\u4ef6:\u6700\u5c111\u4e2a\uff0c\u6700\u591a2\u4e2a","err_msg_resign_vehicle_claim_images":"\u6c7d\u8f66\u7d22\u8d54\u4ef6\uff1a\u975e\u5fc5\u586b\uff0c\u6700\u591a3\u4e2a","resign_vehicle_images":"\u8f66\u8f86\u7167\u7247","resign_vehicle_test_images":"\u8f66\u8f86\u68c0\u9a8c\u6587\u4ef6","resign_vehicle_claim_images":"\u6c7d\u8f66\u7d22\u8d54\u4ef6","msg_type_name_6":"\u7cfb\u7edf\u6d88\u606f","msg_type_name_1":"\u5168\u90e8","msg_type_name_2":"\u901a\u77e5","msg_type_name_3":"\u95ee\u5377","msg_type_name_4":"\u7b7e\u5b57","msg_type_name_5":"\u7b54\u9898","abnormal_assets":"abnormal assets","flash-purchase-records":"Flash employee purchase records","flash_asset_management":"Flash asset management","flash_staff_assets_management":"Flash staff assets management","flash_staff_leave_assets_management":"Flash staff leave assets management","hr_flash_emails":"Flash employee email managemen","hr_flash_flashout_config":"Work orders of outsourcing staff","hr_flash_operate_log_salary":"Flash Staff Operate Log Salary","hr_flash_salary":" Flash branch employee salary structure","hr_flash_salary_headoffice":"Flash headquarters salary structure","hr_shift_config":"Optional shift maintenance","hr_staff_offical_partner":"Cooperators Staff Management","hr_staff_statistics_area":"\u62db\u8058\u8fdb\u5ea6-\u6309\u533a\u57df","store_material_management":"Outlet packaging material management"}