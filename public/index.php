<?php

use Phalcon\Mvc\Application;

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');


try {
    ini_set('memory_limit', '-1');

    /**
     * Use composer autoloader to load vendor classes
     */
    if (file_exists(BASE_PATH . "/vendor/autoload.php")) {
        include BASE_PATH . "/vendor/autoload.php";
    }

    //include APP_PATH . "/util/ErrorHandler.php";
    /**
     * 加载环境变量
     */
    $dotenv = new Dotenv\Dotenv(BASE_PATH);
    $dotenv->load();

    defined('RUNTIME') or define('RUNTIME',env('runtime'));

    date_default_timezone_set(env('timeZoneGMT','Asia/Bangkok'));

    /**
     * Read services
     */
    include APP_PATH . '/config/services.php';

    /**
     * Handle the request
     */
    $application = new Application($di);

    $modules = require APP_PATH . '/config/modules.php';
    /**
     * 国家模块
     */
    $application->registerModules($modules);

    /**
     * Handle routes
     */
    include APP_PATH . '/config/router.php';


    /**
     * Include Autoloader
     */
    include APP_PATH . '/config/loader.php';

    /**
     *
     */
    $application->handle()->send();
} catch (\Throwable $e) {
    $result = [
        'code' => 0,
        'message' => RUNTIME == 'dev' ? $e->getMessage():'something went wrong,',
        'data'=> RUNTIME == 'dev' ? $e->getMessage().$e->getTraceAsString():null,
    ];
    $di = $application->getDI();
    //错误日志
    $log = array(
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'code' => $e->getCode(),
        'msg' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
    );

    $exception_logger = $di->get('logger');
    $exception_logger->error(json_encode($log,JSON_UNESCAPED_UNICODE));
    $response = $application->response;
    $response->setStatusCode(500);
    $response->setJsonContent($result);
    $response->send();
}
