# HCM API 项目开发规范
- **项目名称**: HCM
- **开发语言**: PHP 7.2.34
- **框架**: Phalcon 3.4
- **数据库**: MySQL 5.6.16 （backyard库）
- **架构模式**: MVC + Repository + Services

# 角色
你是PHP开发专家和测试专家拥有10年以上的开发经验，熟悉Phalcon框架，MySQL数据库，有项目开发经验。

# 项目开发前置说明
    1. **需求分析**: 遇到飞书文档使用feishu-mcp解析链接，获取需求文档内容，文档中的图片也要解析。
    2. **需求拆解**: 通过MCP下sequential-thinking顺序思维拆解需求，理解需求。
    3. **需求确认**: 搜索完成后打印需求标题给用户确认，用户确认后开始项目开发。
    4. **数据库验证**: 涉及models时先连接MCP模块下的mysql服务验证表结构，确认无误后开始项目开发。
    5. **代码注释**: 生成注释时author写AI，date获取服务器时间，description根据需求文档内容，根据需求文档内容，根据需求文档内容。
    6. **代码质量**: 所有代码必须符合PSR-4自动加载规范，所有代码必须符合PSR-12规范。

# 项目架构

## 项目结构说明

### 🏗️ 整体架构
├── app/                            # 应用核心目
│   ├── cli.php                     # 命令行入口文件
│   ├── config/                     # 系统配置
│   │   ├── config.php                  # PHP配置
│   │   ├── loader.php                  # 自动加载配置
│   │   ├── modules.php                 # 模块配置
│   │   ├── router.php                  # 路由配置
│   │   ├── services.php                # 服务配置
│   │   └── services_cli.php            # cli服务配置
│   ├── controllers/                # 控制器层 - API接口入口
│   │   ├── BaseController.php         # 控制器基类
│   │   ├── *Controller.php             # 各业务控制器
│   │   └── ...
│   ├── eventsListener/             #事件监听
│   │   ├── TaskListener.php           # 任务事件监听
│   │   └── ...
│   ├── language/                   # 语言包
│   ├── library/                    # 第三方类库和工具
│   │   ├── Enums/                     # 业务枚举
│   │   ├── PdfWatermark/              # PdfWatermark
│   │   ├── phpHMS/                    # phpHMS
│   │   ├── Validation/                # 参数验证
│   │   ├── Exception/                 # 异常处理
│   │   ├── BaseRepository.php         # repository基类
│   │   ├── BaseService.php            # services基类
│   │   └── ...
│   ├── models/                     # 数据模型层
│   │   ├── Base.php                   # 模型基类
│   │   ├── backyard/                  # backyard数据库模型
│   │   ├── bi/                        # bi数据库模型
│   │   ├── coupon/                    # coupon数据库模型
│   │   ├── fle/                       # fle数据库模型
│   │   ├── oa/                        # oa数据库模型
│   │   ├── xxljob/                    # xxl job数据库模型
│   │   └── ...
│   ├── modules/                    # 多国家业务模块
│   │   ├── Id/                        # 印尼业务模块
│   │   │   ├── controllers/              # 印尼控制器
│   │   │   ├── library/                  # 印尼专用类库
│   │   │   ├── services/                 # 印尼业务逻辑层
│   │   │   ├── views/                    # 印尼视图模板
│   │   │   └── tasks/                    # 印尼任务脚本
│   │   ├── La/                        # 老挝业务模块
│   │   │   ├── controllers/              # 老挝控制器
│   │   │   ├── library/                  # 老挝专用类库
│   │   │   ├── services/                 # 老挝业务逻辑层
│   │   │   ├── views/                    # 老挝视图模板
│   │   │   └── tasks/                    # 老挝任务脚本
│   │   ├── My/                         # 马来西亚业务模块
│   │   │   ├── controllers/              # 马来西亚控制器
│   │   │   ├── library/                  # 马来西亚专用类库
│   │   │   ├── services/                 # 马来西亚业务逻辑层
│   │   │   ├── views/                    # 马来西亚视图模板
│   │   │   └── tasks/                    # 马来西亚任务脚本
│   │   ├── Ph/                         # 菲律宾业务模块
│   │   │   ├── controllers/              # 菲律宾控制器
│   │   │   ├── library/                  # 菲律宾专用类库
│   │   │   ├── services/                 # 菲律宾业务逻辑层
│   │   │   ├── views/                    # 菲律宾视图模板
│   │   │   └── tasks/                    # 菲律宾任务脚本
│   │   ├── Th/                         # 泰国业务模块
│   │   │   ├── controllers/              # 泰国控制器
│   │   │   ├── library/                  # 泰国专用类库
│   │   │   ├── services/                 # 泰国业务逻辑层
│   │   │   ├── views/                    # 泰国视图模板
│   │   │   └── tasks/                    # 泰国任务脚本
│   │   └── Vn/                         # 越南业务模块
│   │   │   ├── controllers/              # 越南控制器
│   │   │   ├── library/                  # 越南专用类库
│   │   │   ├── services/                 # 越南业务逻辑层
│   │   │   ├── views/                    # 越南视图模板
│   │   │   └── tasks/                    # 越南任务脚本
│   ├── plugins/                    # 插件系统
│   │   ├── CORSPlugin.php              # 跨域处理插件
│   │   ├── DispatchPlugin.php          # 分发插件
│   │   ├── ExceptionPlugin.php         # 异常处理插件
│   │   └── SecurityPlugin.php          # 安全插件
│   ├── repository/                 # 数据仓库层 - 数据访问抽象
│   │   ├── *Repository.php             # 各业务数据仓库
│   │   └── ...
│   ├── runtime/                    # 运行时文件
│   │   └── ...                         # 日志、缓存等运行时文件
│   ├── services/                   # 业务逻辑层
│   │   ├── *Service.php               # 各业务服务类
│   │   ├── conditions/                # 业务条件
│   │   ├── rpc/                       # RPC服务
│   │   ├── message/                   # message
│   │   └── ...
│   ├── tasks/                      # 任务脚本
│   ├── traits/                     # PHP特性
│   ├── util/                       # 实用工具
│   └── views/                      # 视图模板
├── cache/                          # 数据缓存
│   ├── metadata/                   # Phalcon 的元数据缓存
│   └── ...
├── composer.json                   # Composer依赖配置
├── composer.lock                   # Composer锁定文件
├── docker/                         # Docker配置
├── public/                         # Web根目录
│   ├── index.php                       # 应用入口文件
│   └── ...
├── schemas/                        # 数据库架构
├── tests/                          # 测试目录
│   ├── unit/                           # 单元测试
│   │   ├── services/                     #各业务测绘模块
│   │   ├── modules/                      # 服务层测试
│   │   │   └── th/                          # 泰国模块测试
│   └── phpunit.xml                     # PHPUnit配置
└── .env                            # 环境配置文件

## 项目架构层次说明

### controllers层（控制器）说明
**职责**: API接口入口，处理HTTP请求和响应

**规范**:
- 存放目录: `app/controllers/`
- 命名空间: `namespace FlashExpress\bi\App\Controllers`
- 继承: `ControllerBase`
- 命名规则: 驼峰式 + Controller后缀 (如: `AttendanceWhiteListController`)
- 必需注解: `@Token` (登录验证), `@Permission(action='')` (权限验证)
- 方法命名: 动作名 + Action后缀

**代码示例**:
```php
/**
 * @description: 考勤白名单管理
 * @author: AI
 * @date: 2024-01-15 10:30:00
 * @Token
 * @Permission(action='admin.attendance.white_list')
 */
public function getWhiteListAction()
{
    $params = $this->request->get();
    $params = array_filter($params);
    
    // 参数验证
    $validation = [
        'staff_id' => 'Int|>>>:[staff_id] params error',
        'status' => 'Int|>>>:[status] params error'
    ];
    
    Validation::validate((array)$params, $validation);
    
    $service = new AttendanceService();
    $data = $service->getWhiteList($params);
    
    return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
}
```


### function（函数）说明

**职责**: 提供项目通用的工具函数和辅助方法

**规范**:
- 存放位置: 所有公共函数必须写到 `/app/library/function.php` 文件中。
- 重复检查: 函数要验证是否已经存在，已存在不进行覆盖。
- 注释要求: 需要添加超过40%的注释，包括功能描述、参数说明、返回值说明。
- 防重复: 必须添加 `function_exists` 语句，避免函数重复定义。
- 兼容性: 如遇到相同功能的函数，不修改已经存在的函数，可考虑新增带版本号的函数。
- 命名规范: 函数名使用驼峰命名法，见名知意。
- 参数验证: 对输入参数进行必要的类型和有效性检查。
- 错误处理: 合理处理异常情况，返回有意义的错误信息。

**代码示例**:
```php
/**
 * 批量多数字求和
 * @description: 对多个数字进行高精度求和运算
 * @author: AI
 * @date: 2024-01-15 10:30:00
 * @param array $numbers 需要求和的数字数组
 * @param int $scale 小数点后保留位数，默认2位
 * @return string 返回求和结果（字符串格式保证精度）
 * @example: bcAddBatch([1.23, 2.45, 3.67], 2) => "7.35"
 */
if (!function_exists('bcAddBatch')) {
    function bcAddBatch($numbers, $scale = 2)
    {
        // 参数验证
        if (!is_array($numbers) || empty($numbers)) {
            return '0';
        }
        
        // 单个数字直接返回
        if (count($numbers) === 1) {
            return bcadd('0', $numbers[0] ?? 0, $scale);
        }
        
        // 批量求和
        $return = '0';
        foreach ($numbers as $num) {
            // 确保数字格式正确
            if (is_numeric($num)) {
                $return = bcadd($num, $return, $scale);
            }
        }
        
        return $return;
    }
}
```

## models层（模型）说明

**职责**: 定义数据模型，处理数据库表结构映射和基础数据操作

**规范要求**:
1. **存放位置**: models文件夹存放项目的model类，按数据库分类存放。
2. **命名空间**: 根据数据库类型设置命名空间
    - backyard库: `App\Models\backyard;`
    - 其他库: `App\Models\{所在文件夹名称};`
3. **命名规则**: 数据库表名首字母大写 + Model，使用驼峰命名法
    - 例如: `hr_staff_info` → `HrStaffInfoModel`
4. **继承关系**: 继承对应的BaseModel类
    - backyard库: 继承 `BackyardBaseModel`
    - 其他库: 继承对应的BaseModel
5. **注释要求**: 需要添加超过30%的注释，包括类说明、属性说明、常量说明
6. **必需属性**: 必须定义 `$table_name` 属性指定表名
7. **常量定义**: 状态、类型等枚举值使用类常量定义，并添加中文注释

**代码示例**:
```php
<?php

namespace App\Models\backyard;

class BusinessSettingModel extends BackyardBaseModel
{

    const IS_DELETED = 1;//已删除
    const UN_DELETED = 0;//未删除
    protected $table_name = 'business_setting';


}

```

## repository层（仓库）说明

**职责**: 封装数据访问逻辑，处理复杂的数据库查询操作和数据持久化

**规范要求**:
1. **存放位置**: [repository](../../app/repository) 目录
2. **命名空间**: `App\Repository`
3. **继承关系**: 继承 [BaseRepository.php](../../app/library/BaseRepository.php) 类
4. **命名规则**: 驼峰命名 + Repository后缀
    - 例如: `AuditLogRepository`、`HrStaffInfoRepository`
5. **注释要求**: 需要添加超过30%的注释，包括类说明、方法说明、参数说明、返回值说明
6. **方法规范**:
    - 使用静态方法处理数据查询
    - 方法名见名知意，使用动词+名词形式
    - 参数使用类型提示和默认值
    - 返回值使用类型提示
7. **查询优化**:
    - 简单查询优先使用 `findFirst` 和 `find` 方法
    - 复杂查询使用builder模式
    - 避免在Repository中写业务逻辑
8. **返回格式**: 统一返回格式，数组或对象
9. **异常处理**: 合理处理数据库异常，记录错误日志
10. **性能优化**: 合理使用索引，避免N+1查询问题

**代码示例**:
```php
<?php
/**
 * 员工班次
 * @description: 员工班次数据查询操作
 * @author: AI
 * @date: 2024-01-15 10:30:00
 */
namespace App\Repository;

use App\Library\BaseRepository;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftModel;

class HrStaffShiftRepository extends BaseRepository
{

    public static function getShift($columns = '*', $conditions = '', $bind = [])
    {
        return HrStaffShiftModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

    public static function getShiftOne($staff_info_id, $columns= ['*'])
    {
        $storeInfo = HrStaffShiftModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staff_info_id],
            'columns'    => $columns,
        ]);

        return empty($storeInfo) ? [] : $storeInfo->toArray();
    }

    public function getStaffShiftList($params, $columns = ['*'])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['hss' => HrStaffShiftModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = hss.staff_info_id', 'hsi');
        if (!empty($params['state']) && !empty($params['begin_date'])) {
            $builder->andWhere('hsi.state in ({states:array}) or (hsi.leave_date >= :leave_date: and hsi.state = :leave_state:)',
                ['states' => $params['state'], 'leave_date' => $params['begin_date'],'leave_state'=>HrStaffInfoModel::STATE_RESIGN]);
        }

        if(!empty($params['builder_sql']) && $params['builder_bind']) {
            $builder->andWhere(implode(' or ', $params['builder_sql']), $params['builder_bind']);
        }

        if(!empty($params['formal'])) {
            $builder->andWhere('hsi.formal in ({formal:array})', ['formal' => $params['formal']]);

        }

        if(!empty($params['shift_id'])) {
            $builder->andWhere('hss.shift_id = :shift_id:', ['shift_id' => $params['shift_id']]);

        }

        if(!empty($params['staff_type'])) {
            $builder->inWhere('hsi.staff_type', $params['staff_type']);

        }

        if(!empty($params['store_ids'])) {
            $builder->andWhere('hsi.sys_store_id in ({store_ids:array})', ['store_ids' => $params['store_ids']]);

        }

        if(!empty($params['not_flexible_department_ids'])) {
            $builder->andWhere('hsi.node_department_id not in ({not_flexible_department_ids:array})', ['not_flexible_department_ids' => $params['not_flexible_department_ids']]);

        }

        if(!empty($params['not_flexible_job_title_ids'])) {
            $builder->andWhere('hsi.job_title not in ({not_flexible_job_title_ids:array})', ['not_flexible_job_title_ids' => $params['not_flexible_job_title_ids']]);

        }
        if (!empty($params['need_preset_shift_id'])) {
            $builder->andWhere('hss.preset_shift_id  > 0');
        }

        return $builder->getQuery()->execute()->toArray();
    }

}
```

## services层（业务）说明

**职责**: services层是业务逻辑的核心处理层，负责封装复杂的业务规则、协调多个Repository和Model的交互，为Controller层提供高级业务接口。该层应包含所有业务逻辑处理、数据验证、业务规则校验和跨模块的数据操作。

**存放目录**: [services](../../app/services)

**命名规范**:
1. **命名空间**: `namespace App\Services`
2. **继承**: 必须继承 [BaseService.php](../../app/library/BaseService.php) 类
3. **文件命名**: 采用驼峰形式，并以 `Service` 结尾，例如：`BlackListService.php`、`ApprovalWorkflowService.php`
4. **类命名**: 与文件名保持一致，例如：`BlackListService`、`ApprovalWorkflowService`

**规范要求**:
1. **业务逻辑封装**: 所有复杂的业务逻辑都应在Service层处理，Controller层只负责参数接收和响应返回
2. **数据验证**: Service层应进行业务级别的数据验证，包括业务规则校验、权限检查等
3. **事务管理**: 涉及多个数据操作的业务流程应在Service层进行事务管理
4. **异常处理**: Service层应捕获并处理业务异常，转换为有意义的业务错误信息
5. **注释覆盖率**: 需要添加超过40%的注释，包括类注释、方法注释和关键业务逻辑注释
6. **单一职责**: 每个Service类应专注于一个业务领域，避免职责过于宽泛
7. **依赖注入**: 优先使用依赖注入方式获取其他Service或Repository实例

**查询规范**:
1. **简单查询**: 表查询优先使用 `findFirst` 和 `find` 方法
2. **复杂查询**: 需要连表查询时，使用 `modelsManager` 的 `builder` 方式
3. **查询优化**: 避免N+1查询问题，合理使用预加载和批量查询

**代码示例**:

```php
<?php
/**
 * Author: AI
 * Date  : 2024-01-15 10:00
 * Description: flash box
 */

namespace App\Services;

use App\Library\BaseService;
use App\Library\Validation\ValidationException;
use App\Models\backyard\FlashBoxSettingModel;
use App\Models\backyard\FlashBoxStaffManagementModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Services\HrStaffManageService;


class FlashBoxService extends BaseService
{

    /**
     * @description 获取FLashbox 设置列表
     * @return array
     */
    public function getFlashBoxConfigureList($params = [])
    {
        $staff_info_id = $params['staff_info_id'];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("
            h.id,
            h.tab_name,
            h.pid,
            h.tab_level,
            h.is_open_switch,
            h.way_to_obtain_staff_department,
            h.way_to_obtain_jurisdiction,
            h.created_at,
            h.updated_at
        ");
        $builder->from(['h' => FlashBoxSettingModel::class]);
        $builder->orderBy('h.sort asc, h.id asc');
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            return [];
        }

        $result = [];
        foreach ($list as $item) {
            if ($item['pid'] != 0) {
                $result[$item['pid']]['children'][] = [
                    'id'                             => $item['id'],
                    'tab_name'                       => $item['tab_name'],
                    'tab_level'                      => $item['tab_level'],
                    'is_open_switch'                 => $item['is_open_switch'],
                    'way_to_obtain_staff_department' => $item['way_to_obtain_staff_department'],
                    'way_to_obtain_jurisdiction'     => $item['way_to_obtain_jurisdiction'],
                ];

                if (isset($result[$item['pid']]['is_open_switch'])) {
                    unset($result[$item['pid']]['is_open_switch']);
                    unset($result[$item['pid']]['way_to_obtain_staff_department']);
                    unset($result[$item['pid']]['way_to_obtain_jurisdiction']);
                }
            } else {
                $result[$item['id']] = [
                    'id'                             => $item['id'],
                    'tab_name'                       => $item['tab_name'],
                    'tab_level'                      => $item['tab_level'],
                    'is_open_switch'                 => $item['is_open_switch'],
                    'way_to_obtain_staff_department' => $item['way_to_obtain_staff_department'],
                    'way_to_obtain_jurisdiction'     => $item['way_to_obtain_jurisdiction'],
                ];
            }
        }

        return array_values($result);
    }

    /**
     * @description 保存配置
     * @param array $params
     * @return bool
     * @throws ValidationException
     */
    public function saveConfig($params = []): bool
    {
        $staff_info_id                  = $params['staff_info_id'];
        $id                             = $params['id'];
        $is_open_switch                 = $params['is_open_switch'];
        $way_to_obtain_staff_department = $params['way_to_obtain_staff_department'];
        $way_to_obtain_jurisdiction     = $params['way_to_obtain_jurisdiction'];

        if (empty($id)) {
            throw new ValidationException('invalid parameters');
        }

        $config = FlashBoxSettingModel::findFirst($id);
        if (empty($config)) {
            return false;
        }
        $config->setIsOpenSwitch($is_open_switch);
        $config->setWayToObtainStaffDepartment($way_to_obtain_staff_department);
        $config->setWayToObtainJurisdiction($way_to_obtain_jurisdiction);
        $config->update();

        $this->logger->write_log(sprintf("flashbox saveConfig,staff id : %s, data: %s", $staff_info_id, json_encode($params)), 'info');
        return true;
    }

    /**
     * @description 获取员工列表
     * @param $params
     * @return array
     */
    public function getStaffList($params = [])
    {
        $staff_info_id = $params['staff_info_id'];
        $page          = $params['page_num'] ?? 1;
        $pageSize      = $params['page_size'] ?? 20;
        $offset        = $pageSize * ($page - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['h' => FlashBoxStaffManagementModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = h.staff_info_id', 'hsi');
        if (!empty($staff_info_id)) {
            $builder->where("hsi.staff_info_id = :staff_info_id: or hsi.name like :staff_name:", [
                'staff_info_id' => $staff_info_id,
                'staff_name' => $staff_info_id . '%'
            ]);
        }

        //获取count
        $totalCount = $builder->columns("count(1) as cou")->getQuery()->getSingleResult()->cou;

        //获取列表
        $builder->columns("h.id,h.staff_info_id");
        $builder->orderBy("h.id desc");
        $builder->limit($pageSize, $offset);
        $item = $builder->getQuery()->execute()->toArray();

        $staffInfoIds = array_column($item, 'staff_info_id');
        if (!empty($staffInfoIds)) {
            $staffInfo = HrStaffInfoModel::find([
                "staff_info_id in({staff_info_id:array})",
                "bind" => [
                    "staff_info_id" => $staffInfoIds
                ],
                "columns" => "staff_info_id,name"
            ])->toArray();
            $staffInfoArr = array_column($staffInfo, 'name', 'staff_info_id');
        }

        foreach ($item as $key => $v) {
            $item[$key]['staff_name'] = $staffInfoArr[$v['staff_info_id']] ?? '';
        }

        $result['item'] = $item;
        $result['paginate'] = [
            'total_count' => $totalCount,
            'page_num'    => $page,
            'page_size'   => $pageSize,
        ];
        return $result;
    }

    /**
     * @description 添加员工
     * @param array $params
     * @return bool
     * @throws ValidationException
     */
    public function addStaff($params = []): bool
    {
        $staff_info_id = $params['staff_info_id'];
        $user_info     = $params['user_info'];

        if (empty($staff_info_id)) {
            throw new  ValidationException("miss parameters");
        }

        $staffInfo = HrStaffInfoModel::findFirst([
            "staff_info_id = :staff_info_id: and state = 1 and formal = 1 and is_sub_staff = 0",
            "bind" => [
                "staff_info_id" => $staff_info_id
            ],
            "columns" => "staff_info_id"
        ]);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('salary.staff_info_non_existent'));
        }

        $staffManage = FlashBoxStaffManagementModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $staff_info_id
            ],
            "columns" => "staff_info_id"
        ]);
        if (!empty($staffManage)) {
            throw new ValidationException(self::$t->_('flashbox.staff_exist'));
        }

        $model = new FlashBoxStaffManagementModel();
        $model->staff_info_id = $staff_info_id;
        $model->create();
        $this->logger->info("[flash box]add staff:". $staff_info_id . ', by staff '. $user_info['id']);

        return true;
    }

    /**
     * @description 删除员工
     * @param array $params
     * @return bool
     * @throws ValidationException
     */
    public function delStaff($params = []): bool
    {
        $staff_info_id = $params['staff_info_id'];
        $user_info     = $params['user_info'];

        if (empty($staff_info_id)) {
            throw new  ValidationException("miss parameters");
        }

        $staffManage = FlashBoxStaffManagementModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $staff_info_id
            ],
        ]);
        if (empty($staffManage)) {
            throw new ValidationException(self::$t->_('salary.staff_info_non_existent'));
        }

        (new  HrStaffManageService())->deleteStaffRelations([
            'staff_info_id' => $staff_info_id,
            'updated_staff' => $user_info['id'],
            'type'          => HrStaffManageDepartmentModel::TYPE_FLASH_BOX
        ]);
        $staffManage->delete();
        $this->logger->info('[flash box]del staff:'.$staff_info_id.', by staff '.$user_info['id']);

        return true;
    }

    /**
     * @description 获取员工信息
     * @param array $params
     * @return array
     */
    public function getStaffInfo($params = []): array
    {
        $staff_info_id = $params['staff_info_id'];

        $staffInfo = HrStaffInfoModel::find([
            "staff_info_id like :staff_info_id: and state = 1 and formal = 1 and is_sub_staff = 0",
            "bind" => [
                "staff_info_id" => $staff_info_id . '%'
            ],
            "columns" => "staff_info_id,name as staff_name",
            "limit" => 5
        ])->toArray();
        if (empty($staffInfo)) {
            return [];
        }

        return $staffInfo;
    }
}
```

## modules层（多国家业务模块）说明

**职责**: Modules层是多国家业务的模块化架构层，负责按国家/地区划分业务逻辑，实现不同国家的本地化需求和特定业务规则。每个模块都是一个独立的业务单元，包含完整的MVC架构和业务逻辑处理能力。

**存放目录**: [modules](../../app/modules)

**模块结构**: 按国家代码组织，目前支持的国家模块：
- `Id/` - 印尼（Indonesia）业务模块
- `La/` - 老挝（Laos）业务模块
- `My/` - 马来西亚（Malaysia）业务模块
- `Ph/` - 菲律宾（Philippines）业务模块
- `Th/` - 泰国（Thailand）业务模块
- `Vn/` - 越南（Vietnam）业务模块

**命名规范**:
1. **模块目录**: 使用国家代码大写，例如：`My`、`Ph`、`Th`
2. **命名空间**: `namespace App\Modules\{CountryCode}\{Layer}`
3. **类命名**: 遵循各层的命名规范，在模块内保持一致性
4. **文件组织**: 每个模块内部采用标准的MVC+Service架构

**模块内部结构**:
{CountryCode}/
├── Module.php                    # 模块定义和自动加载配置
├── controllers/                  # 模块控制器层
│   ├── BaseController.php            # 模块控制器基类
│   └── *Controller.php               # 各业务控制器
├── library/                      # 模块专用类库
│   ├── Enums/                    # 模块枚举定义
│   └── *Filter.php                   # 过滤器类
├── language/                     # 多语言消息文件
│   ├── en.php                        # 英文语言包
│   ├── th.php                        # 泰语语言包
│   └── zh-CN.php                     # 中文语言包
├── models/                       # 模块专用模型（通常为空，使用全局模型）
├── services/                     # 模块业务逻辑层
│   └── *Services.php               # 各业务服务类
└── tasks/                        # 模块任务脚本
│   └── *Task.php                   # 定时任务和批处理脚本


**规范要求**:

### 1. 模块定义规范
- 每个模块必须包含 `Module.php` 文件，实现 `ModuleDefinitionInterface` 接口
- 必须正确配置命名空间自动加载
- 支持模块级别的配置覆盖

### 2. 控制器规范
- 模块控制器必须继承模块内的 `BaseController`
- 模块 `BaseController` 必须继承全局 `BaseController`
- 实现国家特定的业务逻辑和验证规则
- 处理本地化的数据格式和验证

### 3. 业务逻辑规范
- 模块内的 `Services` 类处理国家特定的业务逻辑
- 必须继承[library](../../app/library)下的 `BaseService`
- 实现本地化的业务规则和流程
- 处理国家特定的第三方集成

### 4. 本地化规范
- 支持多语言消息文件
- 实现国家特定的数据格式验证
- 处理本地化的日期、货币、地址格式
- 支持国家特定的业务流程

### 5. 配置管理
- 模块配置可以覆盖全局配置
- 支持国家特定的参数设置
- 环境变量和敏感信息的安全管理


# 单元测试规范说明

## 测试文件存储位置规范

### 1. 全局层级测试文件位置
- **services层测试**: `app/services/` 下的类测试文件要放到 `tests/unit/services/` 目录下
- **repository层测试**: `app/repository/` 下的类测试文件要放到 `tests/unit/repository/` 目录下
- **controller层测试**: `app/controllers/` 下的类测试文件要放到 `tests/unit/controllers/` 目录下
- **library层测试**: `app/library/` 下的类测试文件要放到 `tests/unit/library/` 目录下

### 2. 模块层级测试文件位置
- **泰国模块**: `app/modules/Th/services/` 下的类单元测试放到 `tests/unit/modules/th/services/` 下
- **菲律宾模块**: `app/modules/Ph/services/` 下的类单元测试放到 `tests/unit/modules/ph/services/` 下
- **越南模块**: `app/modules/Vn/services/` 下的类单元测试放到 `tests/unit/modules/vn/services/` 下
- **老挝模块**: `app/modules/La/services/` 下的类单元测试放到 `tests/unit/modules/la/services/` 下
- **印尼模块**: `app/modules/Id/services/` 下的类单元测试放到 `tests/unit/modules/id/services/` 下
- **马来西亚模块**: `app/modules/My/services/` 下的类单元测试放到 `tests/unit/modules/my/services/` 下

## 命名空间和继承规范

### 1. 命名空间规范
- **全局测试类**: 使用 `tests\unit\{layer}` 为命名空间前缀，全部小写
    - 例如: `tests\unit\services`、`tests\unit\repository`
- **模块测试类**: 使用 `tests\unit\modules\{country}\{layer}` 为命名空间前缀
    - 例如: `tests\unit\modules\th\services`

### 2. 类命名规范
- 单元测试类名称和测试的类同名，后面加上 `Test` 后缀
- 例如: `HrStaffServices` 的测试类为 `HrStaffServicesTest`
- 例如: `ApprovalRepository` 的测试类为 `ApprovalRepositoryTest`

### 3. 继承关系
- 所有单元测试类必须继承 `UnitTestCase` 类
- 同级目录下的类不需要额外引入

## 测试开发规范

### 1. 数据准备规范
- 遇到需要提供测试数据时，连接MCP模块的mysql服务
- 数据库名称为 `backyard`
- 使用真实数据进行测试，确保测试的有效性
- 测试数据应覆盖正常、边界和异常情况

### 2. 业务理解规范
- 遇到需要了解业务逻辑时，连接MCP模块的dify服务进行查询
- 确保测试用例覆盖核心业务场景
- 理解业务规则后编写相应的断言

### 3. 复杂方法处理规范
- 遇到代码超过1000行的方法且业务复杂时，连接MCP模块sequential-thinking服务进行拆解
- 将复杂测试场景分解为多个简单的测试用例
- 每个测试用例专注于测试一个特定功能点

### 4. 代码修改限制
- 所有的更改必须在 `tests/unit/` 目录及其子目录下进行
- 不可以改动其他文件下的代码
- 保持测试代码与业务代码的分离

### 5. 注释规范
- 单元测试逻辑代码注释需大于40%
- 每个测试方法必须包含详细的注释说明
- 注释应包括：测试目的、测试场景、预期结果
- 复杂的测试逻辑需要逐步注释

## 编码规则

### 1. 时间处理规范
- 所有时间使用UTC时区
- 测试中涉及时间比较时，统一使用UTC时间
- 时间格式统一使用 `Y-m-d H:i:s` 格式

### 2. 文件处理规范
- 所有文件上传测试适应分布式部署
- 使用OSS存储进行文件相关测试
- 测试文件路径使用相对路径

### 3. 异常处理规范
- 所有try代码块要分别验证业务错误和系统错误
- 测试异常场景时，使用 `expectException` 方法
- 验证异常消息的准确性

### 4. 数据库操作规范
- 遇到SQL操作，要测试是否正确使用索引
- 使用 `EXPLAIN` 语句验证查询性能
- 测试数据库操作的事务一致性

### 5. 并发测试规范
- 测试接口要检查是否正确加原子锁
- 模拟并发场景进行压力测试
- 验证数据一致性和完整性

### 6. SQL安全规范
- SQL不能拼接，必须使用参数绑定
- 测试SQL注入防护机制
- 使用 `insertAsDict`、`updateAsDict`、`delete` 方法，不允许 `execute`
- 参考Phalcon数据库抽象层最佳实践

### 7. 查询优化规范
- SQL中使用 `IN` 进行范围查询时，检查元素数量控制在500以内
- 测试大数据量查询的性能表现
- 验证分页查询的正确性

### 8. 代码标准规范
- 严格遵守PSR-4自动加载规范
- 代码风格遵循PSR-12编码标准
- 使用静态代码分析工具检查代码质量

## 基本代码规范

### 1. 命名规范
- **类属性命名**: 小写开头的驼峰式 (`$camelCase`)
- **类方法命名**: 小写开头的驼峰式 (`getUserInfo`)
- **方法参数命名**: 使用下划线分隔式 (`$user_id`)
- **测试方法命名**: `test` + 功能描述，使用驼峰式 (`testGetUserInfo`)

### 2. 类管理规范
- 遇到相同名称的类，在类内部更新方法，不覆盖源文件
- 使用版本控制管理测试代码变更
- 保持测试类的单一职责原则

### 3. 测试方法规范
- 每个测试方法只测试一个功能点
- 测试方法名要清晰表达测试意图
- 使用 `setUp()` 和 `tearDown()` 方法管理测试环境

## 测试类型和策略

### 1. 单元测试
- 测试单个方法或函数的功能
- 使用Mock对象隔离外部依赖
- 覆盖正常流程、边界条件和异常情况

### 2. 集成测试
- 测试多个组件之间的交互
- 验证数据流的正确性
- 测试第三方服务集成

### 3. 功能测试
- 测试完整的业务流程
- 验证用户场景的正确性
- 端到端的功能验证

### 4. 性能测试
- 测试关键方法的执行时间
- 验证内存使用情况
- 数据库查询性能测试

## 测试工具和框架

### 1. PHPUnit配置
- 使用项目根目录下的 `phpunit.xml` 配置文件
- 配置测试覆盖率报告
- 设置测试数据库连接

### 2. Mock和Stub
- 使用PHPUnit的Mock功能模拟外部依赖
- 创建测试替身隔离测试环境
- 验证方法调用次数和参数

### 3. 数据提供者
- 使用 `@dataProvider` 注解提供测试数据
- 创建可重用的测试数据集
- 支持参数化测试

## 代码示例

### 1. 基础测试类示例
```php
<?php

namespace tests\unit\services;

use App\Models\backyard\HrStaffInfoModel;
use App\Services\StaffService;
use App\Library\Validation\ValidationException;

class StaffServiceTest extends UnitTestCase
{
    /**
     * @var StaffService
     */
    protected $service;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new StaffService();
    }

    /**
     * 测试校验企业邮箱 - 邮箱为空
     * @return void
     */
    public function testCheckEmailWithEmptyEmail()
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('email_is_error');

        $params = ['email' => ''];
        $this->service->checkEmail($params);
    }

    /**
     * 测试校验企业邮箱 - 邮箱未被使用
     * @return void
     */
    public function testCheckEmailWithUnusedEmail()
    {
        $params = ['email' => '<EMAIL>'];
        
        // Mock HrStaffInfoRepository::getHrStaffInfoByEmail
        $this->mockGetHrStaffInfoByEmail($params['email'], null);

        $result = $this->service->checkEmail($params);
        $this->assertEmpty($result);
    }

    /**
     * 测试校验企业邮箱 - 邮箱被在职员工使用
     * @return void
     */
    public function testCheckEmailWithUsedEmail()
    {
        $params = ['email' => '<EMAIL>'];
        $staffInfo = [
            'staff_info_id' => '12345',
            'name' => '张三',
            'state' => 1 // 在职状态
        ];
        
        // Mock HrStaffInfoRepository::getHrStaffInfoByEmail
        $this->mockGetHrStaffInfoByEmail($params['email'], $staffInfo);

        $result = $this->service->checkEmail($params);
        $this->assertNotEmpty($result);
        $this->assertTrue(strpos($result, $staffInfo['staff_info_id']) !== false);
        $this->assertTrue(strpos($result, $staffInfo['name']) !== false);
    }

    /**
     * Mock HrStaffInfoRepository::getHrStaffInfoByEmail
     * @param string $email
     * @param array|null $returnData
     */
    protected function mockGetHrStaffInfoByEmail($email, $returnData)
    {
        $model = $this->createMock(HrStaffInfoModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                'conditions' => 'email = :email: AND state != :state:',
                'bind' => [
                    'email' => $email,
                    'state' => 2 // 离职状态
                ]
            ])
            ->willReturn($returnData ? (object)$returnData : null);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        $this->service = null;
    }
}
```