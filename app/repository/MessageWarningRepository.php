<?php
/**
 * Author: Bruce
 * Date  : 2023-12-09 17:24
 * Description:
 */

namespace App\Repository;


use App\Library\BaseRepository;
use App\Library\Enums\MessWarningEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\MessageWarningModel;
use App\Models\backyard\MessageWarningTransferSignModel;

class MessageWarningRepository extends BaseRepository
{

    /**
     * 查询警告书
     * @param $params
     * @param $columns
     * @return mixed
     */
    public function getMessageWarningList($params, $columns = ['*'])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(MessageWarningModel::class);

        $builder= $this->getBuilderWhere($builder, $params);

        $builder->orderBy('id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * where 条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        //证人1 非警告系统发的 警告信，证人1 未签字
        if (!empty($params['witness1_ids'])) {
            $builder->where("witness1_id in ({witness1_ids:array}) and witness1_img = '' and staff_warning_message_id = 0 ", ['witness1_ids' => $params['witness1_ids']]);
        }
        //证人2 非警告系统发的 警告信，证人2 未签字
        if (!empty($params['witness2_ids'])) {
            $builder->where("witness2_id in ({witness2_ids:array}) and witness2_img = '' and staff_warning_message_id = 0 ", ['witness2_ids' => $params['witness2_ids']]);
        }

        if(!empty($params['is_delete'])) {
            $builder->where("is_delete = :is_delete: ", ['is_delete' => $params['is_delete']]);
        }

        if(!empty($params['ids'])) {
            $builder->where("id in ({ids:array}) ", ['ids' => $params['ids']]);
        }

        return $builder;
    }

    /**
     * 查询警告书
     * @param $params
     * @param $columns
     * @return mixed
     */
    public function getMessageWarningToSuperiorList($params, $columns = ['*'])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['mwts' => MessageWarningTransferSignModel::class]);
        $builder->join(MessageWarningModel::class, 'mw.id = mwts.message_warning_id', 'mw');
        $builder->join(HrStaffInfoModel::class, 'mw.staff_info_id = hsi.staff_info_id', 'hsi');

        $builder= $this->getBuilderSuperiorWhere($builder, $params);

        $builder->orderBy('mwts.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    public function getBuilderSuperiorWhere($builder, $params)
    {
        if(!empty($params['is_witness'])) {
            $builder->where(" mw.superior_id != 0 and superior_img != '' ");

        } else {
            $builder->where(" mw.superior_id != 0 and superior_img = '' ");

        }

        if(!empty($params['department_ids'])) {
            $builder->andWhere("hsi.node_department_id in ({department_ids:array})", ['department_ids' => $params['department_ids']]);

        }

        if(!empty($params['type'])) {
            $builder->andWhere(" mwts.type = :type: ", ['type' => $params['type']]);

        }

        if(!empty($params['level'])) {
            $builder->andWhere(" mwts.level in ({level:array}) ", ['level' => $params['level']]);

        }

        if($params['is_warning_sys'] === true) {
            $builder->andWhere(" staff_warning_message_id != 0 ");
        } else {
            $builder->andWhere(" staff_warning_message_id = 0 ");
        }

        return $builder;
    }

    /**
     * th 获取员工 指定警告类型的警告信次数
     * @param $params
     * @return int
     */
    public function getThWarningCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['COUNT(1) as total_count']);
        $builder->from(MessageWarningModel::class);
        $builder->Where('staff_info_id=:staff_info_id: AND is_delete=:is_delete: AND warning_type = :warning_type: AND type_code = :type_code:',
            [
                'staff_info_id' => $params['staff_id'],
                'is_delete'     => MessWarningEnums::IS_DELETED_NO,
                'warning_type'  => $params['warning_type'],
                'type_code'     => $params['type_code'],
            ]);
        $count = $builder->getQuery()->getSingleResult()->total_count;
        $count = $count ? intval($count) : 0;
        return $count;
    }

    public static function getWarningOne($params, $columns)
    {
        $conditions              = 'operator_id = :operator_id: and staff_info_id = :staff_info_id: and warning_type = :warning_type: and created_at >= :start_create_at: and created_at <= :end_create_at:';
        $bind['operator_id']     = $params['operator_id'];
        $bind['staff_info_id']   = $params['staff_info_id'];
        $bind['warning_type']    = $params['warning_type'];
        $bind['start_create_at'] = $params['start_create_at'];
        $bind['end_create_at']   = $params['end_create_at'];
        $data                    = MessageWarningModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    public static function getOne($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1 and is_delete = :is_delete: ';
        $bind['is_delete']       = MessageWarningModel::DELETE_NO;

        if (!empty($params['warning_types']) && is_array($params['warning_types'])) {
            $conditions         .= ' and warning_type in ({warning_types:array})';
            $bind['warning_types'] = $params['warning_types'];
        }

        if (!empty($params['staff_info_id'])) {
            $conditions         .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        $data = MessageWarningModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
            'order'      => 'id desc',
        ]);

        return !empty($data) ? $data->toArray() : [];
    }
}