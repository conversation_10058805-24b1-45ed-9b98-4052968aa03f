<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>第二次评估-th</title>
    <style>
      @page {
        size: 210mm 297mm;
        margin: 25mm 10mm 35mm;
      }

      body {
        margin: 0;
      }

      p {
        margin-bottom: 2mm;
      }

      .box {
        width: 100%;
        margin: 0 auto;
        font-family: "Calibri";
        font-size: 4mm;
        line-height: 6mm;
        text-align: justify;
      }

      .no-rowspan-grid {
        width: 99%;
        margin: 1mm 0;
        border-spacing: 0;
        border-collapse: separate;
        border: none;
        /* 允许表格在页面间分开 */
        page-break-inside: auto;
        break-inside: auto;
      }

      .no-rowspan-grid tr {
        /* 防止行在页面间断开 */
        page-break-inside: avoid;
        break-inside: avoid;
      }

      .no-rowspan-grid td {
        padding: 3mm;
        box-shadow: inset 0.2mm 0.2mm 0 0 black,
          /* 上和左边框 */ 0.2mm 0.2mm 0 0 black;
        /* 右和下边框 */
        border: none;
      }

      .grid {
        width: 99%;
        border-spacing: 0;
        border-collapse: separate;
        border: none;
        /* 将分页控制应用在整个table上 */
        page-break-inside: avoid;
        break-inside: avoid;
        /* 确保包含合并单元格的表格不被拆分 */
        orphans: 1;
        widows: 1;
      }

      /* 合并相邻表格的边框 - 当在同一页面时 */
      .grid + .grid {
        margin-top: -0.2mm;
      }

      @media screen {
        .grid + .grid tr:first-child td {
          box-shadow: inset 0.2mm 0 0 0 black, 0.2mm 0.2mm 0 0 black;
        }
      }

      /* 当表格被分页时，确保边框完整 */
      @media print {
        /* 在打印时，如果表格被分页，取消边框合并 */
        .grid + .grid {
          margin-top: 0;
        }
      }

      .grid td {
        padding: 3mm;
        box-shadow: inset 0.2mm 0.2mm 0 0 black,
          /* 上和左边框 */ 0.2mm 0.2mm 0 0 black;
        /* 右和下边框 */
        border: none;
      }
      .grid-thead {
        width: 99%;
        border-spacing: 0;
        border-top: 0.2mm solid black;
        border-left: 0.2mm solid black;
      }

      .grid-thead td {
        border-bottom: 0.2mm solid black;
        border-right: 0.2mm solid black;
      }
    </style>
  </head>
  <body>
    <div class="box">
      <h4>สรุปผลการประเมินผลทดลองงานครั้งที่ 2</h4>
      <p>
        หมายเหตุ : มาตรฐานการประเมินในช่วงทดลองงาน
        ผลการประเมินของพนักงานต้องมีเกรด B ขึ้นไป (B, B+, A) จึงจะถือว่า 'ผ่าน'
        การทดลองงาน หากต่ำกว่า “B” ถือว่า'ไม่ผ่านทดลองงาน'
        และผลการประเมินครั้งที่ 2
        ซึ่งถือเป็นการประเมินรอบสุดท้ายต้องไม่ต่ำกว่าเกรด B
        โดยผลการประเมินจะมีผลต่อการตัดสินว่าพนักงานจะได้รับการบรรจุเป็นพนักงานประจำหรือไม่
      </p>
      <p>Grade A พนักงานปฏิบัติงานได้เกินเป้าหมายที่ตั้งไว้</p>
      <p>
        Grade B+ พนักงานปฏิบัติงานได้เกินมาตรฐานเป้าหมายที่ตั้งไว้เพียงเล็กน้อย
      </p>
      <p>Grade B พนักงานปฏิบัติงานตามมาตรฐานตามเป้าหมายที่ตั้งไว้</p>
      <p>Grade B- พนักงานปฏิบัติงานได้ต่ำกว่าเป้าหมายบางส่วน</p>
      <p>Grade C พนักงานปฏิบัติงานได้ต่ำกว่ามาตรฐานตามเป้าหมายที่ตั้งไว้</p>
      <table
        style="margin-top: 5mm; text-align: center; margin-bottom: 15mm"
        class="no-rowspan-grid"
      >
        <tr>
          <td>
            <strong>ลำดับ</strong>
          </td>
          <td>
            <strong>เป้าหมาย</strong>
          </td>
          <td><strong>เกณฑ์การวัดผล</strong></td>
          <td><strong>น้ำหนัก</strong></td>
          <td><strong>ผลที่ได้</strong></td>
        </tr>
        <!-- 表格内容 循环 -->
        <#list stage_score_list.target_score as item> 
        <tr>
          <td>${item_index+1}</td>
          <td>${item.name}</td>
          <td>${item.info}</td>
          <td>${item.weight}%</td>
          <td>${item.final_text}</td>
        </tr>
        </#list>
        <!-- Summary Grade -->
        <tr>
          <td colspan="3" style="text-align: left"><strong>สรุปผล</strong></td>
          <td>100%</td>
          <td>${stage_score_list.count_score_text}</td>
        </tr>
      </table>
      <h4>ข้อเสนอแนะ</h4>
      <table class="no-rowspan-grid">
        <thead>
          <tr>
            <td><strong>NO</strong></td>
            <td>
              <strong>Well Done</strong>
            </td>
            <td><strong>Improvement</strong></td>
            <td><strong>Action Plan</strong></td>
          </tr>
        </thead>
        <!-- 表格内容 循环 -->
        <tr>
          <td>1</td>
          <td>${stage_score_list.good_job}</td>
          <td>${stage_score_list.no_good_job}</td>
          <td>${stage_score_list.action_plan}</td>
        </tr>
      </table>
<#if act_version != '0' >
      <h4>ประเมินค่านิยมองค์กร</h4>
      <p>คำอธิบายการประเมิน：</p>
      <p>
        1. การประเมิน Core value Behavoir ต้องทำการประเมินเป็นลำดับทีละข้อ
        หากผ่านข้อที่ 1
      </p>
      <p>
        ถึงจะประเมินข้อในลำดับถัดไปได้ และะอ้างอิงตามข้อเท็จจริง
        (หากไม่ผ่านในขั้นใด จะไม่สามารถประเมินในลำดับถัดไปได้)
      </p>
      <p>
        2. หากไม่เป็นไปตามมาตรฐานตั้งแต่ลำดับขั้นที่ 1 สามารถกำหนดเป็น 0 ได้
      </p>
      <p>
        3. หาก Core Value Behavior ข้อใดข้อหนึ่งได้ลำดับขั้นที่ 0
        ผลการประเมินโดยรวมจะถือว่าไม่ผ่านเกณฑ์.
      </p>
      <table class="grid">
        <tr style="text-align: center">
          <td style="width: 25mm">
            <strong>ค่านิยม</strong>
          </td>
          <td>
            <strong>พฤติกรรม</strong>
          </td>
          <td style="width: 15mm">
            <strong>ลำดับขั้น</strong>
          </td>
          <td style="width: 20mm">
            <strong>ผลประเมิน</strong>
          </td>
        </tr>
      </table>
      <!-- 表格内容 每五个是一个table -->
      <#list stage_act_values_list as item> 
            <table class="grid"> 
                    <#list item.behavior_list as items> 
                    <tr>
                        <#if items_index % 5 == 0>
                                <td style="width: 25mm; text-align: center" rowspan="5">
                                ${item.concept_name!''}
                                </td>
                        </#if>
                        <td>
                            ${items.behavior_name!''}
                        </td>
                        <td style="width: 15mm; text-align: center">${items.grade!''}</td>
                        <#if items_index % 5 == 0>
                           <td style="text-align: center; width: 20mm" rowspan="5">${item.final_score!''}</td>
                        </#if>
                        </tr>
                    </#list>
            </table>
      </#list>
</#if>
      <div style="display: flex; margin: 5mm 0">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <strong>สรุปผลการประเมิน</strong>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
            ${stage_score_list.final_result_text}
          </div>
          <div>(ผ่าน/ไม่ผ่าน)</div>
        </div>
      </div>
      <div style="text-indent: 5mm">
        ข้าพเจ้าได้รับผลการประเมินนี้แล้วและพร้อมที่จะปรับปรุงและพัฒนา
        หากข้าพเจ้าไม่สามารถปรับปรุงและพัฒนาได้ข้าพเจ้ายอมรับว่าข้าพเจ้าไม่มีประสิทธิภาพในการทำงานในตำแหน่งนี้
        และข้าพเจ้ายินยอมที่จะให้บริษัทพิจารณาไม่ผ่านทดลองงานในครั้งนี้
      </div>
      <!-- 员工签字 -->
      <div style="display: flex; margin-bottom: 5mm">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>ลงชื่อพนักงาน :</span>
          </div>
          <div style="width: 80mm; border-bottom: 1px dashed; margin: 0 2mm">
            <!-- 签字图片 -->
            <#if second_done_staff_sign_url !="">
                <img style="width: 60mm; height: 40mm;" src="${second_done_staff_sign_url}">
            </#if>
          </div>
        </div>
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>วันที่:</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
        ${second_done_staff_sign_time}
        </div>
        </div>
      </div>
      <!-- 员工上级签字 -->
      <div style="display: flex; margin-bottom: 5mm">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>ลงชื่อหัวหน้างาน:</span>
          </div>
          <div style="width: 80mm; border-bottom: 1px dashed; margin: 0 2mm">
            <!-- 签字图片 -->
            <#if second_done_manager_sign_url !="">
                <img style="width: 60mm; height: 40mm;" src="${second_done_manager_sign_url}">
            </#if>
          </div>
        </div>
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>วันที่:</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
        ${second_done_manager_sign_time}
        </div>
        </div>
      </div>
    </div>
  </body>
</html>
