<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>第二次评估-en</title>
    <style>
      @page {
        size: 210mm 297mm;
        margin: 25mm 10mm 35mm;
      }

      body {
        margin: 0;
      }

      p {
        margin-bottom: 2mm;
      }

      .box {
        width: 100%;
      margin: 0 auto;
      font-family: "Calibri";
      font-size: 4mm;
      line-height: 6mm;
      text-align: justify;
      }

      .no-rowspan-grid {
        width: 99%;
        margin: 1mm 0;
        border-spacing: 0;
        border-collapse: separate;
        border: none;
        /* 允许表格在页面间分开 */
        page-break-inside: auto;
        break-inside: auto;
      }

      .no-rowspan-grid tr {
        /* 防止行在页面间断开 */
        page-break-inside: avoid;
        break-inside: avoid;
      }

      .no-rowspan-grid td {
        padding: 3mm;
        box-shadow: inset 0.2mm 0.2mm 0 0 black,
          /* 上和左边框 */ 0.2mm 0.2mm 0 0 black;
        /* 右和下边框 */
        border: none;
      }

      .grid {
        width: 99%;
        border-spacing: 0;
        border-collapse: separate;
        border: none;
        /* 将分页控制应用在整个table上 */
        page-break-inside: avoid;
        break-inside: avoid;
        /* 确保包含合并单元格的表格不被拆分 */
        orphans: 1;
        widows: 1;
      }

      /* 合并相邻表格的边框 - 当在同一页面时 */
      .grid + .grid {
        margin-top: -0.2mm;
      }

      @media screen {
        .grid + .grid tr:first-child td {
          box-shadow: inset 0.2mm 0 0 0 black, 0.2mm 0.2mm 0 0 black;
        }
      }

      /* 当表格被分页时，确保边框完整 */
      @media print {
        /* 在打印时，如果表格被分页，取消边框合并 */
        .grid + .grid {
          margin-top: 0;
        }
      }

      .grid td {
        padding: 3mm;
        box-shadow: inset 0.2mm 0.2mm 0 0 black,
          /* 上和左边框 */ 0.2mm 0.2mm 0 0 black;
        /* 右和下边框 */
        border: none;
      }
      .grid-thead {
        width: 99%;
        border-spacing: 0;
        border-top: 0.2mm solid black;
        border-left: 0.2mm solid black;
      }

      .grid-thead td {
        border-bottom: 0.2mm solid black;
        border-right: 0.2mm solid black;
      }
    </style>
  </head>
  <body>
    <div class="box">
      
      
      <h4>Performance Probation Evaluation#2</h4>
      <p>
        Explanation: The probationary evaluation standard requires employees to achieve a grade of B or higher (B, B+, A) to be considered as having "passed" the probation period. If the grade is below B, the employee will be deemed to have "failed" the probation. Additionally, the second evaluation, which serves as the final assessment, must also be no lower than grade B. The evaluation results will directly determine whether the employee is confirmed as a permanent employee.
      </p>
      <p>Grade A: Exceed Expectation</p>
      <p>Grade B+: Partially Over Expectation</p>
      <p>Grade B Meet Standard</p>
      <p>Grade B- Partially Below Expectation</p>
      <p>Grade C Below Expectation</p>
      <table
        style="margin-top: 5mm; text-align: center; margin-bottom: 15mm"
        class="no-rowspan-grid"
      >
        <tr>
          <td>
            <strong>NO.</strong>
          </td>
          <td>
            <strong>Objective</strong>
          </td>
          <td><strong>Measurement Criteria</strong></td>
          <td><strong>Weight</strong></td>
          <td><strong>Grade</strong></td>
        </tr>
        <!-- 表格内容 循环 -->
        <#list stage_score_list.target_score as item> 
        <tr>
          <td>${item_index+1}</td>
          <td>${item.name}</td>
          <td>${item.info}</td>
          <td>${item.weight}%</td>
          <td>${item.final_text}</td>
        </tr>
        </#list>
        <!-- Summary Grade -->
        <tr>
          <td colspan="3" style="text-align: left">Summary Grade</td>
          <td>100%</td>
          <td>${stage_score_list.count_score_text}</td>
        </tr>
      </table>
      <h4>Performance Feedback</h4>
      <table class="no-rowspan-grid">
        <thead>
          <tr>
            <td><strong>NO</strong></td>
            <td>
              <strong>Well Done</strong>
            </td>
            <td><strong>Improvement</strong></td>
            <td><strong>Action Plan</strong></td>
          </tr>
        </thead>
        <!-- 表格内容 循环 -->
        <tr>
          <td>1</td>
          <td>${stage_score_list.good_job}</td>
          <td>${stage_score_list.no_good_job}</td>
          <td>${stage_score_list.action_plan}</td>
        </tr>
      </table>
<#if act_version != '0' >
      <h4>Core Value Behavior Scoreboard</h4>
      <p>Evaluation Description:</p>
      <p>
        1. Based on actual behaviors/facts, the assessment of Core Value
        Behaviors should adopt the approach of level upgrading, which means the
        option of higher level should be based on the achievement of all the
        lower levels. You should start with the lower level to decide the
        suitable level.
      </p>
      <p>
        2. If you haven't reached to the requirement of level 1, you can give 0.
      </p>
      <p>
        3. Once there is 0 for Core Value Behavior, the overall result should
        not be treated as "Meet Standard".
      </p>
      <table class="grid">
        <tr style="text-align: center">
          <td style="width: 25mm">
            <strong>idea</strong>
          </td>
          <td>
            <strong>Core Value Behavior</strong>
          </td>
          <td style="width: 15mm">
            <strong>Level</strong>
          </td>
          <td style="width: 20mm">
            <strong>Evaluation Level</strong>
          </td>
        </tr>
      </table>
      <!-- 表格内容 每五个是一个table -->
      <#list stage_act_values_list as item> 
            <table class="grid"> 
                    <#list item.behavior_list as items> 
                    <tr>
                        <#if items_index % 5 == 0>
                                <td style="width: 25mm; text-align: center" rowspan="5">
                                ${item.concept_name!''}
                                </td>
                        </#if>
                        <td>
                            ${items.behavior_name!''}
                        </td>
                        <td style="width: 15mm; text-align: center">${items.grade!''}</td>
                        <#if items_index % 5 == 0>
                           <td style="text-align: center; width: 20mm" rowspan="5">${item.final_score!''}</td>
                        </#if>
                        </tr>
                    </#list>
            </table>
      </#list>
</#if>
      <div style="display: flex; margin: 5mm 0">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <strong>Summary of evaluation results</strong>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
        ${stage_score_list.final_result_text}
        </div>
        <div>(Pass/Not Pass)</div>
        </div>
      </div>
      <div style="text-indent: 5mm">
        <strong style="border-bottom: 1px solid black"
          ><i
            >I have received the evaluation results and agree to improve and develop. If I cannot improve, I know that I am not suitable for the position.</i
          ></strong
        >
      </div>
      <!-- 员工签字 -->
      <div style="display: flex; margin-bottom: 5mm">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>Employee Signature:</span>
          </div>
          <div style="width: 80mm; border-bottom: 1px dashed; margin: 0 2mm">
            <!-- 签字图片 -->
            <#if second_done_staff_sign_url !="">
                <img style="width: 60mm; height: 40mm;" src="${second_done_staff_sign_url}">
            </#if>
          </div>
        </div>
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>Date:</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
        ${second_done_staff_sign_time}
        </div>
        </div>
      </div>
      <!-- 员工上级签字 -->
      <div style="display: flex; margin-bottom: 5mm">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>Supervisor Signature:</span>
          </div>
          <div style="width: 80mm; border-bottom: 1px dashed; margin: 0 2mm">
            <!-- 签字图片 -->
            <#if second_done_manager_sign_url !="">
                <img style="width: 60mm; height: 40mm;" src="${second_done_manager_sign_url}">
            </#if>
          </div>
        </div>
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>Date:</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          ></div>
        </div>
      </div>
  </body>
</html>
