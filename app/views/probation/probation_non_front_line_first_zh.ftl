<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>第一次评估-cn</title>
    <style>
      @page {
        size: 210mm 297mm;
        margin: 25mm 10mm 35mm;
      }

      body {
        margin: 0;
      }

      p {
        margin-bottom: 2mm;
      }

      .box {
        width: 100%;
        margin: 0 auto;
        font-family: "Calibri";
        font-size: 4mm;
        line-height: 6mm;
        text-align: justify;
      }
      .no-rowspan-grid {
        width: 99%;
        margin: 1mm 0;
        border-spacing: 0;
        border-collapse: separate;
        border: none;
        /* 允许表格在页面间分开 */
        page-break-inside: auto;
        break-inside: auto;
      }

      .no-rowspan-grid tr {
        /* 防止行在页面间断开 */
        page-break-inside: avoid;
        break-inside: avoid;
      }

      .no-rowspan-grid td {
        padding: 3mm;
        box-shadow: inset 0.2mm 0.2mm 0 0 black,
          /* 上和左边框 */ 0.2mm 0.2mm 0 0 black;
        /* 右和下边框 */
        border: none;
      }

      .grid {
        width: 99%;
        border-spacing: 0;
        border-collapse: separate;
        border: none;
        /* 将分页控制应用在整个table上 */
        page-break-inside: avoid;
        break-inside: avoid;
        /* 确保包含合并单元格的表格不被拆分 */
        orphans: 1;
        widows: 1;
      }

      /* 合并相邻表格的边框 - 当在同一页面时 */
      .grid + .grid {
        margin-top: -0.2mm;
      }

      @media screen {
        .grid + .grid tr:first-child td {
          box-shadow: inset 0.2mm 0 0 0 black, 0.2mm 0.2mm 0 0 black;
        }
      }

      /* 当表格被分页时，确保边框完整 */
      @media print {
        /* 在打印时，如果表格被分页，取消边框合并 */
        .grid + .grid {
          margin-top: 0;
        }
      }

      .grid td {
        padding: 3mm;
        box-shadow: inset 0.2mm 0.2mm 0 0 black,
          /* 上和左边框 */ 0.2mm 0.2mm 0 0 black;
        /* 右和下边框 */
        border: none;
      }
      .grid-thead {
        width: 99%;
        border-spacing: 0;
        border-top: 0.2mm solid black;
        border-left: 0.2mm solid black;
      }

      .grid-thead td {
        border-bottom: 0.2mm solid black;
        border-right: 0.2mm solid black;
      }
    </style>
  </head>
  <body>
    <div class="box">
      <p>第一次评估结果</p>
      <p>
        说明：员工试用期绩效评估标准，员工绩效结果需达到至少B （B, B+,
        A）,才视为“通过”第一次试用期考核，否则，视为“不通过”第一次试用期考核，员工第一次考核结果还不是最终的试用期考核结果，员工第二次考核为终次评估，结果必须达到至少B，才能转正，考核结果将影响员工是否转正。
      </p>
      <p>等级A 员工工作结果超出期望</p>
      <p>等级B+ 员工部分工作结果超出期望</p>
      <p>等级B 员工工作结果满足期望</p>
      <p>等级B- 员工工作结果部分满足期望</p>
      <p>等级C 员工工作结果不满足期望</p>
      <table
        style="margin-top: 5mm; text-align: center; margin-bottom: 15mm"
        class="no-rowspan-grid"
      >
        <tr>
          <td>
            <strong>序号</strong>
          </td>
          <td>
            <strong>目标</strong>
          </td>
          <td><strong>测量标准</strong></td>
          <td><strong>权重</strong></td>
          <td><strong>评级</strong></td>
        </tr>
        <!-- 表格内容 循环 -->
        <#list stage_score_list.target_score as item> 
        <tr>
          <td>${item_index+1}</td>
          <td>${item.name}</td>
          <td>${item.info}</td>
          <td>${item.weight}%</td>
          <td>${item.final_text}</td>
        </tr>
        </#list>
        <!-- Summary Grade -->
        <tr>
          <td colspan="3" style="text-align: left">绩效等级结果</td>
          <td>100%</td>
          <td>${stage_score_list.count_score_text}</td>
        </tr>
      </table>
      <p>试用期绩效反馈</p>
      <table class="no-rowspan-grid">
        <thead>
          <tr>
            <td><strong>序号</strong></td>
            <td>
              <strong>做得好</strong>
            </td>
            <td><strong>需改进</strong></td>
            <td><strong>改进计划</strong></td>
          </tr>
        </thead>
        <!-- 表格内容 循环 -->
        <tr>
          <td>1</td>
          <td>${stage_score_list.good_job}</td>
          <td>${stage_score_list.no_good_job}</td>
          <td>${stage_score_list.action_plan}</td>
        </tr>
      </table>
<#if act_version != '0' >
      <p>价值观行为展现</p>
      <p>评价说明：</p>
      <p>
        1、价值观考核以事实为依据，实行通关制，只有达到1评级的标准后，才能得到更高评级，评级时需从最低到高逐一判断
      </p>
      <p>2、如果没达到1评级的标准，可计0评级</p>
      <p>3、其中一项评级为0的，则总体评价等级不得超过合格</p>
      <table class="grid">
        <tr style="text-align: center">
          <td style="width: 25mm">
            <strong>理念</strong>
          </td>
          <td>
            <strong>行为准则</strong>
          </td>
          <td style="width: 15mm">
            <strong>评级标准</strong>
          </td>
          <td style="width: 20mm">
            <strong>评级结果</strong>
          </td>
        </tr>
      </table>
      <!-- 表格内容 每五个是一个table -->
      <#list stage_act_values_list as item> 
            <table class="grid"> 
                    <#list item.behavior_list as items> 
                    <tr>
                        <#if items_index % 5 == 0>
                                <td style="width: 25mm; text-align: center" rowspan="5">
                                ${item.concept_name!''}
                                </td>
                        </#if>
                        <td>
                            ${items.behavior_name!''}
                        </td>
                        <td style="width: 15mm; text-align: center">${items.grade!''}</td>
                        <#if items_index % 5 == 0>
                           <td style="text-align: center; width: 20mm" rowspan="5">${item.final_score!''}</td>
                        </#if>
                        </tr>
                    </#list>
            </table>
      </#list>
</#if>
      <div style="display: flex; margin: 5mm 0">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>评估结果</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
            ${stage_score_list.final_result_text}
          </div>
          <div>(通过/不通过)</div>
        </div>
      </div>
      <div style="text-indent: 5mm">
        我已经收到评估结果，且准备好改进，如果我不能改进和发展，我接受自己在这个岗位上工作效率不高，而且我同意让公司考虑不通过我的试用期。
      </div>
      <!-- 员工签字 -->
      <div style="display: flex; margin-bottom: 5mm">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>员工签字 :</span>
          </div>
          <div style="width: 80mm; border-bottom: 1px dashed; margin: 0 2mm">
            <!-- 签字图片 -->
            <#if first_done_staff_sign_url !="">
                <img style="width: 60mm; height: 40mm;" src="${first_done_staff_sign_url}">
            </#if>
          </div>
        </div>
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>日期:</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
          ${first_done_staff_sign_time}
          </div>
        </div>
      </div>
      <!-- 员工上级签字 -->
      <div style="display: flex; margin-bottom: 5mm">
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>员工上级签字:</span>
          </div>
          <div style="width: 80mm; border-bottom: 1px dashed; margin: 0 2mm">
            <!-- 签字图片 -->
            <#if first_done_manager_sign_url !="">
                <img style="width: 60mm; height: 40mm;" src="${first_done_manager_sign_url}">
            </#if>
          </div>
        </div>
        <div style="display: flex">
          <div style="display: flex; align-items: flex-end">
            <span>日期:</span>
          </div>
          <div
            style="
              width: 40mm;
              border-bottom: 1px dashed;
              margin: 0 2mm;
              display: flex;
              align-items: flex-end;
            "
          >
          ${first_done_manager_sign_time}
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
