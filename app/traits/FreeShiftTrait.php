<?php

namespace App\Traits;


use App\Models\backyard\StaffAuditModel;

trait FreeShiftTrait
{
    /**
     * 执行弹性考勤的职位
     * @var
     */
    protected $free_shift_job_title;

    /**
     * 弹性考勤白班时长
     * @var
     */
    protected $free_shift_day_shift_duration;

    /**
     * 弹性考勤夜班时长
     * @var
     */
    protected $free_shift_night_shift_duration;

    /**
     * 宽容时长
     * @var int
     */
    protected $free_shift_attendance_time_cv = 600; //弹性班次时长误差 10分钟


    public function setFreeShiftInfo($free_shift_config)
    {
        $this->free_shift_job_title            = explode(',', $free_shift_config['free_shift_position']);
        $this->free_shift_day_shift_duration   = $free_shift_config['day_shift_duration'];
        $this->free_shift_night_shift_duration = $free_shift_config['night_shift_duration'];
    }

    /**
     * 是否是弹性班次职位
     * @param $job_title
     * @return bool
     */
    public function isFreeShiftJobTitle($job_title): bool
    {
        return in_array($job_title, $this->free_shift_job_title);
    }

    /**
     * 根据打卡时间 获取 应打卡时长
     * @param $date
     * @param $job_title
     * @param $attendance_started_at
     * @return float[]|int[]
     */
    public function getFreeShiftAttendanceTimeDuration($date, $job_title, $attendance_started_at): array
    {
        if ($this->isFreeShiftJobTitle($job_title) && !empty($attendance_started_at) && $attendance_started_at != '2999-12-31') {
            //白班
            if (strtotime($attendance_started_at) >= strtotime($date . ' 04:00:00') && strtotime($attendance_started_at) < strtotime($date . ' 16:00:00')) {
                $half_day_attendance_duration_init = ($this->free_shift_day_shift_duration / 2) * 3600 - $this->free_shift_attendance_time_cv;
                $full_day_attendance_duration_init = ($this->free_shift_day_shift_duration) * 3600 - $this->free_shift_attendance_time_cv;
            } else {
                //夜班
                $half_day_attendance_duration_init = ($this->free_shift_night_shift_duration / 2) * 3600 - $this->free_shift_attendance_time_cv;
                $full_day_attendance_duration_init = ($this->free_shift_night_shift_duration) * 3600 - $this->free_shift_attendance_time_cv;
            }
        }
        return [$half_day_attendance_duration_init ?? 4.5*3600, $full_day_attendance_duration_init ?? 9*3600];
    }


    /**
     * 根据上班卡返回班次类型
     * @param $date
     * @param $attendance_started_at
     * @return array
     */
    public function getFreeShiftTypeAndDuration($date, $attendance_started_at): array
    {
        $type = 0;
        $duration = 0;
        if (!empty($attendance_started_at)) {

            if (strtotime($attendance_started_at) >= strtotime($date . ' 04:00:00') && strtotime($attendance_started_at) < strtotime($date . ' 16:00:00')) {
                //白班
                $type = 1;
                $duration = $this->free_shift_day_shift_duration;
            } else {
                //夜班
                $type = 2;
                $duration = $this->free_shift_night_shift_duration;

            }
        }
        return [$type,$duration];
    }

    /**
     * 获取 主播 应工作时长
     *
     * 仅主播职位 使用
     *
     * @param $date
     * @param $attendance_started_at
     * @param $leaveType
     * @return float|int
     */
    public function getLiveHours($date, $attendance_started_at, $leaveType)
    {
        $hour = empty($attendance_started_at) ? null : date('H', strtotime($attendance_started_at));
        //日期是今天 返回当前时间的 如果不是今天 并且没打上班卡 就显示空
        if ($date == date('Y-m-d') && empty($hour)) {
            $hour = date('H');
        }

        $dayHour   = $this->free_shift_day_shift_duration;
        $nightHour = $this->free_shift_night_shift_duration;
        //4-16点 白班
        if ($hour >= 4 && $hour < 16) {
            $shift_hour     = $dayHour;
        } else {
            $shift_hour     = $nightHour;
        }

        if(!empty($leaveType) && in_array($leaveType, [$leaveType == StaffAuditModel::LEAVE_TIME_TYPE_MORNING, StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON])) {
            $shift_hour =  $shift_hour / 2;
        }

        return $shift_hour;
    }


}