<?php

namespace App\Library\Enums;

/**
 * 审批相关
 */
final class ApprovalEnums
{

    const AUDIT_SHOW_TYPE_APPLY = 1;//我申请的
    const AUDIT_SHOW_TYPE_APPROVAL = 2;//我审批的

    //审批类型
    const APPROVAL_TYPE_AT = 1;                        //补卡
    const APPROVAL_TYPE_ICAT = 75;                     //泰国个人代理补卡
    const APPROVAL_TYPE_LE = 2;                        //请假
    const APPROVAL_TYPE_LH = 3;                        //LH(废弃)
    const APPROVAL_TYPE_OVERTIME = 4;                  //加班
    const APPROVAL_TYPE_OVERTIME_OS = 77; //外协加班申请
    const APPROVAL_TYPE_HC = 6;                        //HC
    const APPROVAL_TYPE_WMS = 9;                       //物料
    const APPROVAL_TYPE_BT = 10;                       //出差
    const APPROVAL_TYPE_VEHICLE = 11;                  //车辆里程
    const APPROVAL_TYPE_FLEET = 12;                    //加班车
    const APPROVAL_TYPE_RN = 13;                       //离职
    const APPROVAL_TYPE_OS = 14;                       //外协
    const APPROVAL_TYPE_ASSETS = 16;                   //个人资产
    const APPROVAL_TYPE_REPORT = 17;                   //举报
    const APPROVAL_TYPE_JT = 18;                       //转岗
    const APPROVAL_TYPE_PUBLIC_ASSETS = 19;            //公共资产
    const APPROVAL_TYPE_JT_OA = 20;                    //转岗申请OA渠道
    const APPROVAL_TYPE_FD_NETWORK = 21;               //运营产品-network(废弃)
    const APPROVAL_TYPE_SALARY = 22;                   //薪资审批
    const APPROVAL_TYPE_ATT_BUS = 23;                  //出差打卡
    const APPROVAL_TYPE_HC_BUDGET = 24;                //HC预算
    const APPROVAL_TYPE_FD_SALES_PMD = 25;             //运营产品-sales/Project Managerment(废弃)
    const APPROVAL_TYPE_FD_SHOP = 26;                  //运营产品-shop(废弃)
    const APPROVAL_TYPE_ADJUST_ROLE = 27;              //增减角色审批
    const APPROVAL_TYPE_CLAIMER = 30;                  //网点理赔
    const APPROVAL_TYPE_FUEL = 31;                     //油费补贴
    const APPROVAL_TYPE_YCBT = 32;                     //黄牌项目出差
    const APPROVAL_TYPE_MESSAGE = 33;                  //消息审批
    const APPROVAL_TYPE_PA = 34;                       //kit 处罚申诉
    const APPROVAL_TYPE_OFFER_SIGNATURE = 35;          //offer签字
    const APPROVAL_TYPE_OPR = 36;                      //外协特殊价格
    const APPROVAL_TYPE_GO = 37;                       //外出申请
    const APPROVAL_TYPE_GO_CI = 38;                    //外出打卡审批 go out clock in
    const APPROVAL_TYPE_SAS = 39;                      //网点申请支援 - 目前只有菲律宾在用
    const APPROVAL_TYPE_SASS = 40;                     //员工申请支援网点 - 目前只有菲律宾在用
    const APPROVAL_TYPE_SYSTEM_CS = 41;                //外部审核- 众包
    const APPROVAL_TYPE_ABNORMAL_EXPENSE = 42;         //异常费用
    const APPROVAL_TYPE_ASSET_V2 = 46;                 //OA资产申请
    const APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT = 47; //异常费用-单次运费调整
    const APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST = 48;  //外部审核- 司机黑名单
    const APPROVAL_TYPE_MILEAGE = 49;                  //虚假里程
    const APPROVAL_TYPE_WMS_V2 = 50;                   //OA耗材申请
    const APPROVAL_TYPE_HUB_OS_AT = 52;                //HUB外协补卡
    const APPROVAL_TYPE_HR_PENALTY_APPEAL = 54;        //HR处罚申诉
    const APPROVAL_TYPE_HR_RESUME_EMPLOYMENT = 59;     //员工恢复在职审批
    const APPROVAL_TYPE_HUB_OS_OT = 51;                //HUB外协加班
    const APPROVAL_TYPE_CANCEL_CONTRACT = 63;           // MY 解约申请
    const APPROVAL_TYPE_HIRE_TYPE_CHANGE = 64;          //转型个人代理申请
    const APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT = 68;   //公司解约个人代理申请
    const APPROVAL_TYPE_ADVANCE_FUEL = 69;//预支邮费申请
    const APPROVAL_TYPE_SUSPEND_WORK = 72;//个人代理暂停接单申请
    const APPROVAL_TYPE_SUSPENSION = 80;//停职申请


    const APPROVAL_TYPE_SICK_CERTIFICATE = 73;//泰国病假 材料申请
    const APPROVAL_TYPE_WORK_HOME = 81;//居家办公打卡审批



    //审批状态
    const APPROVAL_STATUS_NO_CREATE = 0;         //未生成审批
    const APPROVAL_STATUS_PENDING = 1;         //待审批
    const APPROVAL_STATUS_APPROVAL = 2;        //审批同意
    const APPROVAL_STATUS_REJECTED = 3;        //审批驳回
    const APPROVAL_STATUS_CANCEL = 4;          //审批撤销
    const APPROVAL_STATUS_TIMEOUT = 5;         //审批超时
    const APPROVAL_STATUS_CANCEL_ROLL_BACK = 6; //撤销发起审批
    const APPROVAL_STATUS_APPLY = 8;    //申请
    const APPROVAL_STATUS_ABANDON = 10;   //审批作废
    const APPROVAL_STATUS_APPROVAL_SUPERIOR = 21; // 上级离职自动转交
    const APPROVAL_STATUS_APPROVAL_EMPTY = 22; // 审批人为空 审批人重复自动审批通过
    const APPROVAL_STATUS_APPROVAL_COUNTERSIGN = 24; // 会签审批通过
    const APPROVAL_STATUS_REJECTED_ROLL_BACK = 23; //驳回到某一节点，非最终驳回
    const APPROVAL_STATUS_APPROVAL_EMPTY_AUTO_REJECT = 25; //审批人为空 审批人重复自动审批驳回



    //审批状态对应的多语言
    public static $approval_status = [
        self::APPROVAL_STATUS_PENDING   => 'audit_status.1',
        self::APPROVAL_STATUS_APPROVAL  => 'audit_status.2',
        self::APPROVAL_STATUS_REJECTED  => 'audit_status.3',
        self::APPROVAL_STATUS_CANCEL    => 'audit_status.4',
        self::APPROVAL_STATUS_TIMEOUT   => 'audit_status.5',
        self::APPROVAL_STATUS_APPLY     => 'flow_audit_action.0',
        self::APPROVAL_STATUS_APPROVAL_SUPERIOR   => 'audit_status.21',
        self::APPROVAL_STATUS_APPROVAL_EMPTY   => 'audit_status.22',
        self::APPROVAL_STATUS_APPROVAL_EMPTY_AUTO_REJECT   => 'audit_status.25',
        self::APPROVAL_STATUS_APPROVAL_COUNTERSIGN   => 'audit_status.24',
        self::APPROVAL_STATUS_REJECTED_ROLL_BACK  => 'audit_status.3',
        self::APPROVAL_STATUS_CANCEL_ROLL_BACK  => 'audit_status.6',
    ];
}
