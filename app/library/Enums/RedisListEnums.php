<?php

namespace App\Library\Enums;


final class RedisListEnums
{
    //公共的前缀，必须使用
    const COMMON_PREFIX = 'list_hcm_';
    //到岗后发送消息的队列
    const REST_STAFF_OFF_DAY_PH_DAY = self::COMMON_PREFIX.'rest_staff_off_day_ph_day';

    //协议消息生成队列
    const AGREEMENT_SIGN_STAFF = self::COMMON_PREFIX.'agreement_sign_staff';
    /**
     * 排班建议 规则变更 导入最终建议人数变动给主管发消息
     */
    const SCHEDULING_SUGGESTION__MESSAGE =self::COMMON_PREFIX.'scheduling_suggestion_message';
    /**
     * 上传排班规则中存在指定日期
     * 统计默认休息日和轮休管理手动设置的休息日，并取消掉未来休息日，直到表格中填写的日期 给主管发动消息
     */
    const SCHEDULING_SUGGESTION__MESSAGE_OFF_CHANGE =self::COMMON_PREFIX.'scheduling_suggestion_message_off_change';
    /**
     * 异步更新排班建议
     */
    const  UPDATE_SCHEDULING_SUGGESTION_NUMBER = self::COMMON_PREFIX.'scheduling_suggestion_number_update';

    //正式工转代理签字协议的消息 消费队列
    const INDEPENDENT_TRANSFER_ASSET_PDF_LIST = self::COMMON_PREFIX.'independent_transfer_asset_pdf_list';
    //正式工转代理清理历史数据
    const INDEPENDENT_TRANSFER_CLEAR_DATA_LIST = self::COMMON_PREFIX.'independent_transfer_clear_data_list';
    const STAFF_SOCIAL_SECURITY_LEAVE_DATE = self::COMMON_PREFIX.'staff_social_security_leave_date';

    /**
     * 异步审批车厢
     */
    const ASYNC_AUDIT_VAN_CONTAINER  = self::COMMON_PREFIX.'sync_audit_container';

    /**
     * svc方法 `sync_release_hold` 改为异步处理，防止Hcm、Hris循环调用、调用链过长
     */
    const ASYNC_RELEASE_HOLD =  self::COMMON_PREFIX.'async_release_hold';

    /**
     * 轮休配置变更取消off day队列
     */
    const WORKDAY_DAILY_SETTING = self::COMMON_PREFIX.'workday_daily_setting';
}