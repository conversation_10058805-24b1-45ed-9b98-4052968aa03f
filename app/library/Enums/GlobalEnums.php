<?php

namespace App\Library\Enums;

/**
 * 全局枚举配置
 * Class enums
 */
final class GlobalEnums

{
    //总部名称
    const HEAD_OFFICE = 'Head Office';
    const HEAD_OFFICE_ID = -1;

    // 系统账号
    const SYSTEM_STAFF_ID = 10000;

    // 初始化与系统相关的默认项
    public static function init()
    {
    }

    /**
     * java网点分类 1: 收派件网点 2: 分拨中心 3:第三方代理 4:揽件网点(市场) 5:收派件网点(shop) 6:加盟商网点 7:大学门店 8:Hub
     * from BI system   app/BLL/SysStoreBLL.php
     * @var string[]
     */
    public static $outletsCategory = [
        1 => 'station',// '收派件网点',
        2 => 'distribution_center', // 分拨中心
        3 => 'agent_station', //第三方代理
        4 => 'market_pickup_station', //揽件网点(市场)
        5 => 'station_shop', //收派件网点(shop)
        6 => 'franchisee_store',  //加盟商网点
        7 => 'university_shop', // 大学门店
        8 => 'hub', //Hub
        9 => 'OS',
        10 => 'bdc', //BDC
        11 => 'fulfillment',
        12 => 'B-Hub',
    ];

    //hr_staff_info bank type
    public static $hris_bank_name = [
        '0'=>'Not Known',
        '1'=>'TMB',
        '2'=>'SCB',
    ];

    // 区域定义
    public static $areas = [
        1 => 'BKK',
        2 => 'Cen+East',
        3 => 'Cen+West',
        4 => 'North',
        5 => 'North East',
        6 => 'South',
        7 => 'Cen',
    ];

    /**
     * hc 招聘负责人模块- 工作地点选选项
     * @var array
     */
    public static $hc_manager_worknode_id = [
        '-1'=> self::HEAD_OFFICE,
        '-2'=>'Onsite',
    ];

    //tp3信息 操作action
    public static $tp3_action = [
        'submit' => 1, //提交
        'reject' => 2, //打回（拒绝）
    ];

    //tp3信息 状态
    public static $tp3_state = [
        'unsubmit' => 1, //待提交
        'submitted'=> 2, //已提交
        'rejected' => 3, //已打回
    ];

    const IS_SUB_DEPARTMENT_YES = 1;
    const IS_SUB_DEPARTMENT_NO = 0;
    const DELETED = 1;//已删除
    const NO_DELETED = 0 ; //未删除
    const ALL_SELECT = 1;//全选
    const NO_ALL_SELECT = 0;//多选
    const ALL_JOB_TITLE_OF_DEPARTMENT = 0;//代表是某部门所有职位
    const FORCE_UPDATE = 1;// 强制更新

    //月份对应英文翻译字段
    const MONTHS_MAPS = [
        1 => "January",
        2 => "February",
        3 => "March",
        4 => "April",
        5 => "May",
        6 => "June",
        7 => "July",
        8 => "August",
        9 => "September",
        10 => "October",
        11 => "November",
        12 => "December"
    ];
    //月份对应MY语缩写翻译字段
    const MY_MONTHS_MAPS_ABBR = [
        1 => "Jan",
        2 => "Feb",
        3 => "Mac",
        4 => "Apr",
        5 => "Mei",
        6 => "Jun",
        7 => "Jul",
        8 => "Ogos",
        9 => "Sep",
        10 => "Okt",
        11 => "Nov",
        12 => "Dis"
    ];

    //月份对应MY语全称翻译字段
    const MY_MONTHS_MAPS = [
        1 => "Januari",
        2 => "Februari",
        3 => "Mac",
        4 => "April",
        5 => "Mei",
        6 => "Jun",
        7 => "Julai",
        8 => "Ogos",
        9 => "September",
        10 => "Oktober",
        11 => "November",
        12 => "Disember"
    ];

    //月份对应MY语全称翻译字段
    const MY_SEX_MAPS = [
        1 => "Encik",
        2 => "Cik",
    ];

    //MY语 数字
    const MY_NUMBER = [
        1 => 'satu',
        2 => 'dua',
        3 => 'tiga',
        4 => 'empat',
        5 => 'lima',
        6 => 'enam',
        7 => 'tujuh',
        8 => 'lapan',
        9 => 'sembilan',
    ];

    //数字
    const NUMBER = [
        1 => 'one',
        2 => 'two',
        3 => 'three',
        4 => 'four',
        5 => 'five',
        6 => 'six',
        7 => 'seven',
        8 => 'eight',
        9 => 'nine',
        10=> 'ten',
        11 => 'eleven',
        12 => 'twelve',
        13 => 'thirteen',
    ];

    /**
     * 币种 th us 人命币 新加坡 my vn id la ph
     * @var array
     */
    public static $currencyMap = [
        1 => 'THB',
        2 => 'USD',
        3 => 'CNY',
        4 => 'SPD',
        5 => 'MYR',
        6 => 'VND',
        7 => 'IDR',
        8 => 'LAK',
        9 => 'PHP',
    ];

    const DATA_DEFAULT = 0; //默认数据
    const DATA_EXTEND = 1; //扩展数据

    //List 页数、每页默认个数
    const DEFAULT_PAGE_NUM = 1; //默认第一页
    const DEFAULT_PAGE_SIZE = 20; //默认每页个数
    const MAX_PAGE_SIZE = 1000; //每页最大个数

    const OPTION_YES = 1; //是
    const OPTION_NO = 2; //否

    const OPTION_BOOL_YES = true; //bool型是
    const OPTION_BOOL_NO = false; //bool型否

    const YES = 1;//是
    const NO = 0;//否

    const FIRST_HALF_MONTH = 1; //上半月
    const SECOND_HALF_MONTH = 2;//下半月
}
