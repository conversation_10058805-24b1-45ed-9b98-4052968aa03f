<?php

const BAHT_TEXT_NUMBERS = array('ศูนย์', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า');
const BAHT_TEXT_UNITS = array('', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน');
const BAHT_TEXT_ONE_IN_TENTH = 'เอ็ด';
const BAHT_TEXT_TWENTY = 'ยี่';
const BAHT_TEXT_INTEGER = 'ถ้วน';
const BAHT_TEXT_BAHT = 'บาท';
const BAHT_TEXT_SATANG = 'สตางค์';
const BAHT_TEXT_POINT = 'จุด';

/**
 * 是否为脚本执行
 * @return bool
 */
function is_cli(){
    return preg_match("/cli/i", php_sapi_name()) ? true : false;
}



function isJson($string) {
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}


/**
 * 验证工号格式
 * 是否为工号
 * @param $staff_info_id
 * @return false|int
 */
function is_staff_info_id($staff_info_id)
{
    return preg_match('/^\d{5,9}$/', $staff_info_id);
}


/**
 * https://github.com/ponlawat-w/php-baht_text
 * Convert baht number to Thai text
 * @param double|int $number
 * @param bool $include_unit
 * @param bool $display_zero
 * @return string|null
 */
function baht_text($number, $include_unit = true, $display_zero = true)
{
    if (!is_numeric($number)) {
        return null;
    }

    $log = floor(log($number, 10));
    if ($log > 5) {
        $millions = floor($log / 6);
        $million_value = pow(1000000, $millions);
        $normalised_million = floor($number / $million_value);
        $rest = $number - ($normalised_million * $million_value);
        $millions_text = '';
        for ($i = 0; $i < $millions; $i++) {
            $millions_text .= BAHT_TEXT_UNITS[6];
        }
        return baht_text($normalised_million, false) . $millions_text . baht_text($rest, true, false);
    }

    $number_str = (string)floor($number);
    $text = '';
    $unit = 0;

    if ($display_zero && $number_str == '0') {
        $text = BAHT_TEXT_NUMBERS[0];
    } else for ($i = strlen($number_str) - 1; $i > -1; $i--) {
        $current_number = (int)$number_str[$i];

        $unit_text = '';
        if ($unit == 0 && $i > 0) {
            $previous_number = isset($number_str[$i - 1]) ? (int)$number_str[$i - 1] : 0;
            if ($current_number == 1 && $previous_number > 0) {
                $unit_text .= BAHT_TEXT_ONE_IN_TENTH;
            } else if ($current_number > 0) {
                $unit_text .= BAHT_TEXT_NUMBERS[$current_number];
            }
        } else if ($unit == 1 && $current_number == 2) {
            $unit_text .= BAHT_TEXT_TWENTY;
        } else if ($current_number > 0 && ($unit != 1 || $current_number != 1)) {
            $unit_text .= BAHT_TEXT_NUMBERS[$current_number];
        }

        if ($current_number > 0) {
            $unit_text .= BAHT_TEXT_UNITS[$unit];
        }

        $text = $unit_text . $text;
        $unit++;
    }

    if ($include_unit) {
        $text .= BAHT_TEXT_BAHT;

        $satang = explode('.', number_format($number, 2, '.', ''))[1];
        $text .= $satang == 0
            ? BAHT_TEXT_INTEGER
            : baht_text($satang, false) . BAHT_TEXT_SATANG;
    } else {
        $exploded = explode('.', $number);
        if (isset($exploded[1])) {
            $text .= BAHT_TEXT_POINT;
            $decimal = (string)$exploded[1];
            for ($i = 0; $i < strlen($decimal); $i++) {
                $text .= BAHT_TEXT_NUMBERS[$decimal[$i]];
            }
        }
    }

    return $text;
}

function list_to_tree($list, $pk = 'id', $pid = 'pid', $child = '_child', $root = 0)
{
    $tree = array();// 创建Tree
    if (is_array($list)) {
        // 创建基于主键的数组引用
        $refer = array();
        foreach ($list as $key => $data) {
            $refer[$data[$pk]] =& $list[$key];
        }

        foreach ($list as $key => $data) {
            // 判断是否存在parent
            $parentId = $data[$pid]??0;
            if ($root == $parentId) {
                $tree[$data[$pk]] =& $list[$key];
            } else {
                if (isset($refer[$parentId])) {
                    $parent =& $refer[$parentId];
                    $parent[$child][] =& $list[$key];
                }
            }
        }
    }
    return $tree;
}

/**
 * 获取到零时区的时间，默认是泰国东七区转换为零时区
 * @param $date
 * @param string $format
 * @param string $time_zone_num
 * @param string $action
 * @return false|string
 */
function zero_time_zone($date, $format = 'Y-m-d H:i:s', $time_zone_num = '', $action = '-')
{
    $time_zone_num = $time_zone_num ?: env('add_hour', 7);

    return date($format, strtotime($date. $action . $time_zone_num . ' hours'));
}

/**
 * 获取展示时间，默认是零时区转换为泰国时区
 * @param $date
 * @param string $format
 * @param string $time_zone_num
 * @param string $action
 * @return false|string
 */
function show_time_zone($date, $format = 'Y-m-d H:i:s', $time_zone_num = '', $action = '+')
{
    if (!$date) {
        return '';
    }

    $time_zone_num = $time_zone_num ?: env('add_hour', 7);

    return date($format, strtotime($date . $action . $time_zone_num . ' hours'));
}


/**
 * @param $from
 * @param $to
 * @return string
 */
function format_duration($from, $to)
{
    if ($to > $from) {
        $period = $to - $from;
    } else {
        $period = $from - $to;
    }
    $hour = floor($period / 3600);
    $minute = floor(($period - $hour * 3600) / 60);
    $second = $period - $hour * 3600 - $minute * 60;
    $str = '';
    if ($hour > 0) {
        $str .= $hour . 'h ';
    }
    if ($minute > 0) {
        $str .= $minute . 'm ';
    }
    if ($second) {
        $str .= $second . 's ';
    }
    $str = empty($str) ? '1 m' : trim($str);
    return $str;
}

function registerStream()
{
    $existed = in_array("var", stream_get_wrappers());
    if ($existed) {
        stream_wrapper_unregister("var");
    }
    stream_wrapper_register("var", "App\Library\VarStream");
}

function filter_param($array){
    if(empty($array))
        return $array;
    if(!is_array($array))
        return addcslashes(stripslashes($array),"'");
    else{
        foreach ($array as $key => $value) {
            if (!is_array($value)) {
                $value = addcslashes(stripslashes($value),"'");
                $array[$key] = $value;
            } else {
                filter_param($array[$key]);
            }
        }
    }
    return $array;
}
//验证是否 合法url地址
function validation_url($url = ''){
    if(empty($url)){
        return null;
    }
    if (filter_var($url, FILTER_VALIDATE_URL) === false) {
        return false;
    }
    return true;
}

// 清除多维数组元素两边的空白字符
if (!function_exists('trim_array')) {
    function trim_array ($input)
    {
        if (!is_array($input)) {
            return trim($input);
        }

        return array_map('trim_array', $input);
    }
}

// 多维数组按指定字段排序
if (!function_exists('array_sort')) {
    /**
     * 二维数组根据某个字段排序
     * @param array $array 要排序的数组
     * @param string $keys   要排序的键字段
     * @param mixed $sort  排序类型  SORT_ASC     SORT_DESC
     * @return array 排序后的数组
     */
    function array_sort(array $array, string $keys, $sort = SORT_DESC) {
        $keys_value = [];
        foreach ($array as $k => $v) {
            $keys_value[$k] = $v[$keys];
        }

        array_multisort($keys_value, $sort, $array);
        return $array;
    }
}

/**
 * need molten.so
 * molten_get_traceid
 */
if(!function_exists('molten_get_traceid')) {
    function molten_get_traceid()
    {
        static $tid;
        if (!empty($tid)) {
            return $tid;
        }
        $tid = uniqid('tid');
        return $tid;
    }
}

if(!function_exists('array_only')){
    function array_only($array,$keys){
        return array_intersect_key($array, array_flip((array) $keys));
    }
}

function getRealIp()
{
    if (isset($_SERVER)) {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $real_ip = 'unknown';
            foreach ($arr as $ip) {
                $ip = trim($ip);

                if ($ip != 'unknown') {
                    $real_ip = $ip;
                    break;
                }
            }
        } else {
            if (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $real_ip = $_SERVER['HTTP_CLIENT_IP'];
            } else {
                if (isset($_SERVER['REMOTE_ADDR'])) {
                    $real_ip = $_SERVER['REMOTE_ADDR'];
                } else {
                    $real_ip = '0.0.0.0';
                }
            }
        }
    } else {
        if (getenv('HTTP_X_FORWARDED_FOR')) {
            $real_ip = getenv('HTTP_X_FORWARDED_FOR');
        } else {
            if (getenv('HTTP_CLIENT_IP')) {
                $real_ip = getenv('HTTP_CLIENT_IP');
            } else {
                $real_ip = getenv('REMOTE_ADDR');
            }
        }
    }

    preg_match('/[\\d\\.]{7,15}/', $real_ip, $online_ip);
    $real_ip = (!empty($online_ip[0]) ? $online_ip[0] : '0.0.0.0');
    return $real_ip;
}

function list_to_tree_v2($data, $pk = 'pid', $parentId = 'group_0')
{
    $node = [];
    foreach ($data as $key => $value) {
        if ($parentId == $value[$pk]) {
            $node[] = [
                'id' => $value['id'],
                'name' => $value['name'],
                'children' => list_to_tree($data, $pk, $value['id']),
            ];
        }
    }
    return $node;
}


/**
 * 整数转英文单词
 * 参考:https://support.office.com/zh-cn/article/%E5%B0%86%E6%95%B0%E5%AD%97%E8%BD%AC%E6%8D%A2%E4%B8%BA%E5%8D%95%E8%AF%8D-a0d166fb-e1ea-4090-95c8-69442cd55d98
 * @param $num
 * @return string
 */
if(!function_exists('num2Word')){

    function num2Word($num,$suffix="Baht"){
        if(empty($num))
            return '';

        $place =[1=>"",2=>" Thousand ",3=>" Million ",4=>" Billion ",5=>" Trillion "];

        $res = "";

        //没有小数
        $num = intval($num);

        $strNum = "".$num;

        $count = 1;

        while(!empty($strNum)){
            $temp =getHundreds(getRight($strNum,3));
            if(!empty($temp)){
                $res = $temp.$place[$count].$res;
            }

            if(strlen($strNum)>3){
                $strNum = substr($strNum,0,strlen($strNum)-3);
            }else{
                $strNum="";
            }

            $count++;
        }
        return $res." ".$suffix;
    }
}

function getDigit($digit){
    $digitArr = ["","One","Two","Three","Four","Five","Six","Seven","Eight","Nine"];
    if(empty($digitArr[$digit])){
        return "";
    }else{
        return $digitArr[$digit];
    }
}
function getTens($tensText){
    $str = "".$tensText;

    $tenArr = [10=>"Ten",11=>"Eleven",12=>"Twelve",13=>"Thirteen",14=>"Fourteen",15=>"Fifteen",16=>"Sixteen",17=>"Seventeen",18=>"Eighteen",19=>"Nineteen"];
    //20-99
    if(empty($tenArr[$tensText])){
        $tyArr = ["","","Twenty","Thirty","Forty","Fifty","Sixty","Seventy","Eighty","Ninety"];
        return $tyArr[$str[0]]." ".getDigit($str[1]);
    }else{
        return $tenArr[$tensText];
    }
}


function getRight($str,$offset){
    $start = strlen($str)-$offset;
    if($start<0){
        $start=0;
    }
    return substr($str,$start);
}


function getHundreds($myNumber){
    $result = "";

    if(empty($myNumber)){
        return "";
    }

    //取后三位
    $temp = getRight($myNumber,3);
    //补0
    $temp = sprintf("%03d", intval($temp));

    if($temp[0]!=0){
        $result .= getDigit($temp[0])." Hundred ";
    }

    if($temp[1]!=0){
        $result.= getTens($temp[1].$temp[2]);
    }else{
        $result.= getDigit($temp[2]);
    }
    return $result;
}



/**
 * TODO 废弃了 请使用 baht_text  
 * 整数转泰语
 * 参考:https://github.com/earthchie/BAHTTEXT.js/blob/master/BAHTTEXT.js
 * @param $num
 * @return string
 */
if(!function_exists('bahttext')){
    function bahttext($num,$suffix="บาทถ้วน"){

        $num = intval($num);
        if(empty($num)){
            //0元
            return "ศูนย์บาทถ้วน";
        }

        $strNum = "".$num;

        //十，百，千,万，十万，百万
        $t = ['', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'];
        //1,2,3,4,5,6,7,8,9
        $n = ['', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า'];

        $len =strlen($strNum);
        $text="";


        if ($len > 7) { // more than (or equal to) 10 millions
            $overflow = substr($strNum,0, $len - 6);
            $remains = substr($strNum,$len-6);
            return str_replace("บาทถ้วน",'ล้าน',bahttext($overflow)) . str_replace('ศูนย์','',bahttext($remains));
        } else {
            for ($i = 0; $i < $len; $i = $i + 1) {
                $digit = intval($strNum[$i], 10);

                if ($digit > 0) {
                    if ($len > 2 && $i === $len - 1 && $digit === 1 && $suffix !== 'สตางค์') {
                        $text .= 'เอ็ด' . $t[$len - 1 - $i];
                    } else {
                        $text .= $n[$digit] . $t[$len - 1 - $i];
                    }
                }
            }
            $text= str_replace('หนึ่งสิบ','สิบ',$text);
            $text= str_replace('สองสิบ','ยี่สิบ',$text);
            $text= str_replace('สิบหนึ่ง','สิบเอ็ด',$text);

            return $text .$suffix;
        }
    }
	
	
	if(!function_exists('get_sys_timezone')){
		/**
		 * 获取系统设置时区
		 * @return int|mixed|string
		 */
		function get_sys_timezone(){
			return env('timeZone','+07:00');
		}
	}

	if(!function_exists('getMilliSecond')){
        function getMilliSecond() {
            [$msec, $sec] = explode(' ', microtime());
            return  (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
        }
	}
	
}


/**
 * 获取enums类静态属性值
 */
if (!function_exists('get_enums_class_status_attribute')) {
    function get_enums_class_status_attribute($name)
    {
        $country_code = ucfirst(strtolower(env('country_code', 'th')));
        if ($country_code == 'Th'){
            $enums_class = new \App\Library\Enums();
        }else{
            $enums_namespace = ' App\Modules\\'.$country_code.'\Library\Enums\enums';
            $enums_class = new $enums_namespace;
        }
        return $enums_class::$$name;
    }
}

if (!function_exists('get_country_code')) {
    function get_country_code() {
        return strtoupper(env('country_code', 'TH'));
    }
}

/**
 * 判断国家
 */
if (!function_exists('isCountry')) {
    function isCountry($country = 'TH'): bool
    {
        if (is_array($country)) {
            return in_array(strtolower(env('country_code')), array_map('strtolower', $country));
        }
        return strtolower(env('country_code', 'TH') ) == strtolower($country);
    }
}

/**
 * 获取国家service路径
 */
if (!function_exists('getCountryServicePath')) {
    function getCountryServicePath($name): string
    {
        $countryCode = ucfirst(env('country_code'));
        $path = 'App\Modules\\' . $countryCode . '\Services\\' . $name;
        return class_exists($path) ? $path : '';
    }
}
/**
 * 清除换行 空格 等字符
 */
if (!function_exists('clearSpecialChar')) {
    function clearSpecialChar($str): array
    {
        $result = [];
        if (empty(trim($str))) {
            return [];
        }
        $clear = trim(preg_replace("/(\s+|\,|，)/", " ", $str));
        $arr = explode(' ', $clear);

        foreach ($arr as $item) {
            $staff_id = trim($item);
            $result[] = $staff_id;
        }
        return $result;
    }
}
/**
 * 获取国家默认语言
 * @return string
 */
if (!function_exists('getCountryDefaultLang')) {
    function getCountryDefaultLang(): string
    {
        switch (strtolower(env('country_code'))){
            case 'th':
                $lang = 'th';
                break;
            case 'ph':
            case 'my':
            case 'id':
            case 'la':
                $lang = 'en';
                break;
            case 'vn':
                $lang = 'vi';
                break;
            default:
                $lang = 'en';
        }
        return $lang;
    }
}

if (!function_exists('implode_array_keys')) {
    function implode_array_keys($array = [])
    {
        return implode(',',array_keys($array));
    }
}

if (!function_exists('formatHrDate')) {
    function formatHrDate($date): string
    {
        return  $date ? substr($date,0,10) : '';
    }
}
/**
 * 根据 0.5 取模  大于n.5 取 n.5 小于n.5 大于n 取n
 *
 */
if(!function_exists('half_num'))
{
    function half_num($num)
    {
        $have_days = round($num,2);//看小数点 第二位 四舍五入
        $int_day   = floor($have_days);//2

        $return = $int_day;
        if(bccomp($have_days,$int_day + 0.5,2) >= 0)
            $return = $int_day + 0.5;

        return $return;
    }
}

/**
 * @description:把$class 替换为当前国家下$class 没有的话 返回当前 $class
 * @param null
 * @return     :
 * <AUTHOR> L.J
 * @time       : 2022/6/30 14:48
 */
if (!function_exists('reBuildCountryInstance')) {

    function reBuildCountryInstance($class, $args = [])
    {
        if (is_object($class)) {
            $reflect = new \ReflectionClass($class);
            if (
                strpos($reflect->getName(), ucfirst(strtolower(env('country_code', 'TH')))) === false
            ) {
                $newClass = str_replace("App\\",
                        "App\\Modules\\" . ucfirst(strtolower(env('country_code', 'TH'))) . "\\",
                        $reflect->getNamespaceName()) . "\\" . $reflect->getShortName();
                if (class_exists($newClass)) {

                    $newReFlection = new \ReflectionClass($newClass);
                    if ($newReFlection->inNamespace() && $newReFlection->isInstantiable()) {

                        return $newReFlection->newInstanceArgs($args);
                    }
                }
            }

            return $class;
        } else {
            throw new \Exception('不是一个实例化对象');
        }
    }
}
/**
 * 字符串补位
 */
if (!function_exists('strFillIn')) {
    /**
     * @param $str
     * @param string $fill 要补的字符
     * @param int $number 至多位数
     * @param string $position 补位的位置 l 左 r 右
     * @param null $positionNumber 固定位数
     * @return string
     */
    function strFillIn($str,$fill = '',$number = 0,$position='l',$positionNumber = null): string
    {
        $fillIn = count(mb_str_split($str));
        if ($str === '') {
            $fillIn = 0;
        }
        if ($position == 'l') {
            $str = str_repeat($fill,max($number-$fillIn,0)).$str;
        } else {
            $str = $str.str_repeat($fill,max($number-$fillIn,0));
        }
        return mb_substr($str,0,$number,'UTF-8');
    }
}

//批量 多数字 求和
if (!function_exists('bcAddBatch')) {
    function bcAddBatch($numbers, $scale = 2)
    {
        if ($numbers && count($numbers) >= 2) {
            $return = '0';
            foreach ($numbers as $num) {
                $return = bcadd($num, $return, $scale);
            }
            return $return;
        }

        return bcadd('0', $numbers[0] ?? 0, $scale);
    }
}

/**
 * @param null $dt
 * @param string $format
 * @return string
 * @throws Exception
 */
function localToUtc($dt=null,$format='Y-m-d H:i:s')
{
    $tz = date_default_timezone_get();
    if (is_null($dt)){
        $dt = 'now';
    }
    $dt = new DateTime($dt, new DateTimeZone($tz));
    $dt->setTimezone(new DateTimeZone('UTC'));
    return $dt->format($format);
}

/**
 * @param null $dt
 * @param string $format
 * @return string
 * @throws Exception
 */
function utcToLocal($dt=null,$format='Y-m-d H:i:s')
{
    $tz = date_default_timezone_get();
    if (is_null($dt)){
        $dt = 'now';
    }
    $dt = new DateTime($dt, new DateTimeZone('UTC'));
    $dt->setTimezone(new DateTimeZone($tz));
    return $dt->format($format);
}

/**
 * 尾部固定填充字符
 */
if(!function_exists('fixedEndFillingChar')) {
    function fixedEndFillingChar($str, $char, $len): string
    {
        if (mb_strlen($str) < $len) {
            return $str . $char;
        }
        return mb_substr($str, 0, $len - 1) . $char;
    }
}

/**
 * 转译mysql通配符
 */
if(!function_exists('customEscapeWildcards')) {
    function customEscapeWildcards($str)
    {
        $str = str_replace('%', '\%', $str);
        $str = str_replace('_', '\_', $str);
        return $str;
    }
}

/**
 * 打印数据
 */
if (!function_exists('dd')) {
    function dd(...$args)
    {
        if (function_exists('dump')) {
            dump(...$args);
        } else {
            var_dump(...$args);
        }
        die;
    }
}



/**
 * 验证坐标经纬度 是否超出范围  true 超出范围  false 符合条件
 * 最多验证小数点 后50位
 * @param $lat
 * @return boolean
 */
function checkCoordinateLat($lat){
    //验证 维度 -90 到 90
    if(bccomp('-90' , $lat,50) > 0
        || bccomp($lat , '90',50) > 0
    ){
        return true;
    }
    return false;
}

function checkCoordinateLng($lng){
    //验证 经度 -180 180
    if(bccomp('-180' , $lng,50) > 0
        || bccomp($lng , '180',50) > 0
    ){
        return true;
    }
    return false;
}

//验证 excel 的日期对应年 是否合法
function checkYear($year){
    if($year == 1970 || $year < 2000 || $year > 2999){
        return false;
    }
    //在正确年期间
    return true;
}

//坐标 格式化成小数点后8位
function formatCoordinate($float)
{
    if (empty($float)) {
        return '0';
    }
    $float = (string)$float;
    $arr   = explode('.', $float);
    return $arr[0] . '.' . str_pad(substr($arr[1], 0, 8), 8, '0');
}

/**
 * 字符串提取数字
 * @param $str
 * @return string
 */
function strOnlyNumber($str): string
{
    $preg = "/\d/is";
    preg_match_all($preg, $str, $arr);
    if (isset($arr[0])) {
        return implode('', $arr[0]);
    }
    return '';
}

/**
 * 字符串提取尾部数字
 * @param $str
 * @return string
 */
function strOnlyEndNumber($str): string
{
    $pattern = '/(\d+)$/';
    preg_match($pattern, $str, $matches);
    if ($matches) {
        return $matches[1];
    }
    return '';
}
/*
 *
 * 过滤字符串中特殊字符
 *
 * 该方法过滤的特殊字符都是会导致php报错的特殊字符，严格慎用过滤其他字符！！！
 *
替换的字符要和要过滤 字符 数组的索引值对应
 */
if (!function_exists('nameSpecialCharsReplace')) {
    function nameSpecialCharsReplace($name)
    {
        if(empty($name)){
            return  $name;
        }
        $search_arr = ["​", " "]; //要过滤的特殊字符,字符中有内容（一个红点）只是不可见，不要动
        $replace_arr = [''];//特殊字符对应的替代字符
        return str_replace($search_arr,$replace_arr,$name);
    }
}

/**
 * 计算两个经纬度之间的距离 返回单位米
 */
if (!function_exists('calculateDistance')) {
    function calculateDistance($latitude1, $longitude1, $latitude2, $longitude2) {
        // 将经纬度转换为弧度
        $latitude1_rad = deg2rad($latitude1);
        $longitude1_rad = deg2rad($longitude1);
        $latitude2_rad = deg2rad($latitude2);
        $longitude2_rad = deg2rad($longitude2);

        // 计算地球半径（平均半径）
        $earthRadius = 6371;

        // 使用 Haversine 公式计算距离
        $distance = acos(sin($latitude1_rad) * sin($latitude2_rad) + cos($latitude1_rad) * cos($latitude2_rad) * cos($longitude2_rad - $longitude1_rad)) * $earthRadius;

        // 单位米
        return intval($distance * 1000);
    }
}


/**
 *
 * @param $arr
 * @return mixed
 */
function combination($arr){
    if(count($arr) >= 2){
        $tmp_arr = array();
        $arr1 = array_shift($arr);
        $arr2 = array_shift($arr);
        foreach($arr1 as $k1 => $v1){
            foreach($arr2 as $k2 => $v2){
                $tmp_arr[] = $v1.$v2;
            }
        }
        array_unshift($arr, $tmp_arr);
        $arr = combination($arr);
    }else{
        return $arr;
    }
    return $arr;
}

//把日期转换成 星期对应日期的数组
function formatDateWeek($dateList): array
{
    $return = [];
    if(empty($dateList)){
        return $return;
    }

    foreach ($dateList as $date){
        $key = date('w', strtotime($date));
        if($key == 0){
            $key = 7;
        }
        $return[$key][] = $date;
    }
    return $return;
}




//获取 对应月份的所有日期 list
function getDateByMonth($month){
    $start = date('Y-m-01',strtotime($month));
    $end = date('Y-m-d', strtotime("{$month} last day of"));//每月最后一天
    $list = [];
    while ($start <= $end){
        $list[] = $start;
        $start = date('Y-m-d',strtotime("{$start} +1 day"));
    }

    return $list;

}
//根据列数 获取对应excel 列名
function get_excel_column($number){
    $columnName = '';
    if(empty($number) || $number <= 0){
        return $columnName;
    }
    while ($number > 0) {
        $modulo = ($number - 1) % 26;
        $columnName = chr(65 + $modulo) . $columnName;
        $number = (int)(($number - $modulo) / 26);
    }
    return $columnName;
}

/**
 * 链接数组keys
 */
if (!function_exists('implode_array_keys')) {
    function implode_array_keys($array = [])
    {
        return implode(',',array_keys($array));
    }
}

/**
 * 获取薪酬计算范围时间格式
 * @param string $date
 * @return string[]
 */
function getSalaryCycleFormat(string $date = ''): array
{
    $startTime = $endTime = '';
    if (in_array(get_country_code(), ['TH', 'MY', 'ID'])) {
        $startTime = 'Y-m-24';
        $endTime   = 'Y-m-23';
    }
    if (in_array(get_country_code(), ['LA', 'VN'])) {
        $startTime = 'Y-m-01';
        $endTime   = 'Y-m-t';
    }
    if (isCountry('PH')) {
        if (date('d', strtotime($date)) > 15) {
            $startTime = 'Y-m-16';
            $endTime   = 'Y-m-t';
        } else {
            $startTime = 'Y-m-01';
            $endTime   = 'Y-m-15';
        }
    }
    return [$startTime, $endTime];
}
/**
 * 字符串填充行
 * @param $str
 * @param $lengthPerElement
 * @param int $arraySize
 * @param string $fillChar
 * @return array
 */
function splitAndFillString($str, $lengthPerElement, $arraySize = 3, $fillChar = ' '): array
{
    // 计算所需的总字符数
    $totalLengthNeeded = $lengthPerElement * $arraySize;
    // 截取或填充字符串以匹配总字符数
    $formattedStr = str_pad(mb_substr($str, 0, $totalLengthNeeded), $totalLengthNeeded, $fillChar);
    // 初始化结果数组
    $resultArray = [];
    // 分割字符串并填充一维数组
    for ($i = 0; $i < $arraySize; $i++) {
        $startPos      = $i * $lengthPerElement;
        $resultArray[] = mb_substr($formattedStr, $startPos, $lengthPerElement);
    }
    return $resultArray;
}

/**
 * 检验日期
 * @param $date
 * @param string $format
 * @return bool
 */
function isValidDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * 获取环境
 */
if (!function_exists('get_runtime')) {
    function get_runtime() {
       return strtolower(RUNTIME);
    }
}

/**
 * 异步导入任务-阿里云地址解析
 */
if (!function_exists('get_oss_info_by_url')) {
    function get_oss_info_by_url($url)
    {
        $oss_info = [
            'bucket_name' => '',
            'object_key' => '',
            'file_name' => '',
        ];
        //取bucket_name 把开头的http://去掉
        $have_http = stripos($url, 'http://');
        $have_https = stripos($url, 'https://');
        $first_point = stripos($url, '.');
        if ($have_http !== false) {
            $start_index = $have_http + mb_strlen('http://');
            $end_index = $first_point - $have_http;
            $oss_info['bucket_name'] = mb_substr($url, $start_index, $end_index);
        } elseif ($have_https !== false) {
            $start_index = $have_https + mb_strlen('https://');
            $end_index = $first_point - $start_index;
            $oss_info['bucket_name'] = mb_substr($url, $start_index, $end_index);
        } else {
            $oss_info['bucket_name'] = mb_substr($url, 0, $first_point);
        }
        //取object_key, 取第一个.com/后边的所有字符
        $bucket_name_index = stripos($url, '.com/');
        $oss_info['object_key'] = mb_substr($url, $bucket_name_index + mb_strlen('.com/'));
        //拼接file_name, 使用md5后的object_key, 再拼上原来的后缀
        $file_name_str = md5($oss_info['object_key']);
        $oss_info['file_name'] = 'import_' . $file_name_str;
        $extension = strchr($oss_info['object_key'], '.');
        $oss_info['file_name'] = $oss_info['file_name'] . $extension;
        return $oss_info;
    }
}

/**
 * 签名图转编码
 * @param string $img
 * @param bool $imgHtmlCode
 * @return string
 */
if (!function_exists('img_base64_encode')) {
    function img_base64_encode($img_url): string
    {
        //获取文件内容
        $file_content = file_get_contents($img_url);
        if ($file_content === false) {
            return '';
        }

        $imageInfo = getimagesize($img_url);
        $prefix    = 'data:'.$imageInfo['mime'].';base64,';

        return $prefix.chunk_split(base64_encode($file_content));
    }
}

/**
 * 数组根据指定字段分组
 */
if (!function_exists('array_group_by_column')) {
    function array_group_by_column($array, $column = ''): array
    {
        if (empty($column)) {
            return [];
        }
        $grouped = [];
        foreach ($array as $item) {
            $category = $item[$column] ?? null;

            if (empty($category)) {
                continue;
            }

            // 如果分类不存在，初始化空数组
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            // 将当前元素添加到对应分类
            $grouped[$category][] = $item;
        }
        return $grouped;
    }
}

