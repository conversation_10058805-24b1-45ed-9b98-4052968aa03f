<?php

namespace App\Models\backyard;


use App\Models\backyard\BackyardBaseModel;

class BusinessTripModel extends BackyardBaseModel
{
    protected $table_name = 'business_trip';

    public static $business_trip_type_approval_type = [
        self::BTY_NORMAL   => 10,//出差
        self::BTY_DOMESTIC => 10,//出差
        self::BTY_FOREIGN  => 10,//出差
        self::BTY_YELLOW   => 32, //黄牌出差
        self::BTY_GO_OUT   => 37, //外出
    ];

    //business_trip_type 请注意 目前  普通出差 国内国外出差 为一个审批流   黄牌出差是一个  外出申请 是一个
    // 1234 走 enums::$audit_type['ATB'] 出差外勤打卡    5走 外出打卡审批 enums::$audit_type['GOC']
    const  BTY_NORMAL = 1;//普通出差 历史类型
    const  BTY_YELLOW = 2;//黄牌出差
    const  BTY_DOMESTIC = 3;//国内出差
    const  BTY_FOREIGN = 4;//国外出差
    const  BTY_GO_OUT = 5; //外出申请
    const WORK_FROM_HOME = 9;//居家办公暂时放出差打卡表里 类型是 9对应 打卡表状态是9


    //出差类型翻译key
    public static $tripKey = [
        self::BTY_NORMAL   => 'trip_normal',//出差
        self::BTY_YELLOW   => 'trip_yellow',//黄牌出差
        self::BTY_DOMESTIC => 'trip_domestic',//国内
        self::BTY_FOREIGN  => 'trip_foreign', //国外
        self::BTY_GO_OUT   => 'trip_out', //外出
        self::WORK_FROM_HOME   => 'work_home_attendance', //居家办公打卡
    ];

}