<?php

namespace App\Models\backyard;

class HoldStaffManageModel extends BackyardBaseModel
{
    public $table_name = 'hold_staff_manage';


    const HOLD_TYPE_NO = 0;//不hold
    const HOLD_TYPE_WAGES = 1;//工资
    const HOLD_TYPE_PERCENTAGE = 2;//提成
    const HOLD_TYPE_WAGES_PERCENTAGE = 3;//工资+提成

    const HOLD_HANDLE_PROGRESS_PROCESSED = 3;//已处理
    const HOLD_RELEASE_STATE_TODO = 1;//待确认

    //是否删除
    const IS_DELETE_YES = 1;//是
    const IS_DELETE_NO  = 0;//否
    
    // 已释放
    const RELEASE_STATE_DONE = 2;

    /**
     * hold来源
     */
    const HOLD_SOURCE_ADD_MANUALLY = 1; //手动添加
    const HOLD_SOURCE_HRIS = 2; //HRIS系统
    const HOLD_SOURCE_STAY_AWAY_FROM_WORK = 3; //连续旷工
    const HOLD_SOURCE_FAIL_TO_SUBMIT_PUBLIC_FUNDS = 4; //未缴纳公款
    const HOLD_SOURCE_BY_APPLY = 5;//by申请离职
    const HOLD_SOURCE_BATCH_IMPORT = 6; //批量导入
    const HOLD_SOURCE_WARNING_MENU = 7; //电子警告书
    const HOLD_SOURCE_CRIMINAL_RECORD = 8; //犯罪记录
    const HOLD_SOURCE_PROBATION_FAILED = 9; //试用期未通过员工
    const HOLD_SOURCE_EHS_CORRUPTION = 10; //EHS贪污案件
    const HOLD_SOURCE_CONTRACT_EXPIRE = 11;//合同终止
    const HOLD_SOURCE_COMPANY_TERMINATION = 13;//公司解约个人代理
    const HOLD_SOURCE_FACE_BLACKLIST = 14;//人脸黑名单
    const HOLD_SOURCE_SUSPENSION_AUDIT = 15;//停职申请


    // 未处理
    const HANDLE_PROGRESS_UNPROCESSED = 1;
    // 处理中
    const HANDLE_PROGRESS_PROCESSING = 2;

    //是否处理hold
    const IS_HANDLE_HOLD_NO = 0; //否
    const IS_HANDLE_HOLD_YES = 1; //是


    public static $hold_type = [
        self::HOLD_TYPE_NO               => 'warning_hold_type_1',
        self::HOLD_TYPE_WAGES            => 'warning_hold_type_2',
        self::HOLD_TYPE_PERCENTAGE       => 'warning_hold_type_3',
        self::HOLD_TYPE_WAGES_PERCENTAGE => 'warning_hold_type_4',
    ];


}