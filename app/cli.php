#!/usr/bin/env php
<?php

use App\EventsListener\TaskListener;
use App\Library\Exception\ListenerException;
use Phalcon\Cli\Console;

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

//为了引入env函数
require BASE_PATH . '/vendor/autoload.php';

ini_set('memory_limit', '-1');

/**
 * 加载环境变量
 */
$dotenv = new Dotenv\Dotenv(BASE_PATH);
$dotenv->load();

/**
 * 
 * The FactoryDefault Dependency Injector automatically registers the services that
 * provide a full stack framework. These default services can be overidden with custom ones.
 */
/**
 * Include Services
 */
include APP_PATH . '/config/services_cli.php';


// 设置运行环境变量
$runtime = env('runtime', 'dev');

define('RUNTIME', $runtime);
date_default_timezone_set(env('timeZoneGMT', 'Asia/Bangkok'));

/**
 * Get config service for use in inline setup below
 */
$config = $di->get("config");

/**
 * Include Autoloader
 */
include APP_PATH . '/config/loader.php';


try {
    /**
     * Create a console application
     */

    $console = new Console($di);

    $modules = require APP_PATH . '/config/modules.php';
    /**
     * 国家模块
     */
    $console->registerModules($modules);

    /**
     * 处理console应用参数
     */
    $arguments           = [];
    $arguments['config'] = $config;
    $arguments['module'] = $countryCode;
    foreach ($argv as $k => $arg) {
        if ($k === 1) {
            $arguments["task"] = $arg;
        } elseif ($k === 2) {
            $arguments["action"] = $arg;
        } elseif ($k >= 3) {
            $arguments["params"][] = $arg;
        }
    }
    /**
     * 默认现实command列表
     */
//    if(count($argv) == 1){
//        $arguments["task"] = 'main';
//        $arguments["action"] = 'main';
//    }

    //让task里 可以使用 $this->console->handle
    $di->setShared("console", $console);


    $taskListener = new TaskListener();
    //extracts default events manager
    $eventsManager = new \Phalcon\Events\Manager();
    //attaches new event console:beforeTaskHandle and console:afterTaskHandle
    $eventsManager->attach(
        'console:beforeHandleTask', $taskListener->beforeHandleTask($argv)
    );
    $eventsManager->attach(
        'console:afterHandleTask', $taskListener->afterHandleTask($argv)
    );
    $console->setEventsManager($eventsManager);


    /**
     * Handle
     */
    $console->handle($arguments);

    /**
     * If configs is set to true, then we print a new line at the end of each execution
     *
     * If we dont print a new line,
     * then the next command prompt will be placed directly on the left of the output
     * and it is less readable.
     *
     * You can disable this behaviour if the output of your application needs to don't have a new line at end
     */
    if (isset($config["printNewLine"]) && $config["printNewLine"]) {
        echo PHP_EOL;
    }
} catch (\Throwable $e) {
    $di = $console->getDI();
    //错误日志
    $log              = [
        'code' => $e->getCode(),
        'msg'  => $e->getMessage(),
    ];
    $exception_logger = $di->get('logger');
    echo $e->getMessage() . PHP_EOL;
    if ($e instanceof ListenerException) {
        $exception_logger->info(json_encode($log, JSON_UNESCAPED_UNICODE));
    } else {
        $log = array_merge($log, [
            'file'  => $e->getFile(),
            'line'  => $e->getLine(),
            'trace' => $e->getTraceAsString(),
        ]);
        echo $e->getTraceAsString() . PHP_EOL;
        $exception_logger->error(json_encode($log, JSON_UNESCAPED_UNICODE));
        exit(255);
    }
}

