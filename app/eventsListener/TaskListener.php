<?php

namespace App\EventsListener;

use BaseTask;
use Phalcon\CLI\Console;
use Phalcon\CLI\Dispatcher;
use Phalcon\Events\Event;


/**
 * Class TaskListener
 */
class TaskListener
{
    /**
     * Event fired before task is handling
     *
     * @param $argv
     * @return callable
     */
    public function beforeHandleTask($argv)
    {
        return function (Event $event, Console $console, Dispatcher $dispatcher) use ($argv) {
            if (class_exists($dispatcher->getHandlerClass())) {
                $class = $dispatcher->getHandlerClass();
            } else {
                $class = str_replace('App\Modules\\' . ucfirst(env('country_code')) . '\Tasks', '',
                    $dispatcher->getHandlerClass());
            }
            $annotations = $console->annotations->getMethod(
                $class,
                $dispatcher->getActiveMethod()
            );
            if ($annotations->has('Single') || $annotations->has('single')) {
                BaseTask::checkTaskIsFinish(date('Y-m-d'),
                    BaseTask::getTaskCode($dispatcher->getTaskName(), $dispatcher->getActionName()));
                return true;
            }
            $class = str_replace('App\Modules\\' . ucfirst(env('country_code')) . '\Tasks', '',
                $dispatcher->getHandlerClass());

            if (!class_exists($class)) {
                return true;
            }
            $annotations = $console->annotations->getMethod(
                $class,
                $dispatcher->getActiveMethod()
            );
            if ($annotations->has('Single') || $annotations->has('single')) {
                BaseTask::checkTaskIsFinish(date('Y-m-d'),
                    BaseTask::getTaskCode($dispatcher->getTaskName(), $dispatcher->getActionName()));
                return true;
            }
        };
    }

    /**
     * Event fired after task handle
     *
     * @return callable
     */
    public function afterHandleTask($argv)
    {
        return function (Event $event, Console $console) use ($argv) {
            $dispatcher = $console->dispatcher;
            if (class_exists($dispatcher->getHandlerClass())) {
                $class = $dispatcher->getHandlerClass();
            } else {
                $class = str_replace('App\Modules\\' . ucfirst(env('country_code')) . '\Tasks', '',
                    $dispatcher->getHandlerClass());
            }

            $annotations = $console->annotations->getMethod(
                $class,
                $dispatcher->getActiveMethod()
            );
            if ($annotations->has('Single') || $annotations->has('single')) {
                BaseTask::createTaskLog(date('Y-m-d'),
                    BaseTask::getTaskCode($dispatcher->getTaskName(), $dispatcher->getActionName()));
            }
        };
    }
}
