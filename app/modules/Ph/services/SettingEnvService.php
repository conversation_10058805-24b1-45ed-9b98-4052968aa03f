<?php

namespace App\Modules\Ph\Services;

use App\Services\SettingEnvService as BaseSettingEnvService;


class SettingEnvService extends BaseSettingEnvService
{
    //菲律宾国家独有的配置项
    protected $country_key_list = [
        'WinHR' => [
            'individual_contractor_jobids' => '个人代理员工可用JD',
            'employee_jobids'              => '正式员工quick offer可用JD',
            'monthly_employee_jobids'      => '月薪制合同工quick offer可用JD',
            'winhr_th_head_office_no_like_job'       => '网络TA招聘职位',
            'h5_th_head_office_no_like_job'          => '网络TA招聘岗位JD',
        ],
        'HRIS' => [
            'xz_ticket_add_power'                       => '行政工单权限,格式：JSON，(部门、职位、工号多个时以西文逗号分隔)',
            'support_staff_apply_job_title_config'      => 'HCM导入支援员工：配置可支援职位创建子账号的默认角色{"可支援职位id(一个)":[默认角色id(多个英文,分隔)]}',
            'support_job_title_role_config'             => 'HCM导入支援员工：配置允许被添加为支援员工的职位及其可支援的职位{"可添加职位id(一个)":[可支援职位id(多个英文,分隔)]}',
            'import_staff_support_template'             => 'HCM导入支援员工：配置导入模板文件地址',
            'store_support_manage_job_title'            => 'HCM导入支援需求：配置允许申请的支援职位【支援职位id】',
            'store_support_manage_import_template'      => 'HCM导入支援需求：配置导入模板文件地址',
            'awol_statistics_send_email_department_ids' => '每日AWOL发送情况统计,需要统计发送部门id(多个英文,分隔)',
            'awol_statistics_send_email_address'        => '每日AWOL发送情况统计,统计信息接收邮箱(多个英文,分隔)',
            'individual_contractor_jobids'              => '个人代理员工可用JD',
            'day_shift_duration'                        => '弹性打卡 白班小时数',
            'night_shift_duration'                      => '弹性打卡 夜班小时数',
            'free_shift_position'                       => '执行弹性班次的职位ID',
            'sign_warning_witness_list_default_phnw'     => '【警告信】Network默认证人工号',
            'sign_warning_not_default_witness_staff_ids' => '【警告信】不作为默认证人的工号',
            'sign_warning_automatic_transfer_days'       => '【警告信】被警告人不签字自动转交天数',
            'renew_contract_send_button'                 => '【离职管理】个人代理重发续约确认按钮权限的工号',
            'shift_supervisor_job_title'                 => '只能通过轮休管理设置班次的职位',
            'TruckUpdate'                                => '配置中的工号可以修改Truck driver（Night）【1675】排班',
            'update_staff_department_by_org'             => '部门自动变更失败通知-邮箱',
            'hub_ocw_attendance_job_title_config'        => '计算Hub OCW考勤的外协职位id（英文逗号,分隔）',
            'Misjudgment_limit'                          => '人脸黑名单-命中记录生成后可误判的最晚天数，默认7天',
            'White_List_SetFreeRest'                     => '不卡设置轮休下班打卡白名单',
            'SetFreeRest_Position'                       => '需要限制自由轮休设置天数的职位ID',
            'blackface_os_reason'                        => '同步人脸黑名单的外协黑名单原因id',
            'UnrestrictedSetRest'                        => '不受自由轮休设置限制白名单',
            'support_staff_limit_join_days'              => '限制主账号原职位入职后N天后可以开始支援的天数，[职位多个之间,分隔]|天数 例如：[110,13]|3,[37]|1',
            'ffm_ocw_attendance_job_title_config'        => '计算ffm考勤的外协职位id（英文逗号,分隔）',
        ],
        'Backyard' => [
            'lnflexible_attendance_departments'        => '菲律宾弹性考勤剔除部门 ID(包含子部门)',
            'lnflexible_attendance_jobs'               => '菲律宾弹性考勤剔除职位ID',
            'outsource_field_punch_store'              => '外协员工可外勤网点ID',
            'outsource_field_punch_division_authority' => '外协员工可外勤的分支机构ID',
            'interior_free_buy_permission_set'         => '新入职员工可购买免费工服的员工属性配置',
            'system_external_approval_white_list'      => '众包设置网点白名单，以逗号形式分割如 “PH000001,PH000002”',
            'nw_ot_hour'                               => 'Network Management部门下DC Officer和DC Supervisor职位可申请的超时加班时长',
            'efficiency_of_sp_branches'                => 'SP网点DC Officer和DC supervisor人效标准值',
            'efficiency_of_pdc_branches'               => 'PDC网点DC Officer和DC supervisor人效标准值',
            'efficiency_of_bdc_branches'               => 'NW部门BDC网点DCO、DCS、ADCS人效标准值',
            'off_day_ot_hours'                         => 'NW部门Van Courier、Bike Courier、Tricycle Courier、DC Officer、 DC Supervisor职位可选择的休息日1.3倍OT时长',
            'courier_ot_limitation'                    => 'NW部门Van Courier、Bike Courier、Tricycle Courier自然月累计可申请的休息日1.3倍OT总时长',
            'dco_ot_limitation'                        => 'NW部门DC Officer、 DC Supervisor自然月累计可申请的休息日1.3倍OT总时长',
            'fleet_apply_roles'                       => '加班车申请角色,多个角色以西文逗号进行分隔',
            'administrative_work_order_staff_id'      => '行政工单 指定工号(英文逗号分割)',
            'administrative_work_order_job_title'     => '行政工单 指定职位(英文逗号分割)',
            'administrative_work_order_department_id' => '行政工单 指定所属部门(英文逗号分割)',
            'wpd_manage_department_id'                => '行政工单 Warehouse Procurement Department管辖部门',
            'individual_contractor_job_title'         => '个人代理可选择职位(英文逗号分割)',
            'monthly_contract_employees_job_title'    => '月薪制特殊合同工的职位ID(英文逗号分割)',
            'enable_transition_individual_contractor_job_title' => '可转型为个人代理的职位(英文逗号分割)',//转型
            'quick_offer_position'                          => '使用Quick offer的职位ID',
            'canleavetoday'                                 => '是否可以今天请假，1代表可以申请今天，0代表不能申请今天',
            'facial_verify_face_check'                      => '开启数据安全人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_used_device'                     => '开启常用设备人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_new_wifi'                        => '开启新增WiFi人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_token_expiration_date'           => '登录X天后自动过期，需重新登录刷脸。默认值30',
            'facial_verify_captcha'                         => '开启BY登录腾讯云验证码，0代表关闭，1代表开启，默认0',
            'facial_verify_salary_whitelist_noverifiy_face' => '发薪不打卡白名单无需验证人脸开关，0关闭，1开启，默认1',
            'facial_verify_whitelist_noverifiy_facee'       => '无需人脸校验工号,默认空',
            'ic_cycle_alive_score'                          => '个人代理2小时作弊检测 静默活体分数值 小数',
            'backyard_check_face_switch'                    => '是否开启人脸校验，默认1：开启；0：关闭',
            'backyard_check_seconds'                        => '间隔校验秒数，默认4小时(7200 * 4 秒)，可填写数字。',
            'backyard_check_face_in_all_store_switch'       => '是否开启底片人脸校验，默认1：开启；0：关闭',
            'backyard_check_position'                       => '个人代理作弊检测职位ID(英文逗号分割)',
            'agent_attendance_punch_card_email'             => '个人代理在打卡，校验到作弊通知邮件组',
            'agent_attendance_person_email'                 => '个人代理账号kit端每2小时人脸校验，校验到作弊通知邮件组',
            'agent_attendance_save_attachment_email'        => '底片校验，校验到作弊通知邮件组',
            'check_face_recognition_frequency'              => 'IC人脸识别次数限制',
            'resignation_advance_job_title'                 => '特定职位不能选择N天内离职的职位-非IC',
            'resignation_advance_days'                      => '特定职位不能选择N天内离职的天数-非IC',
            'OS_OT_apply_staff'                             => '允许加班的外协员工属于的部门（含下级）|职位，多组之间,分割',
            'Blackface_verification_position'               => '黑名单人脸校验职位',
            'Blackface_verification_department'             => '黑名单人脸校验部门',
            'Blackface_verification_getmsg_ID'              => '黑名单人脸消息工号',
            'Blackface_verification_Email'                  => '黑名单人脸接收邮箱',
            'suspension_audit_subordinate_job_title_id'  => '【停职申请】可选择下级员工的职位id',
            'suspension_audit_jurisdiction_job_title_id' => '【停职申请】可选择管辖范围员工的职位id',
            'suspension_audit_department_role_id'        => '【停职申请】可选择指定部门员工的角色id：职位:部门,部门|职位:部门,部门',
            'suspension_audit_jurisdiction_role_id'      => '【停职申请】可选择管辖范围员工的角色id',
            'os_half_shift_position'                        => '可选半天班次的外协职位(英文逗号分割)',
        ],
    ];

}
