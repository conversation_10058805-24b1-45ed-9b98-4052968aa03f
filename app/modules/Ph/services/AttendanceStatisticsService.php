<?php

namespace App\Modules\Ph\Services;

use App\Library\AttendanceEnums;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BusinessTripModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\ThailandHolidayModel;
use App\Modules\Ph\library\AttendanceUtil;
use App\Modules\Ph\Services\BackyardAttendanceService;
use App\Services\AttendanceStatisticsService as BaseAttendanceStatisticsService;
use App\Services\DepartmentService;
use App\Services\HrJobTitleService;
use App\Services\HrShiftService;
use App\Services\SysService;
use App\Services\SysStoreService;

class AttendanceStatisticsService extends BaseAttendanceStatisticsService
{

    /**
     * 查询attendance_data_v2的字段
     * @var string[]
     */
    protected $solidify_attendance_data_v2_query_field = [
        'leave_type',
        'attendance_started_at',
        'attendance_end_at',
        'stat_date',
        'staff_info_id',
        "shift_start",
        "shift_end",
        "late_times",//迟到
        "leave_early_times",//早退
        "OFF",
        "BT",
        'AB',
        'RH',
        'RH_UNPAID',
        'SH',
        'shift_id',
        'job_title',
    ];

    /**
     * 获取员工考勤数据
     * @param $params
     */
    public function getStaffsAttendanceInfo($params)
    {
        if(empty($params['start_date'])){
            throw new ValidationException('start_date empty');
        }

        if(empty($params['end_date'])){
            throw new ValidationException('end_date empty');
        }

        $returnData = [
            'total'=>0,
            'list'=>[],
        ];
        //获取员工信息
        if ($this->query_type == 2) {
            $staffs = $this->getHarderOfficeStaffInfo($params);
        } else {
            $staffs = $this->getStoreStaffInfo($params);
        }

        if (!$staffs['list']) {
            return $returnData;
        }

        [$returnData['total'], $list] = [$staffs['total'],$staffs['list']];
        //获取考勤信息

        $params['staff_info_ids'] = array_column($list,'id');

        //获取考勤信息
        $attendanceData = $this->getAttendanceData($params);

        $go_out_works = $this->getGoOutWorks($params);
        //获取请假信息
        $apply_info     = $this->getStaffAuditInfo($params);
	
	    //获取出差打卡数据
	    $reissue_info  =  $this->getStaffAuditReissueInfo($params);
	    //出差
	    $backyard_attendance_bll = new BackyardAttendanceService();
	    $trip_where = " and apply_user in ({apply_user:array}) ";
	    $trip_where_params['apply_user'] = $params['staff_info_ids'] ?? [0];
	
	    $trip_data = $backyard_attendance_bll->getTripData($params['start_date'] ?? '', $params['end_date'] ?? '', $trip_where, $trip_where_params);
	    
        //获取员工工作日 历史的逻辑就是这样的，但是要是有人工作值变了，就尴尬了，最近才给固化表加这个字段，等过一段时间，用固化表的数据是最好的
        $this->getStaffWeekWorkingDay($params);

        $staff_week_working_data = array_column($list,'week_working_day','id');
        //获取固化的员工班次信息
        $staff_shift_history_data = $this->getShiftByDate($params);

        //构建考情数据
        $attendanceData = $attendanceData ? $this->handleAttendanceDataToViewV2(
            $params,
            $attendanceData,
            $staff_week_working_data,
            $apply_info ,
            $go_out_works,
            $reissue_info,
            $trip_data,
            $staff_shift_history_data
        ) :[];

        $returnData['list'] = $this->handelAttendanceOtherInfo($list,$attendanceData,$params);
        return  $returnData;
    }

    /**
     * 整理员工信息
     * @param $staff_list
     * @param $attendanceData
     * @param $params
     * @return mixed
     */
    protected function handelAttendanceOtherInfo($staff_list, $attendanceData, $params)
    {
        $sysService = new SysService();
        $region_info = array_column($sysService->getRegionListFromCache(), 'name', 'id');
        $piece_info = array_column($sysService->getPieceListFromCache(), 'name', 'id');
        $store_info = array_column($sysService->getStoreListFromCache(), 'name', 'id');
        //网点区域
        $sorting_no_map =  array_column( $sysService->storeGeographyCodeFromCache(),'label','value');
        //网点类型
        $store_category_map =  SysStoreService::$category;

        foreach ($staff_list as &$item) {
            $item['attend_data'] = $attendanceData[$item['id']] ?? $this->getEmptyAttendance($params);
            $item['manage_region'] = $item['manage_region'] ? $region_info[$item['manage_region']] : '';
            $item['manage_piece'] = $item['manage_piece'] ? $piece_info[$item['manage_piece']] : '';

            $item['state'] = $item['wait_leave_state'] == 1 && in_array($item['state'], [1, 2]) ? self::$t->_(
                'wait_leave_state_1'
            ) : self::$t->_('staff_state_' . $item['state']);
            $item['store_name'] = $this->query_type == 1 ? ($store_info[$item['organization_id']] ?? '') : '';
            //统计旷工
            $item['absenteeism_num'] = array_sum(array_column($item['attend_data'], 'absenteeism_num'));
            //迟到统计
            $item['late_num'] = array_sum(array_column($item['attend_data'], 'late_num'));
            //迟到分钟数统计
            $item['late_time'] = array_sum(array_column($item['attend_data'], 'late_time'));
            //早退统计
            $item['leave_early_num'] = array_sum(array_column($item['attend_data'], 'leave_early_num'));
            //早退分钟数
            $item['leave_early_time'] = array_sum(array_column($item['attend_data'], 'leave_early_time'));
            //网点区域
            $item['manage_geography_code_name'] = isset($item['manage_geography_code']) ? ($sorting_no_map[$item['manage_geography_code']] ?? '') : '';
            //网点类型             s.category as store_category,
            $item['store_category_name'] = isset($item['store_category']) ? ($store_category_map[$item['store_category']] ?? '') : '';
        }
        return $staff_list;
    }

	
	/**
	 * 构建考勤数据
	 * @param array  $params 日期等参数
	 * @param array $attendanceDataAll 考勤信息
	 * @param array $staff_week_working_data 员工工作制信息
	 * @param array $apply_info 请假信息
	 * @param array $go_out_works 外出信息
	 * @param array $reissue_info 出差打卡数据
	 * @param array $trip_data 出差数据
     * @param array $staff_shift_history_data 历史班次信息
     * @return array
	 */
	
	protected function handleAttendanceDataToViewV2(
        $params,
        array $attendanceDataAll,
        array $staff_week_working_data,
        array $apply_info ,
        array $go_out_works = [],
        array $reissue_info=[],
        array $trip_data=[],
        array $staff_shift_history_data = []
    ) {
		$returnData = [];
		if(empty($attendanceDataAll)){
			return $returnData;
		}
        $HrShiftService = new HrShiftService();

        $holidayService = new HolidayService();
        $regular_holiday = $holidayService->getHoliday(['date'=>$params['start_date'],'type'=>ThailandHolidayModel::HOLIDAY_TYPE_RH]);
        $special_holiday = $holidayService->getHoliday(['date'=>$params['start_date'],'type'=>ThailandHolidayModel::HOLIDAY_TYPE_SH]);

        $date_range = DateHelper::DateRange(strtotime($params['start_date']),strtotime($params['end_date']));
		
		//请假数据
		$apply_info = !empty($apply_info) ? $this->handleAuditDataUseToSelf($apply_info) : [];
		
		//出差打卡数据
		
		$reissue_info = !empty($reissue_info) ? $this->handleAuditReissueDataUseToSelf($reissue_info) :[];
		//出差
		$trap_data2date = AttendanceUtil::btSection2Date($date_range, $trip_data);
        //历史班次信息
        $staff_shift_history_data = !empty($staff_shift_history_data) ? $this->handleShiftByDate($staff_shift_history_data) : [];
        //获取现在员工班次
        $hr_staff_shift_data = $this->getStaffShift($params);

        //员工休息日
		$offDay = $this->getStaffOffDayData($params['start_date'],$params['end_date'],$params['staff_info_ids']);

        //弹性班次
        $free_shift_config = $this->getFreeShiftConfig();
        $this->setFreeShiftInfo($free_shift_config);

		//请假枚举
		$leave_type_config = AttendanceEnums::$leave_type;
        $attendanceDataAll = array_chunk($attendanceDataAll,500);

        //支援信息
        $supportServer = new StaffSupportStoreServer();
        $supportData = $supportServer->getSupportDataBetween($params['staff_info_ids'], $params['start_date'], $params['end_date']);
        $supportData = $supportServer->formatSupportData($supportData, $params['start_date'], $params['end_date']);
        $supportText = self::$t->_("on_support");

        foreach ($attendanceDataAll as $attendanceData) {

            foreach($attendanceData as $value){
                $type = "";
                $holiday_str = '';

                //计算旷工天数
                $absenteeism_num = 0;
                //计算迟到次数
                $late_num = 0;
                //计算迟到时长
                $late_time = 0;
                //计算早退次数
                $leave_early_num = 0;
                //计算早退时长
                $leave_early_time = 0;
                //出差状态拼接
                $business_travel = '';


                if(!empty($value['shift_start']) && strlen($value['shift_start']) < 5 ){
                    $value['shift_start'] = '0'.$value['shift_start'];
                }
                if(!empty($value['shift_end']) && strlen($value['shift_end']) < 5 ){
                    $value['shift_end'] = '0'.$value['shift_end'];
                }

                //如果当v 2 表里班次信息不存在 则尝试获取固化班次信息
                $value['shift_start'] = empty($value['shift_start']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['start']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['start'] : $value['shift_start'];
                $value['shift_end']   = empty($value['shift_end']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['end']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['end'] : $value['shift_end'];
                $value['shift_type']  = empty($value['shift_type']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_type']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_type'] : $value['shift_type'];
                $value['shift_id']  = empty($value['shift_id']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_id']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_id'] : $value['shift_id'];

                //如果固化表也没有获取 hr_staff_shift 表班次
                $value['shift_start'] = empty($value['shift_start']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_start'] : $value['shift_start'];
                $value['shift_end']   = empty($value['shift_end']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_end'] : $value['shift_end'];
                $value['shift_type']  = empty($value['shift_type']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_type'] : $value['shift_type'];
                $value['shift_id']  = empty($value['shift_id']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_id'] : $value['shift_id'];


                //出差打卡 重新赋值打卡时间和班次
                $value = $this->assignReissueAttendanceData($reissue_info, $value);
                $business_travel = $value['business_travel'] ?? '';
                //实际弹性打卡偏移量
                $current_flexible_shift_time = $HrShiftService->getCurrentFlexibleShiftTime([
                    'shift_id'        => $value['shift_id'],
                    'started_at_time' => (empty($value['attendance_started_at']) ? 0 : strtotime($value['attendance_started_at'])),
                    'date'            => $value['stat_date'],
                ]);


                $s1 = !empty($value['shift_start']) ? $value['shift_start'] : '08:00';//上班班次
                $s2 = !empty($value['shift_end'])  ? $value['shift_end'] : '17:00';//下班班次
                $check_start = date('Y-m-d H:i', strtotime($value['attendance_started_at']));//上班打卡时间
                $shift_start = "{$value['stat_date']} {$s1}";
                $shift_start = date('Y-m-d H:i',strtotime($shift_start) + $current_flexible_shift_time);//班次开始时间
                $check_end = date('Y-m-d H:i', strtotime($value['attendance_end_at']));//下班打卡时间
                $shift_end = "{$value['stat_date']} {$s2}";
                $shift_end = date('Y-m-d H:i',strtotime($shift_end) + $current_flexible_shift_time);
                if($s1 > $s2){
                    $shift_end = date('Y-m-d H:i',strtotime("{$shift_end} +1 day"));
                }

                //$background  1 灰色 2 黄色  3 红色
                [$background,$is_punch_out,$is_punch_in,$start_at,$end_at] = $this->initAttendanceTmpParams($value,$check_end,$shift_end,$check_start,$shift_start,$business_travel);

                //班次为空 给红色
                if(($is_punch_out || $is_punch_in ) && (empty($value['shift_start']) || empty($value['shift_end'])) && !$this->isFreeShiftJobTitle($value['job_title'])){
                    $background = 3;
                }
                // AB
                if (empty($is_punch_out) && empty($is_punch_in) && !empty($value['AB'])) {
                    $type = self::$t->_("absenteeism");//这是旷工;
                    $background =  3;   //红色
                    $absenteeism_num = 1;//旷工一天
                }

                $is_rh = in_array($value['stat_date'],$regular_holiday) ;
                $is_sh = in_array($value['stat_date'],$special_holiday) ;

                //优先使用固化表的工作制
                $week_working_day  = $this->static_staff_week_working_data[$value['staff_info_id']][$value['stat_date']]?? $staff_week_working_data[$value['staff_info_id']];

                //6天班公共假期
                if( ($is_sh || $is_rh) && $week_working_day != HrStaffInfoModel::WEEK_WORKING_DAY_5){
                    $background =  1;
                    $holiday_str = $is_rh ? "RH" : "SH";
                    $absenteeism_num = 0;//旷工天数
                    //不是休息日 又没打卡
                    if(!isset($offDay[$value['staff_info_id'].'-'.$value['stat_date']]) && ( $is_punch_out == 0 && $is_punch_in == 0)){
                        $type = self::$t->_("absenteeism_working_day_6");//这是安排加班,未出勤;
                    }
                }

                //5天班公共假期
                if(($is_sh || $is_rh) && $week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_5){
                    $holiday_str = $is_rh ? "RH":"SH";
                    $background  = 1;
                    $absenteeism_num = 0;//旷工天数

                }

                if(isset($offDay[$value['staff_info_id'].'-'.$value['stat_date']])){
                    $background = 1;   //休息日
                    $type = self::$t->_('2017');
                    $absenteeism_num = 0;//旷工天数

                }
                // 注意从这里开始 type 是.=
                //判断是否请假
                //请假图片
                //如果请假了
                [
                    $background,
                    $is_punch_out,
                    $is_punch_in,
                    $absenteeism_num,
                    $type,
                    $reason,
                    $applyLeaveType,
                    $image_path,
                ] = $this->dealAttendanceTmpParamsOfLeave($value, $apply_info, $background, $is_punch_in, $is_punch_out,
                    $shift_start, $shift_end, $leave_type_config, $check_start, $check_end, $absenteeism_num, $type,$offDay,$current_flexible_shift_time);


                //出差打卡审批中
                if(!empty($business_travel)){
                    $background=3;//红色
                }

                //计算迟到时长
                if ($is_punch_in == 5 && in_array($background, [2,3])) {
                    $late_num  = 1;                                          //迟到1 次
                    $late_time = $this->diffTime($check_start, $shift_start);//计算迟到时长
                }
                //计算早退
                if ($is_punch_out == 5 && in_array($background, [2,3])) {
                    $leave_early_num = 1;//早退1 次
                    $leave_early_time  = $this->diffTime($check_end, $shift_end);//计算早退时长
                }

                //打卡时间
                $punch_info = "in:{$start_at} out:{$end_at}";
                $shift_time = $value['shift_start'] ? $value['shift_start'].'-'.$value['shift_end'].($current_flexible_shift_time ? ' (flexible)' : '') : '';
                $shift_type = $value['shift_type'] ? self::$t->_(strtolower($value['shift_type'])) : '';

                if($value['stat_date'] > date('Y-m-d')){
                    $punch_info       = '';
                    $background       = 0;
                    $type             = '';
                    $absenteeism_num  = 0;
                    $leave_early_num  = 0;
                    $late_num         = 0;
                    $late_time        = 0;
                    $leave_early_time = 0;
                }
                $is_free = $this->isFreeShiftJobTitle($value['job_title']);
                if ($is_free) {
                    $leave_early_num  = 0;
                    $late_num         = 0;
                    $late_time        = 0;
                    $leave_early_time = 0;
                    if ($background == 2) {
                        $background = 0;
                    }

                    //设置 早退 黄色
                    //请全天假 不进行 统计。
                    //灰色 不计算。
                    if($applyLeaveType !== 0 && $background != 1) {
                        $leaveData['attendance_started_at'] = $value['attendance_started_at'];
                        $leaveData['attendance_end_at'] = $value['attendance_end_at'];
                        $leaveData['stat_date'] = $value['stat_date'];
                        $leaveData['type'] = $applyLeaveType;
                        $min = $this->setLiveLeaveEarly($leaveData);
                        if($min) {
                            $background = 2;
                            $leave_early_num = 1;
                            $leave_early_time = $min;
                        }
                    }

                    [$shift_time_type, $duration] = $this->getFreeShiftTypeAndDuration($value['stat_date'],
                        $value['attendance_started_at']);

                    $shift_time = $shift_time_type ? self::$t->_('free_shit_type_' . $shift_time_type,
                        ['duration' => $duration]) : '';
                    $shift_type = self::$t->_('shift');
                }

                $checkNotABDate = $is_free ?  [date('Y-m-d')] : [date('Y-m-d'), date('Y-m-d', strtotime('-1 day'))];
                
                //开始时间=上班时间或上班打卡时间取早的，结束时间=开始时间+22小时（马来取班次配置的最晚打卡时间），未超过结束时间未打卡显示「白底+“- -”」；超过结束时间后未打卡显示「红底+“缺卡”」
                [$punch_info, $is_not_show_ab, $background, $absenteeism_num, $type] = $this->dealTodayShowLogic($value,
                    $checkNotABDate, $background, $start_at, $end_at, $type, $absenteeism_num, $punch_info);

                //出差打卡审批中
                if (!empty($business_travel)) {
                  //  $background = 3;//红色
                }
                //判断是否出差
                if(isset($trap_data2date[$value['staff_info_id'].'-'.$value['stat_date']])){
                    $type .=  " " .($trap_data2date[$value['staff_info_id'].'-'.$value['stat_date']]['business_trip_type'] == BusinessTripModel::BTY_GO_OUT ? self::$t->_('out_of_office') :  self::$t->_('business_trip'));
                }

                //新增 支援显示文案
                $supportKey = $value['staff_info_id'] . '-' . $value['stat_date'];
                $isSupport = empty($supportData[$supportKey]) ? '' : $supportText;

                $returnData[$value['staff_info_id']][] = [
                    'stat_date'        => $value['stat_date'],
                    'value'            => trim(($is_punch_out || $is_punch_in || $is_not_show_ab) ? "{$punch_info} {$type} {$isSupport}" : "{$type} {$isSupport}"),
                    'background'       => $background,
                    'reason'           => $reason,
                    'image_path'       => $image_path,
                    'shift_time'       => $shift_time,
                    'shift_type'       => $shift_type,
                    'holiday'          => $holiday_str,  //假期
                    'absenteeism_num'  => $absenteeism_num,
                    'late_num'         => $late_num,
                    'late_time'        => $late_time,
                    'leave_early_num'  => $leave_early_num,
                    'leave_early_time' => $leave_early_time,
                ];
            }
        }


		return  $this->handelVacancyData($returnData,$date_range);
	}

    /**
     * 外协员工考勤
     * https://flashexpress.feishu.cn/docx/VvVXdTlu8oaHzoxu0DDcNMm9nwc
     * @param array $params
     * @return array
     */
    public function handleOutsourcingAttendance(array $params)
    {
        $result = [];
        //没有这个 代表是从考勤进来的 要走权限 否则从 外协公司推送进来的不走权限验证
        if (!isset($params['is_push_setting_task_type']) || empty($params['is_push_setting_task_type'])) {
            $storeAuth = $this->getAuthStore($params['staffInfo']);
            $logger = $this->getDI()->get('logger');
            $logger->info("handleOutsourcingAttendance  storeAuth:" . json_encode($storeAuth, JSON_UNESCAPED_UNICODE));

            if ($storeAuth['is_store_user']) {
                if (empty($storeAuth['store_id'])) {
                    return $result;
                }
                $params['store_id'] = !empty($params['store_id']) ? (array_intersect($params['store_id'],
                    $storeAuth['store_id']) ?: ['empty_store']) : $storeAuth['store_id'];//数组
            }
        }
        $returnData = $this->getOutsourcingStaffInfo($params);
        if (empty($returnData)) {
            return [];
        }
        $params['staff_info_ids'] = array_column($returnData, 'id');
        $staffOutsourcingType     = array_column($this->getStaffOutsourcingType($params['staff_info_ids']), 'value',
            'staff_info_id');

        //获取员工派件任务
        $staff_ticket_info = $this->getTicketDelivery($params);
        $state_text_key    = Enums::$hris_working_state;
        $sysService        = new SysService();
        //大区信息
        $region_info = array_column($sysService->getRegionListFromCache(), 'name', 'id');
        //片区信息
        $piece_info = array_column($sysService->getPieceListFromCache(), 'name', 'id');
        //网点信息
        $store_info = array_column($sysService->getStoreListFromCache(), 'name', 'id');
        //网点分类
        $store_category     = array_column($sysService->getStoreListFromCache(), 'category', 'id');
        $store_category_map = SysStoreService::$category;
        //职位
        $job_title = array_column((new HrJobTitleService())->getJobTitleListFromCache(false), 'job_name', 'id');

        //部门
        $department_list = (new DepartmentService())->getAllDepartmentFromCache();
        //时间范围
        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));
        //获取班次表数据
        $shift_server = new HrShiftService();
        $shift_info   = $shift_server->getShiftInfos($params['staff_info_ids'],
            $date_range);//arr[date_staffId] = ['start' => '11:11','end'=>'22:22']
        //查询 是否 off
        $workday_server = new WorkdayService();
        $off_days       = $workday_server->getOffDays($params['staff_info_ids'], $params['start_date'],
            $params['end_date']);
        //外协工单信息
        $staffOutsourcingOrder = $this->getStaffOutsourcingOrder($params);
        //员工打卡数据
        $attendanceInfo = $this->getStaffWorkAttendance($params);

        //获取外协员工固化信息
        $osStaffPersistentInfo = $this->getPersistentOsStaffInfo($params);
        $today                 = date('Y-m-d');//计算旷工排除当天

        foreach ($returnData as $key => $item) {
            $transfer_store_name = [];
            $orderInfo = [
                'shift_id'        => 0,
                'effective_date'  => '',
                'employment_date' => '',
                'employment_days' => 0,
                'staff_name'      => '',
                'work_day'        => [],
            ];
            //工单创建的外协员工，获取对应网点信息
            if (isset($staffOutsourcingOrder[$item['id']])) {//查询工单对应的网点名称 导出(每日)用
                foreach ($staffOutsourcingOrder[$item['id']] as $order) {
                    $orderInfo = $order;
                    for ($i = 0; $i < $orderInfo['employment_days']; $i++) {
                        $employment_day                       = date("Y-m-d", strtotime("+{$i} day", strtotime($orderInfo["employment_date"])));
                        $transfer_store_name[$employment_day] = $store_info[$orderInfo['store_id']] ?? '';
//                        $work_day[$employment_day] = true;
                    }
                }
            }

            $company_name_ef = $item['company_name_ef']; //员工表中的
            $item            = array_merge($orderInfo, $item);
            $category        = $store_category[$item['sys_store_id']] ?? 0;
            $_res            = [
                'name'                 => $item['name'],
                'staff_info_id'        => $item['id'],
                'staff_type'           => $item['staff_type'],
                'state'                => $item['state'],
                //产品说外协 只有在职和离职不能显示其他状态
                'state_text'           => $item['state'] == HrStaffInfoModel::STATE_ON_JOB ? self::$t->_($state_text_key[$item['state']]) : self::$t->_($state_text_key[HrStaffInfoModel::STATE_RESIGN]),
                'company_name_ef'      => $company_name_ef,
                'outsourcing_type'     => isset($staffOutsourcingType[$item['id']]) ? self::$t->_('outsource_' . $staffOutsourcingType[$item['id']]) : '',
                'store_name'           => $store_info[$item['sys_store_id']] ?? '',//这个是用的当前员工表网点信息
                'store_category'       => self::$t->_($store_category_map[$category] ?? ''),
                'region'               => $item['manage_region'] ? $region_info[$item['manage_region']] : '',
                'piece'                => $item['manage_piece'] ? $piece_info[$item['manage_piece']] : '',
                'attend_data'          => [],
                'node_department_id'   => $item['node_department_id'],
                'job_title'            => $item['job_title'],
                'job_name'             => $job_title[$item['job_title']] ?? '',
                'node_department_name' => $department_list[$item['node_department_id']] ?? '',
                //新增字段
                'ab_days'              => 0,//矿工天数
                'late_num'             => 0,//迟到次数
                'late_duration'        => 0,//迟到时长
                'early_num'            => 0,//早退次数
                'early_duration'       => 0,//早退时长
            ];

            foreach ($date_range as $day) {
                $values          = '';
                $excel_show_text = '';
                //如果配置 off 算休息
                $shift_key        = "{$day}_{$item['id']}";//班次键
                $is_work_day      = empty($off_days[$shift_key]) ? 1 : 0;//是不是工作日 根据轮休判断
                $background       = $is_work_day ? 0 : 5;
                $working_minutes  = ''; //打卡分钟数
                $working_duration = ''; //打卡时长
                $late_minutes     = $early_minutes = 0;
                //工单创建的外协员工 找班次
                $shift_start = $shift_info[$shift_key]['start'] ?? '';
                $shift_end   = $shift_info[$shift_key]['end'] ?? '';
                $_attendanceInfo = $attendanceInfo[$item['id']][$day] ?? [];

                //上述取外协公司的逻辑被替换为从外协员工信息固化表(persistent_outsource_staff_info)里取数据
                $_persistentStaffInfo        = $osStaffPersistentInfo[$item['id']][$day] ?? [];
                $companyNameEfFromPersistent = $_persistentStaffInfo['company_name_ef'] ?? '';

                //优先从固化表里取数据，如无数据，则为空
                $companyNameEf = $companyNameEfFromPersistent ?: '';
                //没班次 不用默认 不算迟到早退
//                $shift_start = $_attendanceInfo ? $_attendanceInfo['shift_start'] : $shift_start;
//                $shift_end   = $_attendanceInfo ? $_attendanceInfo['shift_end'] : $shift_end;

                if (!empty($shift_start) && strlen($shift_start) < 5) {
                    $shift_start = '0' . $shift_start;
                }
                if (!empty($shift_end) && strlen($shift_end) < 5) {
                    $shift_end = '0' . $shift_end;
                }

                if (!empty($_attendanceInfo)) {
                    $start = $_attendanceInfo["started_at"] ? substr($_attendanceInfo["started_at"], 11, 5) : 'N/A';
                    $end   = $_attendanceInfo["end_at"] ? substr($_attendanceInfo["end_at"], 11, 5) : 'N/A';
                    //计算上班时长 -- 小时
                    if ($_attendanceInfo["started_at"] && $_attendanceInfo["end_at"]) {
                        $working_minutes  = $this->diffTime(date('Y-m-d H:i', strtotime($_attendanceInfo["end_at"])),
                            date('Y-m-d H:i', strtotime($_attendanceInfo["started_at"])));
                        $working_duration = $this->minuteToHour($working_minutes);
                    }

                    $values = $excel_show_text = "in:{$start} out:{$end}";
                    if (!empty($shift_start) && $start > $shift_start && $is_work_day) {
                        $background = 2; //迟到
                        //计算迟到时间
                        $late_minutes = $this->getAbsMinute($_attendanceInfo, $shift_start, $shift_end, 'start');
                    }

                    if (!empty($shift_end) && $end < $shift_end && $is_work_day) {
                        $background    = 2; //早退
                        $early_minutes = $this->getAbsMinute($_attendanceInfo, $shift_start, $shift_end, 'end');
                    }
                    if (($start == 'N/A' || $end == 'N/A') && $is_work_day && $day < $today) {
                        $background = 3; //缺勤
                    }
                }

                if ($is_work_day && empty($_attendanceInfo) && $day < $today) {
                    $background = 3;
                    $values     = $excel_show_text = 'AB';
                    if (isset($staff_ticket_info[$item['id']][$day])) {
                        $values = $excel_show_text = 'AB(h)';
                    }
                }

                $operate = !empty($shift_start) ? $shift_start . '-' . $shift_end : '';

                if (empty($transfer_store_name[$day]) && !empty($item['leave_date']) && $item['state'] == 2
                    && ($day > date('Y-m-d', strtotime($item['leave_date'])) || $day < date('Y-m-d',
                            strtotime($item['hire_date'])))) {
                    $values     = 0;
                    $background = 0;
                    $operate    = '';
                }

                if (empty($transfer_store_name[$day]) && !empty($item['hire_date']) && $item['state'] == 1 && $day < date('Y-m-d',
                        strtotime($item['hire_date']))) {
                    $values     = 0;
                    $background = 0;
                    $operate    = '';
                }
                if ($values === 0) {
                    $excel_show_text = '';
                }
                //新增的轮休配置 规则最优先 产品说的
                if (!empty($off_days[$shift_key])) {
                    $background = 1;//灰色
                    //如果没打卡 展示 off
                    if (empty($_attendanceInfo)) {
                        $values = $excel_show_text = 'off';
                    }
                }

                //计算迟到早退旷工
                if (!empty($background)) {
                    //旷工
                    if ($background == 3 && $day < $today) {
                        $_res['ab_days'] += 1;
                    }
                    //有迟到早退
                    if ($background == 2) {
                        if (!empty($late_minutes)) {
                            $_res['late_num']      += 1;
                            $_res['late_duration'] += $late_minutes;
                        }
                        if (!empty($early_minutes)) {
                            $_res['early_num']      += 1;
                            $_res['early_duration'] += $early_minutes;
                        }
                    }
                }

                $_res['attend_data'][] = [
                    'stat_date'        => $day,
                    'value'            => $values,
                    'excel_text'       => $excel_show_text,
                    'background'       => $background,
                    'operate'          => $operate,
                    'reason'           => '',
                    'image_path'       => '',
                    'shift_type'       => '',
                    'working_day'      => $is_work_day,
                    'store_name'       => $transfer_store_name[$day] ?? '',//这是用的 工单里面的网点信息
                    'working_minutes'  => $working_minutes,
                    'working_duration' => $working_duration,
                    'company_name_ef'  => $companyNameEf,//新增当日所在外协公司
                    'late'             => $late_minutes,
                    'early'            => $early_minutes,
                    'shift'            => $shift_start ? $shift_start . '-' . $shift_end : '',
                ];
            }
            $result[] = $_res;
        }
        return $result;
    }

}
