<?php

namespace app\modules\Ph\services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums\ConditionsRulesEnums;
use App\Library\Exception\BusinessException;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SettingEnvModel;
use App\Services\ConditionsRulesService;
use App\Services\HoldManageService as BaseHoldManageService;
use App\Services\LeaveManagerService;
use Exception;

class HoldManageService extends BaseHoldManageService
{



    protected function getLessThanLeaveSource(): array
    {
        $leave_source_default = [
            LeaveManagerService::LEAVE_SOURCE_OFF3DAYS,
            LeaveManagerService::LEAVE_SOURCE_NOTPAY,
            LeaveManagerService::LEAVE_SOURCE_BATCH,
            LeaveManagerService::LEAVE_SOURCE_CRIMINAL,
            LeaveManagerService::LEAVE_SOURCE_HRIS_EDIT,
            LeaveManagerService::LEAVE_SOURCE_PROBATION,
        ];

        $leave_source_special = [
            LeaveManagerService::LEAVE_SOURCE_BACKYARD,
        ];
        return [$leave_source_default, $leave_source_special];
    }

    public function buildResignLessThan($builder)
    {
        [$leave_source_default,$leave_source_special] = $this->getLessThanLeaveSource();

        [$holdAdvanceResignPosition,$resignation_advance_days] = $this->getHoldAdvanceResignConfig();
        $builder->andWhere(" i.leave_date >= '2024-07-05' ");
        $bind      = ['leave_source_default' => $leave_source_default, 'leave_source_special' => $leave_source_special];
        $condition = "i.leave_source in ({leave_source_default:array}) or (i.leave_source in ({leave_source_special:array})  and l.apply_resign_time is not null and datediff(i.leave_date,DATE_FORMAT(CONVERT_TZ(l.apply_resign_time, '+00:00', '{$this->timeZone}'),'%Y-%m-%d')) < 30 )";
        if (!empty($holdAdvanceResignPosition)) {
            $condition             = "
            i.leave_source IN ( {leave_source_default:array} )
		    OR (
                i.leave_source IN ( {leave_source_special:array} )
                AND l.apply_resign_time IS NOT NULL
                AND (
                        ( i.job_title NOT IN ( {special_job_title:array} ) AND datediff(i.leave_date,DATE_FORMAT( CONVERT_TZ( l.apply_resign_time, '+00:00', '{$this->timeZone}' ), '%Y-%m-%d' )) <= 30 )
                        OR
                        (i.job_title IN ( {special_job_title:array} ) AND datediff(i.leave_date,DATE_FORMAT( CONVERT_TZ( l.apply_resign_time, '+00:00', '{$this->timeZone}' ), '%Y-%m-%d' )) <= $resignation_advance_days )
                    )
			)";
            $bind['special_job_title'] = $holdAdvanceResignPosition;
        }

        $builder->andWhere($condition,$bind);
        return $builder;
    }

    public function getHoldAdvanceResignConfig(): array
    {
        if (!empty($this->holdAdvanceResignPosition)) {
            return [$this->holdAdvanceResignPosition,$this->holdAdvanceResignPositionDays];
        }

        $config                        = SettingEnvModel::getMultiEnvByCode([
            'resignation_advance_days',
            'resignation_advance_job_title',
        ]);
        $resignation_advance_days      = intval( $config['resignation_advance_days'] ?? 0);
        $resignation_advance_job_title = empty($config['resignation_advance_job_title']) ? [] : explode(',',
            $config['resignation_advance_job_title']);

        $this->holdAdvanceResignPosition = $resignation_advance_job_title;
        $this->holdAdvanceResignPositionDays = $resignation_advance_days;
        return [$resignation_advance_job_title, $resignation_advance_days];
    }



    public function syncDirectorPayOnBehalfHold($param) {
        return [];
    }
}