<?php

namespace App\Modules\Th\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Th\Services\StaffSupportStoreServer;
use Exception;

class StaffSupportStoreController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 员工支援列表
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function supportListAction()
    {
        $params = $this->request->get();
        $staff_support_server = new StaffSupportStoreServer();

        $validations = $staff_support_server->staffSupportListParamsValidations();
        $validations = array_merge($validations,
            ['page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:1']);
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);
        Validation::validate($params, $validations);
        $date = $staff_support_server->getStaffSupportList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 编辑员工支援信息
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function editStaffSupportAction()
    {
        $params = $this->request->get();
        $validations =  [
            'id' => 'Required|Int',
            'staff_info_id' => 'Required|Int',
            'serial_no' => 'Str',
            'support_begin_date' => 'Required|Date',
            'support_end_date' => 'Required|Date',
            'shift_id' => 'Required|Int',
        ];
        Validation::validate($params, $validations);
        $params['user'] = $this->user;
        $staff_support_server = new StaffSupportStoreServer();
        /**
         * @see StaffSupportStoreServer::editStaffSupport()
         */
        $result = $staff_support_server->editStaffSupportUseLock($params);
        if($result) {
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }

    /**
     * 静态资源
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function sysInfoAction()
    {
        $staff_support_server = new StaffSupportStoreServer();
        $date = $staff_support_server->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }
}