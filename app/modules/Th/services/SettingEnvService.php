<?php

namespace App\Modules\Th\Services;

use App\Services\SettingEnvService as BaseSettingEnvService;


class SettingEnvService extends BaseSettingEnvService
{
    //本国独有的配置项
    protected $country_key_list = [
        'WinHR' => [
            'refresh_resume_information_for_reentry' => '清除旧简历生成新简历员工工号(英文逗号分割)',//刷新简历信息
            'edit_offer_data_staff_ids'              => '可修改offer信息员工ID 如44232,32323',
            'black_and_graylist_frontline_jobids'    => '黑灰名单一线职位',
            'is_check_grey_list'                     => '灰名单是否开启，1=开启 2=关闭 默认开启',
            'resume_hire_type_agent_black_list_category'=> '黑灰名单简历的个人代理过滤黑名单小类code 配置 填写例子: C0301,C0601',
            'individual_contractor_jobids'           => '个人代理员工可用JD',
            'employee_jobids'                        => '正式员工quick offer可用JD',
            'jointly_recruited_ids'                  => '总部TA和网络TA共同招聘的职位',
            'jointly_recruited_jdids'                => '总部TA和网络TA共同招聘的期望岗位',
            'special_ic_contract_branches'           => '个人代理使用新合同模板的网点',
            'winhr_th_head_office_no_like_job'       => '网络TA招聘职位',
            'h5_th_head_office_no_like_job'          => '网络TA招聘岗位JD',
            'view_head_office_contract_role_ids'          => '可查看总部员工合同的角色',
            'view_head_office_contract_staff_ids'         => '可查看总部员工合同的工号',
            'view_branch_contract_role_ids'               => '可查看网点员工合同的角色',
            'view_branch_contract_staff_ids'              => '可查看网点员工合同的工号',
            'contract_salary_permission_staff'       => '可查看劳动合同基本工资的工号',
            'no_carriage_car_rental_coefficient'     => '无车厢Van Courier的车补折算系数',
            'failed_transfer_contract_email'         => '转岗合同发送失败邮件提醒',
            'failed_transfer_contract_cc_email'      => '转岗合同发送失败邮件抄送提醒',
            'hc_hire_type_incorrect_email'           => 'HC雇佣类型错误提醒的邮箱地址(英文逗号分割)',
        ],
        'HRIS' => [
            'pd_staff_white_list'                         => '使用历史规则计算PD的工号(英文逗号分割)',
            'resigned_staff_email_remind_th'              => "被同步工号离职给泰国发邮件提醒",
            'resigned_staff_email_remind_ph'              => "被同步工号离职给菲律宾发邮件提醒",
            'resigned_staff_email_remind_my'              => "被同步工号离职给马来发邮件提醒",
            'resigned_staff_email_remind_vn'              => "被同步工号离职给越南发邮件提醒",
            'resigned_staff_email_remind_la'              => "被同步工号离职给老挝发邮件提醒",
            'resigned_staff_email_remind_id'              => "被同步工号离职给印尼发邮件提醒",
            'Prohibit_BS_shift_setting'                   => '主管仅月初月末可修改班次时间，1 开启，0 关闭',
            'network_management_questionnaire'            => '网络部门快递员离职问卷推送连接',
            'bcc_difficult_pure_large_store'              => '纯大件网点bike系数',
            'bcc_difficult_bangkok_store'                 => '曼谷区域bike系数',
            'bcc_difficult_south_store'                   => '南部区域bike系数',
            'bcc_top_pure_large_store'                    => 'Top纯大件网点bike系数',
            'bcc_top_store'                               => 'Top网点bike系数',
            'bcc_surat_suk_store'                         => '素叻他尼/素坤网点bike系数',
            'bcc_unstable_store'                          => '不稳定网点bike系数',
            'bcc_overload_store'                          => '爆仓网点bike系数',
            'bcc_attendance_offset'                       => '出勤补偿bike系数',
            'DC_from_BDC_store'                           => 'DC继承BDC（大件网点）车补规则',//bike车补系数
            'pd_top_pure_large_store'                     => 'Top纯大件网点pd规则',
            'pd_top_store'                                => 'Top网点pd规则',
            'remote_tourism_store'                        => '偏远/旅游网点',
            'difficulty_store_0624_1'                     => '阶梯车补规则1',
            'difficulty_store_0624_2'                     => '阶梯车补规则2',
            'criminal_review_view_all_role_id'            => '犯罪记录信息审核-查看所有数据的角色id',
            'criminal_review_view_all_staff_id'           => '犯罪记录信息审核-查看所有数据的工号',
            'criminal_review_view_jurisdiction_role_id'   => '犯罪记录信息审核-查看管辖范围数据的角色id',
            'criminal_review_view_jurisdiction_staff_id'  => '犯罪记录信息审核-查看管辖范围数据的工号',
            'criminal_process_view_all_role_id'           => '犯罪记录处理-查看所有数据的角色id',
            'criminal_process_view_all_staff_id'          => '犯罪记录处理-查看所有数据的工号',
            'criminal_process_view_jurisdiction_role_id'  => '犯罪记录处理-查看管辖范围数据的角色id',
            'criminal_process_view_jurisdiction_staff_id' => '犯罪记录处理-查看管辖范围数据的工号',
            'ccc_ladder_store1_1'                         => '阶梯车补规则1.1',
            'ccc_ladder_store1_2'                         => '阶梯车补规则1.2',
            'ccc_bsp_store'                               => 'BSP网点',
            'ccc_ladder_store3_1'                         => '阶梯车补规则3.1',
            'ccc_ladder_store3_2'                         => '阶梯车补规则3.2',
            'ability_commission_front_line_job_title'     => '能力提成一线职位',
            'experience_allowance_special_store'          => '特殊经验补贴网点(多个用英文逗号隔开）',
            'experience_allowance_white_list'             => 'DCO经验补贴白名单(旅游/纯BDC/重点网点)(多个用英文逗号隔开）',
            'support_staff_apply_job_title_config'        => 'HCM导入支援员工：配置可支援职位创建子账号的默认角色{"可支援职位id(一个)":[默认角色id(多个英文,分隔)]}',
            'support_job_title_role_config'               => 'HCM导入支援员工：配置允许被添加为支援员工的职位及其可支援的职位{"可添加职位id(一个)":[可支援职位id(多个英文,分隔)]}',
            'import_staff_support_template'               => 'HCM导入支援员工：配置导入模板文件地址',
            'store_support_manage_job_title'              => 'HCM导入支援需求：配置允许申请的支援职位【支援职位id】',
            'store_support_manage_import_template'        => 'HCM导入支援需求：配置导入模板文件地址',
            'day_shift_duration'                          => '弹性打卡 白班小时数',
            'night_shift_duration'                        => '弹性打卡 夜班小时数',
            'free_shift_position'                         => '执行弹性班次的职位ID',
            '1OT_Courier_Limit'                           => '1倍加班配置快递员职位id',
            '1OT_DCO_Limit'                               => '1倍加班配置仓管员职位id',
            'sign_warning_not_default_witness_staff_ids'  => '【警告信】不作为默认证人的工号',
            'full_company_mobile_job_title'               => '未填写企业号码发送提醒的职位id',
            'staff_insurance_beneficiary_view'            => '员工管理可见保险信息单字段以及筛选框的人员工号',
            'shift_supervisor_job_title'                  => '只能通过轮休管理设置班次的职位',
            'update_staff_department_by_org'              => '部门自动变更失败通知-邮箱',
            'target_set_probation_staff_ids'              => '试用期目标列表数据权限，按照工号配置查看所有数据，多个以逗号隔开',
            'Misjudgment_limit'                           => '人脸黑名单-命中记录生成后可误判的最晚天数，默认7天',
            'er_email'                                    => 'er的邮箱',
            'not_cal_attendance_staff_ids'                => 'attendance salary hidden list',
            'blackface_os_reason'                         => '同步人脸黑名单的外协黑名单原因id',
            'support_staff_limit_join_days'               => '限制主账号原职位入职后N天后可以开始支援的天数，[职位多个之间,分隔]|天数 例如：[110,13]|3,[37]|1',
        ],
        'Backyard' => [
            'car_food_rules_exclude_outlets'                    => '60件车补饭补规则，剔除网点与生效日期(TH000001/2022-08-24)',
            'administrative_work_order_staff_id'                => '行政工单 指定工号(英文逗号分割)',
            'administrative_work_order_job_title'               => '行政工单 指定职位(英文逗号分割)',
            'all_staff_hide_dep_for_certificate'                => '部门下全部员工隐藏证明文件',
            'thai_staff_hide_dep_for_certificate'               => '部门下泰国籍员工隐藏证明文件',
            'use_os_white_list_store_category'                  => '触发外协网点白名单的网点类型 10,2,1',
            'system_external_approval_white_list'               => '外协网点白名单，以逗号形式分割如 TH000001,TH000002',
            'fleet_apply_roles'                                 => '加班车申请角色,多个角色以西文逗号进行分隔',
            'reinstatement_department_list'                     => '可申请恢复在职的部门部门ID(英文逗号分割)',
            'hikcentral_face_picture_staffs'                    => '可编辑所属网点下员工海康人脸照片的工号',
            'individual_contractor_job_title'                   => '个人代理可选择职位(英文逗号分割)',
            'enable_transition_individual_contractor_job_title' => '可转型为个人代理的职位(英文逗号分割)',//转型
            'quick_offer_position'                              => '使用Quick offer的职位ID',
            'canleavetoday'                                     => '是否可以今天请假，1代表可以申请今天，0代表不能申请今天',
            'backyard_check_face_switch'                        => '是否开启人脸校验，默认1：开启；0：关闭',
            'backyard_check_seconds'                            => '间隔校验秒数，默认4小时(7200 * 4 秒)，可填写数字。',
            'backyard_check_face_in_all_store_switch'           => '是否开启底片人脸校验，默认1：开启；0：关闭',
            'backyard_check_position'                           => '个人代理作弊检测职位ID(英文逗号分割)',
            'agent_attendance_punch_card_email'                 => '个人代理在打卡，校验到作弊通知邮件组',
            'agent_attendance_person_email'                     => '个人代理账号kit端每2小时人脸校验，校验到作弊通知邮件组',
            'ic_reissue_days'                                   => '设置IC补结算时间范围，只能申请x天内的服务费',
            'ic_reissue_times'                                  => '设置IC每月补结算次数上限',

            'agent_attendance_save_attachment_email'            => '底片校验，校验到作弊通知邮件组',
            'facial_verify_face_check'                          => '开启数据安全人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_used_device'                         => '开启常用设备人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_new_wifi'                            => '开启新增WiFi人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_token_expiration_date'               => '登录X天后自动过期，需重新登录刷脸。默认值30',
            'facial_verify_captcha'                             => '开启BY登录腾讯云验证码，0代表关闭，1代表开启，默认0',
            'facial_verify_salary_whitelist_noverifiy_face'     => '发薪不打卡白名单无需验证人脸开关，0关闭，1开启，默认1',
            'facial_verify_whitelist_noverifiy_facee'           => '无需人脸校验工号,默认空',
            'check_face_recognition_frequency'                  => 'IC人脸识别次数限制',
            'cs_ticket_job_title_ids'                           => '显示cs工单入口的职位(英文逗号分割)',
            'apply_support_store_category'                      => '支援需求支持的网点类型1-SP,2-DC,10-BDC,13-CDC,14-PDC (多个用英文逗号隔开)',
            'ic_cycle_alive_score'                              => '个人代理2小时作弊检测 静默活体分数值 小数',
            'store_phone_number_package'                        => '特殊网点所能选择到的手机号套餐',
            'head_office_phone_number_package'                  => '总部员工所能选择的手机号套餐',
            'staff_phone_number_package'                        => '支持员工自行申请的SIM卡套餐',
            'staff_unable_apply_company_mobile_job'             => '不可自行申请使用公司企业号码的职位ID，多个用英文逗号,分隔',
            'monthly_special_contract_employees_job'            => 'BY可选择月薪制合同工的职位，多个用英文逗号,分隔',
            'daily_special_contract_employees_job'              => 'BY可选择日薪制合同工的职位，多个用英文逗号,分隔',
            'daily_special_contract_employees_branch'           => 'BY可选择日薪制合同工的网点，多个用英文逗号,分隔',
            'daily_special_contract_employees_city'             => 'BY可选择日薪制合同工的府，多个用英文逗号,分隔',
            'daily_employees_branch_emails'                     => '新开网点是否招聘日薪的提醒邮箱，多个用英文逗号分隔',
            'phone_check_switch'                                => '手机号有效性验证开关（1:开启，0:关闭）',
            'phone_check_position'                              => '手机号有效性验证职位（配置职位id，英文逗号,分隔）',
            'phone_check_days'                                  => '手机号有效性验证频率（配置天数，初始值30）',
            'Blackface_verification_position'                   => '黑名单人脸校验职位',
            'Blackface_verification_department'                 => '黑名单人脸校验部门',
            'Blackface_verification_getmsg_ID'                  => '黑名单人脸消息工号',
            'Blackface_verification_Email'                      => '黑名单人脸接收邮箱',
            'os_staff_contract_sign_template_url'               => '外协外协仓管自动发送合同协议模版链接',
            'tax_pdf_hour_warehouse_template_url'               => '小时工仓管发送50tawi模版链接',
            'bt_start_days'                                     => '出差开始日期选择过去天数，默认配置值 7，支持负数',
            'agent_job_transfer_message_staff_ids'              => '个人代理转岗失败BY消息通知指定工号,多个用英文逗号,分隔',
            'agent_job_transfer_activate_url'                   => '个人代理转岗确认失败激活跳转链接',
        ],
        'OA' => [
            'OT-1.5OT_Approval_position' => 'OT审批流获取累计OT合计的职位',
        ],
        'FBI' => [
            'vehicle_check_position' => '快递员车辆稽查允许导入的职位id，初始值配置110',
        ],

    ];

}
