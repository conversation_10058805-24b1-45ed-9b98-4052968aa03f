<?php

namespace App\Modules\Th\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffApplySupportStoreOperateLogsModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SysStoreModel;
use App\Modules\Th\library\Enums\enums;
use App\Services\BusinessTripService;
use App\Services\HrJobTitleService;
use App\Services\HrShiftService;
use App\Services\StaffContractService;
use App\Services\StaffNoticeService;
use App\Services\StaffService;
use App\Services\StaffSupportService;
use App\Services\StaffSupportStoreServer as GlobalService;
use App\Services\SysService;
use App\Services\SysStoreService;
use App\Services\WorkShiftService;
use Exception;
use App\Library\Enums\EnumSingleton;
use App\Services\MessagesService;

class StaffSupportStoreServer extends GlobalService
{
    /**
     * 导入员工支援网点数据
     * @param $excel_data
     * @return array
     */
    public function ImportStaffSupport($params)
    {
        try {
            $excel_data = $params['excel_data'] ?? [];
            $return_data = ['error_num' => 0, 'url' => '', 'success_num' => 0];
            $staff_info_ids = array_column($excel_data, 0);
            $staff_info_ids = array_map(function ($item) {
                return str_replace(' ', '', $item);
            }, $staff_info_ids);

            $staff_list     = $this->getStaffInfoByIds($staff_info_ids);
            $staff_position = [];
            if (!empty($staff_info_ids)) {
                $staff_position_list = HrStaffInfoPositionModel::find([
                    'conditions' => "staff_info_id in ({staff_ids:array})",
                    'bind'       => [
                        'staff_ids' => $staff_info_ids,
                    ],
                ])->toArray();
                foreach ($staff_position_list as $key => $value) {
                    $staff_position[$value['staff_info_id']][] = (int)$value['position_category'];
                }
            }

            $store_ids = array_column($excel_data, 1);
            $store_list = (new SysStoreService())->getStoreList($store_ids);
            $store_list = !empty($store_list) ? array_column($store_list, null, 'id') : [];

            $shift_list = (new HrShiftService())->getList();//获取所有班次
            $shift_start_list = array_column($shift_list, null, 'start');
            $error_list = [];
            $db = $this->getDI()->get("db_backyard");
            $current_day = date("Y-m-d");
            $sync_ms_params = [];

            $job_title_config = $this->getSupportStaffJobTitleConfig();//setting_env 配置
            $support_job_title_list = $job_title_config['support_job_title_list'];//可支援职位列表
            $temp_job_title = array_flip($support_job_title_list);//可支援职位列表 反转匹配excel导入职位名称
            $support_job_title_role_config = $job_title_config['support_job_title_role_config'];//支援职位对应角色id

            $support_staff_apply_job_title_ids = $job_title_config['support_staff_apply_job_title_ids'];//可以导入员工职位ids
            $support_staff_job_title_config = $job_title_config['support_staff_job_title_config'];//员工职位对应可支援的职位
            $config_job_title_list = $job_title_config['job_title_list'];

            $staff_job_title_ids = array_column($staff_list, 'job_title');
            $staff_job_title_list = (new HrJobTitleService())->getJobTitleMapByIds($staff_job_title_ids);

            $t = BaseService::getTranslation('en');

            $staffSupportService = new StaffSupportService();
            $logServer = new WorkShiftService();
            $next_year_today = date('Y-m-t', strtotime(date('Y-m-01',strtotime(date("Y-m-d"))) ." +1 month last day of"));//下月最后一天

            foreach ($excel_data as $key => $value) {
                //0工号,1支援网点id,2支援职位,3开始日期,4结束日期,5班次
                $error_message   = [];
                $value[3]        = date('Y-m-d', trim($value[3]));
                $value[4]        = date('Y-m-d', trim($value[4]));
                $staff_info_id   = str_replace(' ', '', $value[0]); //工号
                $excel_job_title = trim($value[2]);//职位
                $store_id        = trim($value[1]); //网点id
                $begin_date      = $value[3]; //支援开始日期
                $end_date        = $value[4]; //支援结束日期
                $excel_shift     = trim($value[5]); //支援班次数
                $job_title_id    = $temp_job_title[$excel_job_title] ?? 0; //根据excel表中职位名称反查职位id
                $staff_job_title = $staff_list[$staff_info_id]['job_title'] ?? 0; //员工职位id

                if (empty($excel_job_title)) {
                    $error_message[] = $t->_('staff_support_store_import_error_16');//'职位不能为空';
                }

                if (empty($staff_info_id)) {
                    $error_message[] = $t->_('staff_support_store_import_error_11'); //'工号不能为空';
                } else {
                    $staff_state = $staff_list[$staff_info_id]['state'] ?? 0;
                    if ($staff_state != 1) {
                        $error_message[] = $t->_('staff_support_store_import_error_1'); //'工号非在职';
                    }

                    //验证导入员工职位是否可以去支援
                    if($staff_job_title == 0  || !in_array($staff_job_title, $support_staff_apply_job_title_ids)) {
                        $job_name_list = array_map(function ($v) use ($config_job_title_list) {
                            return $config_job_title_list[$v];
                        }, $support_staff_apply_job_title_ids);
                        $job_title_str = implode(',', $job_name_list);
                        $error_message[] = $t->_('staff_support_store_import_error_29', ['job_title' => $job_title_str]); //只能导入 %职位% 的员工去支援
                    } else {
                        if(!in_array($job_title_id, $support_staff_job_title_config[$staff_job_title])) {
                            $staff_job_title_name = $staff_job_title_list[$staff_job_title] ?? '';
                            $staff_support_job_title_name_arr = array_map(function ($v) use($config_job_title_list) {
                                return $config_job_title_list[$v] ?? '';
                            }, $support_staff_job_title_config[$staff_job_title]);
                            $staff_support_job_title_name_str = implode(',', $staff_support_job_title_name_arr);
                            $error_message[] = $t->_('staff_support_store_import_error_28', ['job_title' => $staff_job_title_name, 'support_job_title' => $staff_support_job_title_name_str]);
                        }
                    }
                }

                $store_name = $store_list[$store_id]['name'] ?? '';
                if (empty($store_name)) {
                    $error_message[] = $t->_('staff_support_store_import_error_12');//'网点不能为空';
                } else {
                    $staff_store_id = $staff_list[$staff_info_id]['sys_store_id'] ?? '';
                    if ($staff_store_id == $store_id) {
                        $error_message[] = $t->_('staff_support_store_import_error_10');//'网点错误';
                    }
                }

                if (!$begin_date || !$end_date || $begin_date >= $next_year_today || $end_date >= $next_year_today) {
                    $error_message[] = $t->_('staff_support_store_import_error_8');//开始日期/结束日期格式错误
                } else {
                    if ($begin_date < $current_day) {
                        $error_message[] = $t->_('staff_support_store_import_error_9');//开始日期/结束日期格式错误
                    }

                    if ($begin_date > $end_date) {
                        $error_message[] = $t->_('staff_support_store_import_error_7');//支援开始日期不能大于结束日期
                    }
                }
                //验证入职日期 和 支援开始日期 是否符合配置
                $hireCheck = $this->checkHireSetting($staff_list[$staff_info_id], $begin_date);
                if($hireCheck !== true){
                    $error_message[] = $hireCheck;
                }

                $staff_shift_start = '';
                if (empty($excel_shift)) {
                    $error_message[] = $t->_('staff_support_store_import_error_14');//'班次不能为空';
                } else {
                    $staff_shift = explode('-', $excel_shift);
                    $staff_shift_start = trim($staff_shift[0]);
                    if (empty($shift_start_list[$staff_shift_start])) {
                        $error_message[] = $t->_('staff_support_store_import_error_4');//'班次数据有误';
                    }
                }
                $is_stay = 0;
                //是否存在出差
                $tripServer = new BusinessTripService();
                $exist = $tripServer->checkExistTime($begin_date,$end_date,$staff_info_id);
                if (!empty($exist)) {
                    $error_message[] = $t->_('support_check_trip');
                }

                //是否存在其他支援
                $support_count = $this->validateConflict($begin_date, $end_date, $staff_info_id);
                if ($support_count > 0) {
                    $error_message[] = $t->_('staff_support_store_import_error_6');//该工号已经有支援数据
                }

                $sub_staff_info_id = 0;
                $staff_info = $staff_list[$staff_info_id];
                if (empty($error_message) && $begin_date == $current_day) {
                    $staff_info['position_category'] = $staff_position[$staff_info_id] ?? [];
                    $staff_info['manager'] = $staff_list[$staff_info_id]['manger'] ?? '';
                    $sub_staff_info_id = $this->createSubStaff($staff_info,
                        [
                            'job_title' => $job_title_id,
                            'store_id' => $store_id,
                            'employment_begin_date' => $begin_date,
                            'position_category' => $support_job_title_role_config[$job_title_id] ?? []
                        ]);
                    if (empty($sub_staff_info_id)) {
                        $error_message[] = $t->_('staff_support_store_import_error_15');//子账号生成失败
                    }
                    //子账号发送合同消息
                    $staffContractService = new StaffContractService();
                    $staffContractService->sendContractMsgToSubStaff($staff_info_id,$sub_staff_info_id);
                    //给子账号 发送简历完善通知 消息
                    $sendResult = (new StaffNoticeService())->sendResumeMessage($staff_info_id, $sub_staff_info_id, $begin_date);
                    $this->logger->info([
                        'function' => 'StaffSupportStoreServer-ImportStaffSupport-sendResumeMessage',
                        'params' => [$staff_info_id, $sub_staff_info_id, $begin_date],
                        'result' => $sendResult
                    ]);
                }

                if (empty($error_message)) {
                    // 计算两个网点经纬度之间的距离
                    $apart = (new SysStoreService())->calculateDistanceStore($store_id,$staff_info['sys_store_id']);
                    $insertData = [
                        'serial_no'             => $staffSupportService->getSerialNo('SASS'),
                        'staff_info_id'         => $staff_info_id,
                        'job_title_id'          => $job_title_id,
                        'store_id'              => $store_id,
                        'store_name'            => $store_name,
                        'employment_begin_date' => $begin_date,
                        'employment_end_date'   => $end_date,
                        'employment_days'       => (strtotime($end_date) - strtotime($begin_date)) / 86400 + 1,
                        'shift_id'              => $shift_start_list[$staff_shift_start]['id'] ?? 0,
                        'shift_type'            => $shift_start_list[$staff_shift_start]['type'] ?? '',
                        'shift_start'           => $shift_start_list[$staff_shift_start]['start'] ?? '',
                        'shift_end'             => $shift_start_list[$staff_shift_start]['end'] ?? '',
                        'status'                => 2,
                        'sub_staff_info_id'     => $sub_staff_info_id,
                        'staff_store_id'        => $staff_info['sys_store_id'] ?? '', //申请人所属网点id
                        'support_status'        => $begin_date == $current_day ? self::$support_status['in_force'] : self::$support_status['pending'],
                        'is_stay'               => $is_stay,
                        'actual_begin_date'     => $begin_date == $current_day ? $begin_date : null,
                        'data_source'           => HrStaffApplySupportStoreModel::DATA_SOURCE_2,
                        'apart'                 => $apart ?? null,
                        'create_staff_id'       => $params['operator_id'],
                    ];
                    $res = $db->insertAsDict("hr_staff_apply_support_store", $insertData);
                    $last_id = $db->lastInsertId();
                    if ($res) {
                        //加班次变更操作日志
                        $logParam['staff_info_id'] = $staff_info_id;
                        $logParam['date_at']       = $begin_date;
                        $logParam['operate_id']    = $params['operator_id'];
                        $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
                        $extend['before']          = '';
                        $extend['after']           = "{$insertData['shift_start']}-{$insertData['shift_end']} ({$begin_date}~{$end_date})";
                        $logServer                 = new WorkShiftService();
                        $logServer->addShiftLog($logType, $logParam, $extend);

                        if($begin_date == $current_day) {
                            $sync_ms_params[] = [
                                'id' => $last_id,
                                'staff_id' => $sub_staff_info_id,
                                'master_staff' => $staff_info_id,
                                'begin_at' => strtotime($begin_date),
                                'end_at' => strtotime($end_date) + 86399
                            ];
                            //如果是当天入职的 van 非个人代理 给子账号发车厢完善消息
                            $hireDate = date('Y-m-d', strtotime($staff_list[$staff_info_id]['hire_date']));
                            if($hireDate == $current_day && $staff_list[$staff_info_id]['job_title'] == enums::$job_title['van']
                                && !in_array($staff_list[$staff_info_id]['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
                                $this->sendVanContainerMsg($sub_staff_info_id);
                            }

                        }
                        //生成班次信息 不管是不是今天开始 都需要生成主账号班次信息 如果是今天需要生成双账号的
                        $createShiftInfo = [
                            'staff_info_id'     => $staff_info_id,
                            'sub_staff_info_id' => $sub_staff_info_id,
                            'shift_start_date'  => $begin_date,
                            'shift_end_date'    => $end_date,
                            'shift_id'          => $insertData['shift_id'],
                            'shift_type'        => $insertData['shift_type'],
                            'shift_start'       => $insertData['shift_start'],
                            'shift_end'         => $insertData['shift_end'],
                            'operator_id'       => $params['operator_id'],
                        ];
                        $this->createSupportStaffShift($createShiftInfo, true);


                        $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
                        //$title = '支援网点通知';
                        //$content = '支援任务：支援网点 %store_name%，支援日期为%employment_date%，支援班次为%staff_shift%，支援期间请在支援网点打卡上班。';
                        $by_message_title = BaseService::getTranslation($lang)->_('staff_support_store_import_message_title');
                        $by_message_content = BaseService::getTranslation($lang)->_('staff_support_store_import_message_content_sno',
                            [
                                'store_name'      => $store_name,
                                'employment_date' => $begin_date.'-'.$end_date,
                                'staff_shift'     => $excel_shift,
                                'serial_no'       => $insertData['serial_no'],

                            ]);
                        $by_message_params = [
                            'staff_info_id' => $staff_info_id,
                            'message_title' => $by_message_title,
                            'message_content' => $by_message_content,
                            'source' => 'import_staff_support'
                        ];

                        $message_result = $this->sendStaffBackyardMessageV2($by_message_params);
                        if ($message_result) {
                            //发送push
                            $message_title = BaseService::getTranslation($lang)->_('staff_support_store_import_message_title');
                            $message_content = BaseService::getTranslation($lang)->_('staff_support_store_import_message_content_sno',
                                [
                                    'store_name'      => $store_name,
                                    'employment_date' => $begin_date.'-'.$end_date,
                                    'staff_shift'     => $excel_shift,
                                    'serial_no'       => $insertData['serial_no'],

                                ]);
                            $message_params = [
                                'staff_info_id' => $staff_info_id,
                                'message_title' => $message_title,
                                'message_content' => $message_content,
                                'source' => 'import_staff_support'
                            ];
                            $this->sendStaffBackyardPush($message_params);
                        }
                    } else {
                        $error_message[] = $t->_('staff_support_store_import_error_5');
                        $value[] = implode(',', $error_message);
                        array_push($error_list, $value);
                    }
                } else {
                    $value[] = implode(',', $error_message);
                    array_push($error_list, $value);
                }
            }
            if (!empty($sync_ms_params)) {
                $this->logger->info([
                    'function' => 'ImportStaffSupport',
                    'message' => '同步子账号信息',
                    'sync_ms_params' => $sync_ms_params,
                ]);

                $this->syncMsSupportApply($sync_ms_params, 'addStaffSupportInfo');
            }
            $return_data['success_num'] = count($excel_data);
            $return_data['error_num'] = 0;
            $return_data['url'] = '';
            if (!empty($error_list)) {
                $fileName = 'ImportSupportError_' . date('Ymdhis') . '_' . self::$language . date('Ymd-His') . '.xlsx';
                $excel_file = $this->exportExcel([
                    'Staff_id',
                    'Give Support Branch Number',
                    'Give Support Position',
                    'Start Date',
                    'End Date',
                    'Support Shift',
                    'Result'
                ], $error_list, $fileName);
                $flashOss = new FlashOss();
                $json_url_name = self::STAFF_SUPPORT_STORE_OSS_PATH . '/' . $fileName;
                $flashOss->uploadFile($json_url_name, $excel_file['data']);
                $url = $flashOss->signUrl($json_url_name, 86400*7);

                $return_data['error_num'] = count($error_list);
                $return_data['success_num'] = $return_data['success_num'] - $return_data['error_num'];
                $return_data['url'] = $url;
            }

            //更新
            $db->updateAsDict(
                'hr_import_staff_support_excel',
                [
                    'result_file_path' => $return_data['url'],
                    'import_success_count' => $return_data['success_num'],
                    'import_error_count' => $return_data['error_num'],
                    'status' => 2,
                ],
                'id = ' . $params['import_excel_id']
            );
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'ImportStaffSupport',
                'message' => $e->getMessage(),
                'Line' => $e->getLine(),
                'file' => $e->getFile()
            ]);
        }
        return $return_data;
    }

    /**
     * 员工支援网点导出
     * @param $params
     * @return string
     * @throws Exception
     */
    public function exportStaffSupport($params)
    {
        $page_num  = 1;
        $page_size = 1000;
        $export_row = [];
        while (true) {
            $offset  = $page_size * ($page_num - 1);
            $builder = $this->getStaffSupportListBuilder($params);
            $builder->limit($page_size, $offset);
            $list = $builder->orderBy('hs.id DESC')->getQuery()->execute()->toArray();
            $list = $this->combinationStaffSupportList($list);
            if (empty($list)) {
                break;
            }
            $page_num ++;
            foreach ($list as $key => $value) {
                $export_row[] = [
                    $value['serial_no'],
                    $value['data_source'],
                    $value['staff_info_id'],
                    $value['staff_info_name'],
                    $value['job_title_name'],
                    $value['staff_store_name'],
                    $value['store_name'],
                    $value['sub_staff_info_id'],
                    $value['employment_begin_date'],
                    $value['employment_end_date'],
                    $value['support_shift'],
                    $value['employment_days'],
                    $value['is_today_punch_in_text'],
                    $value['is_today_punch_out_text'],
                    $value['support_status_text'],
                    $value['status_text'],
                    $value['operate_name'],
                    $value['create_time'],
                    $value['approval_agreed_time'],
                ];
            }

        }

        $head = [
            self::$t->_('title_serial_no'),
            self::$t->_('staff_support_store_data_source'),
            self::$t->_('staff_support_store_staff_info_id'),
            self::$t->_('staff_support_store_staff_name'),
            self::$t->_('staff_support_store_staff_job_title'),
            self::$t->_('staff_support_store_staff_store'),
            self::$t->_('staff_support_store_store'),
            self::$t->_('staff_support_store_sub_staff_info_id'),
            self::$t->_('staff_support_store_begin_date'),
            self::$t->_('staff_support_store_end_date'),
            self::$t->_('staff_support_store_shift'),
            self::$t->_('staff_support_store_employment_days'),
            self::$t->_('staff_support_store_today_punch'),
            self::$t->_('staff_support_store_today_punch_out'),
            self::$t->_('staff_support_store_status'),
            self::$t->_('approve_state_name'),
            self::$t->_('creator'),
            self::$t->_('created_at'),
            self::$t->_('final_approval_time'),
        ];

        $fileName = date("YmdHis") . '_staff_support_list.xls';
        $excel_file = $this->exportExcel($head, $export_row, $fileName);

        $flashOss = new FlashOss();
        //每个国家有每个国家的名字
        $json_url_name = self::STAFF_SUPPORT_STORE_OSS_PATH . '/' . $fileName;
        $flashOss->uploadFile($json_url_name, $excel_file['data']);
        return $json_url_name;
    }

    //入职当天就去支援了 要发车厢信息
    public function sendVanContainerMsg($staffId)
    {
        //没生成子账号
        if(empty($staffId)){
            return true;
        }
        //获取员工语言环境
        $lang = (new StaffService())->getAcceptLanguage($staffId);
        self::setLanguage(strtolower($lang));
        $title          = self::$t->_('van_container_confirm_van_title');
        $content        = 'type=2';
        $messageService = new MessagesService();
        $send_message   = [
            'staff_users'        => [$staffId],
            'message_title'      => $title,
            'message_content'    => $content,
            'staff_info_ids_str' => $staffId,
            'category'           => EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_VAN_CONTAINER'),
            'push_state'         => 1,
            'id'                 => time() . $staffId . rand(1000000, 9999999),
        ];
        $messageService->add_kit_message($send_message);
    }

}