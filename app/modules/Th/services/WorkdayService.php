<?php
/**
 * Author: Bruce
 * Date  : 2022-04-02 17:58
 * Description:
 */

namespace App\Modules\Th\Services;

use App\Library\AttendanceEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\Enums\ShiftEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysStoreModel;
use App\Services\DefaultRestDayService;
use App\Services\HrShiftService;
use App\Services\LeaveBllService;
use App\Services\SchedulingSuggestionService;
use App\Services\StaffPublicHolidayService;
use App\Services\SysService;
use App\Services\WorkdayService as BaseWorkdayService;

class WorkdayService extends BaseWorkdayService
{

    //排班建议用
    public $workDayOff;
    public $shiftData;


    public function formatList($param, $data)
    {
        $month = $param['month'] ?? date('Y-m', time());
        //获取公休日 1:6天班的 2:5天班
        $holiday_data = $this->get_holidays(['type' => 7 - $param['week_working_day'], 'date' => $month . '-01']);
        $holidays     = $holiday_data[$param['week_working_day']] ?? [];

        //获取 轮休日期
        $staff_ids   = array_column($data, 'staff_info_id');
        $workDaysOff = $this->get_workdays_by_ids($staff_ids, $month);

        //班次 班次server  过去日期 history表 当天 hr shift 未来 临时中间表和 preset
        $shiftServer     = new HrShiftService();
        $dateList        = getDateByMonth($month);
        //主播职位
        $this->setLiveJob();
        //如果 查询开始日期小于当天把上个月的也取出来
        $shiftStaffIds = $this->getShiftStaffIds($data);
        if(date('Y-m-d') < $dateList[0]){
            $lastMonth = date('Y-m',strtotime("{$dateList[0]} -1 month"));
            $dates = array_merge(getDateByMonth($lastMonth),$dateList);
            $this->shiftData = $shiftServer->getShiftInfos($shiftStaffIds, $dates);
        }else{
            $this->shiftData = $shiftServer->getShiftInfos($shiftStaffIds, $dateList);
        }

        //获取 请假
        $leaveServer     = new LeaveBllService();
        $leaveData       = $leaveServer->getLeaveByDate($staff_ids, $dateList);
        $this->leaveData = empty($leaveData) ? [] : array_column($leaveData, null, 'u_key');

        //每个格子的请假班次信息 把请假和班次 拼装到一个list
        $cubeInfo = $this->workdayCube($staff_ids, $dateList);

        //获取员工个人维度的公共假期
        $staffPublicHoliday = (new StaffPublicHolidayService())->getMultiStaffData($staff_ids);
        $defaultRestStaffMap = (new DefaultRestDayService())->getAll($staff_ids);
        foreach ($data as &$v) {
            if ($v['state'] == HrStaffInfoModel::STATE_ON_JOB && $v['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $v['state'] = 999;
            }
            $v['state_name']       = !empty(\App\Library\Enums::$hris_working_state[$v['state']]) ? self::$t->_(\App\Library\Enums::$hris_working_state[$v['state']]) : '';

            //主播职位 不能点击班次
            $v['is_live_master'] = in_array($v['job_title'], $this->liveJobId);
            $v['shift'] = empty($workDaysOff[$v['staff_info_id']][$month]) ? [] : $workDaysOff[$v['staff_info_id']][$month];
            //拼接总部名称
            if (empty($v['store_name']) && $v['id'] == '-1') {
                $v['store_name'] = GlobalEnums::HEAD_OFFICE;
            }
            $v['hire_type_text']        = self::$t->_('hire_type_'.$v['hire_type']);

            //默认轮休日
            $v['default_rest_day_date'] = $defaultRestStaffMap[$v['staff_info_id']] ?? [];

            $v['holiday']['ph'] = array_merge($holidays, $staffPublicHoliday[$v['staff_info_id']] ?? []);
            //方块里面新增的信息
            $v['cube_info'] = $cubeInfo[$v['staff_info_id']] ?? [];
        }
        return $data;
    }






}