<?php

namespace App\Modules\My\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SalaryHistoryModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffPayrollModel;
use App\Models\backyard\StaffSalaryItemsModel;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Modules\My\Services\PayrollBankFileService;
use App\Modules\My\Services\SalaryService;
use App\Modules\My\Tasks\PayrollBankFileTask;
use App\Services\ExcelService;


class PayrollBankFileController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 查询
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function queryAction()
    {
        $param['period_type'] = $this->request->get('period_type', 'int');
        $param['month']       = $this->request->get('month', 'trim');
        $param['company_id']  = $this->request->get('company_id', 'trim');
        Validation::validate($param, [
            "period_type" => "Required|IntIn:" . implode(',', array_keys(StaffPayrollModel::$runTypeMap)),
            "month"       => "Required",
            "company_id"  => "Required|IntIn:" . implode(',', array_keys(SalaryEnums::$companyMap)),
        ]);
        $param['operate_id'] = $this->user['id'];
        $bll  = new PayrollBankFileService();
        $data = $bll->list($param);

        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 银行类型
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function bankTypesAction()
    {
        return $this->returnJson(ErrCode::SUCCESS, 'ok', SalaryEnums::$payrollBankMap);
    }

    /**
     * 异步下载 txt 文件
     * @Token
     * @throws ValidationException
     * @throws \Exception
     */
    public function downloadAction()
    {
        $param['period_type'] = $this->request->get('period_type', 'int');
        $param['month']       = $this->request->get('month', 'trim');
        $param['company_id']  = $this->request->get('company_id', 'int');
        $param['bank_type']   = $this->request->get('bank_type', 'int');
        $param['file_type']   = $this->request->get('file_type', 'int');
        Validation::validate($param, [
            "period_type" => "Required|IntIn:" . implode(',', array_keys(StaffPayrollModel::$runTypeMap)),
            "month"       => "Required",
            "company_id"  => "Required|IntIn:" . implode(',', array_keys(SalaryEnums::$companyMap)),
            "bank_type"   => "Required|IntIn:" . implode(',', array_keys(SalaryEnums::$payrollBankMap)),
            "file_type"   => "Required|IntIn:" . implode(',', array_keys(SalaryEnums::$payrollBankFIleMap)),
        ]);
        if ($param['bank_type'] == SalaryEnums::PAYROLL_BANK_CIMB && in_array($param['file_type'],
                [SalaryEnums::PAYROLL_BANK_FILE_SALARY, SalaryEnums::PAYROLL_BANK_FILE_EPF])
            || ($param['bank_type'] == SalaryEnums::PAYROLL_BANK_OCBC && $param['file_type'] == SalaryEnums::PAYROLL_BANK_FILE_SALARY)) {
            $staffType          = $this->request->get('staff_type', 'trim');
            $param['staff_ids'] = $this->request->get('staff_ids', 'trim');
            // 支付日期
            $param['pay_date'] = $this->request->get('pay_date', 'trim');
            if ($staffType == SalaryEnums::PAYROLL_BANK_STAFF_APPOINT && empty($param['staff_ids']) || empty($param['pay_date'])) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'params error');
            }
            if ($staffType == SalaryEnums::PAYROLL_BANK_STAFF_APPOINT) {
                $param['staff_ids'] = clearSpecialChar($param['staff_ids']);
            } else {
                $param['staff_ids'] = null;
            }
        }
        if ($param['bank_type'] == SalaryEnums::PAYROLL_BANK_OCBC && $param['file_type'] == SalaryEnums::PAYROLL_BANK_FILE_SALARY) {
            $param['company_mess'] = $this->request->get('company_mess');
            $param['tape_id']      = $this->request->get('tape_id', 'int');
            preg_match('/^[a-zA-Z0-9\\s]{1,16}+$/', $param['company_mess'], $m);
            preg_match('/^[0-9]{3}$/', $param['tape_id'], $t);
            if (empty($m)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR,
                    'The company profile only supports a combination of letters and numbers');
            }
            if (empty($t)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Tape id format is 3 numbers');
            }
        }
        if ($param['bank_type'] == SalaryEnums::PAYROLL_BANK_HSBC && $param['file_type'] == SalaryEnums::PAYROLL_BANK_FILE_SALARY) {
            $param['company_mess'] = $this->request->get('company_mess');
            $param['remark']       = $this->request->get('remark', 'trim');
            preg_match('/^[a-zA-Z0-9\\s]{1,8}$/', $param['company_mess'], $m);
            preg_match('/^[a-zA-Z0-9]{1,24}$/', $param['remark'], $t);
            if (empty($m)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR,
                    'The company profile only supports a combination of letters and numbers');
            }
            if (empty($t)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR,
                    'Remark only supports a combination of letters and numbers');
            }
        }
        $service    = new ExcelService();
        $userId     = $this->user['id'];
        $fileName   = SalaryEnums::$payrollBankFIleMap[$param['file_type']] . '_' . SalaryEnums::$payrollBankMap[$param['bank_type']] . date('YmdHis') . '.txt';
        $actionName = PayrollBankFileTask::$actionName;
        $data       = $service->insertTask($userId, $actionName, $param, 0, $fileName);
        return $this->returnJson($data['code'], $data['message']);
    }


}
