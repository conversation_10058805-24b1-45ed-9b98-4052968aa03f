<?php

namespace App\Modules\My\Tasks;


use App\Library\FlashOss;
use App\Library\OssHelper;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Modules\My\Services\PayrollBankFileService;
use App\Services\OssService;
use OSS\OssClient;

class PayrollBankFileTask extends \BaseTask
{
    public static $actionName = 'PayrollBankFile download';

    //下载银行相关salary文件
    public function downloadAction()
    {
        [$findTask, $params] = $this->getHcmExcelTack(static::$actionName);
        if ($findTask) {
            if (!isset($params['period_type'], $params['month'], $params['company_id'], $params['bank_type'], $params['file_type'])
                || ($params['bank_type'] == SalaryEnums::PAYROLL_BANK_OCBC && $params['file_type'] == SalaryEnums::PAYROLL_BANK_FILE_SALARY && !isset($params['company_mess'], $params['tape_id']))
                || ($params['bank_type'] == SalaryEnums::PAYROLL_BANK_HSBC && $params['file_type'] == SalaryEnums::PAYROLL_BANK_FILE_SALARY && !isset($params['company_mess'], $params['remark']))
            ) {
                $this->echoErrorLog(__METHOD__.'param Error:', json_encode($params));
                $findTask->save();
                return;
            }
            $service = new PayrollBankFileService();
            $data    = $service->downloadFileTxt($params);
            if (!isset($data['list'])) {
                $this->echoInfoLog("此条件无数据");
                $findTask->save();
                return;
            }
            $this->logger->info([$params,$data]);
            $this->echoInfoLog('总数据量'.count($data['list']));
            try {
                $pathFileName = '/tmp/'.$findTask->file_name;

                $str       = implode(PHP_EOL, $data['list']);
                $source = fopen($pathFileName, "w");
                fwrite($source, $str);
                fclose($source);
                                               //关闭压缩包
                $ossService = new FlashOss();
                $path = OssService::PDF_SPACE_CERTIFICATE.'/'.date('Ymd').'/'.$findTask->file_name;
                $ossService->uploadFile($path,$pathFileName,[OssClient::OSS_CONTENT_DISPOSTION=> 'attachment',OssClient::OSS_CONTENT_TYPE=> OssClient::DEFAULT_CONTENT_TYPE]);

                $findTask->path = $path;
                $findTask->save();
            } catch (\Exception $e) {
                $findTask->save();
                $this->echoErrorLog(__METHOD__.$e->getMessage());
            }
        }
        echo 'Done'.PHP_EOL;
    }


}
