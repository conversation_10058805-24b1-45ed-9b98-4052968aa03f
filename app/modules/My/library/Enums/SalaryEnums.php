<?php

namespace App\Modules\My\library\Enums;

use App\Models\backyard\SalaryBaseSettingModel;
use App\Models\backyard\StaffPayrollModel;
use App\Models\backyard\StaffSalaryTaxLogModel;

/**
 * 常用枚举数据 用于数据导入字符对比
 * Class enums
 */
final class SalaryEnums
{
    public static $salaryBaseType = [
        'salary'    => 1,
        'allowance' => 2,
        'deduction' => 3,
        'bonus'     => 4,
    ];

    const TYPE_SALARY = 1;
    const TYPE_TP = 2;

    public static $itemsType    = [
        'salary' => self::TYPE_SALARY,
        'tp'     => self::TYPE_TP,
    ];
    public static $globalOption = [
        'y' => 1,
        'n' => 2,
    ];

    public static $unit = [
        'day' => SalaryBaseSettingModel::UNIT_TYPE_DAY,
        'month' => SalaryBaseSettingModel::UNIT_TYPE_MONTH,
    ];
    public static $payFlag = [
        'ee' => 1,
        'er' => 2,
    ];

    public static $periodType = [
        'end - month'  => 1,
        'mid - month'  => 2,
        'add - month'  => 3,
        'add - month2' => 4,
        'add - month3' => 5,
    ];

    public static $settlementType = [
        'once a month'           => 1,
        'multiple times a month' => 2,
    ];

    public static $payType = [
        'cash'    => 1,
        'bank'    => 2,
        'cheque'  => 3,
        'witheld' => 4,
    ];

    public static $marital = [
        'single'       => 1,
        'married'      => 2,
        'divorced'     => 3,
        //'child relief' => 4,
    ];

    public static $taxType = [
        'national'     => 1,
        'non-national' => 2,
        'rep'          => 3,
        'irda'         => 4,
        'no tax'       => 5,
    ];

    public static $taxMethod = [
        'ee' => 1,
        'er' => 2,
    ];

    public static $epfState = [
        'normal 11/13'              => 1,
        'over 60 years old 5.5/6.5' => 2,
        'non - national'            => 3,
        'custom'                    => 4,
        'no need to pay'            => 5,
    ];

    public static $socsoState = [
        'national and under 60'      => 1,
        'non - national and over 60' => 2,
        'no socso'                   => 3,
    ];

    public static $eisState = [
        'normal'         => 1,
        'no need to pay' => 2,
    ];

    public static $zakatType = [
        'numerical'  => 1,
        'percentage' => 2,
    ];

    public static $allNotItems = [
        2                                       => 'NORMAL DAY OT',
        3                                       => 'OFF DAY OT',
        4                                       => 'RD',
        5                                       => 'RDOT',
        6                                       => 'PH',
        7                                       => 'PHOT',
        SalaryBaseSettingModel::ABSENT          => 'ABSENT',
        SalaryBaseSettingModel::UNPAID_LEAVE    => 'UNPAID_LEAVE',
        SalaryBaseSettingModel::NPL             => 'NPL',
        SalaryBaseSettingModel::HUB_NIGHT_SHIFT => 'HUB NIGHT SHIFT',
        SalaryBaseSettingModel::PENALTY_CAR     => 'PENALTY CAR',
        SalaryBaseSettingModel::PENALTY_FUEL    => 'PENALTY FUEL',
        SalaryBaseSettingModel::PENALTY_VEHICLE => 'PENALTY VEHICLE',

    ];

    public static $salaryNotItems = [
        8  => 'PV.EPF',
        9  => 'PV.PCB',
        10 => 'PV.SOCSO',
        12 => 'PV.INC',
    ];
    /**
     * 马来真实公司ID
     */
    const FLASH_EXPRESS_COMPANY_ID = 315;
    const F_COMMERCE_COMPANY_ID = 15006;
    const FULFILLMENT_COMPANY_ID = 20001;
    const FLASH_PAY_ID = 60001;
    const FLASH_MONEY_ID = 15050;
    const LNT_ID = -1;

    /**
     * 对应 App\Models\backyard\StaffPayrollModel  public static $companyMap
     * @var int[]
     */
    public static $salaryCompanyCodeMap = [
        self::FLASH_EXPRESS_COMPANY_ID => self::FLASH_EXPRESS,
        self::FULFILLMENT_COMPANY_ID   => self::FLASH_FULFILLMENT,
        self::FLASH_PAY_ID             => self::FLASH_PAY,
        self::F_COMMERCE_COMPANY_ID    => self::FLASH_COMMERCE,
        self::FLASH_MONEY_ID           => self::FLASH_MONEY,
        self::LNT_ID                   => self::LNT,
    ];
    const FLASH_EXPRESS = 1;
    const FLASH_FULFILLMENT = 2;
    const FLASH_PAY = 3;
    const FLASH_COMMERCE = 4;
    const FLASH_MONEY = 5;
    const LNT = 6;//特殊LNT
    public static $companyMap = [
        self::FLASH_EXPRESS     => 'Flash Express',
        self::FLASH_FULFILLMENT => 'Flash Fulfillment',
        self::FLASH_PAY         => 'Flash Pay',
        self::FLASH_COMMERCE    => 'Flash Commerce',
        self::FLASH_MONEY       => 'Flash Money',
        self::LNT               => 'LNT',
    ];
    /**
     * 薪酬计算特殊名单 1～5 配置后出现在对应的公司
     * 枚举对应 $salaryCompanyCodeMap
     * 99 为外籍专才名单
     * 100 为特殊发薪名单
     * @var string[]
     */
    public static $specialStaffTypeMap = [
        self::FLASH_EXPRESS     => 'salary_special_flash_express',
        self::FLASH_FULFILLMENT => 'salary_special_fulfillment',
        self::FLASH_PAY         => 'salary_special_flash_pay',
        self::FLASH_COMMERCE    => 'salary_special_f_commerce',
        self::FLASH_MONEY       => 'salary_special_flash_money',
        self::LNT               => 'salary_special_lnt',
        99                      => 'foreign_professionals_list',
        100                     => 'special_payroll_list',
    ];

    /**
     * 薪酬用户报告
     */
    const PAYROLL_REPORT_OT = 1;
    const PAYROLL_REPORT_PAYROLL_SUMMARY = 2;
    const PAYROLL_REPORT_STATUTORY_FEE = 3;            //法定费用
    const PAYROLL_REPORT_YEAR_COMPANY_STATISTICS = 4;  // 公司 月 汇总
    const PAYROLL_REPORT_YEAR_EMPLOYEE_STATISTICS = 5; // 月员工
    const  PAYROLL_REPORT_YEAR_TO_DATE_PAYCODE = 6; // 月员工 paycode

    public static $payrollReportMap = [
        self::PAYROLL_REPORT_OT                       => 'OT Report',
        self::PAYROLL_REPORT_PAYROLL_SUMMARY          => 'Payroll Summary',
        self::PAYROLL_REPORT_STATUTORY_FEE            => 'Statutory Fee',
        self::PAYROLL_REPORT_YEAR_COMPANY_STATISTICS  => 'Year to Date Company Statistics',
        self::PAYROLL_REPORT_YEAR_EMPLOYEE_STATISTICS => 'Year to Date Employee Statistics',
        self::PAYROLL_REPORT_YEAR_TO_DATE_PAYCODE     => 'Year to Date Paycode',
    ];

    /**
     * 银行类型
     */
    const PAYROLL_BANK_CIMB = 1;
    const PAYROLL_BANK_OCBC = 2;
    const PAYROLL_BANK_HSBC = 3;
    public static $payrollBankMap  = [
        self::PAYROLL_BANK_CIMB => 'CIMB',
        self::PAYROLL_BANK_OCBC => 'OCBC',
        self::PAYROLL_BANK_HSBC => 'HSBC',
    ];
    public static $payrollBankCode = [
        self::PAYROLL_BANK_CIMB => '35',
        self::PAYROLL_BANK_OCBC => '27',
        self::PAYROLL_BANK_HSBC => '10',
    ];
    /**
     * 员工银行卡类型  from hris
     * @var string[]
     */
    public static $bankType            = [
        '15' => 'CIMB',
        '54' => 'Maybank',
        '83' => 'BSN',
    ];
    public static $bankTypeCodeForCIMB = [
        '15' => '35',
        '54' => '27',
        '83' => '10',
        '64' => '10',
        '88' => '32',
        '57' => '12',
        '59' => '08',
        '62' => '02',
        '71' => '22',
        '79' => '33',
        '78' => '29',
        '80' => '18',
        '50' => '14',
        '8'  => '26',
        '75' => '75',
        '70' => '24',
        '61' => '45',
        '63' => '41',
        '56' => '49',
    ];
    public static $bankTypeCode        = [
        '15' => '35',
        '54' => '27',
        '83' => '06',
    ];
    public static $bankTypeCodeV2      = [
        '15' => '*********',
        '54' => '*********',
        '83' => '*********',
        '88' => '*********',
        '57' => '*********',
        '59' => '*********',
        '62' => '*********',
        '71' => '*********',
        '79' => '*********',
        '78' => '*********',
        '80' => '*********',
        '50' => '*********',
        '8'  => '*********',
        '75' => '*********',
        '70' => '*********',
        '61' => '*********',
        '63' => '*********',
        '56' => '*********',
    ];
    /**
     * 银行文件类型
     */
    const PAYROLL_BANK_FILE_SALARY = 1;
    const PAYROLL_BANK_FILE_EIS = 2;
    const PAYROLL_BANK_FILE_SOCSO = 3;
    const PAYROLL_BANK_FILE_EPF = 4;
    const PAYROLL_BANK_FILE_TAX = 5;
    public static $payrollBankFIleMap = [
        self::PAYROLL_BANK_FILE_SALARY => 'Salary',
        self::PAYROLL_BANK_FILE_EIS    => 'EIS',
        self::PAYROLL_BANK_FILE_SOCSO  => 'SOCSO',
        self::PAYROLL_BANK_FILE_EPF    => 'EPF',
        self::PAYROLL_BANK_FILE_TAX    => 'TAX',
    ];
    /**
     * 1全员/2指定工号
     */
    const PAYROLL_BANK_STAFF_ALL = 1;
    const PAYROLL_BANK_STAFF_APPOINT = 2;
    /**
     * 个税类型 MAP
     * @var string[]
     */
    public static $mtdMap = [
        StaffPayrollModel::MTD_FOR_CURRENT => 'mtd_for_current',
        StaffPayrollModel::MTD_FOR_NR      => 'mtd_for_nr',
        StaffPayrollModel::MTD_FOR_REP     => 'mtd_for_rep',
        StaffPayrollModel::MTD_FOR_IRDA    => 'mtd_for_irda',
    ];

    //个税操作日志来源
    public static $salaryLogSource = [
        StaffSalaryTaxLogModel::SOURCE_HCM => 'hcm_edit',
        StaffSalaryTaxLogModel::SOURCE_BY  => 'by_edit',
    ];
    /**
     * 薪资报告下载类型
     * @var string[]
     */
    const REPORT_DOWNLOAD_TYPE_ALL = 'all';
    const REPORT_DOWNLOAD_TYPE_APPOINT = 'appoint';
    public static $salaryReportDownloadType = [
        self::REPORT_DOWNLOAD_TYPE_ALL,
        self::REPORT_DOWNLOAD_TYPE_APPOINT,
    ];
}
