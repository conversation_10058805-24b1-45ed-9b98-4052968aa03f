<?php

namespace App\Modules\My\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums\ApprovalEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\FlashOss;
use App\Library\RocketMQ;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrOvertimeModel;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffShiftV2BackupModel;
use App\Models\backyard\HrStaffShiftV2Model;
use App\Models\backyard\MessageModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\SupportOvertimeBackupModel;
use App\Modules\My\library\Enums\enums;
use App\Services\HrJobTitleService;
use App\Services\HrShiftService;
use App\Services\StaffContractService;
use App\Services\StaffNoticeService;
use App\Services\StaffService;
use App\Services\StaffSupportService;
use App\Services\SysDepartmentService;
use App\Services\SysStoreService;
use App\Services\WorkShiftService;
use Exception;


class StaffSupportStoreServer extends \App\Services\StaffSupportStoreServer
{

    public function getSupportShiftList(): array
    {
        $enable_shift_ids = (new SettingEnvService())->getSetVal('hcm_store_support_shift_ids',',');
        $list       = (new HrShiftService())->setEnableShiftIds($enable_shift_ids)->getV2ShiftList();
        $shift_list = [];
        foreach ($list as $item) {
            $shift_list[] = [
                'id'              => $item['id'],
                'type'            => '',
                'type_text'       => '',
                'start'           => $item['first_start'],
                'shift_name'      => trim($item['shift_name']),
                'check_shift_name'=> trim($item['shift_name']).' '.$item['first_start'].'-'.$item['first_end'],
                'end'             => $item['first_end'],
                'shift_extend_id' => $item['shift_extend_id'],
                'shift_text'      => $item['shift_name'].' '.$item['first_start'].'-'.$item['first_end'],
                'shift_duration'  => $item['shift_duration'],
            ];
        }
        return $shift_list;
    }


    public function getSupportShiftInfo(&$detail, $shift_id)
    {
        $shift_detail            = (new StaffShiftService())->getSupportShiftInfo($shift_id);
        $detail->shift_id        = $shift_id;
        $detail->shift_type      = null;
        $detail->shift_extend_id = $shift_detail['shift_extend_id'];
        $detail->shift_start     = $shift_detail['first_start'];
        $detail->shift_end       = $shift_detail['first_end'];
        return $shift_detail;
    }

    //取消订单 还原主账号 只有没生效的才能取消
    public function cancelStaffSupportShift($supportData): bool
    {
        //是否需要操作子账号
        if ($supportData['support_status'] == self::$support_status['in_force'] && !empty($supportData['sub_staff_info_id'])) {
            //删除子账号班次信息
            HrStaffShiftV2Model::find([
                'conditions' => 'shift_date >= :start_date: and shift_date <= :end_date: and staff_info_id = :sub_id:',
                'bind'       => [
                    'sub_id'     => $supportData['sub_staff_info_id'],
                    'start_date' => $supportData['employment_begin_date'],
                    'end_date'   => $supportData['employment_end_date'],
                ],
            ])->delete();
        }

        $shiftServer = new StaffShiftService();
        $dateList    = DateHelper::DateRange(strtotime($supportData['employment_begin_date']), strtotime($supportData['employment_end_date']));
        $shiftServer->shiftReBack($supportData, $dateList);
        return true;
    }


    /**
     * 处理支援日期调整导致的班次变化
     * @param $beforeSupportData
     * @param $afterSupportData
     * @param $operate_id
     * @return bool
     */
    public function modifyStaffShift($beforeSupportData, $afterSupportData, $operate_id = 10000): bool
    {
        $subFlag = false;//申请是否已生效 判断是否操作 子账号 没生效 没有子账号
        if ($beforeSupportData['support_status'] == self::$support_status['in_force']) {
            $subFlag = true;
        }
        $shiftServer = new StaffShiftService();
        //更新 交集的区间
        $today = date('Y-m-d');
        $start = max($today, $beforeSupportData['employment_begin_date'], $afterSupportData['employment_begin_date']);
        $end   = min($beforeSupportData['employment_end_date'], $afterSupportData['employment_end_date']);

        if ($start > $end) {//没有交集说明 没生效 也没有子账号
            $beforeDateList = DateHelper::DateRange(strtotime($beforeSupportData['employment_begin_date']),
                                                    strtotime($beforeSupportData['employment_end_date']));
            $afterDateList  = DateHelper::DateRange(strtotime($afterSupportData['employment_begin_date']),
                                                    strtotime($afterSupportData['employment_end_date']));
            $shiftServer->shiftReBack($afterSupportData, $beforeDateList);//还原之前
            $shiftServer->shiftBackup($afterSupportData, $afterDateList);//备份修改后的
            return true;
        }

        $whereStaff[] = $beforeSupportData['staff_info_id'];
        if ($subFlag) {//看 带不带子账号
            $whereStaff[] = $beforeSupportData['sub_staff_info_id'];
        }

        //交集区间 修改班次
        if ($beforeSupportData['shift_id'] != $afterSupportData['shift_id']) {
            //如果 今天有打卡记录 就不改班次了
            if($start == $today){
                $attendanceInfo = StaffWorkAttendanceModel::find([
                    'conditions' => 'staff_info_id in ({ids:array}) and attendance_date = :date_at:',
                    'bind' => ['ids' => $whereStaff,'date_at' => $today]
                ])->toArray();
                if(!empty($attendanceInfo)){
                    $start = date('Y-m-d', strtotime('+1 day'));
                }
            }

            HrStaffShiftV2Model::find([
                'conditions' => 'staff_info_id in ({ids:array}) and shift_date between :start_date: and :end_date:',
                'bind' => ['ids' => $whereStaff,'start_date' => $start, 'end_date' => $end]
            ])->update([
                'shift_id'        => $afterSupportData['shift_id'],
                'shift_extend_id' => $afterSupportData['shift_extend_id'],
            ]);

            //加操作日志表 20YY-MM-DD->20YY-MM-DD期间：HH:mm(before) → HH:mm(after)
            $logStart = $start;
            if($afterSupportData['employment_begin_date'] > $today && $afterSupportData['employment_begin_date'] < $beforeSupportData['employment_begin_date']){
                $logStart = $afterSupportData['employment_begin_date'];
            }
            $logParam['staff_info_id'] = $beforeSupportData['staff_info_id'];
            $logParam['date_at'] = $logStart;
            $logParam['operate_id'] = $operate_id;
            $logType = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
            $extend['before'] = "{$logStart}~{$afterSupportData['employment_end_date']}：{$beforeSupportData['shift_start']}-{$beforeSupportData['shift_end']}";
            $extend['after'] = "{$afterSupportData['shift_start']}-{$afterSupportData['shift_end']}";
            $logServer = new WorkShiftService();
            $logServer->addShiftLog($logType, $logParam, $extend);

        }

        //结束时间 提前 需要恢复
        if ($afterSupportData['employment_end_date'] < $beforeSupportData['employment_end_date']) {
            $start = $afterSupportData['employment_end_date'];//3号
            $end = $beforeSupportData['employment_end_date'];//5号
            //需要 恢复 4，5
            $dateList = DateHelper::DateRange(strtotime("{$start} +1 day"),strtotime($end));
            //删除子账号 班次
            if($subFlag){
                HrStaffShiftV2Model::find([
                    'conditions' => 'shift_date in ({dates:array}) and staff_info_id = :sub_id:',
                    'bind'       => [
                        'sub_id'  => $beforeSupportData['sub_staff_info_id'],
                        'dates' => $dateList,
                    ],
                ])->delete();
            }
            $shiftServer->shiftReBack($afterSupportData,$dateList);
        }

        //结束时间 延后 需要备份新增
        if($afterSupportData['employment_end_date'] > $beforeSupportData['employment_end_date']){
            $start = $beforeSupportData['employment_end_date'];//5号
            $end = $afterSupportData['employment_end_date'];//7号
            //需要新增 6，7
            $dateList = DateHelper::DateRange(strtotime("{$start} +1 day"),strtotime($end));
            $shiftServer->shiftBackup($afterSupportData,$dateList);
        }

        //开始时间 延后  需要恢复
        if($afterSupportData['employment_begin_date'] > $beforeSupportData['employment_begin_date']){
            $start = $beforeSupportData['employment_begin_date'];//3号
            $end = $afterSupportData['employment_begin_date'];//5号
            //恢复3，4
            $dateList = DateHelper::DateRange(strtotime($start),strtotime("{$end} -1 day"));
            $shiftServer->shiftReBack($afterSupportData,$dateList);
        }

        //开始时间提前 需要备份新增
        if($afterSupportData['employment_begin_date'] < $beforeSupportData['employment_begin_date']){
            $start = $afterSupportData['employment_begin_date'];//1号
            $end = $beforeSupportData['employment_begin_date'];//3号
            //新增 1，2
            $dateList = DateHelper::DateRange(strtotime($start),strtotime("{$end} -1 day"));
            $shiftServer->shiftBackup($afterSupportData,$dateList);
        }
        return true;
    }

    /**
     * 创建支援账号的班次
     * @param $supportDetail
     * @param $isImport 是否是导入操作 是导入 需要新增父子账号班次 非导入（任务或by申请） 只需要操作子账号
     * @return bool
     */
    public function createSupportStaffShift($supportDetail, $isImport = false): bool
    {
        $sub_staff_info_id = $supportDetail['sub_staff_info_id'];
        $shift_start_date  = $supportDetail['shift_start_date'];
        $shift_end_date    = $supportDetail['shift_end_date'];
        $days = DateHelper::DateRange(strtotime($shift_start_date), strtotime($shift_end_date));
        //非导入 任务跑的 只操作子账号
        if($isImport === false && !empty($supportDetail['sub_staff_info_id'])){
            $subStaffShit = [];
            foreach ($days as $day) {
                $subStaffShit[]  = [
                    'staff_info_id'    => $sub_staff_info_id,
                    'shift_date'       => $day,
                    'shift_id'         => $supportDetail['shift_id'],
                    'shift_extend_id'  => $supportDetail['shift_extend_id'],
                    'is_operator_edit' => 2,
                ];
            }
            return (new HrStaffShiftV2Model())->batch_insert($subStaffShit, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        }

        $shiftServer = new StaffShiftService();
        return $shiftServer->shiftBackup($supportDetail, $days);

    }

    /**
     * 导入员工支援网点数据
     * @param $excel_data
     * @return array
     */
    public function ImportStaffSupport($params)
    {
        BaseService::setLanguage('en');

        $excel_data     = $params['excel_data'] ?? [];
        $return_data    = ['error_num' => 0, 'url' => '', 'success_num' => 0];
        $staff_info_ids = array_column($excel_data, 0);
        $staff_info_ids = array_map(function ($item) {
            return str_replace(' ', '', $item);
        }, $staff_info_ids);

        $staff_list     = $this->getStaffInfoByIds($staff_info_ids);
        $staff_position = [];
        if (!empty($staff_info_ids)) {
            $staff_position_list = HrStaffInfoPositionModel::find([
                'conditions' => "staff_info_id in ({staff_ids:array})",
                'bind'       => [
                    'staff_ids' => $staff_info_ids,
                ],
            ])->toArray();
            foreach ($staff_position_list as $key => $value) {
                $staff_position[$value['staff_info_id']][] = (int)$value['position_category'];
            }
        }

        $store_ids  = array_column($excel_data, 1);
        $store_list = (new SysStoreService())->getStoreList($store_ids);
        $store_list = !empty($store_list) ? array_column($store_list, null, 'id') : [];

        $shift_list = $this->getSupportShiftList();
        $shift_start_list = array_column($shift_list, null, 'check_shift_name');

        $error_list            = [];
        $db                    = $this->getDI()->get("db_backyard");
        $current_day           = date("Y-m-d");
        $sync_ms_params        = [];

        $job_title_config = $this->getSupportStaffJobTitleConfig();
        $support_job_title_list = $job_title_config['support_job_title_list'];//可支援职位列表
        $temp_job_title = array_flip($support_job_title_list);//可支援职位列表 反转匹配excel导入职位名称
        $support_job_title_role_config = $job_title_config['support_job_title_role_config'];//支援职位对应角色id

        $support_staff_apply_job_title_ids = $job_title_config['support_staff_apply_job_title_ids'];//可以导入员工职位ids
        $support_staff_job_title_config = $job_title_config['support_staff_job_title_config'];//员工职位对应可支援的职位
        $config_job_title_list = $job_title_config['job_title_list'];

        $staff_job_title_ids = array_column($staff_list, 'job_title');
        $staff_job_title_list = (new HrJobTitleService())->getJobTitleMapByIds($staff_job_title_ids);

        $t = BaseService::getTranslation('en');
        $staffSupportService = new StaffSupportService();
        $logServer = new WorkShiftService();
        //下个月最后一天

        $shift_end_date = date('Y-m-t', strtotime(date('Y-m-01',strtotime(date("Y-m-d"))) ." +1 month last day of"));//下月最后一天


        foreach ($excel_data as $key => $value) {
            //0工号,1支援网点id,2支援职位,3开始日期,4结束日期,5班次
            $error_message   = [];
            $value[3]        = date('Y-m-d', trim($value[3]));
            $value[4]        = date('Y-m-d', trim($value[4]));
            $staff_info_id   = str_replace(' ', '', $value[0]); //工号
            $store_id        = trim($value[1]); //网点id
            $excel_job_title = trim($value[2]); //职位
            $begin_date      = $value[3];       //支援开始日期
            $end_date        = $value[4];       //支援结束日期
            $excel_shift     = trim($value[5]); //支援班次
            $excel_is_stay   = trim($value[6]); //是否住宿

            $excel_is_separate       = trim($value[7]); //是否揽派分离
            $excel_is_original_store = trim($value[8]); //是否强制原网点打卡

            $job_title_id    = $temp_job_title[$excel_job_title] ?? 0;
            $staff_job_title = $staff_list[$staff_info_id]['job_title'] ?? 0;
            $staff_store_id  = $staff_list[$staff_info_id]['sys_store_id'] ?? '';

            if(empty($excel_job_title)) {
                $error_message[] = $t->_('staff_support_store_import_error_16');//'职位不能为空';
            }

            if (empty($staff_info_id)) {
                $error_message[] = $t->_('staff_support_store_import_error_11'); //'工号不能为空';
            } else {
                $staff_state = $staff_list[$staff_info_id]['state'] ?? 0;
                if ($staff_state != 1) {
                    $error_message[] = $t->_('staff_support_store_import_error_1'); //'工号非在职';
                }

                if ($staff_store_id != GlobalEnums::HEAD_OFFICE_ID) { //非总部员工
                    //验证导入员工职位是否可以去支援
                    if($staff_job_title == 0  || !in_array($staff_job_title, $support_staff_apply_job_title_ids)) {
                        $job_name_list = array_map(function ($v) use ($config_job_title_list) {
                            return $config_job_title_list[$v];
                        }, $support_staff_apply_job_title_ids);
                        $job_title_str = implode(',', $job_name_list);
                        $error_message[] = $t->_('staff_support_store_import_error_29', ['job_title' => $job_title_str]); //只能导入 %职位% 的员工去支援
                    } else {
                        //验证导入职位是否在员工职位对应可支援的职位
                        if(!in_array($job_title_id, $support_staff_job_title_config[$staff_job_title])) {
                            $staff_job_title_name = $staff_job_title_list[$staff_job_title] ?? '';
                            $staff_support_job_title_name_arr = array_map(function ($v) use($config_job_title_list) {
                                return $config_job_title_list[$v] ?? '';
                            }, $support_staff_job_title_config[$staff_job_title]);
                            $staff_support_job_title_name_str = implode(',', $staff_support_job_title_name_arr);
                            $error_message[] = $t->_('staff_support_store_import_error_28', ['job_title' => $staff_job_title_name, 'support_job_title' => $staff_support_job_title_name_str]);
                        }
                    }
                } else {
                    if ($job_title_id != enums::$support_job_title['bike_courier']) {
                        $error_message[] = $t->_('staff_support_store_import_error_24');//总部员工只能支援bike_courier职位
                    }
                }
            }

            $store_name = $store_list[$store_id]['name'] ?? '';
            if (empty($store_name)) {
                $error_message[] = $t->_('staff_support_store_import_error_12');//'网点不能为空';
            } else {
                if ($staff_store_id == $store_id) {
                    $error_message[] = $t->_('staff_support_store_import_error_10');//'网点错误';
                }
            }

            if (!$begin_date || !$end_date || $begin_date >= $shift_end_date || $end_date >= $shift_end_date) {
                $error_message[] = $t->_('staff_support_store_import_error_8');//开始日期/结束日期格式错误
            } else {
                if ($begin_date < $current_day) {
                    $error_message[] = $t->_('staff_support_store_import_error_9');//开始日期/结束日期格式错误
                }

                if ($begin_date > $end_date) {
                    $error_message[] = $t->_('staff_support_store_import_error_7');//支援开始日期不能大于结束日期
                }
            }

            //验证入职日期 和 支援开始日期 是否符合配置
            $hireCheck = $this->checkHireSetting($staff_list[$staff_info_id], $begin_date);
            if($hireCheck !== true){
                $error_message[] = $hireCheck;
            }

            if (empty($excel_shift)) {
                $error_message[] = $t->_('staff_support_store_import_error_14');//'班次不能为空';
            } else {
                if (!in_array($excel_shift, array_keys($shift_start_list))) {
                    $error_message[] = $t->_('staff_support_store_import_error_4');//'班次数据有误';
                }
                $shiftInfo = $shift_start_list[$excel_shift];
                //验证支援开始班次的班次时长是否与原班次的时长一致
                $check_shift_duration = $this->checkStaffShiftDuration(intval($staff_info_id), $begin_date,
                    $shiftInfo['shift_duration']);
                if (!$check_shift_duration) {
                    $error_message[] = $t->_('staff_support_store_shift_duration_error');//'支援班次和现有班次的工作时长不一致';
                }
            }

            $is_stay = 0;
            if (empty($excel_is_stay)) {
                $error_message[] = $t->_('staff_support_store_import_error_21');//'请选择是否在网点住宿';
            } else {
                if (!in_array($excel_is_stay, ['Yes', 'No'])) {
                    $error_message[] = $t->_('staff_support_store_import_error_20');//'是否住宿只能选择 Yes 或者 No';
                } else {
                    $is_stay = $excel_is_stay == 'Yes' ? 1 : 0;
                    if ($begin_date == $end_date && $is_stay == 1) {
                        $error_message[] = $t->_('staff_support_store_import_error_19');//'支援天数为1天时不支持在网点住宿';
                    }
                }
            }
            //新增字段 揽派分离 和 强制原网店打卡 默认否
            $is_separate = $is_original_store = 0;
            if(!empty($excel_is_separate)){
                if (!in_array($excel_is_separate, ['Yes', 'No'])) {
                    $error_message[] = $t->_('staff_support_is_separate_error');//'只能选择 Yes 或者 No';
                }else{
                    $is_separate = $excel_is_separate == 'Yes' ? 1 : 0;
                }
            }
            if(!empty($excel_is_original_store)){
                if (!in_array($excel_is_original_store, ['Yes', 'No'])) {
                    $error_message[] = $t->_('staff_support_is_original_store_error');//'只能选择 Yes 或者 No';
                }else{
                    $is_original_store = $excel_is_original_store == 'Yes' ? 1 : 0;
                }
            }
            //三个 是否字段 只能有一个是
            if(($is_stay + $is_separate + $is_original_store) > 1){
                $error_message[] = $t->_('staff_support_only_one_yes');//只能选一个是
            }

            $support_count = $this->validateConflict($begin_date, $end_date, $staff_info_id);
            if ($support_count > 0) {
                $error_message[] = $t->_('staff_support_store_import_error_6');//该工号已经有支援数据
            }

            $sub_staff_info_id = 0;
            $staff_info        = $staff_list[$staff_info_id];
            if (empty($error_message) && $begin_date == $current_day) {
                $staff_info['position_category'] = $staff_position[$staff_info_id] ?? [];
                $staff_info['manager']           = $staff_list[$staff_info_id]['manger'] ?? '';

                //如果是揽派分离 不生成子账号 $is_separate 是 0 才生成
                if(empty($is_separate)){
                    $sub_staff_info_id               = $this->createSubStaff($staff_info,
                        [
                            'job_title'             => $job_title_id,
                            'store_id'              => $store_id,
                            'employment_begin_date' => $begin_date,
                            'position_category'     => $support_job_title_role_config[$job_title_id] ?? []
                        ]);
                    if (empty($sub_staff_info_id)) {
                        $error_message[] = $t->_('staff_support_store_import_error_15');//子账号生成失败
                    }
                }
                //主账号和子账号发送合同消息 子账号是空 不发子账号
                $staffContractService = new StaffContractService();
                $staffContractService->sendContractMsgToSubStaff($staff_info_id,$sub_staff_info_id);

                //给子账号 发送简历完善通知 消息
                $sendResult = (new StaffNoticeService())->sendResumeMessage($staff_info_id, $sub_staff_info_id, $begin_date);
                $this->logger->info([
                    'function' => 'StaffSupportStoreServer-ImportStaffSupport-sendResumeMessage',
                    'params' => [$staff_info_id, $sub_staff_info_id, $begin_date],
                    'result' => $sendResult
                ]);
            }

            if (empty($error_message)) {
                // 计算两个网点经纬度之间的距离
                $apart = (new SysStoreService())->calculateDistanceStore($store_id,$staff_info['sys_store_id']);
                $insertData = [
                    'serial_no'             => $staffSupportService->getSerialNo('SASS'),
                    'staff_info_id'         => $staff_info_id,
                    'job_title_id'          => $job_title_id,
                    'store_id'              => $store_id,
                    'store_name'            => $store_name,
                    'employment_begin_date' => $begin_date,
                    'employment_end_date'   => $end_date,
                    'employment_days'       => (strtotime($end_date) - strtotime($begin_date)) / 86400 + 1,
                    'shift_id'              => $shiftInfo['id'] ?? 0,
                    'shift_extend_id'       => $shiftInfo['shift_extend_id'] ?? 0,
                    'shift_start'           => $shiftInfo['start'] ?? '',
                    'shift_end'             => $shiftInfo['end'] ?? '',
                    'status'                => 2,
                    'sub_staff_info_id'     => $sub_staff_info_id,
                    'staff_store_id'        => $staff_info['sys_store_id'] ?? '', //申请人所属网点id
                    'support_status'        => $begin_date == $current_day ? self::$support_status['in_force'] : self::$support_status['pending'],
                    'is_stay'               => $is_stay,
                    'is_separate'           => $is_separate,
                    'is_original_store'     => $is_original_store,
                    'actual_begin_date'     => $begin_date == $current_day ? $begin_date : null,
                    'data_source'           => HrStaffApplySupportStoreModel::DATA_SOURCE_2,
                    'apart'                 => $apart ?? null,
                    'create_staff_id'       => $params['operator_id'],
                ];
//                //撤销OT
//                $this->cancelOT($staff_info_id,$begin_date,$end_date,['shift_start' => $insertData['shift_start'], 'shift_end' => $insertData['shift_end']]);


                $res     = $db->insertAsDict("hr_staff_apply_support_store", $insertData);
                $last_id = $db->lastInsertId();
                if ($res) {
                    //立即生效的 要加拆分表数据
                    if($begin_date == $current_day){
                        //员工支援日期拆分表数据
                        $insertData['id'] = $last_id;
                        $this->addSupportSplit($insertData);
                    }
                    //加班次变更操作日志
                    $logParam['staff_info_id'] = $staff_info_id;
                    $logParam['date_at']       = $begin_date;
                    $logParam['operate_id']    = $params['operator_id'];
                    $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
                    $extend['before']          = '';
                    $extend['after']           = "{$insertData['shift_start']}-{$insertData['shift_end']} ({$begin_date}~{$end_date})";
                    $logServer->addShiftLog($logType, $logParam, $extend);

                    if ($begin_date == $current_day && $sub_staff_info_id) {
                        $sync_ms_params = [
                            [
                                'id'           => $last_id,
                                'staff_id'     => $sub_staff_info_id,
                                'master_staff' => $staff_info_id,
                                'begin_at'     => strtotime($begin_date),
                                'end_at'       => strtotime($end_date) + 86399,
                            ],
                        ];
                        //马来 揽派分离类型支援 没有子账号 不需要同步
                        $this->syncMsSupportApply($sync_ms_params, 'addStaffSupportInfo');
                    }
                    //生成班次信息 不管是不是今天开始 都需要生成主账号班次信息 如果是今天需要生成双账号的
                    $createShiftInfo = [
                        'staff_info_id'     => $staff_info_id,
                        'sub_staff_info_id' => $sub_staff_info_id,
                        'shift_start_date'  => $begin_date,
                        'shift_end_date'    => $end_date,
                        'shift_id'          => $insertData['shift_id'],
                        'shift_extend_id'   => $insertData['shift_extend_id'],
                    ];
                    $re              = $this->createSupportStaffShift($createShiftInfo, true);


                    $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
                    //$title = '支援网点通知';
                    //$content = '支援任务：支援网点 %store_name%，支援日期为%employment_date%，支援班次为%staff_shift%，支援期间请在支援网点打卡上班。';
                    $manager_mobile         = $this->getStoreManagerMobile($store_id);
                    $by_message_title       = BaseService::getTranslation($lang)->_('staff_support_store_import_message_title_v2');
                    $by_message_content_key = 'staff_support_store_import_message_default';
                    if ($staff_info['job_title'] == enums::$support_job_title['branch_supervisor'] && $job_title_id == enums::$support_job_title['van_courier']) {
                        $by_message_content_key = 'staff_support_store_import_message_bs';
                    }
                    $replace = '';
                    //如果都没有 就是空
                    if($is_separate){
                        $replace = BaseService::getTranslation($lang)->_('staff_support_store_is_separate_p2');
                    }
                    if($is_original_store){
                        $replace = BaseService::getTranslation($lang)->_('staff_support_store_import_is_original_store_p2');
                    }
                    $by_message_content = BaseService::getTranslation($lang)->_($by_message_content_key,
                        [
                            //'store_name' => $store_name,
                            //'employment_date' => $begin_date . '-' . $end_date,
                            //'staff_shift' => $excel_shift
                            'support_store'            => $store_name,
                            'employment_begin_date'    => $begin_date,
                            'employment_end_date'      => $end_date,
                            'shift'                    => $excel_shift,
                            'district_manager_name'    => $manager_mobile['district_manager_name'],
                            'district_manager_mobile'  => $manager_mobile['district_manager_mobile'],
                            'branch_supervisor_name'   => $manager_mobile['branch_supervisor_name'],
                            'branch_supervisor_mobile' => $manager_mobile['branch_supervisor_mobile'],
                            'serial_no'                => $insertData['serial_no'],
                            'replace_str'              => $replace,
                        ]);
                    $by_message_params  = [
                        'staff_info_id'   => $staff_info_id,
                        'message_title'   => $by_message_title,
                        'message_content' => $by_message_content,
                        'source'          => 'import_staff_support',
                    ];

                    $message_result = $this->sendStaffBackyardMessageV2($by_message_params);
                    if ($message_result) {
                        //发送push
                        $message_title   = BaseService::getTranslation($lang)->_('staff_support_store_import_message_title');
                        //消息内容分成两部分 根据 住宿 揽派分离 和 强制原网店打卡 拆分第二部分
                        $msgPart1 = BaseService::getTranslation($lang)->_('staff_support_store_import_message_content_p1',
                            [
                                'store_name'      => $store_name,
                                'employment_date' => $begin_date.'-'.$end_date,
                                'staff_shift'     => $excel_shift,
                                'serial_no'       => $insertData['serial_no'],
                            ]);
                        $msgPart2 = BaseService::getTranslation($lang)->_('staff_support_store_default_p2');
                        if($is_separate){
                            $msgPart2 = BaseService::getTranslation($lang)->_('staff_support_store_is_separate_p2');
                        }
                        if($is_original_store){
                            $msgPart2 = BaseService::getTranslation($lang)->_('staff_support_store_import_is_original_store_p2');
                        }

                        $message_params  = [
                            'staff_info_id'   => $staff_info_id,
                            'message_title'   => $message_title,
                            'message_content' => $msgPart1.$msgPart2,
                            'source'          => 'import_staff_support',
                        ];
                        $this->sendStaffBackyardPush($message_params);
                    }
                    //写队列 修改ot 时间
                    $otParam['id']          = $last_id;
                    $otParam['lang']        = $lang;
                    $otParam['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_APPLY;
                    $rmq                    = new RocketMQ('ot-support-shift');
                    $rmq->sendToMsg($otParam);
                } else {
                    $error_message[] = $t->_('staff_support_store_import_error_5');
                    $value[]         = implode(',', $error_message);
                    array_push($error_list, $value);
                }
            } else {
                $value[] = implode(',', $error_message);
                array_push($error_list, $value);
            }
        }

        $return_data['success_num'] = count($excel_data);
        $return_data['error_num']   = 0;
        $return_data['url']         = '';
        if (!empty($error_list)) {
            $fileName      = 'ImportSupportError_'.date('Ymdhis').'_'.self::$language.date('Ymd-His').'.xlsx';
            $excel_file    = $this->exportExcel([
                'Staff_id',
                'Give Support Branch Number',
                'Give Support Position',
                'Start Date',
                'End Date',
                'Support Shift',
                'Whether stay overnight at the support branch',
                'Separation of pickup and delivery',
                'Mandatory punch in at the original branch',
                'Result',
            ], $error_list, $fileName);
            $flashOss      = new FlashOss();
            $json_url_name = self::STAFF_SUPPORT_STORE_OSS_PATH.'/'.$fileName;
            $flashOss->uploadFile($json_url_name, $excel_file['data']);
            $url = $flashOss->signUrl($json_url_name, 86400 * 7);

            $return_data['error_num']   = count($error_list);
            $return_data['success_num'] = $return_data['success_num'] - $return_data['error_num'];
            $return_data['url']         = $url;
        }

        //更新
        $db->updateAsDict(
            'hr_import_staff_support_excel',
            [
                'result_file_path'     => $return_data['url'],
                'import_success_count' => $return_data['success_num'],
                'import_error_count'   => $return_data['error_num'],
                'status'               => 2,
            ],
            'id = '.$params['import_excel_id']
        );

        return $return_data;
    }

    /**
     * 创建子账号
     * @param $staff_info
     * @param $apply_info
     * @return int
     */
    public function createSubStaff($staff_info, $apply_info)
    {
        $sub_staff_info_id            = 0;
        $net_work_operations_malaysia = 320;
        try {
            //car courier/bike courier/branch supervisor/van courier
            //0分配员/1快递员/2仓管员/4网点出纳/18网点主管
            $position_category = [];
            switch ($apply_info['job_title']) {
                case self::$support_job_title['bike_courier']:
                case self::$support_job_title['van_courier']:
                case self::$support_job_title['car_courier']:
                    $position_category = [1];
                    break;
                case self::$support_job_title['branch_supervisor']:
                    $position_category = [0, 1, 2, 4, 18];
                    break;
                case self::$support_job_title['dc_officer']:
                    $position_category = [2];
                    break;
            }
            $sys_department_id  = $staff_info['sys_department_id'];
            $node_department_id = $staff_info['node_department_id'];
            if ($staff_info['sys_store_id'] == GlobalEnums::HEAD_OFFICE_ID) {
                //总部员工指定部门Network Operations (Malaysia)
                $node_department_id = $net_work_operations_malaysia;
                $sys_department_id  = (new SysDepartmentService())->SearchSysDeptId($node_department_id);
            }

            $sub_staff_arr = [
                'name'                  => $staff_info['name'],
                'sex'                   => $staff_info['sex'],
                'week_working_day'      => $staff_info['week_working_day'],
                'working_day_rest_type' => $staff_info['week_working_day'] . $staff_info['rest_type'],
                'identity'              => $staff_info['identity'],
                'mobile'                => $staff_info['mobile'],
                'personal_email'        => $staff_info['personal_email'],
                'hire_type'             => $staff_info['hire_type'],
                'job_title'             => (string)$apply_info['job_title'],//职位id
                'sys_store_id'          => $apply_info['store_id'],         //网点id
                'sys_department_id'     => (string)$sys_department_id,      //部门id
                'node_department_id'    => $node_department_id,
                'formal'                => 1,
                'state'                 => 1,
                'is_sub_staff'          => 1,
                'hire_date'             => date('Y-m-d', strtotime($apply_info['employment_begin_date'])),
                'master_staff'          => $staff_info['staff_info_id'],
                'leave_date'            => null,
                'position_category'     => !empty($apply_info['position_category']) ? $apply_info['position_category'] : $position_category,
                'manager'               => $staff_info['manager'],
            ];

            $hr_rpc = (new ApiClient('hris', '', 'create_sub_staff_info', 'zh-CN'));
            $hr_rpc->setParamss($sub_staff_arr);
            $res = $hr_rpc->execute();

            if (isset($res['code']) && $res['code'] == 1) {
                $sub_staff_info_id = $res['data']['staff_info_id'] ?? 0;
                $this->logger->info([
                    'function'     => 'createSubStaff',
                    'staff_params' => $staff_info,
                    'apply_params' => $apply_info,
                    'result'       => $res,
                ]);
            } else {
                $this->logger->error([
                    'function'     => 'createSubStaff',
                    'staff_params' => $staff_info,
                    'apply_params' => $apply_info,
                    'result'       => $res,
                ]);
            }
        } catch (Exception $e) {
            $this->logger->error([
                'function'     => 'createSubStaff',
                'staff_params' => $staff_info,
                'apply_params' => $apply_info,
                'message'      => $e->getMessage(),
                'line'         => $e->getLine(),
                'file'         => $e->getFile(),
            ]);
        }
        return $sub_staff_info_id;
    }

    /**
     * my 差异化班次
     * @return array
     */
    public function getShiftListByChange (): array
    {
        return (new StaffSupportStoreServer())->getSupportShiftList();
    }

    protected function getImportTemplate()
    {
        return SettingEnvModel::get_val('import_staff_support_template_my');
    }


    /**
     * MY
     * 验证支援班次与原本次时长是否一致
     * @param $staff_id
     * @param $support_begin_date
     * @param $shift_duration
     * @return bool
     */
    protected function checkStaffShiftDuration($staff_id, $support_begin_date, $shift_duration): bool
    {

        $staffShift = (new StaffShiftService())->getDailyShiftInfo([$staff_id], $support_begin_date,
            $support_begin_date);
        $staffShift = $staffShift[$staff_id . '-' . $support_begin_date] ?? [];
        if (empty($staffShift)) {
            return false;
        }
        return $staffShift['work_time'] == $shift_duration;
    }


    /**
     * 支援取消OT
     * @param $staff_info_id
     * @param $start_date
     * @param $end_date
     * @param array $support_shift_info
     * @return bool
     */
    public function cancelOT($staff_info_id, $start_date, $end_date, array $support_shift_info=[]): bool
    {
        if (!isCountry('MY')) {
            return false;
        }
        $diff_shift_date = [];
        if (!empty($support_shift_info)) {
            $staffShift = (new StaffShiftService())->getDailyShiftInfo([$staff_info_id], $start_date,
                $end_date);
            foreach ($staffShift as $item) {
                if ($item['first_start'] != $support_shift_info['shift_start'] || $item['first_end'] != $support_shift_info['shift_end']) {
                    $diff_shift_date[] = $item['shift_date'];
                }
            }
        }


        $bind           = [
            'staff_id'   => $staff_info_id,
            'state'      => [ApprovalEnums::APPROVAL_STATUS_APPROVAL, ApprovalEnums::APPROVAL_STATUS_PENDING],
            'start_date' => $start_date,
            'end_date'   => $end_date,
        ];
        $otModel        = HrOvertimeModel::find([
            'conditions' => 'staff_id = :staff_id: and state in ({state:array}) and date_at >= :start_date: and date_at <= :end_date: ',
            'bind'       => $bind,
        ]);

        $reason         = 'system tool cancel duo to support';
        $attendanceTool = new AttendanceToolService();
        $normal_ot_date = $shift_change_ot_date = [];
        $ot_ids = [];
        foreach ($otModel as $item) {
            //工作日加班
            if ($item->type == 1) {
                $normal_ot_date[] = $item->date_at;
                $ot_ids[] = $item->overtime_id;
            }
            if (in_array($item->date_at, $diff_shift_date)) {
                $shift_change_ot_date[] = $item->date_at;
                $ot_ids[] = $item->overtime_id;
            }

            if (!in_array($item->date_at, array_merge($normal_ot_date, $shift_change_ot_date))) {
                continue;
            }

            //撤销
            $cancelParams = [
                'audit_id'      => $item->overtime_id,
                'staff_id'      => $item->staff_id,
                'cancel_reason' => $reason,
            ];
            $res          = $attendanceTool->getApiDatass('by', '', 'cancelOvertime', 'en', $cancelParams);
            if (!isset($res['code']) || $res['code'] != 1) {
                $this->logger->error([
                    'supportCancelNormalDayOTError' => $reason,
                    'staff_id'                      => $staff_info_id,
                    'ot_id'                         => $item->overtime_id,
                ]);
                $item->state         = ApprovalEnums::APPROVAL_STATUS_CANCEL;
                $item->reject_reason = $reason;
                $item->save();
            }
        }

        if (!empty($ot_ids)) {
            AuditApplyModel::find([
                'conditions' => 'submitter_id = :submitter_id: and biz_type = :biz_type: and biz_value in ({biz_value:array}) and state != :state:',
                'bind'       => [
                    'submitter_id' => $staff_info_id,
                    'biz_type'     => ApprovalEnums::APPROVAL_TYPE_OVERTIME,
                    'biz_value'    => $ot_ids,
                    'state'        => ApprovalEnums::APPROVAL_STATUS_CANCEL,
                ],
            ])->update([
                'cancel_reason' => $reason,
                'state'         => ApprovalEnums::APPROVAL_STATUS_CANCEL,
            ]);
        }
        //工作日OT
        if (!empty($normal_ot_date)) {
            $this->sendCancelOtMessage($staff_info_id, $normal_ot_date, 'support_cancel_ot_title',
                'support_cancel_normal_day_ot_content');
        }
        //班次变更OT
        if (!empty($shift_change_ot_date)) {
            $this->sendCancelOtMessage($staff_info_id, $shift_change_ot_date, 'support_cancel_ot_title',
                'support_cancel_ot_content');
        }
        return true;
    }
    /**
     * my
     * 取消OT 发送消息
     * @param $staff_info_id
     * @param $date_list
     * @param $title_key
     * @param $content_key
     * @return void
     */
    protected function sendCancelOtMessage($staff_info_id, $date_list, $title_key, $content_key)
    {
        $staffLang = (new StaffService())->getAcceptLanguage($staff_info_id);
        $t         = BaseService::getTranslation($staffLang);
        $id        = time() . $staff_info_id . rand(1000000, 9999999);

        $kit_param['staff_info_ids_str'] = $staff_info_id;
        $kit_param['staff_users']        = [$staff_info_id];
        $kit_param['message_title']      = $t->_($title_key);
        $kit_param['message_content']    = $t->_($content_key,
            ['date_at' => implode(',', $date_list)]);
        $kit_param['id']                 = $id;
        $kit_param['category']           = MessageModel::MESSAGE_CATEGORY_SHIFT;
        (new MessagesService())->add_kit_message($kit_param);
    }


}
