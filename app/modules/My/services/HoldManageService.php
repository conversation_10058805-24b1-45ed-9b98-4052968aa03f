<?php

namespace App\Modules\My\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\FlashOss;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Services\DepartmentService;
use App\Services\HoldManageService as BaseHoldManageService;
use App\Services\HrJobTitleService;
use App\Services\SysManagePieceService;
use App\Services\SysManageRegionService;
use App\Services\SysStoreService;
use OSS\Core\OssException;
use Phalcon\Mvc\Model\Query\BuilderInterface;

class HoldManageService extends BaseHoldManageService
{
    /**
     * 区域列表
     * @var array
     */
    public static $manage_areas = [
        1 => 'N',
        2 => 'C',
        3 => 'S',
        4 => 'EC',
        5 => 'EM',
    ];

    public function getManageAreasDesc(): array
    {
        $manage_areas_desc = [];
        foreach (self::$manage_areas as $k => $v) {
            $manage_areas_desc[] = [
                'key'   => $k,
                'value' => $v,
            ];
        }
        return $manage_areas_desc;
    }


    /**
     * hold 筛选条件
     * @return array
     */
    public function getScreeningList()
    {
        $hold_reason       = $this->getHoldReason();
        $handle_people     = $this->getHandlePeople();
        $handle_opinion    = $this->getHandleOpinion();
        $tab_list          = $this->getTabList();
        $remark_list       = $this->getRemarkList();
        $remark_source     = $this->getHoldSource();
        $handle_progress   = $this->getHandle_progress();
        $manage_areas_desc = $this->getManageAreasDesc();
        $hire_type_list    = (new SysService())->getHireTypeList();

        return [
            'handle_people'   => $handle_people,     //处理人列表
            'area_list'       => $manage_areas_desc, //区域列表
            'hold_reason'     => $hold_reason,       //hold原因
            'handle_opinion'  => $handle_opinion,
            'tab_list'        => $tab_list,
            'remark_list'     => $remark_list,
            'remark_source'   => $remark_source,
            'handle_progress' => $handle_progress,
            'hire_type_list'  => $hire_type_list,
        ];
    }

    /**
     * hold list
     * @param $params
     * @return array
     */
    public function getHoldList($params)
    {
        $page_num      = intval($params['page_num'] ?? 1);
        $page_size     = intval($params['page_size'] ?? 20);
        $offset        = $page_size * ($page_num - 1);
        $builder       = $this->getHoldListBuilder($params);
        $builder_count = $this->getHoldListBuilder($params);
        $builder->limit($page_size, $offset);
        $list = $builder->orderBy('hm.handle_progress ASC')->getQuery()->execute()->toArray();

        $items = !empty($list) ? $this->combinationHoldList($list) : [];

        //获取total count
        $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();

        return [
            'items'    => $items,
            'paginate' => [
                'total_count' => !empty($totalCount) ? $totalCount->count : 0,
                'page_num'    => $page_num,
                'page_size'   => $page_size,
            ],
        ];
    }

    /**
     * hold list builder
     * @param $params
     * @return BuilderInterface
     */
    public function getHoldListBuilder($params)
    {
        $type                   = $params['type'] ?? 1;                    //hold类型
        $staff_info_id          = $params['staff_info_id'] ?? '';          //工号
        $department_id          = $params['department_id'] ?? '';          //部门id
        $store_id               = $params['store_id'] ?? '';               //网点id
        $handle_people          = $params['handle_people'] ?? '';          //处理人列表
        $handle_progress        = $params['handle_progress'] ?? '';        //处理状态
        $hold_source            = $params['hold_source'] ?? '';            //hold来源
        $hold_reason            = $params['hold_reason'] ?? '';            //hold原因
        $last_update_date_begin = $params['last_update_date_begin'] ?? ''; //最后更新开始时间
        $last_update_date_end   = $params['last_update_date_end'] ?? '';   //最后更新结束时间
        $hold_date_begin        = $params['hold_date_begin'] ?? '';        //hold 开始时间
        $hold_date_end          = $params['hold_date_end'] ?? '';          //hold 结束时间
        $area_id                = $params['area_id'] ?? '';                //所属区域
        $region_id              = $params['region_id'] ?? '';              //大区id
        $piece_id               = $params['piece_id'] ?? '';               //片区id
        $hire_type              = $params['hire_type'] ?? [];              //雇佣类型

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
                            hm.id,
                            hm.hold_id,
                            hm.staff_info_id,
                            hm.hold_reason,
                            hm.hold_source,
                            hm.hold_time,
                            hm.handle_people,
                            hm.staff_submit_id,
                            hm.handle_progress,
                            hm.approval_time,
                            hm.approval_status,
                            hm.handle_result,
                            hm.hold_remark,
                            hm.hold_attachments,
                            hm.type,
                            hm.month_begin,
                            hm.month_end,
                            hm.release_state,
                            hm.release_time,
                            hm.created_at,
                            convert_tz(hm.updated_at, \'+00:00\', \''. $this->timeZone . '\') as update_at,
                            hm.last_hold_time,
                            hm.release_submit_id,
                            hs.name as staff_info_name,
                            hs.sys_store_id,
                            hs.state as staff_state,
                            hs.wait_leave_state,
                            hs.leave_date,
                            hs.sys_department_id,
                            hs.node_department_id,
                            hs.job_title,
                            hs.hire_type,
                            store.manage_region,
                            store.manage_piece,
                            hm.version
                            ');
        $builder->from(['hm' => HoldStaffManageModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hm.staff_info_id = hs.staff_info_id', 'hs');
        $builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
        $builder->where("type = :type:", ['type' => $type]);
        $builder->andWhere("hm.is_delete = 0");
        if (!empty($staff_info_id)) {
            $builder->andWhere("hm.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_info_id]);
        }

        if (!empty($department_id)) {
            $builder->andWhere("hs.node_department_id = :node_department_id:",
                ['node_department_id' => $department_id]);
        }

        if (!empty($store_id)) {
            $builder->andWhere("hs.sys_store_id = :store_id:", ['store_id' => $store_id]);
        }

        if (!empty($hire_type)) {
            $builder->inWhere("hs.hire_type", $hire_type);
        }

        if (!empty($handle_people)) {
            $builder->andWhere("hm.handle_people = :handle_people:", ['handle_people' => $handle_people]);
        }

        if (!empty($handle_progress)) {
            $builder->andWhere("hm.handle_progress = :handle_progress:", ['handle_progress' => $handle_progress]);
        }

        if (!empty($hold_reason)) {
            $builder->andWhere("hm.hold_reason = :hold_reason:", ['hold_reason' => $hold_reason]);
        }

        if (!empty($hold_source)) {
            $builder->andWhere("hm.hold_source = :hold_source:", ['hold_source' => $hold_source]);
        }

        if (!empty($last_update_date_begin)) {
            $builder->andWhere("hm.updated_at >= :last_update_date_begin:",
                ['last_update_date_begin' => gmdate('Y-m-d H:i:s', strtotime($last_update_date_begin . ' 00:00:00'))]);
        }

        if (!empty($last_update_date_end)) {
            $builder->andWhere("hm.updated_at <= :last_update_date_end:",
                ['last_update_date_end' => gmdate('Y-m-d H:i:s', strtotime($last_update_date_end . ' 23:59:59'))]);
        }

        if (!empty($hold_date_begin)) {
            $builder->andWhere("hm.hold_time >= :hold_date_begin:",
                ['hold_date_begin' => $hold_date_begin . ' 00:00:00']);
        }

        if (!empty($hold_date_end)) {
            $builder->andWhere("hm.hold_time <= :hold_date_end:", ['hold_date_end' => $hold_date_end . ' 23:59:59']);
        }

        if (!empty($area_id)) {
            $sorting_no = self::$manage_areas[$area_id] ?? '';
            if (!empty($sorting_no)) {
                //$builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
                $builder->andWhere("store.sorting_no = :sorting_no:", ['sorting_no' => $sorting_no]);
            }
        }

        if (!empty($region_id)) {
            $builder->andWhere("store.manage_region = :manage_region:", ['manage_region' => $region_id]);
        }

        if (!empty($piece_id)) {
            $builder->andWhere("store.manage_piece = :manage_piece:", ['manage_piece' => $piece_id]);
        }
        if (!empty($params['max_id'])) {
            $builder->andWhere('hm.id > :max_id:', ['max_id' => $params['max_id']]);
        }
        return $builder;
    }

    /**
     * 组合hold 数据
     * @param $list
     * @return mixed
     */
    public function combinationHoldList($list)
    {
        $store_ids        = [];
        $staff_submit_ids = [];
        foreach ($list as $key => $value) {
            $store_ids[]        = $value['sys_store_id'];
            $staff_submit_ids[] = $value['staff_submit_id'];
        }
        $hire_type_list = array_column((new \App\Services\SysService())->getHireTypeList(), 'label', 'value');
        $sys_store_list = (new SysStoreService())->getStoreList($store_ids);
        $sys_store_list = array_column($sys_store_list, null, 'id');

        $submit_staff_list = $this->getStaffList($staff_submit_ids);
        $submit_staff_list = array_column($submit_staff_list, null, 'staff_info_id');

        foreach ($list as $key => &$value) {
            $value['staff_name']           = $value['staff_info_name'] . ' ' . $value['staff_info_id'];
            $value['hold_source_name']     = self::$t->_(self::$hold_source[$value['hold_source']]);
            $value['handle_progress_name'] = self::$t->_(self::$handle_progress[$value['handle_progress']]);
            $value['hold_id']              = $value['hold_id'] . '000' . $value['id'];
            $value['handle_people']        = self::$t->_($value['handle_people']);
            $handle_result                 = json_decode($value['handle_result'], true);
            $value['handle_result']        = $handle_result;
            $value['hold_attachments']     = json_decode($value['hold_attachments'], true);
            $value['area_name']            = '';
            if ($value['sys_store_id'] == '-1') {
                $value['store_name'] = GlobalEnums::HEAD_OFFICE;
            } else {
                $value['store_name'] = $sys_store_list[$value['sys_store_id']]['name'] ?? '';
                $value['area_name']  = $sys_store_list[$value['sys_store_id']]['sorting_no'] ?? '';
            }
            if ($value['staff_submit_id'] == 10000) {
                $value['staff_submit_name'] = '超管10000';
            } else {
                $submit_name                = $submit_staff_list[$value['staff_submit_id']]['name'] ?? '';
                $value['staff_submit_name'] = !empty($submit_name) ? ($submit_name . ' ' . $value['staff_submit_id']) : '';
            }
            $value['hold_month'] = ($value['month_begin'] != '0000-00-00' && $value['month_end'] != '0000-00-00' && $value['month_begin'] != "1970-01-01" && $value['month_end'] != "1970-01-01") ? date("Y-m",
                    strtotime($value['month_begin'])) . " - " . date("Y-m", strtotime($value['month_end'])) : "";

            $value['department_name'] = $this->showDepartmentName($value['node_department_id']);
            $value['job_title_name']  = $this->showJobTitleName($value['job_title']);

            $handle_result_str = [];
            if (!empty($handle_result)) {
                foreach ($handle_result as $k => $v) {
                    $handle_result_str[] = self::$t->_($v['key']) . $v['value'];
                }
            }
            $value['handle_result_str']  = !empty($handle_result_str) ? implode(',', $handle_result_str) : '';
            $value['manage_region_name'] = $this->showRegionName($value['manage_region']);
            $value['manage_piece_name']  = $this->showPieceName($value['manage_piece']);
            $value['hire_type_text']     = $hire_type_list[$value['hire_type']] ?? '';
        }
        return $list;
    }


    /**
     * hold 下载
     * @param $params
     * @return array
     * @throws OssException
     */
    public function exportHoldList($params):array
    {
        $limit = 500;
        if (get_runtime() == 'dev') {
            $limit = 50;
        }
        $export_row = [];
        while (true) {
            $builder    = $this->getHoldListBuilder($params);
            $builder->orderBy('hm.id ASC')->limit($limit);
            $list       = $builder->getQuery()->execute()->toArray();
            //使用游标或者逐条处理的方式，避免将大量数据一次性加载到内存中。例如可以使用 Phalcon 的 Resultset 并通过迭代器处理 



            if (empty($list)) {
                break;
            }
            $list       = $this->combinationHoldList($list);

            foreach ($list as $key => $value) {
                //处理最大值
                $params['max_id']  = $value['id'];
                $staff_state_text  = ($value['staff_state'] == 1 && $value['wait_leave_state'] == 1) ? self::$t->_('wait_leave_state_' . $value['wait_leave_state']) : self::$t->_('staff_state_' . $value['staff_state']);
                $staff_leave_date  = $value['staff_state'] == 2 || ($value['staff_state'] == 1 && $value['wait_leave_state'] == 1) ? date("Y-m-d",
                    strtotime($value['leave_date'])) : '';
                $release_state     = $value['release_state'] == 2 ? self::$t->_("paid") : '';
                $hold_reason       = self::$t->_($value['hold_reason']);
                $staff_submit_name = $value['staff_submit_name'];
                $hold_source       = self::$t->_(self::$hold_source[$value['hold_source']]);
                if (empty($staff_submit_name)) {
                    $staff_submit_name = 'system auto';
                }

                $subStaffId = '';
                if(strstr($value['hold_remark'], 'sub_staff_id')){
                    $arr = explode(' ', $value['hold_remark']);
                    $subStaffId = end($arr);
                }
                $export_row[] = [
                    $value['hold_id'],              //hold ID
                    $hold_reason,                   //hold原因
                    $hold_source,                   //hold 来源
                    $subStaffId,                    //子账号
                    $value['staff_info_id'],        //员工工号
                    $value['staff_name'],           //员工姓名
                    $value['store_name'],           //所属网点
                    $value['manage_region_name'],   //大区
                    $value['manage_piece_name'],    //片区
                    $value['job_title_name'],       //职位
                    $value['department_name'],      //部门
                    $value['hire_type_text'],       //雇佣类型
                    $value['handle_people'],        //处理人
                    $staff_submit_name,             //提交人
                    $value['handle_progress_name'], //处理状态
                    $value['hold_time'],            //hold时间
                    $value['update_at'],            //最后更新时间
                    $value['area_name'],            //所属区域
                    $value['handle_result_str'],    //处理结果
                    $staff_state_text,              //员工在职状态
                    $staff_leave_date,              //离职日期
                    $value['hold_month'],           //hold 月份
                    $release_state,                 //是否已释放

                ];
            }
        }

        $head = [
            self::$t->_('hold_id'),                             //hold ID,
            self::$t->_('hold_reason'),                         //hold原因,
            self::$t->_('hold_source'),                         //hold来源
            self::$t->_('staff_support_store_sub_staff_info_id'),//子账号
            self::$t->_('staff_info_id'),                       //员工工号,
            self::$t->_('staff_info_name'),                     //员工姓名,
            self::$t->_('dot'),                                 //所属网点,
            self::$t->_('store_area'),                          //所属大区
            self::$t->_('store_district'),                      //所属片区
            self::$t->_('job_name'),                            //职位,
            self::$t->_('headquarters_department'),             //部门,
            self::$t->_("hire_type"),                           //雇佣类型
            self::$t->_('submit_name'),                         //处理人,
            self::$t->_('sender'),                              //提交人,
            self::$t->_('handle_progress'),                     //处理状态,
            self::$t->_('hold_time'),                           //hold时间,
            self::$t->_('latest_update_time'),                  //最后更新时间,
            self::$t->_('hr_probation_field_store_area_text'),  //所属区域,
            self::$t->_('handle_result'),                       //处理结果,
            self::$t->_('staff_state'),                         //在职状态,
            self::$t->_('staff_leave_date'),                    //离职日期,
            self::$t->_('staff_hold_month'),                    //hold 月份,
            self::$t->_("hold_release_state"),                  //是否已释放
        ];

        return $this->makeHoldExportResult($head,$export_row,$params);
    }

    /**
     * 释放列表
     * @param $params
     * @return array
     */
    public function getReleaseList($params)
    {
        $page_num  = intval($params['page_num'] ?? 1);
        $page_size = intval($params['page_size'] ?? 20);
        $offset    = $page_size * ($page_num - 1);

        $builder       = $this->getReleaseListBuilder($params);
        $builder_count = $this->getReleaseListBuilder($params);
        $builder->groupBy('hm.staff_info_id');
        $builder->orderBy('hm.handle_progress ASC');
        $builder->limit($page_size, $offset);
        $list = $builder->getQuery()->execute()->toArray();

        $items = !empty($list) ? $this->combinationReleaseList($list) : [];

        //获取total count
        $totalCount = $builder_count->groupBy('hm.staff_info_id')->getQuery()->execute()->toArray();

        return [
            'items'    => $items,
            'paginate' => [
                'total_count' => count($totalCount),
                'page_num'    => $page_num,
                'page_size'   => $page_size,
            ],
        ];
    }

    /**
     * release list builder
     * @param $params
     * @return BuilderInterface|null
     */
    public function getReleaseListBuilder($params)
    {
        $type                   = $params['type'] ?? 1;                    //hold类型
        $staff_info_id          = $params['staff_info_id'] ?? '';          //工号
        $store_id               = $params['store_id'] ?? '';               //网点id
        $release_state          = $params['release_state'] ?? '';          //处理状态
        $last_update_date_begin = $params['last_update_date_begin'] ?? ''; //最后更新开始时间
        $last_update_date_end   = $params['last_update_date_end'] ?? '';   //最后更新结束时间
        $hold_date_begin        = $params['hold_date_begin'] ?? '';        //hold 开始时间
        $hold_date_end          = $params['hold_date_end'] ?? '';          //hold 结束时间
        $area_id                = $params['area_id'] ?? '';                //所属区域
        $region_id              = $params['region_id'] ?? '';              //大区id
        $piece_id               = $params['piece_id'] ?? '';               //片区id
        $hire_type              = $params['hire_type'] ?? [];              //雇佣类型
        $builder                = $this->modelsManager->createBuilder();

        $builder->columns("
                            GROUP_CONCAT(hm.id) as id,
			                GROUP_CONCAT(concat(hm.hold_id,'000',hm.id)) as hold_id,
			                GROUP_CONCAT(hm.hold_reason) as hold_reasons,
			                GROUP_CONCAT(hm.hold_source) as source,
			                GROUP_CONCAT(hm.version) as version,
                            hm.staff_info_id,
                            hm.type,
                            hm.release_time,
                            hm.release_state,
                            hm.release_submit_id,
                            hs.name as staff_info_name,
                            hs.sys_store_id,
                            hs.state as staff_state,
                            hs.wait_leave_state,
                            hs.leave_date,
                            hs.sys_department_id,
                            hs.node_department_id,
                            hs.job_title,
                            hs.hire_type,
                            store.manage_region,
                            store.manage_piece
                            ");
        $builder->from(['hm' => HoldStaffManageModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hm.staff_info_id = hs.staff_info_id', 'hs');
        $builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
        $builder->where("type = :type:", ['type' => $type]);
        $builder->andWhere("hm.is_delete = 0");
        $builder->andWhere('not exists (select mm.staff_info_id from App\Models\backyard\HoldStaffManageModel mm where mm.handle_progress != 3 and mm.type = :type: and hm.staff_info_id = mm.staff_info_id and mm.is_delete = 0)',
            ['type' => $type]);

        if (!empty($staff_info_id)) {
            $builder->andWhere("hm.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_info_id]);
        }

        if (!empty($store_id)) {
            $builder->andWhere("hs.sys_store_id = :store_id:", ['store_id' => $store_id]);
        }

        if (!empty($hire_type)) {
            $builder->inWhere("hs.hire_type", $hire_type);
        }

        if (!empty($release_state)) {
            $builder->andWhere("hm.release_state = :release_state:", ['release_state' => $release_state]);
        }

        if (!empty($last_update_date_begin)) {
            $builder->andWhere("hm.update_at >= :last_update_date_begin:",
                ['last_update_date_begin' => $last_update_date_begin . ' 00:00:00']);
        }

        if (!empty($last_update_date_end)) {
            $builder->andWhere("hm.update_at <= :last_update_date_end:",
                ['last_update_date_end' => $last_update_date_end . ' 23:59:59']);
        }

        if (!empty($hold_date_begin)) {
            $builder->andWhere("hm.release_time >= :hold_date_begin:",
                ['hold_date_begin' => $hold_date_begin . ' 00:00:00']);
        }

        if (!empty($hold_date_end)) {
            $builder->andWhere("hm.release_time <= :hold_date_end:",
                ['hold_date_end' => $hold_date_end . ' 23:59:59']);
        }

        if (!empty($area_id)) {
            $sorting_no = self::$manage_areas[$area_id] ?? '';
            if (!empty($sorting_no)) {
                //$builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
                $builder->andWhere("store.sorting_no = :sorting_no:", ['sorting_no' => $sorting_no]);
            }
        }

        if (!empty($region_id)) {
            $builder->andWhere("store.manage_region = :manage_region:", ['manage_region' => $region_id]);
        }

        if (!empty($piece_id)) {
            $builder->andWhere("store.manage_piece = :manage_piece:", ['manage_piece' => $piece_id]);
        }

        return $builder;
    }

    /**
     * 释放列表组合数据
     * @param $list
     * @return mixed
     */
    public function combinationReleaseList($list)
    {
        $release_submit_ids = array_values(array_filter(array_column($list, 'release_submit_id')));

        $release_submit_list = $this->getStaffList($release_submit_ids);
        $release_submit_list = array_column($release_submit_list, null, 'staff_info_id');

        $store_ids = array_column($list, 'sys_store_id');

        $hire_type_list = array_column((new \App\Services\SysService())->getHireTypeList(), 'label', 'value');

        $sys_store_list = (new SysStoreService())->getStoreList($store_ids);
        $sys_store_list = array_column($sys_store_list, null, 'id');

        foreach ($list as $key => &$value) {
            $value['id']              = explode(',', $value['id']);
            $value['hold_id']         = explode(',', $value['hold_id']);
            $value['hold_reason_arr'] = explode(',', $value['hold_reasons']);
            $value['version']         = explode(',', $value['version']);
            switch ($value['release_state']) {
                case 1:
                    $value['release_state_name'] = self::$t->_('franchisee_status_0');
                    break;
                case 2:
                    $value['release_state_name'] = self::$t->_('paid');
                    break;
                default:
                    $value['release_state_name'] = '';
            }
            if (!empty($value['release_submit_id'])) {
                if ($value['release_submit_id'] == 10000 || $value['release_submit_id'] == 10003) {
                    $value['staff_submit_name'] = 'system auto';
                } else {
                    $submit_name                = $release_submit_list[$value['release_submit_id']]['name'] ?? '';
                    $value['staff_submit_name'] = $submit_name . ' ' . $value['release_submit_id'];
                }
            } else {
                $value['staff_submit_name'] = '';
            }

            $value['area_name'] = '';
            if ($value['sys_store_id'] == '-1') {
                $value['store_name'] = GlobalEnums::HEAD_OFFICE;
            } else {
                $value['store_name'] = $sys_store_list[$value['sys_store_id']]['name'] ?? '';
                $value['area_name']  = $sys_store_list[$value['sys_store_id']]['sorting_no'] ?? '';
            }

            $value['staff_info_name'] = $value['staff_info_name'] . ' ' . $value['staff_info_id'];
            $value['source']          = explode(',', $value['source']);

            $value['manage_region_name'] = $this->showRegionName($value['manage_region']);
            $value['manage_piece_name']  = $this->showPieceName($value['manage_piece']);
            $value['hire_type_text']     = $hire_type_list[$value['hire_type']] ?? '';
        }
        return $list;
    }

    /**
     * release 下载
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function exportReleaseList($params)
    {
        $builder = $this->getReleaseListBuilder($params);
        $builder->groupBy('hm.staff_info_id');
        $builder->orderBy('hm.handle_progress ASC');
        $list       = $builder->getQuery()->execute()->toArray();
        $items      = !empty($list) ? $this->combinationReleaseList($list) : [];
        $export_row = [];

        foreach ($items as $key => $value) {
            $staff_state_text  = ($value['staff_state'] == 1 && $value['wait_leave_state'] == 1) ? self::$t->_('wait_leave_state_' . $value['wait_leave_state']) : self::$t->_('staff_state_' . $value['staff_state']);
            $hold_reasons      = array_filter(explode(',', $value['hold_reasons']));
            $hold_reasons_text = '';
            foreach ($hold_reasons as $k => $v) {
                $hold_reasons_text .= ' ' . self::$t->_($v);
            }
            $export_row[] = [
                $value['staff_info_id'],        //员工
                $value['staff_info_name'],      //员工
                implode(',', $value['hold_id']),//HoldID
                $value['store_name'],           //网点
                $value['manage_region_name'],   //所属大区
                $value['manage_piece_name'],    //所属片区
                $value['area_name'],            //所属区域
                $value['hire_type_text'],       //雇佣类型
                $value['staff_submit_name'],    //操作人
                $value['release_state_name'],   //处理状态
                $hold_reasons_text,
                $staff_state_text,       // 在职状态
                $value['release_time'],  //最后更新时间

            ];
        }

        $head = [
            'staff_info_id',                                   //'员工',
            self::$t->_('staff'),                              //'员工',
            self::$t->_('hold_id'),                            //'HoldID',
            self::$t->_('dot'),                                //'网点',
            self::$t->_('store_area'),                         //所属大区
            self::$t->_('store_district'),                     //所属片区
            self::$t->_('hr_probation_field_store_area_text'), //'所属区域',
            self::$t->_('hire_type'),                          //雇佣类型
            self::$t->_('submit_name'),                        //'操作人',
            self::$t->_('handle_progress'),                    //'处理状态',
            self::$t->_('hold_reason'),                        // hold原因
            self::$t->_('staff_state'),                        //'在职状态',
            self::$t->_('latest_update_time'),                 //'最后更新时间',
        ];

        $fileName   = gmdate('YmdHis', time() + ($this->timeOffset) * 3600) . '_release_list.xls';
        $excel_file = $this->exportExcel($head, $export_row, $fileName);

        $flashOss = new FlashOss();
        //每个国家有每个国家的名字
        $json_url_name = self::HOLD_MANAGER_OSS_PATH . '/' . $fileName;
        $flashOss->uploadFile($json_url_name, $excel_file['data']);
        $download_url = $flashOss->signUrl($json_url_name);

        return ['url' => $download_url];
    }
}