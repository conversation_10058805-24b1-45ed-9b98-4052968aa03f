<?php

namespace App\Modules\My\Services;

use App\Services\SettingEnvService as BaseSettingEnvService;


class SettingEnvService extends BaseSettingEnvService
{
    //马来国家独有的配置项
    protected $country_key_list = [
        'WinHR' => [
            'refresh_resume_information_for_reentry' => '清除旧简历生成新简历员工工号(英文逗号分割)',//刷新简历信息
            'individual_contractor_jobids'           => '个人代理员工可用JD',
            'employee_jobids'                        => '正式员工quick offer可用JD',
            'monthly_employee_jobids'                => '月薪制合同工quick offer可用JD',
            'winhr_th_head_office_no_like_job'       => '网络TA招聘职位',
            'h5_th_head_office_no_like_job'          => '网络TA招聘岗位JD',
            'lnt_contract_email'                     => 'PDPA-LNT邮箱配置',
            'lnt_recruitment_jd_ids'                 => 'LNT公司招聘JD',
            'lnt_recruitment'                        => 'LNT公司招聘开关，0代表关闭，1代表开启，默认1',
            'edit_hc_priority_staff'                 => '可将HC优先级调整为P0的工号',

        ],
        'HRIS'     => [
            'xz_ticket_add_power'                           => '行政工单权限,格式：JSON，(部门、职位、工号多个时以西文逗号分隔)',
            'flexible_scheduling'                           => '灵活排班开关，1开启，0关闭',
            'show_vehicle_type_category_job_title'          => '展示车类型的职位',
            'staff_support_close_Check_Pickup_Delivery_COD' => '取消支援是否验证揽派件和公款 0开启验证 1关闭验证',
            'support_staff_apply_job_title_config'          => 'HCM导入支援员工：配置可支援职位创建子账号的默认角色{"可支援职位id(一个)":[默认角色id(多个英文,分隔)]}',
            'support_job_title_role_config'                 => 'HCM导入支援员工：配置允许被添加为支援员工的职位及其可支援的职位{"可添加职位id(一个)":[可支援职位id(多个英文,分隔)]}',
            'import_staff_support_template'                 => 'HCM导入支援员工：配置导入模板文件地址',
            'store_support_manage_job_title'                => 'HCM导入支援需求：配置允许申请的支援职位【支援职位id】',
            'store_support_manage_import_template'          => 'HCM导入支援需求：配置导入模板文件地址',
            'new_attendance_policy'                         => '部门ID，逗号隔开',
            'IC_staff_absent_days'                          => '个人代理旷工停职天数',
            'IC_staff_leave_days'                           => '个人代理旷工离职天数',
            'part_time_IC_staff_absent_days'                => '兼职个人代理旷工停职天数',
            'part_time_IC_staff_leave_days'                 => '兼职个人代理旷工离职天数',
            'day_shift_duration'                            => '弹性打卡 白班小时数',
            'night_shift_duration'                          => '弹性打卡 夜班小时数',
            'free_shift_position'                           => '执行弹性班次的职位ID',
            'Leave_Policy_Department'                       => '执行新请假规则的部门ID（含子部门），用逗号隔开',
            'update_staff_department_by_org'                => '部门自动变更失败通知-邮箱',
            'Misjudgment_limit'                             => '人脸黑名单-命中记录生成后可误判的最晚天数，默认7天',
            'email_agent_suspension_resign'                 => '个人代理停职离职抄送人待查看列表,多个以英文逗号分隔',
            'blackface_os_reason'                           => '同步人脸黑名单的外协黑名单原因id',
            'lnt_company_ids'                               => 'LNT公司ID',
            'support_staff_limit_join_days'                 => '限制主账号原职位入职后N天后可以开始支援的天数，[职位多个之间,分隔]|天数 例如：[110,13]|3,[37]|1',

        ],
        'Backyard' => [
            'free_punch_white_list'                             => '快递配置不校验人脸、不限制打卡地点名单',
            'os_effective_date_add_hours'                       => '外协申请如果申请雇佣日期是当天，工作班次开始时间需晚于当前时间+X小时',
            'fleet_apply_roles'                                 => '加班车申请角色,多个角色以西文逗号进行分隔',
            'individual_contractor_job_title'                   => '个人代理可选择职位(英文逗号分割)',
            'enable_transition_individual_contractor_job_title' => '可转型为个人代理的职位(英文逗号分割)',//转型
            'quick_offer_position'                              => '使用Quick offer的职位ID',
            'canleavetoday'                                     => '是否可以今天请假，1代表可以申请今天，0代表不能申请今天',
            'facial_verify_face_check'                          => '开启数据安全人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_used_device'                         => '开启常用设备人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_new_wifi'                            => '开启新增WiFi人脸验证，0代表关闭，1代表开启，默认0',
            'facial_verify_token_expiration_date'               => '登录X天后自动过期，需重新登录刷脸。默认值30',
            'facial_verify_captcha'                             => '开启BY登录腾讯云验证码，0代表关闭，1代表开启，默认0',
            'facial_verify_salary_whitelist_noverifiy_face'     => '发薪不打卡白名单无需验证人脸开关，0关闭，1开启，默认1',
            'facial_verify_whitelist_noverifiy_facee'           => '无需人脸校验工号,默认空',
            'hub_store_work_partition_store_ids'                => 'HUB网点工作分区网点配置(多个英文,分隔)',
            'rent_car_store_staff_config'                       => '租车流程特殊网点特殊工号配置',
            'Blackface_verification_position'                   => '黑名单人脸校验职位',
            'Blackface_verification_department'                 => '黑名单人脸校验部门',
            'Blackface_verification_getmsg_ID'                  => '黑名单人脸消息工号',
            'Blackface_verification_Email'                      => '黑名单人脸接收邮箱',
            'PLKN_Date'                                         => '国民假期的周期',
            'lnt_recruitment_job_ids'                           => 'LNT公司招聘职位，多个用英文逗号,分隔',
            'NotAllowedLoginCompanyID'                          => '限制不能登录BY的子公司ID',
            'WhitelistOfLogin'                                  => '子公司能登录BY的员工工号，用逗号隔开',
            'lnt_vehicle_type_ids'                              => '若所选转岗员工的合同公司=LNT，转岗后职位=Van Courier/Car Courier时，车类型字段可选',
            'job_transfer_can_not_apply_position_ids'           => '禁用转岗的一线职位,多个职位使用,间隔',
        ],
        'SYS' => [
            'No_Show_Regulation_CompanyID'         => '不显示规章制度的公司ID',
            'No_Show_Information_Center_CompanyID' => '不显示信息共享中心的公司ID',
            'No_Show_Help_CompanyID'               => '不显示帮助与客服的公司ID',
        ],
    ];

}
