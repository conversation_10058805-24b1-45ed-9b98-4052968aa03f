<?php

namespace App\Modules\La\Controllers;

use App\Controllers\HoldManageController as BaseHoldManageController;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\La\Services\HoldManageService;
use Exception;

class HoldManageController extends BaseHoldManageController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 筛选条件
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function screeningAction()
    {
        $data = (new HoldManageService())->getScreeningList();
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * hold list
     * @Token
     * @Permission(action='hold_manage.hold_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function holdListAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = $hold_manage_service->HoldListParamsValidations();
        $validations = array_merge($validations, ['page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:10']);
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, $validations);

        $list = $hold_manage_service->getHoldList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $list);
    }


    /**
     * 工资/提成释放列表
     * @Token
     * @Permission(action='hold_manage.release_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function releaseListAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = $hold_manage_service->releaseListParamsValidations();
        $validations = array_merge($validations, ['page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:10']);
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, $validations);

        $list = (new HoldManageService())->getReleaseList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $list);
    }

    /**
     * 工资/提成释放下载
     * @Token
     * @Permission(action='hold_manage.release_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportReleaseAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();
        $validations = $hold_manage_service->releaseListParamsValidations();

        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, array_merge($validations));

        $data = (new HoldManageService())->exportReleaseList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }
}