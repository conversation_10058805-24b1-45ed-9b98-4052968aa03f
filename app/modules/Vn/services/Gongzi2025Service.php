<?php

namespace app\modules\Vn\services;

class Gongzi2025Service extends Gongzi2023Service
{

    public $excelShowMap = [
        'Emp.'                   => 'staff_info_id',
        'Name'                   => 'staff_name',
        'Corporate Name'         => 'company_real_id', //真实
        'Payroll Company name'   => 'company_id',      //发薪公司
        'Branch'                 => 'branch_name',
        'Department'             => 'department_name',
        'Position'               => 'job_title_name',
        'Rank'                   => 'job_grade',
        'Country of Citizenship' => 'nation_type',
        'Tax Type'               => 'tax_mode',
        'Social Status'          => 'cal_mode',
        'First-line positions'   => 'frontline_mode',
        'job type'               => 'week_day',


        'Start Date'       => 'start',
        'Last working day' => 'last_working_day',
        'working days'     => 'should_working_days',

        'Days' => 'total_month_days',

        'LW'                      => 'lw',
        'AB'                      => 'ab',
//        'Isolation leave'        => 'covid_on',
        'OFF'                     => 'off',
        'Days not in employment'  => 'not_working_days',
        'Total working days'      => 'total_working_days',
        'Transfer day'            => 'transfer_day',
        'After transfer days'     => 'after_transfer_days',
//        'After Isolation leave'  => 'covid_transfer_after_on',
        'Food ON transfer before' => 'food_on_transfer_before',
        'Food ON transfer after'  => 'food_on_transfer_after',

        'after transfer job type' => 'week_day_adj',
        //固定部分
        'Base Salary'             => 'salary_base',    //check
        'Base Salary Adj.'        => 'salary_adj',
        'Salary Difference'       => 'salary_difference',
        'Salary'                  => 'salary',

        'Performance Allowance Base'       => 'performance_base',
        'Performance Allowance Base Adj.'  => 'performance_adj',
        'Performance Allowance Difference' => 'performance_difference',
        'Performance Allowance'            => 'performance',

        'Attendance Allowance Base'       => 'attendance_base',
        'Attendance Allowance Base Adj.'  => 'attendance_adj',
        'Attendance Allowance Difference' => 'attendance_difference',
        'Attendance Allowance'            => 'attendance',

        'Traffic Rental Base'       => 'traffic_base',
        'Traffic Rental Base Adj.'  => 'traffic_adj',
        'Traffic Rental Difference' => 'traffic_difference',
        'Traffic Rental'            => 'traffic',

        'Telephone Rental Base'       => 'phone_base',
        'Telephone Rental Base Adj.'  => 'phone_adj',
        'Telephone Rental Difference' => 'phone_difference',
        'Telephone Rental'            => 'phone',

        'Food Rental Base'       => 'food_base',
        'Food Rental Base Adj.'  => 'food_adj',
        'Food Rental Difference' => 'food_difference',
        'Food Rental'            => 'food',

        'Rate per Hour' => 'salary_hour',

        'Working 1.5 times' => 'ot1_15_len',
        'OT 1.5 times'      => 'ot1_15',
        'Working 2.0 times' => 'ot1_2_len',
        'OT 2.0 times'      => 'ot1_2',
        'Working 2.1 times' => 'ot1_21_len',
        'OT 2.1 times'      => 'ot1_21',

        'Weekend 2 time'   => 'ot2_2_len',
        'OT 2 time'        => 'ot2_2',
        'Weekend 2.7 time' => 'ot2_27_len',
        'OT 2.7 time'      => 'ot2_27',

        'Holiday 3 time'             => 'ot3_3_len',
        'OT 3 time'                  => 'ot3_3',
        'Holiday 3.9 time'           => 'ot3_39_len',
        'OT 3.9 time'                => 'ot3_39',
        'OT'                         => 'ot',
        //'Annual leave discount days' => 'annual_leave_discount_days',
        //'Converted Paid Leaves'      => 'converted_paid',
        'OT exempt'                  => 'ot_notax',
        'Night Shift'                => 'night_shift_len',
        'Night Shift Allowance'      => 'night_shift_allowance',

        'Covid Leave Salary' => 'covid_salary',

        'Notebook Rental Base'   => 'notebook_base',
        'Notebook Rental'        => 'notebook',
        'House rental Base'      => 'house_base',
        'House rental'           => 'house',
        'Firestarter Award Base' => 'firestarer_award_base',
        'Firestarter Award'      => 'firestarer_award',

        'Bonus' => 'bonus',

        'Recommendation'               => 'recommended',
        'Compensation'                 => 'compensation',
        'Incentive'                    => 'incentive',
        'other income1'                => 'other_income1',  //fixed/social new
        'other income2'                => 'other_income2',  //fixed/social new
        'Performance allowance deduct' => 'pre_tax_deduct1',//fixed new

        'total income' => 'total_income',

        'Social base'                  => 'social_base',
        'Social insurance ER'          => 'social_insurance_er',      //2023 new 雇主社保
        'Health insurance ER'          => 'health_insurance_er',      //2023 new 雇主医疗
        'Unemployment insurance ER'    => 'unemployment_insurance_er',//2023 new 雇主失业
        'sss ER Company'               => 'sss_company',
        'Social insurance EE'          => 'social_insurance_ee',      //2023 new 雇员社保
        'Health insurance EE'          => 'health_insurance_ee',      //2023 new 雇员医疗
        'Unemployment insurance EE'    => 'unemployment_insurance_ee',//2023 new 雇员失业
        'sss EE Staff'                 => 'sss_staff',
        'No of dependants'             => 'dependants_num',
        'Special additional deduction' => 'special_tax_deduct',
        'Taxable Pay'                  => 'tax_base',
        'Tax'                          => 'tax',

        'Equipment'       => 'equipment',
        'Uniform'         => 'uniform',
        'Incentive(paid)' => 'incentive_paid',
        'other deduct1'   => 'other_deduct1',
        'other deduct2'   => 'other_deduct2',
        'Total Deduct'    => 'total_deduct',

        'Wedding gold'         => 'wedding',
        'Funeral money'        => 'funeral',
        'Employee death grant' => 'death',
        'After tax income1'    => 'after_tax_income1',

        'Net Amount'       => 'net_income',
        'negative data'    => 'negative_data',
        'Remark'           => 'remark',
        'Remark1'          => 'remark1',//new
        'Remark2'          => 'remark2',//new
        'Team'             => 'team',
        'Bank name'        => 'bank_type',
        'Bank account no.' => 'bank_no',
        'status'           => 'status',
        'update time'      => 'updated_at',
    ];
}
