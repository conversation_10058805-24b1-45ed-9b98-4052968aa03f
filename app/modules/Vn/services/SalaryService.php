<?php


namespace App\Modules\Vn\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\Enums;
use App\Library\Enums\SalaryEnums;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Models\backyard\FileOssUrlModel;
use App\Models\backyard\SysStoreModel;
use App\Services\CertificateService;
use App\Services\FormPdfServer;
use App\Services\SettingEnvService;
use App\Services\StaffInfoService;
use App\Models\backyard\SalaryGongziModel;
use App\Services\OssService;
use App\Services\SalaryService as BaseSalaryService;



class SalaryService extends BaseSalaryService
{
    public $salary_read_db = 'db_rby';
    protected static $payroll_backyard_url = null;
    public static $list_name = 'salary_send';
    public static $key_name = 'salary_send_in_all';
    /**
     * 员工设备语言
     * @var array
     */
    public static $equipmentLang= [];
    const SALARY_STATUS_PAID = 3;
    //$excelShowMap每加一个字段,需要在 $str_type_column(导出字符串类型), $int_type_column(导出整形类型),$map_type_column (需要枚举类型关联)里添加进去,如果不加,默认是导出float类型, 写的是salary_gongzi数据库里的字段名; 没有的不需要添加,只是为了导出
    public static $str_type_column = [
        'start',
        'last_working_day',
        'transfer_day',
        'remark',
        'remark1',
        'remark2',
        'team',
        'staff_name',
        'job_title_name',
        'branch_name',
        'department_name',
        'bank_no',
        'updated_at',
    ];

    public static $int_type_column = [
        'staff_info_id',
        'job_title_id',
        'department_id',
        'salary_base',
        'salary_adj',
        'performance_base',
        'performance_adj',
        'taxsubsidy_base',
        'taxsubsidy_adj',
        'subsidy_base',
        'subsidy_adj',
        'deminimis_base',
        'deminimis_adj',
        'food_base',
        'food_adj',
        'car_base',
        'car_adj',
        'risk_base',
        'risk_adj',
    ];

    public static $map_type_column = ['bank_type', 'cal_mode', 'tax_mode', 'status'];
    public static $bank_type_name = [
        '0' => 'Not Known',
        '2' => 'SCB',
        '21' => 'UB',
    ];

    //生成pdf 的方法 类型 1 mpdf 2 go
    const PDF_FUNC_1 = 1;
    const PDF_FUNC_2 = 2;

    /**
     * 下面这些字段不管有没有金额 都要展示在pdf
     * 除了这些字段 其他的金额为0 不展示
     */
    public $showColumns = [
        'salary_base',                   //基本工资
        'salary',                        //本月工资
        'performance',                   //绩效工资
        'attendance',                    //出勤补贴
        'traffic',                       //交通补贴
        'phone',                         //话费补贴
        'food',                          //餐补
        'sss_staff',                     //社保个人部分
        'tax',                           //个税
    ];



    /**
     * 获取 区间内 工资信息 原定调薪资那边方法 改为自己查库
     * @param $param
     * @return array
     */
    public function get_payroll_list($param)
    {
        $conditions = ['status = :status:'];
        $bind['status'] = SalaryEnums::SALARY_STATUS_PAID;

        //如果有工号搜索 显示员工维度
        if (!empty($param['month_start'])) {
            $conditions[] = 'excel_month >= :month_start:';
            $bind['month_start'] = $param['month_start'];
            $conditions[] = 'excel_month <= :month_end:';
            $bind['month_end'] = $param['month_end'] ?? $param['month_start'];
        }

        if (!empty($param['time_start'])) {
            $conditions[] = 'paid_day >= :time_start:';
            $bind['time_start'] = $param['time_start'];
            $conditions[] = 'paid_day <= :time_end:';
            $bind['time_end'] = $param['time_end'];
        }

        if (!empty($param['company_id'])) {
            $conditions[] = 'company_id = :company_id:';
            $bind['company_id'] = $param['company_id'];
        }

        if (!empty($param['staff_id'])) {
            $conditions[] = 'staff_info_id = :staff_id:';
            $bind['staff_id'] = $param['staff_id'];
            $queryInfo = [
                'conditions' => implode(' and ', $conditions),
                'bind' => $bind,
                'columns' => 'staff_info_id as staff_id ,excel_month,paid_day',
            ];
        } else {
            $queryInfo = [
                'conditions' => implode(' and ', $conditions),
                'bind' => $bind,
                'columns' => 'count(1) as staff_num ,excel_month, paid_day',
                'group' => 'excel_month, paid_day',
            ];
        }
        $salary = SalaryGongziModel::find($queryInfo);

        return $salary ? $salary->toArray() : [];
    }

    /**
     * 发送工资条 按钮 发送消息 和 邮件 消费队列 操作
     * @param $param
     * @return bool
     */
    public function send_all($param)
    {
        $redis = $this->getDI()->get("redis");
        //如果 存在正在发送的操作 则不能操作继续发送
        $waiting = $redis->lLen(self::$list_name);
        if (!empty($waiting)) {
            return false;
        }

        $conditions = ['status = :status:'];
        $bind['status'] = SalaryEnums::SALARY_STATUS_PAID;

        if (!empty($param['month_start'])) {
            $conditions[] = 'excel_month >= :month_start:';
            $bind['month_start'] = $param['month_start'];
            $conditions[] = 'excel_month <= :month_end:';
            $bind['month_end'] = $param['month_end'] ?? $param['month_start'];
        }

        if (!empty($param['time_start'])) {
            $conditions[] = 'paid_day >= :time_start:';
            $bind['time_start'] = $param['time_start'];
            $conditions[] = 'paid_day <= :time_end:';
            $bind['time_end'] = $param['time_end'] ?? $param['time_start'];
        }

        if (!empty($param['company_id'])) {
            $conditions[] = 'company_id = :company_id:';
            $bind['company_id'] = $param['company_id'];
        }

        if (!empty($param['staff_id'])) {
            $conditions[] = 'staff_info_id = :staff_id:';
            $bind['staff_id'] = $param['staff_id'];
        }
        $queryInfo = [
            'conditions' => implode(' and ', $conditions),
            'bind' => $bind,
            'columns' => 'staff_info_id as staff_id ,excel_month,paid_day',
        ];


        $data = SalaryGongziModel::find($queryInfo)->toArray();
        if (empty($data)) {
            throw new BusinessException(static::$t->_('no_data'));
        }
        $this->checkPaidDayRange($data,$param);
        //标记点击发送
        $model = SalaryGongziModel::class;
        $clickSendSql = "update {$model} set click_send=".SalaryGongziModel::CLICK_SEND .' where '. implode(' and ', $conditions);
        $this->modelsManager->executeQuery($clickSendSql,$bind)->success();
        //写缓存总数
        $count = count($data);
        $redis->set(self::$key_name, $count, 18 * 3600);
        //标记点击发送
        $model = SalaryGongziModel::class;
        $clickSendSql = "update {$model} set click_send=".SalaryGongziModel::CLICK_SEND .' where '. implode(' and ', $conditions);
        $this->modelsManager->executeQuery($clickSendSql,$bind)->success();
        $di = $this->getDI();
        $logger = $di->get('logger');
        $logger->info("salary sheet send all data" . json_encode($data, JSON_UNESCAPED_UNICODE));
        //入队列
        foreach ($data as $da) {
            $value = $da['staff_id'] . '_' . $da['excel_month'];
            $redis->lpush(self::$list_name, $value);
        }

        return true;
    }

    /**
     * 消费队列任务调用 发送消息和邮件
     * @param $staff_id
     * @param $month
     * @return bool|string|void
     */
    public function send_salary($staff_id, $month)
    {
        $logger = $this->logger;
        try {
            //验证是否有数据
            $sql = "select staff_info_id as staff_id ,excel_month,paid_day ,staff_name from salary_gongzi
                    where  staff_info_id = {$staff_id} and excel_month = '{$month}'
                   ";
            $check = $this->{$this->salary_read_db}->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (empty($check)) {
                $message = "salary_task 员工工资条_{$staff_id}_{$month} 没有记录";
                $logger->info($message);
                return $message;
            }
            //获取消息 模板 格式化
            $param['staff_id'] = $check['staff_id'];
            $param['name'] = $check['staff_name'];
            $param['date_time'] = date('Y-m-d', strtotime("{$month} last day of"));//每月最后一天
            $param['month'] = $month;
            $template = $this->format_content_message($param);
            $title = $template['title'];
            $content = $template['content'];

            //添加消息 必填项
            $message_param['staff_users'] = [$staff_id];//数组 多个员工id
            $message_param['message_title'] = $title;
            $message_param['message_content'] = addslashes($content);
            $message_param['staff_info_ids_str'] = $staff_id;
            $message_param['id'] = $message_param['message_content_id'] = time() . $staff_id . rand(1000000, 9999999);
            $message_param['category'] = 8;
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$message_param]);
            $res = $bi_rpc->execute();

            if (isset($res['code']) && $res['code'] == 1) {
                $logger->info("salary_ext_message {$staff_id}_{$month} 写入message成功");
                //发push
                $data = [
                    "staff_info_id" => $staff_id,  //推送人员工ID
                    "src" => "backyard",      //1:'kit'; 2:'backyard','c';
                    "message_title" => $title,  //标题
                    "message_content" => '',//内容
                    'message_scheme' => "flashbackyard://fe/tab?index=message",
                ];
                $client = new ApiClient('bi_rpc', '', 'push_to_staff');
                $client->setParamss($data);
                $res = $client->execute();
                if (!$res) {
                    $this->getDI()->get('logger')->info('handle fill company mobile message push to backyard  return false :' . json_encode($data));
                }
            } else {
                $logger->info("salary_ext_message {$staff_id}_{$month} 写入message失败");
            }
            //前端提供的登录url 这个页面是在by项目中的
            $url = static::$payroll_backyard_url;
            if (is_null(static::$payroll_backyard_url)) {
                $url = static::$payroll_backyard_url = (new SettingEnvService())->getSetVal('payroll_backyard_url');
            }
            if (empty($url)) {
                $logger->info("salary_payroll_backyard_url  setting is null");
                return 'salary_payroll_backyard_url  setting is null';
            }
            $pdf_url = $url . '?date=' . $month;
            $template = $this->format_content($param, $pdf_url);
            $title = $template['title'];
            $content = $template['content'];

            //获取 邮箱信息
            $staff_bll = new StaffInfoService();
            $staff_info = $staff_bll->getStaffInfo_hr($staff_id);

            $mail_flag_1 = $mail_flag_2 = false;
            if (!empty($staff_info['email'])) {
                try {
                    $mail_flag_1 = BiMail::send_salary($staff_info['email'], $title, $content);
                }catch (\Exception $e){
                    $logger->info( 'staff_info_id :' .$staff_id ." email :".$e->getMessage());
                }
            }
            if (!empty($staff_info['personal_email'])) {
                try {
                    $mail_flag_2 = BiMail::send_salary($staff_info['personal_email'], $title, $content);
                }catch (\Exception $e){
                    $logger->info( 'staff_info_id :' .$staff_id ." personal_email :" .$staff_info['personal_email'] .' error:'.$e->getMessage());
                }
            }

            $logger->info("salary_ext_mail 员工工资条：{$staff_id}-{$month} 发送邮件_{$mail_flag_1}_{$mail_flag_2}");

            //最后 生成pdf
            $url = $this->format_pdf($staff_id, $month);

            $logger->info("salary_ext_send_salary {$staff_id}_{$month} url {$url}");
            return $url;
        } catch (\Exception $e) {
            //记录下日志
            $logger->info("salary_ext_send_salary {$staff_id}_{$month} 发送消息邮件失败 " . $e->getMessage() . $e->getFile() . $e->getLine() . $e);
            return $e->getMessage();
        }
    }


    /**
     * 获取已支付的工资信息
     * @param $staff_info_id
     * @param $gongzi_month
     * @return mixed
     */
    public function getPaidSalaryDataFromDb($staff_info_id, $gongzi_month)
    {
        $paid_type = SalaryEnums::SALARY_STATUS_PAID;
        $sql = "select *  from salary_gongzi where staff_info_id =:staff_info_id and  excel_month =:excel_month and status =:status";
        $bind = ['staff_info_id' => $staff_info_id, 'excel_month' => $gongzi_month, 'status' => $paid_type];
        return $this->{$this->salary_read_db}->query($sql, $bind)->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 生成PDF
     * @param $staff_id
     * @param $month
     * @param int $is_by 0 默认 task 调用  1 by 调用工资条 返回data 数据   2 by 调用 工资条返回pdf 地址 3 by 调用返回 非固定pdf 地址
     * @param int $lang
     * @return array|string|string[]
     */
    public function format_pdf($staff_id, $month, $is_by = 0, $lang = 0)
    {
        if (empty($lang)) {
            $lang = strtolower(substr(self::$language, 0, 2));
            $lang = FileOssUrlModel::$lang_list[$lang] ?? FileOssUrlModel::LANG_EN;//默认
        }
        $file = ['file_name' => '', 'url' => ''];
        if (empty($month)) {
            return $file;
        }
        $data_res = $this->getPaidSalaryDataFromDb($staff_id, $month);

        if (empty($data_res)) {
            $this->logger->write_log("format_pdf 员工工资条_{$staff_id}_{$month} 没有记录", 'info');
            return $is_by != Enums::SALARY_SOURCE_DATA ? $file : [];
        }
        $data_res = current($data_res);

        $data = $this->formatInterfaceV2($data_res);
        //获取员工信息
        $staff_info = $this->getHrStaffInfo($staff_id);
        if (empty($staff_info)) {
            return $data;
        }

        //渲染页面 数据
        $data['staff_id'] = (string) $staff_id;
        $data['paid_day'] = (string)$data_res['paid_day'];
        $data['name'] = (string)$staff_info['name'];
        $data['position'] = (string)(empty($data_res['job_title_name']) ? $staff_info['job_title_name'] : $data_res['job_title_name']);
        $data['department'] = (string)($data_res['sys_store_id'] == SysStoreModel::HEAD_OFFICE_ID ? $data_res['department_name'] : $data_res['branch_name']);
        $data['bank_no'] = (string)(empty($data_res['bank_no']) ? $staff_info['bank_no'] : $data_res['bank_no']);
        $data['transfer_day'] = $data_res['transfer_day'];
        $data['month'] = date('m/Y',strtotime($month));
        $data['period'] = date('Y-m-01',strtotime($data_res['excel_month'])) .'~'.date('Y-m-t',strtotime($data_res['excel_month']));
        $data['salary_date']= $month;
        $data['have_annual_leave'] = $month < '2022-08';// 判断年假是否在使用
        //计算加班 用的 发薪周期
        $data['salary_cycle'] = $data_res['excel_month'];
        $data['salary_date_period'] = self::$t->_('salary_date_period',
            ['month' => $data['salary_date'], 'period' => $data['period']]);
        //backyard 工资条页面 调用
        if ($is_by == Enums::SALARY_SOURCE_DATA) {
            return $data;
        }

        $data['background_img'] = $this->getCompanyLogo($data_res['company_id']);
        $companyConfigData = (new CertificateService())->getPdfCompanyInfoByCompanyId(['company_id' => $data_res['company_id']]);
        $ossParam['config_data'] = !empty($companyConfigData) ? $companyConfigData : [];

        //验证是否 上传过 oss
        $p['month'] = $month;
        $p['lang'] = $lang;
        $oss_bll = new OssService();
        $url = $oss_bll->get_infoV2($staff_id, OssService::SALARY_PDF, $p);
        if (empty($url)) {
            $ossParam['month'] = $month;
            $ossParam['type']  = OssService::SALARY_PDF;
            $ossParam['lang']  = intval($lang);
            $ossParam['data']  = $data;
            $url               = $this->getPdfUrl($ossParam);
        }

        //页面下载 返回  数组
        if($is_by == SalaryEnums::SALARY_SOURCE_DOWNLOAD){
            return [$url];
        }
        return urldecode($url);
    }


    /**
     * 发送消息内容
     * @param $param
     * @return string[]
     */
    public function format_content_message($param): array
    {
        //['en'=>1,'zh'=>2,'zh-CN'=>2,'vi' =>3];
        $mouth = date('m/Y', strtotime($param['month']));
        $lang = static::$equipmentLang[$param['staff_id']] ?? 3;
        $title = "Phiếu lương {$mouth}";
        $content = "<p style='font-size: 45px'>Phiếu lương {$mouth} của bạn đã được gửi, vui lòng kiểm tra. Nếu có thắc mắc yêu cầu phản hồi lại trong thời gian 3 ngày kể từ ngày nhận lương.</p>";

        if ($lang == 1) {
            $title = "{$mouth} PAY SLIP";
            $content = "<p style='font-size: 45px'>Your payslip for {$mouth} has been sent. Kindly check and send your questions within 3 days from date of receipt.</p>";
        }
        if ($lang == 2) {
            $title = "{$mouth} 工资条";
            $content = "<p style='font-size: 45px'>您的{$mouth} 工资条已发送，请检查并在3日内反馈您的问题。</p>";
        }
        return ['title' => $title, 'content' => $content];
    }
    /**
     * 邮件的 标题内容格式 邮件比消息 多一个pdf url地址
     * @param $param
     * @param string $pdf_url 跳转pdf 地址 需要登陆验证的
     * @return array
     */
    public function format_content($param, $pdf_url = '',$is_to_message=false)
    {
        $mouth = date('m', strtotime($param['month']));
        $year = date('Y', strtotime($param['month']));
        //消息和邮件 标题
        $title = "{$mouth}/{$year} 工资条 / Phiếu lương";

        $content = "<p>您的{$mouth}/{$year} 工资条已发送，请检查并在3日内反馈您的问题。</p>";
        $content .= "<p><a href='{$pdf_url}' target='_blank'>点击查看工资条。</a></p><br/>";
        $content .= "<p>Phiếu lương {$mouth}/{$year} của bạn đã được gửi, vui lòng kiểm tra. Nếu có thắc mắc yêu cầu phản hồi lại trong thời gian 3 ngày kể từ ngày nhận lương.</p>";
        $content .= "<p><a href='{$pdf_url}' target='_blank'>Ấn để kiểm tra phiếu lương. </a></p><br/>";
        return ['title' => $title, 'content' => $content];
    }

    public function get_salary_info($staff_id, $month)
    {

        $paid_type = self::SALARY_STATUS_PAID;
        $sql = "select * from salary_gongzi where staff_info_id = {$staff_id} and excel_month = '{$month}' and status = {$paid_type}";
        $data = $this->{$this->salary_read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        return $data;
        $new_data = [];
        foreach ($data as $item) {
            $fmt_item = [];
            foreach ($item as $key => $value) {
                if (in_array($key, self::$str_type_column) || in_array($key, self::$map_type_column)) {
                    $fmt_value = $value;
                } elseif (in_array($key, self::$int_type_column)) {
                    $fmt_value = intval($value);
                } else {
                    $fmt_value = floatval($value);
                }
                $fmt_item[] = $fmt_value;
            }
            $new_data[] = $fmt_item;
        }

        return $new_data;
    }


    /**
     * 整理工资条字段
     * @param $salaryData
     * @return array
     */
    protected function formatInterfaceV2($data)
    {
        $defaultItem = [
            'index'           => '',       //字段
            'name'            => '',       //字段名字的显示
            'num'             => '',       //数量
            'amount'          => '',       //金额
            'detection_field' => 'amount', //检测字段 默认 amount
        ];
        if ($data['excel_month'] < '2025-03') {
            $foodNum = '-';
        } else {
            $foodNum = $data['frontline_mode'] == GongziService::FRONTLINE_MODE_ISNOT ? $data['total_working_days'] : $data['food_on_transfer_before'] + $data['food_on_transfer_after'];
        }
        //第一个 table name - 字段名字的显示 num 字段对应的数量 amount 字段对应的金额 每行2个成员 对key 取模 如果没有值 不显示字段
        $left = [
            [//基本工资
                'index'  => 'salary_base',
                'name'   => 'salary_base',
                'num'    => '-',
                'amount' => $data['salary_base'] ?? '',
            ],
            [ //本月工资
                'index'  => 'salary',
                'name'   => 'salary',
                'num'    => $data['total_working_days'] ?? '',
                'amount' => $data['salary'] ?? '',
            ],
            [ //绩效工资
                'index'  => 'performance',
                'name'   => 'performance',
                'num'    => $data['total_working_days'] ?? '',
                'amount' => $data['performance'] ?? '',
            ],
            [ //出勤补贴
                'index'  => 'attendance',
                'name'   => 'attendance',
                'num'    => '-',
                'amount' => $data['attendance'] ?? '',
            ],
            [ //交通补贴
                'index'  => 'traffic',
                'name'   => 'traffic',
                'num'    => '-',
                'amount' => $data['traffic'] ?? '',
            ],
            [ //话费补贴
                'index'  => 'phone',
                'name'   => 'phone',
                'num'    => '-',
                'amount' => $data['phone'] ?? '',
            ],
            [ //餐补
                'index'  => 'food',
                'name'   => 'food',
                'num'    => $foodNum,
                'amount' => $data['food'] ?? '',
            ],
            [ //工作日1.5倍加班
                'index'  => 'ot1_15',
                'name'   => 'ot1_15',
                'num'    => $data['ot1_15_len'] ?? '',
                'amount' => $data['ot1_15'] ?? '',
            ],
            [ //工作日2.0倍加班
                'index'  => 'ot1_2',
                'name'   => 'ot1_2',
                'num'    => $data['ot1_2_len'] ?? '',
                'amount' => $data['ot1_2'] ?? '',
            ],
            [ //工作日2.1倍加班
                'index'  => 'ot1_21',
                'name'   => 'ot1_21',
                'num'    => $data['ot1_21_len'] ?? '',
                'amount' => $data['ot1_21'] ?? '',
            ],
            [ //周末2.0倍加班
                'index'  => 'ot2_2',
                'name'   => 'ot2_2',
                'num'    => $data['ot2_2_len'] ?? '',
                'amount' => $data['ot2_2'] ?? '',
            ],
            [//周末2.7倍加班
                'index'  => 'ot2_27',
                'name'   => 'ot2_27',
                'num'    => $data['ot2_27_len'] ?? '',
                'amount' => $data['ot2_27'] ?? '',
            ],
            [ //节假日3.0倍加班
                'index'  => 'ot3_3',
                'name'   => 'ot3_3',
                'num'    => $data['ot3_3_len'] ?? '',
                'amount' => $data['ot3_3'] ?? '',
            ],
            [ //节假日3.9倍加班
                'index'  => 'ot3_39',
                'name'   => 'ot3_39',
                'num'    => $data['ot3_39_len'] ?? '',
                'amount' => $data['ot3_39'] ?? '',
            ],
            [//转换的带薪假
                'index'  => 'converted_paid',
                'name'   => 'converted_paid',
                'num'    => $data['annual_leave_discount_days'] ?? '',
                'amount' => $data['converted_paid'] ?? '',
            ],
            [//隔离假工资
                'index'  => 'covid_salary',
                'name'   => 'covid_salary',
                'num'    => '-',
                'amount' => $data['covid_salary'] ?? '',
            ],
            [//夜班津贴：
                'index'  => 'night_shift_allowance',
                'name'   => 'night_shift_allowance',
                'num'    => $data['night_shift_len'] ?? '',
                'amount' => $data['night_shift_allowance'] ?? '',
            ],
            [//笔记本补贴
                'index'  => 'notebook',
                'name'   => 'notebook',
                'num'    => '-',
                'amount' => $data['notebook'] ?? '',
            ],
            [//租房补贴
                'index'  => 'house',
                'name'   => 'house',
                'num'    => '-',
                'amount' => $data['house'] ?? '',
            ],
            [//星火激励
                'index'  => 'firestarer_award',
                'name'   => 'firestarer_award',
                'num'    => '-',
                'amount' => $data['firestarer_award'] ?? '',
            ],
            [ //奖金
                'index'  => 'bonus',
                'name'   => 'bonus',
                'num'    => '-',
                'amount' => $data['bonus'] ?? '',
            ],
            [//补偿金
                'index'  => 'compensation',
                'name'   => 'compensation',
                'num'    => '-',
                'amount' => $data['compensation'] ?? '',
            ],
            [//提成
                'index'  => 'incentive',
                'name'   => 'incentive',
                'num'    => '-',
                'amount' => $data['incentive'] ?? '',
            ],
            [//其他收入（纳税）
                'index'  => 'other_income',
                'name'   => 'other_income',
                'num'    => '-',
                'amount' => $data['other_income1'] + $data['other_income2'],
            ],
            [//结婚礼金（免税）
                'index'  => 'wedding',
                'name'   => 'wedding',
                'num'    => '-',
                'amount' => $data['wedding'] ?? '',
            ],
            [//丧葬金（免税）
                'index'  => 'funeral',
                'name'   => 'funeral',
                'num'    => '-',
                'amount' => $data['funeral'] ?? '',
            ],
            [//员工去世补助金（免税）
                'index'  => 'death',
                'name'   => 'death',
                'num'    => '-',
                'amount' => $data['death'] ?? '',
            ],
            [//其他收入（免税
                'index'  => 'after_tax_income1',
                'name'   => 'after_tax_income1',
                'num'    => '-',
                'amount' => $data['after_tax_income1'],
            ],
        ];

        //没有金额的不展示
        $left = $this->salaryTableEmptyCheck($left);
        //右边的
        $right = [
            [//绩效扣除（免税）
                'index'  => 'pre_tax_deduct1',
                'name'   => 'pre_tax_deduct1',
                'num'    => '-',
                'amount' => $data['pre_tax_deduct1'] ?? '',
            ],
            [//设备
                'index'  => 'equipment',
                'name'   => 'equipment',
                'num'    => '-',
                'amount' => $data['equipment'] ?? '',
            ],
            [//工服
                'index'  => 'uniform',
                'name'   => 'uniform',
                'num'    => '-',
                'amount' => $data['uniform'] ?? '',
            ],
            [//提成（已支付）
                'index'  => 'incentive_paid',
                'name'   => 'incentive_paid',
                'num'    => '-',
                'amount' => (int)$data['incentive_paid'],
            ],
            [//其他扣款（纳税）
                'index'  => 'other_deduct',
                'name'   => 'other_deduct',
                'num'    => '-',
                'amount' => $data['other_deduct1'] + $data['other_deduct2'],
            ],
            [//社保个人部分
                'index'  => 'sss_staff',
                'name'   => 'sss_staff',
                'num'    => '-',
                'amount' => $data['sss_staff'] ?? '',
            ],
            [//个税
                'index'  => 'tax',
                'name'   => 'tax',
                'num'    => '-',
                'amount' => $data['tax'] ?? '',
            ],
        ];


        //没有金额的 不展示
        $right = $this->salaryTableEmptyCheck($right);

        //拼装table
        $result_data['salary']['table'] = $this->assemblePdfTable($left, $right);
        //外层 变量下面的总计
        //收入总额
        $result_data['salary']['total_income'] = bcadd(($data['total_income'] + $data['pre_tax_deduct1'] + $data['wedding'] + $data['funeral'] + $data['death'] + $data['after_tax_income1']),
            0, 2);
        //扣款总额
        $result_data['salary']['total_deduction'] = bcadd(($data['total_deduct'] + $data['pre_tax_deduct1']), 0, 2);
        $result_data['salary']['net_income']      = bcadd($data['net_income'], 0, 2);//净收入

        //如果 是by 过来的 还有第三个table  薪资构成 判断是否有transfer date table 展示不一样 (一个两列 一个三列)

        $result_data['transfer_day'] = empty($data['transfer_day']) ? $data['transfer_day'] : date('d/m/Y',
            strtotime($data['transfer_day']));
        $structural                  = [
            [
                'index'      => 'salary_base',
                'name'       => 'salary_base',
                'amount'     => bcadd($data['salary_base'], 0, 2),
                'amount_adj' => bcadd($data['salary_adj'], 0, 2),
            ],
            [
                'index'      => 'performance',
                'name'       => 'performance',
                'amount'     => bcadd($data['performance_base'], 0, 2),
                'amount_adj' => bcadd($data['performance_adj'], 0, 2),
            ],
            [//出勤补贴
                'index'      => 'attendance',
                'name'       => 'attendance',
                'amount'     => bcadd($data['attendance_base'], 0, 2),
                'amount_adj' => bcadd($data['attendance_adj'], 0, 2),
            ],
            [//交通补贴
                'index'      => 'traffic',
                'name'       => 'traffic',
                'amount'     => bcadd($data['traffic_base'], 0, 2),
                'amount_adj' => bcadd($data['traffic_adj'], 0, 2),
            ],
            [
                //话费补贴
                'index'      => 'phone',
                'name'       => 'phone',
                'amount'     => bcadd($data['phone_base'], 0, 2),
                'amount_adj' => bcadd($data['phone_adj'], 0, 2),
            ],
            [
                //餐补
                'index'      => 'food',
                'name'       => 'food',
                'amount'     => bcadd($data['food_base'], 0, 2),
                'amount_adj' => bcadd($data['food_adj'], 0, 2),
            ],
            [
                //笔记本补贴
                'index'      => 'notebook',
                'name'       => 'notebook',
                'amount'     => bcadd($data['notebook_base'], 0, 2),
                'amount_adj' => bcadd($data['notebook_base'], 0, 2),
            ],
            [
                //租房补贴
                'index'      => 'house',
                'name'       => 'house',
                'amount'     => bcadd($data['house_base'], 0, 2),
                'amount_adj' => bcadd($data['house_base'], 0, 2),
            ],
            [
                //星火激励
                'index'      => 'firestarer_award',
                'name'       => 'firestarer_award',
                'amount'     => bcadd($data['firestarer_award_base'], 0, 2),
                'amount_adj' => bcadd($data['firestarer_award_base'], 0, 2),
            ],
        ];

        $structural                       = $this->salaryTableEmptyCheck($structural);
        $result_data['salary_structural'] = $structural;


        return $result_data;
    }


    //获取工资条模板 url
    public function getSalaryViewsUrl(){
        $file_path = APP_PATH . '/modules/Vn/views/salary/salary_pdf_vn_new.ftl';
        // 上传OSS
        $upload_result = OssHelper::uploadFile($file_path,OssService::PDF_SPACE_CERTIFICATE);
        return $upload_result['object_url'];
    }






}
