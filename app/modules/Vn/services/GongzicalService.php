<?php

namespace App\Modules\Vn\Services;

use App\Library\DateHelper;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Services\GongziCalBaseService;

class GongzicalService extends GongziCalBaseService
{

    const TAX_CONFIG = [
        [5000000, 0.05, 0],
        [10000000, 0.1, 250000],
        [18000000, 0.15, 750000],
        [32000000, 0.2, 1650000],
        [52000000, 0.25, 3250000],
        [80000000, 0.3, 5850000],
        [999999999999, 0.35, 9850000],
    ];

    public $partb_tax_data = [];
    public $partc_tax_data = [];

    /**
     * 快递公司ID
     * @var int
     */
    public static $express_company_id = GongziService::COMPANY_ID_EXPRESS;

    public function __construct($yearMonth= null)
    {
        parent::__construct();
        if ($yearMonth >= '2025-03') {
            $this->gongzi_bll = new Gongzi2025Service();
        } elseif (substr($yearMonth,0,4) >= '2023') {
            $this->gongzi_bll = new Gongzi2023Service();
        } else {
            $this->gongzi_bll = new GongziService();
        }
        $this->import_bll = new GongziImportService();
        $this->backyard_bll = new BackyardAttendanceService();
        //保留几位小数
        $this->decimal_places = 2;
    }


    public function main($param)
    {
        try {
            $init_param_ret = $this->init_param($param);
            if ($init_param_ret !== true) {
                return $init_param_ret;
            }
            //[1]初始化发薪公司数据
            $this->init_payroll_company_data();
            //[2]初始化管辖信息
            $this->setOperateId($this->operate_id);
            $this->setPayrollManageInfo($this->payroll_company_data,GongziService::COMPANY_ID_FULLFILLMENT);

            $init_attendance_ret = $this->init_attendance_data();
            if ($init_attendance_ret !== true) {
                return $init_attendance_ret;
            }
            $this->init_other_data();

            $check_paid_ret = $this->check_paid();
            if ($check_paid_ret !== true) {
                return $check_paid_ret;
            }


            //结合考勤数据+other数据 获得出现在工资表的员工数据
            $staff_data_ret = $this->init_staff_data();
            if ($staff_data_ret !== true) {
                return $staff_data_ret;
            }
            $this->init_nation_data();
            $this->init_frontline_data();
            $this->init_salary_data();
            $this->init_transfer_data();
            $this->init_cover_data();

            $this->init_config_base();
            $this->init_config_cal();
            $this->init_no_social_data();
            $this->init_no_tax_data();

            $this->init_company_data();
            $this->init_staff_work_days();
            $totalStaffInfo = $this->processAllStaff();
            $this->fix_some_field($totalStaffInfo);
            $ret = $this->save_db($totalStaffInfo);
            if ($ret) {
                $result = array(
                    'code' => 1,
                    'msg' => 'calculate ' . count($totalStaffInfo) . ' staff success' . $this->check_staff_msg,
                    'data' => [],
                );
            } else {
                $result = array(
                    'code' => 0,
                    'msg' => 'calculate ' . count($totalStaffInfo) . ' staff fail' . $this->check_staff_msg,
                    'data' => [],
                );
            }
            return $result;
        } catch (\Exception $e) {
            $this->getDi()->get('logger')->write_log($e->getMessage() . $e->getTraceAsString(), 'error');
            $result = array(
                'code' => 0,
                'msg' => 'main have exception',
                'data' => [],
            );
            return $result;
        }
    }


    //外部参数初始化
    private function init_param($argv)
    {
        //默认当前月
        if (isset($argv[0])) {
            $this->gongzi_month = trim($argv[0]);
        } else {
            $this->gongzi_month = date("Y-m");
        }

        //$staff_type = 0 全部  1 总部 2 网点
        if (isset($argv[1])) {
            $this->staff_type = $argv[1];
        } else {
            $this->staff_type = 0;
        }

        //$op_type = ''  中国团队  $op_type = 'th'  泰国团队
        if (isset($argv[2])) {
            $this->op_type = $argv[2];
        } else {
            $this->op_type = '';
        }

        if (isset($argv[3])) {
            $this->staff_ids = $argv[3];
        } else {
            $this->staff_ids = [];
        }

        if (empty($argv[4])) {
            return  [
                'code' => 0,
                'msg'  => '操作人工号为空！',
                'data' => [],
            ];
        } else {
            $this->operate_id = $argv[4];
        }

        //0整月 上半个月1 ,下半个月2
        $this->month_type = 0;
        $this->start_date = $this->gongzi_month . '-01';
        $this->end_date = date("Y-m-d", strtotime($this->gongzi_month . '-01 +1 month -1 day'));

        $this->unset_vars = $this->get_unset_config();


        //普通计算,不是计算本周期或上周期,又没有指定员工号,直接退出
        $permit_month = $this->get_permit_month();

        if (in_array($this->gongzi_month, $permit_month)) {
            $this->is_cal_this_month = true;
        } else {
            $this->is_cal_this_month = false;
        }
        if (!in_array($this->gongzi_month, $permit_month) && empty($this->staff_ids)) {
            return array(
                'code' => 0,
                'msg' => '计算历史月份工资必须输入工号',
                'data' => [],
            );
        }
        return true;

    }

    /*
     * 除了返回false,每个字段都需要赋值,比如null,0
     */
    private function init_config_base()
    {
        $this->config_base = [
            [
                'method' => 'get_cal_mode',
                'fields' => 'cal_mode',
            ],
            [
                'method' => 'get_tax_mode',
                'fields' => 'tax_mode',
            ],
            [
                'method' => 'get_frontline_mode',
                'fields' => 'frontline_mode',
            ],
            [
                'method' => 'get_company_id',
                'fields' => 'company_real_id',
            ],
            [
                'method' => 'get_payroll_company_id',
                'fields' => 'company_id',
            ],

            [
                'method' => 'get_staff_data',
                'fields' => [
                    'sys_store_id' => 'sys_store_id',
                    'staff_name' => 'staff_info_name',
                    'job_title_name' => 'job_name',
                    'job_title_id' => 'job_title',
                    'branch_name' => 'store_name',
                    'department_name' => 'organization_name',
                    'department_id' => 'sys_department_id',
                    'bank_type' => 'bank_type',
                    'bank_no' => 'bank_no',
                    'week_day' => 'week_working_day',
                    'nation_type' => 'nation_type',
                    'job_grade' => 'job_grade',
                ],
            ],
            [
                'method' => 'get_salary_data',
                'fields' => [
                    'salary_base' => 'base_salary',
                    'performance_base' => 'performance_allowance',
                    'attendance_base' => 'attendance_allowance',
                    'traffic_base' => 'transport_allowance',
                    'phone_base' => 'phone_subsidy',
                    'food_base' => 'food_allowance',
                    'house_base' => 'house_rental',
                    'notebook_base' => 'notebook_rental',
                    'firestarer_award_base' => 'firestarer_award',
                ],
                'default_value' => 0,
            ],
            [
                'method' => 'get_transfer_data',
                'fields' => ['salary', 'performance', 'attendance', 'traffic', 'phone', 'food'],
                'param' => [],
            ],
            [
                'method' => 'get_attendance_data',
                'fields' => [
                    'lw' => 'LW',
//                    'lw_before' => 'LW_transfer_before',
                    'lw_after' => 'LW_transfer_after',

                    'ab' => 'AB',
//                    'ab_before' => 'AB_transfer_before',
                    'ab_after' => 'AB_transfer_after',

                    'off' => 'OFF',
//                    'off_before' => 'OFF_transfer_before',
                    'off_after' => 'OFF_transfer_after',

//                    'on' => 'On',
                    'covid_on' => 'ISL',
                    'covid_transfer_before_on' => 'ISL_transfer_before',
                    'covid_transfer_after_on' => 'ISL_transfer_after',

                    'night_shift_len' => 'night_shift_minutes',
//                    'late_len' => 'late_times',
                    'ot1_15_len' => 'times1_5',
                    'ot1_2_len' => 'times2_normal',
                    //todo
                    'ot1_21_len' => 'times2_1',
//                    'ot1_21_len' => 'times2_1',
                    'ot2_2_len' => 'times2_off',
                    'ot2_27_len' => 'times2_7',
                    'ot3_3_len' => 'times3',
                    'ot3_39_len' => 'times3_9',
                    'annual_leave_discount_days' => 'annual_leave_discount_days',
                    'food_on_transfer_before' => 'Food_ON_transfer_before',
                    'food_on_transfer_after' => 'Food_ON_transfer_after',
                ],
                'default_value' => 0,
                'param' => [],
            ],

            [
                'method' => 'get_other_data_number',
                'fields' => [],
//                'default_value' => 0,
            ],

            [
                'method' => 'get_other_data_string',
                'fields' => [],
//                'default_value' => '',
            ],
            [
                'method' => 'get_cover_data',
                'fields' => [],
            ],
        ];
    }

    private function init_config_cal()
    {
        $this->config_cal = [
            [
                'method' => 'cal_day_info',
                'fields' => [],
            ],
            [
                'method' => 'cal_fixed_field',
                'fields' => ['salary', 'performance', 'attendance', 'traffic', 'phone'],
            ],
            [
                'method' => 'cal_food',
                'fields' => [],
            ],
            [
                'method' => 'cal_salary_hour',
                'fields' => ['salary_hour'],
            ],

            [
                'method' => 'cal_ot',
                'fields' => [
                    'ot1_15' => 1.5,
                    'ot1_2' => 2,
                    'ot1_21' => 2.1,
                    'ot2_2' => 2,
                    'ot2_27' => 2.7,
                    'ot3_3' => 3,
                    'ot3_39' => 3.9,
                ],
            ],
            [
                'method' => 'cal_night_shift_allowance',
                'fields' => [],
            ],
            [
                'method' => 'cal_ot_notax',
                'fields' => [],
            ],
            [
                //
                'method' => 'cal_covid_salary',
                'fields' => [],
            ],
            [
                'method' => 'cal_converted_paid',
                'fields' => [],
            ],

            [
                'method' => 'cal_total_income',
                'fields' => [],
            ],
            [
                //社保基数
                'method' => 'cal_social_base',
                'fields' => [],
            ],
            [
                //社保
                'method' => 'cal_sss',
                'fields' => [],
            ],

            [
                //公积金
                'method' => 'cal_tax',
                'fields' => [],
            ],

            [
                //公积金
                'method' => 'cal_total_deduct',
                'fields' => [],
            ],
            [
                'method' => 'cal_net_income',
                'fields' => [],
            ],
            [
                'method' => 'cal_negative_data',
                'fields' => [],
            ],

        ];
    }


    private function get_unset_config()
    {
        return ['lw_after', 'ab_after', 'off_after'];
    }

    protected function init_nation_data()
    {
        $staff_ids = array_keys($this->staff_data);
        $t_arr = HrStaffItemsModel::query()->columns(['value', 'staff_info_id'])->inWhere('staff_info_id', $staff_ids)->where('item=\'NATIONALITY\'')->execute()->toArray();
        $nation_info = array_column($t_arr, "value", 'staff_info_id');
        foreach ($this->staff_data as $staff_id => $item) {
            if (isset($nation_info[$staff_id]) && $nation_info[$staff_id] == 5) {
                $this->staff_data[$staff_id]['nation_type'] = 1;
            } else {
                $this->staff_data[$staff_id]['nation_type'] = 0;
            }
        }
    }

    protected function init_salary_data()
    {
        $table = 'hr_staff_salary';
        if ($this->is_cal_this_month || RUNTIME != 'pro' || $this->gongzi_month < '2022-01') {
            $sql = "select * from $table where staff_info_id>0";
        } else {
            $table = 'hr_staff_salary_history';
            $sql = "select * from $table where staff_info_id>0 and month='{$this->gongzi_month}'";
        }
        $result = $this->{$this->read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        $this->salary_data = array_column($result, null, 'staff_info_id');
    }

    protected function init_no_tax_data()
    {
        $gongzi_month = $this->gongzi_month;
        $table_name = $this->get_table_name('salary_special_staff', $this->op_type);
        $sql = "select staff_info_id  from $table_name  where type=" . GongziService::SPECIAL_STAFF_TYPE_NO_TAX . " and excel_month='" . $gongzi_month . "'";
        $result = $this->{$this->read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        $this->no_tax_data = array_column($result, 'staff_info_id');

        $sql = "select staff_info_id  from $table_name  where type=" . GongziService::SPECIAL_STAFF_TYPE_VN_B_TAX . " and excel_month='" . $gongzi_month . "'";
        $result = $this->{$this->read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        $this->partb_tax_data = array_column($result, 'staff_info_id');

        $sql = "select staff_info_id  from $table_name  where type=" . GongziService::SPECIAL_STAFF_TYPE_VN_C_TAX . " and excel_month='" . $gongzi_month . "'";
        $result = $this->{$this->read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        $this->partc_tax_data = array_column($result, 'staff_info_id');
    }

    protected function init_frontline_data()
    {
        $gongzi_month = $this->gongzi_month;
        $table_name = $this->get_table_name('salary_special_staff', $this->op_type);
        $sql = "select staff_info_id  from $table_name  where type=" . self::SPECIAL_STAFF_TYPE_VN_FRONTLINE . " and excel_month='" . $gongzi_month . "'";
        $result = $this->{$this->read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        $this->frontline_data = array_column($result, 'staff_info_id');
    }

    protected function init_company_data()
    {
        $this->company_data = array_column($this->staff_data,'company_real_id','staff_info_id');
    }

    /**
     * todo 逻辑变更时 需要同步修改 GongziService get_staff_id_payroll_company_info 方法
     * 真实公司company_real_id
     * @param $staff_info_id
     * @param $realRowInfo
     * @param $fields
     * @return bool
     */
    protected function get_company_id($staff_info_id, &$realRowInfo, $fields): bool
    {
        $realRowInfo['company_real_id'] = empty($this->company_data[$staff_info_id]) ? GongziService::COMPANY_ID_FULLFILLMENT :$this->company_data[$staff_info_id];
        return true;
    }
    /**
     * 初始化员工发薪公司
     */
    protected function init_payroll_company_data()
    {
        $this->payroll_company_data = $this->gongzi_bll->get_staff_id_payroll_company_info($this->gongzi_month, $this->op_type);
    }
    /**
     * 获取发薪公司
     * 和获取正常公司一样的 都是 找不到默认为FULLFILLMENT
     */
    protected function get_payroll_company_id($staff_info_id, &$realRowInfo, $fields): bool
    {
        if (empty($this->payroll_company_data[$staff_info_id])) {
            $realRowInfo['company_id'] = GongziService::COMPANY_ID_FULLFILLMENT;
        } else {
            $realRowInfo['company_id'] = $this->payroll_company_data[$staff_info_id];
        }
        return true;
    }
    //是否交税,覆盖父类
    protected function get_tax_mode($staff_info_id, &$realRowInfo, $fields)
    {
        if (in_array($staff_info_id, $this->no_tax_data)) {
            $realRowInfo['tax_mode'] = GongziService::VN_TAX_MODE_N;
        } elseif (in_array($staff_info_id, $this->partb_tax_data)) {
            $realRowInfo['tax_mode'] = GongziService::VN_TAX_MODE_B;
        } elseif (in_array($staff_info_id, $this->partc_tax_data)) {
            $realRowInfo['tax_mode'] = GongziService::VN_TAX_MODE_C;
        } else {
            $realRowInfo['tax_mode'] = GongziService::VN_TAX_MODE_A;
        }
        return true;
    }

    protected function get_frontline_mode($staff_info_id, &$realRowInfo, $fields)
    {
        if (in_array($staff_info_id, $this->frontline_data)) {
            $realRowInfo['frontline_mode'] = GongziService::FRONTLINE_MODE_IS;
        } else {
            $realRowInfo['frontline_mode'] = GongziService::FRONTLINE_MODE_ISNOT;
        }
        return true;
    }

    public function get_attendance_data($staff_info_id, &$realRowInfo, $fields)
    {
        $data = $this->attendance_data[$staff_info_id] ?? [];
        if (empty($data)) {
            foreach ($fields as $key => $val) {
                $realRowInfo[$key] = 0;
            }
            $this->get_real_cover_data($realRowInfo,'food_on_transfer_before');
            $this->get_real_cover_data($realRowInfo,'food_on_transfer_after');
            return true;
        }
        foreach ($fields as $key => $val) {
            $realRowInfo[$key] = $data[$val];
        }
        if (empty($realRowInfo['transfer_day'])) {
            $realRowInfo['food_on_transfer_before'] = $data['Food_ON'];
        }
        $this->get_real_cover_data($realRowInfo,'food_on_transfer_before');
        $this->get_real_cover_data($realRowInfo,'food_on_transfer_after');
        return true;
    }


    /*
     * 各个字段获取的方法end
     */

    /*
    * 各个字段需要计算的方法begin
    */
    public function cal_day_info(&$realRowInfo, $fields)
    {
        $realRowInfo['total_month_days'] = 0; //0
        $realRowInfo['should_working_days'] = 0;//1 应出勤=total_month_days-rest_days
        $realRowInfo['not_working_days'] = 0; //2
        $realRowInfo['total_working_days'] = 0;//3
        $realRowInfo['after_transfer_days'] = 0;//4

        //0 days
        $start_day = $realRowInfo['start'];
        $last_working_day = $realRowInfo['last_working_day'];
        $realRowInfo['total_month_days'] = $this->getRealDaysByTwoDay($this->start_date, $this->end_date);

        //1 working_days
        if ($this->gongzi_month >= '2022-02') {
            $leaveDate = $realRowInfo['last_working_day'] == null ? 0 :  date('Y-m-d',strtotime($realRowInfo['last_working_day']) + 3600*24);
            $days =  DateHelper::get_weekend_days($this->start_date,$this->end_date);
            $total_days = $days['total_days'];
            $total_relax =  $days['total_relax'];
            if ($realRowInfo['week_day'] == 6) {
                $beforeDay = [];//入职前的考勤周期周日天数
                $afterDay = [];//离职后的考勤周期周日天数
                $offDayCount = 0; //考勤周期内的应休休息日
                //考勤周期内入职
                if ($realRowInfo['start'] >= $this->start_date && $realRowInfo['start'] <= $this->end_date) {
                    foreach (DateHelper::DateRange(strtotime($this->start_date),strtotime($realRowInfo['start'])) as $date) {
                        if (date('N',strtotime($date)) == 7) {
                            $beforeDay[] = date('W',strtotime($date));
                        }
                    }
                }
                //考勤周期内离职
                if ($leaveDate && $last_working_day && $last_working_day >= $this->start_date && $last_working_day <= $this->end_date) {
                    foreach (DateHelper::DateRange(strtotime($leaveDate),strtotime($this->end_date)) as $date) {
                        if (date('N',strtotime($date)) == 7) {
                            $afterDay[] = date('W',strtotime($date));
                        }
                    }
                }
                if (isset($this->staffOffDayData[$realRowInfo['staff_info_id']]) && is_array($this->staffOffDayData[$realRowInfo['staff_info_id']])) {
                    $staffOffDayByPH = $this->staffOffDayByPH[$realRowInfo['staff_info_id']] ?? [];
                    foreach ($this->staffOffDayData[$realRowInfo['staff_info_id']] as $date) {
                        if ($date < $realRowInfo['start']) {
                            continue;
                        }
                        if ($leaveDate && $last_working_day && $date > $last_working_day) {
                            continue;
                        }
                        //每周不能有两个休息日 (ph与off 重合增加的off除外)
                        if (!in_array($date,$staffOffDayByPH))  {
                            if (in_array(date('W',strtotime($date)),$beforeDay)) {
                                continue;
                            }
                            if (in_array(date('W',strtotime($date)),$afterDay)) {
                                continue;
                            }
                        }
                        if ($date >= $this->start_date &&  $date <= $this->end_date) {
                            $offDayCount += 1;
                        }
                    }
                }
                $offDay = count($beforeDay) + count($afterDay) + $offDayCount;
                $realRowInfo['should_working_days'] =  $total_days - ($offDay > 6 ? 6 : ($offDay < 4 ? 4 : $offDay));
            } else {
                $realRowInfo['should_working_days'] =  (int)($total_days - $total_relax);
            }
        } else {
            if ($realRowInfo['week_day'] == 6) {
                $realRowInfo['should_working_days'] = 26;
            }else {
                $realRowInfo['should_working_days'] = 22;
            }
        }
        //2 not_working_days
        //比开始日晚几天入职
        $not_working_days_a = 0;
        if ($start_day > $this->start_date) {
            $not_working_days_a = $this->getRealDaysByTwoDay($this->start_date, $start_day) - 1;
            if ($not_working_days_a >= $realRowInfo['total_month_days']) {
                $not_working_days_a = $realRowInfo['total_month_days'];
            }
        }
        //比结束日提前几天离职
        $not_working_days_b = 0;
        if (!empty($last_working_day) && $last_working_day < $this->end_date) {
            $not_working_days_b = $this->getRealDaysByTwoDay($last_working_day, $this->end_date) - 1;
            if ($not_working_days_b >= $realRowInfo['total_month_days']) {
                $not_working_days_b = $realRowInfo['total_month_days'];
            }
        }
        $realRowInfo['not_working_days'] = $not_working_days_a + $not_working_days_b;
        $this->get_real_cover_data($realRowInfo, 'not_working_days');

        //3 total_working_days
        $realRowInfo['total_working_days'] = $realRowInfo['total_month_days'] - $realRowInfo['not_working_days'] - $realRowInfo['lw'] - $realRowInfo['ab'] - $realRowInfo['off'] - $realRowInfo['covid_on'];
        $realRowInfo['total_working_days'] = min($realRowInfo['total_working_days'],$realRowInfo['should_working_days']);
        $realRowInfo['total_working_days'] = round($realRowInfo['total_working_days'], 1);
        if ($realRowInfo['total_working_days'] < 0) {
            $realRowInfo['total_working_days'] = 0;
        }

        if ($realRowInfo['week_day'] == 6) {
            if ($realRowInfo['total_working_days'] >= 27) {
                $realRowInfo['total_working_days'] = 27;
            }
        } else {
            if ($realRowInfo['total_working_days'] >= 23) {
                $realRowInfo['total_working_days'] = 23;
            }
        }

        // 4 after_transfer_days
        // 周期内
        if (!empty($last_working_day) && $last_working_day < $this->end_date) {
            $last_day = $last_working_day;
        } else {
            $last_day = $this->end_date;
        }
        if (empty($realRowInfo['transfer_day'])) {
            return true;
        }

        if ($realRowInfo['transfer_day'] <= $this->end_date && $realRowInfo['transfer_day'] >= $this->start_date) {
            $realRowInfo['after_transfer_days'] = $this->getRealDaysByTwoDay($realRowInfo['transfer_day'], $last_day)
                - $realRowInfo['lw_after'] - $realRowInfo['ab_after'] - $realRowInfo['off_after'];
        } elseif ($realRowInfo['transfer_day'] > $this->end_date) {
            $realRowInfo['after_transfer_days'] = 0;
        } else {
            // 周期外 todo
        }
        $this->get_real_cover_data($realRowInfo, 'after_transfer_days');

        return true;
    }

    public function cal_fixed_field(&$realRowInfo, $fields)
    {
        foreach ($fields as $type_name) {
            $realRowInfo[$type_name . '_difference'] = 0;
            $realRowInfo[$type_name] = 0;

            if (!empty($realRowInfo['transfer_day'])) {
                $diffSalary = $realRowInfo[$type_name . '_adj'] - $realRowInfo[$type_name . '_base'];
                $realRowInfo[$type_name . '_difference'] = $this->cal_model_1($diffSalary, $realRowInfo['should_working_days'], $realRowInfo['after_transfer_days']);
                $realRowInfo[$type_name] = $this->cal_model_1($realRowInfo[$type_name . '_base'], $realRowInfo['should_working_days'], $realRowInfo['total_working_days']) + $realRowInfo[$type_name . '_difference'];
            } else {
                $realRowInfo[$type_name] = $this->cal_model_1($realRowInfo[$type_name . '_base'], $realRowInfo['should_working_days'], $realRowInfo['total_working_days']);
            }
            $realRowInfo[$type_name] = round($realRowInfo[$type_name],2);
        }

        //如果有覆盖的,使用覆盖值
//        foreach ($fields as $type_name) {
//            $this->get_real_cover_data($realRowInfo, $type_name);
//        }
        $this->get_real_cover_data($realRowInfo, 'attendance');
        return true;
    }


    /*
     * 日薪 除以 12or8 ,  如果有transfer 按照transfer 后的算
     */
    public function cal_salary_hour(&$realRowInfo, $fields)
    {
        //一线员工判断
        $isFirstClassStaff = $realRowInfo['frontline_mode'] == GongziService::FRONTLINE_MODE_IS;
        if ($isFirstClassStaff) {
            if (empty($realRowInfo['transfer_day'])) {
                $realRowInfo['salary_hour'] = round($realRowInfo['salary_base'] / ($realRowInfo['should_working_days'] * 8),2);
            } else {
                $realRowInfo['salary_hour'] = round($realRowInfo['salary_adj'] / ($realRowInfo['should_working_days'] * 8),2);
            }
        } else {
            if (empty($realRowInfo['transfer_day'])) {
                $realRowInfo['salary_hour'] = round(($realRowInfo['salary_base'] + $realRowInfo['performance_base'] + $realRowInfo['attendance_base'] + $realRowInfo['traffic_base'] + $realRowInfo['phone_base'] + $realRowInfo['food_base']) / ($realRowInfo['should_working_days'] * 8),2);
            } else {
                $realRowInfo['salary_hour'] = round(($realRowInfo['salary_adj'] + $realRowInfo['performance_adj'] + $realRowInfo['attendance_adj'] + $realRowInfo['traffic_adj'] + $realRowInfo['phone_adj'] + $realRowInfo['food_adj']) / ($realRowInfo['should_working_days'] * 8),2);
            }
        }
        return true;
    }

    /*
     * 计算无需纳税的加班费
     * //todo 1.  21有没有，2.  时间单位是不是小时
     */
    public function cal_ot_notax(&$realRowInfo, $fields)
    {
        $realRowInfo['ot_notax'] = round($realRowInfo['ot'] - $realRowInfo['salary_hour'] * ($realRowInfo['ot1_15_len'] + $realRowInfo['ot1_2_len'] + +$realRowInfo['ot1_21_len'] + $realRowInfo['ot2_2_len'] + $realRowInfo['ot2_27_len'] + $realRowInfo['ot3_3_len'] + $realRowInfo['ot3_39_len']),2);
    }

    public function cal_night_shift_allowance(&$realRowInfo, $fields)
    {
        if ($this->gongzi_month < '2025-07') {
            $realRowInfo['night_shift_len'] =  null;
            $realRowInfo['night_shift_allowance'] =  null;
            return;
        }
        $realRowInfo['night_shift_allowance'] = round($realRowInfo['salary_hour'] * $realRowInfo['night_shift_len'] * 0.3);
    }
    /*
     * covid隔离假工资,日工资的一半
     */
    public function cal_covid_salary(&$realRowInfo, $fields)
    {
        if (empty($realRowInfo['transfer_day'])) {
            $realRowInfo['covid_salary'] = round(($realRowInfo['salary_base'] + $realRowInfo['performance_base'] + $realRowInfo['attendance_base'] + $realRowInfo['traffic_base'] + $realRowInfo['phone_base'] + $realRowInfo['food_base']) * $realRowInfo['covid_on'] * 0.5 / ($realRowInfo['should_working_days']));
        } else {
            $realRowInfo['covid_salary'] = round((
                    ($realRowInfo['salary_base'] + $realRowInfo['performance_base'] + $realRowInfo['attendance_base'] + $realRowInfo['traffic_base'] + $realRowInfo['phone_base'] + $realRowInfo['food_base']) * $realRowInfo['covid_transfer_before_on'] * 0.5 +
                    ($realRowInfo['salary_adj'] + $realRowInfo['performance_adj'] + $realRowInfo['attendance_adj'] + $realRowInfo['traffic_adj'] + $realRowInfo['phone_adj'] + $realRowInfo['food_adj']) * $realRowInfo['covid_transfer_after_on'] * 0.5
                ) / ($realRowInfo['should_working_days']));
        }
    }


    /*
     * 税前工资
     */
    public function cal_total_income(&$realRowInfo, $fields)
    {
        $realRowInfo['total_income'] =
            $realRowInfo['salary'] + $realRowInfo['performance'] + $realRowInfo['attendance'] + $realRowInfo['traffic'] +
            $realRowInfo['phone'] + $realRowInfo['food'] +
            $realRowInfo['ot'] + $realRowInfo['notebook'] + $realRowInfo['house'] +
            $realRowInfo['bonus'] + $realRowInfo['recommended']  + $realRowInfo['compensation'] + $realRowInfo['incentive'] + $realRowInfo['other_income1'] + $realRowInfo['other_income2'] + $realRowInfo['covid_salary']
            - $realRowInfo['pre_tax_deduct1'] + $realRowInfo['firestarer_award'] + $realRowInfo['night_shift_allowance'] ;
        $realRowInfo['total_income'] = round($realRowInfo['total_income'],2);
        if ($realRowInfo['total_income'] < 0) {
            $realRowInfo['total_income'] = 0;
        }
        return true;
    }


    //扣款开始


    //社保基数
    public function cal_social_base(&$realRowInfo, $fields)
    {
        if ($realRowInfo['cal_mode'] == GongziService::CAL_MODE_NO_SHEBAO) {
            $realRowInfo['social_base'] = 0;
        } else {
            if (!empty($realRowInfo['transfer_day'])) {
                $realRowInfo['social_base'] = $realRowInfo['salary_adj'];
            } else {
                $realRowInfo['social_base'] = $realRowInfo['salary_base'];
            }
        }
        $this->get_real_cover_data($realRowInfo, 'social_base');
        return true;
    }

    //社保
    public function cal_sss(&$realRowInfo, $fields)
    {
        $realRowInfo['sss_company'] = 0;
        $realRowInfo['sss_staff'] = 0;
        $realRowInfo['sss'] = 0;
        if ($realRowInfo['cal_mode'] == GongziService::CAL_MODE_NO_SHEBAO) {
            return true;
        }
        $social_base = $realRowInfo['social_base'];
        //nation_type == 1 越南籍
        if ($realRowInfo['nation_type'] == 1) {
            $realRowInfo['sss_company'] = round($social_base * 0.21,2);
            $realRowInfo['sss_staff'] = round($social_base * 0.105,2);
        } else {
            $realRowInfo['sss_company'] = round($social_base * 0.20,2);
            $realRowInfo['sss_staff'] = round($social_base * 0.095,2);
        }
        if ($this->gongzi_month >= $this->gongzi_bll->month_logic_v1) {
            if ($realRowInfo['nation_type'] == 1) {
                $realRowInfo['sss_company'] = round($social_base * 0.215,2);
            } else {
                $realRowInfo['sss_company'] = round($social_base * 0.205,2);
            }
        }
        $realRowInfo['sss'] = $realRowInfo['sss_company'] + $realRowInfo['sss_staff'];
        $this->get_real_cover_data($realRowInfo, 'sss_company');
        $this->get_real_cover_data($realRowInfo, 'sss_staff');
        $this->get_real_cover_data($realRowInfo, 'sss');
        return true;
    }

    public function cal_tax(&$realRowInfo, $fields)
    {
        $realRowInfo['special_tax_deduct'] = 0;
        $realRowInfo['tax_base'] = 0;
        $realRowInfo['tax'] = 0;

        if ($realRowInfo['tax_mode'] == GongziService::VN_TAX_MODE_N) {
            return true;
        }
        //专项附加扣除额
        if ($realRowInfo['tax_mode'] == GongziService::VN_TAX_MODE_A) {
            $realRowInfo['special_tax_deduct'] = $realRowInfo['dependants_num'] * 4400000 + 11000000;
        }
        //应纳税额
        if ($realRowInfo['tax_mode'] == GongziService::VN_TAX_MODE_A) {
            $realRowInfo['tax_base'] = $realRowInfo['total_income'] - $realRowInfo['ot_notax'] - min($realRowInfo['food'], 730000) - $realRowInfo['sss_staff'] - $realRowInfo['special_tax_deduct'] - $realRowInfo['phone'] - $realRowInfo['night_shift_allowance'];
        } else {
            $realRowInfo['tax_base'] = $realRowInfo['total_income'];
        }

        $realRowInfo['tax_base'] = round($realRowInfo['tax_base'],2);
        if ($realRowInfo['tax_base'] < 0) {
            $realRowInfo['tax_base'] = 0;
        }
        //tax
        if ($realRowInfo['tax_mode'] == GongziService::VN_TAX_MODE_B) {
            $realRowInfo['tax'] = round($realRowInfo['total_income'] < 2000000 ? 0 : $realRowInfo['tax_base'] * 0.1);
        } elseif ($realRowInfo['tax_mode'] == GongziService::VN_TAX_MODE_C) {
            $realRowInfo['tax'] = round($realRowInfo['total_income'] < 2000000 ? 0 : $realRowInfo['tax_base'] * 0.2);
        } elseif ($realRowInfo['tax_mode'] == GongziService::VN_TAX_MODE_A) {
            $tax_base = $realRowInfo['tax_base'];
            foreach (self::TAX_CONFIG as $taxInfo) {
                if ($tax_base <= $taxInfo[0]) {
                    $realRowInfo['tax'] = round($tax_base * $taxInfo[1] - $taxInfo[2]);
                    break;
                }
            }
        } else {
            return true;
        }
        $this->get_real_cover_data($realRowInfo, 'tax');
        return true;
    }

    public function cal_total_deduct(&$realRowInfo, $fields)
    {
        $realRowInfo['total_deduct'] =
            $realRowInfo['tax'] + $realRowInfo['sss_staff'] + $realRowInfo['equipment']
            + $realRowInfo['uniform'] + $realRowInfo['incentive_paid'] + $realRowInfo['other_deduct1'] + $realRowInfo['other_deduct2'] ;
        $realRowInfo['total_deduct'] = round($realRowInfo['total_deduct'],2);
        return true;
    }

    /*
     * net amount
     * 公用方法不能用
     */
    public function cal_net_income(&$realRowInfo, $fields)
    {
        $realRowInfo['net_income'] = $realRowInfo['total_income'] - $realRowInfo['total_deduct'] + $realRowInfo['wedding'] + $realRowInfo['funeral'] + $realRowInfo['death'] + $realRowInfo['after_tax_income1'];
        $realRowInfo['net_income'] = $realRowInfo['net_income'] <= 0 ? 0 : round($realRowInfo['net_income']);
        return true;
    }

    //negative data
    //total_income - total_deduct 的实际值
    public function cal_negative_data(&$realRowInfo, $fields)
    {
        $realRowInfo['negative_data'] = $realRowInfo['total_income'] - $realRowInfo['total_deduct'] + $realRowInfo['wedding'] + $realRowInfo['funeral'] + $realRowInfo['death'] + $realRowInfo['after_tax_income1'];
        $realRowInfo['negative_data'] = round($realRowInfo['negative_data']);
        return true;
    }

    /*
     * 根据当前日期,计算出允许计算的excel_month
     * 2021-06-08==> 2021-05,2021-06
     * 2021-06-20==> 2021-06
     */
    private function get_permit_month()
    {
        $today = gmdate('Y-m-d', time() + ($this->timeOffset) * 3600);
        $this_month = substr($today, 0, 7);
        $last_month = date('Y-m', strtotime($this_month . '-01' . '-1 month'));

        if (date('d', strtotime($today)) > '15') {
            return [$this_month];
        } else {
            return [$this_month, $last_month];
        }
    }

    public function cal_converted_paid(&$realRowInfo)
    {
        if ($this->gongzi_month >= $this->gongzi_bll->month_logic_v1) {
            $realRowInfo['converted_paid'] = null;
            $realRowInfo['annual_leave_discount_days'] = null;
            return;
        }
        $realRowInfo['converted_paid'] = 0;
        if ($realRowInfo['should_working_days']) {
            if ($realRowInfo['transfer_day']) {
                $realRowInfo['converted_paid'] = (
                        $realRowInfo['salary_adj'] + $realRowInfo['performance_adj']
                        + $realRowInfo['attendance_adj'] + $realRowInfo['traffic_adj'] + $realRowInfo['phone_adj'] + $realRowInfo['food_adj']
                    )/$realRowInfo['should_working_days'] * $realRowInfo['annual_leave_discount_days'];
            } else {
                $realRowInfo['converted_paid'] = (
                        $realRowInfo['salary_base'] + $realRowInfo['performance_base']
                        + $realRowInfo['attendance_base'] + $realRowInfo['traffic_base'] + $realRowInfo['phone_base'] + $realRowInfo['food_base']
                    )/$realRowInfo['should_working_days'] * $realRowInfo['annual_leave_discount_days'];
            }

            $realRowInfo['converted_paid'] = round($realRowInfo['converted_paid'] ,2);
        }
    }
    /**
     * 获取当前考勤周期内六天班员工轮休日
     */
    protected function init_staff_work_days()
    {
        $ids = [];
        //当前是六天班
        foreach ($this->staff_data as $staffInfoId => $item) {
            if ($item['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_SIX) {
                $ids[] = $staffInfoId;
            }
        }
        if ($ids) {
            $Holiday = (new HolidayService())->getHoliday(['date'=>$this->start_date]);
            $phDay = $Holiday[HrStaffInfoModel::WEEK_WORKING_DAY_SIX]; //六天班员工PH

            //六天班休息日
            $hr_staff_work_days = \App\Models\backyard\HrStaffWorkDaysModel::class;
            $leave_sql = "select staff_info_id,date_at,src_date from {$hr_staff_work_days} where  date_at >= :start_date: and date_at <= :end_date: and staff_info_id in ({ids:array})";
            $where_params['start_date'] = $this->start_date;
            $where_params['end_date'] = $this->end_date;
            $where_params['ids'] = $ids;
            $hrStaffWorkDays = $this->modelsManager->executeQuery($leave_sql, $where_params)->toArray();
            foreach (static::yieldData()($hrStaffWorkDays) as $item) {
                //因ph 和off在同一天 补充的off
                if ($item['src_date']) {
                    $this->staffOffDayByPH[$item['staff_info_id']][] = $item['date_at'];
                }
                //过滤考勤周期内 六天班 当天是PH
                if (in_array($item['date_at'],$phDay)) {
                    continue;
                }
                $this->staffOffDayData[$item['staff_info_id']][] = $item['date_at'];
            }
        }
    }
}
