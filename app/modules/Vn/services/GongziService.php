<?php
/*
 * 除了计算逻辑,上传逻辑其他的业务逻辑,相当于gongzi_other_bll 菲律宾独有的写在这里
 */

namespace App\Modules\Vn\Services;

use App\Library\BaseService;
use App\Library\Enums\SalaryEnums;
use App\Models\backyard\AttendanceDataMonthModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Services\GongziBaseService;
use App\Services\SettingEnvService;


class GongziService extends GongziBaseService
{
    const COMPANY_ID_FULLFILLMENT = 20001;
    //越南竟然是直接用的 真的公司ID来保存的 ，那么延续原来的逻辑
    const COMPANY_F_COMMERCE = 20000;
    const COMPANY_ID_EXPRESS = 20025;

    const COMPANY_TYPE_NAME_MAP = [
        self::COMPANY_ID_EXPRESS => 'Flash Express',
        self::COMPANY_ID_FULLFILLMENT => 'Flash FulFillment',
        self::COMPANY_F_COMMERCE => 'F commerce',
    ];

    const FRONTLINE_MODE_IS = 1;//是一线员工
    const FRONTLINE_MODE_ISNOT = 0;//不是一线员工
    const FRONTLINE_NAME_MAP = [
        self::FRONTLINE_MODE_IS =>'Y',
        self::FRONTLINE_MODE_ISNOT=>'N',
    ];

    const VN_TAX_MODE_A = 0; //正常交 A
    const VN_TAX_MODE_N = 1;//不交税 N
    const VN_TAX_MODE_B = 2;//类型B
    const VN_TAX_MODE_C = 3;//类型C
    const TAX_MODE_NAME_ARR = [
        self::VN_TAX_MODE_A =>'A',
        self::VN_TAX_MODE_N=>'N',
        self::VN_TAX_MODE_B=>'B',
        self::VN_TAX_MODE_C=>'C',
    ];

    const NATION_TYPE_VN = 1;
    const NATION_TYPE_NOT_VN = 0;
    const NATION_TYPE_NAME_MAP = [
        self::NATION_TYPE_VN => 'VN',
        self::NATION_TYPE_NOT_VN => 'NOT VN',
    ];

    public static $bank_type_name = [
        '0' => 'Not Known',
        '2' => 'HSBC',
    ];

    public static $specialCompanyStaffCodes = ['special_flashexpress_staff','special_fulfillment_staff','special_fcommerce_staff'];
    //导出excel 和 db字段的对应关系
    //默认是 db字段, 还可能是空, 还可能是常数
    //如果db字段是字符串 如name,不是数字,一定要在下面string类型里建好
    //注意 staff_info_id 必须是在第二列,不能变, 后面发工资条有用到
    public $excelShowMap = [
        //员工
//        'No' => '',//外部加序号
        'Emp.' => 'staff_info_id',
        'Name' => 'staff_name',
        'Corporate Name' => 'company_real_id', //真实
        'Payroll Company name' => 'company_id', //发薪公司
//        'Store Id'=>'sys_store_id',
//        'branch type'=>'sys_store_id',//sys_store_id通过这个获取网点类型
        'Branch' => 'branch_name',
        'Department' => 'department_name',
        'Position' => 'job_title_name',
        'Rank'=>'job_grade',
//        'Disablity Status'=>'disability_status',
        'Country of Citizenship'=>'nation_type',
        'Tax Type' => 'tax_mode',
        'Social Status' => 'cal_mode',
        'First-line positions'=>'frontline_mode',
        'job type' => 'week_day',

//       'store id'=>'sys_store_id'
        'Start Date' => 'start',
        'Last working day' => 'last_working_day',
        'working days' => 'should_working_days',
//        'Rest days' => 'rest_days',

        'Days' => 'total_month_days',

        'LW' => 'lw',
        'AB' => 'ab',
        'Isolation leave'=>'covid_on',
        'OFF' => 'off',
//        'pl' => 'PL Trial(-)',//2020年不用了
//        'Other UL(-)'=>0,//new 本期不做,直接为0
        'Days not in employment' => 'not_working_days',
        'Total working days' => 'total_working_days',
        'Transfer day' => 'transfer_day',
        'After transfer days' => 'after_transfer_days',
        'After Isolation leave'=>'covid_transfer_after_on',

        'after transfer job type' => 'week_day_adj',
        //固定部分
        'Base Salary' => 'salary_base',//check
        'Base Salary Adj.' => 'salary_adj',
        'Salary Difference' => 'salary_difference',
        'Salary' => 'salary',

        'Performance Allowance Base' => 'performance_base',
        'Performance Allowance Base Adj.' => 'performance_adj',
        'Performance Allowance Difference' => 'performance_difference',
        'Performance Allowance' => 'performance',

        'Attendance Allowance Base' => 'attendance_base',
        'Attendance Allowance Base Adj.' => 'attendance_adj',
        'Attendance Allowance Difference' => 'attendance_difference',
        'Attendance Allowance' => 'attendance',

        'Traffic Rental Base' => 'traffic_base',
        'Traffic Rental Base Adj.' => 'traffic_adj',
        'Traffic Rental Difference' => 'traffic_difference',
        'Traffic Rental' => 'traffic',

        'Telephone Rental Base' => 'phone_base',
        'Telephone Rental Base Adj.' => 'phone_adj',
        'Telephone Rental Difference' => 'phone_difference',
        'Telephone Rental' => 'phone',

        'Food Rental Base' => 'food_base',
        'Food Rental Base Adj.' => 'food_adj',
        'Food Rental Difference' => 'food_difference',
        'Food Rental' => 'food',

        'Rate per Hour' => 'salary_hour',

        'Working 1.5 times' => 'ot1_15_len',
        'OT 1.5 times' => 'ot1_15',
        'Working 2.0 times' => 'ot1_2_len',
        'OT 2.0 times' => 'ot1_2',
        'Working 2.1 times' => 'ot1_21_len',
        'OT 2.1 times' => 'ot1_21',

        'Weekend 2 time' => 'ot2_2_len',
        'OT 2 time' => 'ot2_2',
        'Weekend 2.7 time' => 'ot2_27_len',
        'OT 2.7 time' => 'ot2_27',

        'Holiday 3 time' => 'ot3_3_len',
        'OT 3 time' => 'ot3_3',
        'Holiday 3.9 time' => 'ot3_39_len',
        'OT 3.9 time' => 'ot3_39',
        'OT' => 'ot',
        'Annual leave discount days' => 'annual_leave_discount_days',
        'Converted Paid Leaves' => 'converted_paid',
        'OT exempt'=>'ot_notax',

        'Covid Leave Salary'=>'covid_salary',

        'Notebook Rental Base' => 'notebook_base',
        'Notebook Rental' => 'notebook',
        'House rental Base' => 'house_base',
        'House rental' => 'house',
        'Firestarter Award Base' => 'firestarer_award_base',
        'Firestarter Award' => 'firestarer_award',

        'Bonus' => 'bonus',

        'Recommendation' => 'recommended',
        'Compensation' => 'compensation',
        'Incentive' => 'incentive',
        'other income1' => 'other_income1',//fixed/social new
        'other income2' => 'other_income2',//fixed/social new
        'Performance allowance deduct' => 'pre_tax_deduct1',//fixed new

        'total income' => 'total_income',

        'Social base' => 'social_base',
        'sss ER Company' => 'sss_company',
        'sss EE Staff' => 'sss_staff',
//        'sss total' => 'sss',
        'No of dependants'=>'dependants_num',
        'Special additional deduction'=>'special_tax_deduct',
        'Taxable Pay' => 'tax_base',
        'Tax' => 'tax',

        'Equipment' => 'equipment',
        'Uniform' => 'uniform',
        'Incentive(paid)' => 'incentive_paid',
        'other deduct1' => 'other_deduct1',
        'other deduct2' => 'other_deduct2',
        'Total Deduct' => 'total_deduct',

        'Wedding gold'=>'wedding',
        'Funeral money'=>'funeral',
        'Employee death grant'=>'death',
        'After tax income1'=>'after_tax_income1',

        'Net Amount' => 'net_income',
        'negative data' => 'negative_data',
        'Remark' => 'remark',
        'Remark1' => 'remark1',//new
        'Remark2' => 'remark2',//new
        'Team' => 'team',
        'Bank name' => 'bank_type',
        'Bank account no.' => 'bank_no',
        'status' => 'status',
        'update time' => 'updated_at',
    ];

    /**
     * 字符串类型字段
     * @var string[]
     */
    public static $str_type_column = ['staff_name', 'job_title_name', 'branch_name', 'sys_store_id', 'department_name', 'start', 'last_working_day', 'transfer_day', 'remark', 'remark1', 'remark2', 'team', 'bank_no', 'updated_at'];

    /**
     * decimal类型字段
     * @var string[]
     */
    public static $float_type_column = ['annual_leave_discount_days','not_working_days', 'total_working_days', 'total_month_days', 'after_transfer_days', 'should_working_days', 'ot1_15_len', 'ot1_2_len', 'ot1_21_len','ot2_2_len', 'ot2_27_len', 'ot3_3_len','ot3_39_len', 'ab', 'lw', 'off','covid_on','covid_transfer_before_on','covid_transfer_after_on', 'incentive_paid', 'incentive',
    'salary','performance','attendance','traffic','phone','food','salary_hour','ot1_15','ot1_2','ot1_21','ot2_2','ot2_27','ot3_3','ot3_39','ot','ot_notax','notebook','house','bonus','recommended','converted_paid',
        'compensation','incentive','other_income1','other_income2','pre_tax_deduct1','total_income','sss_company','sss_staff','tax_base','tax','equipment','uniform','incentive_paid','other_deduct1','other_deduct2',
    'total_deduct','wedding','funeral','death','after_tax_income1','firestarer_award','social_insurance_er','health_insurance_er','unemployment_insurance_er',
        'social_insurance_ee','health_insurance_ee','unemployment_insurance_ee','food_on_transfer_before','food_on_transfer_after'];

    public static $map_type_column = ['bank_type', 'cal_mode', 'tax_mode','frontline_mode','nation_type', 'status','company_real_id','company_id'];
    /**
     * 部分逻辑变成开始月份
     * @var string
     */
    public $month_logic_v1 = '2022-08';

    /**
     * 组装查询sql
     * @param $company_id_map
     * @return string
     */
    private function mkSelectSql($company_id_map): string
    {

        $mkCaseSql = function ($field, $config) {
            $sql = " case {$field} ";
            foreach ($config as $value => $show_value) {
                $sql .= " when '{$value}' then  '{$show_value}' ";
            }
            $sql .= " else '' end as {$field} ";
            return $sql;
        };

        $select_sql = '';
        $fields = array_values($this->excelShowMap);
        foreach ($fields as $field) {
            if (in_array($field, self::$map_type_column)) {
                switch ($field) {
                    case 'bank_type':
                        $config = self::$bank_type_name;
                        break;
                    case 'cal_mode':
                        $config = parent::CAL_MODE_NAME_ARR;
                        break;
                    case 'tax_mode':
                        $config = self::TAX_MODE_NAME_ARR;
                        break;
                    case 'frontline_mode':
                        $config = self::FRONTLINE_NAME_MAP;
                        break;
                    case 'nation_type':
                        $config = self::NATION_TYPE_NAME_MAP;
                        break;
                    case  'status':
                        $config = parent::STATUS_NAME_MAP;
                        break;
                    case 'company_real_id':
                        $config = $company_id_map;
                        break;
                    case 'company_id':
                        $config = self::COMPANY_TYPE_NAME_MAP;
                        break;
                }
                $field_sql = $mkCaseSql($field, $config);
            } else {
                $field_sql = " {$field} ";
            }
            $select_sql .= $field_sql . ',';
        }
        return rtrim($select_sql, ',');
    }


    /*
     * $staff_type: 1总部, 2 网点 ,
     * $sql_filter : 工资表查询where条件。  第一个and 不写, 多个条件的话,中间的and 要写
     *
     */
    public function getGongziDataForExport($gongzi_month, $staff_type, $op_type = '', $export_type, $staff_id_arr = [], $sql_filter = '', $company_id = null)
    {
        try {
            $table_map = SalaryEnums::GONGZI_TABLE_TYPE_MAP;
            if (!isset($table_map[$export_type])) {
                return [];
            } else {
                $table_base_name = $table_map[$export_type];
            }

            $table_name = $this->get_table_name($table_base_name, $op_type);

            $sql_in = '';
            if (!empty($staff_id_arr)) {
                $staff_id_str = implode(',', $staff_id_arr);
                $sql_in = ' and staff_info_id in (' . $staff_id_str . ') ';
            }

            if (!empty($company_id)) {
                $sql_in .= ' and company_id = ' . $company_id;
            }

            if ($this->not_manage_staffs) {
                $sql_in .= ' and staff_info_id NOT IN (' . implode(',', $this->not_manage_staffs) . ') ';
            }

            if ($this->only_manage_staffs) {
                $sql_in .= ' and staff_info_id IN (' . implode(',', $this->only_manage_staffs) . ') ';
            }
            $company_id_map = (new GongziService())->get_company_id_map();

            if ($staff_type == 1) {
                $sql_where = ' and sys_store_id = "-1" ' . $sql_in;
            } elseif ($staff_type == 2) {
                $sql_where = ' and sys_store_id != "-1" ' . $sql_in;
            } elseif ($staff_type == 0) {
                $sql_where = $sql_in;
            } else {
                return [];
            }

            $sql_condition = '';
            if (!empty($sql_filter)) {
                $sql_condition = ' and ' . $sql_filter . ' ';
            }

            $select_sql = $this->mkSelectSql($company_id_map);
            $sql = "select $select_sql from $table_name where excel_month='" . $gongzi_month . "' " . $sql_where . $sql_condition;
            $data = $this->{$this->read_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
            $new_data = [];
            foreach ($data as $item) {
                $fmt_item = [];
                foreach ($item as $key => $value) {
                    if (in_array($key, self::$str_type_column) || in_array($key, self::$map_type_column) || (in_array($key,self::$null_float_map) && is_null($value))) {
                        $fmt_value = $value;
                    } elseif (in_array($key, self::$float_type_column)) {
                        $fmt_value = floatval($value);
                    } else {
                        $fmt_value = intval($value);
                    }
                    $fmt_item[] = $fmt_value;
                }
                $new_data[] = $fmt_item;
            }
            return $new_data;
        } catch (\Exception $e) {
            $this->getDi()->get('logger')->write_log('getGongziDataForExport have exception' . $e->getMessage() . $e->getTraceAsString(), 'error');
            return [];
        }

    }

    //获取员工对应的公司id begin
    public function get_staff_id_company_info($gongzi_month, $op_type = '')
    {
        $id_company_map = [];
        //看这里并没有去真实的获取公司 是因为 目前越南都是fulfillment。
        $fullfillment_staffs = $this->get_fulfillment_staff($gongzi_month, $op_type);
        $this->set_company_info($fullfillment_staffs, self::COMPANY_ID_FULLFILLMENT, $id_company_map);

        return $id_company_map;
    }

    //获取员工对应的公司id end

    /*
     * 根据当前日期,计算上一个计算周期,用于取上个月的excel模板
     * 老挝算1-30, 8号发工资, 那大概率3号下载上个月的话,应该是上上个月的数据; 可以改成从数据库查最小值
     */
    public function get_last_month()
    {
        $today = gmdate('Y-m-d', time() + ($this->timeOffset) * 3600);
        $this_month = substr($today, 0, 7);
        $last_month = date('Y-m', strtotime($this_month . '-01' . '-1 month'));
        $last_2month = date('Y-m', strtotime($this_month . '-01' . '-2 month'));

        if (date('d', strtotime($today)) > '8') {
            return $last_month;
        } else {
            return $last_2month;
        }
    }

    /**
     * todo 逻辑变更时 需要同步修改 GongzicalService get_company_id 方法
     * 获取员工对应发薪的公司
     * @param $month
     * @param $op_type
     * @return array
     */
    public function get_staff_id_payroll_company_info($month ,$op_type): array
    {

        $id_company_map = [];
        //Flash Express
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
        $builder->innerJoin(SysDepartmentModel::class, 'hsi.node_department_id = ssd.id', 'ssd');
        $builder->where('ssd.company_id = :company_id:',['company_id'=>self::COMPANY_ID_EXPRESS]);
        $builder->andWhere('adm.stat_period = :query_month: ', ['query_month' => $month]);
        $attendance_data = $builder->getQuery()->execute()->toArray();
        $expressStaff = array_column($attendance_data,'staff_info_id');
        $this->set_company_info($expressStaff, self::COMPANY_ID_EXPRESS, $id_company_map);
        //F Commerce
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
        $builder->innerJoin(SysDepartmentModel::class, 'hsi.node_department_id = ssd.id', 'ssd');
        $builder->where('ssd.company_id = :company_id:',['company_id'=>self::COMPANY_F_COMMERCE]);
        $builder->andWhere('adm.stat_period = :query_month: ', ['query_month' => $month]);
        $attendance_data = $builder->getQuery()->execute()->toArray();
        $commerceStaff = array_column($attendance_data,'staff_info_id');
        $this->set_company_info($commerceStaff, self::COMPANY_F_COMMERCE, $id_company_map);

        $settingEnv = (new SettingEnvService());
        //这个fulfillment 是上传的，优先级最低
        $fullfillment_staffs = $this->get_fulfillment_staff($month, $op_type);
        $this->set_company_info($fullfillment_staffs, self::COMPANY_ID_FULLFILLMENT, $id_company_map);

        $commerce_staffs = $settingEnv->getSetVal('special_fcommerce_staff');
        if ($commerce_staffs) {
            $this->set_company_info(explode(',',$commerce_staffs), self::COMPANY_F_COMMERCE, $id_company_map);
        }
        $flash_staffs = $settingEnv->getSetVal('special_flashexpress_staff');
        if ($flash_staffs) {
            $this->set_company_info(explode(',',$flash_staffs), self::COMPANY_ID_EXPRESS, $id_company_map);
        }
        $fulfillment_staffs = $settingEnv->getSetVal('special_fulfillment_staff');
        if ($fulfillment_staffs) {
            $this->set_company_info(explode(',',$fulfillment_staffs), self::COMPANY_ID_FULLFILLMENT, $id_company_map);
        }
        return $id_company_map;
    }

    public function cal_food(&$realRowInfo, $fields): bool
    {
        $realRowInfo['food_difference'] = 0;
        $realRowInfo['food']            = 0;
        if ($realRowInfo['excel_month'] < '2025-03' || $realRowInfo['frontline_mode'] == self::FRONTLINE_MODE_ISNOT) {
            if (!empty($realRowInfo['transfer_day'])) {
                $diffSalary                     = $realRowInfo['food_adj'] - $realRowInfo['food_base'];
                $realRowInfo['food_difference'] = $this->cal_model_1($diffSalary, $realRowInfo['should_working_days'],
                    $realRowInfo['after_transfer_days']);
                $realRowInfo['food']            = $this->cal_model_1($realRowInfo['food_base'],
                        $realRowInfo['should_working_days'],
                        $realRowInfo['total_working_days']) + $realRowInfo['food_difference'];
            } else {
                $realRowInfo['food'] = $this->cal_model_1($realRowInfo['food_base'],
                    $realRowInfo['should_working_days'], $realRowInfo['total_working_days']);
            }
            $realRowInfo['food'] = round($realRowInfo['food'], 2);
            return true;
        }
        if (!empty($realRowInfo['transfer_day'])) {
            $realRowInfo['food']            = $realRowInfo['food_base'] * $realRowInfo['food_on_transfer_before'] + $realRowInfo['food_adj'] * $realRowInfo['food_on_transfer_after'];
        } else {
            $realRowInfo['food'] = $realRowInfo['food_base'] * $realRowInfo['food_on_transfer_before'];
        }
        $realRowInfo['food_difference'] = 0;
        $realRowInfo['food'] = round($realRowInfo['food'], 2);
        $this->get_real_cover_data($realRowInfo, 'food');
        return true;
    }

}
