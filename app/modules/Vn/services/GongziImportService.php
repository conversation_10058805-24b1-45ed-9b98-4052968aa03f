<?php
namespace App\Modules\Vn\Services;

use App\Services\GongziImportBaseService;
use App\Services\HrJobTitleService;

class GongziImportService extends GongziImportBaseService
{
    /*
     * 增加类型上传 左边excel列名,必须全小写,右边db字段
     */
    public static $other_fields_number = [
        'house rental' => 'house',
        'notebook rental' => 'notebook',
        'bonus' => 'bonus',
        'recommended fee' => 'recommended',
//        'converted paid leaves' => 'converted_paid', //12073【LAO&MY&VN&ID|BY&HCM】员工剩余年假处理 移除
        'compensation' => 'compensation',//离职补偿
        'incentive' => 'incentive',//提成
        'other income1' => 'other_income1',
        'other income2' => 'other_income2',
        'performance allowance deduct'=>'pre_tax_deduct1',
//        'hire purchase' => 'hire_purchase',
        'equipment' => 'equipment',
        'uniform' => 'uniform',
        'incentive(paid)' => 'incentive_paid',
        'other deduct1' => 'other_deduct1',
        'other deduct2' => 'other_deduct2',
        'wedding gold'=>'wedding',
        'funeral money'=>'funeral',
        'employee death grant'=>'death',
        'after tax income1'=>'after_tax_income1',
        'no of dependants'=>'dependants_num',//抚养人数
        'firestarter award' => 'firestarer_award',
    ];


    public static $other_fields_string = [
    ];

    /*
     * 覆盖类型上传 左边excel列名,必须全小写,右边db字段
     */

    public static $cover_fields = [
//考勤覆盖
        'lw(-)' => 'lw_thismonth',
        'ab(-)' => 'ab_thismonth',
        'off' => 'off_thismonth',
        'covid-19 leave'=>'covid_thismonth',
        'days not in employment' => 'not_working_days_thismonth',
        'pd days transfer before' => 'pd_transfer_before_on_thismonth',//预留
        'pd days transfer after' => 'pd_transfer_after_on_thismonth',//预留
        'no of hrs late' => 'late_len_thismonth',//预留

        'after transfer days' => 'after_transfer_days_thismonth',

        'social base' => 'social_base_thismonth',
//        'sss er company' => 'sss_company_thismonth',
//        'sss ee staff' => 'sss_staff_thismonth',
//        'sss total' => 'sss_thismonth',
        'social insurance er' => 'social_insurance_er_thismonth', //2023 new 雇主社保
        'health insurance er' => 'health_insurance_er_thismonth', //2023 new 雇主医疗
        'unemployment insurance er' => 'unemployment_insurance_er_thismonth',//2023 new 雇主失业
        'social insurance ee' => 'social_insurance_ee_thismonth',//2023 new 雇员社保
        'health insurance ee' => 'health_insurance_ee_thismonth',//2023 new 雇员医疗
        'unemployment insurance ee' => 'unemployment_insurance_ee_thismonth',//2023 new 雇员失业
        'working ot 1.5 time' => 'ot1_15_len_thismonth',
        'working ot 2 time' => 'ot1_2_len_thismonth',
        'working ot 2.1 time' => 'ot1_21_len_thismonth',
        'weekend ot 2 time' => 'ot2_2_len_thismonth',
        'weekend ot 2.7 time' => 'ot2_27_len_thismonth',
        'holiday ot 3 time' => 'ot3_3_len_thismonth',
        'holiday ot 3.9 time' => 'ot3_39_len_thismonth',
        'ot' => 'ot_thismonth',
        'tax' => 'tax_thismonth',
        'attendance allowance' => 'attendance_thismonth',
        'food on transfer before' => 'food_on_transfer_before_thismonth',
        'food on transfer after'  => 'food_on_transfer_after_thismonth',
        'food rental'  => 'food_thismonth',
        'night shift'  => 'night_shift_len_thismonth',
    ];

    //不做扩展
    public function import_transfer($gongzi_month, $file_name, $op_type = '', $staff_type = 0)
    {

        $this->logger->info('import_transfer begin');

        if (!file_exists($file_name)) {
            $result = array(
                'code' => -2,
                'msg' => 'excel not exist',
                'data' => [],
            );
            return $result;
        }

        $table_name = $this->get_table_name("salary_transfer", $op_type);

        try {
            $datas = $this->getExcelDataBaseForFile($file_name, 0, 3, '', ['eff.date'], 'emp.no.', false);
            $job_title_map = (new HrJobTitleService)->getJobTitleIdMap();
            $store_name_map = $this->getStoreNameMap();


            $have_error = false;
            $warning_msg = '';
//            var_dump($datas);

            foreach ($datas as $item) {

                $staff_id = $item['emp.no.'];
                $this->logger->info('begin process staff_id:' . $staff_id);

                $transfer_day = $item['eff.date'];
                if (empty($transfer_day)) {
                    $warning_msg .= $staff_id . ' has no eff.date' . ';';
                    $this->logger->info('warning not get transfer_day');
                    continue;
                }
                $salary_transfer = [];

                $job_name_old = trim(strtolower($item['position (old)']));
                $job_name_new = trim(strtolower($item['position (new)']));
                $salary_transfer['position_name_old'] = $job_name_old;
                $salary_transfer['position_name_new'] = $job_name_new;

                if (isset($job_title_map[$job_name_old])) {
                    $salary_transfer['job_old'] = $job_title_map[$job_name_old];
                } else {
                    $warning_msg .= $staff_id . ' can not get job old position for ' . $job_name_old . ';';
                    $this->logger->info($staff_id . ' can not get job old position for ' . $job_name_old);
                    $salary_transfer['job_old'] = 0;
                }

                if (isset($job_title_map[$job_name_new])) {
                    $salary_transfer['job_new'] = $job_title_map[$job_name_new];
                } else {
                    $warning_msg .= $staff_id . ' can not get job new position for ' . $job_name_new . ';';
                    $this->logger->info($staff_id . ' can not get job new position for ' . $job_name_new);
                    $salary_transfer['job_new'] = 0;
                }


                $store_name_old = trim(mb_strtolower($item['branch (old)']));
                $store_name_new = trim(mb_strtolower($item['branch (new)']));

                if (isset($store_name_map[$store_name_old])) {
                    $salary_transfer['store_id_old'] = $store_name_map[$store_name_old];
                } else {
                    if (!empty($store_name_old)) {
                        $warning_msg .= $staff_id . ' can not get old store id for ' . $store_name_old . ';';
                        $this->logger->info($warning_msg);
                    }
                    $salary_transfer['store_id_old'] = '';
                }

                if (isset($store_name_map[$store_name_new])) {
                    $salary_transfer['store_id_new'] = $store_name_map[$store_name_new];
                } else {
                    if (!empty($store_name_new)) {
                        $warning_msg .= $staff_id . ' can not get new store id for ' . $store_name_new . ';';
                        $this->logger->info($warning_msg);
                    }
                    $salary_transfer['store_id_new'] = '';
                }

                $salary_transfer['staff_info_id'] = $staff_id;
                $salary_transfer['transfer_day'] = $transfer_day;
                $salary_transfer['excel_month'] = $gongzi_month;
                $salary_transfer['week_day_old'] = $item['week day'];

                $salary_transfer['salary_old'] = $item['base salary'];
                $salary_transfer['performance_old'] = $item['performance allowance base'];
                $salary_transfer['attendance_old'] = $item['attendance allowance base'];
                $salary_transfer['traffic_old'] = $item['traffic rental base'];
                $salary_transfer['phone_old'] = $item['telephone rental base'];
                $salary_transfer['food_old'] = $item['food rental base'];
                $salary_transfer['house_old'] = $item['house rental'] == '' ? 0 :$item['house rental'];
                $salary_transfer['notebook_old'] = $item['notebook rental'] == '' ? 0 : $item['notebook rental'];


                $salary_transfer['week_day_new'] = $item['week day new'];
                $salary_transfer['salary_new'] = $item['base salary new'];
                $salary_transfer['performance_new'] = $item['performance allowance base new'];
                $salary_transfer['attendance_new'] = $item['attendance allowance base new'];
                $salary_transfer['traffic_new'] = $item['traffic rental base new'];
                $salary_transfer['phone_new'] = $item['telephone rental base new'];
                $salary_transfer['food_new'] = $item['food rental base new'];
                $salary_transfer['notebook_new'] = $item['notebook rental new'] == '' ? 0 : $item['notebook rental new'];
                $salary_transfer['house_new'] = $item['house rental new'] == '' ? 0 : $item['house rental new'];
//                $salary_transfer['island_new'] = $item['island allowance new'];
//                $salary_transfer['gasoline_new'] = $item['gasoline allowance new'];

                $salary_transfer['remark'] = $item['remark'];
//var_dump($salary_transfer);

                $sql = "select * from $table_name where staff_info_id = $staff_id and excel_month='" . $salary_transfer['excel_month'] . "'";
                $msalaryTransfer = $this->{$this->read_db}->fetchOne($sql);
                if ($msalaryTransfer) {
                    $sql_del = "delete from $table_name where staff_info_id = $staff_id and excel_month='" . $salary_transfer['excel_month'] . "'";
                    $this->{$this->write_db}->execute($sql_del);
                }

                $success = $this->{$this->write_db}->insertAsDict(
                    $table_name,
                    $salary_transfer
                );
                if (!$success) {
                    $this->logger->error('insert fail ' . $staff_id . " month:" . $gongzi_month);
                    $have_error = true;
                }
            }
            if (!empty($warning_msg)) {
                $warning_msg = 'warning:' . $warning_msg;
            }

            if ($have_error) {
                $result = array(
                    'code' => -6,
                    'msg' => 'some item save db error; ' . $warning_msg,
                    'data' => [],
                );
            } else {
                $result = array(
                    'code' => 1,
                    'msg' => 'import succ; ' . $warning_msg,
                    'data' => ['total' => count($datas)],
                );
            }
            return $result;
        } catch (Exception $e) {
            $result = array(
                'code' => -5,
                'msg' => 'have exception:' . $e->getMessage() . $warning_msg,
                'data' => [],
            );
            return $result;
        }

    }
    public function import_staff_no_social($gongzi_month, $file_name, $op_type = '', $staff_type = 0)
    {
        return $this->import_staff_base($gongzi_month, $file_name, $op_type, self::SPECIAL_STAFF_TYPE_NO_SOCIAL);
    }

    public function import_staff_frontline($gongzi_month, $file_name, $op_type = '', $staff_type = 0)
    {
        return $this->import_staff_base($gongzi_month, $file_name, $op_type, self::SPECIAL_STAFF_TYPE_VN_FRONTLINE);
    }

    public function import_staff_no_tax($gongzi_month, $file_name, $op_type = '', $staff_type = 0)
    {
        if (!file_exists($file_name)) {
            $result = array(
                'code' => -2,
                'msg' => 'excel not exist',
                'data' => [],
            );
            return $result;
        }
        $table_name = $this->get_table_name("salary_special_staff", $op_type);
        try {
            $datas = $this->getExcelDataBaseForFile($file_name, 0, 1, '', [], 'emp.no.', false);
            $have_error = false;
            foreach ($datas as $item) {
                $staff_id = $item['emp.no.'];
                if(isset($item['type'])){
                    $type_name = strtolower(trim($item['type']));
                    if($type_name == 'b'){
                        $special_staff_category = self::SPECIAL_STAFF_TYPE_VN_B_TAX;
                    }elseif($type_name == 'c'){
                        $special_staff_category = self::SPECIAL_STAFF_TYPE_VN_C_TAX;
                    }elseif($type_name == 'n'){
                        $special_staff_category = self::SPECIAL_STAFF_TYPE_NO_TAX;
                    }else{
                        continue;
                    }
                }else{
                    continue;
                }
                $salary_special_item['staff_info_id'] = $staff_id;
                $salary_special_item['excel_month'] = $gongzi_month;
                $salary_special_item['type'] = $special_staff_category; //type 1 为no tax staff
                $sql = "select * from $table_name where type={$special_staff_category} and staff_info_id = $staff_id and excel_month='" . $salary_special_item['excel_month'] . "'";
                $mSalarySpecialStaff = $this->{$this->read_db}->fetchOne($sql);
                if ($mSalarySpecialStaff) {
                    continue;
                }
                $success = $this->{$this->write_db}->insertAsDict(
                    $table_name,
                    $salary_special_item
                );
                if (!$success) {
                    $this->logger->error('insert fail ' . $staff_id . " month:" . $gongzi_month);
                    $have_error = true;
                }
            }
            if ($have_error) {
                $result = array(
                    'code' => -6,
                    'msg' => 'some item save db error',
                    'data' => [],
                );
            } else {
                $result = array(
                    'code' => 1,
                    'msg' => 'import succ; ',
                    'data' => ['total' => count($datas)],
                );
            }
            return $result;
        } catch (\Exception $e) {
            $result = array(
                'code' => -5,
                'msg' => 'have exception:' . $e->getMessage(),
                'data' => [],
            );
            return $result;
        }
    }


    public function import_monthstaff_ffm_staff($file_name, $op_type = '', $staff_type = 0)
    {
        return $this->import_month_staff_base($file_name, $op_type, self::SPECIAL_STAFF_TYPE_FFM);
    }

    //是整月,最后一个参数$is_whole_month 需要设置为true,month字段就会认为是时间字段,并且会取前7个字符
    public function import_paid($file_name, $op_type = '', $staff_type = 0)
    {
        return $this->import_paid_base($file_name, $op_type, $staff_type, true);
    }

}