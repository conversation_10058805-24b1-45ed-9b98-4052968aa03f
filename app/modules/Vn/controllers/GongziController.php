<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/4/2
 * Time: 下午2:20
 */

namespace App\Modules\Vn\Controllers;

use App\Library\Enums\SalaryEnums;
use App\Library\Exception\BusinessException;
use App\Modules\Vn\Services\Gongzi2023Service;
use app\modules\Vn\services\Gongzi2025Service;
use App\Modules\Vn\Services\GongziService;
use App\Modules\Vn\Services\GongzicalService;
use App\Modules\Vn\Services\GongziImportService;

use App\Library\ErrCode;
use App\Models\backyard\AsyncTaskModel;
use App\Modules\Vn\Services\SalaryAttendanceAllowanceService;
use App\Services\AsyncTaskService;
use App\Services\GongziBaseService;
use App\Services\PayrollManageService;
use App\Services\SettingEnvService;


class GongziController extends \App\Controllers\BaseController
{
    private $logger;

    public function initialize()
    {
        $this->logger = $this->getDi()->get('logger');
        $config = $this->getDi()->get('config');
        $this->_timeOffset = $config->application->add_hour;
        parent::initialize();
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * @Token
     */
    public function hoindexAction()
    {
        $month = $this->request->get('month');
        $staff_type = 1;
        $op_type = '';
        $title = 'Headoffice Salary Calculation';
        return $this->indexBase($staff_type, $op_type, $title,$month);
    }

    /**
     * @Token
     */
    public function hoindexthAction()
    {
        $month = $this->request->get('month');
        $staff_type = 1;
        $op_type = 'th';
        $title = 'Headoffice Salary Calculation';
        return $this->indexBase($staff_type, $op_type, $title,$month);
    }

    /**
     * @Token
     */
    public function dcindexAction()
    {
        $month = $this->request->get('month');
        $staff_type = 2;
        $op_type = '';
        $title = 'DC Salary Calculation';
        return $this->indexBase($staff_type, $op_type, $title,$month);
    }

    /**
     * @Token
     */
    public function dcindexthAction()
    {
        $month = $this->request->get('month');
        $staff_type = 2;
        $op_type = 'th';
        $title = 'DC Salary Calculation';
        return $this->indexBase($staff_type, $op_type, $title,$month);
    }

    public function indexBase($staff_type, $op_type, $title,$month)
    {
        if (empty($month)) {
            $today = date('Y-m-d');
            $this_month = substr($today, 0, 7);
            $ym = date('Y-m', strtotime($this_month . '-01' . '-1 month'));
        } else {
            $ym = $month;
        }
        if ($ym >= '2025-03') {
            $specialAttendanceTpl = 'https://tc-static-asset-internal.flashexpress.vn/HCM/2025/07/16/17526355316877188bc4f52.xlsx';
        } else {
            $specialAttendanceTpl = 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1660639478-2550b40437b548edb40d9d6c620b3505.xlsx';
        }


        $data['month'] = $ym;
        $data['title'] = $title;
        $data['staff_type'] = $staff_type;
        $data['op_type'] = $op_type;
        $data['company_list'] =(new PayrollManageService())->getStaffManageCompanyConfig($this->user['id']);


        $data['tabs']['upload']['title'] = 'Upload';
        $data['tabs']['upload']['upload_list']['other'] = [
            ['excel_type' => 'other', 'sub_type' => 'multi', 'name' => 'Multi Column', 'tpl' => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1654498109-1515b7b9e5b54f90aeefe80660e9d335.xlsx', 'tpl_type' => 0],
        ];

        $data['tabs']['upload']['upload_list']['transfer'] = [
            ['excel_type' => 'transfer', 'sub_type' => 'transfer', 'name' => 'transfer', 'tpl' => 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1643288892-5e611df0bc1e41dba47809806d9b3c44.xlsx', 'del_type' => 'transfer', 'del_name' => 'Delete Transfer Emp.No.', 'tpl_type' => 0],
        ];

        $data['tabs']['upload']['upload_list']['cover'] = [
            ['excel_type' => 'cover', 'sub_type' => 'social_thismonth', 'name' => 'Social', 'tpl' => 'https://fex-vn-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1675416481-08497c5dee284f0b854010210ca65f12.xlsx', 'tpl_type' => 0],
            ['excel_type' => 'cover', 'sub_type' => 'tax_thismonth', 'name' => 'Tax', 'tpl' => 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1643288988-434499125fd141f6947b6557481d080c.xlsx', 'tpl_type' => 0],
            ['excel_type' => 'cover', 'sub_type' => 'attendance_thismonth', 'name' => 'Special Attendance', 'tpl' => $specialAttendanceTpl, 'tpl_type' => 0],
            ['excel_type' => 'cover', 'sub_type' => 'ot_thismonth', 'name' => 'special OT', 'tpl' => 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1643289056-e59ac5fb8df04f3f8f92a9f3fb41f3dc.xlsx', 'tpl_type' => 0],
            ['excel_type' => 'cover', 'sub_type' => 'attendance_thismonth', 'name' => 'Attendance Allowance', 'tpl' => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1656854174-128c627086184a87931d5c739339eba6.xlsx', 'tpl_type' => 0],
        ];
        if ($ym >= '2025-03') {
            $data['tabs']['upload']['upload_list']['cover'][] = ['excel_type' => 'cover', 'sub_type' => 'food_thismonth', 'name' => 'Food Rental', 'tpl' => 'https://tc-static-asset-internal.flashexpress.vn/HCM/2025/04/01/174348941867eb898a4e75b.xlsx', 'tpl_type' => 0];
        }
        $data['tabs']['upload']['upload_list']['staff'] = [
            ['excel_type' => 'staff', 'sub_type' => 'no_social', 'name' => 'No Social Staff', 'tpl' => 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1643289121-42a958a6bb494ff594f5987d21ea068b.xlsx', 'tpl_type' => 0, 'del_type' => 'special_staff', 'del_sub_type' => GongziBaseService::SPECIAL_STAFF_TYPE_NO_SOCIAL, 'del_name' => 'No Social Staff Delete'],
            ['excel_type' => 'staff', 'sub_type' => 'no_tax', 'name' => 'Special Tax Staff', 'tpl' => 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1643289158-f29efb369e47470fb85aefad565cc4b9.xlsx', 'tpl_type' => 0, 'del_type' => 'special_staff', 'del_sub_type' => GongziBaseService::SPECIAL_STAFF_TYPE_NO_TAX, 'del_name' => 'Special Tax Staff Delete'],
            ['excel_type' => 'staff', 'sub_type' => 'frontline', 'name' => 'Frontline Staff', 'tpl' => 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1643289188-1371d42ce4a249738e80108ce579dc06.xlsx', 'tpl_type' => 0, 'del_type' => 'special_staff', 'del_sub_type' => GongziBaseService::SPECIAL_STAFF_TYPE_VN_FRONTLINE, 'del_name' => 'Frontline Staff Delete'],
        ];

        $data['tabs']['output']['title'] = 'Output';
        $data['tabs']['output']['field_list'] = (new GongziImportService())->getAllTypeList();

        $data['tabs']['cal']['title'] = 'Calculate';

        if ($op_type == '') {
            $data['tabs']['report']['title'] = 'Salary Report';
            $data['tabs']['report']['upload_list']['paid'] = [
                ['excel_type' => 'paid', 'sub_type' => 'paid', 'name' => 'salary report', 'tpl' => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1639815658-773b832097b048e598d06d75315f3da9.xlsx', 'tpl_type' => 0],
                ['excel_type' => 'cancel_paid', 'sub_type' => 'cancel_paid', 'name' => 'cancel paid',  'tpl' => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1650443164-3f2b9580a53d4083882eeedd4b1a4833.xlsx', 'tpl_type' => 0],
            ];

            $data['tabs']['other_upload']['title'] = 'Other Upload';
            $setting_env = new SettingEnvService();
            $data['tabs']['other_upload']['upload_list']['special_salary_white_list'] = [
                ['code' => 'special_flashexpress_staff','name' => 'Special Flashexpress Staff', 'value'=> $setting_env->getSetVal('special_flashexpress_staff')],
                ['code' => 'special_fulfillment_staff','name' => 'Special Fulfillment Staff', 'value'=> $setting_env->getSetVal('special_fulfillment_staff')],
                ['code' => 'special_fcommerce_staff','name' => 'Special Fcommerce Staff', 'value'=> $setting_env->getSetVal('special_fcommerce_staff')],
            ];
        }
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    //中国团队计算总部

    /**
     * @Token
     * Permission(action='gongzi.ho.calculate')
     */
    public function calculate_hoAction()
    {
        $staff_type = 1;
        $op_type = '';
        return $this->calculateBase($staff_type, $op_type);
    }

    //泰国团队计算ho

    /**
     * @Token
     * @Permission(action='gongzi.hoth.calculate')
     */
    public function calculate_hothAction()
    {
        $staff_type = 1;
        $op_type = 'th';
        return $this->calculateBase($staff_type, $op_type);
    }
    //中国团队计算dc

    /**
     * @Token
     * @Permission(action='gongzi.dc.calculate')
     */
    public function calculate_dcAction()
    {
        $staff_type = 2;
        $op_type = '';
        return $this->calculateBase($staff_type, $op_type);
    }
    //泰国团队计算dc

    /**
     * @Token
     * @Permission(action='gongzi.dcth.calculate')
     */
    public function calculate_dcthAction()
    {
        $staff_type = 2;
        $op_type = 'th';
        return $this->calculateBase($staff_type, $op_type);
    }


    public function calculateBase($staff_type, $op_type)
    {
        $this->view->disable();
        $month = $this->request->get('month', 'trim');
        $staff_info_ids = $this->request->get('staff_info_ids', 'trim');

        if (!$this->checkMonthValidate($month)) {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'can not calculate this month', []);
        }

        $check = (new PayrollManageService())->getStaffManageCompanyIds($this->user['id']);
        if (empty($check)) {
            throw new BusinessException($this->t->_('no_have_permission'));
        }

        $gongzicalBll = new GongzicalService();
        $staff_id_arr = $gongzicalBll->get_staff_arr_by_input($staff_info_ids);

        $task_info = [
            "task" => "gongzi",
            "action" => "calculate",
            'params' => [$month, $staff_type, $op_type, $staff_id_arr,$this->user['id']],
        ];
        $operator_id = $this->user['id'];
//            $operator_id = 10000;
        $tasks[] = $task_info;
        $category = AsyncTaskModel::CATEGORY_GONGZI_CALCULATE;
        $type = $staff_type;

        $svc = new AsyncTaskService();
        $result = $svc->addAsyncTask($operator_id, $tasks, $category, $type);
        return $this->returnJson($result['code'], $result['message'], []);
    }

    //中国团队导出总部

    /**
     * @Token
     * @Permission(action='gongzi.ho.download')
     */
    public function download_hoAction()
    {
        $staff_type = 1;
        $op_type = '';
        return $this->downloadBase($staff_type, $op_type);
    }

    //中国团队导出dc

    /**
     * @Token
     * @Permission(action='gongzi.dc.download')
     */
    public function download_dcAction()
    {
        $staff_type = 2;
        $op_type = '';
        return $this->downloadBase($staff_type, $op_type);
    }

    //泰国团队导出ho

    /**
     * @Token
     * @Permission(action='gongzi.hoth.download')
     */
    public function download_hothAction()
    {
        $staff_type = 1;
        $op_type = 'th';
        return $this->downloadBase($staff_type, $op_type);
    }

    //泰国团队导出dc

    /**
     * @Token
     * @Permission(action='gongzi.dcth.download')
     */
    public function download_dcthAction()
    {
        $staff_type = 2;
        $op_type = 'th';
        return $this->downloadBase($staff_type, $op_type);
    }

    //same with ph ,except month_type
    public function downloadBase($staff_type, $op_type)
    {

        $staff_info_ids = $this->request->get('staff_info_ids', 'trim');
        $export_type = $this->request->get('export_type', 'trim');
        $month = $this->request->get('month', 'trim');
        $company_id = $this->request->get('department', 'int');

        if (!array_key_exists($export_type, SalaryEnums::GONGZI_TABLE_TYPE_MAP)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'can not export this type', []);
        }

        if (!$this->checkMonthValidate($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'can not export this month', []);
        }

        $check = (new PayrollManageService())->getStaffManageCompanyIds($this->user['id']);
        if (empty($check)) {
            throw new BusinessException($this->t->_('no_have_permission'));
        }
        if ($month >= '2025-03') {
            $bll = new Gongzi2025Service();
        }elseif (substr($month,0,4) >= '2023') {
            $bll = new Gongzi2023Service();
        } else {
            $bll = new GongziService();
        }
        $staff_id_arr = $bll->get_staff_arr_by_input($staff_info_ids);
        $header = array_keys($bll->excelShowMap);
        //header增加序号
        array_unshift($header, 'No.');

        $fmt_month = $month;

        if ($staff_type == 1) {
            $file_name = 'salary_headoffice_' . $fmt_month;
        } else {
            $file_name = 'salary_dc_' . $fmt_month;
        }
        //设置操作人数据权限
        $bll->setOperateId($this->user['id'])->setPayrollManageInfo($bll->get_staff_id_payroll_company_info($month,$op_type),GongziService::COMPANY_ID_FULLFILLMENT);

        $sql_filter = '';

        $data = $bll->getGongziDataForExport($fmt_month, $staff_type, $op_type, $export_type, $staff_id_arr, $sql_filter, $company_id);

        $hasno_data = [];
        foreach ($data as $k => $item) {
            array_unshift($item, $k + 1);
            $hasno_data[] = $item;
        }

        $flag = $this->exportExcelReturn($header, $hasno_data, $file_name);

        if ($flag['code'] == 1) {
            return $this->returnJson(ErrCode::SUCCESS, 'success', $flag['data']);
        } else {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'ERROR', []);
        }
    }

    //中国团队导出总部

    /**
     * @Token
     * @Permission(action='gongzi.ho.list')
     */
    public function list_hoAction()
    {
        $staff_type = 1;
        $op_type = '';
        return $this->listBase($staff_type, $op_type);
    }

    //中国团队导出dc

    /**
     * @Token
     * @Permission(action='gongzi.dc.list')
     */
    public function list_dcAction()
    {
        $staff_type = 2;
        $op_type = '';
        return $this->listBase($staff_type, $op_type);
    }

    //泰国团队导出ho

    /**
     * @Token
     * @Permission(action='gongzi.hoth.list')
     */
    public function list_hothAction()
    {
        $staff_type = 1;
        $op_type = 'th';
        return $this->listBase($staff_type, $op_type);
    }

    //泰国团队导出dc

    /**
     * @Token
     * @Permission(action='gongzi.dcth.list')
     */
    public function list_dcthAction()
    {
        $staff_type = 2;
        $op_type = 'th';
        return $this->listBase($staff_type, $op_type);
    }

    //same with ph , except monty_type
    public function listBase($staff_type, $op_type)
    {
        $month = $this->request->get('month', 'trim');

        $type = $this->request->get('type', 'trim');
        $sub_type = $this->request->get('sub_type', 'trim');

        if (!$this->checkMonthValidate($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'validate error', []);
        }

        if ($type == 'upload') {
            $bll = new GongziImportService();
            $result = $bll->list_upload_field($sub_type, $staff_type, $month, $op_type);
        } else {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'type param error', []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['DataList' => $result]);
    }


    //是否可以计算这个月的工资,最多计算上个月的,再之前的不支持
    private function checkMonthValidate($month)
    {
        if (strlen($month) != 7) {
            return false;
        }
        if (!is_numeric(substr($month, 0, 4))) {
            return false;
        }
        $min_month = $this->getMinMonth();
        if ($month >= $min_month) {
            return true;
        } else {
            return false;
        }
    }

    private function getMinMonth()
    {
        $min_month1 = '2021-12';
        return $min_month1;

//        $gongzi_month = gmdate("Y-m",time()+($this->_timeOffset)*3600);
//        $gongzi_month_first_day = $gongzi_month . '-01';
//        $min_month2 = date('Y-m', strtotime($gongzi_month_first_day . ' -3 month'));
//        $min_month = max($min_month1,$min_month2);
//        return $min_month;
    }

    //中国团队计算总部

    /**
     * @Token
     * @Permission(action='gongzi.ho.upload')
     */
    public function upload_hoAction()
    {
        $staff_type = 1;
        $op_type = '';
        return $this->uploadBase($staff_type, $op_type);
    }

    //泰国团队计算ho

    /**
     * @Token
     * @Permission(action='gongzi.hoth.upload')
     */
    public function upload_hothAction()
    {
        $staff_type = 1;
        $op_type = 'th';
        return $this->uploadBase($staff_type, $op_type);
    }
    //中国团队计算dc

    /**
     * @Token
     * @Permission(action='gongzi.dc.upload')
     */
    public function upload_dcAction()
    {
        $staff_type = 2;
        $op_type = '';
        return $this->uploadBase($staff_type, $op_type);
    }
    //泰国团队计算dc

    /**
     * @Token
     * @Permission(action='gongzi.dcth.upload')
     */
    public function upload_dcthAction()
    {
        $staff_type = 2;
        $op_type = 'th';
        return $this->uploadBase($staff_type, $op_type);
    }

    //same with ph, except month_type
    public function uploadBase($staff_type, $op_type)
    {
        if (!$this->request->hasFiles()) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'no file found', []);
        }
        $files = $this->request->getUploadedFiles();
        $month_in_excel_arr = ['paid', 'monthstaff','cancel_paid'];
        $have_sub_type_arr = ['staff', 'monthstaff'];

        $excel_type = $this->request->get('excel_type', 'trim');
        $sub_type = $this->request->get('sub_type', 'trim');

        if (!in_array($op_type, ['', 'th'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error', []);
        }

        //excel里有月的不需要检查
        if (!in_array($excel_type, $month_in_excel_arr)) {
            $month = $this->request->get('month', 'trim');

            if (!$this->checkMonthValidate($month)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'validate error', []);
            }
            $gongzi_month = $month;
        }
        $file = $files[0];
        $file_name = $file->getTempName();

        $bll = new GongziImportService();
        if (in_array($excel_type, $have_sub_type_arr)) {
            //包含子类型的
            $function_name = 'import_' . $excel_type . '_' . $sub_type;
        } else {
            //没有子类型
            $function_name = 'import_' . $excel_type;
        }

        if (!method_exists($bll, $function_name)) {
            return $this->ajax_return('upload method not exist', 0, []);
        }
        if (in_array($excel_type, $month_in_excel_arr)) {
            //excel里包含月
            $result = call_user_func_array(array($bll, $function_name), array($file_name, $op_type, $staff_type));
        } else {
            //excel里没有月
            $result = call_user_func_array(array($bll, $function_name), array($gongzi_month, $file_name, $op_type, $staff_type));
        }
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * @Token
     * @Permission(action='gongzi.ho.delete')
     */
    public function delete_hoAction()
    {
        $staff_type = 1;
        $op_type = '';
        return $this->deleteBase($staff_type, $op_type);
    }

    /**
     * @Token
     * @Permission(action='gongzi.hoth.delete')
     */
    public function delete_hothAction()
    {
        $staff_type = 1;
        $op_type = 'th';
        return $this->deleteBase($staff_type, $op_type);
    }

    /**
     * @Token
     * @Permission(action='gongzi.dc.delete')
     */
    public function delete_dcAction()
    {
        $staff_type = 2;
        $op_type = '';
        return $this->deleteBase($staff_type, $op_type);
    }
    //泰国团队计算dc

    /**
     * @Token
     * @Permission(action='gongzi.dcth.delete')
     */
    public function delete_dcthAction()
    {
        $staff_type = 2;
        $op_type = 'th';
        return $this->deleteBase($staff_type, $op_type);
    }

    public function deleteBase($staff_type, $op_type)
    {

        $this->view->disable();
        $month = $this->request->get('month', 'trim');
        $gongzi_month = $month;

        if (!$this->checkMonthValidate($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'validate error', []);
        }
        $staff_info_ids = $this->request->get('staff_info_ids', 'trim');
        $delete_type = $this->request->get('del_type');
        $delete_sub_type = $this->request->get('del_sub_type', "int");

        //目前只支持2种员工删除
        if ($delete_type == 'special_staff') {
            if (!in_array($delete_sub_type, [GongziBaseService::SPECIAL_STAFF_TYPE_VN_FRONTLINE,GongziBaseService::SPECIAL_STAFF_TYPE_NO_TAX, GongziBaseService::SPECIAL_STAFF_TYPE_NO_SOCIAL])) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'validate error', []);
            }
        }

        $bll = new GongziImportService();
        $staff_id_arr = $bll->get_staff_arr_by_input_comma($staff_info_ids);

        $flag = $bll->delete_staffs($gongzi_month, $staff_id_arr, $op_type, $staff_type, $delete_type, $delete_sub_type);
        if ($flag['code'] == 1) {
            return $this->returnJson(ErrCode::SUCCESS, $flag['msg'], $flag['data']);
        } else {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, $flag['msg'], []);
        }
    }

    /**
     * @Token
     * @Permission(action='gongzi.ho.lastmonth')
     */
    public function lastmonth_hoAction()
    {
        $op_type = '';
        return $this->lastmonthBase($op_type);
    }

    /**
     * @Token
     * @Permission(action='gongzi.dc.lastmonth')
     */
    public function lastmonth_dcAction()
    {
        $op_type = '';
        return $this->lastmonthBase($op_type);
    }

    /**
     * @Token
     * @Permission(action='gongzi.hoth.lastmonth')
     */
    public function lastmonth_hothAction()
    {
        $op_type = 'th';
        return $this->lastmonthBase($op_type);
    }

    /**
     * @Token
     * @Permission(action='gongzi.dcth.lastmonth')
     */
    public function lastmonth_dcthAction()
    {
        $op_type = 'th';
        return $this->lastmonthBase($op_type);
    }

    //same with ph
    public function lastmonthBase($op_type)
    {
        $type = intval($this->request->get('type', 'int'));

        $_data = [];
        $bll = new GongziService();
        $last_gongzi_month = $bll->get_last_month();
        switch ($type) {
            case 2: //ffm staff
                $data = $bll->get_special_staff($last_gongzi_month, 2, $op_type);
                $file_name = 'ffm_staff_' . $last_gongzi_month;
                break;
        }

        $header = ['month', 'staff_info_id'];
        if (!empty($data)) {
            foreach ($data as $k => $v) {
                $_data[] = [
                    'month' => $last_gongzi_month,
                    'staff_info_id' => $v['staff_info_id'],
                ];
            }
        }
        //直接输出的,不是ajax
        //        $this->excel($header, $_data, $file_name);
        $flag = $this->exportExcelReturn($header, $_data, $file_name);

        if ($flag['code'] == 1) {
            return $this->returnJson(ErrCode::SUCCESS, 'success', $flag['data']);
        } else {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'ERROR', []);
        }

    }
    /**
     * 设置员工到设置的公司
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function set_special_companyAction()
    {
        $code = $this->request->get('code','trim');
        $set_val = $this->request->get('value','trim');
        $service = new GongziService();
        $settingEnv = new SettingEnvService();
        if (!in_array($code,GongziService::$specialCompanyStaffCodes)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Invalid type');
        }
        if (empty($set_val)) {
            $settingEnv->saveSettingByCode(['code' =>$code,'set_val' => '']);
            return  $this->returnJson(ErrCode::SUCCESS, 'success');
        }
        if ($notExistStaff = $service->haveNotExistStaff($set_val)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'staff not exist：'.implode(',',$notExistStaff));
        }
        if ($tip = $service->specialSameStaffs($code,explode(',',$set_val),GongziService::$specialCompanyStaffCodes)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR,$tip);
        }
        $settingEnv->saveSettingByCode(['code' =>$code,'set_val' => $set_val ]);
        return  $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 经验津贴导出
     * @Permission(action='gongzi.dc.attendance_allowance')
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface|void
     * @throws BusinessException
     */
    public function attendanceAllowanceExportAction()
    {
        $month = $this->request->get('attendance_month');
        if (empty($month)) {
            return  $this->returnJson(ErrCode::VALIDATE_ERROR, 'please choose month');
        }
       $url = (new SalaryAttendanceAllowanceService())->export($month);
        return  $this->returnJson(ErrCode::SUCCESS, 'success',['url'=>$url]);
    }
    /**
     * 经验津贴导出（payroll）
     * @Permission(action='gongzi.dc.attendance_allowance_payroll')
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface|void
     * @throws BusinessException
     */
    public function attendanceAllowancePayrollExportAction()
    {
        $month = $this->request->get('attendance_month');
        if (empty($month)) {
            return  $this->returnJson(ErrCode::VALIDATE_ERROR, 'please choose month');
        }
        $url = (new SalaryAttendanceAllowanceService())->export($month);
        return  $this->returnJson(ErrCode::SUCCESS, 'success',['url'=>$url]);
    }

    /**
     * 同步出勤津贴到工资表
     * @Token
     * @Permission(action='gongzi.dc.attendance_allowance')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface|void
     * @throws BusinessException
     */
    public function syncAttendanceAllowanceAction()
    {
        $month = $this->request->get('attendance_month');
        if (empty($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please choose month');
        }
        $operateId = $this->user['id'];
        (new SalaryAttendanceAllowanceService())->syncUseLock($month, false, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 同步出勤津贴到工资表(payroll)
     * @Token
     * @Permission(action='gongzi.dc.attendance_allowance_payroll')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface|void
     * @throws BusinessException
     */
    public function syncAttendanceAllowancePayrollAction()
    {
        $month = $this->request->get('attendance_month');
        if (empty($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please choose month');
        }
        $operateId = $this->user['id'];
        (new SalaryAttendanceAllowanceService())->syncUseLock($month, true, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

}