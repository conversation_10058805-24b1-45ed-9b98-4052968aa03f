<?php
/**
 * 对外部 Ffm 提供的api 接口 单独鉴权
 */

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Library\SyncClient;
use App\Library\Validation\ValidationException;
use App\Services\FfmStaffInfoService;
use App\Services\SettingEnvService;
use App\Services\StaffInfoService;

class ExternalFfmApiController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
        //第一步先鉴权
        $this->apiAuthentication();
    }

    //鉴权
    public function apiAuthentication()
    {
        //获取参数
        $params = $this->request->get();
        //获取鉴权 key  // dev: DdBsCA5JE31tqRYJ7SQEkTZ8WpvcaScb
        $secret_key = (new SettingEnvService())->getSetValFromCache('external_ffm_api_secret_key');
        $SyncClient = new SyncClient(['secret_key'=>$secret_key]);
        //获取接口请求时间  1 小时误差  也就是 1 小时前请求的 会鉴权失败
        $sign_time = time() - (60 * 60); //可鉴权最小时间戳
        //判断鉴权
        if (!isset($params['sign'])
            || !isset($params['sign_time'])
            || ($params['sign_time'] < $sign_time)
            || !($SyncClient->check_sign($params))) {

            unset($params['sign']);
            $this->logger->info(['params' => $params, 'sign' => $SyncClient->get_sign($params)]);
            //鉴权失败
            throw new ValidationException(' Authentication failed ! ');
        };
    }

    /**
     * 获取员工档案信息-- 相关部门下面的
     * 提供单独鉴权方式
     *
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStaffListAction()
    {
        $params              = $this->request->get();
        $FfmStaffInfoService = new  FfmStaffInfoService();
        $result              = $FfmStaffInfoService->getStaffList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data'] ?? []);
    }

    /**
     * 获取部门信息 -- 相关部门下面的
     * 提供单独鉴权方式
     *
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getDepartmentListAction()
    {
        $params              = $this->request->get();
        $FfmStaffInfoService = new  FfmStaffInfoService();
        $result              = $FfmStaffInfoService->getDepartmentList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data'] ?? []);
    }

}