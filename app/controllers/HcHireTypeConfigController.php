<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcHireTypeConfigModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AsyncImportTaskService;
use App\Services\HcHireTypeConfigService;
use App\Services\ExcelService;
use App\Services\SettingEnvService;
use App\Services\SysService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class HcHireTypeConfigController extends BaseController
{

    /**
     * @api /hc-hire-type-config/enums
     * @Permission(action='winhr.hc_hire_type_config')
     * @return Response|ResponseInterface
     */
    public function enumsAction()
    {
        $data['status_list']    = (new HcHireTypeConfigService())->statusList();
        $data['hire_type_list'] = (new SysService())->getHireTypeList();
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 列表
     * @api /hc-hire-type-config/list
     * @Permission(action='winhr.hc_hire_type_config')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params     = $this->request->get();
        $validation = [
            'page'        => 'Required|IntGe:1',
            'size'        => 'Required|IntGe:1',
            'config_type' => 'Required|IntIn:1,2',
        ];
        $params     = array_filter($params);
        Validation::validate($params, $validation);

        $data = (new HcHireTypeConfigService())->list($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 列表导出
     * @api /hc-hire-type-config/listExport
     * @Permission(action='winhr.hc_hire_type_config')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listExportAction()
    {
        $params         = $this->request->get();
        $params['user'] = $this->user;
        $params['lang'] = $this->locale;
        //获取文件名
        $file_name = 'HC hire type position_' . date('YmdHis') . '.xlsx';
        if ($params['config_type'] == HcHireTypeConfigModel::CONFIG_TYPE_BRANCH) {
            $file_name = 'HC hire type  branch_' . date('YmdHis'). '.xlsx';
        }
        $action_name = 'hc-hire-type-config' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'configListExport';
        //入 task 表 走队列导出
        $data = (new ExcelService())->insertTask(
            $this->user['id'],
            $action_name,
            $params,
            HcmExcelTackModel::TYPE_DEFAULT,
            $file_name,false
        );
        return $this->returnJson(
            $data['code'] ?? ErrCode::SYSTEM_ERROR,
            $data['message'] ?? 'success',
            $data['data'] ?? []
        );
    }


    /**
     * @api /hc-hire-type-config/batchObsolete
     * 批量作废
     * @Permission(action='winhr.hc_hire_type_config')
     * @throws ValidationException|BusinessException
     */
    public function batchObsoleteAction()
    {
        $importPath = $this->request->get('file_url');
        if (empty($importPath)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'path not found');
        }
        $args['lang'] = $this->request->getBestLanguage();
        $operatorId   = $this->user['id'] ?? 0;

        $data = (new AsyncImportTaskService())->insertTask(
            $operatorId,
            AsyncImportTaskModel::HC_HIRE_TYPE_CONFIG_BATCH_OBSOLETE,
            $importPath,
            $args
        );

        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 作废
     * @api /hc-hire-type-config/obsolete
     * @Permission(action='winhr.hc_hire_type_config')
     * @throws ValidationException|BusinessException
     */
    public function obsoleteAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|IntGe:0',
        ]);
        $data           = (new HcHireTypeConfigService())->obsolete($params,$this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * 职位导入
     * @api /hc-hire-type-config/positionImport
     * @Permission(action='winhr.hc_hire_type_config')
     * @throws ValidationException|BusinessException
     */
    public function positionImportAction()
    {
        $importPath = $this->request->get('file_url');
        if (empty($importPath)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'path not found');
        }

        $args['lang'] = $this->request->getBestLanguage();
        $operatorId   = $this->user['id'] ?? 0;

        $data = (new AsyncImportTaskService())->insertTask(
            $operatorId,
            AsyncImportTaskModel::HC_HIRE_TYPE_CONFIG_IMPORT_POSITION,
            $importPath,
            $args
        );


        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 网点职位导入
     * @api /hc-hire-type-config/storePositionImport
     * @Permission(action='winhr.hc_hire_type_config')
     * @throws ValidationException|BusinessException
     */
    public function storePositionImportAction()
    {
        $importPath = $this->request->get('file_url');
        if (empty($importPath)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'path not found');
        }

        $args['lang'] = $this->request->getBestLanguage();
        $operatorId   = $this->user['id'] ?? 0;

        $data = (new AsyncImportTaskService())->insertTask(
            $operatorId,
            AsyncImportTaskModel::HC_HIRE_TYPE_CONFIG_IMPORT_STORE,
            $importPath,
            $args
        );

        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 列表导出作废
     * @api /hc-hire-type-config/listExportObsolete
     * @Permission(action='winhr.hc_hire_type_config')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listExportObsoleteAction()
    {
        $params              = $this->request->get();
        $params['user_info'] = $this->user;
        $params['lang']      = $this->locale;
        //获取文件名
        $file_name   = uniqid('hc_hire_type_config_obsolete_' . $this->locale . date('Ymd_His')) . '.xlsx';
        $action_name = 'hcHireTypeConfig' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'configListExport';
        //入 task 表 走队列导出
        $data = (new ExcelService())->insertTask(
            $this->user['id'],
            $action_name,
            $params,
            HcmExcelTackModel::TYPE_DEFAULT,
            $file_name
        );
        return $this->returnJson(
            $data['code'] ?? ErrCode::SYSTEM_ERROR,
            $data['message'] ?? 'success',
            $data['data'] ?? []
        );
    }

    /**
     * @api /hc-hire-type-config/operatorLogList
     * 操作记录
     * @Permission(action='winhr.hc_hire_type_config')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function operatorLogListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'config_id'   => 'Required|IntGe:0',
        ]);

        $params = array_filter($params);
        $data   = (new HcHireTypeConfigService())->operatorLogList($params['config_id']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 导入模板下载
     * @Permission(action='winhr.hc_hire_type_config')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api /hc-hire-type-config/importTemplateUrl
     */
    public function importTemplateUrlAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'config_type' => 'Required|IntIn:1,2',
        ]);

        if ($params['config_type'] == HcHireTypeConfigModel::CONFIG_TYPE_BRANCH) {
            $url = 'https://tc-static-asset-internal.flashexpress.com/workOrder/1754549707-f3a6e91c024541dcb6c699850f637be2.xlsx';
        } else {
            $url = 'https://tc-static-asset-internal.flashexpress.com/workOrder/1754549660-ce63851f4fe644988df9243dec1380cb.xlsx';
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }


    /**
     * @api /hc-hire-type-config/batchObsoleteTemplateUrl
     * @Permission(action='winhr.hc_hire_type_config')
     * 批量作废模板下载
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function batchObsoleteTemplateUrlAction()
    {
        $params['status']      = [ HcHireTypeConfigModel::STATUS_ACTIVE];
        $params['config_type'] = HcHireTypeConfigModel::CONFIG_TYPE_BRANCH;
        $result                = (new HcHireTypeConfigService())->getEffectiveConfigListData($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url'=>$result['request_uri']]);
    }
}
