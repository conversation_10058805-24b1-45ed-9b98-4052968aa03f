<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Validation\Validation;
use App\Services\AttendanceStatisticsService;
use App\Services\AttendanceStatisticsReportService;

class AttendanceStatisticsController extends BaseController
{

    /**
     * @Token
     * @Permission(action='branch_attendance_statistics_list')
     * @throws ValidationException
     */
	public function store_attendance_listAction()
	{
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'region_id' => 'StrLenGeLe:0,30|>>>:[region_id] params error' ,
            'piece_id' => 'Arr|>>>:[piece_id]] params error' ,
            'store_id' => 'Arr|>>>:[store_id]] params error' ,
            //'store_kind' => 'Str|>>>:[store_kind] params error' ,
            'geography_code' => 'Arr|>>>:[geography_code]] params error' ,
            'staff_state' => 'Arr|>>>:[staff_state]] params error' ,
            'start_date' => 'Required|Date|>>>:[start_time] params error' ,
            'end_date' => 'Required|Date|>>>:[end_time] params error' ,
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error' ,

            'page' => 'Required|Int|IntGe:1|>>>:[page] params error' ,
            'size' => 'Required|Int|>>>:[page size] params error' ,
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $attendanceStatisticsService =  new AttendanceStatisticsService();
        $attendanceStatisticsService->query_type = 1;
        $data               = $attendanceStatisticsService->getStaffsAttendanceInfo($params);

        return $this->returnJson(ErrCode::SUCCESS, '', $data);
	}

    /**
     * @Token
     * @Permission(action='branch_attendance_statistics_export')
     */
    public function store_attendance_exportAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'region_id' => 'StrLenGeLe:0,30|>>>:[region_id] params error' ,
            'piece_id' => 'Arr|>>>:[piece_id]] params error' ,
            'store_id' => 'Arr|>>>:[store_id]] params error' ,
            //'store_kind' => 'Int|>>>:[store_kind] params error' ,
            'geography_code' => 'Arr|>>>:[geography_code]] params error' ,
            'staff_state' => 'Arr|>>>:[staff_state]] params error' ,
            'start_date' => 'Required|Date|>>>:[start_time] params error' ,
            'end_date' => 'Required|Date|>>>:[end_time] params error' ,
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error' ,
        ];


        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['query_type'] = 1;
        $params['lang'] = $this->locale;
        $params['file_name'] = uniqid('attendance_branch_'.$this->locale.'_').'.xlsx';
        $attendanceStatisticsService =  new AttendanceStatisticsService();
        $data               = $attendanceStatisticsService->handleExportAttendance($params);

        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * @Token
     * @Permission(action='headeroffice_attendance_statistics_list')
	 */
	public function head_office_attendance_listAction()
	{
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            //'department_id' => 'Arr|>>>:[department_id] params error' ,
            'staff_state' => 'Arr|>>>:[staff_state]] params error' ,
            'start_date' => 'Required|Date|>>>:[start_time] params error' ,
            'end_date' => 'Required|Date|>>>:[end_time] params error' ,
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error' ,
            'department' => 'Str|>>>:[department] params error',
            'page' => 'Required|Int|IntGe:1|>>>:[page] params error' ,
            'size' => 'Required|Int|>>>:[page size] params error' ,
            'is_sub_department'  => 'Required|IntIn:0,1|>>>:is_sub_department param error' // 是否包含子部门，0 不包含 1 包含
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $attendanceStatisticsService =  new AttendanceStatisticsService();
        $attendanceStatisticsService->query_type = 2;
        $data               = $attendanceStatisticsService->getStaffsAttendanceInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
	}

    /**
     * @Token
     * @Permission(action='headeroffice_attendance_statistics_export')
     */
    public function head_office_attendance_exportAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            //'department_id' => 'Arr|>>>:[department_id] params error' ,
            'staff_state' => 'Arr|>>>:[staff_state]] params error' ,
            'start_date' => 'Required|Date|>>>:[start_time] params error' ,
            'end_date' => 'Required|Date|>>>:[end_time] params error' ,
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error' ,
            'department' => 'Str|>>>:[department] params error',
            'is_sub_department'  => 'Required|IntIn:0,1|>>>:is_sub_department param error' // 是否包含子部门，0 不包含 1 包含
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['lang'] = $this->locale;
        $params['query_type'] = 2;
        $params['file_name'] = uniqid('attendance_'.$this->locale.'_').'.xlsx';
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsService();

        $data = $attendanceStatisticsService->handleExportAttendance($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }


    /**
     *
     * @Token
     * @Permission(action='outsourcing_attendance_statistics_list')
     * @throws ValidationException
     */
    public function outsourcing_attendance_listAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'region_id' => 'StrLenGeLe:0,30|>>>:[region_id] params error' ,
            'piece_id' => 'Arr|>>>:[piece_id]] params error' ,
            'store_id' => 'Arr|>>>:[store_id]] params error' ,

            'start_date' => 'Required|Date|>>>:[start_time] params error' ,
            'end_date' => 'Required|Date|>>>:[end_time] params error' ,
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error' ,

            'page' => 'Required|Int|IntGe:1|>>>:[page] params error' ,
            'size' => 'Required|Int|>>>:[page size] params error' ,
        ];


        Validation::validate((array)$params, $validation);

        $params['staff_id']          = $this->user['id'];
        $params['staffInfo']         = $this->user;
        $attendanceStatisticsService = new AttendanceStatisticsService();
        $attendanceStatisticsService = reBuildCountryInstance($attendanceStatisticsService);
        $return['total']             = $attendanceStatisticsService->get_os_count($params);
        $return['list']              = $attendanceStatisticsService->handleOutsourcingAttendance((array)$params);
        return $this->returnJson(ErrCode::SUCCESS, '', $return);
    }

    /**
     * @Token
     * 按日导出
     * @Permission(action='outsourcing_attendance_statistics_export')
     * @throws ValidationException
     */
    public function outsourcing_attendance_exportAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'region_id' => 'StrLenGeLe:0,30|>>>:[region_id] params error' ,
            'piece_id' => 'Arr|>>>:[piece_id]] params error' ,
            'store_id' => 'Arr|>>>:[store_id]] params error' ,
            'start_date' => 'Required|Date|>>>:[start_time] params error' ,
            'end_date' => 'Required|Date|>>>:[end_time] params error' ,
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error' ,

        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $params['lang'] = $this->locale;
        $params['file_name'] = uniqid('outsourcing_attendance_'.$this->locale.'_').'.xlsx';

        $attendanceStatisticsService =  new AttendanceStatisticsService();
        $data               = $attendanceStatisticsService->handleExportOutsourcingAttendance((array)$params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    //外协员工出勤统计 横向导出

    /**
     * @Token
     * @Permission(action='os_attendance_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function os_exportAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);
        // 公共验证字段
        $validation = [
            'region_id'     => 'StrLenGeLe:0,30|>>>:[region_id] params error',
            'piece_id'      => 'Arr|>>>:[piece_id]] params error',
            'store_id'      => 'Arr|>>>:[store_id]] params error',
            'start_date'    => 'Required|Date|>>>:[start_time] params error',
            'end_date'      => 'Required|Date|>>>:[end_time] params error',
            'staff_info_id' => 'Int|>>>:[staff_info_id] params error',

        ];
        Validation::validate((array)$params, $validation);
        $params['staff_id']          = $this->user['id'];
        $params['staffInfo']         = $this->user;
        $params['lang']              = $this->locale;
        $params['file_name']         = 'outsourcing_attendance_range_' . $this->locale . '_' . date('YmdHis') . '.xlsx';
        $params['source']            = 'list_export';//每日导出 纵向
        $attendanceStatisticsService = new AttendanceStatisticsService();
        $data                        = $attendanceStatisticsService->handleExportOutsourcingAttendance((array)$params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }



    /**
     * @description: 考勤统计表
     * @author: L.J
     * @time: 2022/10/18 17:17
     * @Permission(action='attendance_statistics_report')
     * @Token
     */

    public function getAttendanceStatisticsReportListAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'getAttendanceStatisticsReportListAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'store_id' => 'Arr|>>>:[store_id]] params error' ,
            'page' => 'Required|Int|IntGe:1|>>>:[page] params error' ,
            'size' => 'Required|Int|>>>:[page size] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        [$data,$return_staff_calculate_detail]               = $attendanceStatisticsService->getAttendanceList((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, '', $data);

    }
    /**
     * @description: 考勤统计表导出
     * @author: L.J
     * @time: 2022/10/18 17:17
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function attendanceStatisticsReportExportAction()
    {
        $params = $this->request->get();
        $this->logger->info(['action'       => 'attendanceStatisticsReportExportAction',
                             'params'       => $params,
                             'action_staff' => $this->user['id'],
        ]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error',
            'end_date'   => 'Required|Date|>>>:[end_date] params error',
            'store_id' => 'Arr|>>>:[store_id]] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $attendanceStatisticsService = new AttendanceStatisticsReportService();
        $file_name                   = date('Ymd', strtotime($params['start_date'])).'_'.date('Ymd',
                strtotime($params['end_date']));
        $params['staff_id']          = $this->user['id'];
        $params['lang']              = $this->locale;
        $params['file_name']         =  'attendance_at_'.date('Ymd', strtotime($params['start_date'])).'_'.date('Ymd',
                strtotime($params['end_date'])).'_'.(time()).'.xlsx';
        $params['staffInfo']         = $this->user;

        $data = $attendanceStatisticsService->handleExportAttendance($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);

    }

    /**
     * @description:获取 实际出勤天数  on 的列表
     * @author: L.J
     * @time: 2022/10/20 20:22
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function getOnStatisticsReportListAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'getOnStatisticsReportListAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $data['list']               = $attendanceStatisticsService->getOnStatisticsReportList((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);

    }

    /**
     * @description:导出 实际出勤天数  on 的列表
     * @author: L.J
     * @time: 2022/10/20 20:22
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function  downloadONExportAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'downloadONExportAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $result               = $attendanceStatisticsService->onStatisticsReportExport((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url'=>$result['data'] ?? '']);
    }

    /**
     * @description:获取 请假列表
     * @author: L.J
     * @time: 2022/10/20 20:22
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function getLeaveListAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'getLeaveListAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
            'type' => 'Required|Str|>>>:[type] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $data['list']               = $attendanceStatisticsService->getLeaveList((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);

    }

    /**
     * @description:导出  请假列表
     * @author: L.J
     * @time: 2022/10/20 20:22
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function  downloadLeaveExportAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'downloadLeaveExportAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
            'type' => 'Required|Str|>>>:[type] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $result               = $attendanceStatisticsService->leaveExport((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url'=>$result['data'] ?? '']);
    }


    /**
     * @description:获取AB 列表
     * @author: L.J
     * @time: 2022/10/24 11:06
     * @Permission(action='attendance_statistics_report')
     * @Token
     */

    public function getABListAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'getABListAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $data['list']               = $attendanceStatisticsService->getABList((array)$params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @description:导出  AB 列表
     * @author: L.J
     * @time: 2022/10/20 20:22
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function  downloadABExportAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'downloadABExportAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $result               = $attendanceStatisticsService->ABExport((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url'=>$result['data'] ?? '']);
    }


    /**
     * @description:获取OT 列表
     * @author: L.J
     * @time: 2022/10/24 14:18
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function  getOTListAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'getABListAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $data['list']               = $attendanceStatisticsService->getOTList((array)$params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
    /**
     * @description:导出  OT 列表
     * @author: L.J
     * @time: 2022/10/20 20:22
     * @Permission(action='attendance_statistics_report')
     * @Token
     */
    public function  downloadOTExportAction(){
        $params = $this->request->get();
        $this->logger->info(['action'=>'downloadOTExportAction','params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error' ,
        ];
        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['staffInfo'] = $this->user;
        $attendanceStatisticsService =  new AttendanceStatisticsReportService();
        $result               = $attendanceStatisticsService->OTExport((array)$params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url'=>$result['data'] ?? '']);
    }



}
