<?php

namespace App\Controllers;


use App\Library\ErrCode;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Services\HrStaffManageService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;


class HrStaffManageController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 保存flash-box 管辖范围
     * @Permission(action='hr_staff_manage.jurisdiction')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     * @Token
     */
    public function saveFlashBoxAction()
    {
        $params      = $this->request->get();
        $validations['staff_info_id'] = 'Required|IntGe:1|>>>: staff_info_id  param error';
        Validation::validate($params, $validations);
        $params['operator_user']  = $this->user;
        $params['type'] = HrStaffManageDepartmentModel::TYPE_FLASH_BOX;
        $data = (new HrStaffManageService())->save($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @Permission(action='hr_staff_manage.jurisdiction')
     * @description:获取详情范围
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     * @Token
     */
    public function detailFlashBoxAction(){
        $params      = $this->request->get();
        $validations['staff_info_id'] = 'Required|IntGe:1|>>>: staff_info_id  param error';
        Validation::validate($params, $validations);
        $params['operator_user']  = $this->user;
        $params['type'] = HrStaffManageDepartmentModel::TYPE_FLASH_BOX;
        $data = (new HrStaffManageService())->getStaffRelations($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
    /**
     *
     * @description:管辖范围-编辑部门页面回显接口
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     * @Token
     */
    public function getStaffRelationsDepartmentAction(){
        $params      = $this->request->get();
        $validations['staff_info_id'] = 'Required|IntGe:1|>>>: staff_info_id  param error';
        Validation::validate($params, $validations);
        $params['operator_user']  = $this->user;
        $params['type'] = HrStaffManageDepartmentModel::TYPE_FLASH_BOX;
        $data = (new HrStaffManageService())->getStaffRelationsDepartment($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


}
