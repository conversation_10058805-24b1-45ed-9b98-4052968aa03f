<?php

namespace app\controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\DepartmentService;
use App\Services\SysStoreService;
use app\services\ToolStaffInfoService;
use App\Services\ExcelService;
use App\Services\SysService;

class ToolStaffInfoController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 创建工具号
     * @Token
     * @Permission(action='tool_staff_info.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function createToolStaffAction()
    {
        $params                = $this->request->get();
        $params['operator_id'] = $this->user['id'];
        $validation            = [
            'name'               => 'Required|StrLenGeLe:1,50|>>>:name param error',
            'node_department_id' => 'Required|Int',
            'job_title'          => 'Required|Int',
            'sys_store_id'       => 'Required|StrLenGeLe:0,12|>>>:store_id param error',
            'position_category'  => 'Required|Arr',
            'hire_date'          => 'Required|Date',
            'formal'             => 'Required|IntIn:0,1|>>>:formal param error',
        ];
        // 个人号码国家验证差异
        if (isCountry(['TH', 'VN'])) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{10}$/|>>>:mobile param error";
        } elseif (isCountry(['MY', 'LA'])) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{10,11}$/|>>>:mobile param error";
        } elseif (isCountry('PH')) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{11}$/|>>>:mobile param error";
        } elseif (isCountry('ID')) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{8,13}$/|>>>:mobile param error";
        }
        if (!empty($params['remarks'])) {
            $validation['remarks'] = 'Required|StrLenGeLe:0,500|>>>:remarks param error';
        }
        if (!empty($params['person_email'])) {
            $validation['person_email'] = 'Required|Email|>>>:person_email error';
        }
        if (!empty($params['bank_no'])) {
            $validation['bank_no'] = 'Required|StrLenGeLe:1,50|>>>:bank_no param error';
        }
        if (!empty($params['identity'])) {
            $validation['identity'] = 'Required|Regexp:/^[a-zA-Z0-9]{8,13}$/|>>>:identity param error';
        }
        if (!empty($params['address'])) {
            $validation['address'] = 'Required|StrLenGeLe:1,500|>>>:address param error';
        }


        if (!empty($params['staff_info_id_main'])) {
            $validation['staff_info_id_main'] = 'Required|Int';
        }
        if (!empty($params['mobile_company'])) {
            $validation['mobile_company'] = 'Required|Int';
        }
        Validation::validate($params, $validation);
        $result = (new ToolStaffInfoService())->createToolStaffUseLock($params);
        /**
         * @see ToolStaffInfoService::createToolStaff()
         */
        if ($result) {
            return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
        } else {
            return $this->returnJson(ErrCode::FAIL, 'error', $result);
        }
    }

    /**
     * 修改工具号信息
     * @Token
     * @Permission(action='tool_staff_info.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function updateToolStaffAction()
    {
        $params                = $this->request->get();
        $params['operator_id'] = $this->user['id'];
        $validation            = [
            'staff_info_id'      => 'Required|Int',
            'name'               => 'Required|StrLenGeLe:1,50|>>>:name param error',
            'node_department_id' => 'Required|Int',
            'job_title'          => 'Required|Int',
            'sys_store_id'       => 'Required|StrLenGeLe:0,12|>>>:store_id param error',
            'position_category'  => 'Required|Arr',
            'hire_date'          => 'Required|Date',
            'formal'             => 'Required|IntIn:0,1|>>>:formal param error',
            'staff_state'        => 'Required|IntIn:1,2,3',
        ];
        // 个人号码国家验证差异
        if (isCountry(['TH', 'VN'])) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{10}$/|>>>:mobile param error";
        } elseif (isCountry(['MY', 'LA'])) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{10,11}$/|>>>:mobile param error";
        } elseif (isCountry('PH')) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{11}$/|>>>:mobile param error";
        } elseif (isCountry('ID')) {
            $validation['mobile'] = "Required|Regexp:/^[0-9]{8,13}$/|>>>:mobile param error";
        }
        if (!empty($params['state'])) {
            if ($params['state'] == 2) {
                $validation['leave_date'] = 'Required|Date';
            } elseif ($params['state'] == 3) {
                $validation['stop_duties_date'] = 'Required|Date';
            }
        }
        if (!empty($params['remarks'])) {
            $validation['remarks'] = 'Required|StrLenGeLe:0,500|>>>:remarks param error';
        }
        if (!empty($params['staff_info_id_main'])) {
            $validation['staff_info_id_main'] = 'Required|Int';
        }
        if (!empty($params['mobile_company'])) {
            $validation['mobile_company'] = 'Required|Int';
        }
        if (!empty($params['person_email'])) {
            $validation['person_email'] = 'Required|Email|>>>:person_email error';
        }
        if (!empty($params['bank_no'])) {
            $validation['bank_no'] = 'Required|StrLenGeLe:1,50|>>>:bank_no param error';
        }
        if (!empty($params['identity'])) {
            $validation['identity'] = 'Required|Regexp:/^[a-zA-Z0-9]{8,13}$/|>>>:identity param error';
        }
        if (!empty($params['address'])) {
            $validation['address'] = 'Required|StrLenGeLe:1,500|>>>:address param error';
        }

        Validation::validate($params, $validation);
        $result = (new ToolStaffInfoService())->updateToolStaffUseLock($params);
        /**
         * @see ToolStaffInfoService::updateToolStaff()
         */
        if ($result) {
            return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
        } else {
            return $this->returnJson(ErrCode::FAIL, 'error', $result);
        }
    }

    /**
     * 列表
     * @Token
     * @Permission(action='tool_staff_info.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getListAction()
    {
        $params     = $this->request->get();
        $validation = [
            'page_num'  => 'Required|IntGe:1',
            'page_size' => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);

        $data = (new ToolStaffInfoService())->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 列表导出
     * @Token
     * @Permission(action='tool_staff_info.exportList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportListAction()
    {
        $params              = $this->request->get();
        $params['user_info'] = $this->user;
        $params['lang']      = $this->locale;
        $params['is_export'] = 1;

        //获取文件名
        $file_name   = uniqid('tool_staff_info_' . $this->locale . date('Ymd_His')) . '.xlsx';
        $action_name = 'tool_staff_info' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'dow_excel';

        //入 task 表 走队列导出
        $data = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $file_name);

        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success',
            $data['data'] ?? []);
    }

    /**
     * 角色下拉
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getRoleListAction()
    {
        $params     = $this->request->get();
        $validation = [
            'type' => 'Required|Arr',
        ];
        Validation::validate($params, $validation);

        $data = (new ToolStaffInfoService())->getRoleList($params['type']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 获取静态资源
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStaticListAction()
    {
        $data = (new ToolStaffInfoService())->getStaticList();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 重置密码
     * @Token
     * @Permission(action='tool_staff_info.reset_staff_password')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function reset_staff_passwordAction()
    {
        $params     = $this->request->get();
        $user_info  = $this->user;
        $validation = [
            'staff_info_id' => 'Required|Int',
        ];
        Validation::validate($params, $validation);
        $data = (new ToolStaffInfoService())->reset_staff_password($params['staff_info_id'], $user_info['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }
}