<?php

namespace App\Controllers;

use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\ExcelService;
use App\Services\FaceBlacklistService;
use App\Library\ErrCode;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class StaffFaceBlacklistController extends BaseController
{
    /**
     * 静态枚举
     * @Token
     */
    public function staticAction()
    {
        $data = (new FaceBlacklistService())->staticData();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 列表
     * @Token
     * @Permission(action='staff_face_blacklist')
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'identity'      => 'StrLenGeLe:0,50|>>>:[identity] params error',
            'staff_info_id' => 'IntLe:999999999|>>>:' . $this->t->_('sign_msg_021'),
            'job_title'     => 'Arr|>>>:[job_title]] params error',
            'formal'        => 'Arr|>>>:[formal] params error',
            'hire_type'     => 'Arr|>>>:[hire_type] params error',
            'page'          => 'Required|Int|IntGe:1|>>>:[page] params error',
            'size'          => 'Required|Int|>>>:[page size] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $data               = (new FaceBlacklistService())->list($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
    /**
     * 导出
     * @Token
     * @Permission(action='staff_face_blacklist_export')
     * @throws ValidationException
     * @throws \Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'identity'      => 'StrLenGeLe:0,50|>>>:[identity] params error',
            'staff_info_id' => 'IntLe:999999999|>>>:' . $this->t->_('sign_msg_021'),
            'job_title'     => 'Arr|>>>:[job_title]] params error',
            'formal'        => 'Arr|>>>:[formal] params error',
            'hire_type'     => 'Arr|>>>:[hire_type] params error',
        ];

        Validation::validate((array)$params, $validation);


        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        $params['is_export'] = 1;
        $action_name         = 'staff-blacklist'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'faceBlacklistExport';

        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, uniqid('face_blacklist_').'.xlsx',false);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 删除
     * @Token
     * @Permission(action='staff_face_blacklist_del')
     * @throws ValidationException
     * @throws BusinessException
     */
    public function deleteAction()
    {
        $params = $this->request->get();

        $validation = [
            'id' => 'Required|Int|>>>:[id] params error',
        ];

        Validation::validate((array)$params, $validation);

        $data               = (new FaceBlacklistService())->delete((array)$params,$this->user);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 导入
     * @Token
     * @Permission(action='staff_face_blacklist_import')
     * @throws ValidationException|BusinessException
     */
    public function uploadAction()
    {

        $params = (array)$this->request->get();
        Validation::validate($params, [
            'file_url' => 'Required|Str',
        ]);
        $data               = (new FaceBlacklistService())->upload($this->user['id'],$params['file_url']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 命中记录
     * @Permission(action='staff_face_blacklist_hit_record')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function hitRecordAction()
    {

        $params = $this->request->get();

        // 公共验证字段
        $validation = [
            'identity'      => 'StrLenGeLe:0,50|>>>:[identity] params error',
            'staff_info_id' => 'IntLe:999999999|>>>:' . $this->t->_('sign_msg_021'),
            'job_title'     => 'Arr|>>>:[job_title]] params error',
            'formal'        => 'Arr|>>>:[formal] params error',
            'hire_type'     => 'Arr|>>>:[hire_type] params error',
            'page'          => 'Required|Int|IntGe:1|>>>:[page] params error',
            'size'          => 'Required|Int|>>>:[page size] params error',
            'start_date'    => 'Date|>>>:[start_date] params error',
            'end_date'      => 'Date|>>>:[end_date] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $data               = (new FaceBlacklistService())->hitRecord($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 命中记录导出
     * @Permission(action='staff_face_blacklist_hit_record_export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function hitRecordExportAction()
    {

        $params = $this->request->get();

        // 公共验证字段
        $validation = [
            'identity'      => 'StrLenGeLe:0,50|>>>:[identity] params error',
            'staff_info_id' => 'IntLe:999999999|>>>:' . $this->t->_('sign_msg_021'),
            'job_title'     => 'Arr|>>>:[job_title]] params error',
            'formal'        => 'Arr|>>>:[formal] params error',
            'hire_type'     => 'Arr|>>>:[hire_type] params error',
            'start_date'    => 'Date|>>>:[start_date] params error',
            'end_date'      => 'Date|>>>:[end_date] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        $params['is_export'] = 1;
        $action_name         = 'staff-blacklist'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'hitRecordExport';

        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, uniqid('hit_record_').'.xlsx',false);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }


    /**
     * staff-face-blacklist/misjudge
     * 误判
     * @Permission(action='staff_face_blacklist_hit_record_misjudge')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function misjudgeAction()
    {
        $params = $this->request->get();
        // 公共验证字段
        $validation = [
            'id' => 'Required|IntLe:999999999|>>>:need id',
        ];

        Validation::validate((array)$params, $validation);
        $data               = (new FaceBlacklistService())->misjudge($params['id'],$this->user);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * /staff-face-blacklist/confirmHit
     * 命中 ph  my
     * @Permission(action='staff_face_blacklist_hit_record_hit_confirm')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function confirmHitAction()
    {
        $params = $this->request->get();
        // 公共验证字段
        $validation = [
            'id' => 'Required|IntLe:999999999|>>>:need id',
        ];

        Validation::validate((array)$params, $validation);
        $data               = (new FaceBlacklistService())->confirmHit($params['id'],$this->user);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 辞退  th
     * @Token
     * @Permission(action='staff_face_blacklist_hit_record_dismissed')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dismissedAction()
    {
        $params = $this->request->get();
        // 公共验证字段
        $validation = [
            'id' => 'Required|IntLe:999999999|>>>:need id',
        ];

        Validation::validate((array)$params, $validation);
        $data               = (new FaceBlacklistService())->dismissed($params['id'],$this->user);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }





}