<?php

/**
 * 犯罪记录审核 从fbi迁移至hcm
 * date：2023-07-27
 * author：zmma
 */

namespace app\controllers;

use App\Library\Enums\MessWarningEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\CriminalService;
use App\Services\ExcelService;
use App\Services\StaffService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class CriminalReviewController extends BaseController
{

    protected $userinfo;
    public function initialize()
    {
        parent::initialize();
        if (isset($this->user['id'])) {
            $this->userinfo = (new StaffService())->get_fbi_user_info($this->user['id']);
        }
    }

    /**
     * 获取基本的枚举信息
     * @return Response|ResponseInterface
     * @Token
     */
    public function sysInfoAction()
    {
        $sys_info = (new CriminalService())->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $sys_info);
    }

    /**
     * 获取 审核数据接口 支持翻页
     * @return ResponseInterface|Response
     * @Permission(action='crime.record.review')
     * @throws ValidationException
     */
    public function getListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, CriminalService::$criminal_review_list_validate);
        // 获取数据
        $params['user_info'] = $this->userinfo;
        $list = (new CriminalService())->getCriminalReviewList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * 批量更新
     * @return ResponseInterface|Response
     * @Permission(action='crime.record.review')
     * @throws ValidationException
     */
    public function importAction()
    {
        $params = $this->request->get();
        Validation::validate($params, CriminalService::$import_validate);
        $params['user_info'] = $this->userinfo;
        $result              = (new CriminalService())->import($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 上传证据
     * @return ResponseInterface|Response
     * @Permission(action='crime.record.review')
     * @throws ValidationException
     * @throws \Exception
     */
    public function importCertificateAction()
    {
        $params = $this->request->get();
        Validation::validate($params, CriminalService::$import_certificate_validate);
        $params['user_info'] = $this->userinfo;
        $result = (new CriminalService())->importCertificate($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * EXCEL 异步导出
     * @return ResponseInterface|Response
     * @Permission(action='crime.record.review')
     * @throws ValidationException
     * @throws \Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        // excel数据导出 不需要翻页参数
        unset(CriminalService::$criminal_review_list_validate['page_num'], CriminalService::$criminal_review_list_validate['page_size']);
        Validation::validate($params, CriminalService::$criminal_review_list_validate);
        $params['is_download'] = 1;
        $params['file_name']   = 'criminal-review-'.date('YmdHis').'.xlsx';
        $params['lang']        = $this->locale;
        $params['user_info']   = $this->userinfo;

        $action_name = 'criminal_data'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'exportCriminalReview';
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 犯罪记录审核 - 信息编辑
     * @return Response|ResponseInterface
     * @Permission(action='crime.record.review')
     * @throws ValidationException
     */
    public function editAction()
    {
        $params      = $this->request->get();
        $validations = [
            // 工号
            'staff_id'                       => 'Required|StrLenGeLe:1,10',
            // 审核状态 0 => 待提交 1 => 待审核 2 => 已通过 3 => 已驳回
            'review_status'                  => 'IntIn:'.MessWarningEnums::REVIEW_STATUS_0.','.MessWarningEnums::REVIEW_STATUS_1.','.MessWarningEnums::REVIEW_STATUS_2.','.MessWarningEnums::REVIEW_STATUS_3,
            // 犯罪记录是否已检查 1 => 已检查 2 => 未检查
            'record_status'                  => 'IntIn:0,1',
            // 是否有犯罪记录 1 => 有 2 => 没有
            'is_has_criminal'                => 'IntIn:'.MessWarningEnums::IS_HAS_CRIMINAL_1.','.MessWarningEnums::IS_HAS_CRIMINAL_2,
            // 驳回原因
            'reject_reason'                  => 'StrLenGeLe:0,500',
            // 犯罪记录
            'criminals'                      => 'Arr',
            // 检查证明
            'inspection_certificate'         => 'Arr|ArrLenGeLe:0,5|>>>:'.$this->t->_('inspection_certificate_max_len'),
            // 公司检查证明
            'company_inspection_certificate' => 'Arr|ArrLenGeLe:0,5|>>>:'.$this->t->_('company_inspection_certificate_max_len'),
        ];
        Validation::validate($params, $validations);
        $params['user_info'] = $this->userinfo;
        $result              = (new CriminalService())->edit($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 编辑前获取详细信息
     * @return Response|ResponseInterface
     * @Permission(action='crime.record.review')
     * @throws ValidationException
     */
    public function infoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, CriminalService::$info_validate);
        $result = (new CriminalService())->getDetailByStaffId($params['staff_id']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }
}