<?php

namespace App\Controllers;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrProbationTargetDetailModel;
use App\Models\backyard\HrProbationTargetMessageModel;
use App\Models\backyard\HrProbationTargetModel;
use App\Services\ExcelService;
use App\Services\ProbationTargetService;

class ProbationTargetController extends BaseController
{
    /**
     * 接收参数
     */
    public function receiveParameters(): array
    {
        $paramIn['staff_info_id']     = $this->request->get('staff_info_id', 'int!');
        $paramIn['staff_name']        = $this->request->get('staff_name', 'trim');
        $paramIn['department_id']     = $this->request->get('department_id', 'trim');
        $paramIn['is_sub_department'] = $this->request->get('is_sub_department', 'int!');
        $paramIn['job_title_id']      = $this->request->get('job_title_id', 'int!');
        $paramIn['store_id']          = $this->request->get('store_id', 'trim');
        $paramIn['staff_state']       = $this->request->get('staff_state', 'int!');
        $paramIn['manage_staff_id']   = $this->request->get('manage_staff_id', 'int!');
        $paramIn['hire_date_start']   = $this->request->get('hire_date_start', 'trim');
        $paramIn['hire_date_end']     = $this->request->get('hire_date_end', 'trim');
        $paramIn['setting_state']     = $this->request->get('setting_state', 'int!');
        $paramIn['send_state']        = $this->request->get('send_state', 'int!');
        $paramIn['sign_state']        = $this->request->get('sign_state', 'int!');
        $paramIn['working_country']   = $this->request->get('working_country', 'int!');
        $paramIn['page_num']          = $this->request->get('page_num', 'int!');
        $paramIn['page_size']         = $this->request->get('page_size', 'int!');
        $paramIn['tab_setting_state'] = $this->request->get('tab_setting_state', 'int!');

        return $paramIn;
    }

    /**
     * 参数验证器
     */
    public function getValidate($params): array
    {
        $return = [
            'staff_info_id'     => 'IntGeLe:0,9999999999|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'staff_info_id']),
            'staff_name'        => 'StrLenGeLe:0,200|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'staff_name']),
            'department_id'     => 'StrLenGeLe:0,50|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'department_id']),
            'is_sub_department' => 'IntIn:' . GlobalEnums::IS_SUB_DEPARTMENT_YES . ',' . GlobalEnums::IS_SUB_DEPARTMENT_NO . '|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'is_sub_department']),
            'manage_staff_id'   => 'IntGeLe:0,9999999999|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'manage_staff_id']),
            'hire_date_start'   => 'StrLenGeLe:0,10|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'hire_date_start']),
            'hire_date_end'     => 'StrLenGeLe:0,10|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'hire_date_end']),
            'setting_state'     => 'IntGeLe:0,100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'setting_state']),
            'send_state'        => 'IntGeLe:0,100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'send_state']),
            'sign_state'        => 'IntGeLe:0,100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'sign_state']),
            'tab_setting_state'        => 'Required|IntGeLe:1,2|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'tab_setting_state']),
        ];

        if (!empty($params['job_title_id'])) {
            $return['job_title_id']    = 'ArrLenLe:100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'job_title_id']);
            $return['job_title_id[*]'] = 'IntGeLe:0,9999999999|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'job_title_id']);
        }

        if (!empty($params['store_id'])) {
            $return['store_id']    = 'ArrLenLe:100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'store_id']);
            $return['store_id[*]'] = 'StrLenGeLe:0,100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'store_id']);
        }

        if (!empty($params['staff_state'])) {
            $return['staff_state']    = 'ArrLenLe:10|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'staff_state']);
            $return['staff_state[*]'] = 'IntGeLe:0,10000|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'staff_state']);
        }

        return $return;
    }

    /**
     * 目标列表
     * @Token
     * @Permission(action='not_frontline_probation_target.list')
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->receiveParameters();

        Validation::validate($params, $this->getValidate($params));
        $params['user_id'] = $this->user['id'];

        $probationService = new ProbationTargetService();
        $list             = $probationService->getList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * 列表导出
     * @Token
     * @Permission(action='not_frontline_probation_target.export_list')
     * @throws ValidationException
     */
    public function exportListAction()
    {
        $params = $this->receiveParameters();

        Validation::validate($params, $this->getValidate($params));

        $params['file_name'] = 'probation_target_' . date('YmdHis') . '_' . $this->user['id'] . '.xlsx';
        $params['lang']      = $this->locale;
        $params['user_id']   = $this->user['id'];

        $actionName = 'probation-target' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'exportList';

        $result = (new ExcelService())->insertTask($this->user['id'], $actionName, $params, HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);

        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }

        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 目标保存
     * @Token
     * @Permission(action='not_frontline_probation_target.save')
     */
    public function saveAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');
        $params['stage']         = $this->request->get('stage', 'int!');
        $params['duty_info']     = $this->request->get('duty_info', 'trim');
        $params['target_info']   = $this->request->get('target_info', 'trim');

        $validate = [
            'staff_info_id' => 'IntGeLe:1,9999999999|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'staff_info_id']),
            'duty_info'     => 'StrLenGeLe:0,10000|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'duty_info']),
            'stage'         => 'IntIn:' . implode_array_keys(HrProbationTargetDetailModel::$stageList) . '|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'stage']),
        ];

        if (!empty($params['target_info'])) {
            $validate['target_info']           = 'ArrLenLe:100|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'target_info']);
            $validate['target_info[*].name']   = 'StrLenGeLe:0,10000|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'name']);
            $validate['target_info[*].info']   = 'StrLenGeLe:0,10000|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'info']);
            $validate['target_info[*].weight'] = 'StrLenGeLe:0,3|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'weight']);
        }

        Validation::validate($params, $validate);

        $params['user_id'] = $this->user['id'];

         (new ProbationTargetService())->saveUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 目标保存并发送
     * @Token
     * @Permission(action='not_frontline_probation_target.send')
     */
    public function saveSendAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');
        $params['stage']         = $this->request->get('stage', 'int!');
        $params['duty_info']     = $this->request->get('duty_info', 'trim');
        $params['target_info']   = $this->request->get('target_info', 'trim');

        $validate = [
            'staff_info_id'         => 'Required|IntGeLe:1,9999999999|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'staff_info_id']),
            'duty_info'             => 'Required|StrLenGeLe:1,10000|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'duty_info']),
            'stage'                 => 'Required|IntIn:' . implode_array_keys(HrProbationTargetDetailModel::$stageList) . '|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'stage']),
            'target_info'           => 'Required|ArrLenLe:100|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'target_info']),
            'target_info[*].name'   => 'Required|StrLenGeLe:1,10000|>>>:'. $this->t->_('input_param_nonstandard', ['field' => 'name']),
            'target_info[*].info'   => 'Required|StrLenGeLe:1,10000|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'info']),
            'target_info[*].weight' => 'Required|IntGeLe:1,100|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'weight']),
        ];

        Validation::validate($params, $validate);

        $params['user_id'] = $this->user['id'];

        (new ProbationTargetService())->saveSendUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 获取试用期目标详情接口
     *
     * 用于获取员工的试用期目标设定详细信息，包括：
     * - 员工基本信息
     * - 目标设定内容
     * - 签字状态信息
     * - 评估阶段信息
     * @Token
     * @throws ValidationException 参数验证失败时抛出异常
     * @throws BusinessException 业务逻辑处理失败时抛出异常
     */
    public function detailAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');
        $params['stage']         = $this->request->get('stage', 'int!');

        $validate = [
            'staff_info_id' => 'IntGeLe:0,9999999999|>>>:' . $this->t->_('input_param_nonstandard',
                    ['field' => 'staff_info_id']),
            'stage'         => 'IntGeLe:0,2|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'stage']),
        ];

        Validation::validate($params, $validate);

        $params['user_id'] = $this->user['id'];
        $data              = (new ProbationTargetService())->detail($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 发送目标
     * @Token
     * @Permission(action='not_frontline_probation_target.send')
     * @throws ValidationException 参数验证失败时抛出异常
     * @throws BusinessException 业务逻辑处理失败时抛出异常
     */
    public function sendAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');

        if (empty($params['staff_info_id'])) {
            throw new ValidationException($this->t->_('operation_data_does_no_exist'));
        }

        $params['user_id'] = $this->user['id'];
        (new ProbationTargetService())->sendUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 目标取消
     * @Token
     * @Permission(action='not_frontline_probation_target.send')
     */
    public function cancelAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');

        if (empty($params['staff_info_id'])) {
            throw new ValidationException($this->t->_('operation_data_does_no_exist'));
        }

        $params['user_id'] = $this->user['id'];
        (new ProbationTargetService())->cancelUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }


    /**
     * 变更记录下拉
     * 获取并返回试用期目标相关的变更记录列表
     * @Token
     * @Permission(action='not_frontline_probation_target.record')
     * @throws ValidationException 参数验证失败时抛出异常
     * @throws BusinessException 业务逻辑处理失败时抛出异常
     */
    public function logListAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');

        if (empty($params['staff_info_id'])) {
            throw new ValidationException($this->t->_('operation_data_does_no_exist'));
        }

        $params['user_id'] = $this->user['id'];
        $data              = (new ProbationTargetService())->logList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 变更记录详情
     * @Token
     * @Permission(action='not_frontline_probation_target.record')
     */
    public function logDetailAction()
    {
        $params['target_business_id'] = $this->request->get('target_business_id', 'int!');

        if (empty($params['target_business_id'])) {
            throw new ValidationException($this->t->_('operation_data_does_no_exist'));
        }

        $params['user_id'] = $this->user['id'];
        $data              = (new ProbationTargetService())->logDetail($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', (object)$data);
    }

    /**
     * 目标下载
     * @Token
     * @Permission(action='not_frontline_probation_target.down')
     * @throws ValidationException 参数验证失败时抛出异常
     * @throws BusinessException 业务逻辑处理失败时抛出异常
     */
    public function downloadAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');

        $validate = [
            'staff_info_id' => 'IntGeLe:0,9999999999|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'staff_info_id']),
        ];

        Validation::validate($params, $validate);

        $params['user_id'] = $this->user['id'];
        $data              = (new ProbationTargetService())->download($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 消息审批 - 不是审批流
     * 调整后和调整前分开展示
     * @Token
     * @Permission(action='not_frontline_probation_target.list')
     * @throws ValidationException
     * @throws BusinessException
     */
    public function msgLogAction()
    {
        $params['staff_info_id'] = $this->request->get('staff_info_id', 'int!');

        if (empty($params['staff_info_id'])) {
            throw new ValidationException($this->t->_('operation_data_does_no_exist'));
        }

        $params['user_id'] = $this->user['id'];
        $data              = (new ProbationTargetService())->msgLog($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', (object)$data);
    }

    /**
     * 红点接口
     * @Token
     */
    public function redCountAction()
    {
        $params['user_id'] = $this->user['id'];
        $data              = (new ProbationTargetService())->redCount($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
}