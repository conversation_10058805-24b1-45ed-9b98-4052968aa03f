<?php
/**
 * Author: Bruce
 * Date  : 2023-02-23 17:31
 * Description:
 */

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\AttendanceRecordService;

class AttendanceRecordController extends BaseController
{
    /**
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function sysInfoAction()
    {
        $result = (new AttendanceRecordService())->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     * @Permission(action='clock_in_recort_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getListAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'page' => 'IntGt:0',
            'page_size' => 'IntGt:0',
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
        ];
        Validation::validate($params, $validate_rule);

        $params['user_id'] = $this->user['id'];
        $params['is_check_data_num'] = false;

        $result = (new AttendanceRecordService())->getListInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     * @Permission(action='clock_in_recort_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function  getListExportAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'start_date' => 'Required|Date|>>>:[start_date] params error' ,
            'end_date' => 'Required|Date|>>>:[end_date] params error' ,
        ];
        Validation::validate($params, $validate_rule);

        $params['user_id'] = $this->user['id'];
        $params['lang']        = $this->locale;
        $params['file_name']   = uniqid('attendance_record_list_export_'.$this->locale.'_').'.xlsx';

        /**
         * @see AttendanceRecordService::getListInfoExport
         */
        // 加锁处理
        $get_result = (new AttendanceRecordService())->getListInfoExportUseLock($params);

        return $this->returnJson($get_result['code'], $this->t['file_download_tip'], $get_result['data']);
    }

    /**
     * 获取考勤扫脸记录列表
     * @Token
     * @Permission(action='attendance_face_record_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getFaceListAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'page_num' => 'Required|IntGt:0',
            'page_size' => 'Required|IntGt:0',
            'attendance_date_start' => 'Required|Date|>>>:[attendance_date_start] params error',
            'attendance_date_end' => 'Required|Date|>>>:[attendance_date_end] params error',
        ];
        Validation::validate($params, $validate_rule);

        // 检查日期范围不能超过一个月
        if (strtotime($params['attendance_date_end']) - strtotime($params['attendance_date_start']) > 86400 * 30) {
            throw new ValidationException($this->t->_('date_span_limit', ['num' => 30]));
        }

        $params['user_id'] = $this->user['id'];

        $result = (new AttendanceRecordService())->getFaceList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 考勤扫脸记录导出
     * @Token
     * @Permission(action='attendance_face_record_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getFaceListExportAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'attendance_date_start' => 'Required|Date|>>>:[attendance_date_start] params error',
            'attendance_date_end' => 'Required|Date|>>>:[attendance_date_end] params error',
        ];
        Validation::validate($params, $validate_rule);

        $params['user_id'] = $this->user['id'];
        $params['lang'] = $this->locale;
        $params['file_name'] = uniqid('attendance_face_record_export_'.$this->locale.'_').'.xlsx';

        /**
         * @see AttendanceRecordService::getFaceListExport
         */
        // 加锁处理
        $get_result = (new AttendanceRecordService())->getFaceListExportUseLock($params);

        return $this->returnJson($get_result['code'], $this->t['file_download_tip'], $get_result['data']);
    }
}
