<?php
/**
 * Author: Bruce
 * Date  : 2025-04-28 11:44
 * Description:
 */

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\VehicleInspectionService;

class VehicleInspectionController extends BaseController
{

    /**
     * 快递员车辆稽查-列表
     * @Token
     * @Permission(action='express_vehicle_inspection')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function courierListAction()
    {
        $params = $this->request->get();

        $validate_rule = [
            'page_num'  => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];
        Validation::validate($params, $validate_rule);

        $params['user_id'] = $this->user['id'];

        $vehicleInspectionService = new VehicleInspectionService();

        $result = $vehicleInspectionService->getCourierList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 快递员车辆稽查-导出
     * @Token
     * @Permission(action='express_vehicle_inspection')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function courierExportAction()
    {
        $params              = $this->request->get();
        $params['file_name'] = uniqid('vehicle_inspection_' . date('YmdHis') . $this->locale . '_') . '.xlsx';
        $params['lang']      = $this->locale;
        $params['staff_id']  = $this->user['id'];
        // 加锁处理
        (new VehicleInspectionService())->courierExportUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], []);
    }

    /**
     * 快递员车辆稽查-查看
     * @Token
     * @Permission(action='express_vehicle_inspection')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function courierCheckAction()
    {
        $params     = $this->request->get();
        $validation = [
            "id" => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $params['staff_id'] = $this->user['id'];

        $data = (new VehicleInspectionService())->courierCheck($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * 快递员车辆稽查-获取下载模板
     * @Token
     * @Permission(action='express_vehicle_inspection')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function importTemplateAction()
    {
        $data = (new VehicleInspectionService())->importTemplate();

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * 快递员车辆稽查-导入
     * @Token
     * @Permission(action='express_vehicle_inspection')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function courierImportAction()
    {
        $params     = $this->request->get();
        $validation = [
            "file_name" => "Required|StrLenGe:1",
            "file_url"  => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $params['lang']     = $this->request->getBestLanguage();
        $params['staff_id'] = $this->user['id'];

        (new VehicleInspectionService())->courierImportUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t['async_upload_tip'], []);
    }

    /**
     * 枚举
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @Token
     */
    public function sysInfoAction()
    {
        $data = (new VehicleInspectionService())->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * 快递员车辆稽查-审核
     * @Token
     * @Permission(action='express_vehicle_inspection')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function submitAction()
    {
        $params              = $this->request->get();
        $validation = [
            "id"                => "Required|StrLenGe:1",
            "video_status"      => "Required|StrLenGe:1",
            "plate_status"      => "StrLenGe:0",
            "mileage_status"    => "StrLenGe:0",
            "carriage_type"     => "StrLenGe:0",
            "audit_mileage_num" => "StrLenGe:0",
        ];
        Validation::validate($params, $validation);
        $params['staff_id']  = $this->user['id'];
        $params['staff_name']  = $this->user['name'];
        // 加锁处理
        (new VehicleInspectionService())->submitUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', []);
    }
}