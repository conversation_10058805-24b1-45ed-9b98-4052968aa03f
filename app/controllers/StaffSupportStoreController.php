<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\OssService;
use App\Services\StaffSupportStoreServer;
use Exception;

class StaffSupportStoreController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * * 导入员工支援网点
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function importStaffSupportAction()
    {
        $files = $this->request->getUploadedFiles()[0];
        if (empty($files)) {
            throw new ValidationException('file not exist');
        }
        $extension = $files->getExtension();
        if ($extension !== "xlsx") {
            throw new ValidationException('file extension error');
        }
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);
        // 读取文件 0工号,1支援网点id,2支援职位,3开始日期,4结束日期,5班次
        $excel_data = $excel->openFile($files->getTempName())
            ->openSheet()
            ->setSkipRows(1)
            ->setType([
                \Vtiful\Kernel\Excel::TYPE_INT,
                \Vtiful\Kernel\Excel::TYPE_STRING,
                \Vtiful\Kernel\Excel::TYPE_STRING,
                \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                \Vtiful\Kernel\Excel::TYPE_STRING,
            ])
            ->getSheetData();

        if (count($excel_data) < 1) {
            throw new ValidationException($this->t->_('file_content_is_null'));

        }

        if (count($excel_data) > 100) {
            throw new ValidationException($this->t->_('file_download_limit',['num'=>100]));
        }
        $tmpPath = sys_get_temp_dir() . '/' . time() . '.xlsx';
        file_put_contents($tmpPath, file_get_contents($files->getTempName()));
        $uploadFile = OssHelper::uploadFile($tmpPath);
        if (empty($uploadFile['object_url'])) {
            $this->logger->error(json_encode($uploadFile));
            throw new ValidationException($this->t->_('upload_fail'));
        }
        $params['operator_id'] = $this->user['id']; //操作人工号
        $params['file_name']   = $files->getName(); //上传文件名称
        $params['excel_data']  = $excel_data;
        $params['import_path'] = $uploadFile['object_url'];
        $date                  = (new StaffSupportStoreServer())->addStaffSupportExcel($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 导入支援历史数据
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function importStaffSupportHistoryListAction()
    {
        $params      = $this->request->get();
        $validations = [
            'page_num'  => 'Required|IntGe:1',
            'page_size' => 'Required|IntGe:1',
            'type'      => 'Required|Int|IntIn:1,2',
        ];
        Validation::validate($params, $validations);
        $params['user']       = $this->user;
        $staff_support_server = new StaffSupportStoreServer();
        $date                 = $staff_support_server->getImportHistoryList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 删除历史数据
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delImportHistoryAction()
    {
        $params = $this->request->get();
        $validations =  [
            'del_ids' => 'Required|Arr',
            'del_ids[*]' => 'Required|Int',
        ];
        Validation::validate($params, $validations);
        $staff_support_server = new StaffSupportStoreServer();
        $params['user'] = $this->user;
        $result = $staff_support_server->delImportHistory($params);
        if($result) {
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }

    /**
     * 员工支援列表
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function supportListAction()
    {
        $params = $this->request->get();
        $staff_support_server = new StaffSupportStoreServer();

        $validations = $staff_support_server->staffSupportListParamsValidations();
        $validations = array_merge($validations,
            ['page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:1']);
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);
        Validation::validate($params, $validations);
        $date = $staff_support_server->getStaffSupportList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 员工支援列表导出
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportStaffSupportAction()
    {
        $params               = $this->request->get();
        $staff_support_server = new StaffSupportStoreServer();
        $validations          = $staff_support_server->staffSupportListParamsValidations();
        $params               = array_only($params, array_keys($validations));
        $params               = array_filter($params);
        Validation::validate($params, $validations);
        $params['lang']        = $this->locale;
        $params['staff_id']    = $this->user['id'];
        $date = $staff_support_server->getStaffSupportList(array_merge($params,['page_num'=>1,'page_size'=>1]));
        // 限制50000
        if (isset($date['paginate']['total_count']) && $date['paginate']['total_count'] > 50000){
            return $this->returnJson(ErrCode::SYSTEM_ERROR, $this->t->_('export_staff_support_error_1'), []);
        }
        $params['file_name']   = 'staff_support_store_export';
        $params['is_download'] = 1;
        $data                  = $staff_support_server->addExportStaffSupportUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * 编辑员工支援信息
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function editStaffSupportAction()
    {
        $params = $this->request->get();
        $validations =  [
            'id' => 'Required|Int',
            'staff_info_id' => 'Required|Int',
            'serial_no' => 'Str',
            'support_begin_date' => 'Required|Date',
            'support_end_date' => 'Required|Date',
            'shift_id' => 'Required|Int',
            'is_stay' => 'Required|Int'
        ];
        Validation::validate($params, $validations);
        $params['user'] = $this->user;
        $staff_support_server = new StaffSupportStoreServer();
        $result = $staff_support_server->editStaffSupport($params);
        if($result) {
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }

    /**
     * 取消员工支援
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function cancelStaffSupportAction()
    {
        $params = $this->request->get();
        $validations =  [
            'id' => 'Required|Int',
            'staff_info_id' => 'Required|Int',
            'serial_no' => 'Str'
        ];
        Validation::validate($params, $validations);
        $staff_support_server = new StaffSupportStoreServer();
        $params['user'] = $this->user;
        $result = $staff_support_server->cancelStaffSupport($params);
        if($result) {
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }

    /**
     * 静态资源
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function sysInfoAction()
    {
        $staff_support_server = new StaffSupportStoreServer();
        $date = $staff_support_server->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 员工搜索
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchStaffAction()
    {
        $params = $this->request->get();
        $validations = [
            'search_staff' => 'Required|StrLenGeLe:1,20|>>>:' . $this->t->_('support_store_error_1'),
            'page_size' => 'Int'
        ];
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);
        Validation::validate($params, $validations);
        $staff_support_server = new StaffSupportStoreServer();
        $date = $staff_support_server->searchStaff($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 员工搜索
     * @Token
     * @Permission(action='import.staff.support.manage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getOperateLogsAction()
    {
        $params      = $this->request->get();
        $validations = [
            'id'   => 'Int',
            'type' => 'Required|Int|IntIn:1,2',
        ];
        Validation::validate($params, $validations);
        $staff_support_server = reBuildCountryInstance(new StaffSupportStoreServer());
        $date                 = $staff_support_server->getOperateLogs($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }
}