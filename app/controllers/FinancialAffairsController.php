<?php

namespace App\Controllers;


use App\Library\ErrCode;
use App\Services\FinancialAffairsService;

class FinancialAffairsController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 未回公款员工信息
     * @Token
     * @Permission(action='financial-affairs.notreturnedlist')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getNotReturnedPublicFundsListAction()
    {
        $formal = $this->request->get('formal', 'int', 1);
        $result = (new FinancialAffairsService())->uncollectedPayment($formal);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
    }

    /**
     * 未回公款员工信息-导出
     * @Token
     * @Permission(action='financial-affairs.notreturnedexport')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function notReturnedPublicFundsInfoExportAction()
    {
        $formal = $this->request->get('formal', 'int', 1);
        $result = (new FinancialAffairsService())->exportUncollectedPayment($formal);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
    }
}