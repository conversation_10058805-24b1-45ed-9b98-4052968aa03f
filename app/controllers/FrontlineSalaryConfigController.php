<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\FrontlineSalaryRuleConfigModel;
use App\Modules\My\Services\SalaryService;
use App\Services\FrontlineSalaryConfigService;
use App\Services\SettingEnvService;
use app\services\SpecialCarriageService;

class FrontlineSalaryConfigController extends BaseController
{
    /**
     * 特殊网点配置列表
     * @Token
     * @Permission(action='frontline_salary_config.store')
     * @throws \Exception
     */
    public function specialStoreListAction()
    {
        $data = (new FrontlineSalaryConfigService)->getSpecialStoreList();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 编辑特殊网点配置
     * @Token
     * @Permission(action='frontline_salary_config.store')
     * @throws \Exception
     */
    public function editSpecialStoreAction()
    {
        $params['operate_id'] = $this->user['id'];
        $params['store']      = $this->request->get('store');
        $params['id']         = $this->request->get('id');
        Validation::validate($params, [
            "store" => "Required|StrLenGeLe:1,2000",
        ]);
        $params['store'] = clearSpecialChar($params['store']);
        (new FrontlineSalaryConfigService)->editSpecialStore($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 新增特殊网点配置
     * @Token
     * @Permission(action='frontline_salary_config.store')
     * @throws \Exception
     */
    public function createSpecialStoreAction()
    {
        $params['operate_id'] = $this->user['id'];
        $params['rule_name']  = $this->request->get('rule_name');
        $params['store']      = $this->request->get('store');
        $validation           = [
            "rule_name" => "Required|StrLenGeLe:1,50",
            "store"     => "Required|StrLenGeLe:1,2000",
        ];
        Validation::validate($params, $validation);
        $params['store'] = clearSpecialChar($params['store']);
        (new FrontlineSalaryConfigService)->editSpecialStore($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 删除特殊网点配置
     * @Token
     * @Permission(action='frontline_salary_config.store')
     * @throws \Exception
     */
    public function deleteSpecialStoreAction()
    {
        $params['operate_id'] = $this->user['id'];
        $params['id']         = $this->request->get('id');
        (new FrontlineSalaryConfigService)->deleteSpecialStore($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }
    /**
     * 薪资规则配置列表
     * @Token
     * @throws \Exception
     */
    public function salaryRuleConfigListAction()
    {
        $salaryItem = $this->request->get('salary_item');
        $jotTitle   = $this->request->get('job_title');
        $state      = $this->request->get('state');
        $pageSize   = $this->request->get('page_size', 'trim', 20);
        $pageNum    = $this->request->get('page_num', 'trim', 1);
        if (isCountry() && $salaryItem && array_diff($salaryItem, FrontlineSalaryRuleConfigModel::$salaryItemTypeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Salary Item params error');
        }
        if (isCountry('PH') && $salaryItem && array_diff($salaryItem, FrontlineSalaryRuleConfigModel::$salaryItemTypePHMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Salary Item params error');
        }
        if (isCountry() && $state && array_diff($state, FrontlineSalaryRuleConfigModel::$stateMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'state params error');
        }
        /**
         * @var  $service FrontlineSalaryConfigService
         */
        $service = reBuildCountryInstance(new FrontlineSalaryConfigService());
        $data    = $service->salaryRuleConfigList([
            'salary_item_type' => $salaryItem,
            'job_title'        => $jotTitle,
            'state'            => $state,
            'page_size'        => empty($pageSize) ? 20 : $pageSize,
            'page_num'         => empty($pageNum) ? 1 : $pageNum,
        ]);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 薪资规则配置导出
     * @Token
     * @throws \Exception
     */
    public function salaryRuleConfigExportAction()
    {
        $salaryItem = $this->request->get('salary_item');
        $jotTitle   = $this->request->get('job_title');
        $state      = $this->request->get('state');
        if (isCountry() && $salaryItem && array_diff($salaryItem, FrontlineSalaryRuleConfigModel::$salaryItemTypeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Salary Item params error');
        }
        if (isCountry() && $state && array_diff($state, FrontlineSalaryRuleConfigModel::$stateMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'state params error');
        }
        /**
         * @var  $service FrontlineSalaryConfigService
         */
        $service = reBuildCountryInstance(new FrontlineSalaryConfigService());
        $list    = $service->salaryRuleConfigList([
            'salary_item_type' => $salaryItem,
            'job_title'        => $jotTitle,
            'state'            => $state,
            'export'           => true,
        ]);
        $url     = $service->getSalaryRuleConfigExportUrl($list);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 薪资规则配置批量导入模板
     * @Token
     */
    public function salaryRuleConfigBatchImportTemplateAction()
    {
        $code = 'upload_fl_salary_tpl';
        if (!isCountry() && $this->locale == 'en') {
            $code = 'upload_fl_salary_tpl_en';
        }
        $url = (new SettingEnvService)->getSetVal($code);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 薪资规则配置批量导入(实时)
     * @Token
     * @throws \Exception
     */
    public function salaryRuleConfigBatchImportAction()
    {
        $operateId = $this->user['id'] ?? 0;
        $fileUrl   = $this->request->get('path');
        if (empty($fileUrl)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found file url');
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl);
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'read file error');
        }
        /**
         * @var $service FrontlineSalaryConfigService
         */
        $service = reBuildCountryInstance(new FrontlineSalaryConfigService());
        $result  = $service->salaryRuleConfigBatchImport($tmpFilePath, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'Upload success', $result);
    }

    /**
     * 薪资规则配置状态更改
     * @Token
     * @throws \App\Library\Exception\BusinessException
     */
    public function salaryRuleConfigStateUpdateAction()
    {
        $id    = $this->request->get('id');
        $state = $this->request->get('state');
        if ($state && !in_array($state, FrontlineSalaryRuleConfigModel::$stateMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'params error');
        }
        $service   = new FrontlineSalaryConfigService();
        $operateId = $this->user['id'] ?? 0;
        $service->salaryRuleConfigStateUpdate($id, $state, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 薪资规则配置删除
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function salaryRuleConfigDeleteAction()
    {
        $id        = $this->request->get('id');
        $service   = new FrontlineSalaryConfigService();
        $operateId = $this->user['id'] ?? 0;
        $service->salaryRuleConfigDelete($id, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 府最低薪资配置列表（无分页）
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function governmentMinSalaryConfigListAction()
    {
        $provinceName  = $this->request->get('province_name');
        $effectiveDate = $this->request->get('effective_date');
        $service       = new FrontlineSalaryConfigService();
        $data          = $service->getGovernmentMinSalaryConfigList($provinceName, $effectiveDate);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 府最低薪资配置 生效日期列表
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function governmentMinSalaryConfigEffectiveDateAction()
    {
        $service          = new FrontlineSalaryConfigService();
        $effectiveDateMap = $service->getGovernmentMinSalaryConfigEffectiveDate();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $effectiveDateMap);
    }

    /**
     * 府最低薪资配置列表导出
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function governmentMinSalaryConfigExportAction()
    {
        $provinceName  = $this->request->get('province_name');
        $effectiveDate = $this->request->get('effective_date');
        $service       = new FrontlineSalaryConfigService();
        $data          = $service->getGovernmentMinSalaryConfigList($provinceName, $effectiveDate);
        $url           = $service->getGovernmentMinSalaryConfigExportUrl($data);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 府最低薪资配置批量导入(实时)
     * @permission(action='frontline_salary_config.province')
     * @Token
     * @throws \Exception
     */
    public function governmentMinSalaryConfigBatchImportAction()
    {
        $operateId = $this->user['id'] ?? 0;
        $fileUrl   = $this->request->get('path');
        if (empty($fileUrl)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found file url');
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl);
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'read file error');
        }
        $service = new FrontlineSalaryConfigService();
        $result  = $service->governmentMinSalaryConfigBatchImport($tmpFilePath, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'Upload success', $result);
    }

    /**
     * 府最低薪资配置批量导入模板
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function governmentMinSalaryConfigBatchImportTemplateAction()
    {
        $url = (new SettingEnvService)->getSetVal('upload_province_base_salary_tpl');
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * Bike车补配置导入模板
     * @Token
     */
    public function bikeSubsidyConfigImportTemplateAction()
    {
        $type = $this->request->get('type');

        if ($type == 'store') {
            $url = (new SettingEnvService)->getSetVal('bike_subsidy_store_tpl');
        } else {
            $url = (new SettingEnvService)->getSetVal('bike_subsidy_province_tpl');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * Bike车补配置列表
     * @Token
     */
    public function bikeSubsidyConfigListAction()
    {
        $storeIds = $this->request->get('store_ids');
        $pageSize = $this->request->get('page_size', 'trim', 20);
        $pageNum  = $this->request->get('page_num', 'trim', 1);
        $service  = new FrontlineSalaryConfigService();
        $data     = $service->getBikeSubsidyConfigList([
            'store_ids' => $storeIds,
            'page_size' => empty($pageSize) ? 20 : $pageSize,
            'page_num'  => empty($pageNum) ? 1 : $pageNum,
        ]);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * Bike车补配置导出
     * @Token
     */
    public function bikeSubsidyConfigExportAction()
    {
        $storeIds = $this->request->get('store_ids');
        $service  = new FrontlineSalaryConfigService();
        $data     = $service->getBikeSubsidyConfigList(['export' => true, 'store_ids' => $storeIds]);
        $url      = $service->getBikeSubsidyConfigExportUrl($data);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * Bike车补配置导入(实时)
     * @Token
     * @throws \Exception
     */
    public function bikeSubsidyConfigImportAction()
    {
        $fileUrl = $this->request->get('path');
        $type    = $this->request->get('type');
        if (!in_array($type, ['store', 'province'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        if (empty($fileUrl)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found file url');
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl);
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'read file error');
        }
        $operateId = $this->user['id'] ?? 0;
        $service   = new FrontlineSalaryConfigService();
        if ($type == 'province') {
            $result = $service->provinceBikeSubsidyConfigImport($tmpFilePath, $operateId);
        } else {
            $result = $service->storeBikeSubsidyConfigImport($tmpFilePath, $operateId);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'Upload success', $result);
    }

    /**
     * Bike车补配置清空
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function bikeSubsidyConfigClearAction()
    {
        $id        = $this->request->get('id');
        $service   = new FrontlineSalaryConfigService();
        $operateId = $this->user['id'] ?? 0;
        $service->bikeSubsidyConfigClear($id, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 其他配置展示
     * @Token
     */
    public function otherConfigShowAction()
    {
        $service = new FrontlineSalaryConfigService();
        $data    = $service->otherConfigShowData(false);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 其他配置保存
     * @Token
     * @throws \App\Library\Exception\BusinessException
     */
    public function otherConfigSaveAction()
    {
        $otherKey   = $this->request->get('key');
        $otherValue = $this->request->get('val');
        $service    = new FrontlineSalaryConfigService();
        if (!in_array($otherKey, $service->otherSettingKeysEdit)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found');
        }
        $service->otherConfigSave($otherKey, $otherValue);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-列表（无分页）
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function citySalaryConfigListAction()
    {
        $service = new FrontlineSalaryConfigService();
        $data    = $service->getCitySalaryConfigList();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 特殊县薪资配置-新增
     * @permission(action='frontline_salary_config.province')
     * @Token
     * @throws ValidationException|BusinessException
     */
    public function specialCitySalaryConfigAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'city_code'      => 'Required|StrLen:6|>>>: city code error',
            'base_salary'    => 'Required|IntGeLe:0,99999|>>>: salary error',
            'effective_date' => 'Required|Date|>>>: effective date error',
        ]);
        $service              = new FrontlineSalaryConfigService();
        $params['operate_id'] = $this->user['id'];
        $service->specialCitySalaryConfigAdd($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-导出
     * @permission(action='frontline_salary_config.province')
     * @Token
     * @throws BusinessException
     */
    public function specialCitySalaryConfigExportAction()
    {
        $service = new FrontlineSalaryConfigService();
        $data    = $service->specialCitySalaryConfigExport();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 特殊县薪资配置-修改
     * @permission(action='frontline_salary_config.province')
     * @Token
     * @throws BusinessException|ValidationException
     */
    public function specialCitySalaryConfigEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'city_code'      => 'Required|StrLen:6|>>>: city code error',
            'base_salary'    => 'Required|IntGeLe:0,99999|>>>: salary error',
            'effective_date' => 'Required|Date|>>>: effective date error',
            'edit_reason'    => 'Required|StrLenGeLe:0,200|>>>: edit reason error',
        ]);
        $service              = new FrontlineSalaryConfigService();
        $params['operate_id'] = $this->user['id'];
        $service->specialCitySalaryConfigEdit($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-启用/禁用
     * @permission(action='frontline_salary_config.province')
     * @Token
     * @throws BusinessException
     * @throws ValidationException
     */
    public function specialCitySalaryConfigStateEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id'    => 'Required|Int|>>>: id error',
            'state' => 'Required|IntIn:1,2|>>>: state error',
        ]);
        $params['operate_id'] = $this->user['id'];
        $service              = new FrontlineSalaryConfigService();
        $service->specialCitySalaryConfigStateEdit($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-变更记录
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function specialCitySalaryConfigLogsAction()
    {
        $cityCode = $this->request->get('city_code');
        $service  = new FrontlineSalaryConfigService();
        $data     = $service->specialCitySalaryConfigLogs($cityCode);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 特殊县薪资配置-县名称搜索
     * @permission(action='frontline_salary_config.province')
     * @Token
     */
    public function specialCitySalaryConfigNameSearchAction()
    {
        $cityCode = $this->request->get('city_code');
        $service  = new FrontlineSalaryConfigService();
        $data     = $service->specialCitySalaryConfigNameSearch($cityCode);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 一线员工薪资配置-配置特殊无车厢名单-列表
     * @permission(action='action.frontline_salary_config.van_carriage.detail')
     * @Token
     */
    public function getSpecialNonCarriageListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'page_num'  => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ]);

        $service      = new SpecialCarriageService();
        $listResponse = $service->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $listResponse);
    }

    /**
     * 一线员工薪资配置-配置特殊无车厢名单-导出
     * @permission(action='action.frontline_salary_config.van_carriage.detail')
     * @Token
     */
    public function exportSpecialNonCarriageListAction()
    {
        $params = $this->request->get();
        $service = new SpecialCarriageService();
        $data    = $service->export($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 一线员工薪资配置-配置特殊无车厢名单-查看日志
     * @permission(action='action.frontline_salary_config.van_carriage.detail')
     * @Token
     */
    public function getSpecialNonCarriageOperateLogAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'staff_info_id'        => 'Int|>>>: staff_info_id error',
        ]);
        $service = new SpecialCarriageService();
        $data    = $service->getOperateLog($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 一线员工薪资配置-配置特殊无车厢名单-导入
     * @permission(action='action.frontline_salary_config.van_carriage.import')
     * @Token
     */
    public function importSpecialNonCarriageAction()
    {
        if (env('local_dev', 0)) {
            if (!$this->request->hasFiles()) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'no file found', null);
            }

            $files       = $this->request->getUploadedFiles();
            $file        = $files[0];
            $tmpFilePath = $file->getTempName();
        } else {
            $fileUrl   = $this->request->get('file');
            if (empty($fileUrl)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found file url');
            }
            $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl);
            if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'read file error');
            }
        }
        $operateId = $this->user['id'];

        $currentHour = date('H');
        $currentMinute = date('i');
        // 检查是否在 23:30 ~ 23:59
        if ($currentHour == env('system_maintenance_hour', 23) && $currentMinute >= env('system_maintenance_minute', 30)) {
            throw new ValidationException($this->t->_('system_maintenance'));
        }

        $server = new SpecialCarriageService();
        $data   = $server->import($tmpFilePath, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 一线员工薪资配置-配置特殊无车厢名单-获取导入模板
     * @permission(action='action.frontline_salary_config.van_carriage.import')
     * @Token
     */
    public function getImportTemplateAction()
    {
        $server = new SpecialCarriageService();
        $data   = $server->getImportTemp();
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }
}