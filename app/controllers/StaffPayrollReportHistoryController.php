<?php


namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Id\Services\MessagesService;
use App\Services\StaffPayrollListOperateLogService;
use App\Models\backyard\StaffPayrollReportOperateLogModel;


class StaffPayrollReportHistoryController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }


    /**
     * @description: 获取History历史记录  这里需要权限
     * @param null
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:32
     * @Token
     * @Permission(action='staff_payroll_report_history.getList')
     */
    public function getListAction()
    {
        $params = $this->request->get();
        $params['operate_id']   = $this->user['id'];
        $result = (new StaffPayrollListOperateLogService())->getList($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * @description: 获取操作类型枚举
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 15:31
     * @Token
     *
     */
    public function getStaticEnumsAction()
    {
        $params = $this->request->get();
        $result = (new StaffPayrollListOperateLogService())->getStaticEnums();
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }

}