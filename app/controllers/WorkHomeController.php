<?php

namespace App\Controllers;


use App\Library\DateHelper;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\HrStaffInfoModel;
use App\Services\WorkHomeService;

class WorkHomeController extends BaseController{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @Permission(action='work_from_home_view')
     * @throws \App\Library\Validation\ValidationException
     */
    public function configListAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'store_id'      => 'Arr',//网点
            'state'         => 'Arr',//在职状态
            'audit_state'   => 'Arr',//审批状态
            'staff_info_id' => 'Str',//用户
            'page'          => 'Required|int',
            'size'          => 'Required|int',
        ]);
        $server = new WorkHomeService();
        $data = $server->list($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }



    /**
     * @Permission(action='work_from_home_edit')
     * @throws \App\Library\Validation\ValidationException
     */
    public function configAddAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'type' => 'Required|int',//类型
            'department_id' => 'Required|ArrLenGeLe:1,50|>>>:[department] at most 50',//部门
            'hire_type'     => 'Required|Arr',//雇佣类型
            'province_code' => 'Required|Arr',//所在州
            'remark' => 'StrLenGeLe:0,500|>>>:[remark] params error' ,
        ]);
        $server = new WorkHomeService();
        $data = $server->add($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);

    }


    /**
     * @Permission(action='work_from_home_del')
     * @throws \App\Library\Validation\ValidationException
     */
    public function configDelAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'id'     => 'Required|int',//删除id
        ]);

        $server = new WorkHomeService();
        $data = $server->delete($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * @Permission(action='work_from_home_view')
     * @throws \App\Library\Validation\ValidationException
     */
    public function staffConfigListAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            "type"          => 'int',
            'staff_info_id' => 'int',//用户
            'page'          => 'Required|int',
            'size'          => 'Required|int',
        ]);
        $server = new WorkHomeService();
        $data = $server->staffList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * @Permission(action='work_from_home_edit')
     * @throws \App\Library\Validation\ValidationException
     */
    public function staffConfigAddAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'type'          => 'Required|int',
            'date_list'     => 'Arr',//日期
            'staff_info_id' => 'Required|StrLenGeLe:0,700|>>>:[staff_info_id] too long' ,
            'remark' => 'StrLenGeLe:0,500|>>>:[remark] params error' ,
        ]);
        $server = new WorkHomeService();
        $data = $server->addStaffSetting($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);


    }

    /**
     * @Permission(action='work_from_home_del')
     * @throws \App\Library\Validation\ValidationException
     */
    public function staffConfigDelAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'id'     => 'Required|int',//删除id
        ]);

        $server = new WorkHomeService();
        $data = $server->deleteStaff($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    public function testAction(){

        $staffData = HrStaffInfoModel::find([
            'columns' => 'staff_info_id,name staff_name,sex,week_working_day
                ,sys_store_id id
                ,sys_store_id
                ,node_department_id
                ,hire_type
                ,state
                ,wait_leave_state
                ,job_title',
            'conditions' => 'staff_info_id in (121020)'
        ])->toArray();
        $dateList = DateHelper::DateRange(strtotime('2025-03-01'), strtotime('2025-03-31'));

        $server = new WorkHomeService();
        $data = $server->getWhDays($dateList,$staffData);
        var_dump($data);;exit;
    }


}