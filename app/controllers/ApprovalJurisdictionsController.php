<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HcmStaffManageListModel;
use App\Models\backyard\RolesModel;
use App\Services\ApprovalJurisdictionsService;
use App\Services\ExcelService;
use App\Services\StaffWarningService;
use App\Services\SysService;

class ApprovalJurisdictionsController extends BaseController
{

    /**
     * 管辖权限列表
     * @Token
     * @Permission(action='approval_jurisdictions_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $paramIn = $this->request->get();
        $data = (new ApprovalJurisdictionsService())->getList($paramIn);
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, 'success', $data['data'] ?? []);

    }

    /**
     * @description:获取编辑详情
     * @Token
     * @Permission(action='approval_jurisdictions_edit')
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function getDetailsAction()
    {
        $paramIn = $this->request->get();
        Validation::validate($paramIn, [
            'staff_info_id' => 'Required|int',        //员工
        ]);
        $data = (new ApprovalJurisdictionsService())->getStaffManageRegions($paramIn);
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, 'success', $data['data'] ?? []);
    }

    /**
     * @description:获取编辑详情 这里只返回了部门信息
     * @Token
     * @Permission(action='approval_jurisdictions_edit')
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function getStaffManageRegionsAction()
    {
        $paramIn = $this->request->get();
        Validation::validate($paramIn, [
            'staff_info_id' => 'Required|int',        //员工
        ]);
        $data = (new ApprovalJurisdictionsService())->getStaffManageRegions($paramIn);
        $data['data'] = $data['data']['department_ids_arr'] ?? [];
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, 'success', $data['data'] ?? []);
    }



    /**
     * @description:编辑
     * @Token
     * @Permission(action='approval_jurisdictions_edit')
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function editAction()
    {
        $paramIn = $this->request->get();
        $paramIn['operator_id'] =$this->user['id'];
        Validation::validate($paramIn, [
            'staff_info_id' => 'Required|int',        //员工
            'department_ids_arr' => 'Required|Arr',
            'region_ids_arr' => 'Required|Arr',
            'piece_ids_arr' => 'Required|Arr',
            'store_ids_arr' => 'Required|Arr',
        ]);
        // 加锁处理
        $lock_key = md5('approval_jurisdictions_edit' . $paramIn['operator_id']);
        $data = $this->atomicLock(function() use ($paramIn){
            return  (new ApprovalJurisdictionsService())->edit($paramIn);
        }, $lock_key, 60);
        if($data === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, 'success', $data['data'] ?? []);
    }

    /**
     * @description:导出
     * @Token
     * @Permission(action='approval_jurisdictions_export')
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function exportAction()
    {
        $paramIn = $this->request->get();
        $paramIn['operator_id'] = $this->user['id'];
        $paramIn['lang'] = $this->locale;//语言
        //获取文件名
        $file_name     = 'approval_jurisdictions' . '_' . $this->locale . date('Ymd-His') . '.xlsx';
        $file_name = str_replace(" ","_",$file_name);
        $action_name   = 'approval_jurisdictions'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'dow_excel';

        // 加锁处理
        $lock_key = md5('approval_jurisdictions_export' . $paramIn['operator_id']);
        //入 task 表 走队列导出
        $data = $this->atomicLock(function() use ($paramIn,$action_name,$file_name){
            return (new ExcelService())->insertTask($this->user['id'], $action_name, $paramIn, HcmExcelTackModel::TYPE_ATTENDANCE, $file_name);
        }, $lock_key, 60);
        if($data === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success', $data['data'] ?? []);
    }

    /**
     * @description: 获取枚举
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/23 18:03
     * @Token
     */

    public function getEnumerateAction()
    {
        $data = [
            'roles'               => [
                [
                    'value' => RolesModel::ROLES_HR_BP,
                    'label' => $this->t->_('role_'.RolesModel::ROLES_HR_BP),
                ],
            ],                                                                           //角色
            'state_arr'           => (new SysService())->HrisAllWorkingState(),          //在职状态
            'configure_state_arr' => [
                [
                    'value' => HcmStaffManageListModel::CONFIGURE_STATE_1,
                    'label' => $this->t->_('configure_state_'.HcmStaffManageListModel::CONFIGURE_STATE_1),
                ],
                [
                    'value' => HcmStaffManageListModel::CONFIGURE_STATE_2,
                    'label' => $this->t->_('configure_state_'.HcmStaffManageListModel::CONFIGURE_STATE_2),
                ],
            ],
        ];
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
}