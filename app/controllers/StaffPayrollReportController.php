<?php


namespace App\Controllers;

use App\Library\DateHelper;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Modules\My\Services\PayrollReportService;
use App\Modules\My\Tasks\PayslipReportTask;
use App\Services\ExcelService;
use App\Services\StaffPayrollYearListService;
use App\Models\backyard\StaffPayrollReportOperateLogModel;


class StaffPayrollReportController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }

    public static $lock_expire = 5; //锁时间


    /**
     * @description: 获取PCB列表
     * @param null
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:32
     * @Token
     * @Permission(action='staff_payroll_report.getListPCB')
     */
    public function getListPCBAction()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getList($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description: 获取EA列表
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:32
     * @Token
     * @Permission(action='staff_payroll_report.getListEAForm')
     */
    public function getListEAFormAction()
    {
        $params               = $this->request->get();
        $params['method']     = 'getListEAForm';
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getList($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description:获取 CP39 列表
     * @param null
     * @return     :
     * @throws \App\Library\Exception\BusinessException
     * <AUTHOR> L.J
     * @time       : 2022/6/7 11:00
     * @Token
     * @Permission(action='staff_payroll_report.getListCP39')
     */
    public function getListCP39Action()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getListCP39($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 获取 SOCSO8 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.getListSOCSO8')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function getListSOCSO8Action()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getListSOCSO8orKWSP6($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 获取 EIS1 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.getListEIS1')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function getListEIS1Action()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getListEIS1($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 获取 KWSP6 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.getListKWSP6')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function getListKWSP6Action()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getListSOCSO8orKWSP6($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 获取 CP22 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.getListCP22')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getListCP22Action()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->getListCP22($params);
        return $this->returnJson($result['code'] ?? ErrCode::SUCCESS, 'success',
            $result);
    }

    /**
     * 获取 CP22 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.getListCP22')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function batchDownloadCP22Action()
    {
        $params                  = $this->request->get();
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_cp22;
        $params['operate_type']  = StaffPayrollReportOperateLogModel::operate_type_3;
        // 加锁处理
        $lock_key = 'batchDownloadCP22Action_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->batchDownloadCP22($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 导出列表 PCB模板
     * @Token
     * @Permission(action='staff_payroll_report.exportTemplatePCB')
     */
    public function exportTemplatePCBAction()
    {
        $templateUrl = 'https://tc-static-asset-internal.flashexpress.my/workOrder/1755763427-2c6ac08f929a4ae189fea502bc348bae.xlsx';
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $templateUrl]);
    }

    /**
     * 导出列表模板 EAForm
     * @Token
     * @Permission(action='staff_payroll_report.exportTemplateEAForm')
     */
    public function exportTemplateEAFormAction()
    {
        $params                     = $this->request->get();
        $params['operate_id']       = $this->user['id'];
        // 加锁处理
        $lock_key = 'exportTemplateEAFormAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->exportEAFileTemplate($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 上传数据PCB
     * @Token
     * @Permission(action='staff_payroll_report.exportTemplatePCB')
     * @throws ValidationException
     */
    public function uploadExcelPCBAction()
    {
        if (!$this->request->hasFiles()) {
            throw  new ValidationException('no file found');
        }
        $files                      = $this->request->getUploadedFiles();
        $file                       = $files[0];
        $file_name                  = $file->getTempName();
        $params['file_name']        = $file_name;
        $params['operate_id']       = $this->user['id'];
        // 加锁处理
        $lock_key = 'uploadExcelPCBAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new PayrollReportService())->uploadPCBExcel($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 上传EA数据
     * @Token
     * @Permission(action='staff_payroll_report.exportTemplateEAForm')
     */
    public function uploadExcelEAFormAction()
    {
        if (!$this->request->hasFiles()) {
            throw  new ValidationException('no file found');
        }
        $files                = $this->request->getUploadedFiles();
        $file                 = $files[0];
        $file_name            = $file->getTempName();
        $params['file_name']  = $file_name;
        $params['operate_id'] = $this->user['id'];

        // 加锁处理
        $lock_key = 'uploadExcelEAFormAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->uploadEAExcel($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description: 获取详情 主要是个人邮箱下拉选项   (发送邮箱权限)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/6 17:26
     * @Token
     * @Permission(action='staff_payroll_report.getDetailPCB')
     */
    public function getDetailPCBAction()
    {
        $params = $this->request->get();
        $result = (new StaffPayrollYearListService())->getDetail($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description: 获取详情 主要是个人邮箱下拉选项  (发送邮箱权限)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/6 17:26
     * @Token
     * @Permission(action='staff_payroll_report.getDetailEAForm')
     */
    public function getDetailEAFormAction()
    {
        $params = $this->request->get();
        $result = (new StaffPayrollYearListService())->getDetail($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }


    /**
     * @description:CP39 Setting 列表    (编辑权限)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 11:37
     * @Token
     * @Permission(action='staff_payroll_report.getListCP39Setting')
     */
    public function getListCP39SettingAction()
    {
        $params = $this->request->get();
        $result = (new StaffPayrollYearListService())->getListCP39Setting($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description: CP39 Setting 删除   (编辑权限)
     * @param null
     * @return     :
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2022/6/7 11:49
     * @Token
     * @Permission(action='staff_payroll_report.getListCP39Setting')
     */
    public function deleteCP39SettingAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollYearListService::$delete_CP39_setting);
        $params['operate_id'] = $this->user['id'];
        $result               = (new StaffPayrollYearListService())->deleteCP39Setting($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }


    /**
     * @description: CP39 Setting 添加 编辑   (编辑权限)
     * @param null`
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 11:50
     * @Token
     * @Permission(action='staff_payroll_report.getListCP39Setting')
     * @throws ValidationException
     */
    public function saveCP39SettingAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollYearListService::$save_CP39_setting);
        // 加锁处理
        $lock_key = 'saveCP39SettingAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->saveCP39Setting($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description:PCB 发送邮件
     * @param null
     * @return     :
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2022/6/6 17:31
     * @Token
     * @Permission(action='staff_payroll_report.getDetailEAForm')
     */
    public function sendEmailPCBAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollYearListService::$send_email_param);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_pcb;//PCB
        // 加锁处理
        $lock_key = 'sendEmailPCBAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->sendEmail($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description:EA 发送邮件
     * @param null
     * @return     :
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2022/6/6 17:31
     * @Token
     * @Permission(action='staff_payroll_report.getDetailEAForm')
     */
    public function sendEmailEAFormAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollYearListService::$send_email_param);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_ea;//EA
        // 加锁处理
        $lock_key = 'sendEmailEAFormAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->sendEmail($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载EA form
     * @Token
     * @Permission(action='staff_payroll_report.downloadEAForm')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadEAFormAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollYearListService::$download_param);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_ea;//EA
        $params['operate_type']  = $params['operate_type'] ?? 0;
        if (!in_array($params['operate_type'],
            [StaffPayrollReportOperateLogModel::operate_type_3, StaffPayrollReportOperateLogModel::operate_type_4])) {
            throw new ValidationException('condition error');
        }
        $method = 'downloadEaForm';
        //批量下载Ea form
        if ($params['operate_type'] == StaffPayrollReportOperateLogModel::operate_type_3) {
            $method = 'batchDownloadEaForm';
        }
        // 加锁处理
        $lock_key = 'downloadEAFormAction_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params, $method) {
            return (new PayrollReportService())->{$method}($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * pcb 下载
     * @Token
     * @Permission(action='staff_payroll_report.downloadPCB')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadPCBAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollYearListService::$download_param);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_pcb;//PCB
        $params['operate_type']  = $params['operate_type'] ?? 0;
        if (!in_array($params['operate_type'],
            [StaffPayrollReportOperateLogModel::operate_type_3, StaffPayrollReportOperateLogModel::operate_type_4])) {
            throw new ValidationException('condition error');
        }
        $lock_key = 'downloadPCBAction_' . md5($params['operate_id']);
        //批量下载PCB
        if ($params['operate_type'] == StaffPayrollReportOperateLogModel::operate_type_3) {
            $result = $this->atomicLock(function () use ($params) {
                return (new PayrollReportService())->batchDownloadPcb($params);
            }, $lock_key, self::$lock_expire);
        } else {
            $result = $this->atomicLock(function () use ($params) {
                return (new PayrollReportService())->downloadPcb($params);
            }, $lock_key, self::$lock_expire);
        }
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载CP39
     * @Token
     * @Permission(action='staff_payroll_report.downloadCP39')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadCP39Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'operate_type' => 'Required|IntIn:5,6',
            'month'        => 'Required',
            'company_id'   => 'Required|IntIn:' . implode(',', array_keys(SalaryEnums::$companyMap)),
        ]);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_cp39;//CP39
        // 加锁处理
        $lock_key = 'downloadCP39Action_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->downloadCP39($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载SOSOC 8A 文件
     * @Token
     * @Permission(action='staff_payroll_report.downloadSOCSO8')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadSOCSO8Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'operate_type' => 'Required|IntIn:7,8',
            'month'        => 'Required',
        ]);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_socso8;
        if (!in_array($params['type'], SalaryEnums::$salaryReportDownloadType) || !isset($params['store_id'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        if ($params['type'] == SalaryEnums::REPORT_DOWNLOAD_TYPE_APPOINT && empty($params['staff_ids'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('staff_payroll_report_staff_empty'));
        }
        $params['staff_ids'] = clearSpecialChar($params['staff_ids']);
        // 加锁处理
        $lock_key = 'downloadSOCSO8Action_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->downloadSOCSO8OrKWSP6($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载EIS1 文件
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadEIS1Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'operate_type' => 'Required|IntIn:9,10',
            'company_id'   => 'Required|IntIn:' . implode(',', array_keys(SalaryEnums::$companyMap)),
            'month'        => 'Required',
        ]);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_eis1;
        // 加锁处理
        $lock_key = 'downloadEIS1Action_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->downloadEIS1($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载 KWSP6 文件
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadKWSP6Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'operate_type' => 'Required|IntIn:11,12,13',
            'company_id'   => 'Required|IntIn:' . implode(',', array_keys(SalaryEnums::$companyMap)),
            'month'        => 'Required',
        ]);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_kwsp6;
        if ($params['operate_type'] != StaffPayrollReportOperateLogModel::operate_type_12) {
            if (!in_array($params['type'], SalaryEnums::$salaryReportDownloadType)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
            }
            if ($params['type'] == SalaryEnums::REPORT_DOWNLOAD_TYPE_APPOINT && empty($params['staff_ids'])) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('staff_payroll_report_staff_empty'));
            }
        }
        //$params['store_id'] 选填写的
        $params['staff_ids'] = clearSpecialChar($params['staff_ids']);
        // 加锁处理
        $lock_key = 'downloadKWSP6Action_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->downloadSOCSO8OrKWSP6($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载 CP22 文件
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function downloadCP22Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'company_id'    => 'Required|IntIn:' . implode(',', array_keys(SalaryEnums::$companyMap)),
            'month'         => 'Required',
            'staff_info_id' => 'Required',
        ]);
        $params['operate_id']    = $this->user['id'];
        $params['business_type'] = StaffPayrollReportOperateLogModel::business_type_cp22;
        $params['operate_type']  = StaffPayrollReportOperateLogModel::operate_type_14;
        // 加锁处理
        $lock_key = 'downloadCP22Action_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollYearListService())->downloadCP22($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description: Generate  PCB 生成
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 10:11
     * @Token
     * @Permission(action='staff_payroll_report.generatePCB')
     */
    public function generatePCBAction()
    {
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * @description: Generate EAForm 生成
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 10:11
     * @Token
     * @Permission(action='staff_payroll_report.generateEAForm')
     */
    public function generateEAFormAction()
    {
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data'] ?? []);
    }


    /**
     * @description: 刷新
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 10:11
     * @Token
     * @Permission(action='staff_payroll_report.refreshPCB')
     */
    public function refreshPCBAction()
    {
        $result['data'] = (new PayrollReportService())->schedule('pcb');
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data']);
    }


    /**
     * @description: 刷新
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 10:11
     * @Token
     * @Permission(action='staff_payroll_report.refreshEAForm')
     */
    public function refreshEAFormAction()
    {
        $result['data'] = (new PayrollReportService())->schedule('ea_form');
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data']);
    }

    /**
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function refreshPayslipAction()
    {
        $result['data'] = (new PayrollReportService())->schedule('payslip');
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data']);
    }

    public function refreshAllAction()
    {
        $data = (new PayrollReportService())->schedule();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 获取 CP21 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.cp21_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function getListCP21Action()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        Validation::validate($params, [
            'year' => 'Required',
        ]);
        $result = (new PayrollReportService())->getListCP21OrCP22A($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载cp21文件
     * @Token
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function downloadCP21Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'staff_info_id' => 'Required',
            'company_id'    => 'Required|IntIn:' . implode(',',
                    SalaryEnums::$salaryCompanyCodeMap) . '|>>>:' . $this->t->_('payroll_report_choose_company'),
            'year'          => 'Required',
            'leave_date'    => 'Required|date',
            'leave_reason'  => 'Required|StrLenLe:100',
        ]);
        $params['operate_id'] = $this->user['id'];
        $fileData             = (new PayrollReportService())->downloadCP21($params);
        if (!isset($fileData['object_url'])) {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'sys error');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success',
            ['url' => $fileData['object_url'] ?? '', 'file_name' => $fileData['file_name'] ?? '']);
    }

    /**
     * 获取 CP22A 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.cp22a_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getListCP22AAction()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $service              = new PayrollReportService();
        Validation::validate($params, [
            'year' => 'Required',
        ]);
        $result = $service->getListCP21OrCP22A($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * 下载cp22a文件
     * @Token
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function downloadCP22AAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'staff_info_id' => 'Required',
            'company_id'    => 'Required|IntIn:' . implode(',',
                    SalaryEnums::$salaryCompanyCodeMap) . '|>>>:' . $this->t->_('payroll_report_choose_company'),
            'year'          => 'Required',
        ]);
        $params['operate_id'] = $this->user['id'];
        $fileData             = (new PayrollReportService())->downloadCP22A($params);
        if (!isset($fileData['object_url'])) {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'sys error');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success',
            ['url' => $fileData['object_url'] ?? '', 'file_name' => $fileData['file_name'] ?? '']);
    }

    /**
     * 获取 CP22A txt 文件列表
     * @Token
     * @Permission(action='staff_payroll_report.cp22a_txt_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getListCP22ATXTAction()
    {
        $params     = $this->request->get();
        $validation = [
            'start_time' => 'Required|date',
            'end_time'   => 'Required|date',
        ];
        if (!empty($params['company_id'])) {
            $validation['company_id'] = 'Required|IntIn:' . implode(',',
                    SalaryEnums::$salaryCompanyCodeMap) . '|>>>:' . $this->t->_('payroll_report_choose_company');
        }
        Validation::validate($params, $validation);
        $countDay = count(DateHelper::DateRange(strtotime($params['start_time']), strtotime($params['end_time'])));
        if ($countDay > 30) {
            throw new ValidationException('Time range, with a maximum length of 30 days');
        }
        $params['operate_id'] = $this->user['id'];
        $params['operate']    = 'search';
        $service              = new PayrollReportService();
        $list                 = $service->getCP22ATXTList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * 下载 CP22A txt
     * @Token
     * @Permission(action='staff_payroll_report.cp22a_txt_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     * @throws \Exception
     */
    public function downloadCP22ATXTAction()
    {
        $params     = $this->request->get();
        $validation = [
            'start_time' => 'Required|date',
            'end_time'   => 'Required|date',
            'company_id' => 'Required|IntIn:' . implode(',',
                    SalaryEnums::$salaryCompanyCodeMap) . '|>>>:' . $this->t->_('payroll_report_choose_company'),
        ];
        Validation::validate($params, $validation);
        $countDay = count(DateHelper::DateRange(strtotime($params['start_time']), strtotime($params['end_time'])));
        if ($countDay > 30) {
            throw new ValidationException('Time range, with a maximum length of 30 days');
        }
        $params['operate_id'] = $this->user['id'];
        $service              = new PayrollReportService();
        $result               = $service->downloadCP22ATXTCon($params);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 获取 CP8D 文件列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function getListCP8DAction()
    {
        $params = $this->request->get();
        if (!empty($params['company_id']) && !in_array($params['company_id'], SalaryEnums::$salaryCompanyCodeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_report_choose_company'));
        }
        if (empty($params['year'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_report_choose_year'));
        }
        $params['operate_id'] = $this->user['id'];
        $data                 = (new PayrollReportService())->getCP8DListOrFormE($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 下载CP8D excel txt a txt b
     * @Token
     * @throws \Exception
     */
    public function downloadCP8DAction()
    {
        $params = $this->request->get();
        if (empty($params['year']) || empty($params['company_id']) || empty($params['type'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        if (!in_array($params['company_id'], SalaryEnums::$salaryCompanyCodeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_report_choose_company'));
        }
        $types = ['excel', 'txt_a', 'txt_b'];
        if (!in_array($params['type'], $types)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        $companyInfo                 = (new PayrollReportService())->getCompanyInfoV1($params['company_id']);
        $company_registration_number = empty($companyInfo[$params['company_id']]['company_registration_number']) ? '' : trim(str_replace('E',
            '',
            $companyInfo[$params['company_id']]['company_registration_number']));
        $operate_id                  = $this->user['id'];
        $file_name                   = 'P' . $company_registration_number . '_' . $params['year'] . ($params['type'] == 'excel' ? '.xlsx' : '.txt');
        if ($params['type'] == 'txt_a') {
            $txtAParams['company_name']                = empty($companyInfo[$params['company_id']]['company_registration_number']) ? '' : $companyInfo[$params['company_id']]['company_name'];
            $txtAParams['company_registration_number'] = $company_registration_number;
            $txtAParams['year']                        = $params['year'];
            $url                                       = (new PayrollReportService())->getCp8dTxtA($file_name,
                $txtAParams);
            return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
        }
        $actionName = '';
        if ($params['type'] == 'excel') {
            $actionName = PayslipReportTask::ACTION_NAME_14;
        }
        if ($params['type'] == 'txt_b') {
            $actionName = PayslipReportTask::ACTION_NAME_15;
        }
        $result = (new ExcelService())->insertTask($operate_id, $actionName,
            $params,
            HcmExcelTackModel::TYPE_PAYSLIP_REPORT, $file_name);
        return $this->returnJson($result['code'], $result['message'], $result['data'] ?? []);
    }

    /**
     * 获取 Form E 文件列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function getListFormEAction()
    {
        $params = $this->request->get();
        if (!empty($params['company_id']) && !in_array($params['company_id'], SalaryEnums::$salaryCompanyCodeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_report_choose_company'));
        }
        if (empty($params['year'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_report_choose_year'));
        }
        $params['operate_id'] = $this->user['id'];
        $data                 = (new PayrollReportService())->getCP8DListOrFormE($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 异步下载form e pdf
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function downloadFormEAction()
    {
        $params = $this->request->get();
        if (!in_array($params['company_id'], SalaryEnums::$salaryCompanyCodeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_report_choose_company'));
        }
        if (empty($params['year']) || !in_array($params['type'], SalaryEnums::$salaryReportDownloadType)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        if ($params['type'] == SalaryEnums::REPORT_DOWNLOAD_TYPE_APPOINT && empty($params['staff_ids'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('staff_payroll_report_staff_empty'));
        }
        $params['staff_ids'] = clearSpecialChar($params['staff_ids']);
        $operate_id          = $this->user['id'];
        $file_name           = 'Form E' . '_' . $params['year'] . '_' . SalaryEnums::$companyMap[$params['company_id']] . '_' . date('YmdHis') . '.pdf';
        $result              = (new ExcelService())->insertTask($operate_id, PayslipReportTask::ACTION_NAME_16,
            $params,
            HcmExcelTackModel::TYPE_PAYSLIP_REPORT, $file_name);
        return $this->returnJson($result['code'], $result['message'], $result['data'] ?? []);
    }

}