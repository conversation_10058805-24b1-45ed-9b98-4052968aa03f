<?php
/**
 * Author: Bruce
 * Date  : 2023-08-08 17:11
 * Description:
 */

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\StaffShiftSettingService;

class StaffShiftController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 是否可以设置班次--前端仅th调用
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function checkSettingPermissionAction()
    {
        $params['staff_info_id'] = $this->user['id'];

        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->isManager($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 员工班次设置列表 数据
     * @Token
     * @Permission(action='staff_shift_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @throws \Exception
     */
    public function getStaffListAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'page_num' => 'IntGt:0',
            'page_size' => 'IntGt:0',
            'month' => 'Required|StrLenGe:1|>>>:month param error',
        ];
        $params['staff_info_id'] = $this->user['id'];
        Validation::validate($params, $validate_rule);

        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->getStaffList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     *
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @throws \Exception
     */
    public function staffShiftsAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'staff_ids' => 'Required|StrLenGe:1|>>>:staff_ids param error',
            'effective_date' => 'Required|StrLenGe:1|>>>:effective_date param error',
        ];
        $params['staff_info_id'] = $this->user['id'];
        Validation::validate($params, $validate_rule);
        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->staffShiftPreview($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 确认设置(编辑)
     * @Token
     * @Permission(action='staff_shift_edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function createsAction()
    {
        $params['data']          = $this->request->get();
        $params['staff_info_id'] = $this->user['id'];
        $params['job_title']     = $this->user['job_title'];
        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->createsUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 确认设置(编辑)
     * @Token
     * @Permission(action='staff_shift_edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function editShiftAction()
    {
        $params                  = $this->request->get();
        $params['staff_info_id'] = $this->user['id'];

        $validate_rule = [
            'staff_id'       => 'Required|StrLenGe:1|>>>:staff_id param error',
            'effective_date' => 'Required|StrLenGe:1|>>>:effective_date param error',
            'shift_id'       => 'Required|Int|>>>:shift_id param error',
        ];
        Validation::validate($params, $validate_rule);

        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->editShiftUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 编辑详情
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function viewAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'staff_info_id' => 'Required|IntGe:0|>>>:staff_info_id param error',
            'is_edit' => 'Required|Int|>>>:is_edit param error',
        ];
        Validation::validate($params, $validate_rule);
        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->getDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 编辑详情
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function viewSingleDayAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'staff_info_id'  => 'Required|IntGe:0|>>>:staff_info_id param error',
            'effective_date' => 'Required|StrLenGe:1|>>>:effective_date param error',
        ];
        Validation::validate($params, $validate_rule);
        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->getSingelDayShiftDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 导入
     * @Token
     * @Permission(action='staff_shift_import')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function importShiftAction()
    {
        $params = $this->request->get();
        $validate_rule = [
            'file' => 'Required|StrLenGe:1|>>>:file param error',
        ];
        Validation::validate($params, $validate_rule);

        $params['user_id']   = $this->user['id'];
        $params['job_title'] = $this->user['job_title'];
        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result = $staffShiftSettingService->importShiftUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 导出
     * @Token
     * @Permission(action='staff_shift_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function exportShiftAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'month' => 'Required|StrLenGe:1|>>>:month param error',
        ];

        Validation::validate($params, $validate_rule);

        $params['staff_info_id'] = $this->user['id'];
        $params['lang']          = $this->locale;
        $params['file_name']     = 'Schedule_of_staff' . date('YmdHms') . '.xlsx';

        $staffShiftSettingService = reBuildCountryInstance(new StaffShiftSettingService());
        $result                   = $staffShiftSettingService->exportShiftUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], $result);
    }

}