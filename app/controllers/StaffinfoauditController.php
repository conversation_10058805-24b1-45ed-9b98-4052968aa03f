<?php

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Services\ExcelService;
use App\Services\SettingEnvService;
use App\Services\StaffInfoAuditService;
use App\Services\StaffService;

class StaffinfoauditController extends BaseController
{
    /**
     * rename backupBankInfoAudit to auditCard
     * 备用银行卡、社保卡、医保卡、公积金、税号 审核接口 从ph 复制过来的 目前只有银行卡审核用到
     * @Token
     * @Permission(action='staff_info_audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditCardAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            [
                'staff_info_id' => 'Required|Int|IntGt:0',         // 员工工号
                'action'        => 'Required|Int|IntIn:1,2',       //1表示通过 2 表示拒绝
                'type'          => 'Required|Int|IntIn:2,3,4,5,6', //2、银行卡审核 3、社保号审核 4、公积金审核 5、医疗保险号审核 6、税号审核
            ]
        );

        $params['reject_reason'] = trim($params['reject_reason']);
        // 拒绝时原因必填
        if (HrStaffAnnexInfoModel::AUDIT_STATE_REJECT == $params['action'] && empty($params['reject_reason'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'reject_reason is empty!', '');
        }

        if (HrStaffAnnexInfoModel::AUDIT_STATE_REJECT == $params['action'] && mb_strlen($params['reject_reason'],
                'UTF-8') > 200) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'reject_reason characters are 200!', '');
        }

        // 当前操作人信息
        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new StaffInfoAuditService())->auditCard($params);
        if (false == $result) {
            return $this->returnJson(ErrCode::FAIL, 'error', []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 员工身份证审核列表
     * @Token
     * @Permission(action='staff_info_audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStafflistAction()
    {
        $params               = $this->request->get();
        $params['staff_info'] = $this->user;
        $data                 = (new StaffInfoAuditService())->getStaffList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 员工身份证审核列表导出
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportStaffListAction()
    {
        $params               = $this->request->get();

        $staffInfoId = $this->user['id'];

        $params['is_export']   = 1;
        $params['file_name']   = 'staff-info-audit-list-'.date('YmdHis').'-'.$staffInfoId.'.xlsx';
        $params['lang']        = $this->locale;
        $params['staff_info']   = $this->user;

        $action_name = 'staff-info-audit'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'exportStaffList';
        $result = (new ExcelService())->insertTask($staffInfoId, $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }

        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 员工身份证信息审核详情
     * @Token
     * @Permission(action='staff_info_audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['staff_info_id' => 'Required|IntGt:0',]);

        $data = (new StaffInfoAuditService())->getStaffDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 身份证审核 - 通过
     * @Token
     * @Permission(action='staff_info_audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function identityAuditApproveAction()
    {
        $params = $this->request->get();

        Validation::validate($params, ['staff_info_id' => 'Required|IntGt:0',]);

        $params['audit_state']   = 1; //通过
        $params['operator']      = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new StaffInfoAuditService())->staffIdentityAudit($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $result);
    }

    /**
     * 身份证审核 - 驳回
     * @Token
     * @Permission(action='staff_info_audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function identityAuditRejectAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            ['staff_info_id' => 'Required|IntGt:0', 'reject_reason' => 'Required|StrLenGeLe:1, 200']);

        $params['audit_state']   = 2; //驳回
        $params['operator']      = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new StaffInfoAuditService())->staffIdentityAudit($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $result);
    }

    /**
     * @Token
     * @Permission(action='staff_info_audit')
     * 审核记录
     */
    function getAuditLogListAction()
    {
        $params = $this->request->get();
        $result = (new StaffInfoAuditService())->getAuditLogList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $result);
    }

    /**
     * @Token
     * @Permission(action='staff_info_audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function modifyBankNoAction () {
        $params = $this->request->get();
        Validation::validate($params,
            [
                'staff_info_id' => 'Required|Int|IntGt:0',          // 员工工号
                'bank_no_name' => 'Required|StrLenGeLe:0,100|>>>:bank_no_name error', //持卡人
                'bank_type'    => 'Required|Int|>>>:bank_type error', // 银行类型
            ]
        );

        $params['loginUserId'] = (int) $this->user['id'];
        $result = (new StaffInfoAuditService())->modifyBankCardNo($params);
        return $this->returnJson($result['code'], $result['msg'],[]);
    }

    public function bank_type_listAction()
    {
        $params = $this->request->get();
        $result = (new StaffInfoAuditService())->bank_type_list($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $result);
    }

    /**
     * @Token
     * @Permission(action='staff_info_audit')
     * 员工信息审核-身份证保存
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function saveIdentityAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            [
                'staff_info_id' => 'Required|StrLenGe:0|>>>:staff_info_id error', //工号
                'name'          => 'Required|StrLenGeLe:0,300|>>>:name error', //姓名
                'name_en'       => 'StrLenGeLe:0,300|>>>:name_en error', //英文名
                'identity'      => 'Required|StrLenGeLe:0,300|>>>:identity error', //身份证
                'birthday'      => 'Date|>>>:birthday error', //生日
            ]
        );
        $params['loginUserId'] = (int) $this->user['id'];
        (new StaffInfoAuditService())->saveIdentityInfoUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', []);
    }

    /**
     * @Token
     * @Permission(action='staff_info_audit')
     * 保存 户口簿信息
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function residenceBookletSaveAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            [
                'staff_info_id'        => 'Required|StrLenGe:0|>>>:staff_info_id error', //工号
                'register_country'     => 'Required|IntGt:0|>>>:register_country error', //国家/地区
                'register_province'    => 'Required|StrLenGe:0|>>>:register_province error', //省
                'register_city'        => 'Required|StrLenGe:0|>>>:register_city error', //市
                'register_district'    => 'Required|StrLenGe:0|>>>:register_district error', //乡
                'register_postcodes'   => 'Required|StrLenGe:0|>>>:register_postcodes error', //邮编
                'register_house_num'   => 'Required|StrLenGe:0|>>>:register_house_num error', //门牌号
                'register_village_num' => 'Required|StrLenGe:0|>>>:register_village_num error', //村号
                'register_village'     => 'Required|StrLenGe:0|>>>:register_village error', //村庄
                'register_alley'       => 'Required|StrLenGe:0|>>>:register_alley error', //巷
                'register_street'      => 'Required|StrLenGe:0|>>>:register_street error', //街道
            ]
        );
        $params['loginUserId'] = (int) $this->user['id'];
        (new StaffInfoAuditService())->residenceBookletSaveUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', []);
    }

    /**
     * @Token
     * @Permission(action='staff_info_audit')
     * 户口簿审核
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function residenceBookletAuditAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            [
                'staff_info_id' => 'Required|StrLenGe:0|>>>:staff_info_id error', //工号
                'action'        => 'Required|Int|IntIn:1,2', //1表示通过 2 表示拒绝
            ]
        );
        $params['operator_id']   = (int)$this->user['id'];
        $params['operator_name'] = $this->user['name'];
        (new StaffInfoAuditService())->residenceBookletAuditUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', []);
    }
}