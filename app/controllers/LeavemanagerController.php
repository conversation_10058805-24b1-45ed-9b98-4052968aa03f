<?php

namespace App\Controllers;

//use App\Library\BaseService;
use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Services\AsyncImportTaskService;
use App\Services\BllService;
use App\Services\ExcelService;
use App\Services\HoldManageService;
use App\Services\LeaveManageListService;
use App\Services\LeaveManagerService;
use App\Services\RenewContractBusinessService;
use App\Services\SettingEnvService;
use App\Services\StaffInfoService;
use App\Services\StaffSearchService;
use App\Services\StaffService;
use App\Traits\TokenTrait;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class LeavemanagerController extends BaseController
{


    protected $userinfo;
    public function initialize()
    {
        parent::initialize();
        if (isset($this->user['id'])) {
            $this->userinfo = (new StaffService())->get_fbi_user_info($this->user['id']);
        }
    }



    /**
     * 查询列表
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     */
    public function listV2Action()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        // 当前端传递0 时 会被array_filter 过滤掉
        if (!isset($params['is_sub_department'])) {
            $params['is_sub_department'] = 0;
        }

        $validation = [
            'page' => 'Required|Int|>>>:page error',
            'size' => 'Required|Int|>>>:size error',
        ];
        Validation::validate($params, array_merge($validation, LeaveManageListService::$validate_leave_list_params));
        $params['user'] = $this->user;
        $result = (new LeaveManageListService())->staffListV2($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 导出
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        // array_filter 会过滤掉 取值为0的参数 ，is_sub_department 参数会传递0值
        //$params = array_filter($params);
        foreach ($params as $k => $v) {
            if (in_array($k, ['hire_date_begin', 'hire_date_end', 'leave_date_begin', 'leave_date_end'])) {
                if (!empty($v)) {
                    $params[$k] = date('Y-m-d', strtotime($v));
                }
            }
        }
        // 异步导出
        unset($params['_url'], $params['time'], $params['auth'], $params['fbid']);
        //判断是否是fbi 操作的 导出
        $headerData = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && $headerData['From'] === 'fbi' ? $headerData['From'] : '';

        $params['userinfo_id'] = $this->user['id'];
        $params['file_name'] = 'leavemanager_export';
        $params['lang'] = $this->locale;
        $params['user'] = $this->user;
        $args = base64_encode(json_encode($params));

        $hcmExcelTask = HcmExcelTackModel::find([
            'columns' => 'staff_info_id,status,args_json',
            'conditions' => ' staff_info_id = :user_id: and action_name = :action_name:  and status = 0 and is_delete = 0',
            'bind' => [
                'user_id' => $this->user['id'],
                'action_name' => 'leavemanager'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'export_v2',
            ],
        ])->toArray();

        if ($hcmExcelTask) {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, $this->t['downloading'], []);
        }

        //hcm用导出列表
        $hcmExcelTask = new HcmExcelTackModel();
        $hcmExcelTask->staff_info_id = $this->user['id'];
        $hcmExcelTask->file_name = $params['file_name'];
        $hcmExcelTask->executable_path = APP_PATH . '/cli.php';
        $hcmExcelTask->action_name = 'leavemanager'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'export_v2';
        $hcmExcelTask->created_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $hcmExcelTask->args_json = $args;
        $hcmExcelTask->type = HcmExcelTackModel::TYPE_LEAVE_MANAGER_EXPORT;
        $hcmExcelTask->save();
        $hcmExcelTaskId = $hcmExcelTask->id;
        if (!empty($hcmExcelTaskId) && $params['From'] == 'fbi') {
            (new BllService())->addFbiExportMessage(
                $params['userinfo_id'],
                'hcm_leavemanager_export',
                $params['file_name'],
                $hcmExcelTaskId
            );
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t['downloading'], []);
    }

    /**
     * 批量导入
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function importAction()
    {
        if (!$this->request->hasFiles()) {
            throw  new ValidationException('file error');
        }
        $files = $this->request->getUploadedFiles();
        $file = $files[0];
        $extension = $file->getExtension();
        if ($extension !== "xlsx") {
            throw  new ValidationException('file extension error');
        }

        $path = $file->getTempName();
        $leaveManagerService = new LeaveManagerService();
        $leaveManagerService::setLanguage($this->locale);
        $positions = HrStaffInfoPositionModel::find([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $this->userinfo['id'],
            ],
        ])->toArray();
        $categoryIds = implode(",", array_column($positions, 'position_category')) ;
        $this->userinfo['position_category'] = $categoryIds;
        $res = $leaveManagerService->import($path, $this->userinfo);
        if ($res === false) {
            throw  new ValidationException('no auth');
        }
        if(is_array($res)&&isset($res['result'])&&$res['result']=='fail'){
            $msg='fail';
            if(isset($res['remarks'])){
                $msg=$msg.$res['remarks'];
            }
            return   $this->returnJson(ErrCode::VALIDATE_ERROR, $msg, $res);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }

    /**
     * 导入模版
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function importTemplateAction()
    {
        $lang = $this->request->get('lang');
        if ($lang == 'zh' || $lang == 'zh-CN') {
            $lang = 'zh';
        }
        $t            = BaseService::getTranslation($lang);
        $country_code = strtolower(env('country_code', 'th'));
        $url          = $t->_($country_code . '_leave_manager_import_template');
        if (empty($url)) {
            throw new \Exception('请注意没有获取到对应语言的离职导出模板!');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);

    }

    /**
     * 获得是否能撤销和导入的权限
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     */
    public function getPermissionsAction()
    {
        $positions = HrStaffInfoPositionModel::find([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $this->userinfo['id'],
            ],
        ])->toArray();
        $categoryIds = array_column($positions, 'position_category');
        $data = (new LeaveManagerService())->getPermission($this->userinfo['id'], implode(',', $categoryIds));
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 员工 离职申请详情接口
     * 有以下三个页面在用
     * 1、离职记录 需要传 resign_id
     * 2、离职管理
     * 3、离职问卷
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function staffResignDetailAction()
    {
        $staffInfoId = $this->request->get("staff_info_id");
        $resign_id = $this->request->get("resign_id");
        $validation = [
            'staff_info_id' => 'Required|Int',
            'resign_id' => 'Int',
        ];
        Validation::validate(['staff_info_id' => $staffInfoId, 'resign_id' => $resign_id], $validation);
        $leaveManagerService = new LeaveManagerService();

        $res = $leaveManagerService->getStaffResignApprovalDetail($staffInfoId, $resign_id);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }


    /**
     * flash 导出离职pdf文件
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function leavePdfAction()
    {
        $staffInfoId = $this->request->get('staff_info_id');
        $validation = [
            'staff_info_id' => 'Required|Int'];
        Validation::validate(['staff_info_id' => $staffInfoId], $validation);

        $data=(new LeaveManagerService())->leavePdf($staffInfoId);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 修改离职日期
     * 离职日期修改范围：
     * 1、同步hris 修改离职日期接口（世龙接口）
     * 2、修改leave_manager表的leave_date 字段；
     * backyard来源的员工离职数据，在修改上述1、2两点之后，还要增加如下两处修改
     * 3、离职申请表（staff_resign）表的离职日期字段和最后工作日字段；
     * 4、hold 处理（ 露森 service)
     * @Permission(action='leave-assets-manager-v2.list')
     * @throws ValidationException
     */
    public function editLeaveDateAction()
    {
        $staffInfoId       = $this->request->get('staff_info_id');
        $edit_leave_date   = $this->request->get('edit_leave_date');
        $edit_short_notice = $this->request->get('edit_short_notice') ?: 0;
        $validation        = [
            'staff_info_id'     => 'Required|Int',
            'edit_leave_date'   => 'Required|Date',
            'edit_short_notice' => 'Required|IntGe:0',
        ];

        Validation::validate(['staff_info_id'     => $staffInfoId,
                              'edit_leave_date'   => $edit_leave_date,
                              'edit_short_notice' => $edit_short_notice,
        ], $validation);

        $leaveManagerService = new LeaveManagerService();
        $staffInfoService    = new StaffInfoService();
        //获取申请离职员工离职装态和申请离职来源等信息
        $staff_info = $staffInfoService->getStaffInfoByIdv4($staffInfoId, 'staff_info_id, state, hire_date, wait_leave_state, leave_source,leave_date');
        if (empty($staff_info)) {
            throw new ValidationException($this->t->_('staff_id_not_found'));
        }

        // 判断是否可以修改离职日期或者离职日期范围是否合法
        $leaveManagerService->isCanEditLeaveDate($staff_info, $edit_leave_date);

        $before_short_notice = 0;
        if (isCountry('MY')) {
            $leaveInfo = $leaveManagerService->getLeaveInfo($staffInfoId);
            $before_short_notice = $leaveInfo['edit_short_notice'] ?? $leaveInfo['short_notice'];
        }
        $before_leave_date = formatHrDate($staff_info['leave_date']);
        //离职日期修改逻辑处理开始
        $leave_manager_res = $leaveManagerService->edit_leave_manager_date($staff_info,$this->userinfo, $before_leave_date,$before_short_notice,$edit_leave_date,$edit_short_notice);
        if ($leave_manager_res) {
            //backyard 来源 离职申请
            if ($staff_info['leave_source'] == LeaveManagerService::LEAVE_SOURCE_BACKYARD) {
                $hold_res = $leaveManagerService->addLeaveDataByAdm($this->userinfo['id'], $staffInfoId, $edit_leave_date);
                //接口同步成功后同步修改离职申请表
                $resign_res = $leaveManagerService->edit_resign_leave_date($staffInfoId, $edit_leave_date);
                //失败返回处理
                if ($hold_res && $resign_res) {
                    return $this->returnJson(ErrCode::SUCCESS, 'success', []);
                } else {
                    $this->logger->info('editLeaveDate失败2：' . json_encode(['hold_res' => $hold_res, 'resign_res' => $resign_res]));
                    return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', ['hold_res' => $hold_res, 'resign_res' => $resign_res]);
                }
            } else {
                return $this->returnJson(ErrCode::SUCCESS, 'success', []);
            }

        }
        $this->logger->info('editLeaveDate失败1：' . json_encode([ 'leave_manager_res' => $leave_manager_res]));
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }


    /**
     * 撤销离职员工
     * @Permission(action='leave-assets-manager-v2.list')
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $staffInfoId = $this->getPost('staff_info_id');
        $reason = $this->getPost("reason");
        $is_cancel_staff_resign = $this->getPost("is_cancel_staff_resign");
        Validation::validate(
            [
                'staff_info_id' => $staffInfoId,
                'reason' => $reason,
            ],
            [
                'staff_info_id' => 'Required|Int',
                'reason' => 'Required|StrLenGeLe:1,500|>>>:cancel reason error',
            ]
        );
        $positions = HrStaffInfoPositionModel::find([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $this->userinfo['id'],
            ],
        ])->toArray();
        $categoryIds = implode(",", array_column($positions, 'position_category')) ;
        $this->userinfo['position_category'] = $categoryIds;
        $res = (new LeaveManagerService())->cancel(["staff_info_id" => $staffInfoId, "reason" => $reason, "is_cancel_staff_resign" => $is_cancel_staff_resign], $this->userinfo);
        $this->getDI()->get('logger')->info([
            'function' => 'cancelAction',
            'message' => '撤销员工操作',
            'staff_info_id' => $staffInfoId,
            'reason' => $reason,
            'result' => $res,
        ]);
        if ($res === true) {
            return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
        } else {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', $res);
        }
    }


    /**
     * flash 员工离职钱款查询接口
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function moneyAction()
    {
        $staffInfoId = $this->request->get('staff_info_id');
        Validation::validate(['staff_info_id' => $staffInfoId], [
            'staff_info_id' => 'Required|Int',
        ]);
        return $this->returnJson(ErrCode::SUCCESS, 'success', (new LeaveManagerService())->moneyInfo($staffInfoId));
    }


    /**
     * flash 员工离职资产编辑
     * @Permission(action='leave-assets-manager-v2.list')
     */
    public function singleEditAssetAction()
    {
        $staffInfoId = $this->getPost("staff_info_id");
        $type        = $this->getPost("type");
        $remark      = $this->getPost("remark");
        $state       = $this->getPost("state");
        Validation::validate(
            [
                'staffInfoId' => $staffInfoId,
                'type'        => $type,
                'state'       => $state,
            ], [
                'type'        => 'Required|StrIn:' . LeaveManagerService::TYPE_ASSETS,
                'state'       => 'Required|IntIn:1,2,3,4',
                'staffInfoId' => 'Required|Int',
            ]
        );
        if (!in_array($this->userinfo['id'], (new HoldManageService())->assetsManagerStaffs())) {
            throw new ValidationException($this->t->_('no_have_permission'));
        }

        $assets       = $this->getPost("assets");
        $assets       = array_filter($assets, function ($v) {
            return $v['goods_id'];
        });
        $publicAssets = $this->getPost("public_assets");
        $publicAssets = array_filter($publicAssets, function ($v) {
            return $v['goods_id'];
        });
        $batchAssets  = $this->getPost("batch_assets");
        $batchAssets  = array_filter($batchAssets, function ($v) {
            return $v['goods_id'];
        });
        if (!is_array($assets)) {
            throw new ValidationException('assets not json string');
        }
        if (!is_array($publicAssets)) {
            throw new ValidationException('publicAssets not json string');
        }
        if (!is_array($batchAssets)) {
            throw new ValidationException('batchAssets not json string');
        }
        $attachMents = $this->getPost('assets_object_key');

        $edit['assets_remand_state'] = $state;

        [$isOk, $msg] = (new LeaveManagerService())->editAssets($staffInfoId, $assets, $publicAssets, $batchAssets,
            $state, $remark, (array)$attachMents, $this->userinfo['id']);
        if (!$isOk) {
            $this->getDI()->get('logger')->info($msg . " staffId:" . $staffInfoId . " operaterId:" . $this->userinfo['id']);
            return $this->returnJson(ErrCode::SYSTEM_ERROR, $msg, []);
        }
        (new LeaveManagerService())->syncHold($staffInfoId, $edit);

        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * @Permission(action='leave-assets-manager-v2.list')
     * 编辑钱款信息
     * @throws Exception
     */
    public function singleEditMoneyAction()
    {
        $staffInfoId = $this->getPost("staff_info_id");
        $type        = $this->getPost("type");
        $remark      = $this->getPost("remark");
        $state       = $this->getPost("state");
        Validation::validate(
            [
                'staffInfoId' => $staffInfoId,
                'type'        => $type,
                'state'       => $state,
            ], [
                'type'        => 'Required|StrIn:' . LeaveManagerService::TYPE_MONEY,
                'state'       => 'Required|IntIn:1,2,3,4',
                'staffInfoId' => 'Required|Int',
            ]
        );
        if (!(new HoldManageService())->isHasPositionPermission($this->userinfo['id'], LeaveManagerService::POSITION_HEADQUARTER_ACCOUNT)) {
            throw new ValidationException($this->t->_('no_have_permission'));
        }
        $unpaid_money = $this->getPost("unpaid_money");
        $cashier_money = $this->getPost("cashier_money");
        if ($cashier_money === null || $cashier_money === '') {
            $cashier_money = '-1';
        }
        Validation::validate(
            [
                'unpaid_money' => $unpaid_money,
            ], [
                'unpaid_money' => 'Required|IntLt:********',
            ]
        );

        $edit['money_remand_state'] = $state;

        $attachMents = $this->getPost('money_object_key');
        [$isOk, $msg] = (new LeaveManagerService())->editMoney($staffInfoId, $unpaid_money, $cashier_money, $state, $remark, $attachMents, $this->userinfo['id']);
        if (!$isOk) {
            $this->getDI()->get('logger')->info($msg . " staffId:" . $staffInfoId . " operaterId:" . $this->userinfo['id']);
            return $this->returnJson(ErrCode::SYSTEM_ERROR, $msg, []);
        }
        (new LeaveManagerService())->syncHold($staffInfoId, $edit);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }


    /**
     * 废弃了，拆分成两个接口
     * @deprecated
     * flash 员工离职资产钱款编辑接口
     * @Permission(action='leave-assets-manager-v2.list')
     */
    public function editAction()
    {
        if ($this->request->isPost()) {
            $staffInfoId = $this->getPost("staff_info_id");
            $type = $this->getPost("type");
            $remark = $this->getPost("remark");
            $state = $this->getPost("state");
            Validation::validate(
                [
                    'staffInfoId' => $staffInfoId,
                    'type' => $type,
                    'state' => $state,
                ], [
                    'type' => 'Required|StrIn:' . LeaveManagerService::TYPE_ASSETS . ',' . LeaveManagerService::TYPE_MONEY,
                    'state' => 'Required|IntIn:1,2,3,4',
                    'staffInfoId' => 'Required|Int',
                ]
            );
            if ($type == LeaveManagerService::TYPE_ASSETS) {

                if (!in_array($this->userinfo['id'], (new HoldManageService())->assetsManagerStaffs())) {
                    throw new ValidationException($this->t->_('no_have_permission'));
                }

                $assets = $this->getPost("assets");
                $assets = array_filter($assets, function ($v) {
                    return $v['goods_id'];
                });
                $publicAssets = $this->getPost("public_assets");
                $publicAssets = array_filter($publicAssets, function ($v) {
                    return $v['goods_id'];
                });
                $batchAssets = $this->getPost("batch_assets");
                $batchAssets = array_filter($batchAssets, function ($v) {
                    return $v['goods_id'];
                });
                if (!is_array($assets)) {
                    throw new ValidationException('assets not json string');
                }
                if (!is_array($publicAssets)) {
                    throw new ValidationException('publicAssets not json string');
                }
                if (!is_array($batchAssets)) {
                    throw new ValidationException('batchAssets not json string');
                }
                $attachMents = $this->getPost('assets_object_key');
//                    foreach ($batchAssets as $batchAsset) {
//                        if (!isset($batchAsset['num']) || !isset($batchAsset['useing_num']) || $batchAsset['num'] > $batchAsset['useing_num']) {
//                            $this->ajax_return('请检查 useing_num num 字段', 1);
//                        }
//                    }

                $edit['assets_remand_state'] = $state;

                [$isOk, $msg] = (new LeaveManagerService())->editAssets($staffInfoId, $assets, $publicAssets, $batchAssets, $state, $remark, (array)$attachMents, $this->userinfo['id']);
            } else if ($type == LeaveManagerService::TYPE_MONEY) {
                if (!(new HoldManageService())->isHasPositionPermission($this->userinfo['id'], LeaveManagerService::POSITION_HEADQUARTER_ACCOUNT)) {
                    /**
                     *
                     * 权限判断 测试环境注释掉 线上打开！！！！！
                     */
                    throw new ValidationException($this->t->_('no_have_permission'));
                }
                $unpaid_money = $this->getPost("unpaid_money");
                $cashier_money = $this->getPost("cashier_money");
                if ($cashier_money === null || $cashier_money === '') {

                    $cashier_money = '-1';
                }
                Validation::validate(
                    [
                        'unpaid_money' => $unpaid_money,
                    ], [
                        'unpaid_money' => 'Required|IntLt:********',
                    ]
                );

                $edit['money_remand_state'] = $state;

                $attachMents = $this->getPost('money_object_key');
                [$isOk, $msg] = (new LeaveManagerService())->editMoney($staffInfoId, $unpaid_money, $cashier_money, $state, $remark, (array)$attachMents, $this->userinfo['id']);
            } else {
                throw new ValidationException('type类型错误');
            }
            if (!$isOk) {
                $this->getDI()->get('logger')->info($msg . " staffId:" . $staffInfoId . " operaterId:" . $this->userinfo['id']);
                return $this->returnJson(ErrCode::SYSTEM_ERROR, $msg, []);
            }
            (new LeaveManagerService())->syncHold($staffInfoId, $edit);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * flash 员工离职资产钱款编辑接口
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function editMoneyAction()
    {
        if (isCountry(['PH', 'TH'])) {
            //泰国 菲律宾 走异步
            throw new ValidationException($this->t['no_have_permission']);
        }

        if ($this->request->isPost()) {

            $staffIds = $this->getPost("staff_ids");
            $status = $this->getPost("status");

            //非总部会计角色没有编辑权限
            if (!(new HoldManageService())->isHasPositionPermission($this->userinfo['id'], LeaveManagerService::POSITION_HEADQUARTER_ACCOUNT)) {
                return $this->returnJson(ErrCode::SYSTEM_ERROR, $this->t['no_have_permission'], []);
            }

            $leaveManagerServer = new LeaveManagerService();
            $moneies = $leaveManagerServer->getLeaveInfos($staffIds);

            $money_backup = $leaveManagerServer->getOtherMoneys($staffIds);
            // 存在已处理状态，禁止操作 重新选择
            foreach ($moneies as $money) {
                if ($money['money_remand_state'] == LeaveManagerService::ASSETS_STATE_PROCESSED) {
                    return $this->returnJson(ErrCode::SYSTEM_ERROR, $this->t['leavemanager_edit_monry_error_1'], []);
                }
            }

            foreach ($staffIds as $staffId) {
                [$isOk, $msg]  = $leaveManagerServer->editMoney(
                    $staffId,
                    isset($money_backup[$staffId]) ? $money_backup[$staffId]['money'] : 0,
                    isset($money_backup[$staffId]) ? $money_backup[$staffId]['cashier_money'] : '-1',
                    $status,
                    '',
                    [],
                    $this->userinfo['id']);
                if ($isOk) {
                    $edit['money_remand_state'] = $status;
                    (new LeaveManagerService())->syncHold($staffId, $edit);
                } else {
                    return $this->returnJson(ErrCode::SYSTEM_ERROR, $msg, []);
                }
            }

            return $this->returnJson(ErrCode::SUCCESS, 'success', []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);

    }


    /**
     * @Permission(action='leave-manager-import-money')
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function getEditMoneyTemplateAction()
    {
        $t = $this->t;
        $country_code = strtolower(env('country_code', 'th'));
        $url          = $t->_($country_code . '_leave_manager_import_edit_money_template');
        if (empty($url)) {
            throw new BusinessException('请注意没有获取到对应语言的离职钱款导入模板!');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);

    }

    /**
     * 导入修改钱款
     * @Token
     * @Permission(action='leave-manager-import-money')
     * @throws ValidationException|BusinessException
     */
    public function importEditMoneyAction()
    {

        $params = (array)$this->request->get();
        Validation::validate($params, [
            'file_url' => 'Required|Str',
        ]);
        $data               = (new LeaveManagerService())->importEditMoney($this->user['id'],$params['file_url']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @description 获取下载结果列表
     * @Permission(action='leave-manager-import-money')
     * @Token
     */
    public function getImportEditMoneyResultAction()
    {
        $pageSize = $this->request->get('page_size');
        $pageNum  = $this->request->get('page_num');

        $params = [
            'type'        => AsyncImportTaskModel::EDIT_STAFF_LEAVE_MONEY,
            'operator_id' => $this->user['id'] ?? 0,
        ];

        //获取下载结果列表
        $data = (new AsyncImportTaskService())->list($params, $pageSize, $pageNum);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * flash 员工离职资产查询接口
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function assetsAction()
    {
        $staffInfoId = $this->request->get('staff_info_id');
        Validation::validate(
            ['staff_info_id' => $staffInfoId], [
                'staff_info_id' => 'Required|Int',
            ]
        );
        $data = (new LeaveManagerService())->assetsInfo($staffInfoId);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 资产申请书 管理书pdf
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws \Mpdf\MpdfException
     */
    public function deduction_noticeAction()
    {
        $staffId = $this->request->get('staff_id');
        $type = $this->request->get('type');
        $data = (new LeaveManagerService())->deduction_notice($staffId, $type);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 离职资产未还扣款须知协议 pdf
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function assetsDeductionProtocolPdfAction() {
        $staff_info_id = $this->request->get('staff_id');
        Validation::validate(
            ['staff_id' => $staff_info_id], [
                'staff_id' => 'Required|Int|>>>:staff_info_id error',
            ]
        );
        $data = (new LeaveManagerService())->getAssetsDeductionProtocolPdf(['staff_info_id' => $staff_info_id]);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 不验证登录信息
     * @return Response|ResponseInterface
     */
    public function assets_listAction()
    {
        $staffId             = $this->request->get('staff_id');
        $lang                = $this->request->get('lang');
        $is_new_asset        = $this->request->get('is_new_asset');
        $leaveManagerService = new LeaveManagerService();
        $assetsList          = [];

        if ($is_new_asset == 1) {
            $assetsList = $leaveManagerService->assetsByStaffIdNew($staffId, $lang);
        } else {
            $assets = $leaveManagerService->assetsByStaffId($staffId, $lang);
            foreach ($assets as $asset) {
                $assetsList[] = [
                    "name"      => $asset['name'],
                    "price"     => $asset['price'],
                    "sn_code"   => $asset['sn_code'],
                    "num"       => $asset['num'],
                    "all_price" => bcdiv($asset['price'] * 100 * $asset['num'], 100, 2),
                ];
            }
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', $assetsList);
    }

    /**
     * flash 员工资产与钱款查询接口
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function infoAction()
    {
        $params = $this->request->get();
        $validations = [
            'staff_info_id'  => 'Required|Int',        //工号
        ];
        Validation::validate($params, $validations);

        $result = (new LeaveManagerService())->userStateInfo($params);
        if(!empty($result)) {
            return $this->returnJson(ErrCode::SUCCESS, '', ['data' => $result]);
        } else {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
        }
    }

    /**
     * 离职记录列表
     * @Permission(action='leave-assets-manager-v2.log.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function logListAction() {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        // 当前端传递0 时 会被array_filter 过滤掉
        if (!isset($params['is_sub_department'])) {
            $params['is_sub_department'] = 0;
        }

        $validation = [
            'page' => 'Required|Int|>>>:page error',
            'size' => 'Required|Int|>>>:size error',
        ];
        Validation::validate($params, array_merge($validation, LeaveManageListService::$validate_leave_log_list_params));
        $params['user'] = $this->user;
        $result = (new LeaveManageListService())->leaveLogList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 离职记录详情
     * @Permission(action='leave-assets-manager-v2.log.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function logDetailAction() {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        $validation = [
            'page' => 'Required|Int|>>>:page error',
            'size' => 'Required|Int|>>>:size error',
            'staff_info_id' => 'Required|Int|>>>:staff_info_id error',
        ];
        Validation::validate($params, $validation);

        $result = (new LeaveManageListService())->leaveLogDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);

    }

    /**
     * 申请离职列表
     * @Permission(action='leave-assets-manager-v2.log.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function applyResignListAction()
    {
        $params = $this->convertParams($this->request->get());
        $validation = [
            'page' => 'Required|Int|>>>:page error',
            'size' => 'Required|Int|>>>:size error',
            'staff_info_id' => 'Required|Int|>>>:staff_info_id error',
        ];
        Validation::validate($params, $validation);
        $result = (new LeaveManageListService())->staffRegionList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 获取离职申请 hris编辑时用
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStaffResignTipAction()
    {
        $params = $this->convertParams($this->request->get());
        $validation = [
            'staff_info_id' => 'Required|Int|>>>:staff_info_id error',
        ];
        Validation::validate($params, $validation);
        $result = (new LeaveManageListService())->get_staff_lasted_resign($params['staff_info_id']);
        $result['tip'] = '';
        $today = gmdate('Y-m-d', time() + ($this->getDi()->get('config')->application->add_hour) * 3600);
        if (!empty($result) && $result['status'] == 2 && date('Y-m-d',strtotime($result['leave_date'])) > $today){
            $result['tip'] = $this->t->_('staff_resign_tip_1', ['leave_date' => $result['leave_date']]);
            return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * @Token
     * @Permission(action='leave-assets-manager-v2.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function resendRenewBusinessAction()
    {
        $params = $this->convertParams($this->request->get());
        $validation = [
            'contract_business_id' => 'Required|Int|>>>:contract_business_id error',
        ];
        Validation::validate($params, $validation);
        $params['user'] = $this->user;
        $result = (new RenewContractBusinessService())->resendRenewContractUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function checkFileListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'staff_info_id' => 'Required|IntGt:0',
        ];
        Validation::validate($params, $validate_rule);

        $result = (new LeaveManageListService())->checkFileList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-再次发送邮件
     * @Token
     * @Permission(action='suspension_list')
     */
    public function sendEmailAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'id'    => 'Required|IntGt:0',
            'email' => 'Required|Email|>>>:' . $this->t->_('email_is_error'),
        ];
        Validation::validate($params, $validate_rule);

        $result = (new LeaveManageListService())->sendEmail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 离职问卷列表
     * @Permission(action='leave-questionnaire-list')
     */
    public function questionnaireListAction()
    {
        $params = $this->request->get();
        $validation = [
            'page' => 'Required|Int|>>>:page error',
            'size' => 'Required|Int|>>>:size error',
        ];
        Validation::validate($params, $validation);
        $params['user'] = $this->user;
        $result = (new LeaveManageListService())->getStaffQuestionnaireList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     *  离职问卷导出
     * @Permission(action='leave-questionnaire-list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function questionnaireExportAction()
    {
        $params = $this->request->get();
        $params['user']  = $this->user;
        $params['lang']      = $this->locale;
        $params['file_name'] = uniqid('Resignation_Questionnaire_' . date('YmdHis')).'.xlsx';

        $result = (new LeaveManageListService())->staffQuestionnaireExport($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t['downloading'], $result);

    }

    /**
     * 申请离职列表（包含离职问卷）
     * @Permission(action='leave-questionnaire-list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function authorizedPersonnelApplyResignListAction()
    {
        $params = $this->convertParams($this->request->get());
        $validation = [
            'staff_info_id' => 'Required|Int|>>>:staff_info_id error',
        ];
        Validation::validate($params, $validation);
        $result = (new LeaveManageListService())->staffRegionQuestionnaireList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 离职文件详情
     * @Permission(action='leave-questionnaire-list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function questionnaireDetailAction()
    {
        $params = $this->convertParams($this->request->get());
        $validation = [
            'resign_id' => 'Required|Int|>>>:resign_id error',
        ];
        Validation::validate($params, $validation);
        $result = (new LeaveManageListService())->staffQuestionnaireDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }



}