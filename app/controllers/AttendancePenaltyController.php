<?php

namespace App\Controllers;

use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AttendancePenaltyService;
use App\Services\ExcelService;
use App\Library\ErrCode;

class AttendancePenaltyController extends BaseController
{
    public $paramIn;
    public $userinfo;
    public $staff_service;

    public function initialize()
    {
        parent::initialize();
    }


    /**
     * 静态枚举
     * @Token
     */
    public function staticAction()
    {
        $data = (new AttendancePenaltyService())->staticData();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 列表
     * @Token
     * @Permission(action='penalty_view')
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'staff_name'     => 'StrLenGeLe:0,50|>>>:[store_id] params error',
            'staff_info_id'  => 'IntLe:999999999|>>>:'.$this->t->_('sign_msg_021'),
            'region_id'      => 'StrLenGeLe:0,30|>>>:[region_id] params error' ,
            'piece_id'       => 'Arr|>>>:[piece_id]] params error' ,
            'store_ids'      => 'Arr|>>>:[store_ids] params error',
            'penalty_state'  => 'Arr|>>>:[penalty_state]] params error',
            'penalty_reason' => 'Arr|>>>:[penalty_reason]] params error',
            'page'           => 'Required|Int|IntGe:1|>>>:[page] params error',
            'size'           => 'Required|Int|>>>:[page size] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $data               = (new AttendancePenaltyService())->list($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 详情
     * @Token
     * @Permission(action='penalty_view')
     * @throws ValidationException
     * @throws BusinessException
     */
    public function detailAction()
    {
        $params = $this->request->get();

        $validation = [
            'id' => 'Required|IntLe:9999999999|>>>:[id] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $data               = (new AttendancePenaltyService())->detail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 审批详情
     * @Token
     * @Permission(action='penalty_view')
     * @throws ValidationException
     * @throws BusinessException
     */
    public function auditDetailAction()
    {
        $params = $this->request->get();

        $validation = [
            'appeal_id' => 'Required|IntLe:9999999999|>>>:[appeal_id] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $data               = (new AttendancePenaltyService())->auditDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 导出
     * @Token
     * @Permission(action='attendance_penalty_export')
     * @throws ValidationException
     * @throws \Exception
     */
    public function exportListAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);

        // 公共验证字段
        $validation = [
            'staff_name'     => 'StrLenGeLe:0,50|>>>:[store_id] params error',
            'staff_info_id'  => 'IntLe:999999999|>>>:' . $this->t->_('sign_msg_021'),
            'store_ids'      => 'Arr|>>>:[store_ids] params error',
            'penalty_state'  => 'Arr|>>>:[penalty_state]] params error',
            'penalty_reason' => 'Arr|>>>:[penalty_reason]] params error',
            'start_date'     => 'Required|Date|>>>:' . $this->t->_('start_date_cannot_empty'),
            'end_date'       => 'Required|Date|>>>:' . $this->t->_('end_date_cannot_empty'),
        ];

        Validation::validate((array)$params, $validation);

        if (strtotime($params['end_date']) - strtotime($params['start_date']) > 86400 * 30) {
            throw new ValidationException($this->t->_('date_span_limit', ['num' => 30]));
        }

        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        $params['file_name'] = 'attendance_deduction_'.date('YmdHis').'.xlsx';
        $params['is_export'] = 1;
        $action_name         = 'attendance-penalty'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'export';

        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 导出处罚
     * @Token
     * @Permission(action='attendance_penalty_export_penalty')
     * @throws ValidationException
     */
    public function exportPenaltyAction()
    {
        $params = $this->request->get();
        $params['month'] = $this->request->get('month', 'trim', date('Y-m'));

        $validation = [
            'month'      => 'Required|StrLenGeLe:0,10|>>>:[month] params error',
            'month_type' => 'Required|IntIn:1,2|>>>:[month_type] params error',
        ];
        Validation::validate((array)$params, $validation);

        if ($params['month_type'] == 1) {
            $params['start_date'] = date('Y-m-01', strtotime($params['month']));
            $params['end_date']   = date('Y-m-15', strtotime($params['month']));
        } else {
            $params['start_date'] = date('Y-m-16', strtotime($params['month']));
            $params['end_date']   = date('Y-m-t', strtotime($params['month']));
        }

        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        $params['file_name'] = 'penalty_'.date('YmdHis').'.xlsx';
        $params['is_export'] = 1;
        $action_name         = 'attendance-penalty'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'exportPenalty';
        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

}