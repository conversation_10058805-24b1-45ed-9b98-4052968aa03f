<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/15
 * Time: 10:13 PM
 */

namespace App\Controllers;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\EaFormReportModel;
use App\Models\backyard\FileOssUrlModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\StaffPayrollModel;
use App\Models\backyard\StaffPayslipReportModel;
use App\Modules\My\Services\AgreementSignRecordService;
use App\Modules\My\Services\FrontlineSalaryInfoService;
use app\modules\My\services\HubStorePartitionService;
use App\Modules\My\Services\PayrollReportService;
use App\Modules\My\Services\PayrollService;
use App\Modules\My\Services\StaffSalaryService;
use App\Modules\Ph\Services\HoldManageService as HoldManageService_PH;
use App\Modules\Ph\Services\DriverCrowdsourcingService;
use App\Services\ApprovalJurisdictionsService;
use App\Services\AttendanceConfigService;
use App\Services\AttendanceStatisticsService;
use App\Services\BlackListService;
use App\Services\BusinessTripService;
use app\services\DailyFrontlineSalaryConfigService;
use App\Services\DictionaryService;
use App\Services\FrontlineSalaryConfigService;
use App\Services\HoldManageService;
use App\Services\CertificateService;
use App\Services\FpsPermissionSystemService;
use App\Services\HolidayService;
use App\Services\HrProbationContractService;
use App\Services\HrStaffService;
use App\Services\HubOutSourcingStaffService;
use App\Services\JobTransferSalaryService;
use App\Services\LeaveManagerService;
use App\Services\MessagesService;
use App\Services\OssService;
use App\Services\PayrollFileService;
use App\Services\ProbationService;
use app\services\ProbationTargetRpcService;
use App\Services\ProbationTargetService;
use App\Services\QuestionnaireLibService;
use App\Services\RenewContractBusinessService;
use App\Services\rpc\FinanceRpcService;
use App\Services\SalaryService;
use App\Services\SchedulingSuggestionService;
use App\Services\StaffInfoService;
use App\Services\staffManage\PartnerStaffService;
use App\Services\StaffNotRefundedSuspensionService;
use App\Services\StaffService;
use App\Services\StaffShiftService;
use App\Services\StoreClassService;
use App\Services\SuspensionService;
use App\Services\SvcService;
use App\Services\SysService;
use app\services\ToolStaffInfoService;
use App\Services\UserService;
use App\Services\VehicleInspectionService;
use App\Services\WarningService;
use App\Services\WorkdayService;
use App\Services\FfmStaffInfoService;
use App\Services\AttendanceWhiteListService;
use App\Services\staffManage\OutsourcingStaffService;
use App\Services\WorkdaySettingService;
use JsonRPC\Server as rpc_server;
use Exception;

class SvcController extends BaseController
{

    public function callAction()
    {
        if (function_exists('molten_get_traceid')) {
            header('traceid:' . molten_get_traceid());
        }
        $server = new rpc_server();
        $procedureHandler = $server->getProcedureHandler();
        $procedureHandler
            //backyard APP工资条页面调用
            ->withCallback('get_salary_data', function ($local, $param) {
                try {
                    $this->getDI()->get('logger')->write_log('svc get_salary_data request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    $locale = $local['locale'] ?? 'th';
                    BaseService::setLanguage($locale);
                    /**
                     * @see \App\Modules\La\Services\SalaryService
                     * @see \App\Modules\Vn\Services\SalaryService
                     * @see \App\Modules\Ph\Services\SalaryService
                     * @see \App\Modules\My\Services\SalaryService
                     * @see \App\Modules\Id\Services\SalaryService
                     * const SALARY_SOURCE_TASK = 0;//工资条任务 调用来源
                    const SALARY_SOURCE_DATA = 1;//by 获取工资数据 来源
                    const SALARY_SOURCE_URL = 2;//by 获取 工资条pdf 地址 来源
                    const SALARY_SOURCE_UNFIX_URL = 3;//by 获取 非固定薪资pdf 来源 印尼定制
                     *
                     */
                    if ($service = getCountryServicePath('SalaryService')) {
                        $bll = new $service;
                    } else {
                        $bll = new SalaryService();
                    }
                    $staff_id = $param['staff_id'];
                    if (!isset($param['month'])) {
                        return ['code' => -1, 'msg' => 'params error'];
                    }
                    $is_by = $param['is_by'] ?? Enums::SALARY_SOURCE_DATA;

                    if (isset($param['run_type']) && isCountry('MY')) {
                        $data = (new PayrollReportService())->payslipForBy($staff_id, $param['month'], $param['run_type']);
                    } else {
                        $data = $bll->format_pdf($staff_id, $param['month'], $is_by);
                        $data = empty($data) ? [] : $data;
                    }
                    $this->getDI()->get('logger')->write_log('svc get_salary_data   res:'.json_encode($data,JSON_UNESCAPED_UNICODE), 'info');
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() .'|'. $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' => $e->getMessage()];
                }
                return ['code' => 1,'msg' => 'success','data' => $data];
            })
            //backyard 模拟登录后 获取 生成的薪资条数据
            ->withCallback('get_payroll_pdf', function ($local, $param) {
                try {

                    $this->getDI()->get('logger')->write_log('svc get_payroll_pdf request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    if (empty($param['staff_id'])) {
                        return ['code' => -1, 'msg' => 'Illegal call'];
                    }
                    $locale = $local['locale'] ?? 'en';//默认都是英文
                    $locale = substr($locale,0,2);
                    BaseService::setLanguage($locale);
                    $language = FileOssUrlModel::$lang_list;
                    $lang = $language[$locale] ?? $language['en'];//默认取英文 3
                    /**
                     * @see \App\Modules\La\Services\SalaryService
                     * @see \App\Modules\Ph\Services\SalaryService
                     * @see \App\Modules\My\Services\SalaryService
                     * @see \App\Modules\Id\Services\SalaryService
                     * @see \App\Modules\Vn\Services\SalaryService
                     */

                    if ($service = getCountryServicePath('SalaryService')) {
                        $bll = new $service;
                    } else {
                        $bll = new SalaryService();
                    }

                    $p['month'] = $param['month'];
                    $p['lang'] = $lang;
                    $oss_bll = new OssService();
                    if (isCountry('vn')) {
                        $url = $oss_bll->get_infoV2($param['staff_id'],OssService::SALARY_PDF,$p);
                    }else {
                        $url = $oss_bll->get_info($param['staff_id'],OssService::SALARY_PDF,$p);
                    }

                    if(empty($url)){
                        $url = $bll->format_pdf($param['staff_id'],$param['month'],Enums::SALARY_SOURCE_URL,$lang);
                        $url = empty($url) ? '' : $url;
                    }
                    $this->getDI()->get('logger')->write_log('svc get_payroll_pdf   res:'.json_encode($url,JSON_UNESCAPED_UNICODE), 'info');
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() .'|'. $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' => 'sys error'];
                }
                return ['code' => 1,'msg' => 'success','data' => $url];
            })
            // 印尼 另外一个 工资条的pdf
            ->withCallback('get_unfixed_pdf', function ($local, $param) {
                try {

                    $this->getDI()->get('logger')->write_log('svc get_unfixed_pdf request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    $locale = $local['locale'] ?? 'en';//默认都是英文
                    $locale = substr($locale,0,2);
                    BaseService::setLanguage($locale);
                    $language = FileOssUrlModel::$lang_list;
                    $lang = $language[$locale] ?? $language['en'];//默认取英文 3

                    if ($service = getCountryServicePath('SalaryService')) {
                        $bll = new $service;
                    } else {
                        $bll = new SalaryService();
                    }

                    $p['month'] = $param['month'];
                    $p['lang'] = $lang;
                    $oss_bll = new OssService();
                    $url = $oss_bll->get_info($param['staff_id'],OssService::SALARY_UNFIXED_PDF,$p);

                    if(empty($url)){
                        //没有 url 重新生成
                        $url = $bll->format_pdf($param['staff_id'],$param['month'],Enums::SALARY_SOURCE_UNFIX_URL,$lang);
                        $url = empty($url) ? '' : $url;
                    }
                    $this->getDI()->get('logger')->write_log('svc get_payroll_pdf   res:'.json_encode($url,JSON_UNESCAPED_UNICODE), 'info');
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() .'|'. $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' => $e->getMessage(). $e->getFile().$e->getLine()];
                }
                return ['code' => 1,'msg' => 'success','data' => $url];
            })
            //backyard 模拟登录后 获取 生成的 工资相关文件  PCB EA form 新版本工资条 只有马来使用
            ->withCallback('get_payroll_report', function ($local, $param) {
                try {

                    $this->logger->info('svc get_payroll_report request: ' .json_encode($param,JSON_UNESCAPED_UNICODE));
                    $locale = $local['locale'] ?? 'th';
                    BaseService::setLanguage($locale);
                    if (!isCountry('MY')) {
                        return ['code' => -1, 'msg' =>'Not currently supported','data' => null];
                    }
                    //  pcb  year type =1
                    //工资条  month run_type cycle_start cycle_end
                    $service = new PayrollReportService();
                    if (isset($param['type']) && !in_array($param['type'],[1,2,4])) {
                        return ['code' => -1, 'msg' =>'parameter is invalid','data' => null];
                    }
                    if (in_array($param['type'],[1,2]) && !$param['year']) {
                        return ['code' => -1, 'msg' =>'parameter is invalid','data' => null];
                    }
                    if ($param['type'] == StaffPayslipReportModel::FILE_TYPE_4
                        && (
                            empty($param['month'])
                            || empty($param['run_type'])
                            || empty($param['cycle_start'])
                            || empty($param['cycle_end'])
                        )
                    ) {
                        return ['code' => -1, 'msg' =>'parameter is invalid','data' => null];
                    }
                    $type = $param['type'] ?? 0;
                    $year = $param['year'] ?? '';
                    $month = $param['month'] ?? '';
                    $run_type = $param['run_type'] ?? '';
                    $cycle_start = $param['cycle_start'] ?? '';
                    $cycle_end = $param['cycle_end'] ?? '';
                    $staff_info_id = $param['staff_id'] ?? 0;
                    $companyId = $param['company_id'] ?? null;
                    $url='';
                    if (StaffPayslipReportModel::FILE_TYPE_1 == $type) {
                       $file = $service->downLoadReportFile($year, [$staff_info_id],$type,$companyId);
                        $url = $file[0]['object_url'] ?? '';
                    }
                    // ea  year=2022  type=2  lang_type =2(马来语言的ea 文件)  or  ( year=2022  type=2 company_id=1)
                    if (StaffPayslipReportModel::FILE_TYPE_2 == $type) {
                        if (empty($companyId)) {
                            return ['code' => -1, 'msg' =>'parameter is invalid','data' => null];
                        }
                        $url = $service->getSingleEAForm($staff_info_id, $companyId, $year,$param['lang_type']?? EaFormReportModel::LANG_EN);
                    }
                    if (StaffPayslipReportModel::FILE_TYPE_4 == $type) {
                      $bind = ['staff_info_id' => $staff_info_id,'month' => $month,'run_type' => $run_type,'cycle_start' => $cycle_start,'cycle_end' => $cycle_end,'click_send'=>StaffPayrollModel::CLICK_SEND];
                      $find =  StaffPayrollModel::findFirst([
                            'columns' => 'id',
                            'conditions' => 'staff_info_id=:staff_info_id:  and  month=:month: and run_type=:run_type: and cycle_start=:cycle_start: and cycle_end=:cycle_end:  and paid_date is not null and click_send=:click_send: and is_deleted=0',
                            'bind' => $bind,
                        ]);
                      if ($find) {
                          $file = $service->downLoadPaySlip([$find->id]);
                          $url = $file[0]['object_url'] ?? '';
                      } else {
                          return ['code' => -1, 'msg' =>'not found'];
                      }
                    }
                    $this->logger->info('svc get_payroll_report   res:'.json_encode([$url,$bind??[]],JSON_UNESCAPED_UNICODE));
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() .'|'. $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' => 'system error'];
                }
                return ['code' => 1,'msg' => 'success','data' => $url];
            })
            //by 获取 工资展示 月份(MY类型)列表
            ->withCallback('get_salary_type_list', function ($local, $param) {
                try {
                    $locale = $local['locale'] ?? 'en';
                    BaseService::setLanguage($locale);
                    if (!isset($param['staff_id'])) {
                        return ['code' => -1, 'msg' => 'param error'];
                    }
                    /**
                     * @see \App\Modules\My\Services\SalaryService
                     */
                    if ($service = getCountryServicePath('SalaryService')) {
                        $bll = new $service;
                    } else {
                        $bll = new SalaryService();
                    }
                    $salaryTypeList =$bll->getSalaryTypeList($param['staff_id']);

                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() .'|'. $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' =>'system error'];
                }
                $this->logger->info('svc get_salary_type_list request: ' .json_encode([$param,$salaryTypeList],JSON_UNESCAPED_UNICODE));
                return ['code' => 1,'msg' => 'success','data' => $salaryTypeList];
            })
            //by 获取 统计员工是否可以申请OT的对比薪酬金额
            ->withCallback('get_ot_salary_amount', function ($local, $param) {
                try {
                    $locale = $local['locale'] ?? 'en';
                    BaseService::setLanguage($locale);
                    if (!isset($param['staff_id'], $param['month'])) {
                        return ['code' => -1, 'msg' => 'param error'];
                    }
                    if (!isCountry('MY')) {
                        return ['code' => -1, 'msg' => 'Countries not supported for query'];
                    }
                    $amount = (new PayrollService())->otSalaryAmount($param['month'], $param['staff_id']);
                } catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() . '|' . $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' => 'system error'];
                }
                return ['code' => 1, 'msg' => 'success', 'data' => ['amount_total' => $amount]];
            })
	        //backyard 休息日请假撤销验证
	        ->withCallback('by_revoke_check', function ($local, $param) {
		        try {
			        $locale = $local['locale'] ?? 'th';
			        $this->getDI()->get('logger')->write_log('svc by_revoke_check request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    /**
                     * @see \App\Modules\Ph\Services\WorkdayService
                     * @see \App\Modules\My\Services\WorkdayService
                     */
                    if ($service = getCountryServicePath('WorkdayService')) {
                        $bll = new $service;
                    } else {
                        $bll = new WorkdayService();
                    }
			        $staff_info_id = $param['staff_info_id'] ?? 0;
			        $leave_start_time = $param['leave_start_time'] ?? '';
			        $leave_end_time = $param['leave_end_time'] ?? '';
			        $leave_type = $param['leave_type'] ?? '';
			
			        $data = $bll->by_revoke_check($staff_info_id,$leave_start_time,$leave_end_time,$leave_type,$locale);
			        $this->getDI()->get('logger')->write_log('svc by_revoke_check   res:'.json_encode($data,JSON_UNESCAPED_UNICODE), 'info');
		        }catch (\Exception $e) {
			        $this->getDI()->get('logger')->write_log($e->getTraceAsString() . $e->getMessage(), 'error');
			        return ['code' => -1, 'msg' => $e->getMessage(). $e->getFile().$e->getLine(),'data'=>[]];
		        }
		        return ['code' => 1,'msg' => 'success','data' => $data];
	        })
            //网点主管代付停职员工公款 目前只有 ph  代还公款
            ->withCallback('director_pay_on_behalf', function ($local, $param) {
                try {
                    $this->getDI()->get('logger')->write_log('svc director_pay_on_behalf  request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    $bll = new HoldManageService_PH();
                    $data = $bll->syncDirectorPayOnBehalfHold($param);
                    $this->getDI()->get('logger')->write_log('svc director_pay_on_behalf  res:'.json_encode($data,JSON_UNESCAPED_UNICODE), 'info');
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() . $e->getMessage(), 'error');
                    return ['code' => -1, 'msg' => $e->getMessage(). $e->getFile().$e->getLine(),'data'=>[]];
                }
                return ['code' => 1,'msg' => 'success','data' => []];
            })
            //fps权限系统获取角色数据
            ->withCallback('fps_roles_list', function ($local, $param) {
                try {
                    $locale = $local['locale'];
                    if(!in_array($locale,['zh','th','en'])){
                        return ['code' => -1,'msg' => 'local error','data' => []];
                    }
                    $this->getDI()->get('logger')->write_log('svc fps_roles_list request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    $bll = new FpsPermissionSystemService();
                    $data = $bll->getMsRolesList($locale,$param);
                    $this->getDI()->get('logger')->write_log('svc by_revoke_check   res:'.json_encode($data,JSON_UNESCAPED_UNICODE), 'info');
                    return ['code' => 1,'msg' => 'success','data' => $data];
                }catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return ['code' => -1, 'msg' => $e->getMessage(). $e->getFile().$e->getLine(),'data'=>[]];
                }
            })
            //fps权限系统获取部门列表数据
            ->withCallback('fps_department_list', function ($local, $param) {
                try {
                    $locale = $local['locale'];

                    $this->getDI()->get('logger')->write_log('svc fps_department_list request: ' .json_encode($param,JSON_UNESCAPED_UNICODE) , 'info');
                    $data = (new FpsPermissionSystemService())->getDepartmentList($param);
                    $this->getDI()->get('logger')->write_log('svc fps_department_list   res:'.json_encode($data,JSON_UNESCAPED_UNICODE), 'info');
                    return ['code' => 1,'msg' => 'success','data' => $data];
                }catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return ['code' => -1, 'msg' => $e->getMessage(). $e->getFile().$e->getLine(),'data'=>[]];
                }
            })
            //发送警告书给上级或者证明人
            ->withCallback('sendWarningMessage', function ($local, $param) {
                try {
                    $countryCode = ucfirst(env('country_code'));
                    $WarningService = 'App\Modules\\' .$countryCode. '\Services\WarningService';
                    if (class_exists($WarningService)) {
                        $bll = new $WarningService;
                    } else {
                        $bll = new WarningService();
                    }
                    $bll->sendMessage($param['staff_ids'], $param['role'], $param['warning_id'], $param['level'], $param['witness'], $param['related_id']);

                    return [
                        "code" => 1,
                        "msg" => "ok",
                        "data" => $param
                    ];
                } catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log($e->getTraceAsString() . $e->getMessage() . " sendWarningMessage", "error");
                    return array('code' => -1, 'msg' => $e->getMessage());
                }
            })
            //薪资证明 下载接口  backyard 证明下载用 目前只有 ph th my
            ->withCallback('by_download', function ($local, $data) {
                try {
                    $this->logger->info("by_download data :" . json_encode($data));
                    if (!isCountry(['th','ph','my'])) {
                        throw new ValidationException("Not currently supported");
                    }
                    $staff_id = $data['staff_info_id'];
                    $type = intval($data['type']);
                    $type_arr['use_type'] = intval($data['sub_type'] ?? 0);
                    if ($type == 1) {//证明 工资条 分语言环境
                        if (in_array($local['locale'], array('zh', 'zh-CN')))
                            $type = OssService::SALARY_PDF_CN;
                        if ($local['locale'] == 'en')
                            $type = OssService::SALARY_PDF_EN;
                    }
                    $type_arr['file_type'] = $type;// $type 证明类型  1- 工资条 2- 在职证明  3- 就业证明（离职证明） 4 5 薪资证明  7 cimb(my)
                    $date = empty($data['date']) ? array() : $data['date'];
                    $mail_type = 1;
                    if (!empty($data['mail_type']))
                        $mail_type = intval($data['mail_type']);
                    $countryCode = ucfirst(env('country_code'));
                    /**
                     * @see \App\Modules\Ph\Services\CertificateService
                     * @see \App\Modules\My\Services\CertificateService
                     */
                    $certificateService = 'App\Modules\\' . $countryCode . '\Services\CertificateService';
                    if (class_exists($certificateService)) {
                        $bll = new $certificateService;
                    } else {
                        $bll = new CertificateService();
                    }
                    $bll::setLanguage($local['locale'] ?? 'en');
                    $sendEmail = $data['is_send']  ?? 1;
                    $data = $bll->certificate_download($staff_id, $type_arr , $date, 3,$mail_type,$sendEmail);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            // my 证明下载 : <在职证明>或<CIMB开户函> data
            ->withCallback('proof_download_data', function ($local, $data) {
                try {
                    $this->logger->info("proof_download_data :" . json_encode($data));
                    if (!isCountry('my')) {
                        throw new ValidationException("Not currently supported");
                    }
                    $staff_id = $data['staff_id'];
                    $type     = intval($data['type']);
                    $bll = new \App\Modules\My\Services\CertificateService;
                    $bll::setLanguage($local['locale'] ?? 'en');
                    $data = [];
                    if (in_array($type,[$bll::CERTIFICATE_TYPE_ON_JOB,$bll::CERTIFICATE_TYPE_CIMB])) {
                        $data = $bll->onJobAndCIMBData($staff_id, false);
                    }

                    return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data];
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() . '|' . $e->getMessage());
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            //同步hold
            ->withCallback('synchronize_hold', function ($local, $params) {
                try {
                    $locale = $local['locale'];

                    $this->getDI()->get('logger')->write_log(['function' => 'svc_synchronize_hold', 'params' => $params], 'info');
                    $countryCode = ucfirst(env('country_code'));
                    $certificateService = 'App\Modules\\' . $countryCode . '\Services\HoldManageService';
                    if (class_exists($certificateService)) {
                        $bll = new $certificateService;
                    } else {
                        $bll = new HoldManageService();
                    }

                    $data = $bll->synchronizeHoldStaff($params);
                    if($data == 0) {
                        $this->logger->error(['function' => 'svc_synchronize_hold', 'params' => $params, 'result' => $data]);
                        return ['code' => ErrCode::SYSTEM_ERROR,'msg' => 'error','data' => $data];
                    }
                    $this->getDI()->get('logger')->write_log(['function' => 'svc_synchronize_hold', 'params' => $params, 'result' => $data], 'info');
                    return ['code' => 1,'msg' => 'success','data' => $data];
                }catch (\Exception $e) {
                    $this->logger->error(['function' => '', 'message' => $e->getTraceAsString() . $e->getMessage()]);
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //离职申请 撤销驳回调用 释放hold
            ->withCallback('sync_release_hold', function ($local, $params) {
                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'sync_release_hold', 'params' => $params, 'locale' => $locale]);
                    $staffService = new HoldManageService();
                    $staffService->pushAsyncReleaseHold($params);
                    $this->logger->info(['function' => 'sync_release_hold', 'params' => $params, 'result' => 'success']);

                    return ['code' => 1,'msg' => 'success','data' => null];
                }catch (\Exception $e) {
                    $this->logger->warning(['function' => 'svc_sync_release_hold', 'message' => $e->getTraceAsString() . $e->getMessage()]);
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //问卷消息-详情
            ->withCallback('questionnaire_detail', function ($local, $params) {
                try {
                    $this->logger->info("questionnaire_detail data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->questionnaireDetail($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷消息-提交问题结果
            ->withCallback('questionnaire_submit', function ($local, $params) {
                try {
                    $this->logger->info("questionnaire_submit data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->questionnaireSubmit($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //问卷题库模板列表
            ->withCallback('getQuesTemplateList', function ($local, $params) {
                try {
                    $this->logger->info("getQuesTemplateList data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->getQuesListInfoSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //问卷题库模板-业务应用绑定
            ->withCallback('bindQuesTemplate', function ($local, $params) {
                try {
                    $this->logger->info("bindQuesTemplate data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->bindQuesTemplateSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {

                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {

                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷题库统计
            ->withCallback('questionStatistics', function ($local, $params) {
                try {
                    $this->logger->info("questionStatistics data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->questionStatisticsSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷题库-填空题统计
            ->withCallback('questionCompletion', function ($local, $params) {
                try {
                    $this->logger->info("questionCompletion data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->questionCompletionSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷题库-文件上传题统计
            ->withCallback('questionUploadFileList', function ($local, $params) {
                try {
                    $this->logger->info("questionUploadFileList data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->questionUploadFileListSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷题库-题库详情BY端
            ->withCallback('getQuestionDetail', function ($local, $params) {
                try {
                    $this->logger->info("getQuestionDetail data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->getQuestionDetailSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷题库-答案提交
            ->withCallback('submitAnswer', function ($local, $params) {
                try {
                    $this->logger->info("submitAnswer data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->submitAnswerSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷题库详情-业务绑定的题库
            ->withCallback('getQuestionRead', function ($local, $params) {
                try {
                    $this->logger->info("getQuestionRead data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->getQuestionReadSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //问卷结果下载
            ->withCallback('resultDownload', function ($local, $params) {
                try {
                    $this->logger->info("resultDownload data :" . json_encode($params));
                    $service = new QuestionnaireLibService();
                    $service::setLanguage($local['locale'] ?? 'en');
                    $data = $service->resultDownloadSvc($params);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                }
            })
            //hcm 管辖范围变动
            ->withCallback('approval_jurisdiction_changes', function ($local, $params) {
                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'approval_jurisdiction_changes', 'params' => $params]);
                    $bll = new ApprovalJurisdictionsService();
                    $result = $bll->redisListAdd($params);
                    $this->logger->info(['function' => 'approval_jurisdiction_changes', 'params' => $params, 'result' => $result]);
                    return $result;
                }catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);

                }
            })
            //ffm 获取员工档案信息 列表
            ->withCallback('ffm_get_staff_list', function ($local, $params) {
                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'ffm_get_staff_list', 'params' => $params, 'locale' => $locale]);
                    $FfmStaffInfoService = new  FfmStaffInfoService();
                    $result = $FfmStaffInfoService->getStaffList($params);
                    $this->logger->info(['function' => 'ffm_get_staff_list', 'params' => $params, 'result' => $result]);
                    return $result;
                }catch (\Exception $e) {
                    $this->logger->warning(['function' => 'ffm_get_staff_list', 'message' => $e->getTraceAsString() . $e->getMessage()]);
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //ffm  获取员工考勤信息 列表
            ->withCallback('ffm_get_staff_attendance_list', function ($local, $params) {

                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'ffm_get_staff_attendance_list', 'params' => $params, 'locale' => $locale]);
                    $FfmStaffInfoService = new  FfmStaffInfoService();
                    $result = $FfmStaffInfoService->getAttendanceList($params);
                    $this->logger->info(['function' => 'ffm_get_staff_attendance_list', 'params' => $params, 'result' => $result]);
                    return $result;
                }catch (\Exception $e) {
                    $this->logger->warning(['function' => 'ffm_get_staff_attendance_list', 'message' => $e->getTraceAsString() . $e->getMessage()]);
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            ->withCallback('syncApprovalStatus', function ($local, $data) {
                $this->getDI()->get('logger')->write_log('syncApprovalStatus' . json_encode($data,
                        JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $staffId        = $data['staff_id'];
                    $approvalStatus = $data['approval_status'];
                    if (!$staffId || !$approvalStatus) {
                        throw new \Exception('staff_id 或 approval_status 为空');
                    }
                    $leaveDate = $data['leave_date'];
                    $applyResignTime = $data['apply_resign_time']??null;
                    $src       = isset($data['src']) ?: LeaveManagerService::LEAVE_SRC_STAFF_REGIN;
                    /**
                     * @see LeaveManagerService::sycnApprovalStatus()
                     */
                    (new LeaveManagerService)->sycnApprovalStatusUseLock($staffId, $approvalStatus, $leaveDate, $src,$applyResignTime);
                    return ['code' => 1, 'msg'  => 'ok',];
                } catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log('syncApprovalStatus' . sprintf("E_File: %s, E_Line: %s, E_Method: %s, E_Msg: %s",
                            $e->getFile(),
                            $e->getLine(),
                            __METHOD__,
                            $e->getMessage()), 'error');
                    return ['code' => -1, 'msg' => $e->getMessage()];
                }
            })
            //员工变更职位部门，发送邮件。
            ->withCallback('staff_change_send_email', function ($local, $data) {
                $this->getDI()->get('logger')->write_log('staff_change_send_email' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    (new HrStaffService())->staffChangeSendEmail($data);
                    return array(
                        'code' => 1,
                        'msg' => 'ok',
                    );
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log('staff_change_send_email' . sprintf("E_File: %s, E_Line: %s, E_Method: %s, E_Msg: %s",
                            $e->getFile(),
                            $e->getLine(),
                            __METHOD__,
                            $e->getMessage()), 'error');
                    return array('code' => -1, 'msg' => $e->getMessage());
                }


            })
            //获取员工管辖范围权限
            ->withCallback('get_staff_jurisdiction', function ( $local, $data ) {
                $this->getDI()->get('logger')->write_log('get_staff_jurisdiction ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $result = ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => []];
                    $locale = $local['locale'];
                    $staff_id = $data['staff_id'] ?? '';
                    $type = $data['type'] ?? '1';
                    $result['data'] = (new  StaffService())->setExpire(600)->getStaffJurisdictionFromCache($staff_id,$type);
                    $this->logger->info(['function' => 'get_staff_jurisdiction', 'params' => $data, 'result' => $result['data']]);
                    return $result;
                }catch (\Exception $e) {
                    $this->logger->warning(['function' => 'ffm_get_staff_attendance_list', 'message' => $e->getTraceAsString() . $e->getMessage()]);
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }

            })
            //清理员工未来的休息日和补的PH
            ->withCallback('del_staff_off_ph_day', function ($local, $params) {

                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'del_staff_off_day', 'params' => $params, 'locale' => $locale]);
                    $staffService = new  StaffService();
                    $staffService->pushRestStaffOffDayPublicHoliday($params);
                    $this->logger->info(['function' => 'del_staff_off_day', 'params' => $params, 'result' => 'success']);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => null);
                }catch (\Exception $e) {
                    $this->logger->warning(['function' => 'del_staff_off_day', 'message' => $e->getTraceAsString() . $e->getMessage(),'params'=>$params]);
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                }
            })
            //更新排班建议
            ->withCallback('updateSchedulingSuggestNumber', function ($local, $params) {

                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'updateSchedulingSuggestNumber', 'params' => $params, 'locale' => $locale]);
                    $staffService = new  SchedulingSuggestionService();
                    $staffService->pushUpdateSchedulingSuggestNumber($params);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => null];
                }catch (\Exception $e) {
                    $this->logger->warning(['function' => 'updateSchedulingSuggestNumber', 'message' => $e->getTraceAsString() . $e->getMessage(),'params'=>$params]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //获取打卡白名单数据
            ->withCallback('getAttendanceWhiteListData', function ($local, $param) {
                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'getAttendanceWhiteListData', 'params' => $param]);
                    $result = ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => []];
                    $result['data'] = (new  AttendanceWhiteListService())->getAttendanceWhiteListData($param);
                    $this->logger->info(['function' => 'getAttendanceWhiteListData', 'params' => $param, 'result' => $result['data']]);
                    return $result;

                }catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //Payroll_fleet_driver_crowdsourcing推送接口
            ->withCallback('sync_driver_crowdsourcing_list', function ($local, $params) {
                try {
                    BaseService::setLanguage($local['locale'] ?? 'en');
                    return (new  DriverCrowdsourcingService())->syncDriverCrowdsourcingList($params);
                } catch (ValidationException $e) {
                    $this->logger->info([
                        'function' => 'sync_driver_crowdsourcing_list',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => null];
                } catch (Exception $e) {
                    $this->logger->warning([
                        'function' => 'sync_driver_crowdsourcing_list',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //Payroll_fleet_driver_crowdsourcing查询接口
            ->withCallback('get_driver_crowdsourcing_info', function ($local, $params) {
                try {
                    BaseService::setLanguage($local['locale'] ?? 'en');
                    $data = (new  DriverCrowdsourcingService())->getDriverCrowdsourcingInfo($params);

                    return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $data];
                } catch (ValidationException $e) {
                    $this->logger->info([
                        'function' => 'get_driver_crowdsourcing_info',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => null];
                } catch (Exception $e) {
                    $this->logger->warning([
                        'function' => 'get_driver_crowdsourcing_info',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            // 获取试用期评估时间
            ->withCallback('assembleEvaluationTime', function ($local, $params) {
                try {
                    $locale = $local['locale'];
                    $this->logger->info(['function' => 'assembleEvaluationTime', 'params' => $params, 'locale' => $locale]);
                    $row['job_title_grade_v2']          = $params['job_title_grade_v2'] ?? '';
                    $row['hire_date']                   = $params['hire_date'] ?? '';
                    $item['first_deadline_date_start']  = "";
                    $item['first_deadline_date_end']    = "";
                    $item['second_deadline_date_start'] = "";
                    $item['second_deadline_date_end']   = "";
                    $data                               = (new  ProbationService())->assembleEvaluationTime($row,
                        $item);
                    $this->logger->info([
                        'function' => 'assembleEvaluationTime',
                        'params'   => $params,
                        'result'   => 'success',
                    ]);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $data];
                } catch (\Exception $e) {
                    $this->logger->warning([
                        'function' => 'assembleEvaluationTime',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //同步员工资产处理状态 oa离职资产同步至离职管理列表
            ->withCallback('sync_leave_manager_assets_remand_state', function ($local, $param) {
                try {
                    BaseService::setLanguage($local['locale'] ?? 'en');
                    $result = ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => null];
                    $result['data'] = (new LeaveManagerService())->editNewAssetsState($param);
                    $this->logger->info([
                        'function' => 'sync_leave_manager_assets_remand_state',
                        'params'   => $param,
                        'result'   => $result['data'],
                    ]);
                    return $result;
                } catch (ValidationException $e) {
                    $this->logger->notice([
                        'function' => 'sync_leave_manager_assets_remand_state',
                        'message'  => $e->getMessage(),
                        'file'     => $e->getFile(),
                        'line'     => $e->getLine(),
                        'trace'    => $e->getTraceAsString(),
                        'params'   => $param,
                    ]);
                    return ['code' => ErrCode::VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => null];
                } catch (Exception $e) {
                    $this->logger->error([
                        'function' => 'sync_leave_manager_assets_remand_state',
                        'message'  => $e->getMessage(),
                        'file'     => $e->getFile(),
                        'line'     => $e->getLine(),
                        'param'    => $param,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //发送by消息 迁移自FBI
            ->withCallback('add_kit_message', function ($local, $param) {
                $this->logger->info('svc add_kit_message 参数列表: locale - ' . json_encode($local, JSON_UNESCAPED_UNICODE) . '; param - ' . json_encode($param, JSON_UNESCAPED_UNICODE));
                try {
                    $data =  (new MessagesService())->add_kit_message($param);
                    $this->logger->info('add_kit_message 数据返回: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
                    $return['code'] = $data[1];
                    $return['msg'] = $data[0];
                    $return['message'] = $data[0];
                    $return['data'] = $data[2];
                    return $return;
                } catch (\Exception $e) {
                    $this->logger->error('add_kit_message, exception: ' . $e->getMessage());
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage();
                    $return['message'] = $e->getMessage();
                    $return['data'] = [];
                    return $return;
                }
            })
            //用户协议推送
            ->withCallback('push_staff_argeement_sign', function ($local, $params) {
                try {
                    BaseService::setLanguage($local['locale'] ?? 'en');
                    if ((new  AgreementSignRecordService())->pushStaffSign($params)){
                        return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => null];
                    }

                    throw new Exception('pushStaffSign Error!');
                } catch (Exception $e) {
                    $this->logger->warning([
                        'function' => 'push_staff_argeement_sign',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //MY员工 停职原因为：连续旷工,发送邮件 BOC 文件
            ->withCallback('suspension_send_email', function ($local, $data) {
                $this->getDI()->get('logger')->write_log('suspension_send_email' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    (new SuspensionService())->sendEmailPushRedis($data);
                    return array(
                        'code' => 1,
                        'msg' => 'ok',
                    );
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log('staff_change_send_email' . sprintf("E_File: %s, E_Line: %s, E_Method: %s, E_Msg: %s",
                            $e->getFile(),
                            $e->getLine(),
                            __METHOD__,
                            $e->getMessage()), 'error');
                    return array('code' => -1, 'msg' => $e->getMessage());
                }
            })
            ->withCallback('getSuspensionReason', function ($local, $param) {
                try {
                    $data = [];
                    $t = BaseService::getTranslation($local['locale']);
                    foreach (Enums\StaffEnums::SUSPEND_TYPE_REASON_MAP as $k => $v) {
                        $data[] = [
                            'value' => $k,
                            'label' => $t->_($v),
                        ];
                    }
                    return ['code' => 1, 'data' => $data, 'msg' => 'ok'];
                } catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return ['code' => 0, 'data' => [], 'msg' => $e->getMessage()];
                }
            })
            //员工变成在职后 同步hold by申请离职来源hold变成已处理
            ->withCallback('sync_by_source_hold_handle', function ($local, $params) {
                try {
                    $staff_info_id = $params['staff_info_id'];
                    $hold_manage_service = new HoldManageService();
                    $result = $hold_manage_service->syncIncompleteResignationHoldProgress([
                        'staff_info_id'   => $staff_info_id,
                        'handle_progress' => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                    ]);
                    return ['code' => 1,'msg' => 'success','data' => $result];
                } catch (Exception $e) {
                    $this->logger->warning([
                        'function'        => 'sync_by_source_hold_handle',
                        'trace_as_string' => $e->getTraceAsString(),
                        'message'         => $e->getMessage(),
                        'file'            => $e->getFile(),
                        'line'            => $e->getLine(),
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            //根据工号查找当前员工未回公款
            ->withCallback('staff_outstanding_money', function ($local, $params) {
                try {
                    $staff_info_id = $params['staff_info_id'];
                    $action_type   = $params['action_type'];
                    $leave_manager_service = new LeaveManagerService();
                    if (isCountry('PH')) {
                        if ($action_type == 'AR') {
                            $outstanding_money = (new LeaveManagerService())->moneyInfoAR($staff_info_id);
                        } elseif ($action_type == 'AP') {
                            $outstanding_money = (new LeaveManagerService())->moneyInfoAP($staff_info_id);
                        }
                    } else {
                        $outstanding_money = $leave_manager_service->moneyInfo($staff_info_id);
                    }
                    $this->logger->info([
                        'function' => 'staff_outstanding_money_svc',
                        'params' => $params,
                        'result' => $outstanding_money
                    ]);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $outstanding_money];
                }catch (\Exception $e) {
                    $this->logger->warning([
                        'function' => 'staff_outstanding_money_svc',
                        'message'  => $e->getMessage(),
                        'file'     => $e->getFile(),
                        'line'     => $e->getLine(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //创建众包外协工号
            ->withCallback('crowd_sourcing_outsourcing_staff_create', function ($local, $params) {
                $return = [
                    'code'    => ErrCode::SUCCESS,
                    'message' => 'success',
                    'data'    => [
                        'leave_days'    => '',
                        'staff_info_id' => '',
                    ],
                ];
                $this->logger->info("crowd_sourcing_outsourcing_staff_create params :".json_encode($params));
                BaseService::setLanguage($local['locale'] ?? 'en');
                $outsourcingStaffObj = (reBuildCountryInstance(new OutsourcingStaffService()));
                try {
                    $params['hire_date'] = $params['hire_date'] ?? gmdate('Y-m-d');
                    $params['hire_type'] = $params['hire_type'] ?? 12; //外协雇佣类型 默认众包外协
                    $params['formal'] = HrStaffInfoModel::FORMAL_0;
                    // 业务处理
                    $result                          = $outsourcingStaffObj->setLockConf(30,false)->createStaffUseLock(trim_array($params));
                    $return['data']['staff_info_id'] = $result['staff_info_id'];
                    $this->logger->info(['crowd_sourcing_outsourcing_staff_create'=>$params,'result'=>$return]);
                    return $return;
                } catch (ValidationException $e) {
                    $this->logger->info([
                        'function' => 'crowd_sourcing_outsourcing_staff_create',
                        'message'  => $e->getMessage(),
                        'file'     => $e->getFile(),
                        'line'     => $e->getLine(),
                        'trace'    => $e->getTraceAsString(),
                        'params'   => $params,
                    ]);
                    $return['code']               = $e->getCode();
                    $return['message']            = $e->getMessage();
                    $return['data']['leave_days'] = $outsourcingStaffObj->leave_day;
                    return $return;
                } catch (Exception $e) {
                    $this->logger->error([
                        'function' => 'crowd_sourcing_outsourcing_staff_create',
                        'message'  => $e->getMessage(),
                        'file'     => $e->getFile(),
                        'line'     => $e->getLine(),
                        'param'    => $params,
                    ]);
                    $return['code']    = ErrCode::SYSTEM_ERROR;
                    $return['message'] = 'server error';
                    return $return;
                }
            })
            //根据字典code获取字典列表
            ->withCallback('getDictionaryByDictCode', function ($local, $params) {
                try {
                    BaseService::setLanguage($local['locale'] ?? 'en');
                    $result = ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => []];
                    //data_type 用于兼容数据
                    $result['data'] = (new DictionaryService())->getDictionItemsListByCode($params['dict_code'], $params['data_type'] ?? null);
                    return $result;
                } catch (Exception $e) {
                    $this->logger->warning([
                        'function' => 'getDictionaryByDictCode',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //根据字典code获取全部字典列表(用于解析国家)
            ->withCallback('getTotalDictionaryItemsByDictCode', function ($local, $params) {
                try {
                    BaseService::setLanguage($local['locale'] ?? 'en');
                    $result = ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => []];
                    $result['data'] = (new DictionaryService())->getTotalDictionaryLabelByDictCode($params['dict_code']);
                    return $result;
                } catch (Exception $e) {
                    $this->logger->warning([
                        'function' => 'getTotalDictionaryItemsByDictCode',
                        'message'  => $e->getTraceAsString().$e->getMessage(),
                        'params'   => $params,
                    ]);
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null];
                }
            })
            //获取OA纠纷员工信息
            ->withCallback('get_staff_info_for_complaint', function ($local, $param) {
                try {
                    $service   = new StaffInfoService();
                    $service->setLanguage($local['locale'] ?? getCountryDefaultLang());
                    $staffInfo = $service->getStaffInfoForComplaint($param['staff_info_id']);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $staffInfo];
                } catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                }
                return array('code' => -1, 'message' => $e->getMessage());
            })
            //HUB 新增员工 立即 同步HIK, 写入redis。
            ->withCallback('hub_staff_sync_to_hik', function ($local, $data) {
                BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
                $this->getDI()->get('logger')->write_log('hub_staff_sync_to_hik : ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    (new HubOutSourcingStaffService())->syncToHikData($data);
                    return array(
                        'code' => 1,
                        'msg' => 'ok',
                    );
                }catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log('hub_staff_sync_to_hik' . sprintf("E_File: %s, E_Line: %s, E_Method: %s, E_Msg: %s",
                            $e->getFile(),
                            $e->getLine(),
                            __METHOD__,
                            $e->getMessage()), 'error');
                    return array('code' => -1, 'msg' => $e->getMessage());
                }
            })

            //获取转岗后基础薪资
            ->withCallback('get_job_transfer_base_salary_after', function ($local, $data) {
                $this->logger->info('get_job_transfer_base_salary_after : ' . json_encode($data,
                        JSON_UNESCAPED_UNICODE));
                try {
                    BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
                    if (empty($data['job_transfer_id'])) {
                        return ['code' => -1, 'message' => 'param error'];
                    }
                    $jobTransferId = $data['job_transfer_id'];
                    /**
                     * @var $service JobTransferSalaryService
                     */
                    $service = reBuildCountryInstance(new JobTransferSalaryService());
                    if (isCountry()) {
                        //win-hr 劳动合同 是否使用实际转岗日期
                        $service->useActualTransferDate = $data['use_actual_transfer_date'] ?? null;
                    }
                    if (isCountry('PH')) {
                        //是否为特殊批量转岗
                        $service->isSpecialBatchTransfer = $data['is_special_batch_transfer'] ?? null;
                    }
                    $returnData = $service->getTransferSalaryForBy($jobTransferId);
                    return ['code' => 1, 'message' => 'success', 'data' => $returnData];
                } catch (\Exception $e) {
                    $this->logger->error([$e->getFile(), $e->getLine(), $e->getMessage()]);
                    return ['code' => -1, 'message' => $e->getMessage()];
                }
            })
            //获取转岗薪资范围
            ->withCallback('get_job_transfer_salary_range', function ($local, $data) {
                $this->logger->info('get_job_transfer_salary_range : ' . json_encode($data, JSON_UNESCAPED_UNICODE));
                try {
                    BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
                    if (empty($data['job_transfer_id'])) {
                        return ['code' => -1, 'message' => 'param error'];
                    }
                    $jobTransferId = $data['job_transfer_id'];

                    $service    = new \app\modules\Ph\services\JobTransferSalaryService();
                    $returnData = $service->getTransferApproveSalaryRange($jobTransferId);
                    return ['code' => 1, 'message' => 'success', 'data' => $returnData];
                } catch (\Exception $e) {
                    $this->logger->error([$e->getFile(), $e->getLine(), $e->getMessage()]);
                    return ['code' => -1, 'message' => $e->getMessage()];
                }
            })
            //未回款员工同步最后回款时间
            ->withCallback('ms_sync_staff_refund_time', function ($local, $params) {
                BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
                $this->getDI()->get('logger')->write_log('ms_sync_staff_refund_time : ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $result = (new StaffNotRefundedSuspensionService())->updateLastRefundDate($params);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $result];
                }catch (\Exception $e) {
                    $this->logger->error([
                        'function' => 'ms_sync_staff_refund_time',
                        'message'  => $e->getMessage(),
                        'file'     => $e->getFile(),
                        'line'     => $e->getLine(),
                        'param'    => $params,
                    ]);
                    $return['code']    = ErrCode::SYSTEM_ERROR;
                    $return['message'] = 'server error';
                    return $return;
                }
            })//oss 给ios 安卓人脸用的
            ->withCallback('buildPutObjectParams', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['buildPutObjectParams',[$local,$param]], 'info');

                if (empty($param['biz_type'])) {
                    throw new Exception('need biz_type params');
                }
                if (empty($param['filename'])) {
                    throw new Exception('need filename params');
                }
                try {
                    $s = new OssService();
                    return $s->buildPutObjectParams($param['biz_type'],$param['filename']);
                }catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    throw $e;
                }
            })
            //oss 文件上传
            ->withCallback('buildPutObjectUrl', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['buildPutObjectUrl', [$local, $param]], 'info');
                if (empty($param['biz_type'])) {
                    throw new Exception('need biz_type params');
                }
                if (empty($param['filename'])) {
                    throw new Exception('need filename params');
                }

                try {
                    $s = new OssService();
                    return $s->buildPutObjectUrl($param['biz_type'], $param['filename']);
                } catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString().$e->getMessage());
                    throw $e;
                }
            })
            //生成员工ID
            ->withCallback('generateId', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['generateId',[$local,$param]], 'info');
                if (!isset($param['type'])) {
                    throw new Exception('need type params');
                }
                if (!isset($param['formal'])) {
                    throw new Exception('need formal params');
                }
                try {
                    $service = new StaffService();
                    return $service->generateId($param['type'],$param['formal']);
                } catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    throw $e;
                }
            })
            ->withClassAndMethod('salary_net_pay_to_finance', FinanceRpcService::class, 'getSalaryNetPay')//金融系统获取 薪酬 netPay
            ->withClassAndMethod('salary_advance_deduction_to_finance', FinanceRpcService::class, 'getSalaryAdvanceDeduction')//金融系统获取 获取 薪酬 advance deduction
            ->withClassAndMethod('get_staff_info_to_finance', FinanceRpcService::class, 'staffIdentityInfo')//金融系统获取员工身份相关信息
            // 社保离职日期
            ->withCallback('social_security_leave_date', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['social_security_leave_date',[$local,$param]], 'info');
                if (!isset($param['staff_info_id']) || !isset($param['leave_date'])) {
                    throw new Exception('params error');
                }
                try {
                    $service = new StaffService();
                    return $service->staffSocialSecurityLeaveDate($param['staff_info_id'],$param['leave_date'],$param['operator_id'] ??'-1',$param['only_edit_leave_date']??false);
                } catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    throw $e;
                }
            })
            // 撤销离职逻辑 hris 调用
            ->withCallback('leave_manager_cancel', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['leave_manager_cancel',[$local,$param]], 'info');
                if (!isset($param['staff_info_id']) || !isset($param['reason'])) {
                    throw new Exception('params error');
                }
                try {
                    $service = new LeaveManagerService();
                    $param['from'] = 'hris_rpc';
                    $fb_staff_info = (new StaffInfoService())->getStaffInfoByIdv4($param['fbid'], 'staff_info_id,name');
                    return $service->cancel($param,['id'=>$fb_staff_info['staff_info_id'] ?? '','name'=>$fb_staff_info['name'] ?? '']);
                } catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    throw $e;
                }
            })
            //重置密码
            ->withCallback('reset_password', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['reset_password',[$local,$param]], 'info');
                if (!isset($param['staff_info_id'])) {
                    throw new Exception('need staff_info_id params');
                }
                try {
                    $service = new StaffService();
                    return $service->resetPassword($param['staff_info_id']);
                } catch (Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    throw $e;
                }
            })
            //获取 在职证明，薪资证明 公司信息。
            ->withCallback('get_staff_certificate_info', function ($local, $data) {
                try {
                    $this->logger->info("get_staff_certificate_info data :" . json_encode($data));
                    if (!isCountry(['th','ph'])) {
                        throw new ValidationException("Not currently supported");
                    }
                    $staff_id = $data['staff_info_id'];
                    $countryCode = ucfirst(env('country_code'));
                    /**
                     * @see \App\Modules\Ph\Services\CertificateService
                     */
                    $certificateService = 'App\Modules\\' . $countryCode . '\Services\CertificateService';
                    if (class_exists($certificateService)) {
                        $bll = new $certificateService;
                    } else {
                        $bll = new CertificateService();
                    }
                    $bll::setLanguage($local['locale'] ?? 'en');
                    $data = $bll->getStaffCompanyInfo($staff_id, false);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //获取 hire type
            ->withCallback('getHireTypeList', function ($local, $data) {
                try {
                    BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
                    $list = (new SysService())->getHireTypeList($data['dataType'] ?? Enums::DATA_TYPE_STRING);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list);
                }catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //获取 员工 税务相关信息
            ->withCallback('get_staff_tax_info', function ($local, $data) {
                try {
                    $this->logger->info("get_staff_tax_info data :" . json_encode($data));
                    if (!isCountry(['th'])) {
                        throw new ValidationException("Not currently supported");
                    }
                    $staff_ids = $data['staff_info_ids'];
                    $bll = new StaffInfoService();
                    $bll::setLanguage($local['locale'] ?? 'en');
                    $data = $bll->getStaffTaxInfo($staff_ids);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => ['list' => $data]);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })//获取公共假期
            //获取 my 员工相关信息--TO invoice
            ->withCallback('get_staff_invoice_info', function ($local, $data) {
                try {
                    $this->logger->info("get_staff_invoice_info data :" . json_encode($data));
                    if (!isCountry(['my'])) {
                        throw new ValidationException("Not currently supported");
                    }
                    $staff_ids = $data['staff_info_ids'];
                    $bll = (reBuildCountryInstance(new StaffInfoService()));
                    $bll::setLanguage($local['locale'] ?? 'en');
                    $data = $bll->getStaffInvoiceInfo($staff_ids);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => ['list' => $data]);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })//获取公共假期
            ->withCallback('get_public_holiday', function ($local, $data) {
                try {
                    $service = (new HolidayService());
                    $list  = $service->getHolidayForSvc($data);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list);
                }catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //菲律宾 个人代理续约合同 恢复在职 + 处理 hold
            ->withCallback('renew_contract_business_state_hold', function ($local, $data) {
                try {
                    $this->logger->info("renew_contract_business_state_hold data :" . json_encode($data));

                    $service = (new RenewContractBusinessService());
                    $list  = $service->staffOnJob($data);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list);
                }catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })
            //获取外协分区申请人数
            ->withCallback('hub_store_get_os_partition', function ($local, $params) {
                try {
                    $this->logger->info("hub_store_get_os_partition params :" . json_encode($params));
                    $service = (new HubStorePartitionService());
                    if (empty($params['shift_date']) || empty($params['store_id'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'param error', 'data' => null];
                    }
                    if (isCountry('MY') && isset($params['shift']) && !in_array($params['shift'],array_keys($service->shiftMap))) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'shift param error', 'data' => null];
                    }
                    if (isset($params['partition']) &&  !in_array($params['partition'],array_keys(\App\Modules\My\library\Enums\enums::$partitionMap))) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'partition param error', 'data' => null];
                    }
                    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['shift_date'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'shift_date param error', 'data' => null];
                    }
                    $list  = $service->getOsPartition($params);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list];
                }catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            //获取正式员工考勤数据 为了  HUB网点分区KPI指标库& HUB网点KPI指标库 使用
            ->withCallback('hub_store_kpi_get_formal_data', function ($local, $params) {
                try {
                    $this->logger->info("hub_store_kpi_get_formal_data params :" . json_encode($params));
                    $service = (new HubStorePartitionService());
                    if (empty($params['stat_date']) || empty($params['store_id'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'param error', 'data' => null];
                    }
                    if (isset($params['shift']) && !in_array($params['shift'],array_keys($service->shiftMap))) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'shift param error', 'data' => null];
                    }
                    if (isset($params['partition']) &&  !in_array($params['partition'],array_keys(\App\Modules\My\library\Enums\enums::$partitionMap))) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'partition param error', 'data' => null];
                    }
                    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['stat_date'])) {
                        return ['code'    => ErrCode::VALIDATE_ERROR,
                                'message' => 'stat_date param error',
                                'data'    => null,
                        ];
                    }
                    $list = $service->getFormalAttendanceData($params);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list];
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() . '|' . $e->getMessage());
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            // HUB网点分区KPI指标库& HUB网点KPI指标库 获取外协员工考勤
            ->withCallback('hub_store_kpi_get_outsourcing_data', function ($local, $params) {
                try {
                    $this->logger->info("hub_store_kpi_get_outsourcing_data params :" . json_encode($params));
                    $service = (new HubStorePartitionService());
                    if (empty($params['stat_date']) || empty($params['store_id'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'param error', 'data' => null];
                    }
                    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['stat_date'])) {
                        return [
                            'code'    => ErrCode::VALIDATE_ERROR,
                            'message' => 'stat_date param error',
                            'data'    => null,
                        ];
                    }
                    $list = $service->getOutsourcingAttendanceData($params);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list];
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() . '|' . $e->getMessage());
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            ->withCallback('sync_message_audit_status', function ($local, $param) {
                $this->getDI()->get('logger')->write_log(['sync_message_audit_status'=>$param], 'info');
                $data =  (new MessagesService())->syncAuditStatus($param);
                $this->getDI()->get('logger')->write_log(['sync_message_audit_status_result'=>$data], 'info');
                return $data;
            })
            //获取指定日期员工工作分区
            ->withCallback('hub_store_get_formal_partition', function ($local, $data) {
                try {
                    $this->logger->info("hub_store_get_formal_partition data :" . json_encode($data));
                    BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
                    if (empty($data['shift_date']) || empty($data['staff_ids']) || !is_array($data['staff_ids'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'param error', 'data' => null];
                    }
                    $service = (new HubStorePartitionService());
                    $list  = $service->getFormalPartition($data['staff_ids'],$data['shift_date']);
                    return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $list];
                }catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return ['code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            /**
             * 获取 PDF 公司配置信息。
             * $data['staff_info_id'] 必传
             * $data['department_id'] 非必传，指定部门 所属公司
             */
            ->withCallback('get_company_config_info', function ($local, $data) {
                try {
                    $this->logger->info("get_company_config_info data :" . json_encode($data));
                    if(empty($data['staff_info_id'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'param error', 'data' => null];
                    }

                    $bll = (reBuildCountryInstance(new CertificateService()));

                    $bll::setLanguage($local['locale'] ?? 'en');
                    $data = $bll->getCompanyConfigInfo($data);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            //获取 PDF 公司配置信息。
            ->withCallback('get_company_config_info_to_winhr', function ($local, $data) {
                try {
                    $this->logger->info("get_company_config_info_to_winhr data :" . json_encode($data));
                    if(empty($data['department_id'])) {
                        return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'param error', 'data' => null];
                    }

                    $bll = (reBuildCountryInstance(new CertificateService()));

                    $bll::setLanguage($local['locale'] ?? 'en');
                    $data = $bll->getCompanyConfigInfoWinHr($data);
                    return array('code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => $data);
                } catch (ValidationException $e) {
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => null);
                } catch (\Exception $e) {
                    $this->logger->error($e->getTraceAsString() .'|'. $e->getMessage());
                    return array('code' => ErrCode::SYSTEM_ERROR, 'message' => 'error', 'data' => null);
                }
            })

            ->withClassAndMethod('get_warning_message_company_info', WarningService::class, 'getCompanyInfoForSvc')//公司信息
            ->withClassAndMethod('get_company_config_info_by_company_id', CertificateService::class, 'getCompanyConfigInfoByCompanyId')//根据

            //demo 放下面 新增的时候 好复制
            ->withCallback('test', function ($local, $param) {
                try {
                    return [$local,$param];
                }catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                }
                return [$local,$param];
            })
            ->withClassAndMethod('get_staff_off_leave', WorkdayService::class, 'getStaffOffLeave')//获取员工请假和休息日
            ->withClassAndMethod('workday_daily_detail', WorkdaySettingService::class, 'workdayDailyDetailRpc')//获取员工请假和休息日
            ->withClassAndMethod('get_special_batch_transfer_salary', \app\modules\My\services\JobTransferSalaryService::class, 'getSpecialBatchTransferSalary')//MY 获取批量特殊转岗数据
            ->withClassAndMethod('get_staff_manager_store', StaffService::class, 'getManagerStore')//获取员工管辖网点
            ->withClassAndMethod('get_staff_shift_info', StaffShiftService::class, 'getShiftInfoForSvc')//获取员工班次
            ->withClassAndMethod('get_partner_staff_list', PartnerStaffService::class, 'getStaffList')//合作商管理-合作商工号列表
            ->withClassAndMethod('save_partner_staff_info', PartnerStaffService::class, 'createOrUpdateStaff')//合作商管理-保存合作商工号信息
            ->withClassAndMethod('update_staff_password', StaffService::class, 'updateStaffPassword')//更新员工密码
            ->withClassAndMethod('get_store_courier_attendance_num', AttendanceStatisticsService::class, 'getStoreCourierAttendanceNumForBI')//MY 网点快递员出勤人数
            ->withClassAndMethod('ticket_login_url', UserService::class, 'ticketLoginUrl')//登录临时票证
            ->withClassAndMethod('outsource_company', SysService::class, 'outsource_company_list')//获取外协公司枚举
            ->withClassAndMethod('oss_sign_url', OssService::class, 'signUrlForSvc')//oss文件签名后的地址
            ->withClassAndMethod('send_staff_message', MessagesService::class, 'sendStaffMessage')//站内信
            ->withClassAndMethod('get_leave_source_reason', LeaveManagerService::class, 'getLeaveSourceAndReason')//获取离职来源、离职原因
            ->withClassAndMethod('get_leave_reason_map', LeaveManagerService::class, 'getLeaveReasonMapSvc')//离职原因map
            ->withClassAndMethod('get_leave_scenario_map', LeaveManagerService::class, 'getLeaveScenarioMapSvc')//离职场景map
            ->withClassAndMethod('get_staff_money_asset_info', LeaveManagerService::class, 'getStaffMoneyAssetInfo')//获取员工名下钱款资产信息
            ->withClassAndMethod('get_send_salary_company', StaffSalaryService::class, 'getSendSalaryCompany')//MY 获取发薪公司
            ->withClassAndMethod('hub_os_attendance_export', SvcService::class, 'hubOsAttendanceExport')//hub外协考勤导出
            ->withClassAndMethod('make_message_sign_pdf', MessagesService::class, 'makeMessageSignPdfPushRedis')//hcm -- 消息-签字消息，用户签字完成，立即合成pdf文件，redis
            ->withClassAndMethod('get_conditions_rule', BaseService::class, 'getConditionRule')//根据工号 获取新版规则配置
            ->withClassAndMethod('send_confirmation_evaluation_msg', HrProbationContractService::class, 'sendConfirmationEvaluationMsg')// PH发送转正评估消息
            ->withClassAndMethod('get_hold_enums_info', SysService::class, 'get_hold_enums_info')//获取 hold类型，hold原因 枚举
            ->withClassAndMethod('get_daily_frontline_salary', DailyFrontlineSalaryConfigService::class, 'getDailyFrontlineSalaryConfig')//获取一线员工薪资配置-日薪规则配置
            ->withClassAndMethod('get_frontline_salary', reBuildCountryInstance(new FrontlineSalaryConfigService()), 'getFrontlineSalaryConfig')//获取一线员工薪资配置-月薪规则配置
            ->withClassAndMethod('rehire_lnt_salary', FrontlineSalaryInfoService::class, 'addReHireSalary')//返聘lnt员工薪资设置
            ->withClassAndMethod('get_van_vehicle_subsidy', StoreClassService::class, 'getVanVehicleSubsidy')// 获取 Van车补配置
            ->withClassAndMethod('get_store_coordinate', AttendanceConfigService::class, 'get_store_coordinate')//给众包提供临时坐标和主坐标
            ->withClassAndMethod('get_probation_target_business_pdf_path', ProbationTargetService::class, 'get_probation_target_business_pdf_path')//生成目标pdf路径
            ->withClassAndMethod('payroll_file_2316', PayrollFileService::class, 'payrollFileCreate')//2316生成pdf
            ->withClassAndMethod('sync_payroll_file_2316', PayrollFileService::class, 'syncPayrollFileCreate')//2316生成pdf
            ->withClassAndMethod('add_os_blacklist', BlackListService::class, 'addOsBlacklistSVC')//svc新增外协黑名单
            ->withClassAndMethod('get_ic_settlement_staff_info', StaffInfoService::class, 'getICSettlementStaffInfo')//PH个人代理结算 员工基本信息
            ->withClassAndMethod('get_hold_reason_list', HoldManageService::class, 'getHoldReasonList')//获取 hold 原因
            ->withClassAndMethod('get_business_trip_info', BusinessTripService::class, 'getBusinessTripInfoBySerialNoSVC')
            ->withClassAndMethod('probation_stage_done_message', ProbationService::class, 'probationStageDoneMessage')//非一线转正评估阶段完成消息
            ->withClassAndMethod('get_non_front_line_pdf_sign_data', ProbationService::class, 'getNonFrontLinePdfSignData')//获取非一线转正评估合成pdf数据
            ->withClassAndMethod('get_tool_staff_info', StaffInfoService::class, 'getToolStaffInfo')//MY 获取工具号信息
            ->withClassAndMethod('legal_prosecution_materials', StaffInfoService::class, 'legalProsecutionMaterials')//PH legal起诉材料
            ->withClassAndMethod('get_staff_contract_sign_image', StaffInfoService::class, 'getStaffContractSignImage')//获取员工合同签字图片

            //OSS私有化接口
            ->withClassAndMethod('commonBuildPutObjectUrl', OssService::class, 'commonBuildPutObjectUrl')//Common文件上传
            ->withClassAndMethod('commonSignObjectUrl', OssService::class, 'commonSignObjectUrl')//Common文件获取

            //试用期目标V2
            ->withClassAndMethod('probation_target_list', ProbationTargetRpcService::class, 'probationTargetList') //目标-列表
            ->withClassAndMethod('probation_target_detail', ProbationTargetRpcService::class, 'probationTargetDetail') //目标-详情
            ->withClassAndMethod('probation_target_save', ProbationTargetRpcService::class, 'probationTargetSave') //目标-保存
            ->withClassAndMethod('probation_target_save_send', ProbationTargetRpcService::class, 'probationTargetSaveSend') //目标-保存并发送
            ->withClassAndMethod('probation_target_send', ProbationTargetRpcService::class, 'probationTargetSend') //发送目标
            ->withClassAndMethod('probation_target_cancel', ProbationTargetRpcService::class, 'probationTargetCancel') //目标撤销
            ->withClassAndMethod('send_vehicle_message_to_sub', VehicleInspectionService::class, 'sendVehicleMessageToSubRpc') //快递员车辆稽查 给子账号发消息
            ->withClassAndMethod('license_plate_location', SysService::class, 'licensePlateLocation') //获取上牌地点
            ->withClassAndMethod('clear_login_num', ToolStaffInfoService::class, 'clearLoginNum') //解冻
        ;
        echo $server->execute();
    }
}