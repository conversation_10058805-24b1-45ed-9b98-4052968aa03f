<?php

namespace App\Controllers;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\AttendanceLocationSettingService;

class AttendancelocationsettingController extends BaseController
{

    /**
     * 获取员工列表(考勤打卡)
     * @Token
     * @throws ValidationException
     */
    public function getAttendanceStaffListAction(){
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'trim');
        $param['department_ids'] = $this->request->get('department_ids');
        $param['job_title_id'] = $this->request->get('job_title_id');
        $param['state'] = $this->request->get('state');
        $param['page_num'] = $this->request->get('page_num', 'int');
        $param['page_size'] = $this->request->get('page_size', 'int');
        $validation = ['department_ids' => 'Arr','state' => 'Arr','job_title_id' => 'Arr'];
        Validation::validate(['department_ids' => $param['department_ids'],
                              'job_title_id' => $param['job_title_id'], 'state' => $param['state']], $validation);
        $param['operator'] = $this->user['id'];
        $obj = new AttendanceLocationSettingService();
        $return = $obj->getAttendanceStaffList($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $return);
    }

    /**
     * 打卡地点查看接口
     * @Token
     * @throws ValidationException
     */
    public function getAttendanceStoresByStaffIdAction(){
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'trim');
        $param['operator'] = $this->user['id'];
        $obj = new AttendanceLocationSettingService();
        $return = $obj->getAttendanceStoresByStaffId($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $return);
    }

    /**
     * 编辑打卡地点
     * @Token
     * @throws ValidationException
     */
    public function updateStaffAttendanceStoreAction(){
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'int');
        $storeArr = $this->request->get('store_ids');
        $validation = ['store_ids' => 'Arr'];
        $this->logger->info("updateStaffAttendanceStore operator={$this->user['id']}, staff_info_id={$param['staff_info_id']}, store_ids:" . json_encode($storeArr));
        Validation::validate(['store_ids'=>$param['store_ids']], $validation);
        $storeIds = [];
        foreach($storeArr as $val) {
            if(! isset($val['can_delete']) || ! isset($val['store_id']) || ! in_array($val['can_delete'], [1, 0])){
                throw new ValidationException('param store_ids error');
            }
            if($val['can_delete'] == 1){
                $storeIds[] = $val['store_id'];
            }
        }
        $param['store_ids'] = $storeIds;
        $param['operator'] = $this->user['id'];
        $obj = new AttendanceLocationSettingService();
        $return = $obj->updateStaffAttendanceStore($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $return);
    }
}