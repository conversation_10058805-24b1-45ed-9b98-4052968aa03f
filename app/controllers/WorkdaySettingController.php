<?php

namespace App\Controllers;

//轮休配置 每周休息天数
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\WorkdaySettingDailyDetailModel;
use App\Models\backyard\WorkdaySettingDailyModel;
use App\Services\BllService;
use App\Services\MessagesService;
use App\Services\WorkdaySettingService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class WorkdaySettingController extends BaseController
{


    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @api workday-setting/list
     * @Permission(action='free_workday_view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $param = $this->request->get();
        Validation::validate($param, [
            'store_id'      => 'Arr',//网点
            'state'         => 'Arr',//在职状态
            'audit_state'   => 'Arr',//审批状态
            'staff_info_id' => 'Str',//用户
        ]);
        $server        = new WorkdaySettingService();
        $server->param = $param;
        $data          = $server->getCount()->getList();
        $count         = $server->count;
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['list' => $data, 'count' => $count]);
    }


    /**
     * 导入
     * @Permission(action='free_workday_import')
     */
    public function importAction()
    {
        $params              = $this->request->get();
        $params['file_type'] = AsyncImportTaskModel::WORKDAY_SETTING;
        Validation::validate($params, [
            'file_url' => 'Required|Str',
        ]);
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->import($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    //导入模板

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function templateAction()
    {
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->getTmp($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * @Permission(action='free_workday_import')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deletePartAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'ids' => 'Required|Arr',
        ]);
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->deleteData($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    //删除 所有

    /**
     * @Permission(action='free_workday_import')
     * @return Response|ResponseInterface
     */
    public function deleteAllAction()
    {
        $server                  = new WorkdaySettingService();
        $params['user_info']     = $this->user;
        $params['is_delete_all'] = true;
        $data                    = $server->deleteData($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    //导出

    /**
     * @Permission(action='free_workday_export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'store_id'      => 'Arr',//网点
            'state'         => 'Arr',//在职状态
            'audit_state'   => 'Arr',//审批状态
            'staff_info_id' => 'Str',//用户
        ]);
        //获取文件名
        $file_name             = 'free_workday_setting';
        $action_name           = 'workday' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'workdaySettingExport';
        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];
        [$msg, $code] = (new BllService())->exportToDownloadCenter($this->user['id'], $file_name, $action_name, $params);
        if ($code == ErrCode::SUCCESS) {
            return $this->returnJson(ErrCode::SUCCESS, $msg, []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'export failed', []);
    }

    /**
     * 删除全部 获取所有记录条目数
     * @Permission(action='free_workday_delete')
     */
    public function deleteInfoAction(){
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $num                 = $server->deleteNum();
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['count' => $num]);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function remindAction()
    {
        $params     = $this->request->get();
        $validation = [
           // 'month' => 'Required|Str|>>>:month error',
        ];
        Validation::validate($params, $validation);
        $server               = new WorkdaySettingService();
        $params['operate_id'] = $this->user['id'];
        $result               = $server->getPageRemindData($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }


    /**
     * 按日配置列表
     * @Token
     * @Permission(action='free_workday_view')
     */
    public function listDailyAction()
    {
        $params     = $this->request->get();
        $validation = [
            'page_num'  => 'IntGe:1',
            'page_size' => 'IntGe:1|IntLe:100',
            'month'     => 'Required|Str|>>>:month error',
        ];

        Validation::validate($params, $validation);

        // 设置默认分页参数
        $params['page_num']  = $params['page_num'] ?? 1;
        $params['page_size'] = $params['page_size'] ?? 20;
        $params['month']     = $params['month'] ?? date('Y-m');

        $service        = new WorkdaySettingService();
        $service->param = $params;
        $data           = $service->getDailyCount()->getDailyList();
        $count          = $service->count;

        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), [
            'list'  => $data,
            'count' => $count,
        ]);
    }

    /**
     * 异步导出按日配置到下载中心
     * @description: 导出按日配置数据到下载中心，支持按月份和其他条件筛选
     * @author: AI
     * @date: 2024-01-15 10:30:00
     * @Permission(action='free_workday_export')
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportDailyAction()
    {
        $params = $this->request->get();
        // 参数验证
        $validation = [
            'month' => 'Required|Str|>>>:[month] params error', // 月份必填
        ];

        Validation::validate($params, $validation);

        // 获取文件名
        $file_name   = 'Rotational_suggestions_' . date('YmdHis');
        $action_name = 'workday' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'workdayDailySettingExport';

        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];

        [$msg, $code] = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            $file_name,
            $action_name,
            $params
        );

        if ($code == ErrCode::SUCCESS) {
            return $this->returnJson(ErrCode::SUCCESS, $msg, []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'export failed', []);
    }

    /**
     * 导入按日配置
     * @description: 导入按日配置Excel文件，支持批量设置网点职位的日期配置
     * @author: AI
     * @date: 2024-01-15 10:30:00
     * @Permission(action='free_workday_import')
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function importDailyAction()
    {
        $params              = $this->request->get();
        $params['file_type'] = AsyncImportTaskModel::WORKDAY_SETTING_DAILY;

        // 参数验证
        Validation::validate($params, [
            'month'    => 'Required|Str|>>>:[month] params error',
            'file_url' => 'Required|Str|>>>:[file_url] params error',
        ]);

        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->importDaily($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 下载按日配置导入模板
     * @description: 生成按日配置导入模板，包含双行表头和下拉菜单
     * @author: AI
     * @date: 2024-01-15 10:30:00
     * @Permission(action='free_workday_import')
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function templateDailyAction()
    {
        $params = $this->request->get();

        // 参数验证
        Validation::validate($params, [
            'month' => 'Required|Str|>>>:[month] params error',
        ]);

        $month = $params['month'];

        // 验证月份格式
        if (!preg_match('/^\d{4}-\d{2}$/', $month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('invalid_month_format'));
        }

        // 验证月份不能是过去的月份
        if ($month < date('Y-m')) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('month_cannot_be_past'));
        }
        $service = new WorkdaySettingService();
        $data    = $service->generateDailyTemplate($month);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 删除按日配置
     * @Permission(action='free_workday_delete')
     */
    public function deleteDailyAction()
    {
        $params = $this->request->get();
        // 参数验证
        Validation::validate($params, [
            'id' => 'Required|Arr|>>>:[id] params error',
        ]);

        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->delete_daily($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 获取轮休配置详情
     * @description: 获取指定日期（单个或多个）和网点的详细轮休配置信息，按职位类型分组展示
     * @author: AI
     * @date: 2024-01-15 10:30:00
     * @api workday-setting/daily-suggest-detail
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dailySuggestDetailAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        // 参数验证
        $validation = [
            'date_at'  => 'Required|Date|>>>:[date] params error',
            'store_id' => 'Required|Str|>>>:[store_id] params error',
        ];
        Validation::validate((array)$params, $validation);
        $params['operate_id'] = $this->user['id'];
        $service              = new WorkdaySettingService();
        $data                 = $service->getDailySuggestDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @Permission(action='free_workday_view')
     */
    public function testAction()
    {
//        $messageType = 'full_to_non_full';
        $messageType = '';
        $changeDates = ['2025-07-02','2025-07-05'];
        $allStaffIds = [120793];
        // 构建消息内容，包含网点、职位类型和变更日期信息
        $dateList = implode(', ', $changeDates);

        // 构建员工用户数组
        $staffUsers = [];
        foreach ($allStaffIds as $staffId) {
            $staffUsers[] = ['id' => $staffId];
        }

        // 准备消息参数
        $messageParam = [
            'staff_info_ids_str' => implode(',', $allStaffIds),
            'staff_users'        => $staffUsers,
        ];

        //普通消息
        if ($messageType == 'full_to_non_full') {
            $messageParam['category']        = 0;
            $messageParam['message_title']   = $this->t->_('full_to_non_title');
            $content                         = $this->t->_('full_to_non_content', ['date_list' => $dateList]);
//            $messageParam['message_content'] = "<p style='font-size: 32px;'>" . $content . "</p>";
            $messageParam['message_content'] = $content;
        }
        //签字消息
        if ($messageType == 'non_full_to_full') {
            $messageParam['category']        = 33;
            $messageParam['category_code']   = 25;
            $messageParam['message_title']   = $this->t->_('non_to_full_title');
            $content                         = $this->t->_('non_to_full_content', ['date_list' => $dateList]);
//            $messageParam['message_content'] = "<p style='font-size: 32px;'>" . $content . "</p>";
            $messageParam['message_content'] = $content;
        }
        if (empty($messageType)) {
            $messageTitle = $this->t->_('workday_suggest_change_title');
            $messageContent = $this->t->_('workday_suggest_change_content', ['update_time' => date('Y-m-d H:i:s')]);
            $messageParam['category']        = 46;
            $messageParam['message_title']   = $messageTitle;
//            $messageParam['message_content'] = "<p style='font-size: 32px;'>" . $messageContent . "</p>";
            $messageParam['message_content'] = $messageContent;
        }

        // 发送消息
        $messageServer = new MessagesService();
        $messageServer->add_kit_message($messageParam);
    }


}
