<?php
/**
 * HUB,B-HUB网点外协员工管理
 */

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\ExcelService;
use App\Services\HubOutSourcingStaffService;

class HubOutSourcingStaffController extends BaseController
{
    /**
     * 获取枚举数据
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function getSysInfoAction()
    {
        $result = (new HubOutSourcingStaffService())->getSysInfo();
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取枚举数据
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getCompanyInfoAction()
    {
        $result = (new HubOutSourcingStaffService())->companyInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }
    
    /**
     * 获取hub外协员工
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function getListAction()
    {
        $params = $this->request->get();
        $params = Validation::ignoreParams($params, ['hire_date', 'name', 'company_id', 'sys_store_id', 'state']);
        $validation = [
            'page' => 'Required|Int',
            'pagesize' => 'Required|Int',
            'name' => 'StrLenGe:0',
            'company_id' => 'IntGe:0',
            'sys_store_id' => 'StrLenGe:0',
            'state' => 'IntGe:0',
        ];
        Validation::validate($params, $validation);
        
        $result = (new HubOutSourcingStaffService())->getList($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * Notes: 导出hub外协员工
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function exportAction()
    {
        $paramIn    = $this->request->get();
        $paramIn    = Validation::ignoreParams($paramIn, ['hire_date', 'name', 'company_id', 'sys_store_id', 'state']);
        $validation = [
            'name'         => 'StrLenGe:0',
            'company_id'   => 'IntGe:0',
            'sys_store_id' => 'StrLenGe:0',
            'state'        => 'IntGe:0',
        ];
        Validation::validate($paramIn, $validation);

        $paramIn['operator_id'] = $this->user['id'];
        $paramIn['lang']        = $this->locale;//语言

        $fileName   = ExcelService::generateExcelFileName('hub_out_sourcing_staff');
        $actionName = ExcelService::generateActionName('hub_out_sourcing_staff', 'down_excel');

        // 加锁处理
        $lock_key = md5('hub_out_sourcing_staff_export'.$paramIn['operator_id']);
        //入 task 表 走队列导出
        $data = $this->atomicLock(function () use ($paramIn, $actionName, $fileName) {
            return (new ExcelService())->insertTask($this->user['id'], $actionName, $paramIn,
                HcmExcelTackModel::TYPE_HUB_OUT_SOURCING_REPORT, $fileName);
        }, $lock_key, 60);
        if ($data === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success',
            $data['data'] ?? []);
    }
    
    /**
     * 获取单个外协员工详情
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function getOneOutSourcingStaffAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_info_id' => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->getOneOutSourcingStaff($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 编辑外协员工数据
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function updateOutSourcingStaffAction()
    {
        $params     = $this->request->get();
        $validation = [
            'staff_info_id'            => "Required|StrLenGe:1",
            "name"                     => "Required|StrLenGeLe:1,32",
            "sex"                      => "Required|IntIn:1,2",
            "nationality"              => "Required|IntGe:1",
            "identity"                 => "Required|StrLenGeLe:1,32",
            "sys_store_id"             => "Required|StrLenGe:1",
            "job_title"                => "Required|IntGe:1",
            "outsourcing_company_id"   => "Required|IntGe:1",
            "outsourcing_company_name" => "Required|StrLenGe:1",
            "hikvision_org_index_code" => "Required|StrLenGe:1",
            "hikvision_org_name"       => "Required|StrLenGe:1",
            "identity_file_a"          => "Required|StrLenGeLe:1,255|>>>:".$this->t->_('identity_file_error'),  //
            "face_img_path"            => "Required|StrLenGeLe:1,255|>>>:".$this->t->_('face_img_error'),       // 请上传正确的人脸照
        ];
        //只允许编辑以下字段，其他传递过来的字段过滤
        foreach ($validation as $key => $val) {
            $new_data[$key] = $params[$key];
        }
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->updateOutSourcingStaff($new_data);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 修改员工在职状态，在职 or 离职
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function updateOutSourcingStaffStateAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_info_id' => 'Required|IntGe:1',
            "state" => "Required|IntIn:1,2",
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->updateOutSourcingStaffState($params, $this->user);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
    
    /**
     * 设置外协员工班组
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function updateHikOrgAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_info_id' => 'Required|IntGe:1',
            'hikvision_org_index_code' => "Required|StrLenGe:1",
            'hikvision_org_name' => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->updateHikOrg($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 批量设置外协员工班组
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException|\App\Library\Exception\BusinessException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function batchUpdateHikOrgAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_info_ids' => 'Required|ArrLenGe:1',
            'hikvision_org_index_code' => "Required|StrLenGe:1",
            'hikvision_org_name' => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->batchUpdateHikOrg($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }
    
    /**
     * 提交同步海康任务
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function syncHikTaskAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_info_ids' => 'ArrLenGe',
        ];
        Validation::validate($params, $validation);
    
        $lock_key = 'sync_hik_task_add' . $this->user['id'];
        $user_info = $this->user;
        $result = $this->atomicLock(function() use ($params, $user_info){
            return (new HubOutSourcingStaffService())->syncHikTask($params, $user_info);
        }, $lock_key, 60);
        
        //$result = (new HubOutSourcingStaffService())->syncHikTask($params, $this->user);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
    
    /**
     * 同步海康结果
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function syncHikResultAction()
    {
        $params = $this->request->get();
        $validation = [
            'page' => 'Required|Int',
            'pagesize' => 'Required|Int',
        ];
        Validation::validate($params, $validation);
        
        $result = (new HubOutSourcingStaffService())->syncHikResult($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * * 获取外协班组组织列表
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function getHikOrgListAction()
    {
        $result = (new HubOutSourcingStaffService())->getHikOrgListInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);

    }

    /**
     * 编辑外协班组组织信息
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function hikOrgUpdateAction()
    {
        $params = $this->request->get();
        $validation = [
            'orgIndexCode' => "Required|StrLenGe:1",
            'orgName' => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->hikOrgUpdateInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 添加外协班组组织信息
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function hikOrgAddAction()
    {
        $params = $this->request->get();
        $validation = [
            'parentIndexCode' => "Required|StrLenGe:1",
            'orgName' => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->hikOrgAddInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 添加外协班组组织信息
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='hub_outsourcing_staff_manage')
     */
    public function hikOrgDeleteAction()
    {
        $params = $this->request->get();
        $validation = [
            'indexCode' => "Required|StrLenGe:1"
        ];
        Validation::validate($params, $validation);
        $result = (new HubOutSourcingStaffService())->hikOrgDeleteInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }
    
}