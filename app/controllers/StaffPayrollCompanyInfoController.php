<?php


namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\StaffPayrollCompanyInfoService;
use App\Services\StaffSupportStoreServer;

class StaffPayrollCompanyInfoController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }
    /**
     * 员工搜索
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchStaffAction()
    {
        $params = $this->request->get();
        $validations = [
            'search_staff' => 'Required|StrLenGeLe:1,20|>>>:' . $this->t->_('support_store_error_1'),
            'page_size' => 'Int'
        ];
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);
        Validation::validate($params, $validations);

        $result = (new StaffPayrollCompanyInfoService())->searchStaff($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }


    /**
     * @description:  修改公司信息
     * @Token
     * @Permission(action='staff_payroll_company_info.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function editAction()
    {
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollCompanyInfoService::$v_param);
        if (empty($params['tax_number'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_company_tax_number_error'));
        }
        if (empty($params['labor_sign_url'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('payroll_company_labor_sign_error'));
        }
        $params['operator'] = $this->user['id'];
        $result = (new StaffPayrollCompanyInfoService())->edit($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * @description: 获取公司详情
     * @param null
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:30
     * @Permission(action='staff_payroll_company_info.getDetail')
     * @Token
     */
    public function getDetailAction()
    {
        $result = (new StaffPayrollCompanyInfoService())->getDetail();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * @description: 获取变更记录
     * @param null
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:32
     * @Token
     * @Permission(action='staff_payroll_company_info.getList')
     */
    public function getListAction()
    {
        $params = $this->request->get();
        $params['operator'] = $this->user['id'];
        $result = (new StaffPayrollCompanyInfoService())->getList($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * 获取公司枚举值
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getCompanyEnumsAction(){
        $result = (new StaffPayrollCompanyInfoService())->getCompanyEnums();
        return $this->returnJson(ErrCode::SUCCESS,'ok',$result);
    }
    /**
     * 获取公司枚举值(有权限)
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getCompanyEnumsWithPermissionAction(){
        $staffId = $this->user['id'];
        $result = (new StaffPayrollCompanyInfoService())->getCompanyEnumsWithPermission($staffId);
        return $this->returnJson(ErrCode::SUCCESS,'ok',$result);
    }


}