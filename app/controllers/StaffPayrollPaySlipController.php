<?php


namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Id\Services\MessagesService;
use App\Modules\My\Services\PayrollReportService;
use App\Services\StaffPayrollCompanyInfoService;
use App\Services\StaffPayrollPaySlipService;
use App\Services\StaffPayrollYearListService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class StaffPayrollPaySlipController extends BaseController
{

    public function initialize() {
        parent::initialize();
    }

    public static $lock_expire = 2; //锁时间

    /**
     * @description:获取操作字段枚举
     * @param null
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:23
     */

    public function  getStaticEnumsAction()
    {
        $result = (new StaffPayrollPaySlipService())->getStaticEnums();
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }


    /**
     * @description: 获取列表
     * @param null
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     * <AUTHOR> L.J
     * @time       : 2022/6/1 17:32
     * @Token
     * @Permission(action='staff_payroll_pay_slip.getList')
     */
    public function getListAction()
    {
        $params = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $result = (new StaffPayrollPaySlipService())->getList($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * @description: 获取详情 主要是个人邮箱下拉选项  (发送邮箱权限)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/6 17:26
     * @Token
     * @Permission(action='staff_payroll_pay_slip.sendEmail')
     */
    public function getDetailAction() {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required']);
        $result = (new StaffPayrollPaySlipService())->getDetail($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * @description:发送邮件
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/8 14:32
     * @Token
     * @Permission(action='staff_payroll_pay_slip.sendEmail')
     */
    public function sendEmailAction(){
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollPaySlipService::$send_email_param);
        $params['operate_id'] = $this->user['id'];
        // 加锁处理
        $lock_key = 'staff_payroll_pay_slip_send_email_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ($params) {
            return (new StaffPayrollPaySlipService())->sendEmail($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success',
            $result['data'] ?? []);
    }

    /**
     * @description:下载数据
     * @return Response|ResponseInterface :
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2022/6/8 14:32
     * @Token
     * @Permission(action='staff_payroll_pay_slip.download')
     */
    public function downloadAction(){
        $params = $this->request->get();
        Validation::validate($params, StaffPayrollPaySlipService::$download_param);
        $params['operate_id']   = $this->user['id'];
        // 加锁处理
        $lock_key = 'staff_payroll_pay_slip_download_' . md5($params['operate_id']);
        $result   = $this->atomicLock(function () use ( $params ) {
            return (new StaffPayrollPaySlipService())->download($params);
        }, $lock_key, self::$lock_expire);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }


    /**
     * @description: Generate 生成
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 10:11
     * @Token
     * @Permission(action='staff_payroll_pay_slip.generate')
     */
    public function generateAction() {
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data'] ?? []);
    }

    /**
     * @description: 刷新
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 10:11
     * @Token
     * @Permission(action='staff_payroll_pay_slip.refresh')
     */
    public function refreshAction() {
        $result['data'] =  (new PayrollReportService())->schedule('payslip');
        return $this->returnJson(ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data']);
    }
    /**
     * @description: 获取 History 列表
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/8 14:38
     * @Token
     * @Permission(action='staff_payroll_pay_slip.getListHistory')
     */
    public function getListHistoryAction() {
        $params = $this->request->get();
        $params['operate_id']   = $this->user['id'];
        $result = (new StaffPayrollPaySlipService())->getListHistory($params);
        return $this->returnJson($result['code'] ?? ErrCode::SYSTEM_ERROR, $result['message'] ?? 'success', $result['data'] ?? []);
    }


}