<?php

namespace App\Controllers;


use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\ErrCode;
use App\Models\backyard\AsyncImportTaskModel;
use App\Services\AsyncImportTaskService;
use App\Services\ExcelService;
use App\Services\SchedulingSuggestionService;
use SchedulingSuggestionTask;

/**
 * 排班建议
 * Class SchedulingSuggestionController
 * @package App\Controllers
 */
class SchedulingSuggestionController extends BaseController
{
    /**
     * 下载模板
     * @Token
     * @throws \Exception
     */
    public function downloadTemplateAction()
    {
        $type = $this->request->get('type');
        if (!in_array($type,
            [SchedulingSuggestionService::EXPORT_TYPE_RULE, SchedulingSuggestionService::EXPORT_TYPE_PEOPLE])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        if ($type == SchedulingSuggestionService::EXPORT_TYPE_PEOPLE) {
            $month = $this->request->get('month');
            preg_match('/[0-9]{4}-[0-9]{2}/', $month, $match);
            if (empty($match) || empty($month)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
            }
            if ($month < date('Y-m')) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('month error'));
            }
            $data = (new SchedulingSuggestionService())->downloadTemplate($month);
        } else {
            $data = (new SchedulingSuggestionService())->downloadRuleTemplate();
        }

        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 查询列表
     * @Token
     * @Permission(action='scheduling_suggestion.select')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $storeSearchIds = $this->request->get('store_ids');
        $regionSearchId = $this->request->get('region_id');
        $pieceSearchIds = $this->request->get('piece_ids');
        $month          = $this->request->get('month');
        $positionType   = $this->request->get('position_type');
        $pageSize       = $this->request->get('page_size');
        $pageNum        = $this->request->get('page_num');
        $operateId      = $this->user['id'] ?? 0;
        if ($positionType) {
            if (!in_array($positionType, array_keys(SchedulingSuggestionEnums::$positionTypeMap))) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
            }
        }
        preg_match('/[0-9]{4}-[0-9]{2}/', $month, $match);
        if (empty($match) || empty($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        $params['month']            = $month;
        $params['store_search_ids'] = $storeSearchIds;
        $params['region_search_id'] = $regionSearchId;
        $params['piece_search_ids'] = $pieceSearchIds;
        $params['position_type']    = $positionType;
        $params['operate_type']     = SchedulingSuggestionEnums::OPERATE_SELECT;
        $params['operate_id']       = $operateId;
        $params['page_size']        = $pageSize;
        $params['page_num']         = $pageNum;
        $data                       = (new SchedulingSuggestionService())->list($params);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 上传导入
     * @Token
     * @Permission(action='scheduling_suggestion.import')
     * @throws \Exception
     */
    public function uploadAction()
    {
        $month      = $this->request->get('month');
        $importPath = $this->request->get('path');
        $type       = $this->request->get('type');// 导入规则1 导入人数2
        if (!in_array($type,
            [SchedulingSuggestionService::EXPORT_TYPE_RULE, SchedulingSuggestionService::EXPORT_TYPE_PEOPLE])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        preg_match('/[0-9]{4}-[0-9]{2}/', $month, $match);
        if (empty($match) || empty($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        if ($month < date('Y-m')) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('month error'));
        }
        if (empty($importPath)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'path not found');
        }
        (new SchedulingSuggestionService())->uploadCheck($month, $importPath, $type);
        $args['month'] = $month;
        $args['lang']  = $this->request->getBestLanguage();
        $args['type']  = $type;
        $operatorId    = $this->user['id'] ?? 0;
        (new AsyncImportTaskService())->insertTask($operatorId,
            AsyncImportTaskModel::SCHEDULING_SUGGESTION, $importPath, $args);
        return $this->returnJson(ErrCode::SUCCESS, 'upload success');
    }

    /**
     * 删除
     * @Token
     * @Permission(action='scheduling_suggestion.delete')
     * @throws \App\Library\Exception\BusinessException
     */
    public function deleteAction()
    {
        $ids        = $this->request->get('ids');
        $operatorId = $this->user['id'] ?? 0;
        if ($ids && !is_array($ids)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        (new SchedulingSuggestionService())->delete($ids, $operatorId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok');
    }

    /**
     * 排班建议异步导出
     * @Token
     * @Permission(action='scheduling_suggestion.select')
     * @throws \Exception
     */
    public function exportAction()
    {
        $storeIds       = $this->request->get('store_ids');
        $regionSearchId = $this->request->get('region_id');
        $pieceSearchIds = $this->request->get('piece_ids');
        $month          = $this->request->get('month');
        $positionType   = $this->request->get('position_type');
        if ($positionType) {
            if (!in_array($positionType, array_keys(SchedulingSuggestionEnums::$positionTypeMap))) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
            }
        }
        preg_match('/[0-9]{4}-[0-9]{2}/', $month, $match);
        if (empty($match) || empty($month)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        $param['store_ids']        = $storeIds;
        $param['month']            = $month;
        $param['position_type']    = $positionType;
        $param['region_search_id'] = $regionSearchId;
        $param['piece_search_ids'] = $pieceSearchIds;
        $service                   = new ExcelService();
        $userId                    = $this->user['id'] ?? 10000;
        $fileName                  = 'scheduling suggestion export ' . date('YmdHis') . '.xlsx';
        $actionName                = SchedulingSuggestionTask::$downloadTaskName;
        $data                      = $service->insertTask($userId, $actionName, $param, 0, $fileName);
        return $this->returnJson($data['code'], $data['message']);
    }

    /**
     * @token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getSchedulingSuggestAction()
    {
        $startTime = $this->request->get('start');
        $endTime   = $this->request->get('end');
        $storeId   = $this->request->get('store_id');
        $data      = (new SchedulingSuggestionService())->getSchedulingSuggest($storeId, $startTime, $endTime);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 获取网点-职位排班规则列表
     * @token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getShiftRateListAction()
    {
        $storeId      = $this->request->get('store_id');
        $positionType = $this->request->get('position_type');
        $data         = (new SchedulingSuggestionService())->getShiftRateList($storeId, $positionType);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 获取网点-职位排班率
     * @token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function getShiftRateAction()
    {
        $ruleId = $this->request->get('rule_id');
        $data   = (new SchedulingSuggestionService())->getShiftRate($ruleId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 保存网点-职位排班率
     * @Permission(action='scheduling_suggestion.rule')
     * @token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function saveShiftRateAction()
    {
        $storeId               = $this->request->get('store_id');
        $positionType          = $this->request->get('position_type');
        $percentage            = $this->request->get('percentage');
        $startTime             = $this->request->get('start_time');
        $endTime               = $this->request->get('end_time');
        $ruleId                = $this->request->get('rule_id');
        $secondaryConfirmation = $this->request->get('is_submit'); //二次确认 1
        $userId                = $this->user['id'];
        $service               = new SchedulingSuggestionService();
        if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $startTime)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'date error');
        }
        if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $endTime) && !in_array($endTime, ['', null, '-'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'date error');
        }
        if (in_array($endTime, ['', null, '-'])) {
            $endTime = $service->finalEndTime;
        }
        if (strtotime($endTime) < strtotime($startTime)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('schedule_suggest_rule_date_error_5'));
        }
        $params['store_id']               = $storeId;
        $params['position_type']          = $positionType;
        $params['percentage']             = $percentage;
        $params['start_time']             = $startTime;
        $params['end_time']               = $endTime;
        $params['rule_id']                = $ruleId;
        $params['operate_id']             = $userId;
        $params['secondary_confirmation'] = empty($secondaryConfirmation) ? 'yes' : 'no';
        $secondaryConfirmationData        = $service->saveShiftRate($params);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $secondaryConfirmationData);
    }
}