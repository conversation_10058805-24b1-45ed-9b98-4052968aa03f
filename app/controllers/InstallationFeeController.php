<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 7/19/23
 * Time: 11:27 AM
 */

namespace App\Controllers;


//三轮车摩托 挎斗安装费 扣款
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\MotorInstallationFeeModel;
use App\Services\BllService;
use App\Services\InstallationFeeService;
use App\Services\TricycleInstallationAllowanceService;
use App\Services\TricycleInstallationFeeService;

class InstallationFeeController extends BaseController{


    /**
     * @description:列表页查询
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function listInstallationAction(){

        $param = $this->request->get();
        $validations = [
            'staff_info_id'     => 'Required',
        ];
        Validation::validate($param, $validations);
        $server = new InstallationFeeService();
        $data = $server->getInstallationData($param);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * @description://扣款状态枚举
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function statusEnumAction(){
        $data = MotorInstallationFeeModel::$deductStatus;
        $return = [];
        foreach ($data as $code => $da){
            $row['label'] = $this->t->_($da);
            $row['value'] = $code;
            $return[] = $row;
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $return);
    }


    /**
     * @description://列表页导出
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function exportInstallationAction(){
        $param = $this->request->get();
        $param['user_info'] = $this->user;
        //公用验证时间跨度
        $param['lang'] = $this->locale;
        $param['current_uid'] = $this->user['id'];
        $res = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "motor_installation_fee",
            "installation_fee".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'listExport',
            $param
        );
        if(empty($res)){
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'server error', []);
        }
        [$msg , $code] = $res;
        return $this->returnJson($code , $msg , []);
    }


    /**
     * @description://上传三轮车补贴员工
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function uploadInstallationAction(){
        $param = $this->request->get();
        Validation::validate($param, [
            'file_url' => 'Required|Str',
        ]);
        $server = new InstallationFeeService();
        $param['user_info'] = $this->user;
        //是否保存
        $param['is_submit'] = true;
        $data   = $server->addStaffImport($param);
        $this->logger->info("uploadInstallation " . json_encode($param).json_encode($data));
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * @description://上传删除三轮车补贴员工
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function uploadDeleteAction(){
        $param = $this->request->get();
        Validation::validate($param, [
            'file_url' => 'Required|Str',
        ]);
        $server = new InstallationFeeService();
        $param['user_info'] = $this->user;
        //是否保存
        $param['is_submit'] = true;
        $this->logger->info("uploadDelete " . json_encode($param));
        $data   = $server->delStaffImport($param);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * @description://统计 扣款数据 异步
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function statisticInstallationAction(){
        $param = $this->request->get();
        $param['user_info'] = $this->user;
        $server = new InstallationFeeService();
        $res = $server->statisticAddTaskUseLock($param);
        return $this->returnJson($res['code'], $res['message'], null);
    }


    /**
     * @description://导出统计后的数据
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function exportStatisticAction(){
        $param = $this->request->get();
        //整理 工号数据 空行分割
        if(!empty($param['staff_ids'])){
            $param['staff_ids'] = clearSpecialChar($param['staff_ids']);
        }
        $param['user_info'] = $this->user;
        //公用验证时间跨度
        $param['lang'] = $this->locale;
        $param['current_uid'] = $this->user['id'];
        $res = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "statistic_installation_fee",
            "installation_fee".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'statisticExport',
            $param
        );
        if(empty($res)){
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'server error', []);
        }
        [$msg , $code] = $res;
        return $this->returnJson($code , $msg , []);
    }


    /**
     * @description://计算任务列表
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function statisticTaskListAction(){
        $param = $this->request->get();
        Validation::validate($param, [
            'page' => 'Required|Int',
            'size' => 'Required|Int',
        ]);
        $server = new InstallationFeeService();
        $data   = $server->taskList($param);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 每月 模板下载
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function templateDownloadAction(){
        $param = $this->request->get();
        Validation::validate($param, [
            'type' => 'Required|Int',
        ]);
        $service = new InstallationFeeService();
        $url = $service->getTemp($param);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $url);
    }

    /**
     * 安装费导出
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function tricycleInstallationFeeExportAction()
    {
        $month = $this->request->get('month');
        $url   = (new TricycleInstallationFeeService())->tricycleInstallationFeeExport($month);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['url' => $url]);
    }

    /**
     * 补贴导出
     * @Permission(action='motor.bike.operate')
     * @Token
     */
    public function tricycleAllowanceExportAction()
    {
        $month = $this->request->get('month');
        $url   = (new TricycleInstallationAllowanceService())->tricycleAllowanceExport($month);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['url' => $url]);
    }

}