<?php
/**
 * Author: Bruce
 * Date  : 2022-12-07 22:00
 * Description:
 */

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use app\models\backyard\OutsourcingHubStaffAttendanceModel;
use App\Services\HubOsAttendanceService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class HubOsAttendanceController extends BaseController
{

    /**
     * 获取筛选项信息
     * @return Response|ResponseInterface
     */
    public function getSelectInfoAction()
    {
        $result = (new HubOsAttendanceService())->getSelectInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取HUB外协考勤数据 列表
     * @Token
     * @Permission(action='hub_attendance_statistict_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStaffListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'job_title'     => 'IntGe:1',   // 职位查询
            'is_late'       => 'IntGe:0|IntIn:0,1,99',      // 是否迟到
            'is_leave_early' => 'IntGe:0|IntIn:0,1,99', // 是否早退
            'page'      => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];

        // 页面初始加载时前端传递null，当选择职位后查询后，在清空查询域此时，前端传递了""，此处做兼容
        if (is_string($params['job_title']) && $params['job_title'] == "") {
            $params['job_title'] = OutsourcingHubStaffAttendanceModel::OUTSOURCING_JOB_ALL;
        }

        if (is_string($params['is_late']) && $params['is_late'] == "") {
            $params['is_late'] = OutsourcingHubStaffAttendanceModel::LATE_ALL;
        }

        if (is_string($params['is_leave_early']) && $params['is_leave_early'] == "") {
            $params['is_leave_early'] = OutsourcingHubStaffAttendanceModel::LEAVE_EARLY_ALL;
        }

        Validation::validate($params, $validate_rule);
        $result = (new HubOsAttendanceService())->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取HUB外协考勤数据 导出
     * @Token
     * @Permission(action='hub_attendance_statistict_export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStaffListExportAction()
    {
        $params                = $this->request->get();
        $params['is_download'] = 1;
        $params['file_name']   = uniqid('hub_attendance_statistics_export'.$this->locale.'_').'.xlsx';
        $params['lang']        = $this->locale;
        $params['staff_id']    = $this->user['id'];
        // 加锁处理
        $lock_key   = 'hub_attendance_statistics_export_'.$this->user['id'];
        $get_result = $this->atomicLock(function () use ($params) {
            return (new HubOsAttendanceService())->handleExportHubAttendance($params);
        }, $lock_key, 60);
        if ($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], $get_result['data']);
    }

    /**
     * 获取HUB外协考勤数据 列表
     * @Token
     * @Permission(action='hub_attendance_statistict_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getHubAttendanceOTDetailAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'os_ot_id' => 'Required|IntGt:0'
        ];
        Validation::validate($params, $validate_rule);

        $result = (new HubOsAttendanceService())->getHubAttendanceOTDetail($params, $this->user);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取HUB外协考勤 生成对账单
     * @Token
     * @Permission(action='hub_attendance_statistict_export')
     * @return Response|ResponseInterface
     */
    public function createBillAction()
    {
        $params                = $this->request->get();
        $params['is_download'] = 1;
        $params['file_name']   = 'hub_attendance_statistics_bill_export';
        $params['lang']        = $this->locale;
        $params['staff_id']    = $this->user['id'];
        $last_id               = (new HubOsAttendanceService())->createBillUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], ['last_id' => $last_id]);
    }

    /**
     * HUB外协加班订单-枚举
     * @Token
     * @Permission(action='hub_overtime_order_list')
     * @return Response|ResponseInterface
     */
    public function getOtSelectInfoAction()
    {
        $result = (new HubOsAttendanceService())-> getOtSelectInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取HUB外协考勤数据 列表
     * @Token
     * @Permission(action='hub_overtime_order_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getOtListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'page_num'      => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];

        Validation::validate($params, $validate_rule);
        $result = (new HubOsAttendanceService())->getOtList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * HUB 外协加班 导出
     * @Token
     * @Permission(action='hub_overtime_order_list_export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getOtListExportAction()
    {
        $params = $this->request->get();

        $params['user_id'] = $this->user['id'];
        $params['lang']        = $this->locale;
        $params['file_name']   = uniqid('hub_ot_list_export_'.$this->locale.'_').'.xlsx';

        // 加锁处理
        $get_result = (new HubOsAttendanceService())->getOtListExportUseLock($params);

        return $this->returnJson($get_result['code'], $this->t['file_download_tip'], $get_result['data']);
    }

    /**
     * HUB 外协加班 查看
     * @Token
     * @Permission(action='hub_overtime_order_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getOtDetailAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'ot_id' => 'Required|IntGt:0'
        ];

        Validation::validate($params, $validate_rule);
        $result = (new HubOsAttendanceService())->getOtDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

}