<?php


namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Services\UserService;
use Exception;

/**
 * 员工的权限管理
 * desc: 用户给查看、编辑员工的权限
 * Class UserPermissionController
 * @package App\Controllers
 */
class UserpermissionController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 按工号配置角色
     *
     * @Permission(action='user.permission.grant')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function grantAction()
    {
        $uid = $this->request->get('staff_id', 'int');
        $pids = $this->request->get('pids');

        $validation = [
            'uid'=> 'Required|Int',
            'pids' => 'Required|Arr'
        ];
        Validation::validate(['uid'=>$uid,'pids'=>$pids], $validation);
        $this->getDI()->get('logger')->info("set staff permissions : login_staff_id:{$this->user['id']} set staff_number:{$uid}  permissions:".implode(',',$pids));
        $userService = new UserService();
        $userService->setUserPermissions($uid, $pids, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', []);
    }

    /**
     * 查询指定工号所有拥有的权限信息
     * @Permission(action='user.permission.grant')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @throws Exception
     */
    public function listAction()
    {
        $staff_id = $this->request->get('staff_id', 'int');
        $validation = [
            'staff_id'=> 'Required|Int',
        ];

        Validation::validate(['staff_id'=>$staff_id], $validation);
        $userService = new UserService();
        $data = $userService->getUserPermissionsInfo($staff_id);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * @Token
     * 按工号查询 payroll相关权限
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function payrollListAction()
    {
        $staff_id = $this->request->get('staff_id', 'int');
        $validation = [
            'staff_id'=> 'Required|Int',
        ];

        Validation::validate(['staff_id'=>$staff_id], $validation);
        $userService = new UserService();
        $data = $userService->getUserPayRolePermissionsInfo($staff_id, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * @Token
     * payroll 提交 权限变更，身份校验
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function identityCheckAction()
    {
        $staff_id = $this->request->get('staff_id', 'trim');
        $password = $this->request->get('password', 'trim');
        $validation = [
            'staff_id'=> 'Required|StrLenGe:1|StrLenLe:10',
            'password' => 'Required|StrLenGe:1|StrLenLe:100'
        ];

        Validation::validate(['staff_id' => $staff_id, 'password' => $password], $validation);
        $userService = new UserService();
        $data = $userService->identityCheckInfo(['login' => $staff_id, 'password' => $password], $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function payrollGrantAction()
    {
        $uid = $this->request->get('staff_id', 'int');
        $pids = $this->request->get('pids');

        $validation = [
            'uid'=> 'Required|Int',
            'pids' => 'Required|Arr'
        ];
        Validation::validate(['uid'=>$uid,'pids'=>$pids], $validation);
        $this->getDI()->get('logger')->info("set staff permissions : login_staff_id:{$this->user['id']} set staff_number:{$uid}  permissions:".implode(',',$pids));
        $userService = new UserService();
        $userService->setUserPayrollPermissions($uid, $pids, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', []);
    }
}
