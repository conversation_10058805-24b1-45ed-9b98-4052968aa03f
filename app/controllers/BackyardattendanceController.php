<?php

namespace App\Controllers;
use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\AttendanceDataLogModel;
use App\Models\backyard\AttendanceOperateLogModel;
use App\Models\backyard\BusinessTripModel;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\fle\StaffInfoModel;
use App\Models\fle\SysStoreModel;
use App\Services\AsyncImportTaskService;
use App\Services\AttendanceBllService;
use App\Services\AttendanceToolService;
use App\Models\backyard\StaffAuditModel;
use App\Library\Enums;
use App\Models\backyard\SettingEnvModel;
use App\Services\BllService;
use App\Services\BusinessTripService;
use App\Services\OvertimeBllService;
use App\Services\StaffInfoService;
use App\Services\StaffService;
use App\Services\ToolService;
use App\Services\WorkdayService;
use App\Services\SettingEnvService;
use App\Services\WorkflowService;
use Exception;
use MyCLabs\Enum\Enum;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;
use App\Library\Enums\BusinessTripEnums;

class BackyardattendanceController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @Token
     * 校验工号是否有申请OT的权限
     * @return Response|ResponseInterface
     */
    public function applyOtCcheckAction(){
        $params = $this->request->get();
        if(empty($params['staff_id']) || !is_numeric($params['staff_id'])){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'staff_id is error', []);
        }
        //校验工号是否有申请OT的权限
        $bool = (new AttendanceToolService())->applyOtCcheckGrade( $params );
        if(!$bool){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['apply_ot_check_grade'], []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * @Token
     * 获取ot类型和时长 是根据每个员工的权限返回对应的加班类型
     * @return Response|ResponseInterface
     */
    public function getOtTypeByStaffInfoIdAction(){
        $params = $this->request->get();
        if(empty($params['staff_id']) || !is_numeric($params['staff_id'])){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'staff_id error', []);
        }
        $data = (new AttendanceToolService())->getOtTypeAndSelectTime($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * 新增OT
     * @Permission(action='tool_add_ot')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function addOtAction()
    {
        $params                  = $this->request->get();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        $validate                = [
            'date_at'       => 'Required|Str',
            'start_time'    => 'Required|Str',
            'duration'      => 'Required|FloatGt:0',
            'type'          => 'Required|int',
            'staff_info_id' => 'Required|int',
        ];
        //校验
        Validation::validate($params, $validate);
        $send_params = [
            'edit_type'     => $params['edit_type'],
            'staff_info_id' => $params['staff_info_id'],
            'staff_id'      => $params['staff_info_id'],
            'operator'      => $params['operator'],
            'operator_name' => $params['operator_name'],
            'date_at'       => $params['date_at'],
            'start_time'    => $params['start_time'],
            'duration'      => $params['duration'],
            'type'          => $params['type'],
            'reason'        => filter_param($params['reason']),
        ];

        if (!empty($params['imgpath'])) {
            $send_params['image_path'] = array_column($params['imgpath'], 'object_url');
        }

        if (isCountry( 'MY')) {
            $send_params['time_type'] = intval($params['time_type']);
        }
        $server = new AttendanceToolService();
        $server->addOt($send_params);
        return $this->returnJson(ErrCode::SUCCESS, 'success!', []);
    }


    /**
     * 修改OT
     * @Token
     * @Permission(action='tool_modify_ot')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws Exception
     */
    public function modifyOtAction()
    {
        $params                  = $this->request->get();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        Validation::validate($params, [
            'staff_id' => 'Required|Str',
        ]);
        $service                 = new AttendanceToolService();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        $params['staff_info_id'] = $params['staff_id'];
        if (false === $service->checkOperatorPermission($params)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['permission_denied'], []);
        }


        $service             = reBuildCountryInstance(new AttendanceToolService());
        $params['user_info'] = $this->user;
        $service->tool_save($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 工具号补卡
     * @Token
     * @Permission(action='tool_staff_make_up')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function toolMakeUpAction()
    {
        $params = $this->request->get();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        Validation::validate($params, [
            'attendance_date' => 'Required|Str',                                      //出勤时间
            'attendance_type' => 'Required|IntIn:1,2|>>>:attendance_type param error',//补卡类型 //1 上班 2 下班 
            'time'            => 'Required|Str',                                      //补卡时间
            'staff_info_id'   => 'Required|IntGt:0',                                  //补卡工号
            'reason'          => 'StrLenGeLe:0,500|>>>:reason param error',  //补卡备注
        ]);
        $service = new AttendanceToolService();
        $service->toolMakeUp($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 补卡
     * @Permission(action='tool_make_up')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function makeUpAction()
    {
        $params = $this->request->get();

        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        //校验
        Validation::validate($params, [
            'attendance_date' => 'Required|Str',               //出勤时间
            'attendance_type' => 'Required|int',               //补卡类型
            'time'            => 'Required|Str',               //补卡时间
            'staff_info_id'   => 'Required|IntGt:0',           //补卡工号
        ]);

        $send_params = [
            'edit_type'       => $params['edit_type'],
            'staff_info_id'   => $params['staff_info_id'],
            'attendance_date' => $params['attendance_date'],
            'attendance_type' => $params['attendance_type'],
            'time'            => $params['time'],
            'audit_reason'    => $params['reason'] ?? '',
            'reason'          => $params['reason'] ?? '',
            'operator'        => $params['operator'],
            'operator_name'   => $params['operator_name'],
        ];

        if (!empty($params['imgpath'])) {
            $send_params['image_path'] = array_column($params['imgpath'], 'object_url');
        }

        $service     = new AttendanceToolService();
        $service->addAttendance($send_params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }


    /**
     * 修改打卡记录
     * @Token
     * @Permission(action='tool_modify_punch_record')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws Exception
     */
    public function modifyPunchRecordAction()
    {
        $params = $this->request->get();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        Validation::validate($params, [
            'staff_info_id' => 'Required|Str',               //补卡时间
        ]);

        $service                 = new AttendanceToolService();
        if (false === $service->checkOperatorPermission($params)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['permission_denied'], []);
        }

        $service             = reBuildCountryInstance(new AttendanceToolService());
        $params['user_info'] = $this->user;
        $service->tool_save($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }


    /**
     * 请假
     * @Permission(action='tool_add_leave')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addLeaveAction()
    {
        $params                  = $this->request->get();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];

        Validation::validate($params, [
            'leave_start_time'  => 'Required|Str',             //请假开始时间
            'leave_start_type'  => 'Required|int',             //上午还是下午
            'leave_end_time'    => 'Required|Str',             //请假开始时间
            'leave_end_type'    => 'Required|int',             //上午还是下午
            'reason'            => 'Required|Str',             //请假原因
            'leave_type'        => 'Required|int',             //请假类型
            'staff_info_id'     => 'Required|int',             //请假类型
            'paid_leave_reason' => 'IfIntEq:leave_type,2|IntGeLe:1,6|>>>:' . $this->t->_('1031'),
        ]);

        if (!empty($params['imgpath']) && $params['leave_type'] !=  StaffAuditModel::LEAVE_TYPE_38) {
            $params['image_path'] = array_column($params['imgpath'], 'object_url');
        }
        //泰国病假 图片结构
        if($params['leave_type'] ==  StaffAuditModel::LEAVE_TYPE_38){
            $params['image_path']['certificate'] = empty($params['certificate']) ? [] : array_column($params['certificate'], 'object_url');
            $params['image_path']['anticipate']  = empty($params['anticipate']) ? [] : array_column($params['anticipate'], 'object_url');
            $params['image_path']['receipt']     = empty($params['receipt']) ? [] : array_column($params['receipt'], 'object_url');
            $params['image_path']['other']       = empty($params['other']) ? [] : array_column($params['other'], 'object_url');
        }
        $service = new AttendanceToolService();
        $service->askForLeave($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 请假撤销
     * @Permission(action='tool_cancel_leave')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelLeaveAction()
    {
        $code                    = ErrCode::SUCCESS;
        $message                 = 'success';
        $params                  = $this->request->get();
        $params['operator_name'] = $this->user['name'];
        $params['operator']      = $this->user['id'];
        Validation::validate($params, [
            'audit_id' => 'Required|int', //请假ID
        ]);
        try {
            $service = new AttendanceToolService();
            $service->undoForLeave($params);
        } catch (BusinessException $be) {
            $code    = $be->getCode() ?? ErrCode::VALIDATE_ERROR;
            $message = $be->getMessage();
        }
        return $this->returnJson($code, $message);
    }


    /**
     * 废弃
     * @Token
     * @Permission(action='attendance-edit')
     */
    public function editDetailAjaxAction()
    {
        return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['permission_denied'], []);
    }

    /**
     * 申请出差
     * @Token
     * @Permission(action='add_trip')
     */
    public function addTripAction()
    {
        $paramIn = $this->request->get();
        $validations                       = [
            //出差原因 翻译key
            "is_stay"             => "Required|IntIn:1,0|>>>:is_stay error",
            //是否报销住宿
            "oneway_or_roundtrip" => "Required|IntIn:1,2|>>>:oneway_or_roundtrip error",
            //单程往返
            "departure_city"      => "Required|StrLenGeLe:1,50|>>>:departure_city error" ,
            //出发地城市 根据国内国外判断
            "destination_city"    => "Required|StrLenGeLe:1,50|>>>:destination_city error",
            //目的地城市
            "start_time"          => "Required|Date|>>>:start_time error",
            "end_time"            => "Required|Date|>>>:end_time error",
            "remark"              => "StrLenGeLe:0,500",
            //备注 非必填
            "business_trip_type"  => "Required|IntIn:1,2,3,4|>>>:出差类型不正确",
        ];
        $paramIn['traffic_tools']          = isset($paramIn['traffic_tools']) ? $paramIn['traffic_tools'] : 0;
        $validations['traffic_tools']      = "Required|IntIn:1,2,3,4|>>>:" . $this->t->_('7122') . 'traffic_tools';
        $validations['reason_application'] = "Required|StrLenGeLe:10,50|>>>:" . $this->t->_('7127');
        //如果有其他交通工具，输入名称
        if ($paramIn['traffic_tools'] == 4) {
            $validations['other_traffic_name'] = "Required|StrLenGeLe:1,20|>>>:" . $this->t->_('7124');
        }

        if ($paramIn['business_trip_type'] == BusinessTripModel::BTY_DOMESTIC) {
            //网点出差
            if ($paramIn['destination_type'] == BusinessTripEnums::DESTINATION_TYPE_STORE) {
                $validations['destination_store'] = "Required|StrLenGeLe:1,256|>>>:destination_store error";
            } else {//非网点出差 手动输入目的地
                $validations['destination_text'] = "Required|StrLenGeLe:1,100|>>>:destination_text error";
            }
            //出差原因
            $validations['reason_enum'] = "Required|StrLenGeLe:1,50|>>>:reason_enum error";
        }
        $tripServer = new BusinessTripService();
        //出差总类型 如果是境外出差 需要选择目的地国家
        if ($paramIn['business_trip_type'] == BusinessTripModel::BTY_FOREIGN) {
            $countryRegion                      = $tripServer->getEnumsList();
            $countryRegion = implode(",", array_column($countryRegion['destination_country'],'value'));
            $validations['destination_country'] = "Required|IntIn:{$countryRegion}|>>>:" . $this->t->_('destination_country_hint');
            //如果是目的地国家是其他 必填 国家名称
            if (isset($paramIn['destination_country']) && $paramIn['destination_country'] == HrStaffInfoModel::WORKING_COUNTRY_OTHER) {
                $validations['destination_country_name'] = 'Required|StrLenGeLe:1,55|>>>:' . $this->t->_('destination_country_name_hint');
            }
        }

        Validation::validate($paramIn, $validations);
        $paramIn['operator'] = $paramIn['operate_id'] = $this->user['id'];

        $server = new BusinessTripService();
        $res = $server->addTrip($paramIn);
        return $this->returnJson($res['code'], $res['message']);
    }

    public function getTripEnumsAction(){
        $server = new BusinessTripService();
        $data = $server->getEnumsList();
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 验证用户信息
     * @Token
     * @Permission(action='attendance-edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getUserInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['staff_info_id' => 'Required|int']);
        $server = new ToolService();
        $staffInfo = $server->checkStaffPermission($params);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $staffInfo);
    }

    /**
     * OT or 考勤列表
     * @Token
     * @Permission(action='attendance-edit')
     * @return Response|ResponseInterface
     */
    public function attendanceStaffListAction()
    {
        $staff_info_id   = $this->request->get('staff_info_id', 'trim');   //员工ID
        $attendance_time = $this->request->get('attendance_time', 'trim'); //出勤日期
        $type            = $this->request->get('type', 'trim');            //1：OT 2：打卡
        $ot_date         = $this->request->get('ot_date', 'trim');         // OT日期
        if (empty($staff_info_id)) {
            return $this->returnJson(ErrCode::SUCCESS, 'success', null);
        }
        $staff_info = StaffInfoModel::findFirst(["conditions" => "id = :id:", 'bind' => ['id' => $staff_info_id]]);
        if (empty($staff_info)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, '用户不存在');
        }
        $staff_info = $staff_info->toArray();

        $params['staff_info_id'] = $staff_info_id;
        $params['operator']      = $this->user['id'];
        $service = new AttendanceToolService();
        if (false === $service->checkOperatorPermission($params)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['permission_denied'], []);
        }

        $list = [];
        //OT列表
        if ($type == 1) {
            $send_params = [
                'staff_id' => $staff_info_id,
                'date_at' => $ot_date,
            ];
            $book = (new BllService())->getTypeBookOtList('getApprovalOtType');
            $book = array_column($book, 'msg', 'code');
            $result = (new AttendanceToolService())->attendanceStaffListOT($send_params);
            if ($result) {
                foreach ($result['data'] as $k => $v) {
                    $result['data'][$k]['type'] = intval($v['type']);
                    $result['data'][$k]['ot_type_text'] = $book[$v['type']] ?? '';

                    $result['data'][$k]['ot_type'] = $result['data'][$k]['type'];
                    $result['data'][$k]['type'] = 1;// type 1 ot  2 打卡 兼容前端
                    $result['data'][$k]['name'] = $this->convertStaffName($staff_info);

                    //新版 时长计算 数据库的duration 废弃不用了
                    $result['data'][$k]['duration'] = $v['duration'];
                }
                $list = $result['data'];
            }
        }
        //出勤列表
        if ($type == 2) {
            $send_params = [
                'staff_info_id'   => $staff_info_id,
                'attendance_date' => $attendance_time,
            ];
            $staff_info_by = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = ?1',
                'bind'       => [
                    1 => $staff_info_id,
                ],
            ]);
            if(empty($staff_info_by)){
                return $this->returnJson(ErrCode::SUCCESS, 'success', null);
            }
            $list        = (new AttendanceToolService())->attendanceStaffList($send_params);
            foreach ($list as $k => $v) {
                $list[$k]['started_at'] = empty($v['started_at']) ? '' : show_time_zone($v['started_at']);
                $list[$k]['end_at']     = empty($v['end_at']) ? '' : show_time_zone($v['end_at']);
                $list[$k]['name']       = $this->convertStaffName($staff_info);
            }
        }
        $search_result['recordsFiltered'] = intval($list); //过滤后的记录数（如果有接收到前台的过滤条件，则返回的是过滤后的记录数）
        $search_result['recordsTotal'] = intval($list); //即没有过滤的记录数（数据库里总共记录数）
        $search_result['DataList'] = $list;
        return $this->returnJson(ErrCode::SUCCESS, 'success', $search_result);
    }

    /**
     * 修改打卡
     * ToDo 废弃
     * 修改OT
     * @return Response|ResponseInterface
     */
    public function editBackyardAction()
    {
        return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['permission_denied'], []);
    }


    /**
     * 修改历史-列表
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function operationHistoryAction()
    {
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'int');
        $param['log_type']      = $this->request->get('log_type', 'int'); //1:oT 2:补卡 3：请假 4：请假撤销
        $param['start_time']    = $this->request->get('start_time', 'trim');
        $param['end_time']      = $this->request->get('end_time', 'trim');
        $param['page_num']      = $this->request->get('page', 'int', 1);
        $param['page_size']     = $this->request->get('size', 'int', 100);
        $param['operator']      = $this->user['id'];

        $server = new AttendanceToolService();
        $list = $server->history_list($param);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * @Permission(action='tool_modify_history')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function tripDetailAction()
    {
        $params['id'] = $this->request->get('id' , 'int');
        //校验
        Validation::validate($params, [
            'id' => 'Required|int' //记录ID
        ]);
        $tripInfo = BusinessTripModel::findFirst($params['id']);
        if(empty($tripInfo)){
            throw new ValidationException('no trip info');
        }
        $tripInfo = $tripInfo->toArray();
        //审批详情
        $auditParam['id_union']     = 'id_' . $params['id'];
        $auditParam['staff_id']     = $tripInfo['apply_user'];
        $auditParam['type']         = BusinessTripModel::$business_trip_type_approval_type[$tripInfo['business_trip_type']] ?? Enums\ApprovalEnums::APPROVAL_TYPE_BT;//出差
        $auditParam['locale']       = $this->locale;
        $auditParam['date_created'] = $tripInfo['created_at'] ?? '';

        //获取审批列表
        $return['audit_list_detail'] = [];
        $audit_detail = (new WorkflowService())->getAuditDetailV2($auditParam,$this->user);
        return $this->returnJson(ErrCode::SUCCESS , '' , $audit_detail);
    }


    /**
     * 修改历史-导出
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function operationHistoryExportAction()
    {
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'int');
        $param['log_type']      = $this->request->get('log_type', 'int'); //1:oT 2:补卡 3：请假 4：请假撤销
        $param['start_time']    = $this->request->get('start_time', 'trim');
        $param['end_time']      = $this->request->get('end_time', 'trim');
        $param['operator']      = $this->user['id'];
        $param['is_export']     = true;
        $param['lang']          = $this->locale;
        $param['file_name']     = 'Modification_history_' . date('YmdHis') . '.xlsx';
        $server                 = new AttendanceToolService();
        $result                 = $server->operationHistoryExport($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }


    /**
     * 请假撤销列表
     * @Token
     * @Permission(action='attendance-edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function leaveSearchAction()
    {

        $params = $this->request->get();
        //校验
        Validation::validate((array)$params, [
            'staff_id'   => 'Required|int',
            'type'       => 'Required|int',
            'start_time' => 'Required|Str',
            'end_time'   => 'Required|Str',
        ]);
        if(strtotime($params['start_time']) > strtotime($params['end_time'])){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, '无效的参数');
        }

        $service = new AttendanceToolService();
        $staff_info = $service->getStaffInfoHr($params['staff_id']);

        //根据创建时间 查询记录
        $list = $service->get_staff_leave($params['staff_id'], $params['type'], $params['start_time'], $params['end_time']);

        $params['staff_info_id'] = $params['staff_id'];
        $params['operator']      = $this->user['id'];
        $service = new AttendanceToolService();
        if (false === $service->checkOperatorPermission($params)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t['permission_denied'], []);
        }

        //格式化数据
        if (!empty($list)) {
            $res_leave_type = (new AttendanceToolService())->getApiDatassFromCache('by', '', 'leaveTypeBookAll', $this->locale, []);
            $leave_type     = empty($res_leave_type['data']) ? [] : array_column($res_leave_type['data'], 'msg', 'code');
            //审批状态文案
            $apply_key = array(
                1 => 'by_state_1',
                2 => 'by_state_2',
                3 => 'by_state_3',
                4 => 'by_state_4',
                5 => 'by_state_5',
            );

            $typeArr = Enums::$time_interval;
            if(isCountry('MY')){
                $typeArr = Enums::$time_half;
            }
            foreach ($list as &$li) {
                $li['name']       = $this->convertStaffName($staff_info);
                $li['staff_info'] = $li['staff_info_id'].'|'.$staff_info['name'];
                //病假特殊处理
                if($li['leave_type'] == StaffAuditModel::LEAVE_TYPE_3 && get_country_code() == 'TH'){
                    $li['leave_type'] = $leave_type[StaffAuditModel::LEAVE_TYPE_38];
                }else{
                    //请假类型
                    $li['leave_type'] = $leave_type[$li['leave_type']] ?? '';
                }

                //状态转换
                $li['status'] = $this->t[$apply_key[$li['status']]];
                //时间转换 为 日期
                $li['start_date']            = date('Y-m-d', strtotime($li['leave_start_time']));
                $li['end_date']              = date('Y-m-d', strtotime($li['leave_end_time']));

                $li['leave_start_type_name'] = isset($typeArr[$li['leave_start_type']]) ? $this->t[$typeArr[$li['leave_start_type']]] : '';
                $li['leave_end_type_name']   = isset($typeArr[$li['leave_end_type']]) ? $this->t[$typeArr[$li['leave_end_type']]] : '';
            }
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    private function convertStaffName($staff_info){
        if(empty($staff_info)){
            return '';
        }
        return $staff_info['id'] . '（' . $staff_info['name'] . '）';
    }

    /**
     * 所有班次列表 枚举 从 hr_shift取数
     * @Token
     */
    public function shiftListAction(){
        $service = new AttendanceToolService();
        $data = $service->get_shift_enum();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * @Token
     * 修改考情记录
     * 请假相关枚举
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getAboutLeaveEnumAction()
    {
        $params = $this->request->get();
        //校验
        Validation::validate((array)$params, [
            'staff_id' => 'Required|int',
        ]);
        $service                     = new AttendanceToolService();
        $data['leaveType']           = $service->dealLeaveTypeForAddLeave($params);
        $data['timeInterval']        = $service->timeInterval();
        $data['paidLeaveReason']     = $service->getPaidLeaveReason();
        $data['militarySettingDate'] = $service->getSettingDate();

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    //加班撤销列表 有权限和 可视范围

    /**
     * @Token
     * @Permission(action='cancel_ot')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function otCancelListAction(){
        $params = $this->request->get();
        //校验
        Validation::validate((array)$params, [
            'staff_info_id' => 'Required|int',
            'start_date' => 'Required|Str',
            'end_date'   => 'Required|Str',
        ]);
        $service = new OvertimeBllService();
        $params['operator']      = $this->user['id'];
        $data = $service->getCancelList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);

    }


    /**
     * @Token
     * @Permission(action='cancel_ot')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelOtAction(){
        $params = $this->request->get();
        //校验
        Validation::validate((array)$params, [
            'overtime_id' => 'Required|int',
        ]);
        $service = new OvertimeBllService();
        $params['user_info']      = $this->user;
        $data = $service->tool_cancel_ot($params);
        return $this->returnJson($data['code'], $data['msg'], $data['data']);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deleteAttendanceAction(){
        $params = $this->request->get();
        //校验
        Validation::validate((array)$params, [
            'id' => 'Required|int',
            'attendance_type' => 'Required|int',
        ]);
        $params['user_info'] = $this->user;
        $server = new AttendanceBllService();
        $server->deleteCard($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

}