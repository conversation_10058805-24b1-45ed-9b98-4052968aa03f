<?php

namespace app\controllers;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Validation\Validation;
use App\Models\backyard\AttendanceWhiteListModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AttendanceStatisticsReportService;
use App\Services\AttendanceWhiteListService;
use App\Services\ExcelService;
use App\Services\SyncStaffService;

class AttendanceWhiteListController extends BaseController
{

    /**
     * @description:查询用户信息
     * @author: L.J
     * @time: 2023/1/30 11:18
     * @Token
     */
    public function getStaffInfoAction()
    {
        $params     = $this->request->get();
        $validation = [
            'staff_info_id' => 'Required|Int|>>>:'.$this->t->_('white_list_staff_info_id_error'),
        ];
        Validation::validate((array)$params, $validation);
        $AttendanceWhiteListService = new AttendanceWhiteListService();
        $data                        = $AttendanceWhiteListService->getStaffInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * @description:当地发薪资不用打卡名单
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/1/30 11:04
     * @Token
     * @Permission(action='admin.attendance_retry.salary_white_list_info')
     */
    public function getPaidLocallyListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);

        // 公共验证字段
        $validation = [
            'staff_info_id'  => 'Int|>>>:[staff_info_id] params error',
            'staff_state'    => 'Arr|>>>:[staff_state] params error',
            'state'          => 'Int|>>>:[state] params error',
            'start_at_begin' => 'Date|>>>:[start_at_begin] params error',
            'start_at_end'   => 'Date|>>>:[start_at_end] params error',
            'end_at_begin'   => 'Date|>>>:[end_at_begin] params error',
            'end_at_end'     => 'Date|>>>:[end_at_end] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id']          = $this->user['id'];
        $params['type']              = AttendanceWhiteListModel::TYPE_PAID_LOCALLY;
        $AttendanceWhiteListService = new AttendanceWhiteListService();
        $data                        = $AttendanceWhiteListService->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @description:导出
     * @param null
     * @author: L.J
     * @time: 2023/1/30 17:59
     * @Permission(action='admin.attendance_retry.salary_white_list_info')
     */
    public function exportPaidLocallyListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);

        // 公共验证字段
        $validation = [
            'staff_info_id'  => 'Int|>>>:[staff_info_id] params error',
            'staff_state'    => 'Arr|>>>:[staff_state] params error',
            'state'          => 'Int|>>>:[state] params error',
            'start_at_begin' => 'Date|>>>:[start_at_begin] params error',
            'start_at_end'   => 'Date|>>>:[start_at_end] params error',
            'end_at_begin'   => 'Date|>>>:[end_at_begin] params error',
            'end_at_end'     => 'Date|>>>:[end_at_end] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        $params['file_name'] = 'Salary_White_List_'.date('YmdHis').'.xlsx';
        $params['staffInfo'] = $this->user;
        $params['type']      = AttendanceWhiteListModel::TYPE_PAID_LOCALLY;
        $params['is_export'] = 1;
        $action_name         = 'attendance_white_list'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'export';

        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], []);
    }


    /**
     * @description:当地发薪资不用打卡名单-保存
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/1/30 11:04
     * @Token
     * @Permission(action='admin.attendance_retry.salary_white_list_save')
     */
    public function addPaidLocallyAction()
    {
        $params = $this->request->get();

        // 公共验证字段
        $validation = [
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error',
            'start_at'      => 'Required|Date|>>>:[start_at] params error',
        ];

        Validation::validate((array)$params, $validation);
        $params['staff_id'] = $this->user['id'];
        $params['type']     = AttendanceWhiteListModel::TYPE_PAID_LOCALLY;

        // 加锁处理
        $data     = (new AttendanceWhiteListService())->addUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @description:当地发薪资不用打卡名单-移除
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/1/30 11:04
     * @Token
     * @Permission(action='admin.attendance_retry.salary_white_list_save')
     */
    public function editPaidLocallyAction()
    {
        $params = $this->request->get();

        // 公共验证字段
        $validation = [
            'id'     => 'Required|Int|>>>:[id] params error',
            'start_at' => 'Required|Date|>>>:[start_at] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['type']     = AttendanceWhiteListModel::TYPE_PAID_LOCALLY;

        $data     = (new AttendanceWhiteListService())->edit($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }



    /**
     * @description:不在当地发薪资不用打卡名单
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/1/30 11:04
     * @Token
     * @Permission(action='admin.attendance_retry.staff_info_turn_positive_info')
     */
    public function getNotPaidLocallyListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);

        // 公共验证字段
        $validation = [
            'staff_info_id'  => 'Int|>>>:[staff_info_id] params error',
            'staff_state'    => 'Arr|>>>:[staff_state] params error',
            'state'          => 'Int|>>>:[state] params error',
            'start_at_begin' => 'Date|>>>:[start_at_begin] params error',
            'start_at_end'   => 'Date|>>>:[start_at_end] params error',
            'end_at_begin'   => 'Date|>>>:[end_at_begin] params error',
            'end_at_end'     => 'Date|>>>:[end_at_end] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id']          = $this->user['id'];
        $params['type']              = AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY;
        $AttendanceWhiteListService = new AttendanceWhiteListService();
        $data                        = $AttendanceWhiteListService->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * @description:当地发薪资不用打卡名单-保存
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/1/30 11:04
     * @Token
     * @Permission(action='admin.attendance_retry.staff_info_turn_positive_save')
     */
    public function addNotPaidLocallyListAction()
    {
        $params = $this->request->get();

        // 公共验证字段
        $validation = [
            'staff_info_id' => 'Required|Int|>>>:[staff_info_id] params error',
            'start_at'      => 'Required|Date|>>>:[start_at] params error',
        ];

        Validation::validate((array)$params, $validation);
        $params['staff_id'] = $this->user['id'];
        $params['type']     = AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY;

        // 加锁处理
        $data     = (new AttendanceWhiteListService())->addUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @description:不在当地发薪资不用打卡名单-移除
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/1/30 11:04
     * @Token
     * @Permission(action='admin.attendance_retry.staff_info_turn_positive_save')
     */
    public function editNotPaidLocallyListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);

        // 公共验证字段
        $validation = [
            'id'     => 'Required|Int|>>>:[id] params error',
            'start_at' => 'Required|Date|>>>:[start_at] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id'] = $this->user['id'];
        $params['type']     = AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY;

        // 加锁处理
        $data     = (new AttendanceWhiteListService())->edit($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * @description:导出
     * @param null
     * @author: L.J
     * @time: 2023/1/30 17:59
     * @Permission(action='admin.attendance_retry.staff_info_turn_positive_info')
     */
    public function exportNotPaidLocallyListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);

        // 公共验证字段
        $validation = [
            'staff_info_id'  => 'Int|>>>:[staff_info_id] params error',
            'staff_state'    => 'Arr|>>>:[staff_state] params error',
            'state'          => 'Int|>>>:[state] params error',
            'start_at_begin' => 'Date|>>>:[start_at_begin] params error',
            'start_at_end'   => 'Date|>>>:[start_at_end] params error',
            'end_at_begin'   => 'Date|>>>:[end_at_begin] params error',
            'end_at_end'     => 'Date|>>>:[end_at_end] params error',
        ];

        Validation::validate((array)$params, $validation);

        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        $params['file_name'] = 'Attendance_White_List_'.date('YmdHis').'.xlsx';
        $params['staffInfo'] = $this->user;
        $params['is_export'] = 1;
        $params['type']      = AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY;
        $action_name         = 'attendance_white_list'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'export';

        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], []);
    }


}
