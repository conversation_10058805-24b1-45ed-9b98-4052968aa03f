<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AttendanceConfigService;
use App\Services\BllService;

/**
 * 考勤配置相关
 */
class AttendanceconfigController extends BaseController
{

    /**
     * 筛选项
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getSysInfoAction()
    {
        $res = (new AttendanceConfigService())->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }

    /**
     * 获取部门职位外勤权限列表
     * @Token
     * @Permission(action='attendanceconfig.department_config')
     * @throws ValidationException
     */
    public function getFieldPersonnelDepartmentListAction()
    {
        Validation::validate((array)$this->request->get(), ['page_num' => 'IntGe:1', 'page_size' => 'IntGe:10']);
        $res = (new AttendanceConfigService())->getFieldPersonnelDepartmentList($this->request->get());
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }

    /**
     * 删除部门职位外勤权限
     * @Token
     * @Permission(action='attendanceconfig.department_config.edit')
     * @throws ValidationException
     */
    public function delFieldPersonnelDepartmentAction()
    {
        $this->getDI()->get('logger')->info([
            'method'   => 'delFieldPersonnelDepartment',
            'params'   => $this->request->get(),
            'operator' => $this->user['id'],
        ]);
        Validation::validate((array)$this->request->get(), ['id' => 'Required|IntGe:1']);
        $res = (new AttendanceConfigService())->deleteFieldPersonnelDepartment($this->request->get('id'));
        return $this->returnJson(ErrCode::SUCCESS, $res ? 'success' : 'fail', $res);
    }

    /**
     * 添加部门职位外勤权限
     * @Token
     * @Permission(action='attendanceconfig.department_config.edit')
     * @throws ValidationException
     * @throws BusinessException
     */
    public function createFieldPersonnelDepartmentAction()
    {
        $this->getDI()->get('logger')->info([
            'method'   => 'createFieldPersonnelDepartment',
            'params'   => $this->request->get(),
            'operator' => $this->user['id'],
        ]);
        $param = $this->request->get();
        Validation::validate((array)$param, [
            'is_force_update'  => 'IntIn:0,1',
            'department_id'    => 'IntGe:1',
            'is_all_job_title' => 'Required|IntIn:0,1',
            'job_title_ids'    => 'Arr',
        ]);
        $param['operator'] = $this->user['id'];
        $res               = (new AttendanceConfigService())->createFieldPersonnelDepartment($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }

    /**
     * 获取工号外勤权限列表
     * @Token
     * @Permission(action='attendanceconfig.staff_config')
     * @throws ValidationException
     */
    public function getFieldPersonnelStaffListAction()
    {
        Validation::validate((array)$this->request->get(), ['page_num' => 'IntGe:1', 'page_size' => 'IntGe:10']);
        $res = (new AttendanceConfigService())->getFieldPersonnelStaffList($this->request->get());
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }

    /**
     * 删除员工外勤权限
     * @Token
     * @Permission(action='attendanceconfig.staff_config.edit')
     * @throws ValidationException
     */
    public function delFieldPersonnelStaffAction()
    {
        $this->getDI()->get('logger')->info([
            'method'   => 'delFieldPersonnelStaff',
            'params'   => $this->request->get(),
            'operator' => $this->user['id'],
        ]);
        Validation::validate((array)$this->request->get(), ['ids' => 'Required|Str']);
        $res = (new AttendanceConfigService())->deleteFieldPersonnelStaff($this->request->get('ids'));
        return $this->returnJson(ErrCode::SUCCESS, $res ? 'success' : 'fail', $res);
    }

    /**
     * 添加员工外勤权限
     * @Token
     * @Permission(action='attendanceconfig.staff_config.edit')
     * @throws ValidationException
     * @throws BusinessException
     */
    public function createFieldPersonnelStaffAction()
    {
        $this->getDI()->get('logger')->info([
            'method'   => 'createFieldPersonnelStaff',
            'params'   => $this->request->get(),
            'operator' => $this->user['id'],
        ]);
        $param = $this->request->get();
        Validation::validate((array)$param, ['staff_info_ids' => 'Required|Str']);
        $param['staff_info_ids'] = str_replace('，', ',', $param['staff_info_ids']);
        $param['staff_info_ids'] = str_replace(' ', '', $param['staff_info_ids']);
        $staffArr                = explode(',', $param['staff_info_ids']);
        foreach ($staffArr as $v) {
            if ($v <= 0) {
                throw new ValidationException('staff_info_id error');
            }
        }
        $res = (new AttendanceConfigService())->createFieldPersonnelStaff($staffArr, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, $res ? 'success' : 'fail', $res);
    }


    /**
     * 获取工号外勤权限列表-导出
     * @Token
     * @Permission(action='attendanceconfig.staff_config.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getFieldPersonnelStaffExportAction()
    {
        $params                = $this->request->get();
        $params['is_download'] = 1;
        $params['file_name']   = 'get_field_personnel_staff_export';
        $params['lang']        = $this->locale;
        $params['staff_id']    = $this->user['id'];
        // 加锁处理
        $lock_key   = 'get_field_personnel_staff_export_'.$this->user['id'];
        $get_result = $this->atomicLock(function () use ($params) {
            return (new AttendanceConfigService())->getFieldPersonnelStaffListExport($params);
        }, $lock_key, 60);
        if ($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], $get_result['data']);
    }

    /**
     * 获取部门外勤权限列表-导出
     * @Token
     * @Permission(action='attendanceconfig.department_config.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getFieldPersonnelDepartmentExportAction()
    {
        $params                = $this->request->get();
        $params['is_download'] = 1;
        $params['file_name']   = 'get_field_personnel_department_export';
        $params['lang']        = $this->locale;
        $params['staff_id']    = $this->user['id'];
        // 加锁处理
        $lock_key   = 'get_field_personnel_department_export_'.$this->user['id'];
        $get_result = $this->atomicLock(function () use ($params) {
            return (new AttendanceConfigService())->getFieldPersonnelDepartmentListExport($params);
        }, $lock_key, 60);
        if ($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], $get_result['data']);
    }



    //------------------------------ 考勤临时坐标配置 start
    /**
     * 考勤临时坐标 配置列表
     * @Token
     * @Permission(action='coordinate-config-list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateListAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'store_id' => 'Str',
                'staff_text' => 'Str'
            ]
        );
        $server = new AttendanceConfigService();
        $data = $server->getCoordinateList($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * 考勤临时坐标 添加 验证 abs 不能大于 90  另外一个不能大于 180
     * @Token
     * @Permission(action='coordinate-config-edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateAddAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'store_id' => 'Required|Str',
                'lat' => 'Required|Str',//维度 没有翻译 下面验证
                'lng' => 'Required|Str',//经度
                'staff' => 'Required|Arr',
                'remark' => 'Str',
            ]
        );

        $flag = checkCoordinateLat($params['lat']);
        //验证 维度 -90 到 90
        if($flag){
            throw new ValidationException($this->t->_('lat_notice'));
        }
        $flag = checkCoordinateLng($params['lng']);
        //验证 经度 -180 180
        if($flag){
            throw new ValidationException($this->t->_('lng_notice'));
        }

        $params['user'] = $this->user;
        $server = new AttendanceConfigService();
        $flag = $server->coordinateAddUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $flag);
    }

    /**
     * 编辑 回显接口
     * @Token
     * @Permission(action='coordinate-config-list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateDetailAction(){

        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'id' => 'Required|Int',
            ]
        );
        $server = new AttendanceConfigService();
        $data = $server->coordinateDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 考勤临时坐标 修改
     * @Token
     * @Permission(action='coordinate-config-edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateEditAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'id' => 'Required|Int',
                'store_id' => 'Required|Str',
                'lat' => 'Required|Str',//维度 没有翻译 下面验证
                'lng' => 'Required|Str',//经度
                'staff' => 'Required|Arr',
                'remark' => 'Str',
            ]
        );

        $flag = checkCoordinateLat($params['lat']);
        //验证 维度 -90 到 90
        if($flag){
            throw new ValidationException($this->t->_('lat_notice'));
        }
        $flag = checkCoordinateLng($params['lng']);
        //验证 经度 -180 180
        if($flag){
            throw new ValidationException($this->t->_('lng_notice'));
        }
        $params['user'] = $this->user;
        $server = new AttendanceConfigService();
        $flag = $server->coordinateEditUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $flag);
    }


    /**
     * 考勤临时坐标 删除
     * @Token
     * @Permission(action='coordinate-config-edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateDelAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'id' => 'Required|Int',
            ]
        );
        $params['user'] = $this->user;
        $server = new AttendanceConfigService();
        $data = $server->coordinateDeleteUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }



    /**
     * 考勤临时坐标导出
     * @Token
     * @Permission(action='coordinate-config-list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateExportAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'store_id' => 'Str',
                'staff' => 'Arr',
            ]
        );

        //获取文件名
        $file_name   = 'coordinateConfig';
        $action_name = 'Attendance_config'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'tmpCoordinateExport';

        $params['lang'] = $this->locale;
        $params['current_uid'] = $this->user['id'];

        [$msg , $code] = (new BllService())->exportToDownloadCenter($this->user['id'], $file_name, $action_name, $params);

        if($code == ErrCode::SUCCESS){
            return $this->returnJson(ErrCode::SUCCESS , $msg , []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR , $msg , []);

    }


    /**
     * 考勤临时坐标 操作记录查看
     * @Permission(action='coordinate-config-log')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function coordinateLogListAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'start_date' => 'Required|Date',
                'end_date' => 'Required|Date',
                'store_id' => 'Str',
                'operate_id' => 'Str',
            ]
        );

        $server = new AttendanceConfigService();
        $data = $server->coordinateLogList($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }



    //下拉菜单 枚举 网点名称和 id 只有nw 部门的
    public function configStoreAction(){
        $server = new AttendanceConfigService();
        if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
            $data = $server->storeSelect();
        }else{
            $data = $server->storeSelectFromCache();
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    //根据输入网点 获取里面的 员工
    public function configStoreStaffAction(){
        $params = $this->request->get();
        Validation::validate((array)$params,
            [
                'store_id' => 'Required|Str',
            ]
        );
        $server = new AttendanceConfigService();
        if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
            $data = $server->storeStaffSelect($params);
        }else{
            $data = $server->storeStaffSelectFromCache($params);
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);

    }

}