<?php

namespace app\controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\DailyFrontlineSalaryRuleConfigModel;
use app\models\backyard\FrontlineSalaryCityConfigLogModel;
use app\models\backyard\FrontlineSalaryCityConfigModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysCityModel;
use App\Services\DailyFrontlineSalaryConfigService;
use App\Services\FrontlineSalaryConfigService;
use App\Services\SettingEnvService;

class DailyFrontlineSalaryConfigController extends BaseController
{
    /**
     * 日薪薪资规则配置列表
     * @Token
     * @Permission(action='frontline_daily_salary_config.rule')
     * @throws \Exception
     */
    public function salaryRuleConfigListAction()
    {
        $salaryItem = $this->request->get('salary_item');
        $jotTitle   = $this->request->get('job_title');
        $pageSize   = $this->request->get('page_size', 'trim', 20);
        $pageNum    = $this->request->get('page_num', 'trim', 1);
        if ($salaryItem && array_diff($salaryItem, DailyFrontlineSalaryRuleConfigModel::$salaryItemTypeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Salary Item params error');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $data    = $service->salaryRuleConfigList([
            'salary_item_type' => $salaryItem,
            'job_title'        => $jotTitle,
            'page_size'        => empty($pageSize) ? 20 : $pageSize,
            'page_num'         => empty($pageNum) ? 1 : $pageNum,
        ]);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 日薪薪资规则配置导出
     * @Token
     * @Permission(action='frontline_daily_salary_config.rule')
     * @throws \Exception
     */
    public function salaryRuleConfigExportAction()
    {
        $salaryItem = $this->request->get('salary_item');
        $jotTitle   = $this->request->get('job_title');
        $state      = $this->request->get('state');
        if ($salaryItem && array_diff($salaryItem, DailyFrontlineSalaryRuleConfigModel::$salaryItemTypeMap)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Salary Item params error');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $list    = $service->salaryRuleConfigList([
            'salary_item_type' => $salaryItem,
            'job_title'        => $jotTitle,
            'export'           => true,
        ]);
        $url     = $service->getSalaryRuleConfigExportUrl($list);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 日薪薪资规则配置批量导入模板
     * @Token
     * @Permission(action='frontline_daily_salary_config.rule')
     */
    public function salaryRuleConfigBatchImportTemplateAction()
    {
        $code = 'upload_dfl_salary_tpl';
        $url  = (new SettingEnvService)->getSetVal($code);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 日薪薪资规则配置批量导入(实时)
     * @Token
     * @Permission(action='frontline_daily_salary_config.rule')
     * @throws \Exception
     */
    public function salaryRuleConfigBatchImportAction()
    {
        $operateId = $this->user['id'] ?? 0;
        $fileUrl   = $this->request->get('path');
        if (empty($fileUrl)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found file url');
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl);
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'read file error');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $result  = $service->salaryRuleConfigBatchImport($tmpFilePath, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'Upload success', $result);
    }

    /**
     * 薪资规则配置删除
     * @Token
     * @Permission(action='frontline_daily_salary_config.rule')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function salaryRuleConfigDeleteAction()
    {
        $id        = $this->request->get('id');
        $service   = new DailyFrontlineSalaryConfigService();
        $operateId = $this->user['id'] ?? 0;
        $service->salaryRuleConfigDelete($id, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 府日薪标准配置列表（无分页）
     * @Token
     * @Permission(action='frontline_daily_salary_config.province')
     */
    public function governmentDailyConfigListAction()
    {
        $provinceName = $this->request->get('province_name');
        $service      = new DailyFrontlineSalaryConfigService();
        $data         = $service->governmentDailyConfigList($provinceName);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 府日薪标准配置导出
     * @Token
     * @Permission(action='frontline_daily_salary_config.province')
     */
    public function governmentDailyConfigExportAction()
    {
        $params = $this->request->get();
        if (empty($params['type']) || !in_array($params['type'], ['new', 'specific'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'type error');
        }
        if ($params['type'] == 'specific' && empty($params['effective_date'])) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'date is not empty');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $url     = $service->governmentDailyConfigExport($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 府日薪标准配置批量导入(实时)
     * @Token
     * @Permission(action='frontline_daily_salary_config.province')
     * @throws \Exception
     */
    public function governmentDailyConfigBatchImportAction()
    {
        $operateId = $this->user['id'] ?? 0;
        $fileUrl   = $this->request->get('path');
        if (empty($fileUrl)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'not found file url');
        }
        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl);
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'read file error');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $result  = $service->governmentDailyConfigBatchImport($tmpFilePath, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'Upload success', $result);
    }

    /**
     * 府日薪标准配置批量导入模板
     * @Token
     * @Permission(action='frontline_daily_salary_config.province')
     */
    public function governmentDailyConfigBatchImportTemplateAction()
    {
        $url = (new SettingEnvService)->getSetVal('upload_province_daily_config_tpl');
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['url' => $url]);
    }

    /**
     * 府日薪标准配置log
     * @Token
     * @Permission(action='frontline_daily_salary_config.province')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function governmentDailyConfigLogAction()
    {
        $provinceCode = $this->request->get('province_code');
        if (empty($provinceCode)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'province code error');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $list    = $service->governmentDailyConfigLog($provinceCode);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * 撤销府日薪标准配置
     * @Token
     * @Permission(action='frontline_daily_salary_config.province')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function governmentDailyWithdrawAction()
    {
        $id        = $this->request->get('id');
        $operateId = $this->user['id'] ?? 0;
        if (empty($id)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'log id error');
        }
        $service = new DailyFrontlineSalaryConfigService();
        $service->governmentDailyWithdraw($id, $operateId);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-列表（无分页）
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     */
    public function citySalaryConfigListAction()
    {
        $service = new DailyFrontlineSalaryConfigService();
        $data    = $service->getCitySalaryConfigList();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 特殊县薪资配置-新增
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     * @throws ValidationException|BusinessException
     */
    public function specialCitySalaryConfigAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'city_code'      => 'Required|StrLen:6|>>>: city code error',
            'daily_wage'     => 'Required|IntGeLe:0,99999|>>>: salary error',
            'effective_date' => 'Required|Date|>>>: effective date error',
        ]);
        $service              = new DailyFrontlineSalaryConfigService();
        $params['operate_id'] = $this->user['id'];
        $service->specialCitySalaryConfigAdd($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-导出
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     * @throws BusinessException
     */
    public function specialCitySalaryConfigExportAction()
    {
        $service = new DailyFrontlineSalaryConfigService();
        $data    = $service->specialCitySalaryConfigExport();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 特殊县薪资配置-修改
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     * @throws BusinessException|ValidationException
     */
    public function specialCitySalaryConfigEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'city_code'      => 'Required|StrLen:6|>>>: city code error',
            'daily_wage'     => 'Required|IntGeLe:0,99999|>>>: salary error',
            'effective_date' => 'Required|Date|>>>: effective date error',
            'edit_reason'    => 'Required|StrLenGeLe:0,200|>>>: edit reason error',
        ]);
        $service              = new DailyFrontlineSalaryConfigService();
        $params['operate_id'] = $this->user['id'];
        $service->specialCitySalaryConfigEdit($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-启用/禁用
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     * @throws BusinessException
     * @throws ValidationException
     */
    public function specialCitySalaryConfigStateEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id'    => 'Required|Int|>>>: id error',
            'state' => 'Required|IntIn:1,2|>>>: state error',
        ]);
        $params['operate_id'] = $this->user['id'];
        $service              = new DailyFrontlineSalaryConfigService();
        $service->specialCitySalaryConfigStateEdit($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * 特殊县薪资配置-变更记录
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     */
    public function specialCitySalaryConfigLogsAction()
    {
        $cityCode = $this->request->get('city_code');
        $service  = new DailyFrontlineSalaryConfigService();
        $data     = $service->specialCitySalaryConfigLogs($cityCode);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 特殊县薪资配置-县名称搜索
     * @permission(action='frontline_daily_salary_config.province')
     * @Token
     */
    public function specialCitySalaryConfigNameSearchAction()
    {
        $cityCode = $this->request->get('city_code');
        $service  = new FrontlineSalaryConfigService();
        $data     = $service->specialCitySalaryConfigNameSearch($cityCode);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
}