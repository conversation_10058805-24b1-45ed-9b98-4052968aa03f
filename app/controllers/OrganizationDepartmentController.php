<?php

namespace App\Controllers;

use App\Library\Validation\Validation;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use Exception;
use App\Services\OrganizationDepartmentService;

class OrganizationDepartmentController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 大区列表
     * @Token
     */
    public function regionListAction()
    {
        $params     = $this->request->get();

        $validation = [];
        if (!empty($params['region_id'])) {
            $validation['region_id'] = 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'region_id']);
        }

        if (!empty($params['region_name'])) {
            $validation['region_name'] = 'Required|StrLenGeLe:1,100|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'region_id']);
        }

        Validation::validate($params, $validation);

        $data = (new OrganizationDepartmentService())->regionList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 大区更新
     * @Token
     */
    public function regionUpdateAction()
    {
        $params = $this->request->get();

        $validation = [
            'id'            => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'id']),
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'department_id']),
        ];

        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->regionUpdate($params, $this->user);

        if (!$r) {
            throw new Exception('regionUpdate Error');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }



    /**
     * 大区关联关系解除
     * @Token
     */
    public function regionRemoveAction()
    {
        $params = $this->request->get();

        $validation = [
            'id'     => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'id']),
        ];

        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->regionRemove($params,$this->user);

        if (!$r) {
            throw new Exception('regionRemove Error');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 片区列表
     * @Token
     */
    public function pieceListAction()
    {
        $params     = $this->request->get();

        $validation = [];
        if (!empty($params['piece_id'])) {
            $validation['piece_id'] = 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'piece_id']);
        }

        if (!empty($params['piece_name'])) {
            $validation['piece_name'] = 'Required|StrLenGeLe:1,100|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'piece_name']);
        }

        $data = (new OrganizationDepartmentService())->pieceList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 片区更新
     * @Token
     */
    public function pieceUpdateAction()
    {
        $params = $this->request->get();

        $validation = [
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'department_id']),
            'region_id'     => 'Required|StrLenGeLe:0,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'region_id']),
            'id'            => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'id'])
        ];

        Validation::validate($params, $validation);

        $r = (new OrganizationDepartmentService())->pieceUpdate($params, $this->user);

        if (!$r) {
            throw new Exception('pieceUpdate Error');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 片区解除
     * @Token
     */
    public function pieceRemoveAction()
    {
        $params = $this->request->get();

        $validation = [
            'id'     => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'id']),
        ];

        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->pieceRemove($params , $this->user);

        if (!$r) {
            throw new Exception('pieceRemove Error');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 网点更新
     * @Token
     */
    public function storeUpdateAction()
    {
        $params = $this->request->get();

        $validation = [
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'department_id']),
            'region_id'     => 'Required|StrLenGeLe:0,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'region_id']),
            'piece_id'      => 'Required|StrLenGeLe:0,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'piece_id']),
            'id'            => 'Required|StrLenGeLe:1,20|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'id'])
        ];

        Validation::validate($params, $validation);

        $r = (new OrganizationDepartmentService())->storeUpdate($params, $this->user);

        if (!$r) {
            throw new Exception('storeUpdate Error');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 网点列表
     * @Token
     */
    public function storeListAction()
    {
        $params     = $this->request->get();

        $validation = [];
        if (!empty($params['store_id'])) {
            $validation['store_id'] = 'Required|StrLenGeLe:1,20|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'store_id']);
        }

        if (!empty($params['store_name'])) {
            $validation['store_name'] = 'Required|StrLenGeLe:1,100|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'store_name']);
        }

        $data = (new OrganizationDepartmentService())->storeList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 网点解除
     * @Token
     */
    public function storeRemoveAction()
    {
        $params = $this->request->get();

        $validation = [
            'id'     => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'id']),
        ];

        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->storeRemove($params , $this->user);

        if (!$r) {
            throw new Exception('storeRemove Error');
        }

        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 根据部门查询大区集合
     * @Token
     */
    public function selectRegionListAction()
    {
        $params['department_id'] = $this->request->get('department_id','trim');

        $validation = [
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'department_id'])
        ];

        Validation::validate($params, $validation);

        $data = (new OrganizationDepartmentService())->selectRegionList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 根据大区查询片区集合
     * @Token
     */
    public function selectPieceListAction()
    {
        $params['region_id']     = $this->request->get('region_id', 'trim');
        $params['department_id'] = $this->request->get('department_id', 'trim');

        $validation = [
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'department_id']),
            'region_id'     => 'Required|StrLenGeLe:0,10|>>>:'.$this->t->_('input_param_nonstandard', ['field' => 'region_id'])
        ];

        Validation::validate($params, $validation);

        $data = (new OrganizationDepartmentService())->selectPieceList($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 加盟商大区列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function franchiseeRegionListAction()
    {
        $params     = $this->request->get();
        $validation = [
            'region_id'   => 'StrLenGeLe:0,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'region_id']),
            'region_name' => 'StrLenGeLe:0,100|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'region_id']),
        ];
        Validation::validate($params, $validation);

        $data = (new OrganizationDepartmentService())->franchiseeRegionList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 加盟商大区更新
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function franchiseeRegionUpdateAction()
    {
        $params     = $this->request->get();
        $validation = [
            'id' => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'id']),
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'department_id']),
        ];

        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->franchiseeRegionUpdate($params, $this->user);

        if (!$r) {
            throw new Exception('regionUpdate Error');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 加盟商大区解除
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function franchiseeRegionRemoveAction()
    {
        $params     = $this->request->get();
        $validation = [
            'id' => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'id']),
        ];

        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->franchiseeRegionRemove($params, $this->user);

        if (!$r) {
            throw new Exception('regionRemove Error');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 加盟商片区列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function franchiseePieceListAction()
    {
        $params     = $this->request->get();
        $validation = [
            'piece_id'   => 'StrLenGeLe:0,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'piece_id']),
            'piece_name' => 'StrLenGeLe:0,100|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'piece_name']),
        ];
        Validation::validate($params, $validation);

        $data = (new OrganizationDepartmentService())->franchiseePieceList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 加盟商片区更新
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function franchiseePieceUpdateAction()
    {
        $params     = $this->request->get();
        $validation = [
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'department_id']),
            'region_id'     => 'Required|StrLenGeLe:0,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'region_id']),
            'id'            => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'id']),
        ];
        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->franchiseePieceUpdate($params, $this->user);
        if (!$r) {
            throw new Exception('pieceUpdate Error');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 加盟商片区解除
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function franchiseePieceRemoveAction()
    {
        $params     = $this->request->get();
        $validation = [
            'id' => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'id']),
        ];
        Validation::validate($params, $validation);
        $r = (new OrganizationDepartmentService())->franchiseePieceRemove($params, $this->user);
        if (!$r) {
            throw new Exception('pieceRemove Error');
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', null);
    }

    /**
     * 根据部门id 搜索部门关联加盟商大区片区
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function selectFranchiseeRegionListAction()
    {
        $params['department_id'] = $this->request->get('department_id', 'trim');

        $validation = [
            'department_id' => 'Required|StrLenGeLe:1,10|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'department_id']),
        ];
        Validation::validate($params, $validation);
        $data = (new OrganizationDepartmentService())->selectFranchiseeRegionList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }
}