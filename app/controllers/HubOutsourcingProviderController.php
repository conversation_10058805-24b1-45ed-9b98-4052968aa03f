<?php
/**
 * HUB,B-HUB 外协公司管理
 */

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\HubOutsourcingConfigService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class HubOutsourcingProviderController extends BaseController
{
    /**
     * @return Response|ResponseInterface
     * @Permission(action='hub_os_config')
     */
    public function listAction()
    {
        $data = (new HubOutsourcingConfigService())->getCompanyList();
        return $this->returnJson(ErrCode::SUCCESS,'ok',$data);
    }

    /**
     * @return Response|ResponseInterface
     * @Permission(action='hub_os_config')
     */
    public function addAction()
    {
        $params = $this->request->get();

        $phoneLenConf = [
            'TH' => '10,10',
            'PH' => '11,11',
            'MY' => '10,11',
        ];
        $phoneLen = $phoneLenConf[get_country_code()] ?? '10,10';
        Validation::validate($params,[
            'company_full_name'=>'Required|StrLenGeLe:1,100',
            'company_name'=>'Required|StrLenGeLe:1,50',
            'company_phone'=>'Required|StrLenGeLe:'.$phoneLen,
            'company_mail'=>'Required|Email',
        ]);
        $data = (new HubOutsourcingConfigService())->addCompany($params,$this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS,'ok',$data);
    }

    /**
     * @return Response|ResponseInterface
     * @Permission(action='hub_os_config')
     */
    public function updateAction()
    {
        $params = $this->request->get();

        $phoneLenConf = [
            'TH' => 10,
            'PH' => 11,
        ];
        $phoneLen = $phoneLenConf[get_country_code()] ?? 10;
        Validation::validate($params,[
            'id' => "Required|Int",
            'company_full_name'=>'Required|StrLenGeLe:1,100',
            'company_name'=>'Required|StrLenGeLe:1,50',
            'company_phone'=>'Required|StrLen:'.$phoneLen,
            'company_mail'=>'Required|Email',
        ]);
        $data = (new HubOutsourcingConfigService())->updateCompany($params, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS,'ok',$data);
    }

    /**
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @Permission(action='hub_os_config')
     */
    public function resetPwdAction()
    {
        $params = $this->request->get();

        Validation::validate($params,[
            'id'=>'Required|Int'
        ]);

        $data = (new HubOutsourcingConfigService())->resetPassword($params['id'],'888888', $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS,'ok',$data);

    }

}