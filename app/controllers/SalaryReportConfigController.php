<?php

namespace App\Controllers;


use App\Library\ErrCode;
use App\Models\backyard\SalaryReportJobConfigModel;
use App\Services\SalaryReportConfigService;


/**
 * 薪酬报表配置
 * Class SalaryReportConfigController
 * @package App\Controllers
 */
class SalaryReportConfigController extends BaseController
{
    /**
     * 岗位配置list
     * @Token
     * @Permission(action='salary_report_job_config')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function jobConfigListAction()
    {
        $list = (new SalaryReportConfigService())->jobConfigList();
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $list);
    }

    /**
     * 岗位配置save
     * @Token
     * @Permission(action='salary_report_job_config')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function jobConfigSaveAction()
    {
        $params = $this->request->get();
        $userId = $this->user['id'];
        $types  = array_keys($params);
        if (array_diff(array_keys(SalaryReportJobConfigModel::$typeMap),
            $types)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        $this->logger->info([$params, $userId]);
        (new SalaryReportConfigService())->jobConfigSave($params, $userId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok');
    }

    /**
     * 汇率配置列表
     * @Token
     * @Permission(action='salary_report_exchange_rate')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exchangeRateListAction()
    {
        $list = (new SalaryReportConfigService())->exchangeRateList();
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $list);
    }

    /**
     * 汇率配置save
     * @Token
     * @Permission(action='salary_report_exchange_rate')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exchangeRateSaveAction()
    {
        $params = $this->request->get();
        $userId = $this->user['id'];
        (new SalaryReportConfigService())->exchangeRateSave($params, $userId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok');
    }

    /**
     * 其他数据补充列表
     * @Token
     * @Permission(action='salary_report_other_data_replenish')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function otherDataReplenishListAction()
    {
        $year = $this->request->get('year');
        if ($year < '2022' || $year > date('Y')) {
            return $this->returnJson(ErrCode::SUCCESS, 'ok', []);
        }
        $list = (new SalaryReportConfigService())->otherDataReplenishList($year);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $list);
    }

    /**
     * 其他数据补充save
     * @Token
     * @Permission(action='salary_report_other_data_replenish')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function otherDataReplenishSaveAction()
    {
        $params = $this->request->get();
        $userId = $this->user['id'];
        (new SalaryReportConfigService())->otherDataReplenishSave($params, $userId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok');
    }
}