<?php
/**
 * HUB,B-HUB 外协薪资配置
 */

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\HubOutsourcingConfigService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class HubOutsourcingSalaryController extends BaseController
{
    /**
     * @return Response|\Phalcon\Http\ResponseInterface
     * @Permission(action='hub_os_config')
     */
    public function configListAction()
    {
        $data = (new HubOutsourcingConfigService())->getStoreSalaryConfigList();
        return $this->returnJson(ErrCode::SUCCESS,'ok',$data);
    }

    /**
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @Permission(action='hub_os_config')
     */
    public function saveConfigAction()
    {
        $params = $this->request->get();
        Validation::validate($params,[
            'store_id'=>"Required|StrLen:10",
            'outsource_per_day'=>"Required|FloatGeLe:0,999999999",
            'security_outsource_per_day'=>"Required|FloatGeLe:0,999999999",
            'food_per_day'=>"Required|FloatGeLe:0,999999999",
        ]);
        $data = (new HubOutsourcingConfigService())->saveSalaryConfig($params);
        return $this->returnJson(ErrCode::SUCCESS,'ok',$data);
    }

    
}