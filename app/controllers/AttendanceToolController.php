<?php

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\OvertimeHourSettingModel;
use App\Services\BllService;
use App\Services\OvertimeBllService;
use App\Services\OvertimeSettingService;

class AttendanceToolController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 限制ot申请日期列表 分页
     * @Token
     * @Permission(action='ot-date-limit')
     */
    public function otLimitListAction()
    {
        $params = $this->request->get();
        $server = new OvertimeBllService();
        $data   = $server->limitDateList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * @description
     * @Permission(action='ot-date-limit')
     * @Token
     */
    public function otLimitAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'date_at'   => 'Required|date',
            'job_title' => 'Required|Arr',
            'ot_type'   => 'Required|Arr',
        ]);
        $params['user_info'] = $this->user;
        $server              = new OvertimeBllService();
        $data                = $server->limitDateAdd($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }

    /**
     * @Permission(action='ot-date-limit')
     * @Token
     */
    public function otLimitDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $params['user_info'] = $this->user;
        $server              = new OvertimeBllService();
        $data                = $server->limitDateDel($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('del_success'), $data);
    }


    /**
     * ot 额度查询 HCM-考勤管理-加班时长限制-OT数据查询
     * @Token
     * @Permission(action='ot-search-offday')
     */
    public function otHourSearchAction()
    {
        $param = $this->request->get();
        Validation::validate($param, [
            'month' => 'Required|Str',
        ]);
        $param['start_date'] = date('Y-m-01', strtotime($param['month']));
        $param['end_date']   = date('Y-m-t', strtotime($param['month']));
        $service             = new OvertimeSettingService();
        $count               = $service->otHourSettingCount($param);
        $data                = $service->otHourSettingList($param);
        return $this->returnJson(ErrCode::SUCCESS, '', ['list' => $data, 'count' => $count]);
    }

    /**
     * ot 额度查询 HCM-考勤管理-加班时长限制-OT数据查询导出
     * @Token
     * @Permission(action='ot-search-offday')
     */
    public function otHourSearchExportAction()
    {
        $params                = $this->request->get();
        $params['user_info']   = $this->user;
        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];
        $params['start_date']   = date('Y-m-01', strtotime($params['month']));
        $params['end_date']     = date('Y-m-t', strtotime($params['month']));
        [$msg, $code] = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "OT_Data_" . date('Y-m-d H:i:s'),
            "attendance-tool" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'otHourSettingExport',
            $params
        );
        return $this->returnJson(ErrCode::SUCCESS, $msg, []);
    }

    /**
     * 网点在职人数 查询
     * @Token
     * @Permission(action='ot-staff-num')
     */
    public function onJobNumAction()
    {
        $param                = $this->request->get();
        $param['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY;
        $service              = new OvertimeSettingService();
        $count                = $service->getOnJobNumCount($param);
        $data                 = $service->getOnJobNum($param);

        return $this->returnJson(ErrCode::SUCCESS, '', ['list' => $data, 'count' => $count]);
    }

    /**
     * 网点在职人数 查询
     * @Token
     * @Permission(action='ot-staff-num')
     */
    public function onJobNumExportAction()
    {
        $params                = $this->request->get();
        $params['user_info']   = $this->user;
        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];
        [$msg, $code] = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "Number_Of_Employee_" . date('Y-m-d H:i:s'),
            "attendance-tool" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'storeOnJobNumExport',
            $params
        );
        return $this->returnJson(ErrCode::SUCCESS, $msg, []);
    }


    /**
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function otWorkDayTemplateAction()
    {
        $service = new OvertimeSettingService();
        $url     = $service->getTemplate(OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $url);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function otOffDayTemplateAction()
    {
        $service = new OvertimeSettingService();
        $url     = $service->getTemplate(OvertimeHourSettingModel::MODULE_TYPE_OFF_DAY);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $url);
    }


    /**
     * 1.5倍 加班小时配置列表
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDayHourListAction()
    {
        $param                = $this->request->get();
        $param['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY;
        $service              = new OvertimeSettingService();
        $data                 = $service->otSettingList($param);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 1倍 配置列表
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDayHourListAction()
    {
        $param                = $this->request->get();
        $param['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_OFF_DAY;
        $service              = new OvertimeSettingService();
        $data                 = $service->otSettingList($param);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }


    /**
     * 主表导入新增配置（工作日）
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDayHourAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'start_date' => 'Required|Date',
            'end_date'   => 'Required|Date',
            'file_url'   => 'Required|Str',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY;
        $data                  = $server->settingHourImport($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }


    /**
     * 主表导入新增配置（休息日）
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDayHourAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'start_date' => 'Required|Date',
            'end_date'   => 'Required|Date',
            'file_url'   => 'Required|Str',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_OFF_DAY;
        $data                  = $server->settingHourImport($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }


    /**
     * 子表详情导入配置（工作日）
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDaySubImportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'file_url' => 'Required|Str',
            'pid'      => 'Required|Int',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY;
        $data                  = $server->subImport($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 子表详情导入配置（休息日）
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDaySubImportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'file_url' => 'Required|Str',
            'pid'      => 'Required|Int',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_OFF_DAY;
        $data                  = $server->subImport($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDaySubAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'pid'      => 'Required|Int',
            'store_id' => 'Required|Str',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY;
        $data                  = $server->subAdd($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDaySubAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'pid'      => 'Required|Int',
            'store_id' => 'Required|Str',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_OFF_DAY;
        $data                  = $server->subAdd($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }


    /**
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDaySubEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_WORK_DAY;
        $data                  = $server->editSub($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDaySubEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server                = new OvertimeSettingService();
        $params['user_info']   = $this->user;
        $params['module_type'] = OvertimeHourSettingModel::MODULE_TYPE_OFF_DAY;
        $data                  = $server->editSub($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('deal_success'), $data);
    }

    /**
     * 1。5倍 详情子列表
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDaySubListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $data                = $server->subList($params);
        $count               = $server->subCount($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['list' => $data, 'count' => $count]);
    }

    /**
     * 1倍 详情子列表
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDaySubListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $data                = $server->subList($params);
        $count               = $server->subCount($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['list' => $data, 'count' => $count]);
    }

    /**
     * 主表 删除
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDayHourDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'pid' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $server->deleteSetting($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('del_success'), null);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDayHourDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'pid' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $server->deleteSetting($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('del_success'), null);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDaySubDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $server->deleteSub($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('del_success'), null);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDaySubDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $server->deleteSub($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('del_success'), null);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDayDetailLogAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $data                = $server->detailLogList($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDayDetailLogAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|Int',
        ]);
        $server              = new OvertimeSettingService();
        $params['user_info'] = $this->user;
        $data                = $server->detailLogList($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * 工作日子列表 导出
     * @Token
     * @Permission(action='ot-hour-limit-workday')
     */
    public function otWorkDayHourExportAction()
    {
        $params              = $this->request->get();
        $params['user_info'] = $this->user;
        //公用验证时间跨度
        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];
        [$msg, $code] = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "Branchs with Monthly 1.5 OT limit_" . date('Y-m-d H:i:s'),
            "attendance-tool" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'workDayHourExport',
            $params
        );
        return $this->returnJson(ErrCode::SUCCESS, $msg, []);
    }

    /**
     * 休息日子列表导出
     * @Token
     * @Permission(action='ot-hour-limit-offday')
     */
    public function otOffDayHourExportAction()
    {
        $params              = $this->request->get();
        $params['user_info'] = $this->user;
        //公用验证时间跨度
        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];
        [$msg, $code] = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "Branchs with Monthly 1 OT limit_" . date('Y-m-d H:i:s'),
            "attendance-tool" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'offDayHourExport',
            $params
        );
        return $this->returnJson(ErrCode::SUCCESS, $msg, []);
    }


}