<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\ExcelService;
use App\Services\StaffWarningDismissService;
use App\Services\StaffWarningService;
use App\Services\WarningService;

class StaffWarningDismissController extends BaseController
{
    /**
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @Token
     */
    public function getSysInfoAction()
    {
        $result = (new StaffWarningDismissService())->getSysInfo();
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取列表-待处理
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning_staff_dismiss.getList_pending')
     */
    public function getListPendingAction()
    {
        $params     = $this->request->get();
        $params     = Validation::ignoreParams($params,
            ['staff_keyword', 'state', 'sys_store_id', 'region_id', 'piece_id', 'handle_result']);
        $validation = [
            'staff_keyword' => 'StrLenGe:0',
            'state'         => 'Arr',
            'sys_store_id'  => 'StrLenGe:0',
            'region_id'     => 'IntGe:0',
            'piece_id'      => 'IntGe:0',
            'handle_result' => 'IntGe:0',
            'page'          => 'Required|IntGe:1',
            'pageSize'      => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);
        $params['is_handle'] = 1;
        $result              = (new StaffWarningDismissService())->getList($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取列表-已处理
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning_staff_dismiss.getList_processed')
     */
    public function getListProcessedAction()
    {
        $params     = $this->request->get();
        $params     = Validation::ignoreParams($params,
            ['staff_keyword', 'state', 'sys_store_id', 'region_id', 'piece_id', 'handle_result']);
        $validation = [
            'staff_keyword' => 'StrLenGe:0',
            'state'         => 'Arr',
            'sys_store_id'  => 'StrLenGe:0',
            'region_id'     => 'IntGe:0',
            'piece_id'      => 'IntGe:0',
            'handle_result' => 'IntGe:0',
            'page'          => 'Required|IntGe:1',
            'pageSize'      => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);
        $params['is_handle'] = 2;
        $result              = (new StaffWarningDismissService())->getList($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 开除信息导出
     * @Token
     * @Permission(action='warning_staff_dismiss.getList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exportListAction()
    {
        try {
            $params = $this->request->get();
            $params = Validation::ignoreParams($params, ['staff_keyword', 'state', 'sys_store_id', 'region_id', 'piece_id', 'handle_result']);

            $validation = [
                'staff_keyword' => 'StrLenGe:0',
                'state' => 'Arr',
                'sys_store_id' => 'StrLenGe:0',
                'region_id' => 'IntGe:0',
                'piece_id' => 'IntGe:0',
                'is_handle' => 'Required|IntGe:0',
                'handle_result' => 'IntGe:0',
            ];
            Validation::validate($params, $validation);
        }catch (ValidationException $e) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $e->getMessage(), []);
        }
    
        $params['staff_info_id'] = $this->user['id'];
        $params['lang'] = $this->locale;
        $params['file_name'] = uniqid('staff_warning_dismiss_' . $this->locale . '_') . '.xlsx';
        $params['type'] = 'dismiss_list';

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "staff_warning_export" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "dismiss";
        // 加锁处理
        $lock_key = 'export_review_list_' . md5($params['staff_info_id']);
        $get_result = $this->atomicLock(function() use ($params, $action_name){
            return (new StaffWarningService())->createExportTask($params, $action_name);
        }, $lock_key, 60);
        if($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($get_result['code'], $this->t['file_download_tip'], $get_result['data']);
    }
    
    /**
     * 根据员工id获取所有警告记录
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getAllWarningListByStaffIdAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_id' => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);
        
        $result = (new WarningService())->getAllWarningListByStaffId($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
    
    /**
     * 处理-忽略
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning_staff_dismiss.getList')
     */
    public function ignoreWarningAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_id' => 'Required|IntGe:1',
            'dialogue_record' => 'Required|StrLenGeLe:1,2000',
        ];
        Validation::validate($params, $validation);
        
        $result = (new StaffWarningDismissService())->ignoreWarning($params, $this->user);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
    
    /**
     * 处理-开除
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning_staff_dismiss.getList')
     */
    public function WarningDismissAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_id' => 'Required|IntGe:1',
            'handle_type' => 'Required|IntIn:2,3',
            'dialogue_record' => 'Required|StrLenGeLe:1,2000',
        ];
        Validation::validate($params, $validation);
        
        $result = (new StaffWarningDismissService())->warningDismiss($params, $this->user);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
    
    /**
     * 查看处理结果详情
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning_staff_dismiss.getList')
     */
    public function getHandleInfoAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_id' => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);
        
        $result = (new StaffWarningDismissService())->getHandleInfo($params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
    
    /**
     * 获取警告书对应的警告通知详情
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning_staff_dismiss.getList')
     */
    public function getWarningMessageNoInfoAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_warning_message_id' => 'Required|IntGe:1',
        ];
        Validation::validate($params, $validation);

        $result = reBuildCountryInstance(new StaffWarningDismissService())->getWarningMessageNoInfo(['warning_id' => $params['staff_warning_message_id']]);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取已发送的开除文件列表
     * @Token
     * @Permission(action='warning_staff_dismiss.getList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function checkFileListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'staff_info_id' => 'Required|StrLenGe:1',
        ];
        Validation::validate($params, $validate_rule);

        $result = (new StaffWarningDismissService())->checkFileListInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 开除文件-预览
     * @Token
     * @Permission(action='warning_staff_dismiss.getList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addFilePreviewAction()
    {
        $params     = $this->request->get();
        $validation = [
            "type"                     => "Required|StrLenGe:1",
            "email"                    => "Required|Email|>>>:" . $this->t->_('4109'),
            "sign_staff_id"            => "Required|StrLenGe:1",
            "staff_info_id"            => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new StaffWarningDismissService())->addFilePreviewUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 开除文件-发送
     * @Token
     * @Permission(action='warning_staff_dismiss.getList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addFileSendAction()
    {
        $params     = $this->request->get();
        $validation = [
            "staff_info_id"            => "Required|StrLenGe:1",
            "type"                     => "Required|StrLenGe:1",
            "email"                    => "Required|Email|>>>:" . $this->t->_('4109'),
            "sign_staff_id"            => "Required|StrLenGe:1",
            "file_url"                 => "Required|StrLenGe:1",
            'cc_email'                 => 'Arr|>>>:' . $this->t->_('empty_mail_notice'),
            'cc_email[*]'              => 'Email|>>>:' . $this->t->_('empty_mail_notice'),
        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new StaffWarningDismissService())->addFileSendUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-再次发送邮件
     * @Token
     * @Permission(action='warning_staff_dismiss.getList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function sendEmailAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'file_id' => 'Required|StrLenGe:1',
            'email'   => 'Required|Email|>>>:' . $this->t->_('email_is_error'),
        ];
        Validation::validate($params, $validate_rule);

        $result = (new StaffWarningDismissService())->sendUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }
    
}