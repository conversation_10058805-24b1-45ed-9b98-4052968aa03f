<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\OutsourcingPushSettingsService;

class OutsourcingPushSettingController extends BaseController
{

    /**
     * 列表
     * @Token
     * @Permission(action='outsourcing_attendance_push')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $paramIn = $this->request->get();
        $data    = (new OutsourcingPushSettingsService())->getList($paramIn);
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success', $data['data'] ?? []);
    }

    /**
     * @description:获取详情
     * @Token
     * @Permission(action='outsourcing_attendance_push')
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function getDetailsAction()
    {
        $paramIn = $this->request->get();
        Validation::validate($paramIn, [
            'id' => 'Required|int',        //id
        ]);
        $data = (new OutsourcingPushSettingsService())->getDetail($paramIn);
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success', $data['data'] ?? []);
    }


    /**
     * @description:编辑
     * @Token
     * @Permission(action='outsourcing_attendance_push')
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function editAction()
    {
        $paramIn                = $this->request->get();
        $paramIn['operator_id'] = $this->user['id'];
        Validation::validate($paramIn, [
            'operator_id'     => 'Required|int',        //员工
            'push_frequency'  => 'Required|int',            //推送频率
            'first_push_date' => 'Required',                //日期
            'push_time'       => 'Required',                //时间
            'mail_data'       => 'Required|Arr|>>>:' . $this->t->_('email_is_error'),
            'company_name_ef' => 'Required',                //公司
        ]);

        //验证邮箱
        if (empty($paramIn['mail_data'])) {
            throw new ValidationException($this->t->_('email_is_error'));
        }
        $mail_data = [];
        foreach ($paramIn['mail_data'] as $v) {
            // 邮箱检验
            $mail_data[] = $v['value'];
            Validation::validate(['email' => $v['value'] ?? ''], ['email' => 'Required|Email|>>>:' . $this->t->_('email_is_error')]);
        }
        $paramIn['mail_data'] = $mail_data;

        // 加锁处理
        $lock_key = md5('outsourcing_push_setting_edit' . $paramIn['operator_id']);
        $data     = $this->atomicLock(function () use ( $paramIn ) {
            $outsourcingPushSettingsService = reBuildCountryInstance(new OutsourcingPushSettingsService());
            return $outsourcingPushSettingsService->edit($paramIn);
        }, $lock_key, 60);
        if ($data === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success', $data['data'] ?? []);
    }

    /**
     * @description: 获取枚举
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/23 18:03
     * @Token
     * @Permission(action='outsourcing_attendance_push')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function getEnumerateAction()
    {
        $outsourcingPushSettingsService = reBuildCountryInstance(new OutsourcingPushSettingsService());
        $data = $outsourcingPushSettingsService->setExpire(60 * 10)->getEnumsFromCache(['locale' => $this->locale]);

        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success', $data['data'] ?? []);
    }

    /**
     * @description: 删除
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/23 18:03
     * @Token
     * @Permission(action='outsourcing_attendance_push')
     */

    public function deleteAction()
    {
        $paramIn                = $this->request->get();
        $paramIn['operator_id'] = $this->user['id'];
        Validation::validate($paramIn, [
            'operator_id' => 'Required|int',        //员工
            'id'          => 'Required|int',            //id
        ]);

        $data = (new OutsourcingPushSettingsService())->delete($paramIn);

        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ?? 'success', $data['data'] ?? []);
    }

}