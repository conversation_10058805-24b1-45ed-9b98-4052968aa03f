<?php

namespace App\Controllers;


use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\ErrCode;
use App\Models\backyard\AsyncImportTaskModel;
use App\Services\AsyncImportTaskService;
use App\Services\ExcelService;
use App\Services\SchedulingSuggestionService;
use SchedulingSuggestionTask;

/**
 * 异步导出任务
 * Class AsyncImportTaskController
 * @package App\Controllers
 */
class AsyncImportTaskController extends BaseController
{
    /**
     * 列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $type     = $this->request->get('type');
        $pageSize = $this->request->get('page_size');
        $pageNum  = $this->request->get('page_num');
        if (!is_array($type)){
            if (!in_array($type, AsyncImportTaskModel::$importTypeMap)) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
            }
        }
        $params = [
            'type'        => $type,
            'operator_id' => $this->user['id'] ?? 0,
        ];
        $data = (new AsyncImportTaskService())->list($params, $pageSize, $pageNum);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 导出结果文件
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function downloadAction()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        $url = (new AsyncImportTaskService())->downloadResultFile($id);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', ['url' => $url]);
    }

    /**
     * 删除
     * @Token
     * @throws \App\Library\Exception\BusinessException
     */
    public function deleteAction()
    {
        $id         = $this->request->get('id');
        $operatorId = $this->user['id'] ?? 0;
        if (empty($id)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        (new AsyncImportTaskService())->delete($id, $operatorId);
        return $this->returnJson(ErrCode::SUCCESS, 'ok');
    }
}