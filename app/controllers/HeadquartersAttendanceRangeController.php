<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\HeadquartersAttendanceRangeService;


class HeadquartersAttendanceRangeController extends BaseController
{

    /**
     * 列表
     * @Token
     * @Permission(action='headquarters_attendance_range-list')
     */
    public function listAction()
    {
        $paramIn = $this->request->get();
        $data = (new HeadquartersAttendanceRangeService())->getList($paramIn);
        return $this->returnJson($data['code'] ?? ErrCode::SUCCESS, 'success', $data['data'] ?? []);
    }

    /**
     * @description:获取详情
     * @Token
     * @Permission(action='headquarters_attendance_range-list')
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function getDetailAction()
    {
        $paramIn = $this->request->get();
        Validation::validate($paramIn, [
            'id' => 'Required|int',        //id
        ]);
        $data = (new HeadquartersAttendanceRangeService())->getDetail($paramIn);
        return $this->returnJson($data['code'] ?? ErrCode::SUCCESS, 'success', $data['data'] ?? []);
    }



    /**
     * @description:编辑新增
     * @Token
     * @Permission(action='headquarters_attendance_range-edit')
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2022/6/23 16:05
     */
    public function editAction()
    {
        $paramIn = $this->request->get();
        $paramIn['operator_id'] =$this->user['id'];
        Validation::validate($paramIn, [
            'longitude' => 'Required|Float',        //经度
            'latitude' => 'Required|Float',      //维度
            'attendance_range' => 'Required|IntGeLe:1,999999999|>>>:Please enter a number from 1 to 999999999',   //打卡范围
        ]);
        // 加锁处理
        $lock_key = md5('headquarters_attendance_range' . $paramIn['operator_id']);
        $data = $this->atomicLock(function() use ($paramIn){
            return  (new HeadquartersAttendanceRangeService())->edit($paramIn);
        }, $lock_key, 60);
        if($data === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ??'success', $data['data'] ?? []);
    }

    /**
     * @description: 删除
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/3 11:46
     * @throws ValidationException
     * @Token
     * @Permission(action='headquarters_attendance_range-edit')
     */
    public function deleteAction()
    {
        $paramIn = $this->request->get();
        $paramIn['operator_id'] =$this->user['id'];
        Validation::validate($paramIn, [
            'id' => 'Required|int',        //删除
        ]);

        $data = (new HeadquartersAttendanceRangeService())->delete($paramIn);

        return $this->returnJson($data['code'] ?? ErrCode::SYSTEM_ERROR, $data['message'] ??'success', $data['data'] ?? []);
    }


}