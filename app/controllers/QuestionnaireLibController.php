<?php
/**
 * Author: Bruce
 * Date  : 2021-12-15 23:59
 * Description:
 */

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\QuestionnaireLibService;
use App\Services\StaffService;
use Exception;


class questionnaireLibController extends BaseController
{

    public $userInfo;
    public $staffService;
    // 消息类型
    public const MSG_TYPE_LIST = ['common', 'sign', 'questionnaire', 'answer'];

    public function initialize()
    {
        parent::initialize();
        $this->staffService = new StaffService();
        $this->userInfo = $this->staffService->get_fbi_user_info($this->user['id']);
    }

    /**
     * 题库列表---question:问卷，answer:答题
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws Exception
     */
    public function listAction()
    {
        $param['page'] = $this->request->get('page', 'int');
        $param['length'] = $this->request->get('length', 'int');
        $param['qn_lib_type'] = $this->request->get('lib_type', 'string');
        $param['key_world'] = $this->request->get('key_world', 'string');

        $param['user_info'] = $this->userInfo;

        $param['qn_lib_type'] = !empty(QuestionnaireLibService::LIB_TYPE[$param['qn_lib_type']]) ? QuestionnaireLibService::LIB_TYPE[$param['qn_lib_type']] : 1;

        $data = (new QuestionnaireLibService())->getList($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 问卷，答题，题库添加
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $param['title'] = $this->request->get('title', 'trim');
        $param['qn_lib_desc'] = $this->request->get('lib_desc', 'trim') ?? '';
        $param['qn_lib_type'] = $this->request->get('lib_type', 'string');
        $param['qn_lib_type'] = !empty(QuestionnaireLibService::LIB_TYPE[$param['qn_lib_type']]) ? QuestionnaireLibService::LIB_TYPE[$param['qn_lib_type']] : 1;
        $param['question'] = $this->request->get('question', 'trim');

        Validation::validate($param, [
            'question' => 'Required|StrLenGe:1|>>>:question params error',
        ]);

        $param['user_info'] = $this->userInfo;
        $result = (new QuestionnaireLibService())->addLib($param);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 编辑问卷，答题，题库
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $param['lib_id'] = $this->request->get('lib_id', 'int');
        $param['title'] = $this->request->get('title', 'trim');
        $param['qn_lib_desc'] = $this->request->get('lib_desc', 'trim') ?? '';
        $param['qn_lib_type'] = $this->request->get('lib_type', 'string');
        $param['qn_lib_type'] = !empty(QuestionnaireLibService::LIB_TYPE[$param['qn_lib_type']]) ? QuestionnaireLibService::LIB_TYPE[$param['qn_lib_type']] : 1;
        $param['question'] = $this->request->get('question', 'trim');

        Validation::validate($param, [
            'question' => 'Required|StrLenGe:1|>>>:question params error',
            'lib_id' => 'Required|IntGt:0|>>>:lib_id params error',
        ]);
        $param['user_info'] = $this->userInfo;

        $result = (new QuestionnaireLibService())->editLib($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);

    }

    /**
     * 预览
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function readAction()
    {
        $param['lib_id'] = $this->request->get('lib_id', 'int');

        Validation::validate($param, ['lib_id' => 'Required|IntGt:0|>>>:lib_id params error']);

        $result = (new QuestionnaireLibService())->read_lib($param);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 问卷库复用
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function copyAction()
    {
        $param['lib_id'] = $this->request->get('lib_id', 'int');

        Validation::validate($param, ['lib_id' => 'Required|IntGt:0|>>>:lib_id params error']);
        $param['user_id'] = $this->userInfo['id'];

        $result = (new QuestionnaireLibService())->copy_lib($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 删除题库
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function delAction()
    {
        $param['lib_id'] = $this->request->get('lib_id', 'int');

        Validation::validate($param, ['lib_id' => 'Required|IntGt:0|>>>:lib_id params error']);
        $param['user_id'] = $this->userInfo['id'];

        $result = (new QuestionnaireLibService())->del_lib($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 筛选项基本信息
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function selectInfoAction()
    {
        $result = (new QuestionnaireLibService())->selectInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 问卷消息统计-导出pdf-记录日志
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function statisticsExportPdfAction()
    {
        $param['msg_id'] = $this->request->get('msg_id', 'int');

        Validation::validate($param, ['msg_id' => 'Required|IntGt:0|>>>:msg_id params error']);
        $param['user_id'] = $this->userInfo['id'];

        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 问卷消息统计
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function statisticsAction()
    {
        $params = $this->request->get();

        Validation::validate($params, ['msg_id' => 'Required|IntGt:0|>>>:msg_id params error']);
        $params['user_id'] = $this->userInfo['id'];

        $result = (new QuestionnaireLibService())->statisticsInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 问卷消息填空题统计
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function completionAction()
    {
        $params = $this->request->get();

        $validation = [
            'msg_id' => 'Required|IntGt:0|>>>:msg_id params error',
            'question_id' => 'Required|>>>:question_id params error'
        ];
        Validation::validate($params, $validation);

        $result = (new QuestionnaireLibService())->getCompletionInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 问卷消息文件上传题统计
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function uploadFileListAction()
    {
        $params = $this->request->get();

        $validation = [
            'msg_id' => 'Required|IntGt:0|>>>:msg_id params error',
            'question_id' => 'Required|>>>:question_id params error'
        ];
        Validation::validate($params, $validation);

        $result = (new QuestionnaireLibService())->uploadFileListInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 提交情况列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function completeListAction()
    {
        $params = $this->request->get();

        $validation = [
            'msg_id' => 'Required|IntGt:0|>>>:msg_id params error'
        ];
        Validation::validate($params, $validation);

        $result = (new QuestionnaireLibService())->getCompleteList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 导出提交情况
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function completeListExportAction()
    {
        $params = $this->request->get();

        $validation = [
            'msg_id' => 'Required|IntGt:0|>>>:msg_id params error'
        ];
        Validation::validate($params, $validation);

        $user_id = $this->user['id'];
        // 加锁处理
        $lock_key = 'completeListExport_'.md5($params['msg_id'].'_'. time() .'_' . $user_id);
        $get_result = $this->atomicLock(function() use ($params){
            return (new QuestionnaireLibService())->completeListExportInfo($params);
        }, $lock_key, 60);

        if($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'please try again', []);
        }
        return $this->returnJson($get_result['code'], $get_result['message'], $get_result['data']);
    }

    /**
     * 提交情况-查看
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function completeDetailAction()
    {
        $params = $this->request->get();

        $validation = [
            'msg_id' => 'Required|IntGt:0|>>>:msg_id params error',
            'staff_id' => 'Required|IntGt:0|>>>:staff_id params error'
        ];
        Validation::validate($params, $validation);

        $result = (new QuestionnaireLibService())->completeDetailInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 问卷题库列表
     * @Token
     * @Permission(action='questionnaire.getQuesList')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getQuesListAction()
    {
        $params = $this->request->get();
        $params['user_info'] = $this->userInfo;
        $validation = [
            'page' => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];
        Validation::validate($params, $validation);

        $params['qn_lib_type'] = !empty(QuestionnaireLibService::LIB_TYPE[$params['qn_lib_type']]) ? QuestionnaireLibService::LIB_TYPE[$params['qn_lib_type']] : 1;

        $result = (new QuestionnaireLibService())->getQuesListInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 获取题库模板详情
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function quesReadAction()
    {
        $params = $this->request->get();

        $validation = [
            'lib_id' => 'Required|IntGt:0|>>>:lib_id params error',
        ];
        Validation::validate($params, $validation);
        $result = (new QuestionnaireLibService())->getQuesRead($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 编辑题库模板
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function quesEditAction()
    {
        $param['lib_id'] = $this->request->get('lib_id', 'int');
        $param['title'] = $this->request->get('title', 'trim');
        $param['qn_lib_desc'] = $this->request->get('lib_desc', 'trim');
        $param['qn_lib_desc'] = $param['qn_lib_desc'] ?? '';
        $param['qn_lib_type'] = $this->request->get('lib_type', 'string');
        $param['qn_lib_type'] = QuestionnaireLibService::LIB_TYPE['questionnaire'];
        $param['question'] = $this->request->get('question', 'trim');

        Validation::validate($param, [
            'question' => 'Required|StrLenGe:1|>>>:question params error',
            'lib_id' => 'Required|IntGt:0|>>>:lib_id params error',
        ]);
        $param['user_info'] = $this->userInfo;

        $result = (new QuestionnaireLibService())->editTemplate($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);

    }

    /**
     * 添加题库模板
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function quesAddAction()
    {
        $param['title'] = $this->request->get('title', 'trim');
        $param['qn_lib_desc'] = $this->request->get('lib_desc', 'trim') ?? '';
        $param['qn_lib_type'] = QuestionnaireLibService::LIB_TYPE['questionnaire'];
        $param['question'] = $this->request->get('question', 'trim');

        Validation::validate($param, [
            'question' => 'Required|StrLenGe:1|>>>:question params error',
        ]);

        $param['user_info'] = $this->userInfo;
        $result = (new QuestionnaireLibService())->addTemplate($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }

    /**
     * 复制题库模板
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function quesCopyAction()
    {
        $param['lib_id'] = $this->request->get('lib_id', 'int');

        Validation::validate($param, ['lib_id' => 'Required|IntGt:0|>>>:lib_id params error']);
        $param['user_id'] = $this->userInfo['id'];

        $result = (new QuestionnaireLibService())->copyTemplate($param);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $result);
    }
}