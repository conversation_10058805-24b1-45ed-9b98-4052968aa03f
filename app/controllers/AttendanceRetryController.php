<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\FlashOss;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\RemoveHrStaffInfoService;
use App\Services\AttendanceRetryService;
use App\Services\BackyardAttendanceService;
use App\Services\ExcelService;
use App\Services\SettingEnvService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AttendanceRetryController extends BaseController
{

    public $data_type_all = [
        0  => ['key' => 0, 'title' => "Salary Attendance Strict"],
        1  => ['key' => 1, 'title' => "Incentive Attendance"],
        2  => ['key' => 2, 'title' => "PD Horizontal"],
        3  => ['key' => 3, 'title' => "PD Detail"],
        4  => ['key' => 4, 'title' => "Hub Full Attendance"],
        5  => ['key' => 5, 'title' => "Salary Attendance Kind"],
        6  => ['key' => 6, 'title' => "Leave Type Sum"],
        7  => ['key' => 7, 'title' => "Incentive Hold List"],
        8  => ['key' => 8, 'title' => "salary Hold List"],
        9  => ['key' => 9, 'title' => "Fulfillment Attendance"],
        10 => ['key' => 10, 'title' => "Flash Money"],
        11 => ['key' => 11, 'title' => "Flash Logistics"],
        12 => ['key' => 12, 'title' => "Flash Pay"],
        13 => ['key' => 13, 'title' => "F commerce"],
        14 => ['key' => 14, 'title' => "Bike compensation coefficient detail"],
        15 => ['key' => 15, 'title' => "FeederA Full Attendance"],
        16 => ['key' => 16, 'title' => "FeederB Full Attendance"],
        17 => ['key' => 17, 'title' => "Site Allowance & Attendance Allowance"],
        18 => ['key' => 18, 'title' => "Entry Allowance"],
        19 => ['key' => 19, 'title' => "Onsite Allowance"],
        20 => ['key' => 20, 'title' => "DC coefficient details"],
        21 => ['key' => 21, 'title' => "Flash Home Operation"],
        22 => ['key' => 22, 'title' => "Car compensation coefficient detail"],
        23 => ['key' => 23, 'title' => "Internship Attendance"],
        24 => ['key' => 24, 'title' => "Bulk Bulk"],
        25 => ['key' => 25, 'title' => "Flash Incorporation"],
    ];


    /**
     * @description:获取相关静态资源列表
     * @return Response|ResponseInterface
     * @Token
     * <AUTHOR> L.J
     * @time       : 2021/10/13 20:41
     */
	public function getStaticListAction()
	{
        $data = [
            'month'                      => date('d') > 15 ? date('Y-m') : date('Y-m', strtotime('last month')),//月份
            'data_type_calculate_attend' => [
                ['key' => 0, 'title' => 'Salary Attendance Strict'],
                ['key' => 1, 'title' => 'Salary Attendance Kind'],
                ['key' => 2, 'title' => 'Incentive Attendance'],
                ['key' => 3, 'title' => 'Salary Intermediate Table Data'],
            ],
            //todo 加这里也要加上边的属性 $data_type_all
            'data_type_all'              => [
                ['key' => 0, 'title' => "Salary Attendance Strict"],
                ['key' => 18, 'title' => "Entry Allowance"],
                ['key' => 17, 'title' => "Site Allowance & Attendance Allowance"],
                ['key' => 1, 'title' => "Incentive Attendance"],
                ['key' => 14, 'title' => "Bike compensation coefficient detail"],
                ['key' => 22, 'title' => "Car compensation coefficient detail"],
                ['key' => 20, 'title' => "DC coefficient details"],
                ['key' => 2, 'title' => "PD Horizontal"],
                ['key' => 3, 'title' => "PD Detail"],
                ['key' => 4, 'title' => "Hub Full Attendance"],
                ['key' => 15, 'title' => "FeederA Full Attendance"],
                ['key' => 16, 'title' => "FeederB Full Attendance"],
                ['key' => 5, 'title' => "Salary Attendance Kind"],
                ['key' => 6, 'title' => "Leave Type Sum"],
                ['key' => 7, 'title' => "Incentive Hold List"],
                ['key' => 8, 'title' => "salary Hold List"],
                ['key' => 9, 'title' => "Fulfillment Attendance"],
                ['key' => 10, 'title' => "Flash Money"],
                ['key' => 11, 'title' => "Flash Logistics"],
                ['key' => 12, 'title' => "Flash Pay"],
                ['key' => 13, 'title' => "F commerce"],
                ['key' => 21, 'title' => "Flash Home Operation"],
                ['key' => 24, 'title' => "Bulk Bulk"],
                ['key' => 25, 'title' => "Flash Incorporation"],
                ['key' => 19, 'title' => 'Onsite Allowance'],
                ['key' => 23, 'title' => "Internship Attendance"],

            ],
            'date_type_special'          => [
                ['key' => 0, 'title' => "Salary Attendance Strict"],
                ['key' => 18, 'title' => "Entry Allowance"],
                ['key' => 17, 'title' => "Site Allowance & Attendance Allowance"],
                ['key' => 1, 'title' => "Incentive Attendance"],
                ['key' => 14, 'title' => "Bike compensation coefficient detail"],
                ['key' => 22, 'title' => "Car compensation coefficient detail"],
                ['key' => 20, 'title' => "DC coefficient details"],
                ['key' => 2, 'title' => "PD Horizontal"],
                ['key' => 3, 'title' => "PD Detail"],
                ['key' => 4, 'title' => "Hub Full Attendance"],
                ['key' => 15, 'title' => "FeederA Full Attendance"],
                ['key' => 16, 'title' => "FeederB Full Attendance"],
                ['key' => 5, 'title' => "Salary Attendance Kind"],
                ['key' => 9, 'title' => "Flash Fulfillment"],
                ['key' => 10, 'title' => "Flash Money"],
                ['key' => 12, 'title' => "Flash Pay"],
                ['key' => 13, 'title' => "F commerce"],
                ['key' => 21, 'title' => "Flash Home Operation"],
                ['key' => 24, 'title' => "Bulk Bulk"],
                ['key' => 25, 'title' => "Flash Incorporation"],
                ['key' => 19, 'title' => 'Onsite Allowance'],
                ['key' => 23, 'title' => "Internship Attendance"],
            ],

        ];
        if (isCountry('TH')) {
            $data['data_type_calculate_attend'][] = ['key' => 4, 'title' => 'Hub Full Attendance'];
        }

        return $this->returnJson(ErrCode::SUCCESS, '', $data);
	}
	
	
	/**
	 * @description:考勤重算 Re Calculate Attend
	 *
	 * @param string month   月份
	 * @param string re_cal_type  类型
	 * @param string staff_id    工号 ,号分割
	 *
	 * @Token
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/14 14:13
	 * @Permission(action='admin.attendance_retry.re_cal_attend_ajax')
	 */
	
	//考勤重算
	public function re_cal_attend_ajaxAction()
	{

        $params['month']              = $this->request->get('month', 'trim', date('Y-m'));
        $params['re_cal_type']        = $this->request->get('re_cal_type', 'int');
        $params['staff_id']           = $this->request->get('staff_id', 'trim');
        $params['edit_staff_info_id'] = $this->user['id'];
        $params['staff_id'] = implode(',',clearSpecialChar($params['staff_id']));
        $validation = [
            'month'       => 'Required',
            're_cal_type' => 'Required|Int',
            'staff_id'    => 'Required|>>>:Staff Info Data Cannot be empty!',
        ];

        Validation::validate((array)$params, $validation);
        //本地化处理
        //此处改为异步处理
        $month = $params['month'];
        if ($month < '2021-01') {
            $salary_start_suffix = '16';
            $salary_end_suffix   = '15';
        } else {
            $salary_start_suffix = '24';
            $salary_end_suffix   = '23';
        }
        $salary_month           = $month . '-' . $salary_start_suffix;
        $salary_start           = date('Y-m-' . $salary_start_suffix, strtotime($salary_month) - 30 * 86400);
        $salary_end             = date('Y-m-' . $salary_end_suffix, strtotime($salary_month));
        $params['salary_start'] = $salary_start;
        $params['salary_end']   = $salary_end;
        $result                 = (new AttendanceRetryService())->re_cal_attend_ajax($params);
        return $this->returnJson($result['code'], $result['message'], []);
		
	}
	
	
	/**
	 * @description:异步考勤计算列表
	 *
	 * @param null
	 *
	 * @Token
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/18 10:21
	 * @Permission(action='admin.attendance_retry.re_cal_attend_list_ajax')
	 */
	public function re_cal_attend_list_ajaxAction()
	{

        $params['page']              = $this->request->get('page', 'int', 1);
        $params['size']        = $this->request->get('size', 'int',20);
        $backyard_attendance_bll = new AttendanceRetryService();
        $validTime = date("Y-m-d 00:00:00",strtotime('-1 month'));
        $task_list               = $backyard_attendance_bll->getAsyncTask('0,1,2', '0,1,2,3,4',null,$params['page'],$params['size'],'id desc',$validTime);
        foreach ($task_list as $k => $v) {
            $task_list[$k]['type_w'] = $v['type'] == 1 ? '全量计算' : '部分计算';
            switch ((int)$v['category']) {
                case  0:
                    $category_w = 'Salary Attendance Strict';
                    break;
                case 1:
                    $category_w = 'Salary Attendance Kind';
                    break;
                case 2:
                    $category_w = 'Incentive Attendance';
                    break;
                case 3:
                    $category_w = 'Attendance Data Month';
                    break;
                case 4:
                    $category_w = 'Hub Full Attendance';
                    break;
                default:
                    $category_w = '';
            }
            switch ((int)$v['state']) {
                case 0:
                    $state_w = '计算中';
                    break;
                case 1:
                    $state_w = '计算完成';
                    break;
                case 2:
                    $state_w = '计算失败';
                    break;
                default:
                    $state_w = '';
            }
            $task_list[$k]['category_w'] = $category_w;
            $task_list[$k]['state_w']    = $state_w;
        }
        $DataList=['DataList' => $task_list];
        $DataList['total'] = $backyard_attendance_bll->getAsyncTaskTotal('0,1,2', '0,1,2,3',$validTime);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $DataList);
		
	}
	
	
	/**
	 * @description:All Data 导出  和 Special Data
	 *
	 * @param null
	 *
	 * @Token
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/18 10:21
	 * @Permission(action='admin.attendance_retry.attendance_export_all')
	 */
	public function attendance_export_allAction()
	{
        $params              = $this->request->get();
        $params['month']     = $params['month'] ?? date('Y-m');
        $params['data_type'] = $params['data_type'] ?? 0;
        $params['staff_id']  = $params['staff_id'] ?? '';
        $params['month_type'] = $params['month_type'] ?? 1;
        $params['staff_id']   = implode(',',clearSpecialChar($params['staff_id']));
        $params['lang']       = $this->locale;
        //获取当前登录人
        $data_type_all = $this->data_type_all;
        $file_name     = 'attendance' . '_' . $this->locale . $data_type_all[$params['data_type']]['title'] . date('Ymd-His') . '.xlsx';
        $file_name = str_replace(" ","_",$file_name);
        $action_name   = 'backyardattendance'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'dow_excel';
        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params, 2, $file_name);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], []);
		
	}
	
	
	/**
	 * @description:修改考勤数据 模板导出
	 *
	 * @param null
	 *
	 * @Token
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/18 17:00
	 */
	
	public function get_tpl_dwnAction()
	{

        $type = $this->request->get('type', 'int', 1);

        switch ((int)$type) {
            case 1:
                $data = ['header' => ['staff_info_id', 'stat_month', 'revise_data'], 'data' => [['10000', '2019-09', 20]], 'update_path' => 'revise_tpl', 'file_name' => 'revise_tpl'];
                break;
            case 2:
                $data = ['header' => ['staff_info_id', 'date'], 'data' => [['10000', '2019-12-01']], 'update_path' => 'gt_9hours', 'file_name' => 'gt_9hours'];
                break;
            case 3:
                $data = ['header' => ['staff_info_id', 'valid_on'], 'data' => [['10000', '20']], 'update_path' => 'revise_hub_full_attend_data_tpl', 'file_name' => 'revise_hub_full_attend_data_tpl'];
                break;
            default:
                $data = ['header' => ['staff_info_id', 'stat_month', 'revise_data'], 'data' => [['10000', '2019-09', 20]], 'update_path' => 'revise_tpl', 'file_name' => 'revise_tpl'];
        }

        $result = (new \App\Services\BllService())->exportExcels($data['header'], $data['data'], $data['update_path'], $data['file_name'] . '.xlsx');
        if ($result['code'] == ErrCode::SUCCESS) {
            $flashOss      = new FlashOss();
            $result['url'] = $flashOss->signUrl($result['url']);
        }
        return $this->returnJson(ErrCode::SUCCESS, '', $result);
		
	}
	
	
	/**
	 * @description:修改考勤数据上传
	 *
	 * @param null
	 *
	 * @Token
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/18 17:00
	 */
	public function revise_attendance_dataAction() {

        $bll = new BackyardAttendanceService();
        //读取 execl
        $row_data = $bll->fileUpload($_FILES['file']);
        if (empty($row_data)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'no data');
        }

        $this->logger->info(['Action' => 'revise_attendance_dataAction', 'params' => $row_data, 'action_staff' => $this->user['id']]);

        $res = $bll->reviseAttendanceData($row_data);

        return $this->returnJson(ErrCode::SUCCESS, '', $res);
		
	}
	
	
}
