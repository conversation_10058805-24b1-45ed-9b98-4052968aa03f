<?php

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\HttpCurl;
use App\Library\RestClient;
use App\Library\OssHelper;
use App\Library\RocketMQ;
use App\Models\********\HoldStaffManageModel;
use App\Models\********\HrBlackGreyListModel;
use App\Models\********\HrBlacklistModel;
use App\Models\********\HrOperateLogsModel;
use App\Models\********\HrOutSourcingBlacklistModel;
use App\Models\********\HrStaffIdentityAnnexModel;
use App\Models\********\HrStaffInfoModel;
use App\Models\********\SettingEnvModel;
use App\Models\********\StaffWorkAttendanceAttachmentModel;
use App\Models\********\StaffWorkAttendanceModel;
use app\models\********\VEmployeesInformationModel;
use App\Services\HubOutSourcingStaffService;
use App\Services\StaffContractService;
use App\Services\StaffSupportStoreServer;
use App\Services\SysStoreService;
use app\traits\FlashTokenTrait;
use App\Traits\TokenTrait;
use App\Services\AttendanceCalculatorService;

class AaTask extends BaseTask
{

    public function ossAction()
    {
        $filePath = BASE_PATH.'/public/template/salary.xlsx';
        $file = OssHelper::uploadFile($filePath,'WORK_ORDER',true);
        dd($file);

    }

    public function ggAction()
    {
      (new \App\Modules\My\Services\PayrollReportService())->sendPayslipMessage([
            ['month' => '2025-01','run_type' => 1,'staff_info_id' => 19987],
        ]);
    }
    public function ssAction()
    {
        $a =  BiMail::send_lnt(['<EMAIL>'],strtoupper(env('country_code')).'-'.env('runtime').'-WinHr角色菜单配合信息','adddd');
        dd($a);

        $s               = (new \app\modules\Ph\services\Gongzi2025Service());
        $s->calMonth     = '2025-032';
        $r['phone_base'] = 500;
        $r['phone']      = 500;
        $r['start']      = '2025-03-15';
//        $r['last_working_day'] =null;
        $r['last_working_day'] = '2025-03-16';
        $s->cal_communication_allowance($r, []);
        var_dump($r);
    }

    public function vvAction()
    {
        $a = new AttendanceCalculatorService();
        //$a->addRecord('PH', 'PH', 'all', 10);

//        $a->addRecord('LW', 'unpaidLeave.15', 'morning', 5);
//        $a->addRecord('LW', 'unpaidLeave.15', 'afternoon', 5);
//        $a->addRecord('OFF', 'OFF', 'all', 10);
//        $a->addRecord('SL', 'paidLeave.10', 'all', 10);

//        $a->addRecord('CT', 'CT', 'morning', 5);
//        $a->addRecord('on_daily', 'actualAttendance', 'all', 10);

//        $a->addRecord('LW', 'unpaidLeave.15', 'morning', 5);
//        $a->addRecord('PH', 'PH', 'all', 10);


//        $a->addRecord('LW', 'unpaidLeave.15', 'morning', 5);
//        $a->addRecord('LW', 'unpaidLeave.15', 'afternoon', 5);
//        $a->addRecord('PH', 'PH', 'all', 10);

//        $a->addRecord('SL', AttendanceCalculatorService::TYPE_PAID_LEAVE.'.1', 'afternoon', 5);
//        $a->addRecord('ON', AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE, 'afternoon', 5);
//        $a->addRecord('ON', AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE, 'morning', 5);


//        $a->addRecord('OFF', 'OFF', 'morning', 5);
//        $a->addRecord('AB', 'AB', 'all', 10);

//        $a->addRecord('SL', 'paidLeave.10', 'all', 10);
//        $a->addRecord('attendance_time', AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE, 'all', 10);


//        $a->addRecord('SL', AttendanceCalculatorService::TYPE_PAID_LEAVE.'.10', 'morning', 5);
//        $a->addRecord('on', AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE, 'morning', 5);

//        $a->addRecord('SL', AttendanceCalculatorService::TYPE_PAID_LEAVE.'.10', 'afternoon', 5);
//        $a->addRecord('on', AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE, 'afternoon', 5);

//        $a->addRecord('SL', AttendanceCalculatorService::TYPE_PAID_LEAVE.'.10', 'afternoon', 5);
////        $a->addRecord('CT', AttendanceCalculatorService::TYPE_BT, 'morning', 5);
//        $a->addRecord('on_daily', AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE, 'afternoon', 5);
//        $a->addRecord('AB', AttendanceCalculatorService::TYPE_ACTUAL_AB, 'all', 10);
        $a->addRecord('attendance_time',
        AttendanceCalculatorService::TYPE_ACTUAL_ATTENDANCE,
        'all', 10);
        $r = $a->calculate();
        foreach ($r as $i) {
            var_dump($i);
        }
    }
    public function ppAction()
    {
      $aa = new \App\Services\BikeCompensationCoefficientService();
      $a = $aa->getBikeCourierStaffId('2023-10-20','2023-10-20',[]);

      var_dump($a);
    }

    public function t22Action()
    {
       $a =  (new \App\Repository\DictionaryRepository)->getCodeTwoCodeMap();
       $a =  (new \App\Modules\My\Services\PayrollReportService())->getCP39DataList('2025-01');
       $a =  (new \App\Modules\My\Services\PayrollReportService())->getCP39TXTDataList('2025-01');
       $a =  (new \App\Modules\My\Services\PayrollBankFileService())->getStaffInfoForTax([121650]);
       dd($a);
       $t =  BaseService::getTranslation('zh');

       dd($t->_('company_rule_16'));


        $a = [

            't_warning_139'=>'损害公司利益类',
        ];
        $auth = 'Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.VuO9WyMKLqLhsuKmIkw7QO29DSFADZsjTA98iFW7kYs';
        foreach ($a as $key =>$item) {

            $request_params = [
                'langs'=>['zh'=>$item],
                'from'=>'add',
                't_key'=>$key,
                'os'=>'********-api',
                'loading'=>false,
            ];
            //2 调用java接口
            
            $result = HttpCurl::post('https://ard-i18n-api.flashexpress.pub/v1/translations/save', json_encode($request_params),['accept-language:zh',$auth]);

            var_dump($result);

        }


    }


    public function getImageTagByBaidu($imageUrl)
    {
        try {
            $fle_rpc = new ApiClient('fle', 'com.flashexpress.fle.svc.api.BaiduImageTagSvc', 'imageTag',
                $this->lang);
            $fle_rpc->withParam($imageUrl);
            $result = $fle_rpc->execute();
            if (isset($result['error'])) {
                $this->getDI()->get('logger')->write_log("从百度获取图片认证失败" . json_encode($imageUrl), 'error');
                return true;
            }
            $this->getDI()->get('logger')->write_log("百度获取图片参数信息：" . json_encode($imageUrl), 'info');
            return $result ?? true;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("里程百度认证图片异常" . $e->getMessage(), 'error');
            return true;
        }
    }
    public function t1Action()
    {
        $filePath = BASE_PATH.'/public/ic_1023.xlsx';

        //获取Excel数据
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,
            2 => \Vtiful\Kernel\Excel::TYPE_STRING,
            3 => \Vtiful\Kernel\Excel::TYPE_STRING,
            4 => \Vtiful\Kernel\Excel::TYPE_STRING,
            5 => \Vtiful\Kernel\Excel::TYPE_STRING,
        ];
        $baseService = new BaseService();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);
        $returnData = $header = [];
        foreach ($excelData as $k => $v) {

            if($k == 0){
                $header = $v;
                $header[] = '百度验证是否作弊';
                continue;
            }

            $v[] = intval(!$this->getImageTagByBaidu($v[3]));
            $returnData[] = $v;
        }

        $return = $baseService->exportExcel($header, $returnData, uniqid('zuobi1023'));
        $flashOss = new FlashOss();
        $object   = 'zoubi/' . date('ymd') . '/' . uniqid('baidu1023').'xlsx';
        $f = $flashOss->uploadFile($object, $return['data']);
        $url = $flashOss->signUrl($object,5*86400);
        dd($url);
    }

    public function t2Action(){
        $object ='zoubi/241023/baidu102367190a2a1bd83xlsx';
        $flashOss = new FlashOss();
        $url = $flashOss->signUrl($object,5*86400);
        dd($url);
    }

    /**不打卡数据
     * @return void
     * @throws Exception
     */
    public function punchInAction()
    {


        $file = fopen('ph 上班.csv', 'r');
        $index = 0;
        $arr = [];
        if ($file) {
            while (($line = fgets($file))!== false) {
                preg_match('/@timestamp"":""([^""]+)""/', $line, $timestampMatches);
                preg_match('/"uid"":\s*""([^""]+)""/', $line, $uidMatches);

                $timestamp = $timestampMatches[1] ?? null;
                $uid = $uidMatches[1] ?? null;
                if (empty($uid)) {
                    continue;
                }
                $arr[$uid]['staff_info_id'] = $uid;
                $arr[$uid]['click_time']     = gmdate('Y-m-d H:i:s', strtotime($timestamp));
                $arr[$uid]['attendance_type']     = 1; //1 上班 2 下班
                ++$index;
            }
            fclose($file);
        } else {
            echo "Unable to open the file.".PHP_EOL;
        }
        (new \app\models\********\DddModel())->batch_insert(array_values($arr));

    }
    public function qqAction(){

        $s = new \App\Services\WarningService();
        $s = $s->getMessageWarningNumber(['warning_type'=>2]);
        dd($s);

        $workerNum = 3;
        $all= [];
        $processArr= [];
        for($p=0;$p<$workerNum;$p++){
            $process = new swoole_process(function(swoole_process $worker) use($workerNum,$p){
                $worker->write('1');
                sleep(5);
            },false);//true  配置后 让线程打印内容输出到管道
            $pid = $process->start();
            $processArr[$pid] = $process;
        }
        foreach ($processArr as $process) {
            $all[] =  json_decode($process->read(),true);
        }

        //回收子进程
        while ($res = swoole_process::wait()) {
            var_dump($res);
        }
    }



    public function vsAction()
    {
        $token= '1716987004_3db62758ccaf433490edae18270bcd2a6238b591964c72e8ead568074137176f_56780';
        [$time,$digest,$staff_id] = explode('_',$token);
        dd($time,$digest,$staff_id);


        $V   = VEmployeesInformationModel::find();
        $box = [];
        foreach ($V as $item) {
            $start =zero_time_zone( $item->hire_date);
            $end   = zero_time_zone(date('Y-m-d 23:59:59',strtotime($item->hire_date)));

            $cv    = 0;
            while (true) {
                if ($cv > 14) {
                    $item->remark = '14天内没有空位';
                    $item->save();
                    break;
                }

                if ($cv > 1) {
                    $start = zero_time_zone( date('Y-m-d',strtotime($item->hire_date . ' -' . $cv . ' days')));
                    $end   = zero_time_zone( date('Y-m-d 23:59:59',strtotime($item->hire_date . ' +' . $cv . ' days')));
                }

                $sql      = "select staff_info_id from hr_staff_info where staff_info_id >= 100000 and  staff_info_id <= 200000 and created_at >= '$start' and  created_at <= '$end' order by staff_info_id ";
                $allStaff = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

                if (empty($allStaff)) {
                    $item->remark = '当天没有人入职';
                    $item->save();
                    break;
                }

                $allStaff = array_column($allStaff, 'staff_info_id');

                $min           = min($allStaff);
                $max           = max($allStaff);
                $staff_info_id = 0;
                for ($i = $min; $i < $max; $i++) {
                    if (!in_array($i, $allStaff) && !in_array($i, $box)) {
                        $staff_info_id = $i;
                    }
                }
                if ($staff_info_id) {
                    $box[]    = $staff_info_id;
                    $item->staff_info_id = $staff_info_id;
                    $item->save();
                    break;
                }
                $cv++;
            }
        }
    }





    public function hikAction()
    {


        $params['staff_info_id'] = 120736;
        //通知ms员工转个人代理
        $data = ['staff_id' => intval($params['staff_info_id'])];
        $rmq  = new RocketMQ('hire-type-change-to-java');
        //发送的消息体
        $params = [];
        $params['jsonCondition'] = json_encode($data);
        $params['handleType'] = RocketMQ::TAG_NAME_STAFF_HIRE_TYPE_TO_13;

        $rid2 = $rmq->sendOrderlyMsg($params);
        dd($rid2);
        $this->logger->write_log('hire-type-change-to-java rid2:' . $rid2 . 'data:' . json_encode($data), $rid2 ? 'info' : 'error');

        $service = new \App\Services\HubOutSourcingStaffService();
        $service->setCacheOrgInfo();
    }

    public function hikCacheAction()
    {
        $redis = $this->getDI()->get("redis");
        $c2    = $redis->get(HubOutSourcingStaffService::REDIS_HIK_ORG_CACHE_2);//缓存1天

        $c1 = $redis->get(HubOutSourcingStaffService::REDIS_HIK_ORG_CACHE_1);//缓存10分钟

        dd($c1, $c2);
    }

    /**
     * @Single
     * @return void
     */
    public function sssAction(){
        // <= 2022-10-20
        //$identity_annex['bank_card_audit_state'] === null  就是待上传
        //刷数据  1、 老数据 有银行卡 则 bank_card_audit_state = 2，2 没有银行卡 则仅塞一条数据
        echo 2345;

    }



    /**
     * 获取员工数据
     * @param int $staff_info_id
     * @param int $max_id
     * @return mixed
     */
    protected function getStaffList(int $staff_info_id = 0, int $max_id = 0)
    {
        $limit = 500;
        if (RUNTIME == 'dev') {
            $limit = 2;
        }
        $sql = "select staff_info_id,bank_no from hr_staff_info where hire_date <= '2022-10-19 00:00:00' and  staff_info_id > {$max_id} ";
        if ($staff_info_id) {
            $sql .= " and staff_info_id = $staff_info_id";
        }
        $sql .= " and  formal in (1,4) and is_sub_staff = 0 order by staff_info_id asc limit $limit";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    public function fixBankAction($params)
    {
        $staff_info_id          = $params[0] ?? 0;
        $max_id                 = 0;
        $i                      = 1;
        while (true) {
            $staff_list = $this->getStaffList(intval($staff_info_id), intval($max_id));
            if (empty($staff_list)) {
                break;
            }

            $max_id = max(array_column($staff_list, 'staff_info_id'));
            $insertData= [];
            foreach ($staff_list as $item) {
                //by申请的有效离职
                $staffIdentity = HrStaffIdentityAnnexModel::FindFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind'       => [
                        'staff_info_id' => $item['staff_info_id'],
                    ],
                ]);
                $bank_state = empty($item['bank_no']) ? null :1;

                if(empty($staffIdentity)){
                    $insertItem['staff_info_id'] = $item['staff_info_id'];
                    $insertItem['bank_card_audit_state']  = $bank_state;
                    $insertData[]                = $insertItem;
                    continue;
                }
                $staffIdentity->bank_card_audit_state = $bank_state;
                $staffIdentity->save();
            }

            if ($insertData) {
                (new HrStaffIdentityAnnexModel())->batch_insert($insertData);
            }
        }
    }

    public function popAction()
    {
        $redis  = $this->getDI()->get("redis");
        $redis_data = $redis->rpop('pod_test');
        $this->logger->write_log('pod_test ' . $redis_data);
    }

    public function pushAction()
    {
        $redis  = $this->getDI()->get("redis");
        $i = 500;
        while ($i > 0) {
            $redis->lpush('pod_test', 'test'.$i);
            echo $i.PHP_EOL;
            $i--;
        }
    }

    public function faceBlackListAction()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['i' => HrStaffInfoModel::class]);
        $builder->innerJoin(HrBlacklistModel::class, 'i.identity = b.identity', 'b');
        $builder->where("b.status = 1 and !(b.identity  = '' or b.identity  is null )");
        $builder->columns(['i.staff_info_id','i.identity']);
        $staffList = $builder->getQuery()->execute()->toArray();
        echo  ' num' .count($staffList).PHP_EOL;
        $chunkStaff = array_chunk($staffList, 500);

        foreach ($chunkStaff as $key => $staffs) {
            echo 'chunk '.$key.PHP_EOL;
            $staff_ids = array_column($staffs, 'staff_info_id');
            $staffAttachment = StaffWorkAttendanceAttachmentModel::find([
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and deleted = 0',
                'bind'       => [
                    'staff_info_ids' => $staff_ids,
                ],
                'columns'    => 'staff_info_id,work_attendance_path',
                'order'      => 'created_at desc',
            ])->toArray();

            $staffAttachment = array_column($staffAttachment, 'work_attendance_path', 'staff_info_id');
            $data = [];
            foreach ($staffs as $item) {
                if (empty($staffAttachment[$item['staff_info_id']])) {
                    continue;
                }
                $image                     = $this->config->application->img_prefix . $staffAttachment[$item['staff_info_id']];
                $tmp['staff_info_id']      = $item['staff_info_id'];
                $tmp['identity']           = $item['identity'];
                $tmp['face_negatives_url'] = $image;
                $tmp['src']                = 2;
                $data[]                    = $tmp;
            }
            
            $data && (new \App\Models\********\StaffFaceBlacklistModel())->batch_insert($data);
        }
    }

    public function fixAction()
    {
          $sql = "select id, `staff_info_id` ,`sub_staff_info_id` ,`employment_begin_date` ,`employment_end_date`  from `hr_staff_apply_support_store`  where `sub_staff_info_id`  IN (522201,
            522210,
            522269,
            522218,
            522246,
            522249,
            522274,
            522252,
            522186,
            522211,
            522215,
            522204,
            522255,
            522187,
            522245,
            522253,
            522277,
            522200,
            522270,
            522276,
            522273,
            522244,
            522248,
            522278,
            522247,
            522219,
            522206,
            522212,
            522198,
            522250,
            522243,
            522268,
            522196,
            522188,
            522203,
            522272,
            522195,
            522209,
            522185,
            522275,
            522199,
            522205,
            522279,
            522271,
            522189,
            522251,
            522202,
            522254) and `support_status` = 2
            ";
        $data =  $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        foreach ($data as $datum) {
            $sync_ms_params = [
                [
                    'id'           => $datum['id'],
                    'staff_id'     => $datum['sub_staff_info_id'],
                    'master_staff' => $datum['staff_info_id'],
                    'begin_at'     => strtotime($datum['employment_begin_date']),
                    'end_at'       => strtotime($datum['employment_end_date']) + 86399,
                ],
            ];
            //马来 揽派分离类型支援 没有子账号 不需要同步
            $res = (new StaffSupportStoreServer())->syncMsSupportApply($sync_ms_params, 'addStaffSupportInfo');
            var_dump($sync_ms_params,$res);
            //
            //
            //
        }

    }


    /**
     * 初始化人脸黑名单数据
     * @return void
     * @throws Exception
     */
    public function initFaceBlackListAction()
    {
        $staffList = [];
        if (isCountry('MY')) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['i' => HrStaffInfoModel::class]);
            $builder->innerJoin(HrBlacklistModel::class, 'i.identity = b.identity', 'b');
            $builder->where("b.status = 1 and !(b.identity  = '' or b.identity  is null )");
            $builder->columns(['i.staff_info_id', 'i.identity']);
            $staffList1 = $builder->getQuery()->execute()->toArray();

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['i' => HrStaffInfoModel::class]);
            $builder->innerJoin(HrOutSourcingBlacklistModel::class, 'i.identity = b.identity', 'b');
            $builder->where("b.status = 1 and b.type in (1,3) and !(b.identity  = '' or b.identity  is null )");
            $builder->columns(['i.staff_info_id', 'i.identity']);
            $staffList2 = $builder->getQuery()->execute()->toArray();

            $staffList = array_merge($staffList1, $staffList2);
        } elseif (isCountry('TH')) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['i' => HrStaffInfoModel::class]);
            $builder->innerJoin(HrBlackGreyListModel::class, 'i.identity = b.identity', 'b');
            $builder->where("b.status = 1 and b.behavior_type = 1 and !(b.identity  = '' or b.identity  is null )");
            $builder->columns(['i.staff_info_id', 'i.identity']);
            $staffList = $builder->getQuery()->execute()->toArray();
        }


        echo  ' num' .count($staffList).PHP_EOL;
        $staffList = array_column($staffList,null,'staff_info_id');
        $chunkStaff = array_chunk($staffList, 500);

        foreach ($chunkStaff as $key => $staffs) {
            echo 'chunk '.$key.PHP_EOL;
            $staff_ids = array_column($staffs, 'staff_info_id');
            $staffAttachment = StaffWorkAttendanceAttachmentModel::find([
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and deleted = 0',
                'bind'       => [
                    'staff_info_ids' => $staff_ids,
                ],
                'columns'    => 'staff_info_id,work_attendance_path',
                'order'      => 'created_at desc',
            ])->toArray();

            $staffAttachment = array_column($staffAttachment, 'work_attendance_path', 'staff_info_id');
            $data = [];
            foreach ($staffs as $item) {
                if (empty($staffAttachment[$item['staff_info_id']])) {
                    continue;
                }
                $image                     = $this->config->application->img_prefix . $staffAttachment[$item['staff_info_id']];
                $tmp['staff_info_id']      = $item['staff_info_id'];
                $tmp['identity']           = $item['identity'];
                $tmp['face_negatives_url'] = $image;
                $tmp['src']                = 2;
                $data[]                    = $tmp;
            }
            $data && (new \App\Models\********\StaffFaceBlacklistModel())->batch_insert($data);
        }
    }

    public function deleteLockAction($params)
    {
        $key = $params[0];
        echo $this->getDI()->get("redis")->del($key);//释放锁

    }










}