<?php

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\FlashOss;
use App\Models\backyard\AttendanceDataV2Model;
use App\Models\backyard\HcmPermissionModel;
use App\Models\backyard\HcmRolePermissionModel;
use App\Models\backyard\HcmStaffPermissionModel;
use App\Models\backyard\HrBlackGreyListModel;
use App\Models\backyard\HrBlacklistModel;
use App\Models\backyard\HrJdModel;
use App\Models\backyard\HrOutSourcingBlacklistModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffIdentityAnnexAuditLogModel;
use App\Models\backyard\StaffPayrollModel;
use App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use App\Models\backyard\TripleToLeaveModel;
use App\Models\bi\RolesModel;
use App\Models\backyard\SysStoreModel;
use App\Models\fle\StaffInfoPositionModel;
use App\Modules\My\Services\BackyardAttendanceService;
use App\Modules\My\Services\FrontlineSalaryInfoService;
use App\Modules\My\Services\StaffPublicHolidayService;
use App\Modules\Ph\library\AttendanceUtil;
use App\Repository\HrStaffShiftRepository;
use App\Services\AttendanceWhiteListService;
use App\Services\BllService;
use App\Services\FormPdfServer;
use App\Services\HoldManageService;
use App\Services\HrJobTitleService;
use App\Services\SettingEnvService;
use App\Services\StaffInfoAuditService;
use App\Services\StaffInfoService;
use App\Services\StaffService;
use App\Services\StaffVacationService;
use App\Services\SysService;


class TestTask extends BaseTask
{

    public function fixImgAction($params)
    {
        $sql = "select * from hr_staff_annex_info where `audit_state` is not null and `annex_path_front` is null and type = 1 ";
        if (!empty($params[0])) {
            $sql .= ' and `staff_info_id` = '.$params['0'];
        }
        $data    = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $success = [];
        foreach ($data as $datum) {
            $log = \App\Models\backyard\HrOperateLogsModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and  before like \'%"identity_annex_url":"https%\' and after like \'%"identity_annex_url":""%\' ',
                'bind'       => [
                    'staff_info_id' => $datum['staff_info_id'],
                ],
                'columns'    => "before",
                'order'      => 'id DESC',
            ]);

            if (!empty($log)) {
                $before = json_decode($log->before, true)['body'];
                if (!empty($before['identity_annex_url'])) {
                    $model                   = \App\Models\backyard\HrStaffAnnexInfoModel::findFirst([
                        'conditions' => 'staff_info_id = :staff_info_id: and type = 1',
                        'bind'       => [
                            'staff_info_id' => $datum['staff_info_id'],
                        ],
                    ]);
                    $model->annex_path_front = $before['identity_annex_url'];
                    $model->save();
                    $success[] = $datum['staff_info_id'];
                }
            }
        }

        $this->logger->info(['fixImgAction' => $success]);
    }


    public function fixIDAction($params)
    {
        $sql = "select n.* from  hr_staff_annex_info as n
  left join staff_identity_annex_audit_log as b on n.`staff_info_id` = b.`staff_info_id`
  and n.`type` = 1
  and b.`type` = 1
where
  b.id IS NOT NULL
  and n.`audit_state` is null  ";
        if (!empty($params[0])) {
            $sql .= ' and n.`staff_info_id` = '.$params['0'];
        }
        $data    = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $success = [];
        foreach ($data as $datum) {
            $audit_log_list_db = StaffIdentityAnnexAuditLogModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and type = 1',
                'bind'       => [
                    'staff_info_id' => $datum['staff_info_id'],
                ],
                'columns'    => "id, staff_info_id, audit_id, audit_name, audit_before_state, audit_after_state, reject_reason, type,  create_at",
                'order'      => 'id DESC',
            ]);

            if (!empty($audit_log_list_db)) {
                $audit_state                = $audit_log_list_db->audit_after_state;
                $audit_state_date           = show_time_zone($audit_log_list_db->create_at, 'Y-m-d');
                $reject_reason              = $audit_log_list_db->reject_reason;
                $audit_staff_info_id        = $audit_log_list_db->audit_id;
                $model                      = HrStaffAnnexInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: and type = 1',
                    'bind'       => [
                        'staff_info_id' => $datum['staff_info_id'],
                    ],
                ]);
                $model->audit_state         = $audit_state;
                $model->reject_reason       = $reject_reason;
                $model->audit_staff_info_id = $audit_staff_info_id;
                $model->audit_state_date    = $audit_state_date;
                $model->save();
                $success[] = $datum['staff_info_id'];
            }
        }

        $this->logger->info(['fixIDAction' => $success]);
    }


    public function initSettingAction()
    {
        $params = [];
        $data   = (reBuildCountryInstance(new SettingEnvService()))->getSettingList($params);
        $map    = array_flip(SettingEnvModel::$category_list);
        foreach ($data as $key => $datum) {
            $i = 0;
            foreach ($datum as $item) {
                $mode = SettingEnvModel::findFirst("code = '".$item['code']."'");
                if (empty($mode)) {
                    $mode           = new SettingEnvModel();
                    $mode->category = $map[$key];
                    $mode->sort     = $i;
                    $mode->is_show  = 1;
                    $mode->code     = $item['code'];
                    $mode->set_val  = '';
                    $mode->remark   = $item['remark'];
                    $mode->save();
                    continue;
                }

                $i++;
                $sql = "update setting_env set sort = ".$i.",category = ".$map[$key].",is_show = 1,updated_at = updated_at where code = '".$item['code']."'";
                $this->getDI()->get('db_backyard')->execute($sql);
            }
        }
    }


    public function rehireLntSalaryAction()
    {
        $staff   = \App\Models\backyard\HireTypeImportListModel::find([
            'conditions' => 'data_type = 2 and new_staff_id > 0',
        ])->toArray();
        $service = new FrontlineSalaryInfoService();
        foreach ($staff as $item) {
            $service->addReHireSalary([], ['staff_info_id' => $item['new_staff_id']]);
        }
    }

    public function remindManagerProbationAction()
    {
        $a = (new \App\Services\CriminalService())->remindManagerProbation(29316);
        var_dump($a);
    }

    public function staff_social_security_leave_dateAction($params)
    {
        $data = (new StaffService())->staffSocialSecurityLeaveDate($params[0], $params[1]);
        print_r($data);
        die;
    }

    public function prAction()
    {
        $a = [[1]];
        print_r(array_pop($a));
        die;
        $service                  = new \App\Modules\My\Services\PayrollReportService();
        $service->salaryCompanyId = 1;
        $service->taskFileName    = '22.xlsx';
        $url                      = $service->downLoadReportFile(2023, [119019], $type = 14, 1);
        var_dump($url);
    }

    public function tAction()
    {
        $staffVacationService = new StaffVacationService();
        $re                   = $staffVacationService->cancelOffDayLeave(56780, '2023-08-17',
            $params['remark'] ?? 'system cancel');
        dd($re);

        if (date('d', strtotime('2023-08-05')) >= 24) {
            $salaryPeriodStartDate = date('Y-m-24', strtotime('-1 month')); //上一发薪周期开始日期
        } else {
            $salaryPeriodStartDate = date('Y-m-24', strtotime('-2 month')); //上上一发薪周期开始日期
        }

        dd($salaryPeriodStartDate);

        $a = 1 .'sss';
        echo $a;
        die;
        $late_times = 60;

        $a = AttendanceUtil::lateOrEarlyMinutes2Hour($late_times / 60);
        var_dump($a);
        die;
        $params = json_decode(base64_decode('eyJyZWdpb25faWQiOiIiLCJwaWVjZV9pZCI6W10sInN0b3JlX2lkIjpbXSwic3RvcmVfa2luZCI6IiIsImdlb2dyYXBoeV9jb2RlIjpbXSwic3RhZmZfc3RhdGUiOlsxLDk5OV0sInN0YXJ0X2RhdGUiOiIyMDIzLTAyLTAxIiwiZW5kX2RhdGUiOiIyMDIzLTAyLTIxIiwic3RhZmZfaW5mb19pZCI6bnVsbCwic3RhZmZfaWQiOiI2MTU0MzEiLCJxdWVyeV90eXBlIjoxLCJsYW5nIjoiZW4tVVMiLCJmaWxlX25hbWUiOiJhdHRlbmRhbmNlX2JyYW5jaF9lbi1VU182M2Y1ODFhY2NkMDRlLnhsc3giLCJGcm9tIjoiZmJpIn0='),
            true);

        var_dump($params);
        die;

        $b = new BackyardAttendanceService();
        $v = $b->getOTToLeaveData('2022-01-01', '2022-12-31', [120088]);
        var_dump($v);
        die;


        $e_mail    = '<EMAIL>';
        $e_mail    = '<EMAIL>';
        $sendEmail = BiMail::send_salary(strval($e_mail), 'This is a test mailbox availability, please ignore it.',
            'This is a test mailbox availability, please ignore it.');
        var_dump($sendEmail);
    }


    public function fixvAction()
    {
        $vehicleTypeCategoryMY = [
            1 => 'Pick-up (sedan)',
            2 => 'Van',
            3 => 'Lorry',
            4 => 'Van Project',
            5 => 'Panel Van',
            6 => 'Window Van',
            7 => 'Car',
            8 => 'MPV',
        ];
        $db                    = $this->getDI()->get('db_rby');
        $sql                   = "SELECT uid,vehicle_type_category FROM vehicle_info WHERE vehicle_type_category >0";
        $relationData          = $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        foreach ($relationData as $relationDatum) {
            $info = HrStaffItemsModel::findFirst(" staff_info_id = ".$relationDatum['uid']." and item  = 'VEHICLE_TYPE_CATEGORY' ");

            if (empty($info)) {
                $info                = new HrStaffItemsModel();
                $info->staff_info_id = $relationDatum['uid'];
                $info->item          = 'VEHICLE_TYPE_CATEGORY';
                $info->value         = $vehicleTypeCategoryMY[$relationDatum['vehicle_type_category']];
                $info->save();
            }
        }
    }


    public function excelAction()
    {
        $aaa          = [
            61 => '6天班轮休',
            62 => '6天班周日休',
            51 => '5天班轮休',
            52 => '5天班周末休',
        ];
        $db           = $this->getDI()->get('db_rby');
        $sql          = "SELECT d.name ,j.job_name, department_id , job_id ,working_day_rest_type  FROM hr_job_department_relation as r LEFT JOIN  sys_department as d on d.id = r.department_id LEFT JOIN hr_job_title as j on j.id  = r.job_id ";
        $relationData = $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        //职位
        $job_title_service = new HrJobTitleService();
        $job_title         = $job_title_service->getJobTitleListFromCache();
        $job_title_list    = array_column($job_title, 'job_name', 'id');

        //部门
        $sysService      = new SysService();
        $department_list = $sysService->getDepartmentListFromCache();
        $department_list = array_column($department_list, 'name', 'id');

        foreach ($relationData as $datum) {
            $node_department_id = $datum['department_id'];
            $job_id             = $datum['job_id'];
            $sql                = "SELECT  `node_department_id`,`job_title`, `week_working_day`, `rest_type` from hr_staff_info where  node_department_id = {$node_department_id} and job_title = '$job_id' and  formal  = 1 and is_sub_staff = 0 and state  = 1 group by  `week_working_day`, `rest_type`";

            $staffData             = $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $working_day_rest_type = [];
            foreach ($staffData as $value) {
                $working_day_rest_type[] = $value['week_working_day'].$value['rest_type'];
            }
            $d_j_w_r = explode(',', $datum['working_day_rest_type']);
            $real    = array_diff($working_day_rest_type, $d_j_w_r);
            if (empty($real)) {
                continue;
            }

            //岗位对应轮休规则
            $d_j_w_r_array = [];
            foreach ($d_j_w_r as $item) {
                $d_j_w_r_array[] = $aaa[$item] ?? $item;
            }
            $d_j_w_r_str = implode(',', $d_j_w_r_array);

            foreach ($real as $item) {
                $working_day = $item[0] ?? null;
                $rest_type   = $item[1] ?? 0;
                $sql         = "SELECT staff_info_id,`name`, `sys_department_id`, `node_department_id`,`job_title`, `week_working_day`, `rest_type` from hr_staff_info where  node_department_id = {$node_department_id} and job_title = '$job_id' and week_working_day =  {$working_day} and rest_type = {$rest_type} and state  = 1 and  formal  = 1 and is_sub_staff = 0 and state  = 1";
                $staffData   = $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                foreach ($staffData as $staffing) {
                    $add[] = [
                        $staffing['staff_info_id'],
                        $staffing['name'],
                        $staffing['job_title'],
                        $job_title_list[$staffing['job_title']] ?? '',
                        $staffing['node_department_id'],
                        $department_list[$staffing['node_department_id']] ?? '',
                        $department_list[$staffing['sys_department_id']] ?? '',
                        $aaa[$staffing['week_working_day'].$staffing['rest_type']],
                        $d_j_w_r_str,
                    ];
                }
            }
        }
        $excel = new \Vtiful\Kernel\Excel([
            'path' => sys_get_temp_dir(),
        ]);

        $filename = env('country_code').'-working_day_rest_type.xlsx';
        //标题结束
        $excelF = $excel->fileName($filename);

        //这里写入标题
        $header = [
            'No.',
            'Name',
            'Position ID',
            'Position Title',
            'Department ID',
            'Department',
            '一级部门',
            '当前员工轮休规则',
            '岗位对应轮休规则',
        ];
        $data   = array_map('array_values', $add);

        $filePath = $excelF->header($header)->data($data)->output();

        $flashOss  = new FlashOss();
        $ossObject = 'test/'.date('YmdHis').'/'.$filename;
        $flashOss->uploadFile($ossObject, $filePath);
        $url = $flashOss->signUrl($ossObject, 5 * 86400);
        var_dump($url);
    }


    public function bbbAction($params)
    {
        $_condition = '';
        if (isset($params[0])) {
            $_condition = ' and staff_info_id = '.$params[0];
        }
        $staff = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'week_working_day = 5 and rest_type = 2 and is_sub_staff = 0 '.$_condition,
        ])->toArray();
        $b     = [];
        foreach ($staff as $item) {
            $transfer = HrStaffTransferModel::findFirst([
                'columns'    => 'staff_info_id,stat_date ',
                'conditions' => 'week_working_day = 6 and  staff_info_id =  '.$item['staff_info_id'],
                'order'      => 'stat_date desc',
            ]);
            if (empty($transfer)) {
                continue;
            }
            $stat_date = $transfer->stat_date;

            if ($stat_date < '2022-07-01') {
                continue;
            }
            $b[] = $item['staff_info_id'];
            $db  = $this->getDI()->get("db_backyard");
            $db->execute("delete from hr_staff_work_days  where date_at > '$stat_date' and staff_info_id = ".$item['staff_info_id']);
        }
        var_dump($b);
    }


    public function aaaAction()
    {
        try {
            $params['staff_info_id'] = 20278;
            $staffService            = new  StaffService();
            $staffService->delStaffOffDayPublicHolidayDay($params);
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    public function fixleavesourceAction($params)
    {
        $s         = new \App\Services\LeaveManagerService();
        $staff_ids = explode(',', $params[0]);
        foreach ($staff_ids as $item) {
            $re = $s->updateLeaveSource($item);
            var_dump($item, $re);
        }
    }

    public function fixholdAction()
    {
        $s                 = '{"type":1,"staff_info_id":55704,"payment_markup":10,"operator_id":-1,"stop_payment_type":"1,2"}';
        $params            = json_decode($s, true);
        $staff_hold_result = (new HoldManageService())->syncHrisStaffHoldSvc($params);
        var_dump($staff_hold_result);
        die;
    }

    public function mailAction()
    {
        $title = 'hello liuchunhua';
        $this->echoInfoLog($title, true);
    }

    public function fixBirthdayAction()
    {
    }

    public function emailAction($p)
    {
        $s  = new \App\Services\ProbationService();
        $re = $s->putFormalLog(123, 234, 345, 456, 567, 678);
        var_dump($re);
        die;


        $role = RolesModel::find(['columns' => 'id,name'])->toArray();

        $_str = " <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>";

        $rolePermission = HcmPermissionModel::find(
            [
                'columns'    => 'id,name,t_key',
                'conditions' => ' is_deleted = 0 and type =1 and ancestry > 0 ',
            ]
        )->toArray();


        $t     = BaseService::getTranslation('zh');
        $cai[] = ['id' => -1, 'name' => '菜单名称'];
        $a     = array_merge($cai, $role);

        $_str .= "<tr>";
        foreach ($a as $item) {
            $_str .= " <td>".$item['name']."</td>";
        }
        $_str .= "</tr>";
        foreach ($rolePermission as $v) {
            $_str .= "<tr>";
            $_str .= "<td>{$v['name']}</td>";

            foreach ($role as $s) {
                echo '--'.$s['id'].PHP_EOL;
                $dd   = HcmRolePermissionModel::findFirst("role_id = {$s['id']}");
                $_str .= "<td>".intval($dd && in_array($v['id'], explode(',', $dd->permission_ids)))."</td>";
            }
            $_str .= "</tr>";
            echo $v['id'].PHP_EOL;
        }
        $_str .= " </table>";
        $a    = BiMail::send(['<EMAIL>', '<EMAIL>'],
            strtoupper(env('country_code')).'-'.env('runtime').'-HCM角色菜单配合信息', $_str);
        var_dump($a);
    }

    public function leavePunchOutAction($params)
    {
        $flag = $params[0] == 1;

        $sql = "SELECT stop_begin_date ,staff_info_id  FROM triple_to_leave where type = 2 AND stop_begin_date >= '2021-11-11' AND stop_begin_date <= '2021-11-22' ";

        $staff_info = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        if (empty($staff_info)) {
            echo 'not find data';
        }


        $staff_info_ids = implode(',', array_unique(array_column($staff_info, 'staff_info_id')));
        echo '待补卡全部工号:'.$staff_info_ids.'&';

        $begin = '2021-11-10 15:59:59';
        $end   = '2021-11-22 16:00:00';
        $sql   = "select 
                    srbd.store_id
                    ,srbd.staff_info_id
                    ,IFNULL(sum(srbd.`receivable_amount`), 0) as not_pay
                    ,count(1) as cnt
                from store_receivable_bill_detail srbd 
                LEFT JOIN `sys_store` ss ON ss.id = srbd.store_id
                LEFT JOIN `staff_info` si ON si.id = srbd.staff_info_id
                 where srbd.`state`= 0  
                 and srbd.`receivable_type_category`!=7
                 and srbd.`receivable_amount` > 0
                 and srbd.`created_at` >= '{$begin}' AND srbd.`created_at` <='{$end}' 
                 and si.`formal` = 1 and srbd.staff_info_id in ($staff_info_ids)
                 GROUP BY srbd.store_id,srbd.staff_info_id
                 having count(1) > 1";

        $data = $this->getDI()->get('db_fle')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $un_pay_staff = array_column($data, 'staff_info_id');

        echo '以下员工有未回公款:'.implode(',', $un_pay_staff).'&';

        $normal_str = '';
        $not_pay    = '';//正常
        foreach ($staff_info as $datum) {
            if (in_array($datum['staff_info_id'], $un_pay_staff)) {
                $not_pay .= $datum['staff_info_id'].'|'.$datum['stop_begin_date'].',';
                continue;
            }
            $normal_str .= $datum['staff_info_id'].'|'.$datum['stop_begin_date'].',';
        }
        echo '未回公款的需要补卡:'.$not_pay.'& ';
        echo '正常补卡数据:'.$normal_str;
    }

    public function initPermissionAction()
    {
        return;
        echo 'start!'.PHP_EOL;
        $error            = [];
        $staff_permission = HcmStaffPermissionModel::find()->toArray();
        if (empty($staff_permission)) {
            exit(' not find staff_permission data');
        }
        foreach ($staff_permission as $item) {
            $permission         = explode(',', $item['permission_ids']);
            $res                = array_diff($permission, [2, 12]);
            $sp                 = HcmStaffPermissionModel::findFirst($item['id']);
            $sp->permission_ids = implode(',', $res);
            if ($sp->save() === false) {
                $error[] = $item['id'];
            }
        }
        if (!empty($error)) {
            echo 'update HcmStaffPermissionModel error:'.implode(',', $error);
        }

        $role_permission = HcmRolePermissionModel::find()->toArray();
        if (empty($role_permission)) {
            exit(' not find $role_permission data');
        }

        $error2 = [];
        foreach ($role_permission as $item) {
            $permission         = explode(',', $item['permission_ids']);
            $res                = array_diff($permission, [2, 12]);
            $sp                 = HcmRolePermissionModel::findFirst($item['id']);
            $sp->permission_ids = implode(',', $res);
            if ($sp->save() === false) {
                $error2[] = $item['id'];
            }
        }

        if (!empty($error2)) {
            echo 'update HcmRolePermissionModel error:'.implode(',', $error);
        }

        echo 'success';
    }

    public function clsAction()
    {
//        $leaveManagerService =
//        new \App\Services\LeaveManagerService();
//
//        $data = [[
//            "staff_info_id" => 119232,
//            "type" => 2,
//            "day" => '2022-07-19 00:00:00',
//            'leave_reason' => 21,
//            'leave_source' => 6, //批量导入
//            'operaterId' => 10000
//        ]];
//        $leaveManagerService->update_staff_state($data);


//        $staffInfoService =
//        new StaffInfoService();
//        var_dump(
//
//            $staffInfoService->StaffDataPurviewV2(87225)
//        );

    }


    public function test_to_mailAction()
    {
        //测试发送邮件
        $ret = (new \App\Library\Mailer($this->config))->send('<EMAIL>', 'test title',
            'test content');
        print_r($ret);
        exit();
    }

    /**
     * 查看员工数据权限
     * @param $params
     */
    public function checkPurviewAction($params)
    {
        if (empty($params[0])) {
            exit('请出入工号');
        }
        $purview = (new StaffInfoService())->StaffDataPurviewV2($params[0]);
        var_dump($purview);
    }


    public function hcmStaffPermissionAction($p)
    {
        $_str = " <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>";


        //获取 工号 姓名  部门  职位  （角色 TODO）
        $staffs = \App\Models\backyard\HrStaffInfoReadModel::find([
            'columns'    => 'staff_info_id,name,node_department_id,job_title',
            'conditions' => ' state = 1 and formal = 1  ',
        ]);

        //标题
        $header = [
            ['id' => -1, 'name' => '工号', 'group' => 1],
            ['id' => -1, 'name' => '姓名', 'group' => 1],
            ['id' => -1, 'name' => '部门', 'group' => 1],
            ['id' => -1, 'name' => '职位', 'group' => 1],
            ['id' => -1, 'name' => '角色', 'group' => 1],
            ['id' => -1, 'name' => '权限来源', 'group' => 1],
        ];


        $hcm_menu      = HcmPermissionModel::find(
            [
                'columns'    => 'id,ancestry as pid,name,t_key',
                'conditions' => ' is_deleted = 0 and type =1 and ancestry > 0 ',
            ]
        )->toArray();
        $hcm_menu_base = HcmPermissionModel::find(
            [
                'columns'    => 'id,ancestry as pid,name,t_key',
                'conditions' => ' is_deleted = 0 and type =1 and ancestry = 0 ',
            ]
        )->toArray();
        $t             = BaseService::getTranslation('zh');


        foreach ($hcm_menu_base as $b) {
            $base_menu[$b['id']] = $b['t_key'] ? $t->_($b['t_key']) : $b['name'];
        }

        $a = array_merge($header, $hcm_menu);

        //标题开始
        $_str .= "<tr>";
        foreach ($a as $item) {
            $pre = isset($item['pid']) ? $base_menu[$item['pid']].'-' : '';


            if (isset($item['t_key'])) {
                $_str .= " <td>".$pre.($item['t_key'] ? $t->_($item['t_key']) : $item['name'])."</td>";
            } else {
                $_str .= " <td>".$pre.$item['name']."</td>";
            }
        }
        $_str .= "</tr>";
        //标题结束

        //静态数据
        $role       = array_column(RolesModel::find(['columns' => 'id,name'])->toArray(), 'name', 'id');
        $department = array_column((new \App\Services\SysService())->getDepartmentList(), 'name', 'id');
        $job_title  = array_column(\App\Models\backyard\HrJobTitleModel::find()->toArray(), 'job_name', 'id');

        foreach ($staffs as $v) {
            //查询角色
            $staff_role = StaffInfoPositionModel::find([
                'columns'    => 'staff_info_id,position_category',
                'conditions' => 'staff_info_id = '.$v['staff_info_id'].' and position_category not in (1,2,4,109)',
            ])->toArray();
            if (empty($staff_role)) {
                continue;
            }

            $staff_role_name = $staff_role_id = [];
            $_str            .= "<tr>";
            $_str            .= "<td>{$v['staff_info_id']}</td>";
            $_str            .= "<td>{$v['name']}</td>";
            $_str            .= "<td> {$department[$v['node_department_id']]} </td>";
            $_str            .= "<td> {$job_title[$v['job_title']]} </td>";

            if ($staff_role) {
                foreach ($staff_role as $item) {
                    $staff_role_id[] = $item['position_category'];
                    //员工的角色
                    $staff_role_name[] = $role[$item['position_category']];
                }
                $_str .= "<td>".implode(',', $staff_role_name)."</td>";//角色
            } else {
                $_str .= "<td>--</td>";
            }
            $staff_permission = HcmStaffPermissionModel::findFirst([
                'conditions' => ' staff_id  = :staff_id:',
                'bind'       => ['staff_id' => $v['staff_info_id']],
            ]);
            $role_permission  = HcmRolePermissionModel::find([
                'conditions' => ' role_id in ({staff_role:array})  ',
                'bind'       => ['staff_role' => $staff_role_id],
            ])->toArray();

            $_str .= $staff_permission ? "<td>工号</td>" : "<td>角色</td>";
            foreach ($hcm_menu as $m) {
                $default_str = "<td>0</td>";

                if ($staff_permission) {
                    $default_str = in_array($m['id'],
                        explode(',', $staff_permission->permission_ids)) ? "<td>1</td>" : "<td>0</td>";
                } else {
                    if ($role_permission) {
                        $_role_permission = explode(',',
                            implode(',', array_column($role_permission, 'permission_ids')));
                        if (in_array($m['id'], $_role_permission)) {
                            $default_str = "<td>1</td>";
                        }
                    }
                }
                $_str .= $default_str;
            }

            $_str .= "</tr>";
        }
        $_str .= " </table>";
        $a    = BiMail::send(['<EMAIL>'],
            strtoupper(env('country_code')).'-'.env('runtime').'-HCM角色菜单配合信息', $_str);
        var_dump($a);
    }

    public function WinStaffPermissionAction($p)
    {
        $_str = " <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>";

        //获取 工号 姓名  部门  职位  （角色 TODO）
        $staffs = \App\Models\backyard\HrStaffInfoReadModel::find([
            'columns'    => 'staff_info_id,name,node_department_id,job_title',
            'conditions' => ' state = 1 and formal = 1  ',
        ]);

        //标题
        $header = [
            ['id' => -1, 'name' => '工号', 'group' => 1],
            ['id' => -1, 'name' => '姓名', 'group' => 1],
            ['id' => -1, 'name' => '部门', 'group' => 1],
            ['id' => -1, 'name' => '职位', 'group' => 1],
            ['id' => -1, 'name' => '角色', 'group' => 1],
        ];


        $win_menu = \App\Models\backyard\HrPermissionAuth::find(
            [
                'columns'    => 'id, pid,describe as name',
                'conditions' => ' pid > 1 ',
            ]
        )->toArray();

        $a = array_merge($header, $win_menu);

        //标题开始
        $_str .= "<tr>";
        foreach ($a as $item) {
            $_str .= " <td>".$item['name']."</td>";
        }
        $_str .= "</tr>";
        //标题结束

        //静态数据
        $role       = array_column(RolesModel::find(['columns' => 'id,name'])->toArray(), 'name', 'id');
        $department = array_column((new \App\Services\SysService())->getDepartmentList(), 'name', 'id');
        $job_title  = array_column(\App\Models\backyard\HrJobTitleModel::find()->toArray(), 'job_name', 'id');

        foreach ($staffs as $v) {
            //查询角色
            $staff_role = StaffInfoPositionModel::find([
                'columns'    => 'staff_info_id,position_category',
                'conditions' => 'staff_info_id = '.$v['staff_info_id'].' and position_category not in (1,2,4,109)',
            ])->toArray();
            if (empty($staff_role)) {
                continue;
            }

            $staff_role_name = $staff_role_id = [];
            $_str            .= "<tr>";
            $_str            .= "<td>{$v['staff_info_id']}</td>";
            $_str            .= "<td>{$v['name']}</td>";
            $_str            .= "<td> {$department[$v['node_department_id']]} </td>";
            $_str            .= "<td> {$job_title[$v['job_title']]} </td>";

            if ($staff_role) {
                foreach ($staff_role as $item) {
                    $staff_role_id[] = $item['position_category'];
                    //员工的角色
                    $staff_role_name[] = $role[$item['position_category']];
                }
                $_str .= "<td>".implode(',', $staff_role_name)."</td>";//角色
            } else {
                $_str .= "<td>--</td>";
            }
            $staff_permission = \App\Models\backyard\HrPermissionRelation::find([
                'conditions' => ' staff_id  = :staff_id:',
                'columns'    => 'staff_id,auth_id',
                'bind'       => ['staff_id' => $v['staff_info_id']],
            ])->toArray();

            $staff_permission = array_column($staff_permission, 'auth_id');


            foreach ($win_menu as $m) {
                $default_str = in_array($m['id'], $staff_permission) ? "<td>1</td>" : "<td>0</td>";

                $_str .= $default_str;
            }

            $_str .= "</tr>";
        }
        $_str .= " </table>";
        $a    = BiMail::send(['<EMAIL>'],
            strtoupper(env('country_code')).'-'.env('runtime').'-WinHr角色菜单配合信息', $_str);
        var_dump($a);
    }

    public function tttwinAction($p)
    {
        //获取 工号 姓名  部门  职位  （角色 TODO）
        $staffs = \App\Models\backyard\HrStaffInfoReadModel::find([
            'columns'    => 'staff_info_id,name,node_department_id,job_title',
            'conditions' => ' state = 1 and formal = 1  ',
        ]);

        //标题
        $header = [
            ['id' => -1, 'name' => '工号', 'group' => 1],
            ['id' => -1, 'name' => '姓名', 'group' => 1],
            ['id' => -1, 'name' => '部门', 'group' => 1],
            ['id' => -1, 'name' => '职位', 'group' => 1],
            ['id' => -1, 'name' => '角色', 'group' => 1],
        ];


        $win_menu = \App\Models\backyard\HrPermissionAuth::find(
            [
                'columns'    => 'id, pid,describe as name',
                'conditions' => ' pid > 1 ',
            ]
        )->toArray();

        $a = array_merge($header, $win_menu);

        $excel = new \Vtiful\Kernel\Excel([
            'path' => sys_get_temp_dir(),
        ]);

        $filename = env('country_code').'-win-menu.xlsx';
        //标题结束
        $excelF = $excel->fileName($filename);

        //这里写入标题
        foreach ($a as $k => $v) {
            $title[] = $v['name'];
        }
        //静态数据
        $role       = array_column(RolesModel::find(['columns' => 'id,name'])->toArray(), 'name', 'id');
        $department = array_column((new \App\Services\SysService())->getDepartmentList(), 'name', 'id');
        $job_title  = array_column(\App\Models\backyard\HrJobTitleModel::find()->toArray(), 'job_name', 'id');
        $add        = [];
        foreach ($staffs as $key => $v) {
            //查询角色
            $staff_role = StaffInfoPositionModel::find([
                'columns'    => 'staff_info_id,position_category',
                'conditions' => 'staff_info_id = '.$v['staff_info_id'].' and position_category not in (1,2,4,109)',
            ])->toArray();
            if (empty($staff_role)) {
                continue;
            }

            $staff_role_name = $staff_role_id = [];

            $add[$key]['a'] = $v['staff_info_id'];
            $add[$key]['b'] = $v['name'];
            $add[$key]['c'] = $department[$v['node_department_id']];
            $add[$key]['d'] = $job_title[$v['job_title']];

            if ($staff_role) {
                foreach ($staff_role as $item) {
                    $staff_role_id[] = $item['position_category'];
                    //员工的角色
                    $staff_role_name[] = $role[$item['position_category']];
                }
                $add[$key]['e'] = implode(',', $staff_role_name);
            } else {
                $add[$key]['e'] = '--';
            }
            $staff_permission = \App\Models\backyard\HrPermissionRelation::find([
                'conditions' => ' staff_id  = :staff_id:',
                'columns'    => 'staff_id,auth_id',
                'bind'       => ['staff_id' => $v['staff_info_id']],
            ])->toArray();

            $staff_permission = array_column($staff_permission, 'auth_id');


            foreach ($win_menu as $kk => $m) {
                $add[$key]['f'.$kk] = in_array($m['id'], $staff_permission) ? 1 : 0;
            }
        }
        $data = array_map('array_values', $add);

        $filePath = $excelF->header($title)->data($data)->output();

        $flashOss  = new FlashOss();
        $ossObject = 'test/'.date('Y-m-d').'/'.$filename;
        $flashOss->uploadFile($ossObject, $filePath);
        $url = $flashOss->signUrl($ossObject, 5 * 86400);
        var_dump($url);
    }

    public function ttthcmAction($p)
    {
        //获取 工号 姓名  部门  职位  （角色 TODO）
        $staffs = \App\Models\backyard\HrStaffInfoReadModel::find([
            'columns'    => 'staff_info_id,name,node_department_id,job_title',
            'conditions' => ' state = 1 and formal = 1  ',
        ]);

        //标题
        $header = [
            ['id' => -1, 'name' => '工号', 'group' => 1],
            ['id' => -1, 'name' => '姓名', 'group' => 1],
            ['id' => -1, 'name' => '部门', 'group' => 1],
            ['id' => -1, 'name' => '职位', 'group' => 1],
            ['id' => -1, 'name' => '角色', 'group' => 1],
            ['id' => -1, 'name' => '权限来源', 'group' => 1],
        ];


        $hcm_menu      = HcmPermissionModel::find(
            [
                'columns'    => 'id,ancestry as pid,name,t_key',
                'conditions' => ' is_deleted = 0 and type =1 and ancestry > 0 ',
            ]
        )->toArray();
        $hcm_menu_base = HcmPermissionModel::find(
            [
                'columns'    => 'id,ancestry as pid,name,t_key',
                'conditions' => ' is_deleted = 0 and type =1 and ancestry = 0 ',
            ]
        )->toArray();
        $t             = BaseService::getTranslation('zh');


        foreach ($hcm_menu_base as $b) {
            $base_menu[$b['id']] = $b['t_key'] ? $t->_($b['t_key']) : $b['name'];
        }

        $excel = new \Vtiful\Kernel\Excel([
            'path' => sys_get_temp_dir(),
        ]);

        $filename = env('country_code').'-hcm-menu.xlsx';
        //标题结束
        $excelF = $excel->fileName($filename);


        $a = array_merge($header, $hcm_menu);

        //标题开始

        foreach ($a as $item) {
            $pre = isset($item['pid']) ? $base_menu[$item['pid']].'-' : '';
            if (isset($item['t_key'])) {
                $title[] = $pre.($item['t_key'] ? $t->_($item['t_key']) : $item['name']);
            } else {
                $title[] = $pre.$item['name'];
            }
        }

        //标题结束

        //静态数据
        $role       = array_column(RolesModel::find(['columns' => 'id,name'])->toArray(), 'name', 'id');
        $department = array_column((new \App\Services\SysService())->getDepartmentList(), 'name', 'id');
        $job_title  = array_column(\App\Models\backyard\HrJobTitleModel::find()->toArray(), 'job_name', 'id');

        foreach ($staffs as $key => $v) {
            //查询角色
            $staff_role = StaffInfoPositionModel::find([
                'columns'    => 'staff_info_id,position_category',
                'conditions' => 'staff_info_id = '.$v['staff_info_id'].' and position_category not in (1,2,4,109)',
            ])->toArray();
            if (empty($staff_role)) {
                continue;
            }

            $staff_role_name = $staff_role_id = [];

            $add[$key]['a'] = $v['staff_info_id'];
            $add[$key]['b'] = $v['name'];
            $add[$key]['c'] = $department[$v['node_department_id']];
            $add[$key]['d'] = $job_title[$v['job_title']];


            if ($staff_role) {
                foreach ($staff_role as $item) {
                    $staff_role_id[] = $item['position_category'];
                    //员工的角色
                    $staff_role_name[] = $role[$item['position_category']];
                }
                $add[$key]['e'] = implode(',', $staff_role_name);
            } else {
                $add[$key]['e'] = '--';
            }
            $staff_permission = HcmStaffPermissionModel::findFirst([
                'conditions' => ' staff_id  = :staff_id:',
                'bind'       => ['staff_id' => $v['staff_info_id']],
            ]);
            $role_permission  = HcmRolePermissionModel::find([
                'conditions' => ' role_id in ({staff_role:array})  ',
                'bind'       => ['staff_role' => $staff_role_id],
            ])->toArray();

            $add[$key]['f'] = $staff_permission ? "工号" : "角色";
            foreach ($hcm_menu as $nn => $m) {
                $default_str = 0;

                if ($staff_permission) {
                    $default_str = in_array($m['id'], explode(',', $staff_permission->permission_ids)) ? 1 : 0;
                } else {
                    if ($role_permission) {
                        $_role_permission = explode(',',
                            implode(',', array_column($role_permission, 'permission_ids')));
                        if (in_array($m['id'], $_role_permission)) {
                            $default_str = 1;
                        }
                    }
                }
                $add[$key]['g'.$nn] = $default_str;
            }
        }
        $data = array_map('array_values', $add);

        $filePath = $excelF->header($title)->data($data)->output();

        $flashOss  = new FlashOss();
        $ossObject = 'test/'.date('Y-m-d').'/'.$filename;
        $flashOss->uploadFile($ossObject, $filePath);
        $url = $flashOss->signUrl($ossObject, 5 * 86400);
        var_dump($url);
    }

    public function tttbiAction($p)
    {
        //获取 工号 姓名  部门  职位  （角色 TODO）
        $staffs = \App\Models\backyard\HrStaffInfoReadModel::find([
            'columns'    => 'staff_info_id,name,node_department_id,job_title',
            'conditions' => ' state = 1 and formal = 1  ',
        ]);

        //标题
        $header = [
            ['id' => -1, 'name' => '工号', 'group' => 1],
            ['id' => -1, 'name' => '姓名', 'group' => 1],
            ['id' => -1, 'name' => '部门', 'group' => 1],
            ['id' => -1, 'name' => '职位', 'group' => 1],
            ['id' => -1, 'name' => '角色', 'group' => 1],
            ['id' => -1, 'name' => '权限来源', 'group' => 1],
        ];


        $bi_menu = \App\Models\bi\BiMenusModel::find(
            [
                'columns'    => 'id,name,parentid,name_key',
                'conditions' => " status = 1 and parentid  in ( 72,48) and  name_key in ('attendance_statistics','cer_download','criminal_record_infomation','flash_staff_hold_management','hr_salary_hold_management','three_days_not_remittance','triple_not_attend','warning_menu')",
            ]
        )->toArray();


        $bi_menu_base  = \App\Models\bi\BiMenusModel::find(
            [
                'columns'    => 'id,name,parentid,name_key',
                'conditions' => ' id in ( 72,48) ',
            ]
        )->toArray();
        $all_menu      = array_merge($bi_menu_base, $bi_menu);
        $check_menu_id = array_column($bi_menu, 'id');
        $t_key         = array_column($all_menu, 'name_key');

        $bi_t = array_column(\App\Models\bi\BiTranslationsModel::find([
            'conditions' => " t_key in ({t_key:array}) and lang ='zh-CN' ",
            'bind'       => ['t_key' => $t_key],
        ])->toArray(), 't_value', 't_key');


        foreach ($bi_menu_base as $b) {
            $base_menu[$b['id']] = $bi_t[$b['name_key']];
        }

        $excel = new \Vtiful\Kernel\Excel([
            'path' => sys_get_temp_dir(),
        ]);

        $filename = env('country_code').'-fbi-menu.xlsx';
        //标题结束
        $excelF = $excel->fileName($filename);


        $a = array_merge($header, $bi_menu);

        //标题开始

        foreach ($a as $item) {
            $pre = isset($item['parentid']) ? $base_menu[$item['parentid']].'-' : '';

            $title[] = isset($item['name_key']) ? ($pre.$bi_t[$item['name_key']]) : $item['name'];
        }

        //标题结束

        //静态数据
        $role       = array_column(RolesModel::find(['columns' => 'id,name'])->toArray(), 'name', 'id');
        $department = array_column((new \App\Services\SysService())->getDepartmentList(), 'name', 'id');
        $job_title  = array_column(\App\Models\backyard\HrJobTitleModel::find()->toArray(), 'job_name', 'id');

        foreach ($staffs as $key => $v) {
            //查询角色
            $staff_role = StaffInfoPositionModel::find([
                'columns'    => 'staff_info_id,position_category',
                'conditions' => 'staff_info_id = '.$v['staff_info_id'].' and position_category not in (1,2,4,109)',
            ])->toArray();
            if (empty($staff_role)) {
                continue;
            }

            $staff_role_id = array_column($staff_role, 'position_category');

            $staff_permission = \App\Models\bi\BiRoleStaffMenusModel::find([
                'conditions' => ' staff_info_id  = :staff_id: and menu_id in ({menu_id:array}) ',
                'bind'       => [
                    'staff_id' => $v['staff_info_id'],
                    'menu_id'  => $check_menu_id,
                ],
            ])->toArray();
            $role_permission  = \App\Models\bi\BiRoleMenusModel::find([
                'conditions' => ' role_id in ({staff_role:array}) and menu_id in ({menu_id:array}) ',
                'bind'       => [
                    'staff_role' => $staff_role_id,
                    'menu_id'    => $check_menu_id,
                ],
            ])->toArray();
            $role_permission  = array_column($role_permission, 'menu_id');
            $staff_permission = array_column($staff_permission, 'menu_id');

            if (empty($staff_permission) && empty($role_permission)) {
                continue;
            }

            $add[$key]['a'] = $v['staff_info_id'];
            $add[$key]['b'] = $v['name'];
            $add[$key]['c'] = $department[$v['node_department_id']];
            $add[$key]['d'] = $job_title[$v['job_title']];

            $staff_role_name = [];
            if ($staff_role) {
                foreach ($staff_role as $item) {
                    //员工的角色
                    $staff_role_name[] = $role[$item['position_category']];
                }
                $add[$key]['e'] = implode(',', $staff_role_name);
            } else {
                $add[$key]['e'] = '--';
            }

            $add[$key]['f'] = $staff_permission ? "工号" : "角色";
            foreach ($bi_menu as $nn => $m) {
                $default_str = 0;
                if ($staff_permission) {
                    $default_str = in_array($m['id'], $staff_permission) ? 1 : 0;
                } else {
                    if ($role_permission) {
                        if (in_array($m['id'], $role_permission)) {
                            $default_str = 1;
                        }
                    }
                }
                $add[$key]['g'.$nn] = $default_str;
            }
        }
        $data = array_map('array_values', $add);

        $filePath = $excelF->header($title)->data($data)->output();

        $flashOss  = new FlashOss();
        $ossObject = 'test/'.date('Y-m-d').'/'.$filename;
        $flashOss->uploadFile($ossObject, $filePath);
        $url = $flashOss->signUrl($ossObject, 5 * 86400);
        var_dump($url);
    }


    public function hcmRoleAction()
    {
        $excel = new \Vtiful\Kernel\Excel([
            'path' => sys_get_temp_dir(),
        ]);

        $filename = env('country_code').'-hcm-role-menu.xlsx';
        //标题结束
        $excelF = $excel->fileName($filename);


        $hcm_menus = HcmPermissionModel::find(
            [
                'columns'    => 'id,ancestry as pid,name,t_key',
                'conditions' => ' is_deleted = 0 ',
            ]
        )->toArray();
        $role      = array_column(RolesModel::find(['columns' => 'id,name'])->toArray(), 'name', 'id');
        $t         = BaseService::getTranslation('zh');
        foreach ($hcm_menus as $it) {
            $hcm_menu[$it['id']] = $it['t_key'] ? $t->_($it['t_key']) : $it['name'];
        }

        $hcm_menu            = array_column($hcm_menu, 't_key', 'id');
        $hcm_role_permission = HcmRolePermissionModel::find()->toArray();
        $title[]             = ['角色', '权限'];


        foreach ($hcm_role_permission as $key => $rp) {
            $_rp = explode(',', $rp['permission_ids']);

            foreach ($_rp as $v) {
                $add['a'] = $role[$rp['role_d']];
                $add['b'] = $hcm_menu[$v];
                $a[]      = $add;
            }
        }

        var_dump($a);
        die;
        $data = array_map('array_values', $a);

        $filePath = $excelF->header($title)->data($data)->output();

        $flashOss  = new FlashOss();
        $ossObject = 'test/'.date('Y-m-d').'/'.$filename;
        $flashOss->uploadFile($ossObject, $filePath);
        $url = $flashOss->signUrl($ossObject, 5 * 86400);
        var_dump($url);
    }


    public function excBiSqlAction($param)
    {
        if ($param[0]) {
            $sql = $param[0];
        }

        if (empty($sql)) {
            die(' sql empty');
        }
        $db_bi = $this->getDI()->get('db_wbi');
        $flag  = $db_bi->execute($sql);
        var_dump($flag);
    }

    public function outOrderAction()
    {
    }


    //泰国发送pdf
    public function sendNoticeAction()
    {
        $date = '2022-06-02';
        echo date('Y-m', strtotime($date) - 30 * 86400);;
    }

    public function get_HR_Mail_contentAction()
    {
        foreach (['th', 'my', 'id', 'la', 'ph', 'vn'] as $countryCode) {
            $staffAbsentSuspensionService = 'App\Modules\\'.$countryCode.'\Services\StaffAbsentSuspensionService';
            if (class_exists($staffAbsentSuspensionService)) {
                /**
                 * @see App\Modules\Ph\Services\StaffAbsentSuspensionService
                 * @see App\Modules\La\Services\StaffAbsentSuspensionService
                 * @see App\Modules\Vn\Services\StaffAbsentSuspensionService
                 * @see App\Modules\Id\Services\StaffAbsentSuspensionService
                 * @see App\Modules\My\Services\StaffAbsentSuspensionService
                 */
                $service = new $staffAbsentSuspensionService;
            } else {
                $service = new \App\Services\StaffAbsentSuspensionService();
            }
            echo $service->getHRMailContent("message");
        }
    }

    public function checkTableNewAction()
    {
        echo date('Y-m-d H:i:s').PHP_EOL;
        //标题
        $tables = [
            ['field' => 'updated_at', 'name' => 'hr_staff_info',],
            ['field' => 'updated_at', 'name' => 'hr_staff_items',],
            ['field' => 'created_at', 'name' => 'hr_staff_info_position',],
            ['field' => 'created_at', 'name' => 'hr_staff_department_store_regions_piece',],
            ['field' => 'updated_at', 'name' => 'remove_hr_staff_info',],
            ['field' => 'updated_att', 'name' => 'hr_emails',],
            ['field' => 'created_at', 'name' => 'hr_staff_logs',],
            ['field' => 'created_at', 'name' => 'hr_operate_logs',],
            ['field' => 'create_at', 'name' => 'hr_staff_transfer_log',],
            ['field' => 'created_at', 'name' => 'hr_staff_attachment',],
            ['field' => 'updated_at', 'name' => 'hr_staff_salary',],
            ['field' => 'updated_at', 'name' => 'triple_to_leave',],
        ];


        foreach ($tables as $item) {
            $sql = " select max(".$item['field'].") as time ,count(*) as total from ".$item['name'];
            $bi  = $this->getDI()->get('db_rbi')->fetchOne($sql);
            $sql = " select max(".$item['field'].") as time ,count(*) as total from ".$item['name'];
            $by  = $this->getDI()->get('db_backyard')->fetchOne($sql);
            echo " bi:'".$bi['time']."' total :  ".$bi['total']." by:'".$by['time']."' total : ".$by['total'].' '.$item['name'].PHP_EOL;
        }
    }


    public function testgetAuditImgAction($a)
    {
        print_r($_SERVER['argv']);
        exit();
        $audit_ids = [1, 2, 1];
        $audit_ids = '1,2,3,168,168';
        $data      = (new StaffInfoAuditService())->getAuditImg($audit_ids);
        var_dump($data);
    }

    public function checkTableAction()
    {
        echo date('Y-m-d H:i:s').PHP_EOL;
        //标题
        $tables = [
            ['field' => 'updated_at', 'name' => 'hr_staff_info',],
            ['field' => 'updated_at', 'name' => 'hr_staff_items',],
            ['field' => 'created_at', 'name' => 'hr_staff_info_position',],
            ['field' => 'created_at', 'name' => 'hr_staff_department_store_regions_piece',],
            ['field' => 'updated_at', 'name' => 'remove_hr_staff_info',],
            ['field' => 'updated_att', 'name' => 'hr_emails',],
            ['field' => 'created_at', 'name' => 'hr_staff_logs',],
            ['field' => 'created_at', 'name' => 'hr_operate_logs',],
            ['field' => 'create_at', 'name' => 'hr_staff_transfer_log',],
            ['field' => 'created_at', 'name' => 'hr_staff_attachment',],
            ['field' => 'updated_at', 'name' => 'hr_staff_salary',],
            ['field' => 'updated_at', 'name' => 'triple_to_leave',],
        ];


        foreach ($tables as $item) {
            $sql = " select max(".$item['field'].") as time from ".$item['name'];
            $bi  = $this->getDI()->get('db_rbi')->fetchOne($sql);
            $sql = " select max(".$item['field'].") as time from ".$item['name'];
            $by  = $this->getDI()->get('db_backyard')->fetchOne($sql);
            echo " bi:'".$bi['time']."' "." by:'".$by['time']."'".$item['name'].PHP_EOL;
        }
    }

    public function updateEnvAction($params)
    {
        //sub_staff_support_switch
        $code    = $params[0];
        $set_val = $params[1];
        $model   = \App\Models\backyard\SettingEnvModel::findFirstByCode($code);
        if ($model) {
            $model->set_val = $set_val;
            var_dump($model->save());
        }
    }

    public function attendanceV2Action()
    {
        $table = AttendanceDataV2Model::class;

        $date           = date('Y-m-d', strtotime('-1 days'));
        $attendance_sql = "select
              count(1) as num
            from {$table}
            where  stat_date = '{$date}'  ";
        $c              = $this->modelsManager->executeQuery($attendance_sql)->toArray();
        var_dump($date, $c);
        die;
    }

    public function do_absenteeismAction()
    {
        $service                          = (new   \App\Services\StaffAbsentSuspensionService());
        $service->staffAbsent             = [777777, 888888];
        $service->staffAbsentDate[777777] = ['2022-06-01', '2022-06-02', '2022-06-03'];
        $service->staffAbsentDate[888888] = ['2022-06-01', '2022-06-02', '2022-06-03'];
        $ee                               = $service->doAbsenteeism();
        var_dump($ee);
    }

    public function hAction()
    {
        var_dump($this->get_week_days('2022-06-02', 0, ['2020-05-30', '2020-05-31']));
    }

    //跟别的国家不一样
    public function get_week_days($date, $is_array = 0, $holidays)
    {
        $date_num = date('w', strtotime($date));
        if ($date_num == 0) {
            $date_num = 7;
        }
        $week[] = $date;
        $num    = 0;
        for ($i = $date_num; $i > 1; $i--) {
            $num++;
            $week[] = date('Y-m-d', strtotime("{$date} -{$num} day"));
        }

        $num = 0;
        for ($i = $date_num; $i < 7; $i++) {
            $num++;
            $week[] = date('Y-m-d', strtotime("{$date} +{$num} day"));
        }
        sort($week);//日期所在周 区间

        $staff_week = [];
        foreach ($week as $k => $d) {
            if (!in_array($d, $holidays)) {
                $staff_week[] = $d;
            }
        }

        return $staff_week;
    }


    public function testAction()
    {
        $server = (new   \App\Services\OutsourcingPushSettingsService());
        $server::setLanguage('zh');
        $server->consumptionRedisList(['company_name_ef' => '111', 'mail_data' => '<EMAIL>']);
    }

    public function ascAction()
    {
        $aa = \App\Services\ConditionsRulesService::getInstance()
            ->setRuleKey('leave_special_branch_belongs')
            ->setParameters(['staff_info_id' => 120627])
            ->getConfig();
        var_dump($aa);
        die;
    }

    public function salary_traAction($params)
    {
        $start = $params[0];
        $end   = $params[1];
        var_dump([$start, $end]);
        var_dump((new \App\Modules\Ph\Services\GongziService())->getEndDataStaffTransfer($start, $end));
    }

    public function pdAction($params)
    {
        try {
            $start                   = $params[0] ?? '2023-11-24';
            $end                     = $params[1] ?? '2023-12-23';
            $param['start_date']     = $start;
            $param['end_date']       = $end;
            $param['staff_info_ids'] = [120631, 120606];
            if (!empty($params[2])) {
                $param['staff_info_ids'] = explode(',', $params[2]);
            }
            $server = new BackyardAttendanceService();
            $b      = $server->pd_data($param);
            var_dump($b);
        } catch (\Exception $e) {
            echo $e->getTraceAsString();
            exit;
        }
    }

    public function get_staff_maAction()
    {
        $builder3_sql[] = ' sys_store_id IN ({sys_store_ids:array}) ';
        print_r(implode(' or ', $builder3_sql));
        exit();

        $staffInfoService = new StaffInfoService();
//
        $hrbpIds = $staffInfoService->findHRBP(105, ['store_id' => 'TH01010101']);
//
        print_r($hrbpIds);
//        exit();
//       print_r((new \App\Services\StaffService())->setExpire(60 * 10)->getStaffJurisdictionFromCache(10000));
    }


    public function fixStaffPhAction()
    {
        $special_ph_day_2022_department_job_title = explode(',',
            (new SettingEnvService())->getSetVal('department_jobtitle_ph'));
        $special_ph_day_2022_staffs               = explode(',', (new SettingEnvService())->getSetVal('staff_ph'));


        $sql = "";
    }

    public function testacAction()
    {
        $staffDepartments = (new  StaffService())->getStaffManageDepartment(56780);
        print_r($staffDepartments);
    }

    public function autoBlackAction()
    {
        $db = $this->getDI()->get('db_backyard');
        //定时发送状态更改为 已发送
        $up_sql = "update hr_blacklist set status = 1 where remark='cto' ";
        $re     = $db->execute($up_sql);
        var_dump($re);
    }

    public function onsiteAction($params)
    {
        $start_date = $params[0] ?? date('Y-m-24', strtotime('-1 month'));
        $end_date   = $params[1] ?? date('Y-m-23');
        $staff_ids  = !empty($params[2]) ? explode(',', $params[2]) : '';

        //获取数据
        $onsite_params          = ['start_date' => $start_date, 'end_date' => $end_date, 'staff_ids' => $staff_ids];
        $onsiteAllowanceService = new \App\Services\OnsiteAllowanceService();
        $result                 = $onsiteAllowanceService->getOnsiteAllowance($onsite_params);
        $onsiteAllowanceService->onsiteAllowanceToAttendanceMonth(date('Y-m', strtotime($end_date)),
            array_column($result, 'onsite_allowance', 'staff_info_id'));
        var_dump($result);
    }


    public function test_whiteAction()
    {
        $attendance_white_list = (new AttendanceWhiteListService())->getAllCycleWhiteList([
            'start_date' => '2023-02-01',
            'end_date'   => '2023-02-28',
        ]);
        print_r($attendance_white_list);
    }

    public function getStaffSalaryAction($params)
    {
        $staffIds            = $params[0] ?? '118673';
        $staffIds            = explode(',', $staffIds);
        $service             = (new \App\Modules\My\Services\PayrollCalculationService());
        $service->runType    = 1;
        $service->cycleStart = '2022-01-01';
        $service->cycleEnd   = '2023-12-31';
        $data                = $service->getStaffSalary($staffIds);
        var_dump($data);
    }

    public function cal_salaryAction($params)
    {
        $service             = (new \App\Modules\My\Services\PayrollCalculationService());
        $service->runType    = 1;
        $service->cycleStart = '2022-01-01';
        $service->cycleEnd   = '2023-12-31';
//        $data = $service->getBaseInfo([21848,17165]);
//        var_dump($service->fixedWeekWorkingDay);
        $calSocsoPaid = $service->calSocsoPaid(70, 1);
        $calSocsoPaid = $service->calSocsoPaid(70, 2);
        $calSocsoPaid = $service->calSocsoPaid(200, 1);
        var_dump($calSocsoPaid);
    }

    public function cal_salary_carAction($params)
    {
        $service = (new \App\Modules\My\Services\PayrollCalculationService());
        // $service->attendanceDataCover[12345]['pd_days_transfer_before']= 10;
        $service->attendanceDataCover[12345]['pd_days_transfer_after'] = 5;
        $itemSalary['amount']                                          = 1;
        $itemSalary['start_date']                                      = '2022-01-01';
        $itemSalary['end_date']                                        = '2022-01-30';
        $service->pdDays[12345]['2022-01-01']                          = 1;
        $service->pdDays[12345]['2022-01-30']                          = 1;
        $data                                                          = $service->carUnfixedAmount(12345, $itemSalary,
            0);
        $data                                                          = $service->carUnfixedAmount(12345, $itemSalary,
            1);
//        $data = $service->carUnfixedAmount(12345, $itemSalary, 2);
//        $data = $service->carUnfixedAmount(12345, $itemSalary, 3);
        var_dump($data);
    }

    public function cal_taxAction($params)
    {
        $service                = (new \App\Modules\My\Services\PayrollCalculationService());
        $service->salaryBase    = $service->initSalaryBase();
        $service->payrollSalary = [
            [
                'salary_base_id' => 1,
                'amount_base'    => 10000.00,
                'amount'         => 10000.00,
                'staff_info_id'  => 119402,
            ],
            [
                'salary_base_id' => 53,
                'amount_base'    => 0.00,
                'amount'         => 500.00,
                'staff_info_id'  => 119402,
            ],
            [
                'salary_base_id' => 20,
                'amount_base'    => 0.00,
                'amount'         => 5000.00,
                'staff_info_id'  => 119402,
            ],
            [
                'salary_base_id' => 21,
                'amount_base'    => 0.00,
                'amount'         => 0.00,
                'staff_info_id'  => 119402,
            ],
        ];
        $data                   = $service->calculatePayrollTaxIncome(['staff_info_id' => 119402]);


        var_dump($data);
    }

    public function myLeaveSourceAction($params)
    {
        if (!isCountry('my')) {
            exit('only my');
        }
        //$sql = "SELECT i.staff_info_id ,i.leave_date,i.state,i.leave_source ,i.leave_reason, leave_type  FROM hr_staff_info as i INNER JOIN staff_resign as r on i.staff_info_id = r.submitter_id where i.leave_source = 5 and r.status = 4 ";
        $sql = "SELECT i.staff_info_id ,i.leave_date,i.state,i.leave_source ,i.leave_reason, leave_type  FROM hr_staff_info as i  where i.leave_date >= '2023-12-08 00:00:00' and  leave_source = 0";
        if (!empty($params[0])) {
            $sql .= "and staff_info_id = ".$params[0];
        }
        $db        = $this->getDI()->get('db_backyard');
        $staffList = $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        foreach ($staffList as $staff) {
//            if ($staff['state'] == 1) {
//                $up_sql = "update hr_staff_info set leave_source = 0,leave_type = null,leave_reason= null ,updated_at = updated_at where staff_info_id =  " . $staff['staff_info_id'];
//                $db->execute($up_sql);
//            } elseif ($staff['state'] == 2) {
            $log_sql = "select * from leave_manage_log where staff_info_id = {$staff['staff_info_id']} order by id desc limit 1";
            $logData = $this->getDI()->get('db_backyard')->fetchOne($log_sql);
            if (empty($logData)) {
                continue;
            }
            $leave_source = $logData['leave_source'];
            $leave_type   = $logData['leave_type'];
            $leave_reason = $logData['leave_reason'];
            $up_sql       = "update hr_staff_info set leave_source = {$leave_source},leave_type = {$leave_type},leave_reason= {$leave_reason} ,updated_at = updated_at where staff_info_id =  ".$staff['staff_info_id'];
            $db->execute($up_sql);
            //}
        }
    }

    public function dbAction()
    {
        $db = $this->db_backyard;
        $db->begin();
        $staffPayrollModel = StaffPayrollModel::class;
        $payroll           = $this->modelsManager->executeQuery("select id,staff_info_id from {$staffPayrollModel} where month='2023-11'")->toArray();
        var_dump(count($payroll));
        $db->commit();
    }

    public function whiteAction()
    {
        $a                          = new \App\Modules\My\Services\PayrollCalculationService();
        $a->cycleStart              = '2023-11-24';
        $a->cycleEnd                = '2023-12-23';
        $a->paramStaffIds[]         = 120661;
        $a->typeNotPaidLocallyStaff = $a->initAttendanceWhiteDataForUnPaidLocally();
        $c                          = $a->checkNotPaidLocallyStaffAllIncluded([
            'staff_info_id' => 120661,
            'hire_date'     => '2023-12-22',
        ]);
        dd($c);
    }

    public function getSalaryForEAFormAction()
    {
        $service                  = new \App\Modules\My\Services\PayrollReportService();
        $service->salaryCompanyId = 1;
        var_dump($service->getSalaryForEAForm(2023, [118718]));
    }

    public function wwAction()
    {
        $data['year'] = '2025';
//        (new \App\Services\PayrollFileService())->formatPdfData($data);die;
//        (new \App\Services\PayrollFileService())->create_pdf($data);die;
        //27750,27903
        \App\Services\message\BuildMessageService::init()->setStaff([29068, 28952]);
//        \App\Modules\Ph\Services\message\BuildMessageService::init()->setStaff([119723]);
        $audit_info = \App\Services\message\BuildMessageService::init()->getExamineAndApproveData();
//        $audit_info = \App\Modules\Ph\Services\message\BuildMessageService::init()->getExamineAndApproveData();
//        print_r($audit_info);die;
        (new \App\Services\StaffNoticeService())->sendResumeMessage(30032, 220125, '2025-05-23');
        die;
    }


    /**
     * 测试社保-数据源2025-规则表
     * @return void
     */
    public function sssAction($params)
    {
        $sssArray = [
            5249.99,
            5250.00,
            5749.99,
            5750.00,
            6249.99,
            6250.00,
            6749.99,
            6750.00,
            7249.99,
            7250.00,
            7749.99,
            7750.00,
            8249.99,
            8250.00,
            8749.99,
            8750.00,
            9249.99,
            9250.00,
            9749.99,
            9750.00,
            10249.99,
            10250.00,
            10749.99,
            10750.00,
            11249.99,
            11250.00,
            11749.99,
            11750.00,
            12249.99,
            12250.00,
            12749.99,
            12750.00,
            13249.99,
            13250.00,
            13749.99,
            13750.00,
            14249.99,
            14250.00,
            14749.99,
            14750.00,
            15249.99,
            15250.00,
            15749.99,
            15750.00,
            16249.99,
            16250.00,
            16749.99,
            16750.00,
            17249.99,
            17250.00,
            17749.99,
            17750.00,
            18249.99,
            18250.00,
            18749.99,
            18750.00,
            19249.99,
            19250.00,
            19749.99,
            19750.00,
            20249.99,
            20250.00,
            20749.99,
            20750.00,
            21249.99,
            21250.00,
            21749.99,
            21750.00,
            22249.99,
            22250.00,
            22749.99,
            22750.00,
            23249.99,
            23250.00,
            23749.99,
            23750.00,
            24249.99,
            24250.00,
            24749.99,
            24750.00,
            25249.99,
            25250.00,
            25749.99,
            25750.00,
            26249.99,
            26250.00,
            26749.99,
            26750.00,
            27249.99,
            27250.00,
            27749.99,
            27750.00,
            28249.99,
            28250.00,
            28749.99,
            28750.00,
            29249.99,
            29250.00,
            29749.99,
            29750.00,
            30249.99,
            30250.00,
            30749.99,
            30750.00,
            31249.99,
            31250.00,
            31749.99,
            31750.00,
            32249.99,
            32250.00,
            32749.99,
            32750.00,
            33249.99,
            33250.00,
            33749.99,
            33750.00,
            34249.99,
            34250.00,
            34749.99,
            34750.00,
        ];

        if ($params[0]) {
            $sssArray = [$params[0]];
        }

        foreach ($sssArray as $v) {
            $info = $this->sssTest($v);

            echo $v.'-----'.print_r($info, true).'-----'.PHP_EOL;
        }
    }

    /**
     * 社保算法
     * @param $sssBaseTotal
     * @return array
     */
    public function sssTest($sssBaseTotal)
    {
        $level = 0;
        $min   = 5250;
        $max   = 20249;
        $er    = 510; //sss_company初始最小值
        $ee    = 250; //sss_staff初始最小值
        if ($sssBaseTotal >= 14750) {
            $min = 15250;
            $max = 34750;
            $er  = 1530;
            $ee  = 750;
        }
        $realRowInfo['sss_company'] = 3530; //最大
        $realRowInfo['sss_staff']   = 1750; // 最大
        if ($sssBaseTotal < 34750) {
            while ($min <= $max) {
                if ($sssBaseTotal < $min) {
                    $realRowInfo['sss_company'] = $er + $level * 50;
                    $realRowInfo['sss_staff']   = $ee + $level * 25;
                    break;
                }
                ++$level;
                $min += 500;
            }
        }

        return $realRowInfo;
    }

    /**
     * 初始化人脸黑名单数据
     * @return void
     * @throws Exception
     */
    public function initFaceBlackListAction()
    {
        $staffList = [];
        if (isCountry('MY')) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['i' => HrStaffInfoModel::class]);
            $builder->innerJoin(HrBlacklistModel::class, 'i.identity = b.identity', 'b');
            $builder->where("b.status = 1 and !(b.identity  = '' or b.identity  is null )");
            $builder->columns(['i.staff_info_id', 'i.identity']);
            $staffList1 = $builder->getQuery()->execute()->toArray();

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['i' => HrStaffInfoModel::class]);
            $builder->innerJoin(HrOutSourcingBlacklistModel::class, 'i.identity = b.identity', 'b');
            $builder->where("b.status = 1 and b.type in (1,3) and !(b.identity  = '' or b.identity  is null )");
            $builder->columns(['i.staff_info_id', 'i.identity']);
            $staffList2 = $builder->getQuery()->execute()->toArray();

            $staffList = array_merge($staffList1, $staffList2);
        } elseif (isCountry('TH')) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['i' => HrStaffInfoModel::class]);
            $builder->innerJoin(HrBlackGreyListModel::class, 'i.identity = b.identity', 'b');
            $builder->where("b.status = 1 and b.behavior_type = 1 and !(b.identity  = '' or b.identity  is null )");
            $builder->columns(['i.staff_info_id', 'i.identity']);
            $staffList = $builder->getQuery()->execute()->toArray();
        }


        echo ' num'.count($staffList).PHP_EOL;
        $staffList  = array_column($staffList, null, 'staff_info_id');
        $chunkStaff = array_chunk($staffList, 500);

        foreach ($chunkStaff as $key => $staffs) {
            echo 'chunk '.$key.PHP_EOL;
            $staff_ids       = array_column($staffs, 'staff_info_id');
            $staffAttachment = StaffWorkAttendanceAttachmentModel::find([
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and deleted = 0',
                'bind'       => [
                    'staff_info_ids' => $staff_ids,
                ],
                'columns'    => 'staff_info_id,work_attendance_path',
                'order'      => 'created_at desc',
            ])->toArray();

            $staffAttachment = array_column($staffAttachment, 'work_attendance_path', 'staff_info_id');
            $data            = [];
            foreach ($staffs as $item) {
                if (empty($staffAttachment[$item['staff_info_id']])) {
                    continue;
                }
                $image                     = $this->config->application->img_prefix.$staffAttachment[$item['staff_info_id']];
                $tmp['staff_info_id']      = $item['staff_info_id'];
                $tmp['identity']           = $item['identity'];
                $tmp['face_negatives_url'] = $image;
                $tmp['src']                = 2;
                $data[]                    = $tmp;
            }
            $data && (new \App\Models\backyard\StaffFaceBlacklistModel())->batch_insert($data);
        }
    }

    public function rush_job_dept_relationAction()
    {
        [$relationDataStr, $jobGroupStr, $jobJobTitleRelateStr] = $this->getData();

        $relationData = json_decode($relationDataStr, true);
        $jobGroupData = json_decode($jobGroupStr, true);
        //$jobJobTitleRelateData = json_decode($jobJobTitleRelateStr, true);
        $jobJobTitleRelateData = [];

        $db = $this->getDI()->get("db_backyard");

        //更新职组
        foreach ($jobGroupData as $oneGroup) {
            $hrJdInfo = HrJdModel::findFirst([
                'conditions' => ' job_id = :job_id: ',
                'bind'       => [
                    'job_id' => $oneGroup['job_id'],
                ],
            ]);
            if ($hrJdInfo) {
                $db->updateAsDict('hr_jd',
                    ['job_group_id' => $oneGroup['job_group_id'], 'updated_at' => $hrJdInfo->updated_at],
                    ["conditions" => "job_id = ?", 'bind' => $oneGroup['job_id']]);
            }
        }

        echo '更新职组完成'.PHP_EOL;

        //更新职组
        foreach ($relationData as $oneRelation) {
            if (isCountry('PH')) {
                $relationInfo = \App\Models\backyard\HrJobDepartmentRelationModel::find([
                    'conditions' => 'job_id = :job_id:',
                    'bind'       => [
                        'job_id' => $oneRelation['job_title'],
                    ],
                ])->toArray();
                if ($relationInfo) {
                    foreach ($relationInfo as $oneJobGroup) {
                        $jobGroupInfo = \App\Models\backyard\HrJobGroupModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind'       => [
                                'id' => $oneRelation['group_id'],
                            ],
                        ]);
                        if (empty($jobGroupInfo)) {
                            $this->logger->write_log('jobGroupInfo is empty : group_id >>>>'.$oneRelation['group_id'],
                                'error');
                            continue;
                        }
                        $jobGroupInfo = $jobGroupInfo->toArray();
                        if (empty($jobGroupInfo['functional_competency_ids'])) {
                            $this->logger->write_log('functional_competency_ids is empty : group_id >>>>'.$oneRelation['group_id'],
                                'error');
                            continue;
                        }
                        $functional_competency_ids = explode(',', $jobGroupInfo['functional_competency_ids']);

                        $allData = [];
                        foreach ($functional_competency_ids as $oneId) {
                            $oneData['id']    = $oneId;
                            $oneData['level'] = 1;
                            $allData[]        = $oneData;
                        }

                        $updateData['group_id']   = $oneRelation['group_id'];
                        $updateData['updated_at'] = $oneJobGroup['updated_at'];

                        if (!empty($allData)) {
                            $updateData['job_competency_zy'] = json_encode($allData, JSON_UNESCAPED_UNICODE);
                        }

                        $db->updateAsDict('hr_job_department_relation', $updateData,
                            ["conditions" => "id = ?", 'bind' => $oneJobGroup['id']]);
                    }
                }
            } else {
                $relationInfo = \App\Models\backyard\HrJobDepartmentRelationModel::findFirst([
                    'conditions' => 'job_id = :job_id: and department_id = :department_id:',
                    'bind'       => [
                        'job_id'        => $oneRelation['job_title'],
                        'department_id' => $oneRelation['department_id'],
                    ],
                ]);
                if ($relationInfo) {
                    $jobGroupInfo = \App\Models\backyard\HrJobGroupModel::findFirst([
                        'conditions' => ' id = :id: ',
                        'bind'       => [
                            'id' => $oneRelation['group_id'],
                        ],
                    ]);
                    if (empty($jobGroupInfo)) {
                        $this->logger->write_log('jobGroupInfo is empty : group_id >>>>'.$oneRelation['group_id'],
                            'error');
                        continue;
                    }
                    $jobGroupInfo = $jobGroupInfo->toArray();
                    if (empty($jobGroupInfo['functional_competency_ids'])) {
                        $this->logger->write_log('functional_competency_ids is empty : group_id >>>>'.$oneRelation['group_id'],
                            'error');
                        continue;
                    }
                    $functional_competency_ids = explode(',', $jobGroupInfo['functional_competency_ids']);

                    $allData = [];
                    foreach ($functional_competency_ids as $oneId) {
                        $oneData['id']    = $oneId;
                        $oneData['level'] = 1;
                        $allData[]        = $oneData;
                    }

                    $updateData['group_id']   = $oneRelation['group_id'];
                    $updateData['updated_at'] = $relationInfo->updated_at;

                    if (!empty($allData)) {
                        $updateData['job_competency_zy'] = json_encode($allData, JSON_UNESCAPED_UNICODE);
                    }

                    $db->updateAsDict('hr_job_department_relation', $updateData,
                        ["conditions" => "id = ?", 'bind' => $relationInfo->id]);
                }
            }
        }
        echo '更新关联组织完成'.PHP_EOL;

        //更新 JD & 职位关联关系
        foreach ($jobJobTitleRelateData as $oneRelate) {
            $db->insertAsDict('hr_jd_job_title_relation', [
                'job_id'      => $oneRelate['job_id'],
                'position_id' => $oneRelate['job_title'],
            ]);
        }
    }

    /**
     * redis队里长度
     * @param $params
     * @return void
     */
    public function listLenAction($params)
    {
        $redis = $this->getDI()->get("redis");
        $key   = $params[0] ?? '';
        $len   = $redis->lLen($key);
        $this->logger->notice('redis listLenAction: '.$key.' len is '.$len);
        var_dump($len);
    }

    private function getData()
    {
        $data        = [];
        $countryCode = strtolower(env('country_code', 'TH'));
        switch ($countryCode) {
            case "th":
                $data = [
                    '[{"job_title":20,"department_id":118,"group_id":23},{"job_title":1183,"department_id":118,"group_id":23},{"job_title":1184,"department_id":1288,"group_id":23},{"job_title":927,"department_id":1299,"group_id":23},{"job_title":1184,"department_id":1299,"group_id":23},{"job_title":1421,"department_id":1299,"group_id":23},{"job_title":1855,"department_id":1340,"group_id":23},{"job_title":774,"department_id":1343,"group_id":23},{"job_title":927,"department_id":1343,"group_id":23},{"job_title":1184,"department_id":1343,"group_id":23},{"job_title":20,"department_id":36,"group_id":23},{"job_title":774,"department_id":36,"group_id":23},{"job_title":1184,"department_id":36,"group_id":23},{"job_title":1331,"department_id":388,"group_id":23},{"job_title":927,"department_id":428,"group_id":23},{"job_title":774,"department_id":429,"group_id":23},{"job_title":776,"department_id":429,"group_id":23},{"job_title":776,"department_id":80006,"group_id":23},{"job_title":1073,"department_id":80015,"group_id":23},{"job_title":775,"department_id":84,"group_id":23},{"job_title":1184,"department_id":861,"group_id":23},{"job_title":220,"department_id":1003,"group_id":10},{"job_title":1412,"department_id":1003,"group_id":10},{"job_title":220,"department_id":1013,"group_id":10},{"job_title":220,"department_id":1043,"group_id":10},{"job_title":1412,"department_id":1043,"group_id":10},{"job_title":220,"department_id":1071,"group_id":10},{"job_title":1412,"department_id":1071,"group_id":10},{"job_title":344,"department_id":1126,"group_id":10},{"job_title":344,"department_id":1141,"group_id":10},{"job_title":344,"department_id":1149,"group_id":10},{"job_title":962,"department_id":1149,"group_id":10},{"job_title":962,"department_id":119,"group_id":10},{"job_title":220,"department_id":1202,"group_id":10},{"job_title":1349,"department_id":1268,"group_id":10},{"job_title":1348,"department_id":1301,"group_id":10},{"job_title":215,"department_id":175,"group_id":10},{"job_title":467,"department_id":175,"group_id":10},{"job_title":1936,"department_id":175,"group_id":10},{"job_title":1937,"department_id":175,"group_id":10},{"job_title":1938,"department_id":175,"group_id":10},{"job_title":480,"department_id":176,"group_id":10},{"job_title":34,"department_id":185,"group_id":10},{"job_title":1958,"department_id":185,"group_id":10},{"job_title":1348,"department_id":192,"group_id":10},{"job_title":1349,"department_id":192,"group_id":10},{"job_title":216,"department_id":3,"group_id":10},{"job_title":1895,"department_id":450,"group_id":10},{"job_title":1896,"department_id":450,"group_id":10},{"job_title":1939,"department_id":450,"group_id":10},{"job_title":1941,"department_id":450,"group_id":10},{"job_title":1412,"department_id":575,"group_id":10},{"job_title":1412,"department_id":581,"group_id":10},{"job_title":1412,"department_id":591,"group_id":10},{"job_title":1412,"department_id":595,"group_id":10},{"job_title":220,"department_id":614,"group_id":10},{"job_title":220,"department_id":624,"group_id":10},{"job_title":220,"department_id":634,"group_id":10},{"job_title":220,"department_id":644,"group_id":10},{"job_title":220,"department_id":654,"group_id":10},{"job_title":220,"department_id":664,"group_id":10},{"job_title":661,"department_id":664,"group_id":10},{"job_title":1412,"department_id":664,"group_id":10},{"job_title":220,"department_id":674,"group_id":10},{"job_title":1412,"department_id":674,"group_id":10},{"job_title":220,"department_id":684,"group_id":10},{"job_title":661,"department_id":684,"group_id":10},{"job_title":1412,"department_id":684,"group_id":10},{"job_title":220,"department_id":694,"group_id":10},{"job_title":220,"department_id":704,"group_id":10},{"job_title":1412,"department_id":704,"group_id":10},{"job_title":220,"department_id":714,"group_id":10},{"job_title":220,"department_id":724,"group_id":10},{"job_title":1412,"department_id":734,"group_id":10},{"job_title":220,"department_id":744,"group_id":10},{"job_title":1412,"department_id":744,"group_id":10},{"job_title":220,"department_id":754,"group_id":10},{"job_title":220,"department_id":774,"group_id":10},{"job_title":1412,"department_id":774,"group_id":10},{"job_title":220,"department_id":784,"group_id":10},{"job_title":661,"department_id":784,"group_id":10},{"job_title":1412,"department_id":784,"group_id":10},{"job_title":220,"department_id":794,"group_id":10},{"job_title":1412,"department_id":794,"group_id":10},{"job_title":220,"department_id":804,"group_id":10},{"job_title":220,"department_id":814,"group_id":10},{"job_title":1412,"department_id":814,"group_id":10},{"job_title":220,"department_id":824,"group_id":10},{"job_title":220,"department_id":834,"group_id":10},{"job_title":1412,"department_id":834,"group_id":10},{"job_title":220,"department_id":844,"group_id":10},{"job_title":1412,"department_id":844,"group_id":10},{"job_title":220,"department_id":854,"group_id":10},{"job_title":1412,"department_id":854,"group_id":10},{"job_title":33,"department_id":88,"group_id":10},{"job_title":34,"department_id":88,"group_id":10},{"job_title":344,"department_id":88,"group_id":10},{"job_title":940,"department_id":88,"group_id":10},{"job_title":941,"department_id":89,"group_id":10},{"job_title":942,"department_id":89,"group_id":10},{"job_title":943,"department_id":89,"group_id":10},{"job_title":1412,"department_id":897,"group_id":10},{"job_title":1412,"department_id":908,"group_id":10},{"job_title":220,"department_id":919,"group_id":10},{"job_title":1412,"department_id":919,"group_id":10},{"job_title":1412,"department_id":962,"group_id":10},{"job_title":220,"department_id":982,"group_id":10},{"job_title":1412,"department_id":982,"group_id":10},{"job_title":220,"department_id":992,"group_id":10},{"job_title":893,"department_id":160,"group_id":26},{"job_title":894,"department_id":160,"group_id":26},{"job_title":1151,"department_id":160,"group_id":26},{"job_title":1714,"department_id":161,"group_id":26},{"job_title":1715,"department_id":161,"group_id":26},{"job_title":1283,"department_id":514,"group_id":26},{"job_title":1284,"department_id":514,"group_id":26},{"job_title":1716,"department_id":514,"group_id":26},{"job_title":261,"department_id":80008,"group_id":26},{"job_title":1921,"department_id":93,"group_id":26},{"job_title":1599,"department_id":1165,"group_id":26},{"job_title":1732,"department_id":1165,"group_id":26},{"job_title":1741,"department_id":1165,"group_id":26},{"job_title":1007,"department_id":222,"group_id":25},{"job_title":486,"department_id":333,"group_id":25},{"job_title":1133,"department_id":388,"group_id":25},{"job_title":1957,"department_id":388,"group_id":25},{"job_title":632,"department_id":444,"group_id":25},{"job_title":1957,"department_id":80002,"group_id":25},{"job_title":23,"department_id":999,"group_id":25},{"job_title":1981,"department_id":1281,"group_id":1},{"job_title":3,"department_id":1282,"group_id":1},{"job_title":1865,"department_id":1283,"group_id":1},{"job_title":1866,"department_id":1283,"group_id":1},{"job_title":1867,"department_id":1283,"group_id":1},{"job_title":1868,"department_id":1283,"group_id":1},{"job_title":1869,"department_id":1283,"group_id":1},{"job_title":1870,"department_id":1283,"group_id":1},{"job_title":1871,"department_id":1284,"group_id":1},{"job_title":1872,"department_id":1284,"group_id":1},{"job_title":1873,"department_id":1284,"group_id":1},{"job_title":1874,"department_id":1284,"group_id":1},{"job_title":1875,"department_id":1284,"group_id":1},{"job_title":1696,"department_id":1285,"group_id":1},{"job_title":1,"department_id":1286,"group_id":1},{"job_title":3,"department_id":1286,"group_id":1},{"job_title":193,"department_id":1286,"group_id":1},{"job_title":1380,"department_id":1286,"group_id":1},{"job_title":1382,"department_id":1286,"group_id":1},{"job_title":1386,"department_id":1286,"group_id":1},{"job_title":1388,"department_id":1286,"group_id":1},{"job_title":1611,"department_id":1286,"group_id":1},{"job_title":1,"department_id":1287,"group_id":1},{"job_title":797,"department_id":1287,"group_id":1},{"job_title":1614,"department_id":1287,"group_id":1},{"job_title":1615,"department_id":1287,"group_id":1},{"job_title":1982,"department_id":1287,"group_id":1},{"job_title":133,"department_id":1314,"group_id":1},{"job_title":586,"department_id":1314,"group_id":1},{"job_title":1391,"department_id":1314,"group_id":1},{"job_title":96,"department_id":1315,"group_id":1},{"job_title":194,"department_id":1315,"group_id":1},{"job_title":586,"department_id":1315,"group_id":1},{"job_title":1391,"department_id":1315,"group_id":1},{"job_title":1286,"department_id":1318,"group_id":1},{"job_title":1554,"department_id":1318,"group_id":1},{"job_title":1555,"department_id":1318,"group_id":1},{"job_title":1602,"department_id":1318,"group_id":1},{"job_title":1992,"department_id":1318,"group_id":1},{"job_title":453,"department_id":17,"group_id":1},{"job_title":1736,"department_id":333,"group_id":1},{"job_title":1979,"department_id":333,"group_id":1},{"job_title":1980,"department_id":333,"group_id":1},{"job_title":589,"department_id":536,"group_id":1},{"job_title":590,"department_id":536,"group_id":1},{"job_title":1517,"department_id":536,"group_id":1},{"job_title":1603,"department_id":536,"group_id":1},{"job_title":26,"department_id":540,"group_id":1},{"job_title":1020,"department_id":540,"group_id":1},{"job_title":1393,"department_id":540,"group_id":1},{"job_title":921,"department_id":80019,"group_id":1},{"job_title":1662,"department_id":1182,"group_id":11},{"job_title":1698,"department_id":1213,"group_id":11},{"job_title":1839,"department_id":1276,"group_id":11},{"job_title":1840,"department_id":1276,"group_id":11},{"job_title":1050,"department_id":273,"group_id":11},{"job_title":1907,"department_id":273,"group_id":11},{"job_title":52,"department_id":44,"group_id":11},{"job_title":608,"department_id":44,"group_id":11},{"job_title":1693,"department_id":44,"group_id":11},{"job_title":255,"department_id":45,"group_id":11},{"job_title":607,"department_id":45,"group_id":11},{"job_title":1846,"department_id":45,"group_id":11},{"job_title":610,"department_id":46,"group_id":11},{"job_title":611,"department_id":46,"group_id":11},{"job_title":644,"department_id":46,"group_id":11},{"job_title":1698,"department_id":80010,"group_id":11},{"job_title":1698,"department_id":80012,"group_id":11},{"job_title":1698,"department_id":80013,"group_id":11},{"job_title":568,"department_id":1001,"group_id":16},{"job_title":568,"department_id":1011,"group_id":16},{"job_title":568,"department_id":1041,"group_id":16},{"job_title":1676,"department_id":1041,"group_id":16},{"job_title":568,"department_id":1069,"group_id":16},{"job_title":1676,"department_id":1069,"group_id":16},{"job_title":1025,"department_id":1082,"group_id":16},{"job_title":1154,"department_id":1082,"group_id":16},{"job_title":1373,"department_id":1082,"group_id":16},{"job_title":1374,"department_id":1082,"group_id":16},{"job_title":1377,"department_id":1082,"group_id":16},{"job_title":1378,"department_id":1082,"group_id":16},{"job_title":1823,"department_id":1082,"group_id":16},{"job_title":1926,"department_id":1082,"group_id":16},{"job_title":620,"department_id":1096,"group_id":16},{"job_title":1025,"department_id":1096,"group_id":16},{"job_title":1154,"department_id":1096,"group_id":16},{"job_title":1542,"department_id":1096,"group_id":16},{"job_title":260,"department_id":111,"group_id":16},{"job_title":478,"department_id":111,"group_id":16},{"job_title":1157,"department_id":111,"group_id":16},{"job_title":1355,"department_id":111,"group_id":16},{"job_title":1932,"department_id":111,"group_id":16},{"job_title":6,"department_id":1152,"group_id":16},{"job_title":1904,"department_id":1191,"group_id":16},{"job_title":568,"department_id":1193,"group_id":16},{"job_title":1355,"department_id":12,"group_id":16},{"job_title":1688,"department_id":1205,"group_id":16},{"job_title":1355,"department_id":1256,"group_id":16},{"job_title":146,"department_id":1283,"group_id":16},{"job_title":1355,"department_id":1283,"group_id":16},{"job_title":1355,"department_id":1284,"group_id":16},{"job_title":1720,"department_id":1285,"group_id":16},{"job_title":1355,"department_id":1286,"group_id":16},{"job_title":710,"department_id":13,"group_id":16},{"job_title":1355,"department_id":13,"group_id":16},{"job_title":553,"department_id":1311,"group_id":16},{"job_title":470,"department_id":1316,"group_id":16},{"job_title":1149,"department_id":1316,"group_id":16},{"job_title":1355,"department_id":1316,"group_id":16},{"job_title":1885,"department_id":1340,"group_id":16},{"job_title":1341,"department_id":18,"group_id":16},{"job_title":1355,"department_id":20,"group_id":16},{"job_title":1149,"department_id":20001,"group_id":16},{"job_title":1712,"department_id":20001,"group_id":16},{"job_title":260,"department_id":217,"group_id":16},{"job_title":995,"department_id":217,"group_id":16},{"job_title":1885,"department_id":222,"group_id":16},{"job_title":4,"department_id":236,"group_id":16},{"job_title":334,"department_id":236,"group_id":16},{"job_title":617,"department_id":236,"group_id":16},{"job_title":1702,"department_id":236,"group_id":16},{"job_title":1103,"department_id":25,"group_id":16},{"job_title":1795,"department_id":3,"group_id":16},{"job_title":1355,"department_id":32,"group_id":16},{"job_title":1879,"department_id":333,"group_id":16},{"job_title":1149,"department_id":388,"group_id":16},{"job_title":1571,"department_id":4,"group_id":16},{"job_title":1355,"department_id":45,"group_id":16},{"job_title":613,"department_id":46,"group_id":16},{"job_title":1355,"department_id":483,"group_id":16},{"job_title":1355,"department_id":500,"group_id":16},{"job_title":1516,"department_id":51,"group_id":16},{"job_title":1355,"department_id":536,"group_id":16},{"job_title":1355,"department_id":540,"group_id":16},{"job_title":478,"department_id":546,"group_id":16},{"job_title":1157,"department_id":546,"group_id":16},{"job_title":1978,"department_id":546,"group_id":16},{"job_title":574,"department_id":558,"group_id":16},{"job_title":1355,"department_id":562,"group_id":16},{"job_title":1676,"department_id":562,"group_id":16},{"job_title":1355,"department_id":567,"group_id":16},{"job_title":574,"department_id":571,"group_id":16},{"job_title":1355,"department_id":575,"group_id":16},{"job_title":1676,"department_id":575,"group_id":16},{"job_title":1355,"department_id":576,"group_id":16},{"job_title":1355,"department_id":577,"group_id":16},{"job_title":1355,"department_id":580,"group_id":16},{"job_title":574,"department_id":586,"group_id":16},{"job_title":1676,"department_id":59,"group_id":16},{"job_title":1355,"department_id":590,"group_id":16},{"job_title":1355,"department_id":591,"group_id":16},{"job_title":1676,"department_id":591,"group_id":16},{"job_title":574,"department_id":598,"group_id":16},{"job_title":1676,"department_id":598,"group_id":16},{"job_title":1355,"department_id":602,"group_id":16},{"job_title":568,"department_id":620,"group_id":16},{"job_title":568,"department_id":630,"group_id":16},{"job_title":1676,"department_id":630,"group_id":16},{"job_title":520,"department_id":64,"group_id":16},{"job_title":568,"department_id":640,"group_id":16},{"job_title":568,"department_id":650,"group_id":16},{"job_title":568,"department_id":660,"group_id":16},{"job_title":568,"department_id":670,"group_id":16},{"job_title":563,"department_id":68,"group_id":16},{"job_title":568,"department_id":68,"group_id":16},{"job_title":574,"department_id":68,"group_id":16},{"job_title":1676,"department_id":68,"group_id":16},{"job_title":568,"department_id":680,"group_id":16},{"job_title":1676,"department_id":680,"group_id":16},{"job_title":568,"department_id":690,"group_id":16},{"job_title":1355,"department_id":70,"group_id":16},{"job_title":568,"department_id":700,"group_id":16},{"job_title":1676,"department_id":700,"group_id":16},{"job_title":568,"department_id":710,"group_id":16},{"job_title":1676,"department_id":710,"group_id":16},{"job_title":568,"department_id":720,"group_id":16},{"job_title":1676,"department_id":720,"group_id":16},{"job_title":568,"department_id":730,"group_id":16},{"job_title":1676,"department_id":730,"group_id":16},{"job_title":568,"department_id":740,"group_id":16},{"job_title":1676,"department_id":740,"group_id":16},{"job_title":568,"department_id":750,"group_id":16},{"job_title":568,"department_id":770,"group_id":16},{"job_title":1676,"department_id":770,"group_id":16},{"job_title":568,"department_id":780,"group_id":16},{"job_title":1676,"department_id":790,"group_id":16},{"job_title":568,"department_id":800,"group_id":16},{"job_title":1676,"department_id":810,"group_id":16},{"job_title":568,"department_id":820,"group_id":16},{"job_title":568,"department_id":830,"group_id":16},{"job_title":1676,"department_id":830,"group_id":16},{"job_title":568,"department_id":840,"group_id":16},{"job_title":1676,"department_id":840,"group_id":16},{"job_title":568,"department_id":850,"group_id":16},{"job_title":1676,"department_id":850,"group_id":16},{"job_title":1355,"department_id":895,"group_id":16},{"job_title":1676,"department_id":908,"group_id":16},{"job_title":568,"department_id":915,"group_id":16},{"job_title":1676,"department_id":915,"group_id":16},{"job_title":1713,"department_id":93,"group_id":16},{"job_title":568,"department_id":960,"group_id":16},{"job_title":568,"department_id":980,"group_id":16},{"job_title":1676,"department_id":980,"group_id":16},{"job_title":1676,"department_id":990,"group_id":16},{"job_title":1852,"department_id":1219,"group_id":27},{"job_title":1916,"department_id":1219,"group_id":27},{"job_title":1894,"department_id":1294,"group_id":27},{"job_title":1774,"department_id":169,"group_id":27},{"job_title":1153,"department_id":19,"group_id":27},{"job_title":1618,"department_id":19,"group_id":27},{"job_title":1768,"department_id":19,"group_id":27},{"job_title":1847,"department_id":19,"group_id":27},{"job_title":38,"department_id":41,"group_id":27},{"job_title":1060,"department_id":41,"group_id":27},{"job_title":1654,"department_id":41,"group_id":27},{"job_title":1707,"department_id":41,"group_id":27},{"job_title":1940,"department_id":41,"group_id":27},{"job_title":1644,"department_id":429,"group_id":27},{"job_title":1278,"department_id":510,"group_id":27},{"job_title":1279,"department_id":510,"group_id":27},{"job_title":226,"department_id":527,"group_id":27},{"job_title":1680,"department_id":527,"group_id":27},{"job_title":1681,"department_id":527,"group_id":27},{"job_title":1620,"department_id":1090,"group_id":15},{"job_title":153,"department_id":1169,"group_id":15},{"job_title":1622,"department_id":1223,"group_id":15},{"job_title":1623,"department_id":1223,"group_id":15},{"job_title":45,"department_id":1263,"group_id":15},{"job_title":1837,"department_id":1263,"group_id":15},{"job_title":1636,"department_id":1319,"group_id":15},{"job_title":1929,"department_id":1319,"group_id":15},{"job_title":176,"department_id":169,"group_id":15},{"job_title":177,"department_id":169,"group_id":15},{"job_title":441,"department_id":169,"group_id":15},{"job_title":240,"department_id":285,"group_id":15},{"job_title":1221,"department_id":285,"group_id":15},{"job_title":1354,"department_id":285,"group_id":15},{"job_title":1608,"department_id":312,"group_id":15},{"job_title":890,"department_id":444,"group_id":15},{"job_title":444,"department_id":47,"group_id":15},{"job_title":1608,"department_id":488,"group_id":15},{"job_title":1608,"department_id":489,"group_id":15},{"job_title":240,"department_id":49,"group_id":15},{"job_title":335,"department_id":49,"group_id":15},{"job_title":1221,"department_id":49,"group_id":15},{"job_title":1354,"department_id":49,"group_id":15},{"job_title":187,"department_id":494,"group_id":15},{"job_title":78,"department_id":50,"group_id":15},{"job_title":92,"department_id":50,"group_id":15},{"job_title":153,"department_id":50,"group_id":15},{"job_title":970,"department_id":50,"group_id":15},{"job_title":1142,"department_id":50,"group_id":15},{"job_title":1186,"department_id":50,"group_id":15},{"job_title":1242,"department_id":50,"group_id":15},{"job_title":1512,"department_id":50,"group_id":15},{"job_title":518,"department_id":500,"group_id":15},{"job_title":870,"department_id":500,"group_id":15},{"job_title":1769,"department_id":500,"group_id":15},{"job_title":979,"department_id":511,"group_id":15},{"job_title":980,"department_id":511,"group_id":15},{"job_title":981,"department_id":511,"group_id":15},{"job_title":1841,"department_id":511,"group_id":15},{"job_title":1842,"department_id":511,"group_id":15},{"job_title":1843,"department_id":511,"group_id":15},{"job_title":1963,"department_id":80005,"group_id":15},{"job_title":873,"department_id":90,"group_id":15},{"job_title":1227,"department_id":90,"group_id":15},{"job_title":1722,"department_id":90,"group_id":15},{"job_title":343,"department_id":1190,"group_id":13},{"job_title":769,"department_id":1298,"group_id":13},{"job_title":1439,"department_id":1298,"group_id":13},{"job_title":27,"department_id":1311,"group_id":13},{"job_title":552,"department_id":1311,"group_id":13},{"job_title":1856,"department_id":1311,"group_id":13},{"job_title":1909,"department_id":1311,"group_id":13},{"job_title":1818,"department_id":185,"group_id":13},{"job_title":1035,"department_id":231,"group_id":13},{"job_title":1109,"department_id":231,"group_id":13},{"job_title":1385,"department_id":231,"group_id":13},{"job_title":1825,"department_id":231,"group_id":13},{"job_title":1942,"department_id":231,"group_id":13},{"job_title":1943,"department_id":231,"group_id":13},{"job_title":1944,"department_id":231,"group_id":13},{"job_title":1946,"department_id":231,"group_id":13},{"job_title":1947,"department_id":231,"group_id":13},{"job_title":1950,"department_id":231,"group_id":13},{"job_title":1966,"department_id":231,"group_id":13},{"job_title":1967,"department_id":231,"group_id":13},{"job_title":1853,"department_id":54,"group_id":13},{"job_title":271,"department_id":1002,"group_id":3},{"job_title":572,"department_id":1002,"group_id":3},{"job_title":271,"department_id":1004,"group_id":3},{"job_title":572,"department_id":1004,"group_id":3},{"job_title":745,"department_id":1004,"group_id":3},{"job_title":271,"department_id":1007,"group_id":3},{"job_title":572,"department_id":1007,"group_id":3},{"job_title":271,"department_id":1012,"group_id":3},{"job_title":572,"department_id":1012,"group_id":3},{"job_title":271,"department_id":1014,"group_id":3},{"job_title":572,"department_id":1014,"group_id":3},{"job_title":271,"department_id":1017,"group_id":3},{"job_title":271,"department_id":1042,"group_id":3},{"job_title":572,"department_id":1042,"group_id":3},{"job_title":271,"department_id":1044,"group_id":3},{"job_title":572,"department_id":1044,"group_id":3},{"job_title":271,"department_id":1047,"group_id":3},{"job_title":572,"department_id":1047,"group_id":3},{"job_title":271,"department_id":1070,"group_id":3},{"job_title":572,"department_id":1070,"group_id":3},{"job_title":745,"department_id":1070,"group_id":3},{"job_title":271,"department_id":1072,"group_id":3},{"job_title":572,"department_id":1072,"group_id":3},{"job_title":745,"department_id":1072,"group_id":3},{"job_title":271,"department_id":1075,"group_id":3},{"job_title":572,"department_id":1075,"group_id":3},{"job_title":75,"department_id":1105,"group_id":3},{"job_title":300,"department_id":1121,"group_id":3},{"job_title":1301,"department_id":1121,"group_id":3},{"job_title":300,"department_id":1122,"group_id":3},{"job_title":1301,"department_id":1122,"group_id":3},{"job_title":300,"department_id":1123,"group_id":3},{"job_title":1301,"department_id":1123,"group_id":3},{"job_title":300,"department_id":1124,"group_id":3},{"job_title":1301,"department_id":1124,"group_id":3},{"job_title":300,"department_id":1134,"group_id":3},{"job_title":918,"department_id":1134,"group_id":3},{"job_title":1301,"department_id":1134,"group_id":3},{"job_title":300,"department_id":1137,"group_id":3},{"job_title":1301,"department_id":1137,"group_id":3},{"job_title":300,"department_id":1139,"group_id":3},{"job_title":1301,"department_id":1139,"group_id":3},{"job_title":300,"department_id":1143,"group_id":3},{"job_title":918,"department_id":1143,"group_id":3},{"job_title":1301,"department_id":1143,"group_id":3},{"job_title":300,"department_id":1144,"group_id":3},{"job_title":1301,"department_id":1144,"group_id":3},{"job_title":300,"department_id":1145,"group_id":3},{"job_title":1301,"department_id":1145,"group_id":3},{"job_title":271,"department_id":1156,"group_id":3},{"job_title":572,"department_id":1156,"group_id":3},{"job_title":271,"department_id":1158,"group_id":3},{"job_title":572,"department_id":1158,"group_id":3},{"job_title":271,"department_id":1161,"group_id":3},{"job_title":13,"department_id":1168,"group_id":3},{"job_title":37,"department_id":1168,"group_id":3},{"job_title":110,"department_id":1168,"group_id":3},{"job_title":1015,"department_id":1168,"group_id":3},{"job_title":1289,"department_id":1168,"group_id":3},{"job_title":13,"department_id":1183,"group_id":3},{"job_title":37,"department_id":1183,"group_id":3},{"job_title":110,"department_id":1183,"group_id":3},{"job_title":1015,"department_id":1183,"group_id":3},{"job_title":733,"department_id":119,"group_id":3},{"job_title":843,"department_id":119,"group_id":3},{"job_title":271,"department_id":1194,"group_id":3},{"job_title":572,"department_id":1194,"group_id":3},{"job_title":271,"department_id":1195,"group_id":3},{"job_title":572,"department_id":1195,"group_id":3},{"job_title":271,"department_id":1198,"group_id":3},{"job_title":572,"department_id":1198,"group_id":3},{"job_title":37,"department_id":1205,"group_id":3},{"job_title":110,"department_id":1205,"group_id":3},{"job_title":111,"department_id":1205,"group_id":3},{"job_title":1015,"department_id":1205,"group_id":3},{"job_title":1844,"department_id":1205,"group_id":3},{"job_title":1845,"department_id":1205,"group_id":3},{"job_title":300,"department_id":1209,"group_id":3},{"job_title":1301,"department_id":1209,"group_id":3},{"job_title":300,"department_id":1210,"group_id":3},{"job_title":1301,"department_id":1210,"group_id":3},{"job_title":300,"department_id":1211,"group_id":3},{"job_title":1301,"department_id":1211,"group_id":3},{"job_title":745,"department_id":1229,"group_id":3},{"job_title":587,"department_id":1295,"group_id":3},{"job_title":300,"department_id":1320,"group_id":3},{"job_title":1301,"department_id":1320,"group_id":3},{"job_title":300,"department_id":1322,"group_id":3},{"job_title":1301,"department_id":1322,"group_id":3},{"job_title":300,"department_id":1324,"group_id":3},{"job_title":300,"department_id":1326,"group_id":3},{"job_title":1301,"department_id":1326,"group_id":3},{"job_title":300,"department_id":1327,"group_id":3},{"job_title":1301,"department_id":1327,"group_id":3},{"job_title":300,"department_id":1328,"group_id":3},{"job_title":300,"department_id":1329,"group_id":3},{"job_title":1301,"department_id":1329,"group_id":3},{"job_title":300,"department_id":1330,"group_id":3},{"job_title":1301,"department_id":1330,"group_id":3},{"job_title":300,"department_id":1335,"group_id":3},{"job_title":918,"department_id":1335,"group_id":3},{"job_title":1301,"department_id":1335,"group_id":3},{"job_title":300,"department_id":1337,"group_id":3},{"job_title":733,"department_id":1340,"group_id":3},{"job_title":1977,"department_id":1340,"group_id":3},{"job_title":1274,"department_id":185,"group_id":3},{"job_title":13,"department_id":32,"group_id":3},{"job_title":37,"department_id":32,"group_id":3},{"job_title":110,"department_id":32,"group_id":3},{"job_title":111,"department_id":32,"group_id":3},{"job_title":452,"department_id":32,"group_id":3},{"job_title":1000,"department_id":32,"group_id":3},{"job_title":1015,"department_id":32,"group_id":3},{"job_title":1497,"department_id":32,"group_id":3},{"job_title":1663,"department_id":32,"group_id":3},{"job_title":1664,"department_id":32,"group_id":3},{"job_title":1844,"department_id":32,"group_id":3},{"job_title":1845,"department_id":32,"group_id":3},{"job_title":1930,"department_id":32,"group_id":3},{"job_title":1274,"department_id":338,"group_id":3},{"job_title":1245,"department_id":438,"group_id":3},{"job_title":1250,"department_id":452,"group_id":3},{"job_title":1294,"department_id":452,"group_id":3},{"job_title":1296,"department_id":452,"group_id":3},{"job_title":13,"department_id":483,"group_id":3},{"job_title":37,"department_id":483,"group_id":3},{"job_title":110,"department_id":483,"group_id":3},{"job_title":111,"department_id":483,"group_id":3},{"job_title":452,"department_id":483,"group_id":3},{"job_title":1000,"department_id":483,"group_id":3},{"job_title":1015,"department_id":483,"group_id":3},{"job_title":1497,"department_id":483,"group_id":3},{"job_title":1663,"department_id":483,"group_id":3},{"job_title":1664,"department_id":483,"group_id":3},{"job_title":1844,"department_id":483,"group_id":3},{"job_title":1845,"department_id":483,"group_id":3},{"job_title":1930,"department_id":483,"group_id":3},{"job_title":745,"department_id":582,"group_id":3},{"job_title":271,"department_id":605,"group_id":3},{"job_title":572,"department_id":605,"group_id":3},{"job_title":271,"department_id":606,"group_id":3},{"job_title":572,"department_id":606,"group_id":3},{"job_title":271,"department_id":607,"group_id":3},{"job_title":572,"department_id":607,"group_id":3},{"job_title":271,"department_id":615,"group_id":3},{"job_title":572,"department_id":615,"group_id":3},{"job_title":745,"department_id":615,"group_id":3},{"job_title":271,"department_id":616,"group_id":3},{"job_title":572,"department_id":616,"group_id":3},{"job_title":745,"department_id":616,"group_id":3},{"job_title":271,"department_id":617,"group_id":3},{"job_title":572,"department_id":617,"group_id":3},{"job_title":271,"department_id":625,"group_id":3},{"job_title":572,"department_id":625,"group_id":3},{"job_title":745,"department_id":625,"group_id":3},{"job_title":271,"department_id":626,"group_id":3},{"job_title":572,"department_id":626,"group_id":3},{"job_title":745,"department_id":626,"group_id":3},{"job_title":271,"department_id":627,"group_id":3},{"job_title":572,"department_id":627,"group_id":3},{"job_title":271,"department_id":635,"group_id":3},{"job_title":572,"department_id":635,"group_id":3},{"job_title":271,"department_id":636,"group_id":3},{"job_title":572,"department_id":636,"group_id":3},{"job_title":745,"department_id":636,"group_id":3},{"job_title":271,"department_id":637,"group_id":3},{"job_title":572,"department_id":637,"group_id":3},{"job_title":521,"department_id":64,"group_id":3},{"job_title":1749,"department_id":64,"group_id":3},{"job_title":1750,"department_id":64,"group_id":3},{"job_title":271,"department_id":645,"group_id":3},{"job_title":572,"department_id":645,"group_id":3},{"job_title":271,"department_id":646,"group_id":3},{"job_title":572,"department_id":646,"group_id":3},{"job_title":271,"department_id":647,"group_id":3},{"job_title":271,"department_id":655,"group_id":3},{"job_title":572,"department_id":655,"group_id":3},{"job_title":271,"department_id":656,"group_id":3},{"job_title":572,"department_id":656,"group_id":3},{"job_title":271,"department_id":657,"group_id":3},{"job_title":572,"department_id":657,"group_id":3},{"job_title":271,"department_id":665,"group_id":3},{"job_title":572,"department_id":665,"group_id":3},{"job_title":271,"department_id":666,"group_id":3},{"job_title":572,"department_id":666,"group_id":3},{"job_title":271,"department_id":667,"group_id":3},{"job_title":572,"department_id":667,"group_id":3},{"job_title":1334,"department_id":67,"group_id":3},{"job_title":271,"department_id":675,"group_id":3},{"job_title":572,"department_id":675,"group_id":3},{"job_title":271,"department_id":676,"group_id":3},{"job_title":572,"department_id":676,"group_id":3},{"job_title":271,"department_id":677,"group_id":3},{"job_title":572,"department_id":677,"group_id":3},{"job_title":271,"department_id":685,"group_id":3},{"job_title":572,"department_id":685,"group_id":3},{"job_title":271,"department_id":686,"group_id":3},{"job_title":572,"department_id":686,"group_id":3},{"job_title":271,"department_id":687,"group_id":3},{"job_title":572,"department_id":687,"group_id":3},{"job_title":271,"department_id":695,"group_id":3},{"job_title":572,"department_id":695,"group_id":3},{"job_title":271,"department_id":696,"group_id":3},{"job_title":572,"department_id":696,"group_id":3},{"job_title":271,"department_id":697,"group_id":3},{"job_title":572,"department_id":697,"group_id":3},{"job_title":271,"department_id":705,"group_id":3},{"job_title":572,"department_id":705,"group_id":3},{"job_title":271,"department_id":706,"group_id":3},{"job_title":572,"department_id":706,"group_id":3},{"job_title":271,"department_id":707,"group_id":3},{"job_title":572,"department_id":707,"group_id":3},{"job_title":271,"department_id":715,"group_id":3},{"job_title":572,"department_id":715,"group_id":3},{"job_title":271,"department_id":716,"group_id":3},{"job_title":572,"department_id":716,"group_id":3},{"job_title":745,"department_id":716,"group_id":3},{"job_title":271,"department_id":717,"group_id":3},{"job_title":572,"department_id":717,"group_id":3},{"job_title":745,"department_id":717,"group_id":3},{"job_title":271,"department_id":725,"group_id":3},{"job_title":572,"department_id":725,"group_id":3},{"job_title":271,"department_id":726,"group_id":3},{"job_title":572,"department_id":726,"group_id":3},{"job_title":745,"department_id":726,"group_id":3},{"job_title":271,"department_id":727,"group_id":3},{"job_title":572,"department_id":727,"group_id":3},{"job_title":271,"department_id":735,"group_id":3},{"job_title":572,"department_id":735,"group_id":3},{"job_title":271,"department_id":736,"group_id":3},{"job_title":572,"department_id":736,"group_id":3},{"job_title":271,"department_id":737,"group_id":3},{"job_title":745,"department_id":737,"group_id":3},{"job_title":271,"department_id":745,"group_id":3},{"job_title":572,"department_id":745,"group_id":3},{"job_title":271,"department_id":746,"group_id":3},{"job_title":572,"department_id":746,"group_id":3},{"job_title":271,"department_id":747,"group_id":3},{"job_title":271,"department_id":765,"group_id":3},{"job_title":572,"department_id":765,"group_id":3},{"job_title":271,"department_id":766,"group_id":3},{"job_title":572,"department_id":766,"group_id":3},{"job_title":271,"department_id":767,"group_id":3},{"job_title":572,"department_id":767,"group_id":3},{"job_title":271,"department_id":775,"group_id":3},{"job_title":572,"department_id":775,"group_id":3},{"job_title":271,"department_id":776,"group_id":3},{"job_title":572,"department_id":776,"group_id":3},{"job_title":271,"department_id":777,"group_id":3},{"job_title":572,"department_id":777,"group_id":3},{"job_title":271,"department_id":785,"group_id":3},{"job_title":572,"department_id":785,"group_id":3},{"job_title":745,"department_id":785,"group_id":3},{"job_title":271,"department_id":786,"group_id":3},{"job_title":572,"department_id":786,"group_id":3},{"job_title":745,"department_id":786,"group_id":3},{"job_title":271,"department_id":787,"group_id":3},{"job_title":572,"department_id":787,"group_id":3},{"job_title":271,"department_id":795,"group_id":3},{"job_title":572,"department_id":795,"group_id":3},{"job_title":271,"department_id":796,"group_id":3},{"job_title":572,"department_id":796,"group_id":3},{"job_title":271,"department_id":797,"group_id":3},{"job_title":572,"department_id":797,"group_id":3},{"job_title":300,"department_id":80020,"group_id":3},{"job_title":1301,"department_id":80020,"group_id":3},{"job_title":271,"department_id":805,"group_id":3},{"job_title":572,"department_id":805,"group_id":3},{"job_title":271,"department_id":806,"group_id":3},{"job_title":572,"department_id":806,"group_id":3},{"job_title":271,"department_id":807,"group_id":3},{"job_title":572,"department_id":807,"group_id":3},{"job_title":271,"department_id":815,"group_id":3},{"job_title":572,"department_id":815,"group_id":3},{"job_title":271,"department_id":816,"group_id":3},{"job_title":572,"department_id":816,"group_id":3},{"job_title":271,"department_id":817,"group_id":3},{"job_title":572,"department_id":817,"group_id":3},{"job_title":271,"department_id":825,"group_id":3},{"job_title":572,"department_id":825,"group_id":3},{"job_title":745,"department_id":825,"group_id":3},{"job_title":271,"department_id":826,"group_id":3},{"job_title":572,"department_id":826,"group_id":3},{"job_title":271,"department_id":827,"group_id":3},{"job_title":572,"department_id":827,"group_id":3},{"job_title":271,"department_id":835,"group_id":3},{"job_title":572,"department_id":835,"group_id":3},{"job_title":271,"department_id":836,"group_id":3},{"job_title":572,"department_id":836,"group_id":3},{"job_title":271,"department_id":837,"group_id":3},{"job_title":572,"department_id":837,"group_id":3},{"job_title":271,"department_id":845,"group_id":3},{"job_title":572,"department_id":845,"group_id":3},{"job_title":745,"department_id":845,"group_id":3},{"job_title":271,"department_id":846,"group_id":3},{"job_title":572,"department_id":846,"group_id":3},{"job_title":271,"department_id":847,"group_id":3},{"job_title":572,"department_id":847,"group_id":3},{"job_title":473,"department_id":871,"group_id":3},{"job_title":753,"department_id":871,"group_id":3},{"job_title":271,"department_id":916,"group_id":3},{"job_title":572,"department_id":916,"group_id":3},{"job_title":745,"department_id":916,"group_id":3},{"job_title":271,"department_id":917,"group_id":3},{"job_title":572,"department_id":917,"group_id":3},{"job_title":271,"department_id":918,"group_id":3},{"job_title":572,"department_id":918,"group_id":3},{"job_title":271,"department_id":961,"group_id":3},{"job_title":572,"department_id":961,"group_id":3},{"job_title":271,"department_id":963,"group_id":3},{"job_title":572,"department_id":963,"group_id":3},{"job_title":271,"department_id":966,"group_id":3},{"job_title":572,"department_id":966,"group_id":3},{"job_title":745,"department_id":966,"group_id":3},{"job_title":271,"department_id":981,"group_id":3},{"job_title":572,"department_id":981,"group_id":3},{"job_title":271,"department_id":983,"group_id":3},{"job_title":572,"department_id":983,"group_id":3},{"job_title":271,"department_id":986,"group_id":3},{"job_title":271,"department_id":991,"group_id":3},{"job_title":572,"department_id":991,"group_id":3},{"job_title":271,"department_id":993,"group_id":3},{"job_title":572,"department_id":993,"group_id":3},{"job_title":271,"department_id":996,"group_id":3},{"job_title":745,"department_id":996,"group_id":3},{"job_title":1887,"department_id":1000,"group_id":22},{"job_title":272,"department_id":1002,"group_id":22},{"job_title":272,"department_id":1004,"group_id":22},{"job_title":272,"department_id":1007,"group_id":22},{"job_title":1335,"department_id":1008,"group_id":22},{"job_title":1887,"department_id":1010,"group_id":22},{"job_title":272,"department_id":1012,"group_id":22},{"job_title":272,"department_id":1014,"group_id":22},{"job_title":1335,"department_id":1018,"group_id":22},{"job_title":1887,"department_id":1020,"group_id":22},{"job_title":272,"department_id":1042,"group_id":22},{"job_title":272,"department_id":1044,"group_id":22},{"job_title":272,"department_id":1047,"group_id":22},{"job_title":1335,"department_id":1048,"group_id":22},{"job_title":1887,"department_id":1050,"group_id":22},{"job_title":724,"department_id":106,"group_id":22},{"job_title":725,"department_id":106,"group_id":22},{"job_title":1474,"department_id":106,"group_id":22},{"job_title":1475,"department_id":106,"group_id":22},{"job_title":578,"department_id":1068,"group_id":22},{"job_title":1678,"department_id":1068,"group_id":22},{"job_title":272,"department_id":1070,"group_id":22},{"job_title":272,"department_id":1072,"group_id":22},{"job_title":272,"department_id":1075,"group_id":22},{"job_title":1335,"department_id":1076,"group_id":22},{"job_title":1605,"department_id":1079,"group_id":22},{"job_title":1474,"department_id":1092,"group_id":22},{"job_title":1475,"department_id":1092,"group_id":22},{"job_title":1572,"department_id":1104,"group_id":22},{"job_title":1126,"department_id":1105,"group_id":22},{"job_title":1342,"department_id":1105,"group_id":22},{"job_title":1771,"department_id":1105,"group_id":22},{"job_title":1863,"department_id":1105,"group_id":22},{"job_title":491,"department_id":1110,"group_id":22},{"job_title":800,"department_id":1110,"group_id":22},{"job_title":1952,"department_id":1110,"group_id":22},{"job_title":1499,"department_id":1112,"group_id":22},{"job_title":1500,"department_id":1112,"group_id":22},{"job_title":1501,"department_id":1112,"group_id":22},{"job_title":1502,"department_id":1112,"group_id":22},{"job_title":1503,"department_id":1112,"group_id":22},{"job_title":1954,"department_id":1112,"group_id":22},{"job_title":294,"department_id":1114,"group_id":22},{"job_title":1848,"department_id":1118,"group_id":22},{"job_title":836,"department_id":1120,"group_id":22},{"job_title":1580,"department_id":1120,"group_id":22},{"job_title":1901,"department_id":1120,"group_id":22},{"job_title":158,"department_id":1121,"group_id":22},{"job_title":917,"department_id":1121,"group_id":22},{"job_title":158,"department_id":1122,"group_id":22},{"job_title":917,"department_id":1122,"group_id":22},{"job_title":158,"department_id":1123,"group_id":22},{"job_title":917,"department_id":1123,"group_id":22},{"job_title":158,"department_id":1124,"group_id":22},{"job_title":917,"department_id":1124,"group_id":22},{"job_title":1588,"department_id":1126,"group_id":22},{"job_title":158,"department_id":1128,"group_id":22},{"job_title":836,"department_id":1131,"group_id":22},{"job_title":1901,"department_id":1131,"group_id":22},{"job_title":158,"department_id":1134,"group_id":22},{"job_title":917,"department_id":1134,"group_id":22},{"job_title":158,"department_id":1137,"group_id":22},{"job_title":917,"department_id":1137,"group_id":22},{"job_title":917,"department_id":1139,"group_id":22},{"job_title":916,"department_id":1142,"group_id":22},{"job_title":1901,"department_id":1142,"group_id":22},{"job_title":158,"department_id":1143,"group_id":22},{"job_title":917,"department_id":1143,"group_id":22},{"job_title":917,"department_id":1144,"group_id":22},{"job_title":917,"department_id":1145,"group_id":22},{"job_title":158,"department_id":1146,"group_id":22},{"job_title":1211,"department_id":1147,"group_id":22},{"job_title":1674,"department_id":1147,"group_id":22},{"job_title":158,"department_id":1149,"group_id":22},{"job_title":913,"department_id":1149,"group_id":22},{"job_title":578,"department_id":1154,"group_id":22},{"job_title":272,"department_id":1156,"group_id":22},{"job_title":16,"department_id":1168,"group_id":22},{"job_title":1290,"department_id":1168,"group_id":22},{"job_title":354,"department_id":1178,"group_id":22},{"job_title":16,"department_id":1183,"group_id":22},{"job_title":1291,"department_id":1183,"group_id":22},{"job_title":1176,"department_id":119,"group_id":22},{"job_title":1667,"department_id":1190,"group_id":22},{"job_title":578,"department_id":1192,"group_id":22},{"job_title":272,"department_id":1194,"group_id":22},{"job_title":272,"department_id":1195,"group_id":22},{"job_title":272,"department_id":1198,"group_id":22},{"job_title":1335,"department_id":1199,"group_id":22},{"job_title":1850,"department_id":120,"group_id":22},{"job_title":16,"department_id":1205,"group_id":22},{"job_title":451,"department_id":1205,"group_id":22},{"job_title":576,"department_id":1205,"group_id":22},{"job_title":577,"department_id":1205,"group_id":22},{"job_title":808,"department_id":1205,"group_id":22},{"job_title":1474,"department_id":1205,"group_id":22},{"job_title":1687,"department_id":1205,"group_id":22},{"job_title":1689,"department_id":1205,"group_id":22},{"job_title":1786,"department_id":1205,"group_id":22},{"job_title":1924,"department_id":1205,"group_id":22},{"job_title":1925,"department_id":1205,"group_id":22},{"job_title":1931,"department_id":1205,"group_id":22},{"job_title":1560,"department_id":1207,"group_id":22},{"job_title":1914,"department_id":1207,"group_id":22},{"job_title":917,"department_id":1209,"group_id":22},{"job_title":917,"department_id":1210,"group_id":22},{"job_title":917,"department_id":1211,"group_id":22},{"job_title":272,"department_id":1229,"group_id":22},{"job_title":1748,"department_id":1247,"group_id":22},{"job_title":79,"department_id":1248,"group_id":22},{"job_title":1860,"department_id":1248,"group_id":22},{"job_title":1905,"department_id":1248,"group_id":22},{"job_title":1532,"department_id":1249,"group_id":22},{"job_title":1560,"department_id":1249,"group_id":22},{"job_title":1747,"department_id":1250,"group_id":22},{"job_title":1759,"department_id":1250,"group_id":22},{"job_title":1762,"department_id":1250,"group_id":22},{"job_title":1793,"department_id":1253,"group_id":22},{"job_title":1959,"department_id":1253,"group_id":22},{"job_title":1960,"department_id":1253,"group_id":22},{"job_title":1961,"department_id":1253,"group_id":22},{"job_title":1962,"department_id":1253,"group_id":22},{"job_title":1787,"department_id":1254,"group_id":22},{"job_title":1788,"department_id":1254,"group_id":22},{"job_title":1789,"department_id":1254,"group_id":22},{"job_title":1915,"department_id":1254,"group_id":22},{"job_title":1953,"department_id":1254,"group_id":22},{"job_title":1783,"department_id":1255,"group_id":22},{"job_title":1784,"department_id":1255,"group_id":22},{"job_title":1785,"department_id":1255,"group_id":22},{"job_title":913,"department_id":1256,"group_id":22},{"job_title":1777,"department_id":1257,"group_id":22},{"job_title":1778,"department_id":1257,"group_id":22},{"job_title":1802,"department_id":1257,"group_id":22},{"job_title":1779,"department_id":1258,"group_id":22},{"job_title":1780,"department_id":1258,"group_id":22},{"job_title":1781,"department_id":1258,"group_id":22},{"job_title":1800,"department_id":1258,"group_id":22},{"job_title":1803,"department_id":1260,"group_id":22},{"job_title":1804,"department_id":1260,"group_id":22},{"job_title":1826,"department_id":1267,"group_id":22},{"job_title":1827,"department_id":1267,"group_id":22},{"job_title":1913,"department_id":1267,"group_id":22},{"job_title":1560,"department_id":1271,"group_id":22},{"job_title":1820,"department_id":1271,"group_id":22},{"job_title":1829,"department_id":1271,"group_id":22},{"job_title":1876,"department_id":1271,"group_id":22},{"job_title":1878,"department_id":1271,"group_id":22},{"job_title":1914,"department_id":1271,"group_id":22},{"job_title":1831,"department_id":1272,"group_id":22},{"job_title":1832,"department_id":1272,"group_id":22},{"job_title":354,"department_id":1278,"group_id":22},{"job_title":1862,"department_id":1278,"group_id":22},{"job_title":527,"department_id":1291,"group_id":22},{"job_title":1508,"department_id":1291,"group_id":22},{"job_title":1835,"department_id":1291,"group_id":22},{"job_title":748,"department_id":1292,"group_id":22},{"job_title":749,"department_id":1292,"group_id":22},{"job_title":1306,"department_id":1295,"group_id":22},{"job_title":293,"department_id":13,"group_id":22},{"job_title":1906,"department_id":1311,"group_id":22},{"job_title":1805,"department_id":1312,"group_id":22},{"job_title":1806,"department_id":1312,"group_id":22},{"job_title":1807,"department_id":1312,"group_id":22},{"job_title":1964,"department_id":1312,"group_id":22},{"job_title":917,"department_id":1320,"group_id":22},{"job_title":917,"department_id":1322,"group_id":22},{"job_title":917,"department_id":1327,"group_id":22},{"job_title":158,"department_id":1328,"group_id":22},{"job_title":158,"department_id":1330,"group_id":22},{"job_title":158,"department_id":1335,"group_id":22},{"job_title":917,"department_id":1335,"group_id":22},{"job_title":1596,"department_id":1340,"group_id":22},{"job_title":1766,"department_id":1340,"group_id":22},{"job_title":1918,"department_id":1340,"group_id":22},{"job_title":1990,"department_id":1340,"group_id":22},{"job_title":1991,"department_id":1340,"group_id":22},{"job_title":878,"department_id":18,"group_id":22},{"job_title":1988,"department_id":185,"group_id":22},{"job_title":1014,"department_id":20001,"group_id":22},{"job_title":1596,"department_id":222,"group_id":22},{"job_title":1854,"department_id":243,"group_id":22},{"job_title":1095,"department_id":25,"group_id":22},{"job_title":1096,"department_id":26,"group_id":22},{"job_title":1104,"department_id":26,"group_id":22},{"job_title":1824,"department_id":26,"group_id":22},{"job_title":1849,"department_id":294,"group_id":22},{"job_title":58,"department_id":30,"group_id":22},{"job_title":548,"department_id":30,"group_id":22},{"job_title":1357,"department_id":30,"group_id":22},{"job_title":1790,"department_id":30,"group_id":22},{"job_title":1174,"department_id":30001,"group_id":22},{"job_title":62,"department_id":31,"group_id":22},{"job_title":147,"department_id":31,"group_id":22},{"job_title":274,"department_id":31,"group_id":22},{"job_title":1216,"department_id":31,"group_id":22},{"job_title":1263,"department_id":31,"group_id":22},{"job_title":16,"department_id":32,"group_id":22},{"job_title":79,"department_id":32,"group_id":22},{"job_title":269,"department_id":32,"group_id":22},{"job_title":451,"department_id":32,"group_id":22},{"job_title":1290,"department_id":32,"group_id":22},{"job_title":1786,"department_id":32,"group_id":22},{"job_title":554,"department_id":33,"group_id":22},{"job_title":555,"department_id":33,"group_id":22},{"job_title":556,"department_id":33,"group_id":22},{"job_title":1264,"department_id":33,"group_id":22},{"job_title":799,"department_id":4,"group_id":22},{"job_title":1459,"department_id":452,"group_id":22},{"job_title":16,"department_id":483,"group_id":22},{"job_title":79,"department_id":483,"group_id":22},{"job_title":269,"department_id":483,"group_id":22},{"job_title":451,"department_id":483,"group_id":22},{"job_title":1786,"department_id":483,"group_id":22},{"job_title":1792,"department_id":483,"group_id":22},{"job_title":454,"department_id":50001,"group_id":22},{"job_title":726,"department_id":50001,"group_id":22},{"job_title":11,"department_id":51,"group_id":22},{"job_title":97,"department_id":51,"group_id":22},{"job_title":98,"department_id":51,"group_id":22},{"job_title":101,"department_id":51,"group_id":22},{"job_title":291,"department_id":51,"group_id":22},{"job_title":718,"department_id":51,"group_id":22},{"job_title":719,"department_id":51,"group_id":22},{"job_title":1884,"department_id":51,"group_id":22},{"job_title":1912,"department_id":51,"group_id":22},{"job_title":570,"department_id":557,"group_id":22},{"job_title":576,"department_id":559,"group_id":22},{"job_title":573,"department_id":560,"group_id":22},{"job_title":50,"department_id":562,"group_id":22},{"job_title":578,"department_id":562,"group_id":22},{"job_title":50,"department_id":563,"group_id":22},{"job_title":578,"department_id":563,"group_id":22},{"job_title":50,"department_id":564,"group_id":22},{"job_title":578,"department_id":564,"group_id":22},{"job_title":578,"department_id":565,"group_id":22},{"job_title":50,"department_id":566,"group_id":22},{"job_title":578,"department_id":566,"group_id":22},{"job_title":50,"department_id":567,"group_id":22},{"job_title":578,"department_id":567,"group_id":22},{"job_title":1678,"department_id":567,"group_id":22},{"job_title":578,"department_id":568,"group_id":22},{"job_title":570,"department_id":570,"group_id":22},{"job_title":576,"department_id":572,"group_id":22},{"job_title":573,"department_id":573,"group_id":22},{"job_title":50,"department_id":575,"group_id":22},{"job_title":578,"department_id":575,"group_id":22},{"job_title":1678,"department_id":575,"group_id":22},{"job_title":50,"department_id":576,"group_id":22},{"job_title":578,"department_id":576,"group_id":22},{"job_title":50,"department_id":577,"group_id":22},{"job_title":578,"department_id":577,"group_id":22},{"job_title":1678,"department_id":577,"group_id":22},{"job_title":578,"department_id":578,"group_id":22},{"job_title":578,"department_id":579,"group_id":22},{"job_title":724,"department_id":58,"group_id":22},{"job_title":725,"department_id":58,"group_id":22},{"job_title":1474,"department_id":58,"group_id":22},{"job_title":1475,"department_id":58,"group_id":22},{"job_title":50,"department_id":580,"group_id":22},{"job_title":578,"department_id":580,"group_id":22},{"job_title":50,"department_id":581,"group_id":22},{"job_title":578,"department_id":581,"group_id":22},{"job_title":578,"department_id":582,"group_id":22},{"job_title":570,"department_id":585,"group_id":22},{"job_title":576,"department_id":587,"group_id":22},{"job_title":573,"department_id":588,"group_id":22},{"job_title":724,"department_id":59,"group_id":22},{"job_title":725,"department_id":59,"group_id":22},{"job_title":1474,"department_id":59,"group_id":22},{"job_title":1475,"department_id":59,"group_id":22},{"job_title":50,"department_id":590,"group_id":22},{"job_title":566,"department_id":590,"group_id":22},{"job_title":578,"department_id":590,"group_id":22},{"job_title":50,"department_id":591,"group_id":22},{"job_title":578,"department_id":591,"group_id":22},{"job_title":1678,"department_id":591,"group_id":22},{"job_title":50,"department_id":592,"group_id":22},{"job_title":578,"department_id":592,"group_id":22},{"job_title":50,"department_id":593,"group_id":22},{"job_title":578,"department_id":593,"group_id":22},{"job_title":50,"department_id":594,"group_id":22},{"job_title":578,"department_id":594,"group_id":22},{"job_title":50,"department_id":595,"group_id":22},{"job_title":578,"department_id":595,"group_id":22},{"job_title":570,"department_id":597,"group_id":22},{"job_title":1887,"department_id":599,"group_id":22},{"job_title":387,"department_id":60,"group_id":22},{"job_title":394,"department_id":60,"group_id":22},{"job_title":524,"department_id":60,"group_id":22},{"job_title":655,"department_id":60,"group_id":22},{"job_title":746,"department_id":60,"group_id":22},{"job_title":747,"department_id":60,"group_id":22},{"job_title":1880,"department_id":60,"group_id":22},{"job_title":1881,"department_id":60,"group_id":22},{"job_title":1949,"department_id":60,"group_id":22},{"job_title":1951,"department_id":60,"group_id":22},{"job_title":573,"department_id":600,"group_id":22},{"job_title":1008,"department_id":60001,"group_id":22},{"job_title":50,"department_id":602,"group_id":22},{"job_title":578,"department_id":602,"group_id":22},{"job_title":1678,"department_id":602,"group_id":22},{"job_title":50,"department_id":603,"group_id":22},{"job_title":578,"department_id":603,"group_id":22},{"job_title":50,"department_id":604,"group_id":22},{"job_title":578,"department_id":604,"group_id":22},{"job_title":272,"department_id":605,"group_id":22},{"job_title":272,"department_id":606,"group_id":22},{"job_title":272,"department_id":607,"group_id":22},{"job_title":1335,"department_id":609,"group_id":22},{"job_title":577,"department_id":611,"group_id":22},{"job_title":1887,"department_id":611,"group_id":22},{"job_title":272,"department_id":615,"group_id":22},{"job_title":272,"department_id":616,"group_id":22},{"job_title":566,"department_id":619,"group_id":22},{"job_title":1335,"department_id":619,"group_id":22},{"job_title":388,"department_id":62,"group_id":22},{"job_title":389,"department_id":62,"group_id":22},{"job_title":519,"department_id":62,"group_id":22},{"job_title":527,"department_id":62,"group_id":22},{"job_title":1887,"department_id":621,"group_id":22},{"job_title":272,"department_id":625,"group_id":22},{"job_title":272,"department_id":626,"group_id":22},{"job_title":272,"department_id":627,"group_id":22},{"job_title":1335,"department_id":629,"group_id":22},{"job_title":1887,"department_id":631,"group_id":22},{"job_title":272,"department_id":635,"group_id":22},{"job_title":272,"department_id":636,"group_id":22},{"job_title":272,"department_id":637,"group_id":22},{"job_title":1335,"department_id":639,"group_id":22},{"job_title":132,"department_id":64,"group_id":22},{"job_title":654,"department_id":64,"group_id":22},{"job_title":750,"department_id":64,"group_id":22},{"job_title":1812,"department_id":64,"group_id":22},{"job_title":1911,"department_id":64,"group_id":22},{"job_title":1927,"department_id":64,"group_id":22},{"job_title":1935,"department_id":64,"group_id":22},{"job_title":1983,"department_id":64,"group_id":22},{"job_title":1984,"department_id":64,"group_id":22},{"job_title":1887,"department_id":641,"group_id":22},{"job_title":272,"department_id":645,"group_id":22},{"job_title":272,"department_id":646,"group_id":22},{"job_title":272,"department_id":647,"group_id":22},{"job_title":1335,"department_id":649,"group_id":22},{"job_title":1887,"department_id":651,"group_id":22},{"job_title":272,"department_id":655,"group_id":22},{"job_title":272,"department_id":656,"group_id":22},{"job_title":272,"department_id":657,"group_id":22},{"job_title":566,"department_id":659,"group_id":22},{"job_title":1335,"department_id":659,"group_id":22},{"job_title":576,"department_id":661,"group_id":22},{"job_title":577,"department_id":661,"group_id":22},{"job_title":1887,"department_id":661,"group_id":22},{"job_title":272,"department_id":665,"group_id":22},{"job_title":272,"department_id":666,"group_id":22},{"job_title":272,"department_id":667,"group_id":22},{"job_title":1335,"department_id":669,"group_id":22},{"job_title":560,"department_id":67,"group_id":22},{"job_title":565,"department_id":67,"group_id":22},{"job_title":571,"department_id":67,"group_id":22},{"job_title":1887,"department_id":671,"group_id":22},{"job_title":272,"department_id":675,"group_id":22},{"job_title":272,"department_id":676,"group_id":22},{"job_title":272,"department_id":677,"group_id":22},{"job_title":566,"department_id":679,"group_id":22},{"job_title":1335,"department_id":679,"group_id":22},{"job_title":576,"department_id":681,"group_id":22},{"job_title":577,"department_id":681,"group_id":22},{"job_title":1887,"department_id":681,"group_id":22},{"job_title":272,"department_id":685,"group_id":22},{"job_title":272,"department_id":686,"group_id":22},{"job_title":566,"department_id":689,"group_id":22},{"job_title":1335,"department_id":689,"group_id":22},{"job_title":561,"department_id":69,"group_id":22},{"job_title":566,"department_id":69,"group_id":22},{"job_title":570,"department_id":69,"group_id":22},{"job_title":1335,"department_id":69,"group_id":22},{"job_title":1364,"department_id":69,"group_id":22},{"job_title":1365,"department_id":69,"group_id":22},{"job_title":1366,"department_id":69,"group_id":22},{"job_title":1645,"department_id":69,"group_id":22},{"job_title":577,"department_id":691,"group_id":22},{"job_title":1887,"department_id":691,"group_id":22},{"job_title":272,"department_id":695,"group_id":22},{"job_title":272,"department_id":696,"group_id":22},{"job_title":272,"department_id":697,"group_id":22},{"job_title":566,"department_id":699,"group_id":22},{"job_title":575,"department_id":70,"group_id":22},{"job_title":576,"department_id":70,"group_id":22},{"job_title":577,"department_id":70,"group_id":22},{"job_title":1285,"department_id":70,"group_id":22},{"job_title":577,"department_id":701,"group_id":22},{"job_title":1887,"department_id":701,"group_id":22},{"job_title":272,"department_id":705,"group_id":22},{"job_title":272,"department_id":706,"group_id":22},{"job_title":272,"department_id":707,"group_id":22},{"job_title":1335,"department_id":709,"group_id":22},{"job_title":562,"department_id":71,"group_id":22},{"job_title":567,"department_id":71,"group_id":22},{"job_title":573,"department_id":71,"group_id":22},{"job_title":1210,"department_id":71,"group_id":22},{"job_title":1677,"department_id":71,"group_id":22},{"job_title":577,"department_id":711,"group_id":22},{"job_title":1887,"department_id":711,"group_id":22},{"job_title":272,"department_id":715,"group_id":22},{"job_title":272,"department_id":716,"group_id":22},{"job_title":272,"department_id":717,"group_id":22},{"job_title":566,"department_id":719,"group_id":22},{"job_title":1335,"department_id":719,"group_id":22},{"job_title":1887,"department_id":721,"group_id":22},{"job_title":272,"department_id":725,"group_id":22},{"job_title":272,"department_id":726,"group_id":22},{"job_title":272,"department_id":727,"group_id":22},{"job_title":1335,"department_id":729,"group_id":22},{"job_title":577,"department_id":731,"group_id":22},{"job_title":1887,"department_id":731,"group_id":22},{"job_title":272,"department_id":735,"group_id":22},{"job_title":272,"department_id":736,"group_id":22},{"job_title":272,"department_id":737,"group_id":22},{"job_title":1335,"department_id":739,"group_id":22},{"job_title":1887,"department_id":741,"group_id":22},{"job_title":272,"department_id":745,"group_id":22},{"job_title":272,"department_id":747,"group_id":22},{"job_title":1335,"department_id":749,"group_id":22},{"job_title":1887,"department_id":751,"group_id":22},{"job_title":272,"department_id":765,"group_id":22},{"job_title":272,"department_id":766,"group_id":22},{"job_title":566,"department_id":769,"group_id":22},{"job_title":577,"department_id":771,"group_id":22},{"job_title":1887,"department_id":771,"group_id":22},{"job_title":272,"department_id":775,"group_id":22},{"job_title":272,"department_id":776,"group_id":22},{"job_title":272,"department_id":777,"group_id":22},{"job_title":566,"department_id":779,"group_id":22},{"job_title":1335,"department_id":779,"group_id":22},{"job_title":576,"department_id":781,"group_id":22},{"job_title":577,"department_id":781,"group_id":22},{"job_title":1887,"department_id":781,"group_id":22},{"job_title":272,"department_id":785,"group_id":22},{"job_title":272,"department_id":786,"group_id":22},{"job_title":272,"department_id":787,"group_id":22},{"job_title":1335,"department_id":789,"group_id":22},{"job_title":577,"department_id":791,"group_id":22},{"job_title":1887,"department_id":791,"group_id":22},{"job_title":272,"department_id":795,"group_id":22},{"job_title":272,"department_id":796,"group_id":22},{"job_title":1335,"department_id":799,"group_id":22},{"job_title":1974,"department_id":80008,"group_id":22},{"job_title":1975,"department_id":80008,"group_id":22},{"job_title":1977,"department_id":80008,"group_id":22},{"job_title":1971,"department_id":80009,"group_id":22},{"job_title":1972,"department_id":80009,"group_id":22},{"job_title":1973,"department_id":80009,"group_id":22},{"job_title":733,"department_id":80017,"group_id":22},{"job_title":917,"department_id":80020,"group_id":22},{"job_title":1887,"department_id":801,"group_id":22},{"job_title":272,"department_id":805,"group_id":22},{"job_title":272,"department_id":806,"group_id":22},{"job_title":272,"department_id":807,"group_id":22},{"job_title":1335,"department_id":809,"group_id":22},{"job_title":1887,"department_id":811,"group_id":22},{"job_title":272,"department_id":815,"group_id":22},{"job_title":272,"department_id":817,"group_id":22},{"job_title":1335,"department_id":819,"group_id":22},{"job_title":1887,"department_id":821,"group_id":22},{"job_title":272,"department_id":825,"group_id":22},{"job_title":272,"department_id":826,"group_id":22},{"job_title":272,"department_id":827,"group_id":22},{"job_title":566,"department_id":829,"group_id":22},{"job_title":1335,"department_id":829,"group_id":22},{"job_title":1647,"department_id":83,"group_id":22},{"job_title":1648,"department_id":83,"group_id":22},{"job_title":1761,"department_id":83,"group_id":22},{"job_title":577,"department_id":831,"group_id":22},{"job_title":1887,"department_id":831,"group_id":22},{"job_title":272,"department_id":835,"group_id":22},{"job_title":272,"department_id":836,"group_id":22},{"job_title":272,"department_id":837,"group_id":22},{"job_title":1335,"department_id":839,"group_id":22},{"job_title":65,"department_id":84,"group_id":22},{"job_title":79,"department_id":84,"group_id":22},{"job_title":1217,"department_id":84,"group_id":22},{"job_title":1887,"department_id":841,"group_id":22},{"job_title":272,"department_id":845,"group_id":22},{"job_title":272,"department_id":846,"group_id":22},{"job_title":272,"department_id":847,"group_id":22},{"job_title":566,"department_id":849,"group_id":22},{"job_title":1335,"department_id":849,"group_id":22},{"job_title":577,"department_id":851,"group_id":22},{"job_title":1887,"department_id":851,"group_id":22},{"job_title":1858,"department_id":86,"group_id":22},{"job_title":1985,"department_id":86,"group_id":22},{"job_title":1986,"department_id":86,"group_id":22},{"job_title":1987,"department_id":86,"group_id":22},{"job_title":1302,"department_id":864,"group_id":22},{"job_title":675,"department_id":871,"group_id":22},{"job_title":50,"department_id":883,"group_id":22},{"job_title":578,"department_id":883,"group_id":22},{"job_title":578,"department_id":884,"group_id":22},{"job_title":578,"department_id":888,"group_id":22},{"job_title":578,"department_id":895,"group_id":22},{"job_title":1678,"department_id":895,"group_id":22},{"job_title":578,"department_id":896,"group_id":22},{"job_title":578,"department_id":897,"group_id":22},{"job_title":578,"department_id":908,"group_id":22},{"job_title":1335,"department_id":913,"group_id":22},{"job_title":577,"department_id":914,"group_id":22},{"job_title":1887,"department_id":914,"group_id":22},{"job_title":272,"department_id":916,"group_id":22},{"job_title":272,"department_id":917,"group_id":22},{"job_title":272,"department_id":918,"group_id":22},{"job_title":724,"department_id":95,"group_id":22},{"job_title":725,"department_id":95,"group_id":22},{"job_title":1474,"department_id":95,"group_id":22},{"job_title":1475,"department_id":95,"group_id":22},{"job_title":272,"department_id":961,"group_id":22},{"job_title":272,"department_id":963,"group_id":22},{"job_title":1335,"department_id":967,"group_id":22},{"job_title":577,"department_id":969,"group_id":22},{"job_title":1887,"department_id":969,"group_id":22},{"job_title":272,"department_id":981,"group_id":22},{"job_title":272,"department_id":983,"group_id":22},{"job_title":272,"department_id":986,"group_id":22},{"job_title":566,"department_id":987,"group_id":22},{"job_title":1335,"department_id":987,"group_id":22},{"job_title":1887,"department_id":989,"group_id":22},{"job_title":272,"department_id":991,"group_id":22},{"job_title":272,"department_id":996,"group_id":22},{"job_title":1335,"department_id":997,"group_id":22},{"job_title":287,"department_id":1191,"group_id":12},{"job_title":288,"department_id":1191,"group_id":12},{"job_title":1269,"department_id":1191,"group_id":12},{"job_title":1851,"department_id":1191,"group_id":12},{"job_title":795,"department_id":1269,"group_id":12},{"job_title":1910,"department_id":1279,"group_id":12},{"job_title":796,"department_id":1339,"group_id":12},{"job_title":1023,"department_id":1339,"group_id":12},{"job_title":68,"department_id":187,"group_id":12},{"job_title":287,"department_id":187,"group_id":12},{"job_title":288,"department_id":187,"group_id":12},{"job_title":287,"department_id":20,"group_id":12},{"job_title":288,"department_id":20,"group_id":12},{"job_title":619,"department_id":20,"group_id":12},{"job_title":647,"department_id":20,"group_id":12},{"job_title":929,"department_id":10,"group_id":24},{"job_title":73,"department_id":1085,"group_id":24},{"job_title":259,"department_id":1085,"group_id":24},{"job_title":465,"department_id":1085,"group_id":24},{"job_title":469,"department_id":1085,"group_id":24},{"job_title":731,"department_id":1085,"group_id":24},{"job_title":771,"department_id":1085,"group_id":24},{"job_title":1816,"department_id":1114,"group_id":24},{"job_title":1968,"department_id":1114,"group_id":24},{"job_title":1969,"department_id":1114,"group_id":24},{"job_title":1970,"department_id":1114,"group_id":24},{"job_title":1441,"department_id":1298,"group_id":24},{"job_title":1923,"department_id":1299,"group_id":24},{"job_title":73,"department_id":1300,"group_id":24},{"job_title":149,"department_id":1300,"group_id":24},{"job_title":462,"department_id":1300,"group_id":24},{"job_title":469,"department_id":1300,"group_id":24},{"job_title":771,"department_id":1300,"group_id":24},{"job_title":946,"department_id":1300,"group_id":24},{"job_title":465,"department_id":1302,"group_id":24},{"job_title":471,"department_id":1302,"group_id":24},{"job_title":731,"department_id":1302,"group_id":24},{"job_title":1903,"department_id":1302,"group_id":24},{"job_title":1956,"department_id":1302,"group_id":24},{"job_title":469,"department_id":1316,"group_id":24},{"job_title":1928,"department_id":1316,"group_id":24},{"job_title":1075,"department_id":1343,"group_id":24},{"job_title":1490,"department_id":217,"group_id":24},{"job_title":1075,"department_id":273,"group_id":24},{"job_title":73,"department_id":36,"group_id":24},{"job_title":259,"department_id":36,"group_id":24},{"job_title":465,"department_id":36,"group_id":24},{"job_title":469,"department_id":36,"group_id":24},{"job_title":579,"department_id":36,"group_id":24},{"job_title":731,"department_id":36,"group_id":24},{"job_title":771,"department_id":36,"group_id":24},{"job_title":946,"department_id":388,"group_id":24},{"job_title":73,"department_id":389,"group_id":24},{"job_title":149,"department_id":389,"group_id":24},{"job_title":462,"department_id":389,"group_id":24},{"job_title":469,"department_id":389,"group_id":24},{"job_title":731,"department_id":389,"group_id":24},{"job_title":771,"department_id":389,"group_id":24},{"job_title":1075,"department_id":389,"group_id":24},{"job_title":1137,"department_id":389,"group_id":24},{"job_title":1428,"department_id":389,"group_id":24},{"job_title":73,"department_id":392,"group_id":24},{"job_title":259,"department_id":392,"group_id":24},{"job_title":465,"department_id":392,"group_id":24},{"job_title":731,"department_id":392,"group_id":24},{"job_title":771,"department_id":392,"group_id":24},{"job_title":73,"department_id":393,"group_id":24},{"job_title":259,"department_id":393,"group_id":24},{"job_title":465,"department_id":393,"group_id":24},{"job_title":469,"department_id":393,"group_id":24},{"job_title":731,"department_id":393,"group_id":24},{"job_title":1048,"department_id":439,"group_id":24},{"job_title":471,"department_id":80006,"group_id":24},{"job_title":777,"department_id":80006,"group_id":24},{"job_title":1268,"department_id":80006,"group_id":24},{"job_title":1956,"department_id":80006,"group_id":24},{"job_title":731,"department_id":80016,"group_id":24},{"job_title":73,"department_id":861,"group_id":24},{"job_title":259,"department_id":861,"group_id":24},{"job_title":465,"department_id":861,"group_id":24},{"job_title":731,"department_id":861,"group_id":24},{"job_title":1411,"department_id":1006,"group_id":9},{"job_title":1410,"department_id":1016,"group_id":9},{"job_title":1411,"department_id":1016,"group_id":9},{"job_title":1411,"department_id":1046,"group_id":9},{"job_title":1410,"department_id":1074,"group_id":9},{"job_title":1411,"department_id":1074,"group_id":9},{"job_title":1148,"department_id":1126,"group_id":9},{"job_title":1148,"department_id":1141,"group_id":9},{"job_title":1148,"department_id":1149,"group_id":9},{"job_title":1411,"department_id":1197,"group_id":9},{"job_title":1411,"department_id":1229,"group_id":9},{"job_title":1760,"department_id":1251,"group_id":9},{"job_title":1763,"department_id":1251,"group_id":9},{"job_title":1770,"department_id":1251,"group_id":9},{"job_title":912,"department_id":1256,"group_id":9},{"job_title":1045,"department_id":1256,"group_id":9},{"job_title":1148,"department_id":1256,"group_id":9},{"job_title":1775,"department_id":1256,"group_id":9},{"job_title":1933,"department_id":1338,"group_id":9},{"job_title":1934,"department_id":1338,"group_id":9},{"job_title":1965,"department_id":1338,"group_id":9},{"job_title":711,"department_id":51,"group_id":9},{"job_title":712,"department_id":51,"group_id":9},{"job_title":1955,"department_id":51,"group_id":9},{"job_title":1410,"department_id":556,"group_id":9},{"job_title":1413,"department_id":556,"group_id":9},{"job_title":1410,"department_id":563,"group_id":9},{"job_title":1410,"department_id":564,"group_id":9},{"job_title":1410,"department_id":567,"group_id":9},{"job_title":1410,"department_id":568,"group_id":9},{"job_title":1410,"department_id":569,"group_id":9},{"job_title":1413,"department_id":569,"group_id":9},{"job_title":1410,"department_id":584,"group_id":9},{"job_title":1413,"department_id":584,"group_id":9},{"job_title":1410,"department_id":592,"group_id":9},{"job_title":1410,"department_id":594,"group_id":9},{"job_title":1410,"department_id":596,"group_id":9},{"job_title":1413,"department_id":596,"group_id":9},{"job_title":1411,"department_id":608,"group_id":9},{"job_title":1411,"department_id":628,"group_id":9},{"job_title":1411,"department_id":638,"group_id":9},{"job_title":1410,"department_id":648,"group_id":9},{"job_title":1411,"department_id":648,"group_id":9},{"job_title":1410,"department_id":658,"group_id":9},{"job_title":1411,"department_id":658,"group_id":9},{"job_title":564,"department_id":66,"group_id":9},{"job_title":1070,"department_id":66,"group_id":9},{"job_title":1071,"department_id":66,"group_id":9},{"job_title":1336,"department_id":66,"group_id":9},{"job_title":1604,"department_id":66,"group_id":9},{"job_title":1653,"department_id":66,"group_id":9},{"job_title":1410,"department_id":678,"group_id":9},{"job_title":1411,"department_id":678,"group_id":9},{"job_title":1410,"department_id":688,"group_id":9},{"job_title":1411,"department_id":688,"group_id":9},{"job_title":1410,"department_id":698,"group_id":9},{"job_title":1411,"department_id":698,"group_id":9},{"job_title":1411,"department_id":708,"group_id":9},{"job_title":1411,"department_id":718,"group_id":9},{"job_title":1411,"department_id":728,"group_id":9},{"job_title":1411,"department_id":738,"group_id":9},{"job_title":1411,"department_id":748,"group_id":9},{"job_title":1410,"department_id":768,"group_id":9},{"job_title":1411,"department_id":768,"group_id":9},{"job_title":1410,"department_id":778,"group_id":9},{"job_title":1411,"department_id":778,"group_id":9},{"job_title":1411,"department_id":788,"group_id":9},{"job_title":1411,"department_id":798,"group_id":9},{"job_title":666,"department_id":80007,"group_id":9},{"job_title":667,"department_id":80007,"group_id":9},{"job_title":781,"department_id":80007,"group_id":9},{"job_title":1570,"department_id":80007,"group_id":9},{"job_title":1922,"department_id":80007,"group_id":9},{"job_title":1410,"department_id":808,"group_id":9},{"job_title":1411,"department_id":818,"group_id":9},{"job_title":1410,"department_id":828,"group_id":9},{"job_title":1411,"department_id":828,"group_id":9},{"job_title":1410,"department_id":838,"group_id":9},{"job_title":1411,"department_id":838,"group_id":9},{"job_title":1411,"department_id":848,"group_id":9},{"job_title":1129,"department_id":86,"group_id":9},{"job_title":1130,"department_id":86,"group_id":9},{"job_title":1410,"department_id":883,"group_id":9},{"job_title":1410,"department_id":884,"group_id":9},{"job_title":1410,"department_id":911,"group_id":9},{"job_title":1411,"department_id":911,"group_id":9},{"job_title":1882,"department_id":94,"group_id":9},{"job_title":1883,"department_id":94,"group_id":9},{"job_title":1411,"department_id":965,"group_id":9},{"job_title":1410,"department_id":985,"group_id":9},{"job_title":82,"department_id":1098,"group_id":4},{"job_title":83,"department_id":1098,"group_id":4},{"job_title":1017,"department_id":1098,"group_id":4},{"job_title":1423,"department_id":1098,"group_id":4},{"job_title":1533,"department_id":1207,"group_id":4},{"job_title":1533,"department_id":1249,"group_id":4},{"job_title":1900,"department_id":1268,"group_id":4},{"job_title":306,"department_id":1271,"group_id":4},{"job_title":1533,"department_id":1271,"group_id":4},{"job_title":1350,"department_id":1301,"group_id":4},{"job_title":886,"department_id":134,"group_id":4},{"job_title":950,"department_id":134,"group_id":4},{"job_title":83,"department_id":504,"group_id":4},{"job_title":1160,"department_id":428,"group_id":8},{"job_title":1859,"department_id":429,"group_id":8},{"job_title":1817,"department_id":430,"group_id":8}]',
                    '[{"job_id":11856,"job_group_id":23},{"job_id":11350,"job_group_id":23},{"job_id":11392,"job_group_id":23},{"job_id":11241,"job_group_id":23},{"job_id":11349,"job_group_id":23},{"job_id":11624,"job_group_id":23},{"job_id":12105,"job_group_id":23},{"job_id":11642,"job_group_id":23},{"job_id":11241,"job_group_id":23},{"job_id":10749,"job_group_id":23},{"job_id":11856,"job_group_id":23},{"job_id":10778,"job_group_id":23},{"job_id":11349,"job_group_id":23},{"job_id":11523,"job_group_id":23},{"job_id":10749,"job_group_id":23},{"job_id":10778,"job_group_id":23},{"job_id":10783,"job_group_id":23},{"job_id":10783,"job_group_id":23},{"job_id":11237,"job_group_id":23},{"job_id":10743,"job_group_id":23},{"job_id":10749,"job_group_id":23},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":10256,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11541,"job_group_id":10},{"job_id":11540,"job_group_id":10},{"job_id":10427,"job_group_id":10},{"job_id":10429,"job_group_id":10},{"job_id":10428,"job_group_id":10},{"job_id":12180,"job_group_id":10},{"job_id":12181,"job_group_id":10},{"job_id":10308,"job_group_id":10},{"job_id":10389,"job_group_id":10},{"job_id":12204,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":10750,"job_group_id":10},{"job_id":12140,"job_group_id":10},{"job_id":12141,"job_group_id":10},{"job_id":12182,"job_group_id":10},{"job_id":12184,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11740,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11740,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11740,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10426,"job_group_id":10},{"job_id":10389,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":10161,"job_group_id":10},{"job_id":10423,"job_group_id":10},{"job_id":10422,"job_group_id":10},{"job_id":10256,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":11616,"job_group_id":10},{"job_id":10480,"job_group_id":10},{"job_id":10959,"job_group_id":26},{"job_id":10960,"job_group_id":26},{"job_id":11315,"job_group_id":26},{"job_id":11958,"job_group_id":26},{"job_id":11958,"job_group_id":26},{"job_id":11471,"job_group_id":26},{"job_id":11470,"job_group_id":26},{"job_id":11958,"job_group_id":26},{"job_id":10045,"job_group_id":26},{"job_id":12165,"job_group_id":26},{"job_id":11846,"job_group_id":26},{"job_id":11979,"job_group_id":26},{"job_id":11995,"job_group_id":26},{"job_id":11787,"job_group_id":25},{"job_id":10333,"job_group_id":25},{"job_id":11295,"job_group_id":25},{"job_id":12203,"job_group_id":25},{"job_id":12203,"job_group_id":25},{"job_id":11768,"job_group_id":25},{"job_id":12228,"job_group_id":1},{"job_id":10984,"job_group_id":1},{"job_id":12115,"job_group_id":1},{"job_id":12116,"job_group_id":1},{"job_id":12117,"job_group_id":1},{"job_id":12118,"job_group_id":1},{"job_id":12119,"job_group_id":1},{"job_id":12120,"job_group_id":1},{"job_id":12121,"job_group_id":1},{"job_id":12122,"job_group_id":1},{"job_id":12123,"job_group_id":1},{"job_id":12124,"job_group_id":1},{"job_id":12125,"job_group_id":1},{"job_id":11949,"job_group_id":1},{"job_id":10232,"job_group_id":1},{"job_id":10984,"job_group_id":1},{"job_id":10036,"job_group_id":1},{"job_id":11578,"job_group_id":1},{"job_id":11580,"job_group_id":1},{"job_id":11587,"job_group_id":1},{"job_id":11589,"job_group_id":1},{"job_id":11858,"job_group_id":1},{"job_id":10232,"job_group_id":1},{"job_id":11781,"job_group_id":1},{"job_id":11861,"job_group_id":1},{"job_id":11862,"job_group_id":1},{"job_id":12229,"job_group_id":1},{"job_id":11582,"job_group_id":1},{"job_id":10434,"job_group_id":1},{"job_id":11592,"job_group_id":1},{"job_id":11595,"job_group_id":1},{"job_id":10364,"job_group_id":1},{"job_id":10434,"job_group_id":1},{"job_id":11592,"job_group_id":1},{"job_id":11474,"job_group_id":1},{"job_id":11779,"job_group_id":1},{"job_id":11780,"job_group_id":1},{"job_id":11849,"job_group_id":1},{"job_id":12238,"job_group_id":1},{"job_id":10330,"job_group_id":1},{"job_id":11988,"job_group_id":1},{"job_id":12225,"job_group_id":1},{"job_id":12226,"job_group_id":1},{"job_id":11455,"job_group_id":1},{"job_id":11454,"job_group_id":1},{"job_id":11734,"job_group_id":1},{"job_id":11850,"job_group_id":1},{"job_id":11583,"job_group_id":1},{"job_id":10291,"job_group_id":1},{"job_id":11594,"job_group_id":1},{"job_id":10986,"job_group_id":1},{"job_id":11913,"job_group_id":11},{"job_id":11958,"job_group_id":11},{"job_id":11975,"job_group_id":11},{"job_id":10313,"job_group_id":11},{"job_id":11213,"job_group_id":11},{"job_id":12151,"job_group_id":11},{"job_id":10123,"job_group_id":11},{"job_id":10492,"job_group_id":11},{"job_id":11946,"job_group_id":11},{"job_id":11728,"job_group_id":11},{"job_id":10173,"job_group_id":11},{"job_id":12098,"job_group_id":11},{"job_id":10493,"job_group_id":11},{"job_id":11790,"job_group_id":11},{"job_id":10394,"job_group_id":11},{"job_id":11958,"job_group_id":11},{"job_id":11958,"job_group_id":11},{"job_id":11958,"job_group_id":11},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11324,"job_group_id":16},{"job_id":11323,"job_group_id":16},{"job_id":11570,"job_group_id":16},{"job_id":11571,"job_group_id":16},{"job_id":11574,"job_group_id":16},{"job_id":11575,"job_group_id":16},{"job_id":12073,"job_group_id":16},{"job_id":12170,"job_group_id":16},{"job_id":10408,"job_group_id":16},{"job_id":11324,"job_group_id":16},{"job_id":11323,"job_group_id":16},{"job_id":11766,"job_group_id":16},{"job_id":10249,"job_group_id":16},{"job_id":11219,"job_group_id":16},{"job_id":11327,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":12176,"job_group_id":16},{"job_id":10224,"job_group_id":16},{"job_id":12148,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11941,"job_group_id":16},{"job_id":12138,"job_group_id":16},{"job_id":10437,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11969,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11708,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10517,"job_group_id":16},{"job_id":11313,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":12137,"job_group_id":16},{"job_id":11532,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11313,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10249,"job_group_id":16},{"job_id":11073,"job_group_id":16},{"job_id":12137,"job_group_id":16},{"job_id":10118,"job_group_id":16},{"job_id":10204,"job_group_id":16},{"job_id":11433,"job_group_id":16},{"job_id":11955,"job_group_id":16},{"job_id":11893,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":12138,"job_group_id":16},{"job_id":12129,"job_group_id":16},{"job_id":11313,"job_group_id":16},{"job_id":11815,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11791,"job_group_id":16},{"job_id":12138,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11739,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11547,"job_group_id":16},{"job_id":11219,"job_group_id":16},{"job_id":11327,"job_group_id":16},{"job_id":10224,"job_group_id":16},{"job_id":10144,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10144,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10144,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10144,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10546,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10222,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10144,"job_group_id":16},{"job_id":10144,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11958,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":11928,"job_group_id":16},{"job_id":12102,"job_group_id":27},{"job_id":12160,"job_group_id":27},{"job_id":12029,"job_group_id":27},{"job_id":12029,"job_group_id":27},{"job_id":11333,"job_group_id":27},{"job_id":11866,"job_group_id":27},{"job_id":12022,"job_group_id":27},{"job_id":12099,"job_group_id":27},{"job_id":10091,"job_group_id":27},{"job_id":11228,"job_group_id":27},{"job_id":11904,"job_group_id":27},{"job_id":12135,"job_group_id":27},{"job_id":12183,"job_group_id":27},{"job_id":11958,"job_group_id":27},{"job_id":11466,"job_group_id":27},{"job_id":11467,"job_group_id":27},{"job_id":11934,"job_group_id":27},{"job_id":11932,"job_group_id":27},{"job_id":11933,"job_group_id":27},{"job_id":11868,"job_group_id":15},{"job_id":11870,"job_group_id":15},{"job_id":11871,"job_group_id":15},{"job_id":11837,"job_group_id":15},{"job_id":11836,"job_group_id":15},{"job_id":11884,"job_group_id":15},{"job_id":12173,"job_group_id":15},{"job_id":10028,"job_group_id":15},{"job_id":11617,"job_group_id":15},{"job_id":10558,"job_group_id":15},{"job_id":10188,"job_group_id":15},{"job_id":11395,"job_group_id":15},{"job_id":11546,"job_group_id":15},{"job_id":11855,"job_group_id":15},{"job_id":11968,"job_group_id":15},{"job_id":10623,"job_group_id":15},{"job_id":11855,"job_group_id":15},{"job_id":11855,"job_group_id":15},{"job_id":10188,"job_group_id":15},{"job_id":10187,"job_group_id":15},{"job_id":11395,"job_group_id":15},{"job_id":11546,"job_group_id":15},{"job_id":10293,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":10305,"job_group_id":15},{"job_id":11202,"job_group_id":15},{"job_id":11304,"job_group_id":15},{"job_id":11365,"job_group_id":15},{"job_id":11428,"job_group_id":15},{"job_id":11724,"job_group_id":15},{"job_id":11414,"job_group_id":15},{"job_id":10294,"job_group_id":15},{"job_id":12024,"job_group_id":15},{"job_id":12090,"job_group_id":15},{"job_id":12092,"job_group_id":15},{"job_id":12094,"job_group_id":15},{"job_id":12091,"job_group_id":15},{"job_id":12093,"job_group_id":15},{"job_id":12095,"job_group_id":15},{"job_id":12209,"job_group_id":15},{"job_id":11967,"job_group_id":15},{"job_id":10632,"job_group_id":15},{"job_id":11833,"job_group_id":15},{"job_id":10318,"job_group_id":13},{"job_id":12106,"job_group_id":13},{"job_id":11643,"job_group_id":13},{"job_id":11958,"job_group_id":13},{"job_id":11958,"job_group_id":13},{"job_id":12106,"job_group_id":13},{"job_id":12152,"job_group_id":13},{"job_id":11762,"job_group_id":13},{"job_id":11841,"job_group_id":13},{"job_id":11272,"job_group_id":13},{"job_id":11585,"job_group_id":13},{"job_id":12075,"job_group_id":13},{"job_id":12185,"job_group_id":13},{"job_id":12186,"job_group_id":13},{"job_id":12187,"job_group_id":13},{"job_id":12190,"job_group_id":13},{"job_id":12188,"job_group_id":13},{"job_id":12194,"job_group_id":13},{"job_id":12212,"job_group_id":13},{"job_id":12213,"job_group_id":13},{"job_id":12103,"job_group_id":13},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":10615,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11550,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":10615,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":10615,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":10916,"job_group_id":3},{"job_id":7,"job_group_id":3},{"job_id":10003,"job_group_id":3},{"job_id":10086,"job_group_id":3},{"job_id":11481,"job_group_id":3},{"job_id":10916,"job_group_id":3},{"job_id":7,"job_group_id":3},{"job_id":10003,"job_group_id":3},{"job_id":10086,"job_group_id":3},{"job_id":11958,"job_group_id":3},{"job_id":11416,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":10899,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":10899,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":10899,"job_group_id":3},{"job_id":7,"job_group_id":3},{"job_id":10003,"job_group_id":3},{"job_id":11680,"job_group_id":3},{"job_id":10086,"job_group_id":3},{"job_id":12096,"job_group_id":3},{"job_id":12097,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":11732,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":10615,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":12153,"job_group_id":3},{"job_id":12224,"job_group_id":3},{"job_id":11462,"job_group_id":3},{"job_id":10916,"job_group_id":3},{"job_id":7,"job_group_id":3},{"job_id":10003,"job_group_id":3},{"job_id":11680,"job_group_id":3},{"job_id":10543,"job_group_id":3},{"job_id":11093,"job_group_id":3},{"job_id":10086,"job_group_id":3},{"job_id":11706,"job_group_id":3},{"job_id":11914,"job_group_id":3},{"job_id":11915,"job_group_id":3},{"job_id":12096,"job_group_id":3},{"job_id":12097,"job_group_id":3},{"job_id":12174,"job_group_id":3},{"job_id":11462,"job_group_id":3},{"job_id":11430,"job_group_id":3},{"job_id":11435,"job_group_id":3},{"job_id":11485,"job_group_id":3},{"job_id":11487,"job_group_id":3},{"job_id":10916,"job_group_id":3},{"job_id":7,"job_group_id":3},{"job_id":10003,"job_group_id":3},{"job_id":11680,"job_group_id":3},{"job_id":10543,"job_group_id":3},{"job_id":11093,"job_group_id":3},{"job_id":10086,"job_group_id":3},{"job_id":11706,"job_group_id":3},{"job_id":11914,"job_group_id":3},{"job_id":11915,"job_group_id":3},{"job_id":12096,"job_group_id":3},{"job_id":12097,"job_group_id":3},{"job_id":12174,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":10362,"job_group_id":3},{"job_id":10362,"job_group_id":3},{"job_id":10303,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":10283,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11492,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":10899,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":10260,"job_group_id":3},{"job_id":10272,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":11613,"job_group_id":3},{"job_id":11047,"job_group_id":3},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":10686,"job_group_id":22},{"job_id":11270,"job_group_id":22},{"job_id":11680,"job_group_id":22},{"job_id":11679,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11852,"job_group_id":22},{"job_id":11680,"job_group_id":22},{"job_id":11679,"job_group_id":22},{"job_id":11816,"job_group_id":22},{"job_id":11549,"job_group_id":22},{"job_id":11548,"job_group_id":22},{"job_id":12026,"job_group_id":22},{"job_id":12113,"job_group_id":22},{"job_id":10647,"job_group_id":22},{"job_id":10893,"job_group_id":22},{"job_id":12196,"job_group_id":22},{"job_id":11711,"job_group_id":22},{"job_id":11712,"job_group_id":22},{"job_id":11713,"job_group_id":22},{"job_id":11714,"job_group_id":22},{"job_id":11715,"job_group_id":22},{"job_id":12198,"job_group_id":22},{"job_id":12217,"job_group_id":22},{"job_id":12100,"job_group_id":22},{"job_id":11330,"job_group_id":22},{"job_id":11824,"job_group_id":22},{"job_id":12145,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":11832,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":11330,"job_group_id":22},{"job_id":12145,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10255,"job_group_id":22},{"job_id":12145,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":11384,"job_group_id":22},{"job_id":11926,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10609,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":21,"job_group_id":22},{"job_id":11480,"job_group_id":22},{"job_id":11683,"job_group_id":22},{"job_id":21,"job_group_id":22},{"job_id":11479,"job_group_id":22},{"job_id":11358,"job_group_id":22},{"job_id":11918,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11917,"job_group_id":22},{"job_id":21,"job_group_id":22},{"job_id":10197,"job_group_id":22},{"job_id":10891,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":10891,"job_group_id":22},{"job_id":11680,"job_group_id":22},{"job_id":11940,"job_group_id":22},{"job_id":11942,"job_group_id":22},{"job_id":12041,"job_group_id":22},{"job_id":12168,"job_group_id":22},{"job_id":12169,"job_group_id":22},{"job_id":11941,"job_group_id":22},{"job_id":11794,"job_group_id":22},{"job_id":12158,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":12110,"job_group_id":22},{"job_id":12149,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":12015,"job_group_id":22},{"job_id":12047,"job_group_id":22},{"job_id":12205,"job_group_id":22},{"job_id":12206,"job_group_id":22},{"job_id":12207,"job_group_id":22},{"job_id":12208,"job_group_id":22},{"job_id":12042,"job_group_id":22},{"job_id":12043,"job_group_id":22},{"job_id":12044,"job_group_id":22},{"job_id":12159,"job_group_id":22},{"job_id":12197,"job_group_id":22},{"job_id":12038,"job_group_id":22},{"job_id":12039,"job_group_id":22},{"job_id":12040,"job_group_id":22},{"job_id":10609,"job_group_id":22},{"job_id":12032,"job_group_id":22},{"job_id":12033,"job_group_id":22},{"job_id":12031,"job_group_id":22},{"job_id":12034,"job_group_id":22},{"job_id":12035,"job_group_id":22},{"job_id":12036,"job_group_id":22},{"job_id":12053,"job_group_id":22},{"job_id":12055,"job_group_id":22},{"job_id":12056,"job_group_id":22},{"job_id":12076,"job_group_id":22},{"job_id":12077,"job_group_id":22},{"job_id":12157,"job_group_id":22},{"job_id":11794,"job_group_id":22},{"job_id":12070,"job_group_id":22},{"job_id":12079,"job_group_id":22},{"job_id":12126,"job_group_id":22},{"job_id":12128,"job_group_id":22},{"job_id":12158,"job_group_id":22},{"job_id":12084,"job_group_id":22},{"job_id":12085,"job_group_id":22},{"job_id":11683,"job_group_id":22},{"job_id":12112,"job_group_id":22},{"job_id":10325,"job_group_id":22},{"job_id":11720,"job_group_id":22},{"job_id":12088,"job_group_id":22},{"job_id":10737,"job_group_id":22},{"job_id":10738,"job_group_id":22},{"job_id":11497,"job_group_id":22},{"job_id":10794,"job_group_id":22},{"job_id":12150,"job_group_id":22},{"job_id":12057,"job_group_id":22},{"job_id":12058,"job_group_id":22},{"job_id":12059,"job_group_id":22},{"job_id":12210,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10175,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":11843,"job_group_id":22},{"job_id":12020,"job_group_id":22},{"job_id":12162,"job_group_id":22},{"job_id":12236,"job_group_id":22},{"job_id":12237,"job_group_id":22},{"job_id":10456,"job_group_id":22},{"job_id":10573,"job_group_id":22},{"job_id":11802,"job_group_id":22},{"job_id":11843,"job_group_id":22},{"job_id":12104,"job_group_id":22},{"job_id":11863,"job_group_id":22},{"job_id":10656,"job_group_id":22},{"job_id":12018,"job_group_id":22},{"job_id":12074,"job_group_id":22},{"job_id":11448,"job_group_id":22},{"job_id":10338,"job_group_id":22},{"job_id":10641,"job_group_id":22},{"job_id":11553,"job_group_id":22},{"job_id":12045,"job_group_id":22},{"job_id":11360,"job_group_id":22},{"job_id":10638,"job_group_id":22},{"job_id":10637,"job_group_id":22},{"job_id":10186,"job_group_id":22},{"job_id":11389,"job_group_id":22},{"job_id":11453,"job_group_id":22},{"job_id":21,"job_group_id":22},{"job_id":10911,"job_group_id":22},{"job_id":10112,"job_group_id":22},{"job_id":10197,"job_group_id":22},{"job_id":11480,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":10640,"job_group_id":22},{"job_id":10382,"job_group_id":22},{"job_id":10324,"job_group_id":22},{"job_id":10251,"job_group_id":22},{"job_id":11018,"job_group_id":22},{"job_id":11485,"job_group_id":22},{"job_id":21,"job_group_id":22},{"job_id":10911,"job_group_id":22},{"job_id":10112,"job_group_id":22},{"job_id":10197,"job_group_id":22},{"job_id":12041,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":10512,"job_group_id":22},{"job_id":10660,"job_group_id":22},{"job_id":10140,"job_group_id":22},{"job_id":10132,"job_group_id":22},{"job_id":10083,"job_group_id":22},{"job_id":10084,"job_group_id":22},{"job_id":10574,"job_group_id":22},{"job_id":12136,"job_group_id":22},{"job_id":12156,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":10910,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":10910,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10686,"job_group_id":22},{"job_id":11270,"job_group_id":22},{"job_id":11680,"job_group_id":22},{"job_id":11679,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":10910,"job_group_id":22},{"job_id":10686,"job_group_id":22},{"job_id":11270,"job_group_id":22},{"job_id":11680,"job_group_id":22},{"job_id":11679,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":10014,"job_group_id":22},{"job_id":10013,"job_group_id":22},{"job_id":10312,"job_group_id":22},{"job_id":10736,"job_group_id":22},{"job_id":10735,"job_group_id":22},{"job_id":10736,"job_group_id":22},{"job_id":12130,"job_group_id":22},{"job_id":12131,"job_group_id":22},{"job_id":12192,"job_group_id":22},{"job_id":12195,"job_group_id":22},{"job_id":10910,"job_group_id":22},{"job_id":11902,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10339,"job_group_id":22},{"job_id":10017,"job_group_id":22},{"job_id":11806,"job_group_id":22},{"job_id":10325,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10310,"job_group_id":22},{"job_id":10563,"job_group_id":22},{"job_id":11807,"job_group_id":22},{"job_id":12064,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":12171,"job_group_id":22},{"job_id":12179,"job_group_id":22},{"job_id":12230,"job_group_id":22},{"job_id":12231,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10919,"job_group_id":22},{"job_id":11525,"job_group_id":22},{"job_id":10210,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10467,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11561,"job_group_id":22},{"job_id":11562,"job_group_id":22},{"job_id":11563,"job_group_id":22},{"job_id":11894,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":10402,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":11473,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10470,"job_group_id":22},{"job_id":10469,"job_group_id":22},{"job_id":10910,"job_group_id":22},{"job_id":11383,"job_group_id":22},{"job_id":10910,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10183,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10266,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":12221,"job_group_id":22},{"job_id":12222,"job_group_id":22},{"job_id":12224,"job_group_id":22},{"job_id":12218,"job_group_id":22},{"job_id":12219,"job_group_id":22},{"job_id":12220,"job_group_id":22},{"job_id":12153,"job_group_id":22},{"job_id":10614,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":11897,"job_group_id":22},{"job_id":11958,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":10367,"job_group_id":22},{"job_id":10064,"job_group_id":22},{"job_id":11390,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":10247,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":12108,"job_group_id":22},{"job_id":12232,"job_group_id":22},{"job_id":12233,"job_group_id":22},{"job_id":12234,"job_group_id":22},{"job_id":11494,"job_group_id":22},{"job_id":10872,"job_group_id":22},{"job_id":10902,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11930,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11749,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":10686,"job_group_id":22},{"job_id":11270,"job_group_id":22},{"job_id":11680,"job_group_id":22},{"job_id":11679,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":11273,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11526,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":12139,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11046,"job_group_id":22},{"job_id":11426,"job_group_id":22},{"job_id":10208,"job_group_id":12},{"job_id":10510,"job_group_id":12},{"job_id":11458,"job_group_id":12},{"job_id":12101,"job_group_id":12},{"job_id":10151,"job_group_id":12},{"job_id":11958,"job_group_id":12},{"job_id":10746,"job_group_id":12},{"job_id":11248,"job_group_id":12},{"job_id":10506,"job_group_id":12},{"job_id":10208,"job_group_id":12},{"job_id":10510,"job_group_id":12},{"job_id":10208,"job_group_id":12},{"job_id":10510,"job_group_id":12},{"job_id":11275,"job_group_id":12},{"job_id":10192,"job_group_id":12},{"job_id":11212,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":10244,"job_group_id":24},{"job_id":10165,"job_group_id":24},{"job_id":10137,"job_group_id":24},{"job_id":12193,"job_group_id":24},{"job_id":10769,"job_group_id":24},{"job_id":12067,"job_group_id":24},{"job_id":12214,"job_group_id":24},{"job_id":12215,"job_group_id":24},{"job_id":12216,"job_group_id":24},{"job_id":11645,"job_group_id":24},{"job_id":12167,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":11317,"job_group_id":24},{"job_id":10230,"job_group_id":24},{"job_id":12019,"job_group_id":24},{"job_id":11653,"job_group_id":24},{"job_id":11001,"job_group_id":24},{"job_id":11958,"job_group_id":24},{"job_id":12201,"job_group_id":24},{"job_id":11958,"job_group_id":24},{"job_id":12147,"job_group_id":24},{"job_id":12200,"job_group_id":24},{"job_id":10137,"job_group_id":24},{"job_id":12172,"job_group_id":24},{"job_id":11602,"job_group_id":24},{"job_id":11697,"job_group_id":24},{"job_id":11215,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":10244,"job_group_id":24},{"job_id":10165,"job_group_id":24},{"job_id":10137,"job_group_id":24},{"job_id":10521,"job_group_id":24},{"job_id":12193,"job_group_id":24},{"job_id":10769,"job_group_id":24},{"job_id":11001,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":11317,"job_group_id":24},{"job_id":10230,"job_group_id":24},{"job_id":10137,"job_group_id":24},{"job_id":12193,"job_group_id":24},{"job_id":10769,"job_group_id":24},{"job_id":11602,"job_group_id":24},{"job_id":11297,"job_group_id":24},{"job_id":11632,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":10244,"job_group_id":24},{"job_id":10165,"job_group_id":24},{"job_id":12193,"job_group_id":24},{"job_id":10769,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":10244,"job_group_id":24},{"job_id":10165,"job_group_id":24},{"job_id":10137,"job_group_id":24},{"job_id":12193,"job_group_id":24},{"job_id":10773,"job_group_id":24},{"job_id":12201,"job_group_id":24},{"job_id":10785,"job_group_id":24},{"job_id":11457,"job_group_id":24},{"job_id":12200,"job_group_id":24},{"job_id":11800,"job_group_id":24},{"job_id":10063,"job_group_id":24},{"job_id":10244,"job_group_id":24},{"job_id":10165,"job_group_id":24},{"job_id":10165,"job_group_id":24},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11312,"job_group_id":9},{"job_id":11312,"job_group_id":9},{"job_id":11312,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":10608,"job_group_id":9},{"job_id":11204,"job_group_id":9},{"job_id":11312,"job_group_id":9},{"job_id":12030,"job_group_id":9},{"job_id":12177,"job_group_id":9},{"job_id":12178,"job_group_id":9},{"job_id":12211,"job_group_id":9},{"job_id":11808,"job_group_id":9},{"job_id":11809,"job_group_id":9},{"job_id":12199,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10284,"job_group_id":9},{"job_id":10466,"job_group_id":9},{"job_id":11619,"job_group_id":9},{"job_id":11527,"job_group_id":9},{"job_id":11851,"job_group_id":9},{"job_id":11903,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":11814,"job_group_id":9},{"job_id":12166,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":11958,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11614,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":12132,"job_group_id":9},{"job_id":12133,"job_group_id":9},{"job_id":11615,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":12080,"job_group_id":4},{"job_id":12066,"job_group_id":4},{"job_id":11839,"job_group_id":4},{"job_id":11626,"job_group_id":4},{"job_id":11755,"job_group_id":4},{"job_id":11958,"job_group_id":4},{"job_id":12144,"job_group_id":4},{"job_id":11793,"job_group_id":4},{"job_id":11755,"job_group_id":4},{"job_id":11542,"job_group_id":4},{"job_id":10942,"job_group_id":4},{"job_id":10995,"job_group_id":4},{"job_id":12066,"job_group_id":4},{"job_id":11336,"job_group_id":8},{"job_id":12109,"job_group_id":8},{"job_id":12068,"job_group_id":8}]',
                    '[{"job_title":929,"job_id":11212},{"job_title":1887,"job_id":12139},{"job_title":568,"job_id":10465},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":745,"job_id":10615},{"job_title":1411,"job_id":11615},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1335,"job_id":11426},{"job_title":1887,"job_id":12139},{"job_title":568,"job_id":10465},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":1411,"job_id":11615},{"job_title":271,"job_id":11047},{"job_title":1335,"job_id":10247},{"job_title":1887,"job_id":12139},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1335,"job_id":10247},{"job_title":1887,"job_id":12139},{"job_title":724,"job_id":10686},{"job_title":725,"job_id":11270},{"job_title":1474,"job_id":11680},{"job_title":1475,"job_id":11679},{"job_title":578,"job_id":11749},{"job_title":1678,"job_id":11930},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":11614},{"job_title":1411,"job_id":11615},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1335,"job_id":11426},{"job_title":1605,"job_id":11852},{"job_title":1025,"job_id":11324},{"job_title":1154,"job_id":11323},{"job_title":1373,"job_id":11570},{"job_title":1374,"job_id":11571},{"job_title":1377,"job_id":11574},{"job_title":1378,"job_id":11575},{"job_title":1823,"job_id":12073},{"job_title":1926,"job_id":12170},{"job_title":73,"job_id":10063},{"job_title":259,"job_id":10244},{"job_title":465,"job_id":10165},{"job_title":469,"job_id":10137},{"job_title":731,"job_id":12193},{"job_title":771,"job_id":10769},{"job_title":1620,"job_id":11868},{"job_title":1474,"job_id":11680},{"job_title":1475,"job_id":11679},{"job_title":620,"job_id":10408},{"job_title":1025,"job_id":11324},{"job_title":1154,"job_id":11323},{"job_title":1542,"job_id":11766},{"job_title":82,"job_id":12080},{"job_title":83,"job_id":12066},{"job_title":1017,"job_id":11839},{"job_title":1423,"job_id":11626},{"job_title":1572,"job_id":11816},{"job_title":75,"job_id":11550},{"job_title":1126,"job_id":11549},{"job_title":1342,"job_id":11548},{"job_title":1771,"job_id":12026},{"job_title":1863,"job_id":12113},{"job_title":260,"job_id":10249},{"job_title":478,"job_id":11219},{"job_title":1157,"job_id":11327},{"job_title":1355,"job_id":11547},{"job_title":1932,"job_id":12176},{"job_title":491,"job_id":10647},{"job_title":800,"job_id":10893},{"job_title":1952,"job_id":12196},{"job_title":1499,"job_id":11711},{"job_title":1500,"job_id":11712},{"job_title":1501,"job_id":11713},{"job_title":1502,"job_id":11714},{"job_title":1503,"job_id":11715},{"job_title":1954,"job_id":12198},{"job_title":294,"job_id":12217},{"job_title":1816,"job_id":12067},{"job_title":1968,"job_id":12214},{"job_title":1969,"job_id":12215},{"job_title":1970,"job_id":12216},{"job_title":1848,"job_id":12100},{"job_title":836,"job_id":11330},{"job_title":1580,"job_id":11824},{"job_title":1901,"job_id":12145},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":344,"job_id":10445},{"job_title":1148,"job_id":11312},{"job_title":1588,"job_id":11832},{"job_title":158,"job_id":10175},{"job_title":836,"job_id":11330},{"job_title":1901,"job_id":12145},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":918,"job_id":10615},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":344,"job_id":10445},{"job_title":1148,"job_id":11312},{"job_title":916,"job_id":10255},{"job_title":1901,"job_id":12145},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":918,"job_id":10615},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":1211,"job_id":11384},{"job_title":1674,"job_id":11926},{"job_title":158,"job_id":10175},{"job_title":344,"job_id":10256},{"job_title":913,"job_id":10609},{"job_title":962,"job_id":11013},{"job_title":1148,"job_id":11312},{"job_title":6,"job_id":10224},{"job_title":578,"job_id":11749},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":1599,"job_id":11846},{"job_title":1732,"job_id":11979},{"job_title":1741,"job_id":11995},{"job_title":13,"job_id":10916},{"job_title":16,"job_id":21},{"job_title":37,"job_id":7},{"job_title":110,"job_id":10003},{"job_title":1015,"job_id":10086},{"job_title":1289,"job_id":11481},{"job_title":1290,"job_id":11480},{"job_title":354,"job_id":11683},{"job_title":20,"job_id":11856},{"job_title":1183,"job_id":11350},{"job_title":1662,"job_id":11913},{"job_title":13,"job_id":10916},{"job_title":16,"job_id":21},{"job_title":37,"job_id":7},{"job_title":110,"job_id":10003},{"job_title":1015,"job_id":10086},{"job_title":1291,"job_id":11479},{"job_title":733,"job_id":11958},{"job_title":843,"job_id":11416},{"job_title":962,"job_id":11013},{"job_title":1176,"job_id":11358},{"job_title":343,"job_id":10318},{"job_title":1667,"job_id":11918},{"job_title":287,"job_id":10208},{"job_title":288,"job_id":10510},{"job_title":1269,"job_id":11458},{"job_title":1851,"job_id":12101},{"job_title":1904,"job_id":12148},{"job_title":578,"job_id":11749},{"job_title":568,"job_id":10465},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":10899},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":10899},{"job_title":1411,"job_id":11615},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":10899},{"job_title":1335,"job_id":11426},{"job_title":1355,"job_id":11547},{"job_title":1850,"job_id":11917},{"job_title":220,"job_id":10480},{"job_title":16,"job_id":21},{"job_title":37,"job_id":7},{"job_title":110,"job_id":10003},{"job_title":111,"job_id":11680},{"job_title":451,"job_id":10197},{"job_title":576,"job_id":10891},{"job_title":577,"job_id":11273},{"job_title":808,"job_id":10891},{"job_title":1015,"job_id":10086},{"job_title":1474,"job_id":11680},{"job_title":1687,"job_id":11940},{"job_title":1688,"job_id":11941},{"job_title":1689,"job_id":11942},{"job_title":1786,"job_id":12041},{"job_title":1844,"job_id":12096},{"job_title":1845,"job_id":12097},{"job_title":1924,"job_id":12168},{"job_title":1925,"job_id":12169},{"job_title":1931,"job_id":11941},{"job_title":1533,"job_id":11755},{"job_title":1560,"job_id":11794},{"job_title":1914,"job_id":12158},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":1698,"job_id":11958},{"job_title":1852,"job_id":12102},{"job_title":1916,"job_id":12160},{"job_title":1622,"job_id":11870},{"job_title":1623,"job_id":11871},{"job_title":272,"job_id":11046},{"job_title":1411,"job_id":11615},{"job_title":1748,"job_id":11958},{"job_title":79,"job_id":11958},{"job_title":1860,"job_id":12110},{"job_title":1905,"job_id":12149},{"job_title":1532,"job_id":11958},{"job_title":1533,"job_id":11958},{"job_title":1560,"job_id":11958},{"job_title":1747,"job_id":11958},{"job_title":1759,"job_id":11958},{"job_title":1762,"job_id":12015},{"job_title":1760,"job_id":11958},{"job_title":1763,"job_id":11958},{"job_title":1770,"job_id":11958},{"job_title":1793,"job_id":12047},{"job_title":1959,"job_id":12205},{"job_title":1960,"job_id":12206},{"job_title":1961,"job_id":12207},{"job_title":1962,"job_id":12208},{"job_title":1787,"job_id":12042},{"job_title":1788,"job_id":12043},{"job_title":1789,"job_id":12044},{"job_title":1915,"job_id":12159},{"job_title":1953,"job_id":12197},{"job_title":1783,"job_id":12038},{"job_title":1784,"job_id":12039},{"job_title":1785,"job_id":12040},{"job_title":912,"job_id":10608},{"job_title":913,"job_id":10609},{"job_title":1045,"job_id":11204},{"job_title":1148,"job_id":11312},{"job_title":1355,"job_id":12138},{"job_title":1775,"job_id":12030},{"job_title":1777,"job_id":12032},{"job_title":1778,"job_id":12033},{"job_title":1802,"job_id":12031},{"job_title":1779,"job_id":12034},{"job_title":1780,"job_id":12035},{"job_title":1781,"job_id":12036},{"job_title":1800,"job_id":12053},{"job_title":1803,"job_id":12055},{"job_title":1804,"job_id":12056},{"job_title":45,"job_id":11837},{"job_title":1837,"job_id":11836},{"job_title":1826,"job_id":12076},{"job_title":1827,"job_id":12077},{"job_title":1913,"job_id":12157},{"job_title":1349,"job_id":11541},{"job_title":1900,"job_id":12144},{"job_title":795,"job_id":10151},{"job_title":306,"job_id":11793},{"job_title":1533,"job_id":11755},{"job_title":1560,"job_id":11794},{"job_title":1820,"job_id":12070},{"job_title":1829,"job_id":12079},{"job_title":1876,"job_id":12126},{"job_title":1878,"job_id":12128},{"job_title":1914,"job_id":12158},{"job_title":1831,"job_id":12084},{"job_title":1832,"job_id":12085},{"job_title":1839,"job_id":11975},{"job_title":1840,"job_id":10313},{"job_title":354,"job_id":11683},{"job_title":1862,"job_id":12112},{"job_title":1910,"job_id":11958},{"job_title":1981,"job_id":12228},{"job_title":3,"job_id":10984},{"job_title":146,"job_id":10437},{"job_title":1355,"job_id":11547},{"job_title":1865,"job_id":12115},{"job_title":1866,"job_id":12116},{"job_title":1867,"job_id":12117},{"job_title":1868,"job_id":12118},{"job_title":1869,"job_id":12119},{"job_title":1870,"job_id":12120},{"job_title":1355,"job_id":11547},{"job_title":1871,"job_id":12121},{"job_title":1872,"job_id":12122},{"job_title":1873,"job_id":12123},{"job_title":1874,"job_id":12124},{"job_title":1875,"job_id":12125},{"job_title":1696,"job_id":11949},{"job_title":1720,"job_id":11969},{"job_title":1,"job_id":10232},{"job_title":3,"job_id":10984},{"job_title":193,"job_id":10036},{"job_title":1355,"job_id":11547},{"job_title":1380,"job_id":11578},{"job_title":1382,"job_id":11580},{"job_title":1386,"job_id":11587},{"job_title":1388,"job_id":11589},{"job_title":1611,"job_id":11858},{"job_title":1,"job_id":10232},{"job_title":797,"job_id":11781},{"job_title":1614,"job_id":11861},{"job_title":1615,"job_id":11862},{"job_title":1982,"job_id":12229},{"job_title":1184,"job_id":11392},{"job_title":527,"job_id":10325},{"job_title":1508,"job_id":11720},{"job_title":1835,"job_id":12088},{"job_title":748,"job_id":10737},{"job_title":749,"job_id":10738},{"job_title":1894,"job_id":12029},{"job_title":587,"job_id":11732},{"job_title":1306,"job_id":11497},{"job_title":769,"job_id":12106},{"job_title":1439,"job_id":11643},{"job_title":1441,"job_id":11645},{"job_title":927,"job_id":11241},{"job_title":1184,"job_id":11349},{"job_title":1421,"job_id":11624},{"job_title":1923,"job_id":12167},{"job_title":293,"job_id":10794},{"job_title":710,"job_id":11708},{"job_title":1355,"job_id":11547},{"job_title":73,"job_id":10063},{"job_title":149,"job_id":11317},{"job_title":462,"job_id":10230},{"job_title":469,"job_id":12019},{"job_title":771,"job_id":11653},{"job_title":946,"job_id":11001},{"job_title":1348,"job_id":11540},{"job_title":1350,"job_id":11542},{"job_title":465,"job_id":11958},{"job_title":471,"job_id":12201},{"job_title":731,"job_id":11958},{"job_title":1903,"job_id":12147},{"job_title":1956,"job_id":12200},{"job_title":27,"job_id":11958},{"job_title":552,"job_id":11958},{"job_title":553,"job_id":11958},{"job_title":1856,"job_id":12106},{"job_title":1906,"job_id":12150},{"job_title":1909,"job_id":12152},{"job_title":1805,"job_id":12057},{"job_title":1806,"job_id":12058},{"job_title":1807,"job_id":12059},{"job_title":1964,"job_id":12210},{"job_title":133,"job_id":11582},{"job_title":586,"job_id":10434},{"job_title":1391,"job_id":11592},{"job_title":96,"job_id":11595},{"job_title":194,"job_id":10364},{"job_title":586,"job_id":10434},{"job_title":1391,"job_id":11592},{"job_title":469,"job_id":10137},{"job_title":470,"job_id":10517},{"job_title":1149,"job_id":11313},{"job_title":1355,"job_id":11547},{"job_title":1928,"job_id":12172},{"job_title":1286,"job_id":11474},{"job_title":1554,"job_id":11779},{"job_title":1555,"job_id":11780},{"job_title":1602,"job_id":11849},{"job_title":1992,"job_id":12238},{"job_title":1636,"job_id":11884},{"job_title":1929,"job_id":12173},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":300,"job_id":1},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":300,"job_id":1},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":1301,"job_id":11492},{"job_title":158,"job_id":10175},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":918,"job_id":10615},{"job_title":1301,"job_id":11492},{"job_title":300,"job_id":1},{"job_title":1933,"job_id":12177},{"job_title":1934,"job_id":12178},{"job_title":1965,"job_id":12211},{"job_title":796,"job_id":10746},{"job_title":1023,"job_id":11248},{"job_title":886,"job_id":10942},{"job_title":950,"job_id":10995},{"job_title":733,"job_id":12153},{"job_title":1596,"job_id":11843},{"job_title":1766,"job_id":12020},{"job_title":1855,"job_id":12105},{"job_title":1885,"job_id":12137},{"job_title":1918,"job_id":12162},{"job_title":1977,"job_id":12224},{"job_title":1990,"job_id":12236},{"job_title":1991,"job_id":12237},{"job_title":774,"job_id":11642},{"job_title":927,"job_id":11241},{"job_title":1075,"job_id":11602},{"job_title":1184,"job_id":10749},{"job_title":893,"job_id":10959},{"job_title":894,"job_id":10960},{"job_title":1151,"job_id":11315},{"job_title":1714,"job_id":11958},{"job_title":1715,"job_id":11958},{"job_title":176,"job_id":10028},{"job_title":177,"job_id":11617},{"job_title":441,"job_id":10558},{"job_title":1774,"job_id":12029},{"job_title":453,"job_id":10330},{"job_title":215,"job_id":10427},{"job_title":467,"job_id":10429},{"job_title":1936,"job_id":10428},{"job_title":1937,"job_id":12180},{"job_title":1938,"job_id":12181},{"job_title":480,"job_id":10308},{"job_title":878,"job_id":10456},{"job_title":1341,"job_id":11532},{"job_title":34,"job_id":10389},{"job_title":1274,"job_id":11462},{"job_title":1818,"job_id":11762},{"job_title":1958,"job_id":12204},{"job_title":1988,"job_id":10573},{"job_title":68,"job_id":10506},{"job_title":287,"job_id":10208},{"job_title":288,"job_id":10510},{"job_title":1153,"job_id":11333},{"job_title":1618,"job_id":11866},{"job_title":1768,"job_id":12022},{"job_title":1847,"job_id":12099},{"job_title":1348,"job_id":11013},{"job_title":1349,"job_id":10445},{"job_title":287,"job_id":10208},{"job_title":288,"job_id":10510},{"job_title":619,"job_id":11275},{"job_title":647,"job_id":10192},{"job_title":1355,"job_id":11958},{"job_title":1014,"job_id":11802},{"job_title":1149,"job_id":11313},{"job_title":1712,"job_id":11958},{"job_title":260,"job_id":10249},{"job_title":995,"job_id":11073},{"job_title":1490,"job_id":11697},{"job_title":1007,"job_id":11787},{"job_title":1596,"job_id":11843},{"job_title":1885,"job_id":12137},{"job_title":1035,"job_id":11841},{"job_title":1109,"job_id":11272},{"job_title":1385,"job_id":11585},{"job_title":1825,"job_id":12075},{"job_title":1942,"job_id":12185},{"job_title":1943,"job_id":12186},{"job_title":1944,"job_id":12187},{"job_title":1946,"job_id":12190},{"job_title":1947,"job_id":12188},{"job_title":1950,"job_id":12194},{"job_title":1966,"job_id":12212},{"job_title":1967,"job_id":12213},{"job_title":4,"job_id":10118},{"job_title":334,"job_id":10204},{"job_title":617,"job_id":11433},{"job_title":1702,"job_id":11955},{"job_title":1854,"job_id":12104},{"job_title":1095,"job_id":11863},{"job_title":1103,"job_id":11893},{"job_title":1096,"job_id":10656},{"job_title":1104,"job_id":12018},{"job_title":1824,"job_id":12074},{"job_title":1050,"job_id":11213},{"job_title":1075,"job_id":11215},{"job_title":1907,"job_id":12151},{"job_title":240,"job_id":10188},{"job_title":1221,"job_id":11395},{"job_title":1354,"job_id":11546},{"job_title":1849,"job_id":11448},{"job_title":216,"job_id":10750},{"job_title":1795,"job_id":11958},{"job_title":58,"job_id":10338},{"job_title":548,"job_id":10641},{"job_title":1357,"job_id":11553},{"job_title":1790,"job_id":12045},{"job_title":1174,"job_id":11360},{"job_title":62,"job_id":10638},{"job_title":147,"job_id":10637},{"job_title":274,"job_id":10186},{"job_title":1216,"job_id":11389},{"job_title":1263,"job_id":11453},{"job_title":1608,"job_id":11855},{"job_title":13,"job_id":10916},{"job_title":16,"job_id":21},{"job_title":37,"job_id":7},{"job_title":79,"job_id":10911},{"job_title":110,"job_id":10003},{"job_title":111,"job_id":11680},{"job_title":269,"job_id":10112},{"job_title":451,"job_id":10197},{"job_title":452,"job_id":10543},{"job_title":1000,"job_id":11093},{"job_title":1015,"job_id":10086},{"job_title":1290,"job_id":11480},{"job_title":1355,"job_id":12138},{"job_title":1497,"job_id":11706},{"job_title":1663,"job_id":11914},{"job_title":1664,"job_id":11915},{"job_title":1786,"job_id":11958},{"job_title":1844,"job_id":12096},{"job_title":1845,"job_id":12097},{"job_title":1930,"job_id":12174},{"job_title":554,"job_id":10640},{"job_title":555,"job_id":10382},{"job_title":556,"job_id":10324},{"job_title":1264,"job_id":10251},{"job_title":486,"job_id":10333},{"job_title":1736,"job_id":11988},{"job_title":1879,"job_id":12129},{"job_title":1979,"job_id":12225},{"job_title":1980,"job_id":12226},{"job_title":1274,"job_id":11462},{"job_title":20,"job_id":11856},{"job_title":73,"job_id":10063},{"job_title":259,"job_id":10244},{"job_title":465,"job_id":10165},{"job_title":469,"job_id":10137},{"job_title":579,"job_id":10521},{"job_title":731,"job_id":12193},{"job_title":771,"job_id":10769},{"job_title":774,"job_id":10778},{"job_title":1184,"job_id":11349},{"job_title":946,"job_id":11001},{"job_title":1133,"job_id":11295},{"job_title":1149,"job_id":11313},{"job_title":1331,"job_id":11523},{"job_title":1957,"job_id":12203},{"job_title":73,"job_id":10063},{"job_title":149,"job_id":11317},{"job_title":462,"job_id":10230},{"job_title":469,"job_id":10137},{"job_title":731,"job_id":12193},{"job_title":771,"job_id":10769},{"job_title":1075,"job_id":11602},{"job_title":1137,"job_id":11297},{"job_title":1428,"job_id":11632},{"job_title":73,"job_id":10063},{"job_title":259,"job_id":10244},{"job_title":465,"job_id":10165},{"job_title":731,"job_id":12193},{"job_title":771,"job_id":10769},{"job_title":73,"job_id":10063},{"job_title":259,"job_id":10244},{"job_title":465,"job_id":10165},{"job_title":469,"job_id":10137},{"job_title":731,"job_id":12193},{"job_title":799,"job_id":11018},{"job_title":1571,"job_id":11815},{"job_title":38,"job_id":10091},{"job_title":1060,"job_id":11228},{"job_title":1654,"job_id":11904},{"job_title":1707,"job_id":12135},{"job_title":1940,"job_id":12183},{"job_title":927,"job_id":10749},{"job_title":1160,"job_id":11336},{"job_title":774,"job_id":10778},{"job_title":776,"job_id":10783},{"job_title":1644,"job_id":11958},{"job_title":1859,"job_id":12109},{"job_title":1817,"job_id":12068},{"job_title":1245,"job_id":11430},{"job_title":1048,"job_id":10773},{"job_title":52,"job_id":10123},{"job_title":608,"job_id":10492},{"job_title":1693,"job_id":11946},{"job_title":890,"job_id":11968},{"job_title":255,"job_id":11728},{"job_title":607,"job_id":10173},{"job_title":1355,"job_id":11547},{"job_title":1846,"job_id":12098},{"job_title":1895,"job_id":12140},{"job_title":1896,"job_id":12141},{"job_title":1939,"job_id":12182},{"job_title":1941,"job_id":12184},{"job_title":1250,"job_id":11435},{"job_title":1294,"job_id":11485},{"job_title":1296,"job_id":11487},{"job_title":1459,"job_id":11485},{"job_title":610,"job_id":10493},{"job_title":611,"job_id":11790},{"job_title":613,"job_id":11791},{"job_title":644,"job_id":10394},{"job_title":444,"job_id":10623},{"job_title":13,"job_id":10916},{"job_title":16,"job_id":21},{"job_title":37,"job_id":7},{"job_title":79,"job_id":10911},{"job_title":110,"job_id":10003},{"job_title":111,"job_id":11680},{"job_title":269,"job_id":10112},{"job_title":451,"job_id":10197},{"job_title":452,"job_id":10543},{"job_title":1000,"job_id":11093},{"job_title":1015,"job_id":10086},{"job_title":1355,"job_id":12138},{"job_title":1497,"job_id":11706},{"job_title":1663,"job_id":11914},{"job_title":1664,"job_id":11915},{"job_title":1786,"job_id":12041},{"job_title":1792,"job_id":11958},{"job_title":1844,"job_id":12096},{"job_title":1845,"job_id":12097},{"job_title":1930,"job_id":12174},{"job_title":1608,"job_id":11855},{"job_title":1608,"job_id":11855},{"job_title":240,"job_id":10188},{"job_title":335,"job_id":10187},{"job_title":1221,"job_id":11395},{"job_title":1354,"job_id":11546},{"job_title":187,"job_id":10293},{"job_title":78,"job_id":10171},{"job_title":92,"job_id":10635},{"job_title":153,"job_id":10305},{"job_title":970,"job_id":11202},{"job_title":1142,"job_id":11304},{"job_title":1186,"job_id":11365},{"job_title":1242,"job_id":11428},{"job_title":1512,"job_id":11724},{"job_title":518,"job_id":11414},{"job_title":870,"job_id":10294},{"job_title":1355,"job_id":11547},{"job_title":1769,"job_id":12024},{"job_title":454,"job_id":10512},{"job_title":726,"job_id":10660},{"job_title":83,"job_id":12066},{"job_title":11,"job_id":10140},{"job_title":97,"job_id":10132},{"job_title":98,"job_id":10083},{"job_title":101,"job_id":10084},{"job_title":291,"job_id":10574},{"job_title":711,"job_id":11808},{"job_title":712,"job_id":11809},{"job_title":1516,"job_id":11739},{"job_title":1884,"job_id":12136},{"job_title":1912,"job_id":12156},{"job_title":1955,"job_id":12199},{"job_title":1278,"job_id":11466},{"job_title":1279,"job_id":11467},{"job_title":979,"job_id":12090},{"job_title":980,"job_id":12092},{"job_title":981,"job_id":12094},{"job_title":1841,"job_id":12091},{"job_title":1842,"job_id":12093},{"job_title":1843,"job_id":12095},{"job_title":1283,"job_id":11471},{"job_title":1284,"job_id":11470},{"job_title":1716,"job_id":11958},{"job_title":226,"job_id":11934},{"job_title":1680,"job_id":11932},{"job_title":1681,"job_id":11933},{"job_title":589,"job_id":11455},{"job_title":590,"job_id":11454},{"job_title":1355,"job_id":11547},{"job_title":1517,"job_id":11734},{"job_title":1603,"job_id":11850},{"job_title":1853,"job_id":12103},{"job_title":26,"job_id":11583},{"job_title":1020,"job_id":10291},{"job_title":1355,"job_id":11547},{"job_title":1393,"job_id":11594},{"job_title":478,"job_id":11219},{"job_title":1157,"job_id":11327},{"job_title":1978,"job_id":10224},{"job_title":1410,"job_id":11614},{"job_title":1413,"job_id":10184},{"job_title":570,"job_id":10183},{"job_title":574,"job_id":10144},{"job_title":576,"job_id":10266},{"job_title":573,"job_id":10910},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1676,"job_id":11928},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":578,"job_id":11749},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1410,"job_id":10184},{"job_title":1678,"job_id":11930},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":1410,"job_id":11614},{"job_title":1413,"job_id":10184},{"job_title":570,"job_id":10183},{"job_title":574,"job_id":10144},{"job_title":576,"job_id":10266},{"job_title":573,"job_id":10910},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1412,"job_id":11616},{"job_title":1676,"job_id":11928},{"job_title":1678,"job_id":11930},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1678,"job_id":11930},{"job_title":578,"job_id":11749},{"job_title":578,"job_id":11749},{"job_title":724,"job_id":10686},{"job_title":725,"job_id":11270},{"job_title":1474,"job_id":11680},{"job_title":1475,"job_id":11679},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1412,"job_id":11616},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":11614},{"job_title":1413,"job_id":10184},{"job_title":570,"job_id":10183},{"job_title":574,"job_id":10144},{"job_title":576,"job_id":10266},{"job_title":573,"job_id":10910},{"job_title":724,"job_id":10686},{"job_title":725,"job_id":11270},{"job_title":1474,"job_id":11680},{"job_title":1475,"job_id":11679},{"job_title":1676,"job_id":11928},{"job_title":50,"job_id":10902},{"job_title":566,"job_id":10183},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1412,"job_id":11616},{"job_title":1676,"job_id":11928},{"job_title":1678,"job_id":11930},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1412,"job_id":11616},{"job_title":1410,"job_id":11614},{"job_title":1413,"job_id":10184},{"job_title":570,"job_id":10183},{"job_title":574,"job_id":10144},{"job_title":1676,"job_id":11928},{"job_title":1887,"job_id":12139},{"job_title":387,"job_id":10014},{"job_title":394,"job_id":10013},{"job_title":524,"job_id":10312},{"job_title":655,"job_id":10736},{"job_title":746,"job_id":10735},{"job_title":747,"job_id":10736},{"job_title":1880,"job_id":12130},{"job_title":1881,"job_id":12131},{"job_title":1949,"job_id":12192},{"job_title":1951,"job_id":12195},{"job_title":573,"job_id":10910},{"job_title":1008,"job_id":11902},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1678,"job_id":11930},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":11426},{"job_title":388,"job_id":10339},{"job_title":389,"job_id":10017},{"job_title":519,"job_id":11806},{"job_title":527,"job_id":10325},{"job_title":568,"job_id":10465},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":132,"job_id":10310},{"job_title":520,"job_id":10546},{"job_title":521,"job_id":10362},{"job_title":654,"job_id":10563},{"job_title":750,"job_id":11807},{"job_title":1749,"job_id":10362},{"job_title":1750,"job_id":10303},{"job_title":1812,"job_id":12064},{"job_title":1911,"job_id":11958},{"job_title":1927,"job_id":12171},{"job_title":1935,"job_id":12179},{"job_title":1983,"job_id":12230},{"job_title":1984,"job_id":12231},{"job_title":568,"job_id":10465},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":1410,"job_id":10184},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":568,"job_id":10465},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":11426},{"job_title":564,"job_id":10284},{"job_title":1070,"job_id":10466},{"job_title":1071,"job_id":11619},{"job_title":1336,"job_id":11527},{"job_title":1604,"job_id":11851},{"job_title":1653,"job_id":11903},{"job_title":568,"job_id":10465},{"job_title":576,"job_id":10266},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":661,"job_id":11740},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1335,"job_id":11426},{"job_title":560,"job_id":10919},{"job_title":565,"job_id":11525},{"job_title":571,"job_id":10210},{"job_title":1334,"job_id":10283},{"job_title":568,"job_id":10465},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":11614},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":11426},{"job_title":563,"job_id":10222},{"job_title":568,"job_id":10465},{"job_title":574,"job_id":10144},{"job_title":1676,"job_id":10144},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":576,"job_id":10266},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":661,"job_id":11740},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":11426},{"job_title":561,"job_id":10467},{"job_title":566,"job_id":11526},{"job_title":570,"job_id":10183},{"job_title":1335,"job_id":11426},{"job_title":1364,"job_id":11561},{"job_title":1365,"job_id":11562},{"job_title":1366,"job_id":11563},{"job_title":1645,"job_id":11894},{"job_title":568,"job_id":10465},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":575,"job_id":10402},{"job_title":576,"job_id":10266},{"job_title":577,"job_id":11273},{"job_title":1285,"job_id":11473},{"job_title":1355,"job_id":11958},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":562,"job_id":10470},{"job_title":567,"job_id":10469},{"job_title":573,"job_id":10910},{"job_title":1210,"job_id":11383},{"job_title":1677,"job_id":10910},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":10247},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11526},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":568,"job_id":10465},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":11614},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":10183},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":11426},{"job_title":568,"job_id":10465},{"job_title":576,"job_id":10266},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":661,"job_id":11740},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":10247},{"job_title":568,"job_id":10465},{"job_title":1957,"job_id":12203},{"job_title":1963,"job_id":12209},{"job_title":471,"job_id":12201},{"job_title":776,"job_id":10783},{"job_title":777,"job_id":10785},{"job_title":1268,"job_id":11457},{"job_title":1956,"job_id":12200},{"job_title":666,"job_id":11958},{"job_title":667,"job_id":11958},{"job_title":781,"job_id":11958},{"job_title":1570,"job_id":11814},{"job_title":1922,"job_id":12166},{"job_title":261,"job_id":10045},{"job_title":1974,"job_id":12221},{"job_title":1975,"job_id":12222},{"job_title":1977,"job_id":12224},{"job_title":1971,"job_id":12218},{"job_title":1972,"job_id":12219},{"job_title":1973,"job_id":12220},{"job_title":1698,"job_id":11958},{"job_title":1698,"job_id":11958},{"job_title":1698,"job_id":11958},{"job_title":1073,"job_id":11237},{"job_title":731,"job_id":11800},{"job_title":733,"job_id":12153},{"job_title":921,"job_id":10986},{"job_title":300,"job_id":1},{"job_title":917,"job_id":10614},{"job_title":1301,"job_id":11492},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":1335,"job_id":11426},{"job_title":1676,"job_id":11928},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":10247},{"job_title":568,"job_id":10465},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":11614},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":10247},{"job_title":1647,"job_id":11958},{"job_title":1648,"job_id":11897},{"job_title":1761,"job_id":11958},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":11614},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":10247},{"job_title":65,"job_id":10367},{"job_title":79,"job_id":10064},{"job_title":775,"job_id":10743},{"job_title":1217,"job_id":11390},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":10899},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":10247},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":1129,"job_id":11958},{"job_title":1130,"job_id":11958},{"job_title":1858,"job_id":12108},{"job_title":1985,"job_id":12232},{"job_title":1986,"job_id":12233},{"job_title":1987,"job_id":12234},{"job_title":73,"job_id":10063},{"job_title":259,"job_id":10244},{"job_title":465,"job_id":10165},{"job_title":731,"job_id":10165},{"job_title":1184,"job_id":10749},{"job_title":1302,"job_id":11494},{"job_title":473,"job_id":10260},{"job_title":675,"job_id":10872},{"job_title":753,"job_id":10272},{"job_title":33,"job_id":10426},{"job_title":34,"job_id":10389},{"job_title":344,"job_id":10445},{"job_title":940,"job_id":10161},{"job_title":50,"job_id":10902},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":578,"job_id":11749},{"job_title":1410,"job_id":10184},{"job_title":578,"job_id":11749},{"job_title":941,"job_id":10423},{"job_title":942,"job_id":10422},{"job_title":943,"job_id":10256},{"job_title":578,"job_id":11749},{"job_title":1355,"job_id":11958},{"job_title":1678,"job_id":11930},{"job_title":578,"job_id":11749},{"job_title":578,"job_id":11749},{"job_title":1412,"job_id":11616},{"job_title":873,"job_id":11967},{"job_title":1227,"job_id":10632},{"job_title":1722,"job_id":11833},{"job_title":578,"job_id":11749},{"job_title":1412,"job_id":11616},{"job_title":1676,"job_id":11928},{"job_title":1410,"job_id":11614},{"job_title":1411,"job_id":11615},{"job_title":1335,"job_id":11426},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":1713,"job_id":11958},{"job_title":1921,"job_id":12165},{"job_title":1882,"job_id":12132},{"job_title":1883,"job_id":12133},{"job_title":724,"job_id":10686},{"job_title":725,"job_id":11270},{"job_title":1474,"job_id":11680},{"job_title":1475,"job_id":11679},{"job_title":568,"job_id":10465},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1411,"job_id":11615},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":1335,"job_id":11426},{"job_title":577,"job_id":11273},{"job_title":1887,"job_id":12139},{"job_title":568,"job_id":10465},{"job_title":1676,"job_id":11928},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":1412,"job_id":11616},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":1410,"job_id":10184},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":566,"job_id":11526},{"job_title":1335,"job_id":11426},{"job_title":1887,"job_id":12139},{"job_title":1676,"job_id":11928},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":572,"job_id":11613},{"job_title":220,"job_id":10480},{"job_title":271,"job_id":11047},{"job_title":572,"job_id":11613},{"job_title":271,"job_id":11047},{"job_title":272,"job_id":11046},{"job_title":1335,"job_id":11426},{"job_title":23,"job_id":11768}]',
                ];
                break;
            case "ph":
                $data = [
                    '[{"job_title":1082,"group_id":11},{"job_title":82,"group_id":7},{"job_title":83,"group_id":7},{"job_title":1081,"group_id":7},{"job_title":1538,"group_id":7},{"job_title":306,"group_id":7},{"job_title":1544,"group_id":7},{"job_title":1545,"group_id":7},{"job_title":1743,"group_id":7},{"job_title":1750,"group_id":7},{"job_title":1535,"group_id":7},{"job_title":1536,"group_id":7},{"job_title":1537,"group_id":7},{"job_title":1674,"group_id":7},{"job_title":1747,"group_id":7},{"job_title":1786,"group_id":7},{"job_title":780,"group_id":12},{"job_title":1509,"group_id":12},{"job_title":400,"group_id":12},{"job_title":912,"group_id":12},{"job_title":913,"group_id":12},{"job_title":666,"group_id":12},{"job_title":781,"group_id":12},{"job_title":76,"group_id":12},{"job_title":1197,"group_id":12},{"job_title":1198,"group_id":12},{"job_title":1474,"group_id":12},{"job_title":1485,"group_id":12},{"job_title":658,"group_id":12},{"job_title":659,"group_id":12},{"job_title":660,"group_id":12},{"job_title":1374,"group_id":12},{"job_title":667,"group_id":12},{"job_title":494,"group_id":12},{"job_title":496,"group_id":12},{"job_title":1690,"group_id":12},{"job_title":1761,"group_id":12},{"job_title":77,"group_id":12},{"job_title":1586,"group_id":12},{"job_title":1587,"group_id":12},{"job_title":1050,"group_id":9},{"job_title":1051,"group_id":9},{"job_title":1053,"group_id":9},{"job_title":1077,"group_id":9},{"job_title":1078,"group_id":9},{"job_title":73,"group_id":9},{"job_title":1079,"group_id":9},{"job_title":149,"group_id":9},{"job_title":772,"group_id":9},{"job_title":1069,"group_id":9},{"job_title":1529,"group_id":9},{"job_title":1785,"group_id":9},{"job_title":68,"group_id":14},{"job_title":287,"group_id":14},{"job_title":288,"group_id":14},{"job_title":619,"group_id":14},{"job_title":647,"group_id":14},{"job_title":1630,"group_id":14},{"job_title":1692,"group_id":14},{"job_title":1784,"group_id":14},{"job_title":1026,"group_id":14},{"job_title":1027,"group_id":14},{"job_title":1028,"group_id":14},{"job_title":1087,"group_id":14},{"job_title":1193,"group_id":14},{"job_title":1208,"group_id":14},{"job_title":1470,"group_id":14},{"job_title":1763,"group_id":14},{"job_title":847,"group_id":14},{"job_title":1504,"group_id":14},{"job_title":1277,"group_id":6},{"job_title":557,"group_id":6},{"job_title":1284,"group_id":6},{"job_title":16,"group_id":6},{"job_title":79,"group_id":6},{"job_title":269,"group_id":6},{"job_title":492,"group_id":6},{"job_title":1276,"group_id":6},{"job_title":1619,"group_id":6},{"job_title":1689,"group_id":6},{"job_title":62,"group_id":6},{"job_title":147,"group_id":6},{"job_title":274,"group_id":6},{"job_title":1452,"group_id":6},{"job_title":1574,"group_id":6},{"job_title":548,"group_id":6},{"job_title":998,"group_id":6},{"job_title":999,"group_id":6},{"job_title":1488,"group_id":6},{"job_title":1688,"group_id":6},{"job_title":554,"group_id":6},{"job_title":555,"group_id":6},{"job_title":556,"group_id":6},{"job_title":1528,"group_id":6},{"job_title":1614,"group_id":6},{"job_title":1615,"group_id":6},{"job_title":1687,"group_id":6},{"job_title":1734,"group_id":6},{"job_title":1048,"group_id":6},{"job_title":1184,"group_id":6},{"job_title":916,"group_id":6},{"job_title":1188,"group_id":6},{"job_title":1472,"group_id":6},{"job_title":1136,"group_id":6},{"job_title":1189,"group_id":6},{"job_title":454,"group_id":6},{"job_title":726,"group_id":6},{"job_title":387,"group_id":6},{"job_title":394,"group_id":6},{"job_title":524,"group_id":6},{"job_title":1564,"group_id":6},{"job_title":1575,"group_id":6},{"job_title":526,"group_id":6},{"job_title":527,"group_id":6},{"job_title":1764,"group_id":6},{"job_title":1107,"group_id":6},{"job_title":207,"group_id":6},{"job_title":352,"group_id":6},{"job_title":354,"group_id":6},{"job_title":793,"group_id":6},{"job_title":794,"group_id":6},{"job_title":1320,"group_id":6},{"job_title":855,"group_id":6},{"job_title":1481,"group_id":6},{"job_title":560,"group_id":6},{"job_title":565,"group_id":6},{"job_title":571,"group_id":6},{"job_title":1322,"group_id":6},{"job_title":1492,"group_id":6},{"job_title":562,"group_id":6},{"job_title":567,"group_id":6},{"job_title":573,"group_id":6},{"job_title":1484,"group_id":6},{"job_title":1493,"group_id":6},{"job_title":802,"group_id":6},{"job_title":803,"group_id":6},{"job_title":804,"group_id":6},{"job_title":1491,"group_id":6},{"job_title":1494,"group_id":6},{"job_title":1723,"group_id":6},{"job_title":1724,"group_id":6},{"job_title":1725,"group_id":6},{"job_title":1726,"group_id":6},{"job_title":1727,"group_id":6},{"job_title":575,"group_id":6},{"job_title":576,"group_id":6},{"job_title":808,"group_id":6},{"job_title":1391,"group_id":6},{"job_title":1495,"group_id":6},{"job_title":561,"group_id":6},{"job_title":566,"group_id":6},{"job_title":570,"group_id":6},{"job_title":1323,"group_id":6},{"job_title":1496,"group_id":6},{"job_title":410,"group_id":6},{"job_title":1211,"group_id":6},{"job_title":1212,"group_id":6},{"job_title":1245,"group_id":6},{"job_title":1287,"group_id":6},{"job_title":50,"group_id":6},{"job_title":272,"group_id":6},{"job_title":1295,"group_id":6},{"job_title":1288,"group_id":6},{"job_title":1289,"group_id":6},{"job_title":1290,"group_id":6},{"job_title":1489,"group_id":6},{"job_title":1296,"group_id":6},{"job_title":1005,"group_id":6},{"job_title":1346,"group_id":6},{"job_title":1669,"group_id":6},{"job_title":1670,"group_id":6},{"job_title":1375,"group_id":6},{"job_title":1376,"group_id":6},{"job_title":1765,"group_id":6},{"job_title":1209,"group_id":6},{"job_title":1562,"group_id":6},{"job_title":1563,"group_id":6},{"job_title":1565,"group_id":6},{"job_title":917,"group_id":6},{"job_title":1447,"group_id":6},{"job_title":1463,"group_id":6},{"job_title":1464,"group_id":6},{"job_title":1465,"group_id":6},{"job_title":1466,"group_id":6},{"job_title":1467,"group_id":6},{"job_title":1490,"group_id":6},{"job_title":1487,"group_id":6},{"job_title":1645,"group_id":6},{"job_title":495,"group_id":6},{"job_title":158,"group_id":6},{"job_title":416,"group_id":6},{"job_title":320,"group_id":6},{"job_title":1588,"group_id":6},{"job_title":1589,"group_id":6},{"job_title":1590,"group_id":6},{"job_title":1591,"group_id":6},{"job_title":1611,"group_id":6},{"job_title":1759,"group_id":6},{"job_title":1612,"group_id":6},{"job_title":1613,"group_id":6},{"job_title":1641,"group_id":6},{"job_title":1583,"group_id":6},{"job_title":1623,"group_id":6},{"job_title":1626,"group_id":6},{"job_title":1627,"group_id":6},{"job_title":1628,"group_id":6},{"job_title":1646,"group_id":6},{"job_title":932,"group_id":6},{"job_title":65,"group_id":6},{"job_title":1656,"group_id":6},{"job_title":1657,"group_id":6},{"job_title":1658,"group_id":6},{"job_title":1685,"group_id":6},{"job_title":1686,"group_id":6},{"job_title":1345,"group_id":6},{"job_title":1716,"group_id":6},{"job_title":1717,"group_id":6},{"job_title":1718,"group_id":6},{"job_title":1719,"group_id":6},{"job_title":1720,"group_id":6},{"job_title":1693,"group_id":6},{"job_title":1694,"group_id":6},{"job_title":1695,"group_id":6},{"job_title":1696,"group_id":6},{"job_title":1697,"group_id":6},{"job_title":1760,"group_id":6},{"job_title":1703,"group_id":6},{"job_title":1704,"group_id":6},{"job_title":1705,"group_id":6},{"job_title":1706,"group_id":6},{"job_title":1707,"group_id":6},{"job_title":1708,"group_id":6},{"job_title":1709,"group_id":6},{"job_title":1710,"group_id":6},{"job_title":1711,"group_id":6},{"job_title":1712,"group_id":6},{"job_title":1698,"group_id":6},{"job_title":1699,"group_id":6},{"job_title":1700,"group_id":6},{"job_title":1701,"group_id":6},{"job_title":1702,"group_id":6},{"job_title":1722,"group_id":6},{"job_title":1655,"group_id":6},{"job_title":1767,"group_id":6},{"job_title":1735,"group_id":6},{"job_title":1736,"group_id":6},{"job_title":141,"group_id":6},{"job_title":996,"group_id":6},{"job_title":1777,"group_id":6},{"job_title":13,"group_id":5},{"job_title":37,"group_id":5},{"job_title":110,"group_id":5},{"job_title":452,"group_id":5},{"job_title":1000,"group_id":5},{"job_title":1194,"group_id":5},{"job_title":1553,"group_id":5},{"job_title":1652,"group_id":5},{"job_title":1675,"group_id":5},{"job_title":1192,"group_id":5},{"job_title":775,"group_id":5},{"job_title":474,"group_id":5},{"job_title":1526,"group_id":5},{"job_title":805,"group_id":5},{"job_title":806,"group_id":5},{"job_title":807,"group_id":5},{"job_title":1446,"group_id":5},{"job_title":1499,"group_id":5},{"job_title":753,"group_id":5},{"job_title":1297,"group_id":5},{"job_title":1412,"group_id":5},{"job_title":1413,"group_id":5},{"job_title":1414,"group_id":5},{"job_title":1416,"group_id":5},{"job_title":1417,"group_id":5},{"job_title":1418,"group_id":5},{"job_title":1770,"group_id":5},{"job_title":300,"group_id":5},{"job_title":1581,"group_id":5},{"job_title":1582,"group_id":5},{"job_title":1173,"group_id":5},{"job_title":1349,"group_id":5},{"job_title":1350,"group_id":5},{"job_title":1668,"group_id":5},{"job_title":1596,"group_id":5},{"job_title":1597,"group_id":5},{"job_title":733,"group_id":5},{"job_title":1778,"group_id":5},{"job_title":1782,"group_id":5},{"job_title":1046,"group_id":18},{"job_title":1633,"group_id":18},{"job_title":1730,"group_id":18},{"job_title":645,"group_id":18},{"job_title":1513,"group_id":18},{"job_title":1654,"group_id":18},{"job_title":1671,"group_id":18},{"job_title":530,"group_id":18},{"job_title":532,"group_id":18},{"job_title":1775,"group_id":18},{"job_title":1065,"group_id":18},{"job_title":1776,"group_id":18},{"job_title":1609,"group_id":18},{"job_title":1639,"group_id":18},{"job_title":1758,"group_id":18},{"job_title":1642,"group_id":18},{"job_title":27,"group_id":18},{"job_title":552,"group_id":18},{"job_title":1713,"group_id":18},{"job_title":1714,"group_id":18},{"job_title":1715,"group_id":18},{"job_title":240,"group_id":19},{"job_title":335,"group_id":19},{"job_title":1476,"group_id":19},{"job_title":1636,"group_id":19},{"job_title":1644,"group_id":19},{"job_title":90,"group_id":19},{"job_title":166,"group_id":19},{"job_title":246,"group_id":19},{"job_title":247,"group_id":19},{"job_title":1283,"group_id":19},{"job_title":78,"group_id":19},{"job_title":248,"group_id":19},{"job_title":978,"group_id":19},{"job_title":1139,"group_id":19},{"job_title":1479,"group_id":19},{"job_title":1660,"group_id":19},{"job_title":1444,"group_id":19},{"job_title":1445,"group_id":19},{"job_title":1618,"group_id":19},{"job_title":1682,"group_id":19},{"job_title":982,"group_id":19},{"job_title":983,"group_id":19},{"job_title":984,"group_id":19},{"job_title":1729,"group_id":19},{"job_title":1762,"group_id":19},{"job_title":969,"group_id":19},{"job_title":1333,"group_id":19},{"job_title":1336,"group_id":19},{"job_title":1681,"group_id":19},{"job_title":1757,"group_id":19},{"job_title":1768,"group_id":19},{"job_title":890,"group_id":19},{"job_title":1448,"group_id":19},{"job_title":1449,"group_id":19},{"job_title":1451,"group_id":19},{"job_title":1326,"group_id":19},{"job_title":1443,"group_id":19},{"job_title":1473,"group_id":19},{"job_title":1680,"group_id":19},{"job_title":1601,"group_id":19},{"job_title":1602,"group_id":19},{"job_title":478,"group_id":19},{"job_title":1183,"group_id":19},{"job_title":1673,"group_id":19},{"job_title":1043,"group_id":19},{"job_title":56,"group_id":21},{"job_title":145,"group_id":21},{"job_title":708,"group_id":21},{"job_title":1098,"group_id":21},{"job_title":1651,"group_id":21},{"job_title":1683,"group_id":21},{"job_title":1321,"group_id":21},{"job_title":1746,"group_id":21},{"job_title":1002,"group_id":21},{"job_title":1471,"group_id":21},{"job_title":1334,"group_id":21},{"job_title":1483,"group_id":21},{"job_title":1605,"group_id":21},{"job_title":1394,"group_id":20},{"job_title":1076,"group_id":20},{"job_title":528,"group_id":20},{"job_title":1514,"group_id":20},{"job_title":4,"group_id":20},{"job_title":115,"group_id":20},{"job_title":334,"group_id":20},{"job_title":1167,"group_id":20},{"job_title":1285,"group_id":20},{"job_title":1010,"group_id":20},{"job_title":620,"group_id":20},{"job_title":1267,"group_id":20},{"job_title":1269,"group_id":20},{"job_title":1274,"group_id":20},{"job_title":1275,"group_id":20},{"job_title":1456,"group_id":20},{"job_title":1684,"group_id":20},{"job_title":1731,"group_id":20},{"job_title":1733,"group_id":20},{"job_title":1108,"group_id":20},{"job_title":1106,"group_id":20},{"job_title":263,"group_id":20},{"job_title":1294,"group_id":20},{"job_title":1548,"group_id":20},{"job_title":1771,"group_id":20},{"job_title":1293,"group_id":20},{"job_title":1292,"group_id":20},{"job_title":1291,"group_id":20},{"job_title":1410,"group_id":20},{"job_title":1512,"group_id":20},{"job_title":617,"group_id":20},{"job_title":1395,"group_id":20},{"job_title":1603,"group_id":20},{"job_title":1025,"group_id":20},{"job_title":995,"group_id":20},{"job_title":1539,"group_id":20},{"job_title":1748,"group_id":20},{"job_title":1029,"group_id":20},{"job_title":1617,"group_id":20},{"job_title":1392,"group_id":20},{"job_title":1584,"group_id":20},{"job_title":1606,"group_id":20},{"job_title":253,"group_id":15},{"job_title":413,"group_id":15},{"job_title":414,"group_id":15},{"job_title":1486,"group_id":15},{"job_title":1516,"group_id":15},{"job_title":1521,"group_id":15},{"job_title":1524,"group_id":15},{"job_title":1721,"group_id":15},{"job_title":1561,"group_id":16},{"job_title":453,"group_id":16},{"job_title":1629,"group_id":16},{"job_title":1125,"group_id":16},{"job_title":1126,"group_id":16},{"job_title":1251,"group_id":16},{"job_title":1252,"group_id":16},{"job_title":1551,"group_id":16},{"job_title":1569,"group_id":16},{"job_title":1121,"group_id":16},{"job_title":1122,"group_id":16},{"job_title":1123,"group_id":16},{"job_title":1249,"group_id":16},{"job_title":1549,"group_id":16},{"job_title":1550,"group_id":16},{"job_title":1568,"group_id":16},{"job_title":633,"group_id":16},{"job_title":1130,"group_id":16},{"job_title":1131,"group_id":16},{"job_title":1260,"group_id":16},{"job_title":1570,"group_id":16},{"job_title":96,"group_id":16},{"job_title":194,"group_id":16},{"job_title":586,"group_id":16},{"job_title":883,"group_id":16},{"job_title":196,"group_id":16},{"job_title":197,"group_id":16},{"job_title":591,"group_id":16},{"job_title":1128,"group_id":16},{"job_title":1426,"group_id":16},{"job_title":3,"group_id":16},{"job_title":190,"group_id":16},{"job_title":797,"group_id":16},{"job_title":1437,"group_id":16},{"job_title":1438,"group_id":16},{"job_title":1439,"group_id":16},{"job_title":1440,"group_id":16},{"job_title":1441,"group_id":16},{"job_title":1442,"group_id":16},{"job_title":1019,"group_id":16},{"job_title":1423,"group_id":16},{"job_title":1424,"group_id":16},{"job_title":1425,"group_id":16},{"job_title":1427,"group_id":16},{"job_title":1428,"group_id":16},{"job_title":1429,"group_id":16},{"job_title":1430,"group_id":16},{"job_title":1074,"group_id":16},{"job_title":1772,"group_id":16},{"job_title":1007,"group_id":10},{"job_title":1165,"group_id":10},{"job_title":632,"group_id":10},{"job_title":1102,"group_id":10},{"job_title":1769,"group_id":10},{"job_title":23,"group_id":10},{"job_title":261,"group_id":17},{"job_title":899,"group_id":17},{"job_title":1036,"group_id":17},{"job_title":1468,"group_id":17},{"job_title":1744,"group_id":17},{"job_title":1745,"group_id":17},{"job_title":337,"group_id":17},{"job_title":399,"group_id":17},{"job_title":1156,"group_id":17},{"job_title":1592,"group_id":17},{"job_title":1752,"group_id":17},{"job_title":1751,"group_id":17},{"job_title":1753,"group_id":17},{"job_title":1754,"group_id":17},{"job_title":1755,"group_id":17},{"job_title":1756,"group_id":17},{"job_title":344,"group_id":13},{"job_title":962,"group_id":13},{"job_title":220,"group_id":13},{"job_title":34,"group_id":13},{"job_title":1469,"group_id":13},{"job_title":1546,"group_id":13},{"job_title":1547,"group_id":13},{"job_title":1635,"group_id":13},{"job_title":1555,"group_id":13},{"job_title":1678,"group_id":13},{"job_title":1556,"group_id":13},{"job_title":1557,"group_id":13},{"job_title":20,"group_id":8},{"job_title":774,"group_id":8},{"job_title":1779,"group_id":8},{"job_title":1780,"group_id":8},{"job_title":1781,"group_id":8}]',
                    '[{"job_id":11294,"job_group_id":11},{"job_id":11919,"job_group_id":7},{"job_id":11920,"job_group_id":7},{"job_id":11292,"job_group_id":7},{"job_id":11891,"job_group_id":7},{"job_id":11874,"job_group_id":7},{"job_id":11936,"job_group_id":7},{"job_id":11927,"job_group_id":7},{"job_id":11403,"job_group_id":7},{"job_id":12147,"job_group_id":7},{"job_id":11888,"job_group_id":7},{"job_id":11890,"job_group_id":7},{"job_id":11889,"job_group_id":7},{"job_id":12050,"job_group_id":7},{"job_id":11888,"job_group_id":7},{"job_id":12189,"job_group_id":7},{"job_id":10780,"job_group_id":12},{"job_id":11858,"job_group_id":12},{"job_id":11205,"job_group_id":12},{"job_id":11207,"job_group_id":12},{"job_id":11206,"job_group_id":12},{"job_id":11466,"job_group_id":12},{"job_id":10858,"job_group_id":12},{"job_id":11412,"job_group_id":12},{"job_id":11596,"job_group_id":12},{"job_id":11595,"job_group_id":12},{"job_id":11814,"job_group_id":12},{"job_id":11832,"job_group_id":12},{"job_id":11694,"job_group_id":12},{"job_id":11696,"job_group_id":12},{"job_id":11697,"job_group_id":12},{"job_id":11695,"job_group_id":12},{"job_id":11818,"job_group_id":12},{"job_id":11211,"job_group_id":12},{"job_id":11213,"job_group_id":12},{"job_id":12070,"job_group_id":12},{"job_id":12160,"job_group_id":12},{"job_id":11964,"job_group_id":12},{"job_id":11962,"job_group_id":12},{"job_id":11963,"job_group_id":12},{"job_id":12159,"job_group_id":9},{"job_id":11223,"job_group_id":9},{"job_id":11225,"job_group_id":9},{"job_id":11268,"job_group_id":9},{"job_id":11529,"job_group_id":9},{"job_id":11293,"job_group_id":9},{"job_id":11267,"job_group_id":9},{"job_id":11574,"job_group_id":9},{"job_id":11222,"job_group_id":9},{"job_id":11289,"job_group_id":9},{"job_id":11576,"job_group_id":9},{"job_id":11659,"job_group_id":9},{"job_id":11739,"job_group_id":14},{"job_id":11022,"job_group_id":14},{"job_id":11100,"job_group_id":14},{"job_id":11740,"job_group_id":14},{"job_id":11786,"job_group_id":14},{"job_id":12004,"job_group_id":14},{"job_id":12073,"job_group_id":14},{"job_id":12188,"job_group_id":14},{"job_id":11444,"job_group_id":14},{"job_id":11445,"job_group_id":14},{"job_id":11446,"job_group_id":14},{"job_id":11307,"job_group_id":14},{"job_id":11447,"job_group_id":14},{"job_id":11462,"job_group_id":14},{"job_id":11810,"job_group_id":14},{"job_id":12162,"job_group_id":14},{"job_id":11406,"job_group_id":14},{"job_id":11853,"job_group_id":14},{"job_id":11572,"job_group_id":6},{"job_id":11288,"job_group_id":6},{"job_id":11579,"job_group_id":6},{"job_id":10925,"job_group_id":6},{"job_id":10911,"job_group_id":6},{"job_id":10797,"job_group_id":6},{"job_id":11792,"job_group_id":6},{"job_id":11571,"job_group_id":6},{"job_id":10925,"job_group_id":6},{"job_id":12071,"job_group_id":6},{"job_id":10922,"job_group_id":6},{"job_id":10923,"job_group_id":6},{"job_id":10924,"job_group_id":6},{"job_id":11796,"job_group_id":6},{"job_id":11939,"job_group_id":6},{"job_id":10885,"job_group_id":6},{"job_id":11091,"job_group_id":6},{"job_id":11092,"job_group_id":6},{"job_id":11835,"job_group_id":6},{"job_id":12069,"job_group_id":6},{"job_id":10964,"job_group_id":6},{"job_id":10965,"job_group_id":6},{"job_id":10966,"job_group_id":6},{"job_id":11879,"job_group_id":6},{"job_id":11989,"job_group_id":6},{"job_id":11990,"job_group_id":6},{"job_id":12068,"job_group_id":6},{"job_id":12126,"job_group_id":6},{"job_id":11198,"job_group_id":6},{"job_id":11434,"job_group_id":6},{"job_id":11202,"job_group_id":6},{"job_id":11439,"job_group_id":6},{"job_id":11812,"job_group_id":6},{"job_id":11376,"job_group_id":6},{"job_id":11440,"job_group_id":6},{"job_id":12037,"job_group_id":6},{"job_id":12138,"job_group_id":6},{"job_id":10863,"job_group_id":6},{"job_id":10865,"job_group_id":6},{"job_id":10861,"job_group_id":6},{"job_id":11705,"job_group_id":6},{"job_id":11942,"job_group_id":6},{"job_id":10857,"job_group_id":6},{"job_id":10855,"job_group_id":6},{"job_id":12163,"job_group_id":6},{"job_id":11334,"job_group_id":6},{"job_id":11084,"job_group_id":6},{"job_id":10867,"job_group_id":6},{"job_id":10869,"job_group_id":6},{"job_id":11332,"job_group_id":6},{"job_id":11331,"job_group_id":6},{"job_id":11624,"job_group_id":6},{"job_id":11433,"job_group_id":6},{"job_id":11827,"job_group_id":6},{"job_id":10919,"job_group_id":6},{"job_id":10915,"job_group_id":6},{"job_id":10917,"job_group_id":6},{"job_id":11631,"job_group_id":6},{"job_id":11840,"job_group_id":6},{"job_id":10913,"job_group_id":6},{"job_id":10908,"job_group_id":6},{"job_id":10910,"job_group_id":6},{"job_id":11831,"job_group_id":6},{"job_id":11841,"job_group_id":6},{"job_id":10907,"job_group_id":6},{"job_id":10905,"job_group_id":6},{"job_id":10903,"job_group_id":6},{"job_id":11839,"job_group_id":6},{"job_id":11844,"job_group_id":6},{"job_id":12108,"job_group_id":6},{"job_id":12109,"job_group_id":6},{"job_id":12110,"job_group_id":6},{"job_id":12111,"job_group_id":6},{"job_id":12113,"job_group_id":6},{"job_id":10894,"job_group_id":6},{"job_id":10892,"job_group_id":6},{"job_id":10891,"job_group_id":6},{"job_id":11714,"job_group_id":6},{"job_id":11842,"job_group_id":6},{"job_id":11592,"job_group_id":6},{"job_id":11594,"job_group_id":6},{"job_id":11593,"job_group_id":6},{"job_id":11632,"job_group_id":6},{"job_id":11843,"job_group_id":6},{"job_id":11522,"job_group_id":6},{"job_id":11492,"job_group_id":6},{"job_id":11493,"job_group_id":6},{"job_id":11514,"job_group_id":6},{"job_id":11584,"job_group_id":6},{"job_id":10902,"job_group_id":6},{"job_id":10901,"job_group_id":6},{"job_id":11597,"job_group_id":6},{"job_id":11585,"job_group_id":6},{"job_id":11586,"job_group_id":6},{"job_id":11587,"job_group_id":6},{"job_id":11836,"job_group_id":6},{"job_id":11598,"job_group_id":6},{"job_id":11621,"job_group_id":6},{"job_id":11659,"job_group_id":6},{"job_id":11951,"job_group_id":6},{"job_id":12137,"job_group_id":6},{"job_id":11698,"job_group_id":6},{"job_id":11699,"job_group_id":6},{"job_id":12164,"job_group_id":6},{"job_id":12122,"job_group_id":6},{"job_id":11700,"job_group_id":6},{"job_id":11703,"job_group_id":6},{"job_id":11821,"job_group_id":6},{"job_id":11717,"job_group_id":6},{"job_id":11791,"job_group_id":6},{"job_id":11883,"job_group_id":6},{"job_id":11884,"job_group_id":6},{"job_id":11815,"job_group_id":6},{"job_id":12121,"job_group_id":6},{"job_id":11882,"job_group_id":6},{"job_id":11838,"job_group_id":6},{"job_id":11834,"job_group_id":6},{"job_id":12020,"job_group_id":6},{"job_id":11212,"job_group_id":6},{"job_id":11440,"job_group_id":6},{"job_id":11969,"job_group_id":6},{"job_id":11855,"job_group_id":6},{"job_id":11965,"job_group_id":6},{"job_id":11966,"job_group_id":6},{"job_id":11967,"job_group_id":6},{"job_id":11968,"job_group_id":6},{"job_id":11986,"job_group_id":6},{"job_id":12157,"job_group_id":6},{"job_id":11987,"job_group_id":6},{"job_id":11988,"job_group_id":6},{"job_id":12016,"job_group_id":6},{"job_id":11956,"job_group_id":6},{"job_id":12054,"job_group_id":6},{"job_id":12052,"job_group_id":6},{"job_id":11995,"job_group_id":6},{"job_id":11994,"job_group_id":6},{"job_id":12053,"job_group_id":6},{"job_id":12059,"job_group_id":6},{"job_id":12058,"job_group_id":6},{"job_id":12031,"job_group_id":6},{"job_id":12032,"job_group_id":6},{"job_id":12033,"job_group_id":6},{"job_id":12066,"job_group_id":6},{"job_id":12067,"job_group_id":6},{"job_id":11656,"job_group_id":6},{"job_id":12099,"job_group_id":6},{"job_id":12100,"job_group_id":6},{"job_id":12101,"job_group_id":6},{"job_id":12102,"job_group_id":6},{"job_id":12103,"job_group_id":6},{"job_id":12074,"job_group_id":6},{"job_id":12075,"job_group_id":6},{"job_id":12076,"job_group_id":6},{"job_id":12077,"job_group_id":6},{"job_id":12078,"job_group_id":6},{"job_id":12158,"job_group_id":6},{"job_id":12084,"job_group_id":6},{"job_id":12085,"job_group_id":6},{"job_id":12086,"job_group_id":6},{"job_id":12087,"job_group_id":6},{"job_id":12088,"job_group_id":6},{"job_id":12089,"job_group_id":6},{"job_id":12090,"job_group_id":6},{"job_id":12091,"job_group_id":6},{"job_id":12092,"job_group_id":6},{"job_id":12093,"job_group_id":6},{"job_id":12079,"job_group_id":6},{"job_id":12080,"job_group_id":6},{"job_id":12081,"job_group_id":6},{"job_id":12082,"job_group_id":6},{"job_id":12083,"job_group_id":6},{"job_id":12107,"job_group_id":6},{"job_id":12029,"job_group_id":6},{"job_id":12167,"job_group_id":6},{"job_id":12128,"job_group_id":6},{"job_id":12127,"job_group_id":6},{"job_id":11273,"job_group_id":6},{"job_id":12036,"job_group_id":6},{"job_id":12179,"job_group_id":6},{"job_id":10916,"job_group_id":5},{"job_id":10914,"job_group_id":5},{"job_id":10918,"job_group_id":5},{"job_id":10920,"job_group_id":5},{"job_id":11093,"job_group_id":5},{"job_id":11449,"job_group_id":5},{"job_id":11911,"job_group_id":5},{"job_id":12027,"job_group_id":5},{"job_id":12055,"job_group_id":5},{"job_id":11443,"job_group_id":5},{"job_id":11083,"job_group_id":5},{"job_id":11520,"job_group_id":5},{"job_id":11880,"job_group_id":5},{"job_id":10899,"job_group_id":5},{"job_id":10897,"job_group_id":5},{"job_id":10895,"job_group_id":5},{"job_id":11790,"job_group_id":5},{"job_id":11847,"job_group_id":5},{"job_id":11599,"job_group_id":5},{"job_id":11600,"job_group_id":5},{"job_id":11750,"job_group_id":5},{"job_id":11751,"job_group_id":5},{"job_id":11744,"job_group_id":5},{"job_id":11748,"job_group_id":5},{"job_id":11752,"job_group_id":5},{"job_id":11749,"job_group_id":5},{"job_id":12170,"job_group_id":5},{"job_id":11746,"job_group_id":5},{"job_id":11954,"job_group_id":5},{"job_id":11955,"job_group_id":5},{"job_id":11421,"job_group_id":5},{"job_id":11663,"job_group_id":5},{"job_id":11666,"job_group_id":5},{"job_id":12123,"job_group_id":5},{"job_id":11972,"job_group_id":5},{"job_id":11974,"job_group_id":5},{"job_id":12165,"job_group_id":5},{"job_id":12180,"job_group_id":5},{"job_id":12184,"job_group_id":5},{"job_id":11195,"job_group_id":18},{"job_id":12008,"job_group_id":18},{"job_id":11262,"job_group_id":18},{"job_id":11264,"job_group_id":18},{"job_id":11862,"job_group_id":18},{"job_id":12030,"job_group_id":18},{"job_id":12047,"job_group_id":18},{"job_id":12047,"job_group_id":18},{"job_id":11260,"job_group_id":18},{"job_id":12177,"job_group_id":18},{"job_id":11262,"job_group_id":18},{"job_id":12178,"job_group_id":18},{"job_id":11984,"job_group_id":18},{"job_id":12014,"job_group_id":18},{"job_id":12156,"job_group_id":18},{"job_id":12017,"job_group_id":18},{"job_id":12098,"job_group_id":18},{"job_id":12096,"job_group_id":18},{"job_id":12094,"job_group_id":18},{"job_id":12095,"job_group_id":18},{"job_id":12097,"job_group_id":18},{"job_id":11190,"job_group_id":19},{"job_id":11189,"job_group_id":19},{"job_id":11820,"job_group_id":19},{"job_id":12011,"job_group_id":19},{"job_id":12019,"job_group_id":19},{"job_id":11909,"job_group_id":19},{"job_id":11909,"job_group_id":19},{"job_id":11788,"job_group_id":19},{"job_id":11817,"job_group_id":19},{"job_id":11578,"job_group_id":19},{"job_id":11393,"job_group_id":19},{"job_id":11065,"job_group_id":19},{"job_id":11067,"job_group_id":19},{"job_id":11941,"job_group_id":19},{"job_id":11824,"job_group_id":19},{"job_id":12040,"job_group_id":19},{"job_id":11788,"job_group_id":19},{"job_id":11789,"job_group_id":19},{"job_id":11993,"job_group_id":19},{"job_id":12062,"job_group_id":19},{"job_id":11062,"job_group_id":19},{"job_id":11063,"job_group_id":19},{"job_id":11064,"job_group_id":19},{"job_id":12119,"job_group_id":19},{"job_id":12161,"job_group_id":19},{"job_id":12129,"job_group_id":19},{"job_id":11645,"job_group_id":19},{"job_id":11648,"job_group_id":19},{"job_id":12061,"job_group_id":19},{"job_id":12155,"job_group_id":19},{"job_id":12168,"job_group_id":19},{"job_id":11309,"job_group_id":19},{"job_id":11382,"job_group_id":19},{"job_id":11794,"job_group_id":19},{"job_id":11794,"job_group_id":19},{"job_id":11636,"job_group_id":19},{"job_id":11787,"job_group_id":19},{"job_id":11813,"job_group_id":19},{"job_id":12060,"job_group_id":19},{"job_id":11977,"job_group_id":19},{"job_id":11978,"job_group_id":19},{"job_id":11807,"job_group_id":19},{"job_id":11432,"job_group_id":19},{"job_id":12048,"job_group_id":19},{"job_id":11193,"job_group_id":19},{"job_id":11219,"job_group_id":21},{"job_id":11090,"job_group_id":21},{"job_id":11089,"job_group_id":21},{"job_id":11281,"job_group_id":21},{"job_id":12026,"job_group_id":21},{"job_id":12063,"job_group_id":21},{"job_id":11816,"job_group_id":21},{"job_id":12144,"job_group_id":21},{"job_id":10834,"job_group_id":21},{"job_id":11811,"job_group_id":21},{"job_id":11646,"job_group_id":21},{"job_id":11830,"job_group_id":21},{"job_id":11981,"job_group_id":21},{"job_id":11573,"job_group_id":20},{"job_id":11285,"job_group_id":20},{"job_id":11287,"job_group_id":20},{"job_id":12049,"job_group_id":20},{"job_id":11169,"job_group_id":20},{"job_id":10495,"job_group_id":20},{"job_id":11168,"job_group_id":20},{"job_id":11736,"job_group_id":20},{"job_id":11580,"job_group_id":20},{"job_id":11949,"job_group_id":20},{"job_id":11295,"job_group_id":20},{"job_id":11562,"job_group_id":20},{"job_id":11564,"job_group_id":20},{"job_id":11569,"job_group_id":20},{"job_id":11570,"job_group_id":20},{"job_id":11801,"job_group_id":20},{"job_id":12064,"job_group_id":20},{"job_id":12125,"job_group_id":20},{"job_id":12124,"job_group_id":20},{"job_id":11333,"job_group_id":20},{"job_id":11330,"job_group_id":20},{"job_id":12028,"job_group_id":20},{"job_id":11591,"job_group_id":20},{"job_id":11901,"job_group_id":20},{"job_id":12171,"job_group_id":20},{"job_id":11590,"job_group_id":20},{"job_id":11589,"job_group_id":20},{"job_id":11588,"job_group_id":20},{"job_id":11737,"job_group_id":20},{"job_id":11861,"job_group_id":20},{"job_id":11735,"job_group_id":20},{"job_id":11722,"job_group_id":20},{"job_id":11979,"job_group_id":20},{"job_id":11800,"job_group_id":20},{"job_id":11850,"job_group_id":20},{"job_id":11892,"job_group_id":20},{"job_id":12145,"job_group_id":20},{"job_id":11934,"job_group_id":20},{"job_id":11992,"job_group_id":20},{"job_id":11958,"job_group_id":20},{"job_id":11957,"job_group_id":20},{"job_id":11716,"job_group_id":20},{"job_id":11104,"job_group_id":15},{"job_id":11105,"job_group_id":15},{"job_id":11106,"job_group_id":15},{"job_id":11833,"job_group_id":15},{"job_id":11865,"job_group_id":15},{"job_id":11871,"job_group_id":15},{"job_id":11875,"job_group_id":15},{"job_id":12056,"job_group_id":15},{"job_id":11923,"job_group_id":16},{"job_id":11194,"job_group_id":16},{"job_id":12003,"job_group_id":16},{"job_id":11362,"job_group_id":16},{"job_id":11363,"job_group_id":16},{"job_id":11543,"job_group_id":16},{"job_id":11544,"job_group_id":16},{"job_id":11904,"job_group_id":16},{"job_id":11930,"job_group_id":16},{"job_id":11358,"job_group_id":16},{"job_id":11359,"job_group_id":16},{"job_id":11541,"job_group_id":16},{"job_id":11357,"job_group_id":16},{"job_id":11902,"job_group_id":16},{"job_id":11903,"job_group_id":16},{"job_id":11929,"job_group_id":16},{"job_id":11556,"job_group_id":16},{"job_id":11371,"job_group_id":16},{"job_id":11372,"job_group_id":16},{"job_id":11555,"job_group_id":16},{"job_id":11933,"job_group_id":16},{"job_id":11931,"job_group_id":16},{"job_id":11121,"job_group_id":16},{"job_id":11548,"job_group_id":16},{"job_id":11218,"job_group_id":16},{"job_id":11366,"job_group_id":16},{"job_id":11932,"job_group_id":16},{"job_id":11552,"job_group_id":16},{"job_id":11367,"job_group_id":16},{"job_id":11762,"job_group_id":16},{"job_id":11773,"job_group_id":16},{"job_id":11776,"job_group_id":16},{"job_id":11775,"job_group_id":16},{"job_id":11774,"job_group_id":16},{"job_id":11777,"job_group_id":16},{"job_id":11778,"job_group_id":16},{"job_id":11779,"job_group_id":16},{"job_id":11780,"job_group_id":16},{"job_id":11781,"job_group_id":16},{"job_id":11869,"job_group_id":16},{"job_id":11758,"job_group_id":16},{"job_id":11759,"job_group_id":16},{"job_id":11761,"job_group_id":16},{"job_id":11763,"job_group_id":16},{"job_id":11764,"job_group_id":16},{"job_id":11765,"job_group_id":16},{"job_id":11766,"job_group_id":16},{"job_id":12133,"job_group_id":16},{"job_id":12173,"job_group_id":16},{"job_id":11907,"job_group_id":10},{"job_id":11413,"job_group_id":10},{"job_id":11948,"job_group_id":10},{"job_id":11335,"job_group_id":10},{"job_id":12169,"job_group_id":10},{"job_id":11947,"job_group_id":10},{"job_id":11203,"job_group_id":17},{"job_id":12134,"job_group_id":17},{"job_id":11244,"job_group_id":17},{"job_id":11808,"job_group_id":17},{"job_id":12143,"job_group_id":17},{"job_id":12140,"job_group_id":17},{"job_id":12141,"job_group_id":17},{"job_id":12142,"job_group_id":17},{"job_id":11396,"job_group_id":17},{"job_id":11969,"job_group_id":17},{"job_id":12150,"job_group_id":17},{"job_id":12149,"job_group_id":17},{"job_id":12151,"job_group_id":17},{"job_id":12152,"job_group_id":17},{"job_id":12153,"job_group_id":17},{"job_id":12154,"job_group_id":17},{"job_id":11081,"job_group_id":13},{"job_id":11628,"job_group_id":13},{"job_id":11715,"job_group_id":13},{"job_id":11324,"job_group_id":13},{"job_id":11809,"job_group_id":13},{"job_id":11899,"job_group_id":13},{"job_id":11900,"job_group_id":13},{"job_id":12010,"job_group_id":13},{"job_id":11913,"job_group_id":13},{"job_id":12057,"job_group_id":13},{"job_id":11914,"job_group_id":13},{"job_id":11915,"job_group_id":13},{"job_id":11630,"job_group_id":8},{"job_id":11870,"job_group_id":8},{"job_id":12181,"job_group_id":8},{"job_id":12182,"job_group_id":8},{"job_id":12183,"job_group_id":8}]',
                    '[{"job_title":1082,"job_id":11294},{"job_title":82,"job_id":11919},{"job_title":83,"job_id":11920},{"job_title":1081,"job_id":11292},{"job_title":1538,"job_id":11891},{"job_title":306,"job_id":11874},{"job_title":1544,"job_id":11936},{"job_title":1545,"job_id":11927},{"job_title":1743,"job_id":11403},{"job_title":1750,"job_id":12147},{"job_title":1535,"job_id":11888},{"job_title":1536,"job_id":11890},{"job_title":1537,"job_id":11889},{"job_title":1674,"job_id":12050},{"job_title":1747,"job_id":11888},{"job_title":1786,"job_id":12189},{"job_title":780,"job_id":10780},{"job_title":1509,"job_id":11858},{"job_title":400,"job_id":11205},{"job_title":912,"job_id":11207},{"job_title":913,"job_id":11206},{"job_title":666,"job_id":11466},{"job_title":781,"job_id":10858},{"job_title":76,"job_id":11412},{"job_title":1197,"job_id":11596},{"job_title":1198,"job_id":11595},{"job_title":1474,"job_id":11814},{"job_title":1485,"job_id":11832},{"job_title":658,"job_id":11694},{"job_title":659,"job_id":11696},{"job_title":660,"job_id":11697},{"job_title":1374,"job_id":11695},{"job_title":667,"job_id":11818},{"job_title":494,"job_id":11211},{"job_title":496,"job_id":11213},{"job_title":1690,"job_id":12070},{"job_title":1761,"job_id":12160},{"job_title":77,"job_id":11964},{"job_title":1586,"job_id":11962},{"job_title":1587,"job_id":11963},{"job_title":1050,"job_id":12159},{"job_title":1051,"job_id":11223},{"job_title":1053,"job_id":11225},{"job_title":1077,"job_id":11268},{"job_title":1078,"job_id":11529},{"job_title":73,"job_id":11293},{"job_title":1079,"job_id":11267},{"job_title":149,"job_id":11574},{"job_title":772,"job_id":11222},{"job_title":1069,"job_id":11289},{"job_title":1529,"job_id":11576},{"job_title":1785,"job_id":11659},{"job_title":68,"job_id":11739},{"job_title":287,"job_id":11022},{"job_title":288,"job_id":11100},{"job_title":619,"job_id":11740},{"job_title":647,"job_id":11786},{"job_title":1630,"job_id":12004},{"job_title":1692,"job_id":12073},{"job_title":1784,"job_id":12188},{"job_title":1026,"job_id":11444},{"job_title":1027,"job_id":11445},{"job_title":1028,"job_id":11446},{"job_title":1087,"job_id":11307},{"job_title":1193,"job_id":11447},{"job_title":1208,"job_id":11462},{"job_title":1470,"job_id":11810},{"job_title":1763,"job_id":12162},{"job_title":847,"job_id":11406},{"job_title":1504,"job_id":11853},{"job_title":1277,"job_id":11572},{"job_title":557,"job_id":11288},{"job_title":1284,"job_id":11579},{"job_title":16,"job_id":10925},{"job_title":79,"job_id":10911},{"job_title":269,"job_id":10797},{"job_title":492,"job_id":11792},{"job_title":1276,"job_id":11571},{"job_title":1619,"job_id":10925},{"job_title":1689,"job_id":12071},{"job_title":62,"job_id":10922},{"job_title":147,"job_id":10923},{"job_title":274,"job_id":10924},{"job_title":1452,"job_id":11796},{"job_title":1574,"job_id":11939},{"job_title":548,"job_id":10885},{"job_title":998,"job_id":11091},{"job_title":999,"job_id":11092},{"job_title":1488,"job_id":11835},{"job_title":1688,"job_id":12069},{"job_title":554,"job_id":10964},{"job_title":555,"job_id":10965},{"job_title":556,"job_id":10966},{"job_title":1528,"job_id":11879},{"job_title":1614,"job_id":11989},{"job_title":1615,"job_id":11990},{"job_title":1687,"job_id":12068},{"job_title":1734,"job_id":12126},{"job_title":1048,"job_id":11198},{"job_title":1184,"job_id":11434},{"job_title":916,"job_id":11202},{"job_title":1188,"job_id":11439},{"job_title":1472,"job_id":11812},{"job_title":1136,"job_id":11376},{"job_title":1189,"job_id":11440},{"job_title":454,"job_id":12037},{"job_title":726,"job_id":12138},{"job_title":387,"job_id":10863},{"job_title":394,"job_id":10865},{"job_title":524,"job_id":10861},{"job_title":1564,"job_id":11705},{"job_title":1575,"job_id":11942},{"job_title":526,"job_id":10857},{"job_title":527,"job_id":10855},{"job_title":1764,"job_id":12163},{"job_title":1107,"job_id":11334},{"job_title":207,"job_id":11084},{"job_title":352,"job_id":10867},{"job_title":354,"job_id":10869},{"job_title":793,"job_id":11332},{"job_title":794,"job_id":11331},{"job_title":1320,"job_id":11624},{"job_title":855,"job_id":11433},{"job_title":1481,"job_id":11827},{"job_title":560,"job_id":10919},{"job_title":565,"job_id":10915},{"job_title":571,"job_id":10917},{"job_title":1322,"job_id":11631},{"job_title":1492,"job_id":11840},{"job_title":562,"job_id":10913},{"job_title":567,"job_id":10908},{"job_title":573,"job_id":10910},{"job_title":1484,"job_id":11831},{"job_title":1493,"job_id":11841},{"job_title":802,"job_id":10907},{"job_title":803,"job_id":10905},{"job_title":804,"job_id":10903},{"job_title":1491,"job_id":11839},{"job_title":1494,"job_id":11844},{"job_title":1723,"job_id":12108},{"job_title":1724,"job_id":12109},{"job_title":1725,"job_id":12110},{"job_title":1726,"job_id":12111},{"job_title":1727,"job_id":12113},{"job_title":575,"job_id":10894},{"job_title":576,"job_id":10892},{"job_title":808,"job_id":10891},{"job_title":1391,"job_id":11714},{"job_title":1495,"job_id":11842},{"job_title":561,"job_id":11592},{"job_title":566,"job_id":11594},{"job_title":570,"job_id":11593},{"job_title":1323,"job_id":11632},{"job_title":1496,"job_id":11843},{"job_title":410,"job_id":11522},{"job_title":1211,"job_id":11492},{"job_title":1212,"job_id":11493},{"job_title":1245,"job_id":11514},{"job_title":1287,"job_id":11584},{"job_title":50,"job_id":10902},{"job_title":272,"job_id":10901},{"job_title":1295,"job_id":11597},{"job_title":1288,"job_id":11585},{"job_title":1289,"job_id":11586},{"job_title":1290,"job_id":11587},{"job_title":1489,"job_id":11836},{"job_title":1296,"job_id":11598},{"job_title":1005,"job_id":11621},{"job_title":1346,"job_id":11659},{"job_title":1669,"job_id":11951},{"job_title":1670,"job_id":12137},{"job_title":1375,"job_id":11698},{"job_title":1376,"job_id":11699},{"job_title":1765,"job_id":12164},{"job_title":1209,"job_id":12122},{"job_title":1562,"job_id":11700},{"job_title":1563,"job_id":11703},{"job_title":1565,"job_id":11821},{"job_title":917,"job_id":11717},{"job_title":1447,"job_id":11791},{"job_title":1463,"job_id":11883},{"job_title":1464,"job_id":11884},{"job_title":1465,"job_id":11815},{"job_title":1466,"job_id":12121},{"job_title":1467,"job_id":11882},{"job_title":1490,"job_id":11838},{"job_title":1487,"job_id":11834},{"job_title":1645,"job_id":12020},{"job_title":495,"job_id":11212},{"job_title":158,"job_id":11440},{"job_title":416,"job_id":11969},{"job_title":320,"job_id":11855},{"job_title":1588,"job_id":11965},{"job_title":1589,"job_id":11966},{"job_title":1590,"job_id":11967},{"job_title":1591,"job_id":11968},{"job_title":1611,"job_id":11986},{"job_title":1759,"job_id":12157},{"job_title":1612,"job_id":11987},{"job_title":1613,"job_id":11988},{"job_title":1641,"job_id":12016},{"job_title":1583,"job_id":11956},{"job_title":1623,"job_id":12054},{"job_title":1626,"job_id":12052},{"job_title":1627,"job_id":11995},{"job_title":1628,"job_id":11994},{"job_title":1646,"job_id":12053},{"job_title":932,"job_id":12059},{"job_title":65,"job_id":12058},{"job_title":1656,"job_id":12031},{"job_title":1657,"job_id":12032},{"job_title":1658,"job_id":12033},{"job_title":1685,"job_id":12066},{"job_title":1686,"job_id":12067},{"job_title":1345,"job_id":11656},{"job_title":1716,"job_id":12099},{"job_title":1717,"job_id":12100},{"job_title":1718,"job_id":12101},{"job_title":1719,"job_id":12102},{"job_title":1720,"job_id":12103},{"job_title":1693,"job_id":12074},{"job_title":1694,"job_id":12075},{"job_title":1695,"job_id":12076},{"job_title":1696,"job_id":12077},{"job_title":1697,"job_id":12078},{"job_title":1760,"job_id":12158},{"job_title":1703,"job_id":12084},{"job_title":1704,"job_id":12085},{"job_title":1705,"job_id":12086},{"job_title":1706,"job_id":12087},{"job_title":1707,"job_id":12088},{"job_title":1708,"job_id":12089},{"job_title":1709,"job_id":12090},{"job_title":1710,"job_id":12091},{"job_title":1711,"job_id":12092},{"job_title":1712,"job_id":12093},{"job_title":1698,"job_id":12079},{"job_title":1699,"job_id":12080},{"job_title":1700,"job_id":12081},{"job_title":1701,"job_id":12082},{"job_title":1702,"job_id":12083},{"job_title":1722,"job_id":12107},{"job_title":1655,"job_id":12029},{"job_title":1767,"job_id":12167},{"job_title":1735,"job_id":12128},{"job_title":1736,"job_id":12127},{"job_title":141,"job_id":11273},{"job_title":996,"job_id":12036},{"job_title":1777,"job_id":12179},{"job_title":13,"job_id":10916},{"job_title":37,"job_id":10914},{"job_title":110,"job_id":10918},{"job_title":452,"job_id":10920},{"job_title":1000,"job_id":11093},{"job_title":1194,"job_id":11449},{"job_title":1553,"job_id":11911},{"job_title":1652,"job_id":12027},{"job_title":1675,"job_id":12055},{"job_title":1192,"job_id":11443},{"job_title":775,"job_id":11083},{"job_title":474,"job_id":11520},{"job_title":1526,"job_id":11880},{"job_title":805,"job_id":10899},{"job_title":806,"job_id":10897},{"job_title":807,"job_id":10895},{"job_title":1446,"job_id":11790},{"job_title":1499,"job_id":11847},{"job_title":753,"job_id":11599},{"job_title":1297,"job_id":11600},{"job_title":1412,"job_id":11750},{"job_title":1413,"job_id":11751},{"job_title":1414,"job_id":11744},{"job_title":1416,"job_id":11748},{"job_title":1417,"job_id":11752},{"job_title":1418,"job_id":11749},{"job_title":1770,"job_id":12170},{"job_title":300,"job_id":11746},{"job_title":1581,"job_id":11954},{"job_title":1582,"job_id":11955},{"job_title":1173,"job_id":11421},{"job_title":1349,"job_id":11663},{"job_title":1350,"job_id":11666},{"job_title":1668,"job_id":12123},{"job_title":1596,"job_id":11972},{"job_title":1597,"job_id":11974},{"job_title":733,"job_id":12165},{"job_title":1778,"job_id":12180},{"job_title":1782,"job_id":12184},{"job_title":1046,"job_id":11195},{"job_title":1633,"job_id":12008},{"job_title":1730,"job_id":11262},{"job_title":645,"job_id":11264},{"job_title":1513,"job_id":11862},{"job_title":1654,"job_id":12030},{"job_title":1671,"job_id":12047},{"job_title":530,"job_id":12047},{"job_title":532,"job_id":11260},{"job_title":1775,"job_id":12177},{"job_title":1065,"job_id":11262},{"job_title":1776,"job_id":12178},{"job_title":1609,"job_id":11984},{"job_title":1639,"job_id":12014},{"job_title":1758,"job_id":12156},{"job_title":1642,"job_id":12017},{"job_title":27,"job_id":12098},{"job_title":552,"job_id":12096},{"job_title":1713,"job_id":12094},{"job_title":1714,"job_id":12095},{"job_title":1715,"job_id":12097},{"job_title":240,"job_id":11190},{"job_title":335,"job_id":11189},{"job_title":1476,"job_id":11820},{"job_title":1636,"job_id":12011},{"job_title":1644,"job_id":12019},{"job_title":90,"job_id":11909},{"job_title":166,"job_id":11909},{"job_title":246,"job_id":11788},{"job_title":247,"job_id":11817},{"job_title":1283,"job_id":11578},{"job_title":78,"job_id":11393},{"job_title":248,"job_id":11065},{"job_title":978,"job_id":11067},{"job_title":1139,"job_id":11941},{"job_title":1479,"job_id":11824},{"job_title":1660,"job_id":12040},{"job_title":1444,"job_id":11788},{"job_title":1445,"job_id":11789},{"job_title":1618,"job_id":11993},{"job_title":1682,"job_id":12062},{"job_title":982,"job_id":11062},{"job_title":983,"job_id":11063},{"job_title":984,"job_id":11064},{"job_title":1729,"job_id":12119},{"job_title":1762,"job_id":12161},{"job_title":969,"job_id":12129},{"job_title":1333,"job_id":11645},{"job_title":1336,"job_id":11648},{"job_title":1681,"job_id":12061},{"job_title":1757,"job_id":12155},{"job_title":1768,"job_id":12168},{"job_title":890,"job_id":11309},{"job_title":1448,"job_id":11382},{"job_title":1449,"job_id":11794},{"job_title":1451,"job_id":11794},{"job_title":1326,"job_id":11636},{"job_title":1443,"job_id":11787},{"job_title":1473,"job_id":11813},{"job_title":1680,"job_id":12060},{"job_title":1601,"job_id":11977},{"job_title":1602,"job_id":11978},{"job_title":478,"job_id":11807},{"job_title":1183,"job_id":11432},{"job_title":1673,"job_id":12048},{"job_title":1043,"job_id":11193},{"job_title":56,"job_id":11219},{"job_title":145,"job_id":11090},{"job_title":708,"job_id":11089},{"job_title":1098,"job_id":11281},{"job_title":1651,"job_id":12026},{"job_title":1683,"job_id":12063},{"job_title":1321,"job_id":11816},{"job_title":1746,"job_id":12144},{"job_title":1002,"job_id":10834},{"job_title":1471,"job_id":11811},{"job_title":1334,"job_id":11646},{"job_title":1483,"job_id":11830},{"job_title":1605,"job_id":11981},{"job_title":1394,"job_id":11573},{"job_title":1076,"job_id":11285},{"job_title":528,"job_id":11287},{"job_title":1514,"job_id":12049},{"job_title":4,"job_id":11169},{"job_title":115,"job_id":10495},{"job_title":334,"job_id":11168},{"job_title":1167,"job_id":11736},{"job_title":1285,"job_id":11580},{"job_title":1010,"job_id":11949},{"job_title":620,"job_id":11295},{"job_title":1267,"job_id":11562},{"job_title":1269,"job_id":11564},{"job_title":1274,"job_id":11569},{"job_title":1275,"job_id":11570},{"job_title":1456,"job_id":11801},{"job_title":1684,"job_id":12064},{"job_title":1731,"job_id":12125},{"job_title":1733,"job_id":12124},{"job_title":1108,"job_id":11333},{"job_title":1106,"job_id":11330},{"job_title":263,"job_id":12028},{"job_title":1294,"job_id":11591},{"job_title":1548,"job_id":11901},{"job_title":1771,"job_id":12171},{"job_title":1293,"job_id":11590},{"job_title":1292,"job_id":11589},{"job_title":1291,"job_id":11588},{"job_title":1410,"job_id":11737},{"job_title":1512,"job_id":11861},{"job_title":617,"job_id":11735},{"job_title":1395,"job_id":11722},{"job_title":1603,"job_id":11979},{"job_title":1025,"job_id":11800},{"job_title":995,"job_id":11850},{"job_title":1539,"job_id":11892},{"job_title":1748,"job_id":12145},{"job_title":1029,"job_id":11934},{"job_title":1617,"job_id":11992},{"job_title":1392,"job_id":11958},{"job_title":1584,"job_id":11957},{"job_title":1606,"job_id":11716},{"job_title":253,"job_id":11104},{"job_title":413,"job_id":11105},{"job_title":414,"job_id":11106},{"job_title":1486,"job_id":11833},{"job_title":1516,"job_id":11865},{"job_title":1521,"job_id":11871},{"job_title":1524,"job_id":11875},{"job_title":1721,"job_id":12056},{"job_title":1561,"job_id":11923},{"job_title":453,"job_id":11194},{"job_title":1629,"job_id":12003},{"job_title":1125,"job_id":11362},{"job_title":1126,"job_id":11363},{"job_title":1251,"job_id":11543},{"job_title":1252,"job_id":11544},{"job_title":1551,"job_id":11904},{"job_title":1569,"job_id":11930},{"job_title":1121,"job_id":11358},{"job_title":1122,"job_id":11359},{"job_title":1123,"job_id":11541},{"job_title":1249,"job_id":11357},{"job_title":1549,"job_id":11902},{"job_title":1550,"job_id":11903},{"job_title":1568,"job_id":11929},{"job_title":633,"job_id":11556},{"job_title":1130,"job_id":11371},{"job_title":1131,"job_id":11372},{"job_title":1260,"job_id":11555},{"job_title":1570,"job_id":11933},{"job_title":96,"job_id":11931},{"job_title":194,"job_id":11121},{"job_title":586,"job_id":11548},{"job_title":883,"job_id":11218},{"job_title":196,"job_id":11366},{"job_title":197,"job_id":11932},{"job_title":591,"job_id":11552},{"job_title":1128,"job_id":11367},{"job_title":1426,"job_id":11762},{"job_title":3,"job_id":11773},{"job_title":190,"job_id":11776},{"job_title":797,"job_id":11775},{"job_title":1437,"job_id":11774},{"job_title":1438,"job_id":11777},{"job_title":1439,"job_id":11778},{"job_title":1440,"job_id":11779},{"job_title":1441,"job_id":11780},{"job_title":1442,"job_id":11781},{"job_title":1019,"job_id":11869},{"job_title":1423,"job_id":11758},{"job_title":1424,"job_id":11759},{"job_title":1425,"job_id":11761},{"job_title":1427,"job_id":11763},{"job_title":1428,"job_id":11764},{"job_title":1429,"job_id":11765},{"job_title":1430,"job_id":11766},{"job_title":1074,"job_id":12133},{"job_title":1772,"job_id":12173},{"job_title":1007,"job_id":11907},{"job_title":1165,"job_id":11413},{"job_title":632,"job_id":11948},{"job_title":1102,"job_id":11335},{"job_title":1769,"job_id":12169},{"job_title":23,"job_id":11947},{"job_title":261,"job_id":11203},{"job_title":899,"job_id":12134},{"job_title":1036,"job_id":11244},{"job_title":1468,"job_id":11808},{"job_title":1744,"job_id":12143},{"job_title":1745,"job_id":12140},{"job_title":337,"job_id":12141},{"job_title":399,"job_id":12142},{"job_title":1156,"job_id":11396},{"job_title":1592,"job_id":11969},{"job_title":1752,"job_id":12150},{"job_title":1751,"job_id":12149},{"job_title":1753,"job_id":12151},{"job_title":1754,"job_id":12152},{"job_title":1755,"job_id":12153},{"job_title":1756,"job_id":12154},{"job_title":344,"job_id":11081},{"job_title":962,"job_id":11628},{"job_title":220,"job_id":11715},{"job_title":34,"job_id":11324},{"job_title":1469,"job_id":11809},{"job_title":1546,"job_id":11899},{"job_title":1547,"job_id":11900},{"job_title":1635,"job_id":12010},{"job_title":1555,"job_id":11913},{"job_title":1678,"job_id":12057},{"job_title":1556,"job_id":11914},{"job_title":1557,"job_id":11915},{"job_title":20,"job_id":11630},{"job_title":774,"job_id":11870},{"job_title":1779,"job_id":12181},{"job_title":1780,"job_id":12182},{"job_title":1781,"job_id":12183}]',
                ];
                break;
            case 'my':
                $data = [
                    '[{"job_title":82,"department_id":248,"group_id":4},{"job_title":904,"department_id":248,"group_id":4},{"job_title":1176,"department_id":248,"group_id":4},{"job_title":1219,"department_id":248,"group_id":4},{"job_title":1276,"department_id":248,"group_id":4},{"job_title":778,"department_id":337,"group_id":4},{"job_title":779,"department_id":337,"group_id":4},{"job_title":1173,"department_id":337,"group_id":4},{"job_title":1731,"department_id":337,"group_id":4},{"job_title":1789,"department_id":337,"group_id":4},{"job_title":1793,"department_id":337,"group_id":4},{"job_title":494,"department_id":15086,"group_id":9},{"job_title":495,"department_id":15086,"group_id":9},{"job_title":496,"department_id":15086,"group_id":9},{"job_title":1228,"department_id":15086,"group_id":9},{"job_title":1768,"department_id":15086,"group_id":9},{"job_title":1683,"department_id":245,"group_id":9},{"job_title":1684,"department_id":245,"group_id":9},{"job_title":1685,"department_id":245,"group_id":9},{"job_title":564,"department_id":328,"group_id":9},{"job_title":569,"department_id":328,"group_id":9},{"job_title":1610,"department_id":328,"group_id":9},{"job_title":1070,"department_id":330,"group_id":9},{"job_title":1781,"department_id":70003,"group_id":9},{"job_title":1782,"department_id":70003,"group_id":9},{"job_title":73,"department_id":15000,"group_id":29},{"job_title":259,"department_id":15000,"group_id":29},{"job_title":462,"department_id":15000,"group_id":29},{"job_title":465,"department_id":15000,"group_id":29},{"job_title":731,"department_id":15000,"group_id":29},{"job_title":1791,"department_id":15007,"group_id":29},{"job_title":73,"department_id":15064,"group_id":29},{"job_title":259,"department_id":15064,"group_id":29},{"job_title":465,"department_id":15064,"group_id":29},{"job_title":731,"department_id":15064,"group_id":29},{"job_title":1736,"department_id":15108,"group_id":29},{"job_title":73,"department_id":15154,"group_id":29},{"job_title":259,"department_id":15154,"group_id":29},{"job_title":465,"department_id":15154,"group_id":29},{"job_title":469,"department_id":15154,"group_id":29},{"job_title":731,"department_id":15154,"group_id":29},{"job_title":465,"department_id":15200,"group_id":29},{"job_title":469,"department_id":15200,"group_id":29},{"job_title":731,"department_id":15200,"group_id":29},{"job_title":772,"department_id":249,"group_id":29},{"job_title":908,"department_id":249,"group_id":29},{"job_title":1664,"department_id":249,"group_id":29},{"job_title":1763,"department_id":249,"group_id":29},{"job_title":771,"department_id":388,"group_id":29},{"job_title":946,"department_id":388,"group_id":29},{"job_title":1761,"department_id":388,"group_id":29},{"job_title":1538,"department_id":15053,"group_id":12},{"job_title":287,"department_id":344,"group_id":12},{"job_title":288,"department_id":344,"group_id":12},{"job_title":619,"department_id":344,"group_id":12},{"job_title":647,"department_id":344,"group_id":12},{"job_title":1720,"department_id":344,"group_id":12},{"job_title":287,"department_id":373,"group_id":12},{"job_title":288,"department_id":373,"group_id":12},{"job_title":263,"department_id":15006,"group_id":27},{"job_title":1792,"department_id":15007,"group_id":27},{"job_title":1676,"department_id":15010,"group_id":27},{"job_title":1692,"department_id":15046,"group_id":27},{"job_title":1256,"department_id":15048,"group_id":27},{"job_title":1752,"department_id":15048,"group_id":27},{"job_title":1376,"department_id":15070,"group_id":27},{"job_title":1377,"department_id":15070,"group_id":27},{"job_title":1378,"department_id":15070,"group_id":27},{"job_title":1412,"department_id":15070,"group_id":27},{"job_title":491,"department_id":15081,"group_id":27},{"job_title":1234,"department_id":15081,"group_id":27},{"job_title":1716,"department_id":15081,"group_id":27},{"job_title":1727,"department_id":15081,"group_id":27},{"job_title":1737,"department_id":15090,"group_id":27},{"job_title":132,"department_id":15109,"group_id":27},{"job_title":1523,"department_id":15109,"group_id":27},{"job_title":1779,"department_id":15109,"group_id":27},{"job_title":1516,"department_id":15111,"group_id":27},{"job_title":1552,"department_id":15117,"group_id":27},{"job_title":1553,"department_id":15117,"group_id":27},{"job_title":1557,"department_id":15118,"group_id":27},{"job_title":354,"department_id":15119,"group_id":27},{"job_title":1558,"department_id":15119,"group_id":27},{"job_title":1724,"department_id":15122,"group_id":27},{"job_title":1569,"department_id":15123,"group_id":27},{"job_title":1571,"department_id":15123,"group_id":27},{"job_title":1654,"department_id":15124,"group_id":27},{"job_title":1655,"department_id":15124,"group_id":27},{"job_title":1574,"department_id":15125,"group_id":27},{"job_title":1575,"department_id":15125,"group_id":27},{"job_title":788,"department_id":15126,"group_id":27},{"job_title":1672,"department_id":15126,"group_id":27},{"job_title":1625,"department_id":15139,"group_id":27},{"job_title":1626,"department_id":15139,"group_id":27},{"job_title":1600,"department_id":15140,"group_id":27},{"job_title":158,"department_id":15180,"group_id":27},{"job_title":836,"department_id":15180,"group_id":27},{"job_title":1261,"department_id":15180,"group_id":27},{"job_title":1697,"department_id":15193,"group_id":27},{"job_title":1699,"department_id":15193,"group_id":27},{"job_title":1700,"department_id":15193,"group_id":27},{"job_title":691,"department_id":15201,"group_id":27},{"job_title":1726,"department_id":15201,"group_id":27},{"job_title":1758,"department_id":15201,"group_id":27},{"job_title":1739,"department_id":15203,"group_id":27},{"job_title":1741,"department_id":15203,"group_id":27},{"job_title":1742,"department_id":15203,"group_id":27},{"job_title":1757,"department_id":15203,"group_id":27},{"job_title":1783,"department_id":15203,"group_id":27},{"job_title":1765,"department_id":15210,"group_id":27},{"job_title":1766,"department_id":15210,"group_id":27},{"job_title":1039,"department_id":244,"group_id":27},{"job_title":158,"department_id":246,"group_id":27},{"job_title":434,"department_id":246,"group_id":27},{"job_title":836,"department_id":246,"group_id":27},{"job_title":1203,"department_id":246,"group_id":27},{"job_title":1417,"department_id":246,"group_id":27},{"job_title":264,"department_id":315,"group_id":27},{"job_title":1451,"department_id":315,"group_id":27},{"job_title":1788,"department_id":315,"group_id":27},{"job_title":799,"department_id":316,"group_id":27},{"job_title":1743,"department_id":316,"group_id":27},{"job_title":1617,"department_id":318,"group_id":27},{"job_title":1096,"department_id":319,"group_id":27},{"job_title":1760,"department_id":319,"group_id":27},{"job_title":16,"department_id":320,"group_id":27},{"job_title":79,"department_id":320,"group_id":27},{"job_title":269,"department_id":320,"group_id":27},{"job_title":1461,"department_id":320,"group_id":27},{"job_title":1485,"department_id":320,"group_id":27},{"job_title":1491,"department_id":320,"group_id":27},{"job_title":999,"department_id":321,"group_id":27},{"job_title":1723,"department_id":321,"group_id":27},{"job_title":1657,"department_id":322,"group_id":27},{"job_title":50,"department_id":328,"group_id":27},{"job_title":272,"department_id":328,"group_id":27},{"job_title":566,"department_id":328,"group_id":27},{"job_title":567,"department_id":328,"group_id":27},{"job_title":576,"department_id":328,"group_id":27},{"job_title":578,"department_id":328,"group_id":27},{"job_title":807,"department_id":328,"group_id":27},{"job_title":808,"department_id":328,"group_id":27},{"job_title":1111,"department_id":328,"group_id":27},{"job_title":1481,"department_id":328,"group_id":27},{"job_title":1614,"department_id":328,"group_id":27},{"job_title":1703,"department_id":328,"group_id":27},{"job_title":1707,"department_id":328,"group_id":27},{"job_title":1718,"department_id":328,"group_id":27},{"job_title":1719,"department_id":328,"group_id":27},{"job_title":576,"department_id":329,"group_id":27},{"job_title":808,"department_id":329,"group_id":27},{"job_title":1280,"department_id":329,"group_id":27},{"job_title":561,"department_id":331,"group_id":27},{"job_title":1532,"department_id":332,"group_id":27},{"job_title":527,"department_id":334,"group_id":27},{"job_title":1660,"department_id":334,"group_id":27},{"job_title":324,"department_id":335,"group_id":27},{"job_title":388,"department_id":335,"group_id":27},{"job_title":389,"department_id":335,"group_id":27},{"job_title":519,"department_id":335,"group_id":27},{"job_title":1232,"department_id":335,"group_id":27},{"job_title":675,"department_id":363,"group_id":27},{"job_title":1327,"department_id":363,"group_id":27},{"job_title":141,"department_id":6,"group_id":27},{"job_title":1008,"department_id":60001,"group_id":27},{"job_title":1784,"department_id":70005,"group_id":27},{"job_title":1785,"department_id":70005,"group_id":27},{"job_title":160,"department_id":999,"group_id":27},{"job_title":1483,"department_id":328,"group_id":27},{"job_title":1704,"department_id":328,"group_id":27},{"job_title":1242,"department_id":15046,"group_id":3},{"job_title":1734,"department_id":15048,"group_id":3},{"job_title":1759,"department_id":15084,"group_id":3},{"job_title":410,"department_id":15109,"group_id":3},{"job_title":474,"department_id":15109,"group_id":3},{"job_title":1701,"department_id":15109,"group_id":3},{"job_title":300,"department_id":15179,"group_id":3},{"job_title":300,"department_id":15180,"group_id":3},{"job_title":1172,"department_id":15180,"group_id":3},{"job_title":1295,"department_id":15180,"group_id":3},{"job_title":1244,"department_id":15209,"group_id":3},{"job_title":1245,"department_id":15209,"group_id":3},{"job_title":300,"department_id":246,"group_id":3},{"job_title":1172,"department_id":246,"group_id":3},{"job_title":1295,"department_id":246,"group_id":3},{"job_title":13,"department_id":320,"group_id":3},{"job_title":37,"department_id":320,"group_id":3},{"job_title":110,"department_id":320,"group_id":3},{"job_title":451,"department_id":320,"group_id":3},{"job_title":1199,"department_id":320,"group_id":3},{"job_title":1759,"department_id":320,"group_id":3},{"job_title":572,"department_id":328,"group_id":3},{"job_title":1484,"department_id":328,"group_id":3},{"job_title":1705,"department_id":328,"group_id":3},{"job_title":1706,"department_id":328,"group_id":3},{"job_title":473,"department_id":363,"group_id":3},{"job_title":812,"department_id":363,"group_id":3},{"job_title":684,"department_id":15002,"group_id":13},{"job_title":1035,"department_id":15002,"group_id":13},{"job_title":1525,"department_id":15002,"group_id":13},{"job_title":1771,"department_id":15002,"group_id":13},{"job_title":1728,"department_id":54,"group_id":13},{"job_title":1624,"department_id":15015,"group_id":15},{"job_title":1366,"department_id":15068,"group_id":15},{"job_title":1414,"department_id":15068,"group_id":15},{"job_title":1496,"department_id":15068,"group_id":15},{"job_title":1319,"department_id":15095,"group_id":15},{"job_title":1501,"department_id":15095,"group_id":15},{"job_title":170,"department_id":15108,"group_id":15},{"job_title":1513,"department_id":15110,"group_id":15},{"job_title":240,"department_id":15214,"group_id":15},{"job_title":1092,"department_id":15214,"group_id":15},{"job_title":1208,"department_id":15214,"group_id":15},{"job_title":1520,"department_id":15214,"group_id":15},{"job_title":1774,"department_id":15214,"group_id":15},{"job_title":1775,"department_id":15214,"group_id":15},{"job_title":1776,"department_id":15214,"group_id":15},{"job_title":1777,"department_id":15214,"group_id":15},{"job_title":1410,"department_id":303,"group_id":15},{"job_title":1411,"department_id":303,"group_id":15},{"job_title":869,"department_id":366,"group_id":15},{"job_title":1084,"department_id":366,"group_id":15},{"job_title":1497,"department_id":366,"group_id":15},{"job_title":1515,"department_id":366,"group_id":15},{"job_title":78,"department_id":367,"group_id":15},{"job_title":92,"department_id":367,"group_id":15},{"job_title":978,"department_id":367,"group_id":15},{"job_title":1319,"department_id":367,"group_id":15},{"job_title":1653,"department_id":367,"group_id":15},{"job_title":1085,"department_id":368,"group_id":15},{"job_title":1086,"department_id":368,"group_id":15},{"job_title":1144,"department_id":368,"group_id":15},{"job_title":1297,"department_id":368,"group_id":15},{"job_title":890,"department_id":444,"group_id":15},{"job_title":729,"department_id":15112,"group_id":32},{"job_title":1790,"department_id":15112,"group_id":32},{"job_title":56,"department_id":15147,"group_id":32},{"job_title":1132,"department_id":15147,"group_id":32},{"job_title":1069,"department_id":15207,"group_id":32},{"job_title":1165,"department_id":15008,"group_id":16},{"job_title":1165,"department_id":15068,"group_id":16},{"job_title":1165,"department_id":15070,"group_id":16},{"job_title":115,"department_id":15079,"group_id":16},{"job_title":334,"department_id":15079,"group_id":16},{"job_title":1324,"department_id":15079,"group_id":16},{"job_title":1419,"department_id":15079,"group_id":16},{"job_title":1424,"department_id":15079,"group_id":16},{"job_title":1165,"department_id":15086,"group_id":16},{"job_title":1025,"department_id":15096,"group_id":16},{"job_title":1154,"department_id":15096,"group_id":16},{"job_title":1423,"department_id":15096,"group_id":16},{"job_title":1735,"department_id":15108,"group_id":16},{"job_title":1755,"department_id":15108,"group_id":16},{"job_title":1780,"department_id":15108,"group_id":16},{"job_title":520,"department_id":15109,"group_id":16},{"job_title":1165,"department_id":15193,"group_id":16},{"job_title":191,"department_id":15197,"group_id":16},{"job_title":1285,"department_id":15197,"group_id":16},{"job_title":1710,"department_id":15198,"group_id":16},{"job_title":1711,"department_id":15198,"group_id":16},{"job_title":1712,"department_id":15198,"group_id":16},{"job_title":1165,"department_id":15201,"group_id":16},{"job_title":1165,"department_id":15210,"group_id":16},{"job_title":1165,"department_id":21,"group_id":16},{"job_title":995,"department_id":217,"group_id":16},{"job_title":4,"department_id":22,"group_id":16},{"job_title":115,"department_id":22,"group_id":16},{"job_title":334,"department_id":22,"group_id":16},{"job_title":1324,"department_id":22,"group_id":16},{"job_title":1103,"department_id":318,"group_id":16},{"job_title":1165,"department_id":322,"group_id":16},{"job_title":568,"department_id":328,"group_id":16},{"job_title":1662,"department_id":328,"group_id":16},{"job_title":1778,"department_id":337,"group_id":16},{"job_title":1165,"department_id":343,"group_id":16},{"job_title":1165,"department_id":344,"group_id":16},{"job_title":1165,"department_id":367,"group_id":16},{"job_title":1165,"department_id":368,"group_id":16},{"job_title":1165,"department_id":373,"group_id":16},{"job_title":1157,"department_id":70002,"group_id":16},{"job_title":1154,"department_id":9,"group_id":16},{"job_title":1165,"department_id":9,"group_id":16},{"job_title":1420,"department_id":999,"group_id":16},{"job_title":413,"department_id":15016,"group_id":11},{"job_title":414,"department_id":15016,"group_id":11},{"job_title":1545,"department_id":15016,"group_id":11},{"job_title":1644,"department_id":15192,"group_id":11},{"job_title":1717,"department_id":373,"group_id":11},{"job_title":1184,"department_id":15018,"group_id":1},{"job_title":1197,"department_id":15018,"group_id":1},{"job_title":1198,"department_id":15018,"group_id":1},{"job_title":3,"department_id":15039,"group_id":1},{"job_title":190,"department_id":15039,"group_id":1},{"job_title":797,"department_id":15039,"group_id":1},{"job_title":3,"department_id":15040,"group_id":1},{"job_title":1432,"department_id":15041,"group_id":1},{"job_title":1756,"department_id":15041,"group_id":1},{"job_title":1112,"department_id":15044,"group_id":1},{"job_title":1212,"department_id":15044,"group_id":1},{"job_title":1019,"department_id":15045,"group_id":1},{"job_title":1443,"department_id":15045,"group_id":1},{"job_title":1444,"department_id":15045,"group_id":1},{"job_title":1445,"department_id":15045,"group_id":1},{"job_title":96,"department_id":15080,"group_id":1},{"job_title":194,"department_id":15080,"group_id":1},{"job_title":1769,"department_id":15213,"group_id":1},{"job_title":1770,"department_id":15213,"group_id":1},{"job_title":1185,"department_id":21,"group_id":1},{"job_title":2,"department_id":23,"group_id":1},{"job_title":193,"department_id":23,"group_id":1},{"job_title":1188,"department_id":23,"group_id":1},{"job_title":1195,"department_id":23,"group_id":1},{"job_title":1267,"department_id":23,"group_id":1},{"job_title":1,"department_id":24,"group_id":1},{"job_title":195,"department_id":24,"group_id":1},{"job_title":1196,"department_id":24,"group_id":1},{"job_title":1214,"department_id":24,"group_id":1},{"job_title":197,"department_id":25,"group_id":1},{"job_title":194,"department_id":26,"group_id":1},{"job_title":586,"department_id":26,"group_id":1},{"job_title":883,"department_id":26,"group_id":1},{"job_title":1430,"department_id":27,"group_id":1},{"job_title":1669,"department_id":333,"group_id":1},{"job_title":1107,"department_id":336,"group_id":1},{"job_title":1007,"department_id":222,"group_id":30},{"job_title":486,"department_id":333,"group_id":30},{"job_title":632,"department_id":444,"group_id":30},{"job_title":1194,"department_id":343,"group_id":31},{"job_title":1524,"department_id":343,"group_id":31},{"job_title":1546,"department_id":343,"group_id":31},{"job_title":1767,"department_id":343,"group_id":31},{"job_title":33,"department_id":15008,"group_id":10},{"job_title":34,"department_id":15008,"group_id":10},{"job_title":344,"department_id":15008,"group_id":10},{"job_title":962,"department_id":15008,"group_id":10},{"job_title":1689,"department_id":15008,"group_id":10},{"job_title":1690,"department_id":15211,"group_id":10},{"job_title":1691,"department_id":15211,"group_id":10},{"job_title":1358,"department_id":247,"group_id":10},{"job_title":1359,"department_id":247,"group_id":10},{"job_title":1360,"department_id":247,"group_id":10},{"job_title":1361,"department_id":247,"group_id":10},{"job_title":220,"department_id":328,"group_id":10},{"job_title":661,"department_id":328,"group_id":10},{"job_title":1663,"department_id":328,"group_id":10},{"job_title":927,"department_id":15199,"group_id":28},{"job_title":1323,"department_id":15199,"group_id":28},{"job_title":1744,"department_id":15199,"group_id":28},{"job_title":1786,"department_id":70004,"group_id":28},{"job_title":1787,"department_id":70004,"group_id":28}]',
                    '[{"job_id":11831,"job_group_id":4},{"job_id":10592,"job_group_id":4},{"job_id":11353,"job_group_id":4},{"job_id":11397,"job_group_id":4},{"job_id":11460,"job_group_id":4},{"job_id":10792,"job_group_id":4},{"job_id":11730,"job_group_id":4},{"job_id":11350,"job_group_id":4},{"job_id":11395,"job_group_id":4},{"job_id":11994,"job_group_id":4},{"job_id":11998,"job_group_id":4},{"job_id":10354,"job_group_id":9},{"job_id":10355,"job_group_id":9},{"job_id":10356,"job_group_id":9},{"job_id":11407,"job_group_id":9},{"job_id":11971,"job_group_id":9},{"job_id":11890,"job_group_id":9},{"job_id":11889,"job_group_id":9},{"job_id":11888,"job_group_id":9},{"job_id":10284,"job_group_id":9},{"job_id":10184,"job_group_id":9},{"job_id":11802,"job_group_id":9},{"job_id":10466,"job_group_id":9},{"job_id":11987,"job_group_id":9},{"job_id":11988,"job_group_id":9},{"job_id":10063,"job_group_id":29},{"job_id":10244,"job_group_id":29},{"job_id":10230,"job_group_id":29},{"job_id":10165,"job_group_id":29},{"job_id":10665,"job_group_id":29},{"job_id":11997,"job_group_id":29},{"job_id":10063,"job_group_id":29},{"job_id":10244,"job_group_id":29},{"job_id":10165,"job_group_id":29},{"job_id":10665,"job_group_id":29},{"job_id":11983,"job_group_id":29},{"job_id":10063,"job_group_id":29},{"job_id":10244,"job_group_id":29},{"job_id":10165,"job_group_id":29},{"job_id":10137,"job_group_id":29},{"job_id":10665,"job_group_id":29},{"job_id":10165,"job_group_id":29},{"job_id":10137,"job_group_id":29},{"job_id":10665,"job_group_id":29},{"job_id":11318,"job_group_id":29},{"job_id":10597,"job_group_id":29},{"job_id":11870,"job_group_id":29},{"job_id":11964,"job_group_id":29},{"job_id":10769,"job_group_id":29},{"job_id":11001,"job_group_id":29},{"job_id":11962,"job_group_id":29},{"job_id":11726,"job_group_id":12},{"job_id":10208,"job_group_id":12},{"job_id":10510,"job_group_id":12},{"job_id":11275,"job_group_id":12},{"job_id":11731,"job_group_id":12},{"job_id":11932,"job_group_id":12},{"job_id":11601,"job_group_id":12},{"job_id":11600,"job_group_id":12},{"job_id":11898,"job_group_id":27},{"job_id":11980,"job_group_id":27},{"job_id":11111,"job_group_id":27},{"job_id":11903,"job_group_id":27},{"job_id":11422,"job_group_id":27},{"job_id":11982,"job_group_id":27},{"job_id":11558,"job_group_id":27},{"job_id":11559,"job_group_id":27},{"job_id":11560,"job_group_id":27},{"job_id":11595,"job_group_id":27},{"job_id":10647,"job_group_id":27},{"job_id":11413,"job_group_id":27},{"job_id":11926,"job_group_id":27},{"job_id":11924,"job_group_id":27},{"job_id":11945,"job_group_id":27},{"job_id":10310,"job_group_id":27},{"job_id":10271,"job_group_id":27},{"job_id":11984,"job_group_id":27},{"job_id":11700,"job_group_id":27},{"job_id":11737,"job_group_id":27},{"job_id":11736,"job_group_id":27},{"job_id":11740,"job_group_id":27},{"job_id":11743,"job_group_id":27},{"job_id":11741,"job_group_id":27},{"job_id":11936,"job_group_id":27},{"job_id":11752,"job_group_id":27},{"job_id":11754,"job_group_id":27},{"job_id":11855,"job_group_id":27},{"job_id":11856,"job_group_id":27},{"job_id":11757,"job_group_id":27},{"job_id":11758,"job_group_id":27},{"job_id":11760,"job_group_id":27},{"job_id":11877,"job_group_id":27},{"job_id":11818,"job_group_id":27},{"job_id":11819,"job_group_id":27},{"job_id":11789,"job_group_id":27},{"job_id":10175,"job_group_id":27},{"job_id":11343,"job_group_id":27},{"job_id":10255,"job_group_id":27},{"job_id":11907,"job_group_id":27},{"job_id":11909,"job_group_id":27},{"job_id":11910,"job_group_id":27},{"job_id":11938,"job_group_id":27},{"job_id":11939,"job_group_id":27},{"job_id":11959,"job_group_id":27},{"job_id":11958,"job_group_id":27},{"job_id":11949,"job_group_id":27},{"job_id":11950,"job_group_id":27},{"job_id":11957,"job_group_id":27},{"job_id":11989,"job_group_id":27},{"job_id":11967,"job_group_id":27},{"job_id":11968,"job_group_id":27},{"job_id":11354,"job_group_id":27},{"job_id":10175,"job_group_id":27},{"job_id":11827,"job_group_id":27},{"job_id":11343,"job_group_id":27},{"job_id":11834,"job_group_id":27},{"job_id":11604,"job_group_id":27},{"job_id":10639,"job_group_id":27},{"job_id":11636,"job_group_id":27},{"job_id":11993,"job_group_id":27},{"job_id":11018,"job_group_id":27},{"job_id":11951,"job_group_id":27},{"job_id":11810,"job_group_id":27},{"job_id":11262,"job_group_id":27},{"job_id":11961,"job_group_id":27},{"job_id":11036,"job_group_id":27},{"job_id":10113,"job_group_id":27},{"job_id":10112,"job_group_id":27},{"job_id":11646,"job_group_id":27},{"job_id":11670,"job_group_id":27},{"job_id":10112,"job_group_id":27},{"job_id":11092,"job_group_id":27},{"job_id":11935,"job_group_id":27},{"job_id":11859,"job_group_id":27},{"job_id":10902,"job_group_id":27},{"job_id":11046,"job_group_id":27},{"job_id":10247,"job_group_id":27},{"job_id":10469,"job_group_id":27},{"job_id":10266,"job_group_id":27},{"job_id":10725,"job_group_id":27},{"job_id":10895,"job_group_id":27},{"job_id":10891,"job_group_id":27},{"job_id":11273,"job_group_id":27},{"job_id":11666,"job_group_id":27},{"job_id":11807,"job_group_id":27},{"job_id":11913,"job_group_id":27},{"job_id":11917,"job_group_id":27},{"job_id":10895,"job_group_id":27},{"job_id":10895,"job_group_id":27},{"job_id":10892,"job_group_id":27},{"job_id":10891,"job_group_id":27},{"job_id":11464,"job_group_id":27},{"job_id":10467,"job_group_id":27},{"job_id":11720,"job_group_id":27},{"job_id":10325,"job_group_id":27},{"job_id":11866,"job_group_id":27},{"job_id":10016,"job_group_id":27},{"job_id":10339,"job_group_id":27},{"job_id":10017,"job_group_id":27},{"job_id":11404,"job_group_id":27},{"job_id":11411,"job_group_id":27},{"job_id":10872,"job_group_id":27},{"job_id":11514,"job_group_id":27},{"job_id":11624,"job_group_id":27},{"job_id":11701,"job_group_id":27},{"job_id":11990,"job_group_id":27},{"job_id":11991,"job_group_id":27},{"job_id":11796,"job_group_id":27},{"job_id":11668,"job_group_id":27},{"job_id":11914,"job_group_id":27},{"job_id":11435,"job_group_id":3},{"job_id":11981,"job_group_id":3},{"job_id":11960,"job_group_id":3},{"job_id":11696,"job_group_id":3},{"job_id":10303,"job_group_id":3},{"job_id":11911,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11347,"job_group_id":3},{"job_id":10614,"job_group_id":3},{"job_id":11434,"job_group_id":3},{"job_id":11433,"job_group_id":3},{"job_id":1,"job_group_id":3},{"job_id":11347,"job_group_id":3},{"job_id":10614,"job_group_id":3},{"job_id":10916,"job_group_id":3},{"job_id":10914,"job_group_id":3},{"job_id":10918,"job_group_id":3},{"job_id":10197,"job_group_id":3},{"job_id":11400,"job_group_id":3},{"job_id":11960,"job_group_id":3},{"job_id":10535,"job_group_id":3},{"job_id":11669,"job_group_id":3},{"job_id":11915,"job_group_id":3},{"job_id":11916,"job_group_id":3},{"job_id":10260,"job_group_id":3},{"job_id":10868,"job_group_id":3},{"job_id":10657,"job_group_id":13},{"job_id":11164,"job_group_id":13},{"job_id":11710,"job_group_id":13},{"job_id":11974,"job_group_id":13},{"job_id":11940,"job_group_id":13},{"job_id":11817,"job_group_id":15},{"job_id":11552,"job_group_id":15},{"job_id":11597,"job_group_id":15},{"job_id":11682,"job_group_id":15},{"job_id":11504,"job_group_id":15},{"job_id":11685,"job_group_id":15},{"job_id":11980,"job_group_id":15},{"job_id":11697,"job_group_id":15},{"job_id":10188,"job_group_id":15},{"job_id":11251,"job_group_id":15},{"job_id":11385,"job_group_id":15},{"job_id":11707,"job_group_id":15},{"job_id":10188,"job_group_id":15},{"job_id":11707,"job_group_id":15},{"job_id":11251,"job_group_id":15},{"job_id":11385,"job_group_id":15},{"job_id":11592,"job_group_id":15},{"job_id":11593,"job_group_id":15},{"job_id":10626,"job_group_id":15},{"job_id":11253,"job_group_id":15},{"job_id":11681,"job_group_id":15},{"job_id":11699,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":11067,"job_group_id":15},{"job_id":11504,"job_group_id":15},{"job_id":11851,"job_group_id":15},{"job_id":11254,"job_group_id":15},{"job_id":11255,"job_group_id":15},{"job_id":11305,"job_group_id":15},{"job_id":11481,"job_group_id":15},{"job_id":10949,"job_group_id":15},{"job_id":10663,"job_group_id":32},{"job_id":11995,"job_group_id":32},{"job_id":11034,"job_group_id":32},{"job_id":11293,"job_group_id":32},{"job_id":11235,"job_group_id":32},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":10235,"job_group_id":16},{"job_id":10204,"job_group_id":16},{"job_id":11372,"job_group_id":16},{"job_id":11606,"job_group_id":16},{"job_id":11610,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11324,"job_group_id":16},{"job_id":11323,"job_group_id":16},{"job_id":11609,"job_group_id":16},{"job_id":11979,"job_group_id":16},{"job_id":11996,"job_group_id":16},{"job_id":11985,"job_group_id":16},{"job_id":10546,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11894,"job_group_id":16},{"job_id":11895,"job_group_id":16},{"job_id":11920,"job_group_id":16},{"job_id":11921,"job_group_id":16},{"job_id":11922,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11073,"job_group_id":16},{"job_id":10118,"job_group_id":16},{"job_id":10235,"job_group_id":16},{"job_id":10204,"job_group_id":16},{"job_id":11372,"job_group_id":16},{"job_id":10288,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":10465,"job_group_id":16},{"job_id":11868,"job_group_id":16},{"job_id":11978,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11327,"job_group_id":16},{"job_id":11323,"job_group_id":16},{"job_id":11336,"job_group_id":16},{"job_id":11607,"job_group_id":16},{"job_id":11105,"job_group_id":11},{"job_id":11106,"job_group_id":11},{"job_id":11728,"job_group_id":11},{"job_id":11843,"job_group_id":11},{"job_id":11927,"job_group_id":11},{"job_id":11503,"job_group_id":1},{"job_id":11376,"job_group_id":1},{"job_id":11377,"job_group_id":1},{"job_id":10984,"job_group_id":1},{"job_id":10986,"job_group_id":1},{"job_id":11617,"job_group_id":1},{"job_id":10984,"job_group_id":1},{"job_id":11618,"job_group_id":1},{"job_id":11956,"job_group_id":1},{"job_id":11274,"job_group_id":1},{"job_id":11390,"job_group_id":1},{"job_id":10153,"job_group_id":1},{"job_id":11629,"job_group_id":1},{"job_id":11630,"job_group_id":1},{"job_id":11631,"job_group_id":1},{"job_id":11440,"job_group_id":1},{"job_id":10364,"job_group_id":1},{"job_id":11973,"job_group_id":1},{"job_id":11972,"job_group_id":1},{"job_id":11363,"job_group_id":1},{"job_id":10079,"job_group_id":1},{"job_id":10036,"job_group_id":1},{"job_id":11366,"job_group_id":1},{"job_id":11374,"job_group_id":1},{"job_id":11438,"job_group_id":1},{"job_id":10232,"job_group_id":1},{"job_id":10034,"job_group_id":1},{"job_id":11375,"job_group_id":1},{"job_id":11392,"job_group_id":1},{"job_id":11393,"job_group_id":1},{"job_id":10364,"job_group_id":1},{"job_id":10434,"job_group_id":1},{"job_id":10397,"job_group_id":1},{"job_id":11615,"job_group_id":1},{"job_id":11874,"job_group_id":1},{"job_id":11713,"job_group_id":1},{"job_id":11852,"job_group_id":30},{"job_id":10333,"job_group_id":30},{"job_id":10388,"job_group_id":30},{"job_id":11373,"job_group_id":31},{"job_id":11709,"job_group_id":31},{"job_id":11969,"job_group_id":31},{"job_id":11970,"job_group_id":31},{"job_id":10426,"job_group_id":10},{"job_id":10389,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":11899,"job_group_id":10},{"job_id":11900,"job_group_id":10},{"job_id":11901,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":11965,"job_group_id":10},{"job_id":10426,"job_group_id":10},{"job_id":10178,"job_group_id":10},{"job_id":11928,"job_group_id":10},{"job_id":11869,"job_group_id":10},{"job_id":10749,"job_group_id":28},{"job_id":11933,"job_group_id":28},{"job_id":11952,"job_group_id":28},{"job_id":11986,"job_group_id":28},{"job_id":11992,"job_group_id":28}]',
                    '[{"job_id":11831,"job_title":82},{"job_id":10592,"job_title":904},{"job_id":11353,"job_title":1176},{"job_id":11397,"job_title":1219},{"job_id":11460,"job_title":1276},{"job_id":10792,"job_title":778},{"job_id":11730,"job_title":779},{"job_id":11350,"job_title":1173},{"job_id":11395,"job_title":1731},{"job_id":11994,"job_title":1789},{"job_id":11998,"job_title":1793},{"job_id":10354,"job_title":494},{"job_id":10355,"job_title":495},{"job_id":10356,"job_title":496},{"job_id":11407,"job_title":1228},{"job_id":11971,"job_title":1768},{"job_id":11890,"job_title":1683},{"job_id":11889,"job_title":1684},{"job_id":11888,"job_title":1685},{"job_id":10284,"job_title":564},{"job_id":10184,"job_title":569},{"job_id":11802,"job_title":1610},{"job_id":10466,"job_title":1070},{"job_id":11987,"job_title":1781},{"job_id":11988,"job_title":1782},{"job_id":10063,"job_title":73},{"job_id":10244,"job_title":259},{"job_id":10230,"job_title":462},{"job_id":10165,"job_title":465},{"job_id":10665,"job_title":731},{"job_id":11997,"job_title":1791},{"job_id":10063,"job_title":73},{"job_id":10244,"job_title":259},{"job_id":10165,"job_title":465},{"job_id":10665,"job_title":731},{"job_id":11983,"job_title":1736},{"job_id":10063,"job_title":73},{"job_id":10244,"job_title":259},{"job_id":10165,"job_title":465},{"job_id":10137,"job_title":469},{"job_id":10665,"job_title":731},{"job_id":10165,"job_title":465},{"job_id":10137,"job_title":469},{"job_id":10665,"job_title":731},{"job_id":11318,"job_title":772},{"job_id":10597,"job_title":908},{"job_id":11870,"job_title":1664},{"job_id":11964,"job_title":1763},{"job_id":10769,"job_title":771},{"job_id":11001,"job_title":946},{"job_id":11962,"job_title":1761},{"job_id":11726,"job_title":1538},{"job_id":10208,"job_title":287},{"job_id":10510,"job_title":288},{"job_id":11275,"job_title":619},{"job_id":11731,"job_title":647},{"job_id":11932,"job_title":1720},{"job_id":11601,"job_title":287},{"job_id":11600,"job_title":288},{"job_id":11898,"job_title":263},{"job_id":11980,"job_title":1792},{"job_id":11111,"job_title":1676},{"job_id":11903,"job_title":1692},{"job_id":11422,"job_title":1256},{"job_id":11982,"job_title":1752},{"job_id":11558,"job_title":1376},{"job_id":11559,"job_title":1377},{"job_id":11560,"job_title":1378},{"job_id":11595,"job_title":1412},{"job_id":10647,"job_title":491},{"job_id":11413,"job_title":1234},{"job_id":11926,"job_title":1716},{"job_id":11924,"job_title":1727},{"job_id":11945,"job_title":1737},{"job_id":10310,"job_title":132},{"job_id":10271,"job_title":1523},{"job_id":11984,"job_title":1779},{"job_id":11700,"job_title":1516},{"job_id":11737,"job_title":1552},{"job_id":11736,"job_title":1553},{"job_id":11740,"job_title":1557},{"job_id":11743,"job_title":354},{"job_id":11741,"job_title":1558},{"job_id":11936,"job_title":1724},{"job_id":11752,"job_title":1569},{"job_id":11754,"job_title":1571},{"job_id":11855,"job_title":1654},{"job_id":11856,"job_title":1655},{"job_id":11757,"job_title":1574},{"job_id":11758,"job_title":1575},{"job_id":11760,"job_title":788},{"job_id":11877,"job_title":1672},{"job_id":11818,"job_title":1625},{"job_id":11819,"job_title":1626},{"job_id":11789,"job_title":1600},{"job_id":10175,"job_title":158},{"job_id":11343,"job_title":836},{"job_id":10255,"job_title":1261},{"job_id":11907,"job_title":1697},{"job_id":11909,"job_title":1699},{"job_id":11910,"job_title":1700},{"job_id":11938,"job_title":691},{"job_id":11939,"job_title":1726},{"job_id":11959,"job_title":1758},{"job_id":11958,"job_title":1739},{"job_id":11949,"job_title":1741},{"job_id":11950,"job_title":1742},{"job_id":11957,"job_title":1757},{"job_id":11989,"job_title":1783},{"job_id":11967,"job_title":1765},{"job_id":11968,"job_title":1766},{"job_id":11354,"job_title":1039},{"job_id":10175,"job_title":158},{"job_id":11827,"job_title":434},{"job_id":11343,"job_title":836},{"job_id":11834,"job_title":1203},{"job_id":11604,"job_title":1417},{"job_id":10639,"job_title":264},{"job_id":11636,"job_title":1451},{"job_id":11993,"job_title":1788},{"job_id":11018,"job_title":799},{"job_id":11951,"job_title":1743},{"job_id":11810,"job_title":1617},{"job_id":11262,"job_title":1096},{"job_id":11961,"job_title":1760},{"job_id":11036,"job_title":16},{"job_id":10113,"job_title":79},{"job_id":10112,"job_title":269},{"job_id":11646,"job_title":1461},{"job_id":11670,"job_title":1485},{"job_id":10112,"job_title":1491},{"job_id":11092,"job_title":999},{"job_id":11935,"job_title":1723},{"job_id":11859,"job_title":1657},{"job_id":10902,"job_title":50},{"job_id":11046,"job_title":272},{"job_id":10247,"job_title":566},{"job_id":10469,"job_title":567},{"job_id":10266,"job_title":576},{"job_id":10725,"job_title":578},{"job_id":10895,"job_title":807},{"job_id":10891,"job_title":808},{"job_id":11273,"job_title":1111},{"job_id":11666,"job_title":1481},{"job_id":11807,"job_title":1614},{"job_id":11913,"job_title":1703},{"job_id":11917,"job_title":1707},{"job_id":10895,"job_title":1718},{"job_id":10895,"job_title":1719},{"job_id":10892,"job_title":576},{"job_id":10891,"job_title":808},{"job_id":11464,"job_title":1280},{"job_id":10467,"job_title":561},{"job_id":11720,"job_title":1532},{"job_id":10325,"job_title":527},{"job_id":11866,"job_title":1660},{"job_id":10016,"job_title":324},{"job_id":10339,"job_title":388},{"job_id":10017,"job_title":389},{"job_id":11404,"job_title":519},{"job_id":11411,"job_title":1232},{"job_id":10872,"job_title":675},{"job_id":11514,"job_title":1327},{"job_id":11624,"job_title":141},{"job_id":11701,"job_title":1008},{"job_id":11990,"job_title":1784},{"job_id":11991,"job_title":1785},{"job_id":11796,"job_title":160},{"job_id":11668,"job_title":1483},{"job_id":11914,"job_title":1704},{"job_id":11435,"job_title":1242},{"job_id":11981,"job_title":1734},{"job_id":11960,"job_title":1759},{"job_id":11696,"job_title":410},{"job_id":10303,"job_title":474},{"job_id":11911,"job_title":1701},{"job_id":1,"job_title":300},{"job_id":1,"job_title":300},{"job_id":11347,"job_title":1172},{"job_id":10614,"job_title":1295},{"job_id":11434,"job_title":1244},{"job_id":11433,"job_title":1245},{"job_id":1,"job_title":300},{"job_id":11347,"job_title":1172},{"job_id":10614,"job_title":1295},{"job_id":10916,"job_title":13},{"job_id":10914,"job_title":37},{"job_id":10918,"job_title":110},{"job_id":10197,"job_title":451},{"job_id":11400,"job_title":1199},{"job_id":11960,"job_title":1759},{"job_id":10535,"job_title":572},{"job_id":11669,"job_title":1484},{"job_id":11915,"job_title":1705},{"job_id":11916,"job_title":1706},{"job_id":10260,"job_title":473},{"job_id":10868,"job_title":812},{"job_id":10657,"job_title":684},{"job_id":11164,"job_title":1035},{"job_id":11710,"job_title":1525},{"job_id":11974,"job_title":1771},{"job_id":11940,"job_title":1728},{"job_id":11817,"job_title":1624},{"job_id":11552,"job_title":1366},{"job_id":11597,"job_title":1414},{"job_id":11682,"job_title":1496},{"job_id":11504,"job_title":1319},{"job_id":11685,"job_title":1501},{"job_id":11980,"job_title":170},{"job_id":11697,"job_title":1513},{"job_id":10188,"job_title":240},{"job_id":11251,"job_title":1092},{"job_id":11385,"job_title":1208},{"job_id":11707,"job_title":1520},{"job_id":10188,"job_title":1774},{"job_id":11707,"job_title":1775},{"job_id":11251,"job_title":1776},{"job_id":11385,"job_title":1777},{"job_id":11592,"job_title":1410},{"job_id":11593,"job_title":1411},{"job_id":10626,"job_title":869},{"job_id":11253,"job_title":1084},{"job_id":11681,"job_title":1497},{"job_id":11699,"job_title":1515},{"job_id":10171,"job_title":78},{"job_id":10635,"job_title":92},{"job_id":11067,"job_title":978},{"job_id":11504,"job_title":1319},{"job_id":11851,"job_title":1653},{"job_id":11254,"job_title":1085},{"job_id":11255,"job_title":1086},{"job_id":11305,"job_title":1144},{"job_id":11481,"job_title":1297},{"job_id":10949,"job_title":890},{"job_id":10663,"job_title":729},{"job_id":11995,"job_title":1790},{"job_id":11034,"job_title":56},{"job_id":11293,"job_title":1132},{"job_id":11235,"job_title":1069},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":10235,"job_title":115},{"job_id":10204,"job_title":334},{"job_id":11372,"job_title":1324},{"job_id":11606,"job_title":1419},{"job_id":11610,"job_title":1424},{"job_id":11336,"job_title":1165},{"job_id":11324,"job_title":1025},{"job_id":11323,"job_title":1154},{"job_id":11609,"job_title":1423},{"job_id":11979,"job_title":1735},{"job_id":11996,"job_title":1755},{"job_id":11985,"job_title":1780},{"job_id":10546,"job_title":520},{"job_id":11336,"job_title":1165},{"job_id":11894,"job_title":191},{"job_id":11895,"job_title":1285},{"job_id":11920,"job_title":1710},{"job_id":11921,"job_title":1711},{"job_id":11922,"job_title":1712},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11073,"job_title":995},{"job_id":10118,"job_title":4},{"job_id":10235,"job_title":115},{"job_id":10204,"job_title":334},{"job_id":11372,"job_title":1324},{"job_id":10288,"job_title":1103},{"job_id":11336,"job_title":1165},{"job_id":10465,"job_title":568},{"job_id":11868,"job_title":1662},{"job_id":11978,"job_title":1778},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11336,"job_title":1165},{"job_id":11327,"job_title":1157},{"job_id":11323,"job_title":1154},{"job_id":11336,"job_title":1165},{"job_id":11607,"job_title":1420},{"job_id":11105,"job_title":413},{"job_id":11106,"job_title":414},{"job_id":11728,"job_title":1545},{"job_id":11843,"job_title":1644},{"job_id":11927,"job_title":1717},{"job_id":11503,"job_title":1184},{"job_id":11376,"job_title":1197},{"job_id":11377,"job_title":1198},{"job_id":10984,"job_title":3},{"job_id":10986,"job_title":190},{"job_id":11617,"job_title":797},{"job_id":10984,"job_title":3},{"job_id":11618,"job_title":1432},{"job_id":11956,"job_title":1756},{"job_id":11274,"job_title":1112},{"job_id":11390,"job_title":1212},{"job_id":10153,"job_title":1019},{"job_id":11629,"job_title":1443},{"job_id":11630,"job_title":1444},{"job_id":11631,"job_title":1445},{"job_id":11440,"job_title":96},{"job_id":10364,"job_title":194},{"job_id":11973,"job_title":1769},{"job_id":11972,"job_title":1770},{"job_id":11363,"job_title":1185},{"job_id":10079,"job_title":2},{"job_id":10036,"job_title":193},{"job_id":11366,"job_title":1188},{"job_id":11374,"job_title":1195},{"job_id":11438,"job_title":1267},{"job_id":10232,"job_title":1},{"job_id":10034,"job_title":195},{"job_id":11375,"job_title":1196},{"job_id":11392,"job_title":1214},{"job_id":11393,"job_title":197},{"job_id":10364,"job_title":194},{"job_id":10434,"job_title":586},{"job_id":10397,"job_title":883},{"job_id":11615,"job_title":1430},{"job_id":11874,"job_title":1669},{"job_id":11713,"job_title":1107},{"job_id":11852,"job_title":1007},{"job_id":10333,"job_title":486},{"job_id":10388,"job_title":632},{"job_id":11373,"job_title":1194},{"job_id":11709,"job_title":1524},{"job_id":11969,"job_title":1546},{"job_id":11970,"job_title":1767},{"job_id":10426,"job_title":33},{"job_id":10389,"job_title":34},{"job_id":10445,"job_title":344},{"job_id":11013,"job_title":962},{"job_id":11899,"job_title":1689},{"job_id":11900,"job_title":1690},{"job_id":11901,"job_title":1691},{"job_id":11013,"job_title":1358},{"job_id":10445,"job_title":1359},{"job_id":11965,"job_title":1360},{"job_id":10426,"job_title":1361},{"job_id":10178,"job_title":220},{"job_id":11928,"job_title":661},{"job_id":11869,"job_title":1663},{"job_id":10749,"job_title":927},{"job_id":11933,"job_title":1323},{"job_id":11952,"job_title":1744},{"job_id":11986,"job_title":1786},{"job_id":11992,"job_title":1787}]',
                ];
                break;
            case 'la':
                $data = [
                    '[{"job_title":344,"department_id":10020,"group_id":10},{"job_title":23,"department_id":999,"group_id":24},{"job_title":190,"department_id":10024,"group_id":1},{"job_title":797,"department_id":10024,"group_id":1},{"job_title":1146,"department_id":10024,"group_id":1},{"job_title":130,"department_id":10027,"group_id":1},{"job_title":477,"department_id":10027,"group_id":1},{"job_title":1141,"department_id":10027,"group_id":1},{"job_title":1143,"department_id":10027,"group_id":1},{"job_title":1151,"department_id":10027,"group_id":1},{"job_title":197,"department_id":10030,"group_id":1},{"job_title":1019,"department_id":10030,"group_id":1},{"job_title":1145,"department_id":10030,"group_id":1},{"job_title":1147,"department_id":10030,"group_id":1},{"job_title":1158,"department_id":10031,"group_id":1},{"job_title":1159,"department_id":10031,"group_id":1},{"job_title":1160,"department_id":10031,"group_id":1},{"job_title":1161,"department_id":10031,"group_id":1},{"job_title":96,"department_id":10039,"group_id":1},{"job_title":194,"department_id":10039,"group_id":1},{"job_title":883,"department_id":10039,"group_id":1},{"job_title":1157,"department_id":10039,"group_id":1},{"job_title":1,"department_id":253,"group_id":1},{"job_title":2,"department_id":253,"group_id":1},{"job_title":1019,"department_id":253,"group_id":1},{"job_title":253,"department_id":10042,"group_id":11},{"job_title":414,"department_id":10042,"group_id":11},{"job_title":1170,"department_id":10042,"group_id":11},{"job_title":115,"department_id":10036,"group_id":16},{"job_title":334,"department_id":10036,"group_id":16},{"job_title":617,"department_id":10036,"group_id":16},{"job_title":1148,"department_id":10036,"group_id":16},{"job_title":1167,"department_id":10036,"group_id":16},{"job_title":620,"department_id":10040,"group_id":16},{"job_title":1025,"department_id":10040,"group_id":16},{"job_title":1164,"department_id":10040,"group_id":16},{"job_title":1174,"department_id":235,"group_id":16},{"job_title":92,"department_id":10038,"group_id":15},{"job_title":1162,"department_id":10038,"group_id":15},{"job_title":1163,"department_id":10038,"group_id":15},{"job_title":153,"department_id":235,"group_id":15},{"job_title":335,"department_id":235,"group_id":15},{"job_title":869,"department_id":235,"group_id":15},{"job_title":978,"department_id":235,"group_id":15},{"job_title":1204,"department_id":235,"group_id":15},{"job_title":37,"department_id":10019,"group_id":3},{"job_title":1128,"department_id":10021,"group_id":3},{"job_title":1129,"department_id":10021,"group_id":3},{"job_title":1187,"department_id":10063,"group_id":3},{"job_title":1184,"department_id":10064,"group_id":3},{"job_title":13,"department_id":203,"group_id":3},{"job_title":110,"department_id":203,"group_id":3},{"job_title":1126,"department_id":10018,"group_id":21},{"job_title":1116,"department_id":10019,"group_id":21},{"job_title":1169,"department_id":10019,"group_id":21},{"job_title":1127,"department_id":10021,"group_id":21},{"job_title":1198,"department_id":10041,"group_id":21},{"job_title":1199,"department_id":10041,"group_id":21},{"job_title":388,"department_id":10050,"group_id":21},{"job_title":389,"department_id":10050,"group_id":21},{"job_title":1186,"department_id":10063,"group_id":21},{"job_title":1185,"department_id":10064,"group_id":21},{"job_title":65,"department_id":164,"group_id":21},{"job_title":388,"department_id":205,"group_id":21},{"job_title":389,"department_id":205,"group_id":21},{"job_title":454,"department_id":40001,"group_id":21},{"job_title":1012,"department_id":40001,"group_id":21},{"job_title":141,"department_id":6,"group_id":21},{"job_title":556,"department_id":7,"group_id":21},{"job_title":1197,"department_id":208,"group_id":9}]',
                    '[{"job_id":10445,"job_group_id":10},{"job_id":10986,"job_group_id":1},{"job_id":11260,"job_group_id":1},{"job_id":11262,"job_group_id":1},{"job_id":11030,"job_group_id":1},{"job_id":11256,"job_group_id":1},{"job_id":11253,"job_group_id":1},{"job_id":11255,"job_group_id":1},{"job_id":11268,"job_group_id":1},{"job_id":11259,"job_group_id":1},{"job_id":10153,"job_group_id":1},{"job_id":11261,"job_group_id":1},{"job_id":11263,"job_group_id":1},{"job_id":11278,"job_group_id":1},{"job_id":11278,"job_group_id":1},{"job_id":11277,"job_group_id":1},{"job_id":11276,"job_group_id":1},{"job_id":11274,"job_group_id":1},{"job_id":10364,"job_group_id":1},{"job_id":10397,"job_group_id":1},{"job_id":11275,"job_group_id":1},{"job_id":10232,"job_group_id":1},{"job_id":10079,"job_group_id":1},{"job_id":10153,"job_group_id":1},{"job_id":11104,"job_group_id":11},{"job_id":11106,"job_group_id":11},{"job_id":11296,"job_group_id":11},{"job_id":10235,"job_group_id":16},{"job_id":10204,"job_group_id":16},{"job_id":11222,"job_group_id":16},{"job_id":11264,"job_group_id":16},{"job_id":11294,"job_group_id":16},{"job_id":10408,"job_group_id":16},{"job_id":11213,"job_group_id":16},{"job_id":11291,"job_group_id":16},{"job_id":11299,"job_group_id":16},{"job_id":10635,"job_group_id":15},{"job_id":11289,"job_group_id":15},{"job_id":11290,"job_group_id":15},{"job_id":10305,"job_group_id":15},{"job_id":10187,"job_group_id":15},{"job_id":10626,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10187,"job_group_id":15},{"job_id":7,"job_group_id":3},{"job_id":11237,"job_group_id":3},{"job_id":11236,"job_group_id":3},{"job_id":11239,"job_group_id":3},{"job_id":7,"job_group_id":3},{"job_id":10916,"job_group_id":3},{"job_id":10003,"job_group_id":3},{"job_id":11235,"job_group_id":21},{"job_id":11239,"job_group_id":21},{"job_id":11295,"job_group_id":21},{"job_id":11238,"job_group_id":21},{"job_id":10367,"job_group_id":21},{"job_id":11219,"job_group_id":21},{"job_id":10339,"job_group_id":21},{"job_id":10017,"job_group_id":21},{"job_id":11239,"job_group_id":21},{"job_id":11239,"job_group_id":21},{"job_id":10367,"job_group_id":21},{"job_id":10339,"job_group_id":21},{"job_id":10017,"job_group_id":21},{"job_id":10512,"job_group_id":21},{"job_id":11285,"job_group_id":21},{"job_id":10192,"job_group_id":21},{"job_id":10324,"job_group_id":21},{"job_id":10315,"job_group_id":9}]',
                    '[{"job_id":10445,"job_title":344},{"job_id":10986,"job_title":190},{"job_id":11260,"job_title":797},{"job_id":11262,"job_title":1146},{"job_id":11030,"job_title":130},{"job_id":11256,"job_title":477},{"job_id":11253,"job_title":1141},{"job_id":11255,"job_title":1143},{"job_id":11268,"job_title":1151},{"job_id":11259,"job_title":197},{"job_id":10153,"job_title":1019},{"job_id":11261,"job_title":1145},{"job_id":11263,"job_title":1147},{"job_id":11278,"job_title":1158},{"job_id":11278,"job_title":1159},{"job_id":11277,"job_title":1160},{"job_id":11276,"job_title":1161},{"job_id":11274,"job_title":96},{"job_id":10364,"job_title":194},{"job_id":10397,"job_title":883},{"job_id":11275,"job_title":1157},{"job_id":10232,"job_title":1},{"job_id":10079,"job_title":2},{"job_id":10153,"job_title":1019},{"job_id":11104,"job_title":253},{"job_id":11106,"job_title":414},{"job_id":11296,"job_title":1170},{"job_id":10235,"job_title":115},{"job_id":10204,"job_title":334},{"job_id":11222,"job_title":617},{"job_id":11264,"job_title":1148},{"job_id":11294,"job_title":1167},{"job_id":10408,"job_title":620},{"job_id":11213,"job_title":1025},{"job_id":11291,"job_title":1164},{"job_id":11299,"job_title":1174},{"job_id":10635,"job_title":92},{"job_id":11289,"job_title":1162},{"job_id":11290,"job_title":1163},{"job_id":10305,"job_title":153},{"job_id":10187,"job_title":335},{"job_id":10626,"job_title":869},{"job_id":10171,"job_title":978},{"job_id":10187,"job_title":1204},{"job_id":7,"job_title":37},{"job_id":11237,"job_title":1128},{"job_id":11236,"job_title":1129},{"job_id":11239,"job_title":1187},{"job_id":7,"job_title":1184},{"job_id":10916,"job_title":13},{"job_id":10003,"job_title":110},{"job_id":11235,"job_title":1126},{"job_id":11239,"job_title":1116},{"job_id":11295,"job_title":1169},{"job_id":11238,"job_title":1127},{"job_id":10367,"job_title":1198},{"job_id":11219,"job_title":1199},{"job_id":10339,"job_title":388},{"job_id":10017,"job_title":389},{"job_id":11239,"job_title":1186},{"job_id":11239,"job_title":1185},{"job_id":10367,"job_title":65},{"job_id":10339,"job_title":388},{"job_id":10017,"job_title":389},{"job_id":10512,"job_title":454},{"job_id":11285,"job_title":1012},{"job_id":10192,"job_title":141},{"job_id":10324,"job_title":556},{"job_id":10315,"job_title":1197}]',
                ];
                break;
            case 'vn':
                $data = [
                    '[{"job_title":33,"department_id":267,"group_id":10},{"job_title":344,"department_id":267,"group_id":10},{"job_title":190,"department_id":20012,"group_id":1},{"job_title":1328,"department_id":20017,"group_id":1},{"job_title":2,"department_id":20026,"group_id":1},{"job_title":1019,"department_id":229,"group_id":1},{"job_title":1009,"department_id":333,"group_id":1},{"job_title":25,"department_id":20035,"group_id":16},{"job_title":1157,"department_id":20041,"group_id":16},{"job_title":25,"department_id":20047,"group_id":16},{"job_title":1280,"department_id":264,"group_id":16},{"job_title":42,"department_id":20078,"group_id":20},{"job_title":1330,"department_id":20041,"group_id":15},{"job_title":1369,"department_id":20071,"group_id":15},{"job_title":981,"department_id":70002,"group_id":15},{"job_title":1332,"department_id":70002,"group_id":15},{"job_title":300,"department_id":20036,"group_id":3},{"job_title":917,"department_id":20036,"group_id":3},{"job_title":1253,"department_id":20036,"group_id":3},{"job_title":300,"department_id":20037,"group_id":3},{"job_title":917,"department_id":20037,"group_id":3},{"job_title":1253,"department_id":20037,"group_id":3},{"job_title":300,"department_id":20038,"group_id":3},{"job_title":917,"department_id":20038,"group_id":3},{"job_title":1253,"department_id":20038,"group_id":3},{"job_title":300,"department_id":20039,"group_id":3},{"job_title":917,"department_id":20039,"group_id":3},{"job_title":1253,"department_id":20039,"group_id":3},{"job_title":300,"department_id":20048,"group_id":3},{"job_title":917,"department_id":20048,"group_id":3},{"job_title":1253,"department_id":20048,"group_id":3},{"job_title":300,"department_id":20049,"group_id":3},{"job_title":917,"department_id":20049,"group_id":3},{"job_title":1253,"department_id":20049,"group_id":3},{"job_title":300,"department_id":20050,"group_id":3},{"job_title":917,"department_id":20050,"group_id":3},{"job_title":1253,"department_id":20050,"group_id":3},{"job_title":300,"department_id":20051,"group_id":3},{"job_title":917,"department_id":20051,"group_id":3},{"job_title":1253,"department_id":20051,"group_id":3},{"job_title":1344,"department_id":20072,"group_id":3},{"job_title":1345,"department_id":20072,"group_id":3},{"job_title":1244,"department_id":20078,"group_id":3},{"job_title":1270,"department_id":20078,"group_id":3},{"job_title":1271,"department_id":20078,"group_id":3},{"job_title":263,"department_id":20000,"group_id":21},{"job_title":726,"department_id":20000,"group_id":21},{"job_title":1371,"department_id":20004,"group_id":21},{"job_title":916,"department_id":20035,"group_id":21},{"job_title":158,"department_id":20037,"group_id":21},{"job_title":916,"department_id":20047,"group_id":21},{"job_title":158,"department_id":20050,"group_id":21},{"job_title":1257,"department_id":20071,"group_id":21},{"job_title":1264,"department_id":20072,"group_id":21},{"job_title":1342,"department_id":20078,"group_id":21},{"job_title":1005,"department_id":218,"group_id":21},{"job_title":1052,"department_id":264,"group_id":21},{"job_title":913,"department_id":265,"group_id":21},{"job_title":141,"department_id":6,"group_id":21},{"job_title":1042,"department_id":70004,"group_id":12},{"job_title":1278,"department_id":70004,"group_id":12},{"job_title":1049,"department_id":269,"group_id":23},{"job_title":1370,"department_id":269,"group_id":23},{"job_title":912,"department_id":265,"group_id":9},{"job_title":1148,"department_id":265,"group_id":9}]',
                    '[{"job_id":11487,"job_group_id":10},{"job_id":10445,"job_group_id":10},{"job_id":10986,"job_group_id":1},{"job_id":11519,"job_group_id":1},{"job_id":10079,"job_group_id":1},{"job_id":10153,"job_group_id":1},{"job_id":10333,"job_group_id":1},{"job_id":11175,"job_group_id":16},{"job_id":10289,"job_group_id":16},{"job_id":11175,"job_group_id":16},{"job_id":11467,"job_group_id":16},{"job_id":10051,"job_group_id":20},{"job_id":11524,"job_group_id":15},{"job_id":11556,"job_group_id":15},{"job_id":11061,"job_group_id":15},{"job_id":11527,"job_group_id":15},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11534,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11439,"job_group_id":3},{"job_id":11537,"job_group_id":3},{"job_id":11536,"job_group_id":3},{"job_id":11429,"job_group_id":3},{"job_id":11457,"job_group_id":3},{"job_id":11458,"job_group_id":3},{"job_id":11531,"job_group_id":21},{"job_id":10660,"job_group_id":21},{"job_id":11444,"job_group_id":21},{"job_id":11558,"job_group_id":21},{"job_id":11533,"job_group_id":21},{"job_id":11558,"job_group_id":21},{"job_id":11533,"job_group_id":21},{"job_id":11444,"job_group_id":21},{"job_id":11451,"job_group_id":21},{"job_id":11457,"job_group_id":21},{"job_id":11102,"job_group_id":21},{"job_id":11216,"job_group_id":21},{"job_id":10609,"job_group_id":21},{"job_id":10506,"job_group_id":21},{"job_id":11180,"job_group_id":12},{"job_id":11465,"job_group_id":12},{"job_id":10774,"job_group_id":23},{"job_id":11559,"job_group_id":23},{"job_id":10608,"job_group_id":9},{"job_id":11312,"job_group_id":9}]',
                    '[{"job_id":11487,"job_title":33},{"job_id":10445,"job_title":344},{"job_id":10986,"job_title":190},{"job_id":11519,"job_title":1328},{"job_id":10079,"job_title":2},{"job_id":10153,"job_title":1019},{"job_id":10333,"job_title":1009},{"job_id":11175,"job_title":25},{"job_id":10289,"job_title":1157},{"job_id":11175,"job_title":25},{"job_id":11467,"job_title":1280},{"job_id":10051,"job_title":42},{"job_id":11524,"job_title":1330},{"job_id":11556,"job_title":1369},{"job_id":11061,"job_title":981},{"job_id":11527,"job_title":1332},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11534,"job_title":300},{"job_id":11555,"job_title":917},{"job_id":11439,"job_title":1253},{"job_id":11537,"job_title":1344},{"job_id":11536,"job_title":1345},{"job_id":11429,"job_title":1244},{"job_id":11457,"job_title":1270},{"job_id":11458,"job_title":1271},{"job_id":11531,"job_title":263},{"job_id":10660,"job_title":726},{"job_id":11444,"job_title":1371},{"job_id":11558,"job_title":916},{"job_id":11533,"job_title":158},{"job_id":11558,"job_title":916},{"job_id":11533,"job_title":158},{"job_id":11444,"job_title":1257},{"job_id":11451,"job_title":1264},{"job_id":11457,"job_title":1342},{"job_id":11102,"job_title":1005},{"job_id":11216,"job_title":1052},{"job_id":10609,"job_title":913},{"job_id":10506,"job_title":141},{"job_id":11180,"job_title":1042},{"job_id":11465,"job_title":1278},{"job_id":10774,"job_title":1049},{"job_id":11559,"job_title":1370},{"job_id":10608,"job_title":912},{"job_id":11312,"job_title":1148}]',
                ];
                break;
            case 'id':
                $data = [
                    '[{"job_title":1421,"department_id":29,"group_id":24},{"job_title":1424,"department_id":70002,"group_id":24},{"job_title":1481,"department_id":70002,"group_id":24},{"job_title":1482,"department_id":70003,"group_id":24},{"job_title":1483,"department_id":70003,"group_id":24},{"job_title":1484,"department_id":70003,"group_id":24},{"job_title":1487,"department_id":70003,"group_id":24},{"job_title":1421,"department_id":95,"group_id":24},{"job_title":962,"department_id":447,"group_id":10},{"job_title":1477,"department_id":447,"group_id":10},{"job_title":1479,"department_id":447,"group_id":10},{"job_title":1007,"department_id":1,"group_id":26},{"job_title":1037,"department_id":21,"group_id":26},{"job_title":1007,"department_id":222,"group_id":26},{"job_title":632,"department_id":289,"group_id":26},{"job_title":486,"department_id":333,"group_id":26},{"job_title":632,"department_id":444,"group_id":26},{"job_title":23,"department_id":999,"group_id":26},{"job_title":1369,"department_id":14,"group_id":1},{"job_title":3,"department_id":15,"group_id":1},{"job_title":190,"department_id":15,"group_id":1},{"job_title":797,"department_id":15,"group_id":1},{"job_title":1370,"department_id":15,"group_id":1},{"job_title":3,"department_id":16,"group_id":1},{"job_title":190,"department_id":16,"group_id":1},{"job_title":797,"department_id":16,"group_id":1},{"job_title":1370,"department_id":16,"group_id":1},{"job_title":1371,"department_id":17,"group_id":1},{"job_title":1372,"department_id":17,"group_id":1},{"job_title":1373,"department_id":17,"group_id":1},{"job_title":1374,"department_id":17,"group_id":1},{"job_title":1375,"department_id":17,"group_id":1},{"job_title":1377,"department_id":19,"group_id":1},{"job_title":1378,"department_id":19,"group_id":1},{"job_title":1379,"department_id":19,"group_id":1},{"job_title":1380,"department_id":19,"group_id":1},{"job_title":1381,"department_id":19,"group_id":1},{"job_title":1267,"department_id":2,"group_id":1},{"job_title":1112,"department_id":20,"group_id":1},{"job_title":1382,"department_id":20,"group_id":1},{"job_title":1383,"department_id":20,"group_id":1},{"job_title":1384,"department_id":20,"group_id":1},{"job_title":588,"department_id":22,"group_id":1},{"job_title":1019,"department_id":22,"group_id":1},{"job_title":1266,"department_id":22,"group_id":1},{"job_title":1385,"department_id":22,"group_id":1},{"job_title":1464,"department_id":333,"group_id":1},{"job_title":96,"department_id":46,"group_id":1},{"job_title":194,"department_id":46,"group_id":1},{"job_title":883,"department_id":46,"group_id":1},{"job_title":1386,"department_id":46,"group_id":1},{"job_title":1272,"department_id":61,"group_id":1},{"job_title":1019,"department_id":63,"group_id":1},{"job_title":1466,"department_id":98,"group_id":1},{"job_title":253,"department_id":21,"group_id":11},{"job_title":414,"department_id":21,"group_id":11},{"job_title":1297,"department_id":21,"group_id":11},{"job_title":1297,"department_id":64,"group_id":11},{"job_title":1263,"department_id":7,"group_id":11},{"job_title":1103,"department_id":1,"group_id":16},{"job_title":1352,"department_id":1,"group_id":16},{"job_title":1010,"department_id":333,"group_id":16},{"job_title":478,"department_id":36,"group_id":16},{"job_title":1157,"department_id":36,"group_id":16},{"job_title":4,"department_id":421,"group_id":16},{"job_title":115,"department_id":421,"group_id":16},{"job_title":334,"department_id":421,"group_id":16},{"job_title":617,"department_id":421,"group_id":16},{"job_title":1262,"department_id":421,"group_id":16},{"job_title":115,"department_id":44,"group_id":16},{"job_title":334,"department_id":44,"group_id":16},{"job_title":617,"department_id":44,"group_id":16},{"job_title":1262,"department_id":44,"group_id":16},{"job_title":1367,"department_id":44,"group_id":16},{"job_title":1296,"department_id":460,"group_id":16},{"job_title":617,"department_id":49,"group_id":16},{"job_title":1390,"department_id":49,"group_id":16},{"job_title":1391,"department_id":49,"group_id":16},{"job_title":1497,"department_id":49,"group_id":16},{"job_title":620,"department_id":505,"group_id":16},{"job_title":1299,"department_id":505,"group_id":16},{"job_title":1300,"department_id":505,"group_id":16},{"job_title":1301,"department_id":505,"group_id":16},{"job_title":1302,"department_id":505,"group_id":16},{"job_title":1303,"department_id":505,"group_id":16},{"job_title":1304,"department_id":505,"group_id":16},{"job_title":1305,"department_id":505,"group_id":16},{"job_title":1306,"department_id":505,"group_id":16},{"job_title":1307,"department_id":505,"group_id":16},{"job_title":1308,"department_id":505,"group_id":16},{"job_title":1309,"department_id":505,"group_id":16},{"job_title":1310,"department_id":505,"group_id":16},{"job_title":1368,"department_id":55,"group_id":16},{"job_title":620,"department_id":58,"group_id":16},{"job_title":1025,"department_id":58,"group_id":16},{"job_title":1154,"department_id":58,"group_id":16},{"job_title":1368,"department_id":58,"group_id":16},{"job_title":577,"department_id":70,"group_id":16},{"job_title":1490,"department_id":70004,"group_id":16},{"job_title":4,"department_id":8,"group_id":16},{"job_title":115,"department_id":8,"group_id":16},{"job_title":652,"department_id":8,"group_id":16},{"job_title":1367,"department_id":8,"group_id":16},{"job_title":263,"department_id":97,"group_id":16},{"job_title":1475,"department_id":99,"group_id":16},{"job_title":1117,"department_id":56,"group_id":22},{"job_title":1408,"department_id":56,"group_id":22},{"job_title":42,"department_id":57,"group_id":22},{"job_title":1478,"department_id":57,"group_id":22},{"job_title":729,"department_id":66,"group_id":22},{"job_title":1450,"department_id":66,"group_id":22},{"job_title":1451,"department_id":66,"group_id":22},{"job_title":1409,"department_id":70009,"group_id":22},{"job_title":1463,"department_id":88,"group_id":22},{"job_title":1463,"department_id":89,"group_id":22},{"job_title":1463,"department_id":90,"group_id":22},{"job_title":1463,"department_id":92,"group_id":22},{"job_title":980,"department_id":23,"group_id":15},{"job_title":981,"department_id":23,"group_id":15},{"job_title":1083,"department_id":23,"group_id":15},{"job_title":1084,"department_id":23,"group_id":15},{"job_title":335,"department_id":235,"group_id":15},{"job_title":443,"department_id":235,"group_id":15},{"job_title":1347,"department_id":24,"group_id":15},{"job_title":1348,"department_id":24,"group_id":15},{"job_title":1298,"department_id":289,"group_id":15},{"job_title":1452,"department_id":289,"group_id":15},{"job_title":1459,"department_id":34,"group_id":15},{"job_title":1460,"department_id":34,"group_id":15},{"job_title":1461,"department_id":34,"group_id":15},{"job_title":1462,"department_id":34,"group_id":15},{"job_title":47,"department_id":35,"group_id":15},{"job_title":187,"department_id":35,"group_id":15},{"job_title":1156,"department_id":35,"group_id":15},{"job_title":335,"department_id":37,"group_id":15},{"job_title":1350,"department_id":37,"group_id":15},{"job_title":182,"department_id":39,"group_id":15},{"job_title":247,"department_id":39,"group_id":15},{"job_title":1485,"department_id":441,"group_id":15},{"job_title":1486,"department_id":441,"group_id":15},{"job_title":890,"department_id":444,"group_id":15},{"job_title":240,"department_id":460,"group_id":15},{"job_title":1468,"department_id":460,"group_id":15},{"job_title":170,"department_id":48,"group_id":15},{"job_title":1387,"department_id":48,"group_id":15},{"job_title":1389,"department_id":48,"group_id":15},{"job_title":78,"department_id":487,"group_id":15},{"job_title":92,"department_id":487,"group_id":15},{"job_title":978,"department_id":487,"group_id":15},{"job_title":1142,"department_id":487,"group_id":15},{"job_title":1186,"department_id":487,"group_id":15},{"job_title":1242,"department_id":487,"group_id":15},{"job_title":78,"department_id":50,"group_id":15},{"job_title":92,"department_id":50,"group_id":15},{"job_title":153,"department_id":50,"group_id":15},{"job_title":162,"department_id":50,"group_id":15},{"job_title":168,"department_id":50,"group_id":15},{"job_title":248,"department_id":50,"group_id":15},{"job_title":682,"department_id":50,"group_id":15},{"job_title":970,"department_id":50,"group_id":15},{"job_title":1142,"department_id":50,"group_id":15},{"job_title":1186,"department_id":50,"group_id":15},{"job_title":92,"department_id":51,"group_id":15},{"job_title":168,"department_id":51,"group_id":15},{"job_title":248,"department_id":51,"group_id":15},{"job_title":978,"department_id":51,"group_id":15},{"job_title":1186,"department_id":51,"group_id":15},{"job_title":1499,"department_id":70004,"group_id":15},{"job_title":1453,"department_id":71,"group_id":15},{"job_title":1454,"department_id":71,"group_id":15},{"job_title":1455,"department_id":72,"group_id":15},{"job_title":1456,"department_id":72,"group_id":15},{"job_title":1457,"department_id":73,"group_id":15},{"job_title":1458,"department_id":73,"group_id":15},{"job_title":248,"department_id":74,"group_id":15},{"job_title":78,"department_id":75,"group_id":15},{"job_title":92,"department_id":75,"group_id":15},{"job_title":978,"department_id":75,"group_id":15},{"job_title":1186,"department_id":75,"group_id":15},{"job_title":78,"department_id":76,"group_id":15},{"job_title":92,"department_id":76,"group_id":15},{"job_title":978,"department_id":76,"group_id":15},{"job_title":1186,"department_id":76,"group_id":15},{"job_title":1361,"department_id":77,"group_id":15},{"job_title":1361,"department_id":78,"group_id":15},{"job_title":1361,"department_id":79,"group_id":15},{"job_title":1361,"department_id":80,"group_id":15},{"job_title":1361,"department_id":81,"group_id":15},{"job_title":1361,"department_id":82,"group_id":15},{"job_title":1361,"department_id":83,"group_id":15},{"job_title":1361,"department_id":84,"group_id":15},{"job_title":1361,"department_id":85,"group_id":15},{"job_title":1361,"department_id":86,"group_id":15},{"job_title":240,"department_id":87,"group_id":15},{"job_title":1361,"department_id":87,"group_id":15},{"job_title":1469,"department_id":96,"group_id":15},{"job_title":170,"department_id":97,"group_id":15},{"job_title":476,"department_id":97,"group_id":15},{"job_title":532,"department_id":293,"group_id":13},{"job_title":684,"department_id":293,"group_id":13},{"job_title":1035,"department_id":293,"group_id":13},{"job_title":645,"department_id":54,"group_id":13},{"job_title":1395,"department_id":97,"group_id":13},{"job_title":1250,"department_id":41,"group_id":3},{"job_title":1274,"department_id":41,"group_id":3},{"job_title":1358,"department_id":42,"group_id":3},{"job_title":1405,"department_id":42,"group_id":3},{"job_title":1406,"department_id":42,"group_id":3},{"job_title":681,"department_id":447,"group_id":3},{"job_title":300,"department_id":480,"group_id":3},{"job_title":917,"department_id":480,"group_id":3},{"job_title":918,"department_id":480,"group_id":3},{"job_title":1265,"department_id":480,"group_id":3},{"job_title":1418,"department_id":70004,"group_id":3},{"job_title":1473,"department_id":70004,"group_id":3},{"job_title":1488,"department_id":70004,"group_id":3},{"job_title":1489,"department_id":70004,"group_id":3},{"job_title":1274,"department_id":70014,"group_id":3},{"job_title":1281,"department_id":70014,"group_id":3},{"job_title":1282,"department_id":70014,"group_id":3},{"job_title":733,"department_id":70017,"group_id":3},{"job_title":1495,"department_id":70018,"group_id":3},{"job_title":1496,"department_id":70018,"group_id":3},{"job_title":141,"department_id":1,"group_id":23},{"job_title":573,"department_id":1,"group_id":23},{"job_title":1095,"department_id":1,"group_id":23},{"job_title":1353,"department_id":1,"group_id":23},{"job_title":1275,"department_id":100,"group_id":23},{"job_title":1014,"department_id":20001,"group_id":23},{"job_title":1275,"department_id":41,"group_id":23},{"job_title":1280,"department_id":41,"group_id":23},{"job_title":1291,"department_id":42,"group_id":23},{"job_title":415,"department_id":441,"group_id":23},{"job_title":1190,"department_id":441,"group_id":23},{"job_title":953,"department_id":460,"group_id":23},{"job_title":1215,"department_id":460,"group_id":23},{"job_title":158,"department_id":480,"group_id":23},{"job_title":836,"department_id":480,"group_id":23},{"job_title":915,"department_id":480,"group_id":23},{"job_title":916,"department_id":480,"group_id":23},{"job_title":454,"department_id":50001,"group_id":23},{"job_title":726,"department_id":50001,"group_id":23},{"job_title":141,"department_id":6,"group_id":23},{"job_title":855,"department_id":60001,"group_id":23},{"job_title":1008,"department_id":60001,"group_id":23},{"job_title":575,"department_id":70,"group_id":23},{"job_title":576,"department_id":70,"group_id":23},{"job_title":1330,"department_id":70004,"group_id":23},{"job_title":1491,"department_id":70004,"group_id":23},{"job_title":1492,"department_id":70004,"group_id":23},{"job_title":1447,"department_id":70012,"group_id":23},{"job_title":1275,"department_id":70014,"group_id":23},{"job_title":1280,"department_id":70014,"group_id":23},{"job_title":1498,"department_id":70014,"group_id":23},{"job_title":1278,"department_id":70015,"group_id":23},{"job_title":65,"department_id":70017,"group_id":23},{"job_title":1470,"department_id":70017,"group_id":23},{"job_title":1494,"department_id":70018,"group_id":23},{"job_title":1443,"department_id":99,"group_id":23},{"job_title":956,"department_id":4,"group_id":12},{"job_title":1264,"department_id":4,"group_id":12},{"job_title":1273,"department_id":4,"group_id":12},{"job_title":847,"department_id":441,"group_id":12},{"job_title":1476,"department_id":447,"group_id":12},{"job_title":1041,"department_id":473,"group_id":12},{"job_title":1042,"department_id":473,"group_id":12},{"job_title":287,"department_id":5,"group_id":12},{"job_title":1011,"department_id":70001,"group_id":12},{"job_title":929,"department_id":99,"group_id":25},{"job_title":1427,"department_id":67,"group_id":4},{"job_title":1429,"department_id":67,"group_id":4},{"job_title":1430,"department_id":67,"group_id":4},{"job_title":1440,"department_id":67,"group_id":4},{"job_title":382,"department_id":480,"group_id":8}]',
                    '[{"job_id":10600,"job_group_id":8},{"job_id":11623,"job_group_id":4},{"job_id":11625,"job_group_id":4},{"job_id":11626,"job_group_id":4},{"job_id":11636,"job_group_id":4},{"job_id":11035,"job_group_id":25},{"job_id":10208,"job_group_id":12},{"job_id":11008,"job_group_id":12},{"job_id":11179,"job_group_id":12},{"job_id":11180,"job_group_id":12},{"job_id":11454,"job_group_id":12},{"job_id":11463,"job_group_id":12},{"job_id":11669,"job_group_id":12},{"job_id":11686,"job_group_id":12},{"job_id":10175,"job_group_id":23},{"job_id":10255,"job_group_id":23},{"job_id":10287,"job_group_id":23},{"job_id":10367,"job_group_id":23},{"job_id":10584,"job_group_id":23},{"job_id":10604,"job_group_id":23},{"job_id":10660,"job_group_id":23},{"job_id":10910,"job_group_id":23},{"job_id":11263,"job_group_id":23},{"job_id":11293,"job_group_id":23},{"job_id":11330,"job_group_id":23},{"job_id":11369,"job_group_id":23},{"job_id":11388,"job_group_id":23},{"job_id":11398,"job_group_id":23},{"job_id":11434,"job_group_id":23},{"job_id":11467,"job_group_id":23},{"job_id":11468,"job_group_id":23},{"job_id":11469,"job_group_id":23},{"job_id":11469,"job_group_id":23},{"job_id":11469,"job_group_id":23},{"job_id":11470,"job_group_id":23},{"job_id":11470,"job_group_id":23},{"job_id":11481,"job_group_id":23},{"job_id":11550,"job_group_id":23},{"job_id":11637,"job_group_id":23},{"job_id":11664,"job_group_id":23},{"job_id":11679,"job_group_id":23},{"job_id":11680,"job_group_id":23},{"job_id":11681,"job_group_id":23},{"job_id":11682,"job_group_id":23},{"job_id":11683,"job_group_id":23},{"job_id":11689,"job_group_id":23},{"job_id":1,"job_group_id":3},{"job_id":10614,"job_group_id":3},{"job_id":10615,"job_group_id":3},{"job_id":10649,"job_group_id":3},{"job_id":10667,"job_group_id":3},{"job_id":11435,"job_group_id":3},{"job_id":11455,"job_group_id":3},{"job_id":11464,"job_group_id":3},{"job_id":11464,"job_group_id":3},{"job_id":11471,"job_group_id":3},{"job_id":11472,"job_group_id":3},{"job_id":11555,"job_group_id":3},{"job_id":11603,"job_group_id":3},{"job_id":11604,"job_group_id":3},{"job_id":11615,"job_group_id":3},{"job_id":11667,"job_group_id":3},{"job_id":11676,"job_group_id":3},{"job_id":11677,"job_group_id":3},{"job_id":11684,"job_group_id":3},{"job_id":11685,"job_group_id":3},{"job_id":11164,"job_group_id":13},{"job_id":11593,"job_group_id":13},{"job_id":11656,"job_group_id":13},{"job_id":11657,"job_group_id":13},{"job_id":10067,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10171,"job_group_id":15},{"job_id":10187,"job_group_id":15},{"job_id":10188,"job_group_id":15},{"job_id":10188,"job_group_id":15},{"job_id":10198,"job_group_id":15},{"job_id":10287,"job_group_id":15},{"job_id":10287,"job_group_id":15},{"job_id":10287,"job_group_id":15},{"job_id":10293,"job_group_id":15},{"job_id":10294,"job_group_id":15},{"job_id":10305,"job_group_id":15},{"job_id":10313,"job_group_id":15},{"job_id":10589,"job_group_id":15},{"job_id":10629,"job_group_id":15},{"job_id":10634,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":10635,"job_group_id":15},{"job_id":11029,"job_group_id":15},{"job_id":11029,"job_group_id":15},{"job_id":11060,"job_group_id":15},{"job_id":11061,"job_group_id":15},{"job_id":11067,"job_group_id":15},{"job_id":11067,"job_group_id":15},{"job_id":11067,"job_group_id":15},{"job_id":11067,"job_group_id":15},{"job_id":11202,"job_group_id":15},{"job_id":11252,"job_group_id":15},{"job_id":11253,"job_group_id":15},{"job_id":11254,"job_group_id":15},{"job_id":11255,"job_group_id":15},{"job_id":11304,"job_group_id":15},{"job_id":11304,"job_group_id":15},{"job_id":11305,"job_group_id":15},{"job_id":11364,"job_group_id":15},{"job_id":11364,"job_group_id":15},{"job_id":11365,"job_group_id":15},{"job_id":11365,"job_group_id":15},{"job_id":11365,"job_group_id":15},{"job_id":11365,"job_group_id":15},{"job_id":11365,"job_group_id":15},{"job_id":11414,"job_group_id":15},{"job_id":11428,"job_group_id":15},{"job_id":11489,"job_group_id":15},{"job_id":11541,"job_group_id":15},{"job_id":11542,"job_group_id":15},{"job_id":11543,"job_group_id":15},{"job_id":11544,"job_group_id":15},{"job_id":11545,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11558,"job_group_id":15},{"job_id":11586,"job_group_id":15},{"job_id":11588,"job_group_id":15},{"job_id":11649,"job_group_id":15},{"job_id":11651,"job_group_id":15},{"job_id":11652,"job_group_id":15},{"job_id":11652,"job_group_id":15},{"job_id":11654,"job_group_id":15},{"job_id":11659,"job_group_id":15},{"job_id":11663,"job_group_id":15},{"job_id":11673,"job_group_id":15},{"job_id":11674,"job_group_id":15},{"job_id":11676,"job_group_id":15},{"job_id":10051,"job_group_id":22},{"job_id":10051,"job_group_id":22},{"job_id":10663,"job_group_id":22},{"job_id":11280,"job_group_id":22},{"job_id":11607,"job_group_id":22},{"job_id":11607,"job_group_id":22},{"job_id":11647,"job_group_id":22},{"job_id":11648,"job_group_id":22},{"job_id":11655,"job_group_id":22},{"job_id":11655,"job_group_id":22},{"job_id":11655,"job_group_id":22},{"job_id":11655,"job_group_id":22},{"job_id":10118,"job_group_id":16},{"job_id":10204,"job_group_id":16},{"job_id":10204,"job_group_id":16},{"job_id":10235,"job_group_id":16},{"job_id":10235,"job_group_id":16},{"job_id":10408,"job_group_id":16},{"job_id":10408,"job_group_id":16},{"job_id":10584,"job_group_id":16},{"job_id":11172,"job_group_id":16},{"job_id":11173,"job_group_id":16},{"job_id":11174,"job_group_id":16},{"job_id":11219,"job_group_id":16},{"job_id":11219,"job_group_id":16},{"job_id":11323,"job_group_id":16},{"job_id":11324,"job_group_id":16},{"job_id":11327,"job_group_id":16},{"job_id":11327,"job_group_id":16},{"job_id":11433,"job_group_id":16},{"job_id":11433,"job_group_id":16},{"job_id":11433,"job_group_id":16},{"job_id":11452,"job_group_id":16},{"job_id":11452,"job_group_id":16},{"job_id":11486,"job_group_id":16},{"job_id":11490,"job_group_id":16},{"job_id":11491,"job_group_id":16},{"job_id":11492,"job_group_id":16},{"job_id":11493,"job_group_id":16},{"job_id":11494,"job_group_id":16},{"job_id":11495,"job_group_id":16},{"job_id":11496,"job_group_id":16},{"job_id":11497,"job_group_id":16},{"job_id":11498,"job_group_id":16},{"job_id":11499,"job_group_id":16},{"job_id":11500,"job_group_id":16},{"job_id":11501,"job_group_id":16},{"job_id":11548,"job_group_id":16},{"job_id":11549,"job_group_id":16},{"job_id":11565,"job_group_id":16},{"job_id":11565,"job_group_id":16},{"job_id":11566,"job_group_id":16},{"job_id":11566,"job_group_id":16},{"job_id":11678,"job_group_id":16},{"job_id":11687,"job_group_id":16},{"job_id":11688,"job_group_id":16},{"job_id":11104,"job_group_id":11},{"job_id":11106,"job_group_id":11},{"job_id":11453,"job_group_id":11},{"job_id":11488,"job_group_id":11},{"job_id":11488,"job_group_id":11},{"job_id":10153,"job_group_id":1},{"job_id":10153,"job_group_id":1},{"job_id":10364,"job_group_id":1},{"job_id":10397,"job_group_id":1},{"job_id":10435,"job_group_id":1},{"job_id":10984,"job_group_id":1},{"job_id":10984,"job_group_id":1},{"job_id":10986,"job_group_id":1},{"job_id":10986,"job_group_id":1},{"job_id":11274,"job_group_id":1},{"job_id":11456,"job_group_id":1},{"job_id":11457,"job_group_id":1},{"job_id":11461,"job_group_id":1},{"job_id":11462,"job_group_id":1},{"job_id":11567,"job_group_id":1},{"job_id":11568,"job_group_id":1},{"job_id":11568,"job_group_id":1},{"job_id":11569,"job_group_id":1},{"job_id":11569,"job_group_id":1},{"job_id":11570,"job_group_id":1},{"job_id":11571,"job_group_id":1},{"job_id":11572,"job_group_id":1},{"job_id":11573,"job_group_id":1},{"job_id":11574,"job_group_id":1},{"job_id":11576,"job_group_id":1},{"job_id":11577,"job_group_id":1},{"job_id":11578,"job_group_id":1},{"job_id":11579,"job_group_id":1},{"job_id":11580,"job_group_id":1},{"job_id":11581,"job_group_id":1},{"job_id":11582,"job_group_id":1},{"job_id":11583,"job_group_id":1},{"job_id":11584,"job_group_id":1},{"job_id":11585,"job_group_id":1},{"job_id":11658,"job_group_id":1},{"job_id":11661,"job_group_id":1},{"job_id":10333,"job_group_id":26},{"job_id":10388,"job_group_id":26},{"job_id":11547,"job_group_id":26},{"job_id":11013,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":11013,"job_group_id":10},{"job_id":10743,"job_group_id":24},{"job_id":11510,"job_group_id":24},{"job_id":11618,"job_group_id":24},{"job_id":11618,"job_group_id":24},{"job_id":11670,"job_group_id":24},{"job_id":11671,"job_group_id":24},{"job_id":11672,"job_group_id":24},{"job_id":11675,"job_group_id":24}]',
                    '[{"job_id":1,"job_title":300},{"job_id":10051,"job_title":42},{"job_id":10051,"job_title":1478},{"job_id":10067,"job_title":1469},{"job_id":10118,"job_title":4},{"job_id":10153,"job_title":1019},{"job_id":10153,"job_title":1019},{"job_id":10171,"job_title":78},{"job_id":10171,"job_title":78},{"job_id":10171,"job_title":78},{"job_id":10171,"job_title":78},{"job_id":10175,"job_title":158},{"job_id":10187,"job_title":335},{"job_id":10188,"job_title":240},{"job_id":10188,"job_title":240},{"job_id":10198,"job_title":162},{"job_id":10204,"job_title":334},{"job_id":10204,"job_title":334},{"job_id":10208,"job_title":287},{"job_id":10235,"job_title":115},{"job_id":10235,"job_title":115},{"job_id":10255,"job_title":916},{"job_id":10287,"job_title":248},{"job_id":10287,"job_title":248},{"job_id":10287,"job_title":248},{"job_id":10287,"job_title":141},{"job_id":10293,"job_title":187},{"job_id":10294,"job_title":1455},{"job_id":10305,"job_title":153},{"job_id":10313,"job_title":47},{"job_id":10333,"job_title":486},{"job_id":10364,"job_title":194},{"job_id":10367,"job_title":65},{"job_id":10388,"job_title":632},{"job_id":10397,"job_title":883},{"job_id":10408,"job_title":620},{"job_id":10408,"job_title":620},{"job_id":10435,"job_title":588},{"job_id":10584,"job_title":1475},{"job_id":10584,"job_title":454},{"job_id":10589,"job_title":476},{"job_id":10600,"job_title":382},{"job_id":10604,"job_title":915},{"job_id":10614,"job_title":917},{"job_id":10615,"job_title":918},{"job_id":10629,"job_title":247},{"job_id":10634,"job_title":682},{"job_id":10635,"job_title":92},{"job_id":10635,"job_title":92},{"job_id":10635,"job_title":92},{"job_id":10635,"job_title":92},{"job_id":10635,"job_title":92},{"job_id":10649,"job_title":681},{"job_id":10660,"job_title":726},{"job_id":10663,"job_title":729},{"job_id":10667,"job_title":733},{"job_id":10743,"job_title":1481},{"job_id":10910,"job_title":573},{"job_id":10984,"job_title":3},{"job_id":10984,"job_title":3},{"job_id":10986,"job_title":190},{"job_id":10986,"job_title":190},{"job_id":11008,"job_title":956},{"job_id":11013,"job_title":962},{"job_id":11013,"job_title":1477},{"job_id":11013,"job_title":1479},{"job_id":11029,"job_title":170},{"job_id":11029,"job_title":170},{"job_id":11035,"job_title":929},{"job_id":11060,"job_title":980},{"job_id":11061,"job_title":981},{"job_id":11067,"job_title":978},{"job_id":11067,"job_title":978},{"job_id":11067,"job_title":978},{"job_id":11067,"job_title":978},{"job_id":11104,"job_title":253},{"job_id":11106,"job_title":414},{"job_id":11164,"job_title":1035},{"job_id":11172,"job_title":652},{"job_id":11173,"job_title":4},{"job_id":11174,"job_title":115},{"job_id":11179,"job_title":1041},{"job_id":11180,"job_title":1042},{"job_id":11202,"job_title":970},{"job_id":11219,"job_title":478},{"job_id":11219,"job_title":1390},{"job_id":11252,"job_title":1083},{"job_id":11253,"job_title":1084},{"job_id":11254,"job_title":1459},{"job_id":11255,"job_title":1462},{"job_id":11263,"job_title":1095},{"job_id":11274,"job_title":1112},{"job_id":11280,"job_title":1117},{"job_id":11293,"job_title":1447},{"job_id":11304,"job_title":1142},{"job_id":11304,"job_title":1142},{"job_id":11305,"job_title":1461},{"job_id":11323,"job_title":1154},{"job_id":11324,"job_title":1025},{"job_id":11327,"job_title":1157},{"job_id":11327,"job_title":1391},{"job_id":11330,"job_title":836},{"job_id":11364,"job_title":168},{"job_id":11364,"job_title":168},{"job_id":11365,"job_title":1186},{"job_id":11365,"job_title":1186},{"job_id":11365,"job_title":1186},{"job_id":11365,"job_title":1186},{"job_id":11365,"job_title":1186},{"job_id":11369,"job_title":1190},{"job_id":11388,"job_title":1215},{"job_id":11398,"job_title":953},{"job_id":11414,"job_title":1456},{"job_id":11428,"job_title":1242},{"job_id":11433,"job_title":617},{"job_id":11433,"job_title":617},{"job_id":11433,"job_title":617},{"job_id":11434,"job_title":141},{"job_id":11435,"job_title":1250},{"job_id":11452,"job_title":1262},{"job_id":11452,"job_title":1262},{"job_id":11453,"job_title":1263},{"job_id":11454,"job_title":1264},{"job_id":11455,"job_title":1265},{"job_id":11456,"job_title":1266},{"job_id":11457,"job_title":1267},{"job_id":11461,"job_title":1272},{"job_id":11462,"job_title":96},{"job_id":11463,"job_title":1273},{"job_id":11464,"job_title":1274},{"job_id":11464,"job_title":1274},{"job_id":11467,"job_title":1278},{"job_id":11468,"job_title":1443},{"job_id":11469,"job_title":1275},{"job_id":11469,"job_title":1275},{"job_id":11469,"job_title":1275},{"job_id":11470,"job_title":1280},{"job_id":11470,"job_title":1280},{"job_id":11471,"job_title":1281},{"job_id":11472,"job_title":1282},{"job_id":11481,"job_title":1291},{"job_id":11486,"job_title":1296},{"job_id":11488,"job_title":1297},{"job_id":11488,"job_title":1297},{"job_id":11489,"job_title":1298},{"job_id":11490,"job_title":1299},{"job_id":11491,"job_title":1300},{"job_id":11492,"job_title":1301},{"job_id":11493,"job_title":1302},{"job_id":11494,"job_title":1303},{"job_id":11495,"job_title":1304},{"job_id":11496,"job_title":1305},{"job_id":11497,"job_title":1306},{"job_id":11498,"job_title":1307},{"job_id":11499,"job_title":1308},{"job_id":11500,"job_title":1309},{"job_id":11501,"job_title":1310},{"job_id":11510,"job_title":1424},{"job_id":11541,"job_title":1460},{"job_id":11542,"job_title":1347},{"job_id":11543,"job_title":1348},{"job_id":11544,"job_title":1156},{"job_id":11545,"job_title":1350},{"job_id":11547,"job_title":1007},{"job_id":11548,"job_title":1352},{"job_id":11549,"job_title":1103},{"job_id":11550,"job_title":1353},{"job_id":11555,"job_title":1358},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11558,"job_title":1361},{"job_id":11565,"job_title":1367},{"job_id":11565,"job_title":1367},{"job_id":11566,"job_title":1368},{"job_id":11566,"job_title":1368},{"job_id":11567,"job_title":1369},{"job_id":11568,"job_title":1370},{"job_id":11568,"job_title":1370},{"job_id":11569,"job_title":797},{"job_id":11569,"job_title":797},{"job_id":11570,"job_title":1371},{"job_id":11571,"job_title":1372},{"job_id":11572,"job_title":1373},{"job_id":11573,"job_title":1374},{"job_id":11574,"job_title":1375},{"job_id":11576,"job_title":1377},{"job_id":11577,"job_title":1378},{"job_id":11578,"job_title":1379},{"job_id":11579,"job_title":1380},{"job_id":11580,"job_title":1381},{"job_id":11581,"job_title":1382},{"job_id":11582,"job_title":1383},{"job_id":11583,"job_title":1384},{"job_id":11584,"job_title":1385},{"job_id":11585,"job_title":1386},{"job_id":11586,"job_title":1387},{"job_id":11588,"job_title":1389},{"job_id":11593,"job_title":1395},{"job_id":11603,"job_title":1405},{"job_id":11604,"job_title":1406},{"job_id":11607,"job_title":1408},{"job_id":11607,"job_title":1409},{"job_id":11615,"job_title":1418},{"job_id":11618,"job_title":1421},{"job_id":11618,"job_title":1421},{"job_id":11623,"job_title":1427},{"job_id":11625,"job_title":1429},{"job_id":11626,"job_title":1430},{"job_id":11636,"job_title":1440},{"job_id":11637,"job_title":1008},{"job_id":11647,"job_title":1450},{"job_id":11648,"job_title":1451},{"job_id":11649,"job_title":1452},{"job_id":11651,"job_title":1453},{"job_id":11652,"job_title":1454},{"job_id":11652,"job_title":1457},{"job_id":11654,"job_title":1458},{"job_id":11655,"job_title":1463},{"job_id":11655,"job_title":1463},{"job_id":11655,"job_title":1463},{"job_id":11655,"job_title":1463},{"job_id":11656,"job_title":684},{"job_id":11657,"job_title":532},{"job_id":11658,"job_title":1464},{"job_id":11659,"job_title":182},{"job_id":11661,"job_title":1466},{"job_id":11663,"job_title":1468},{"job_id":11664,"job_title":1470},{"job_id":11667,"job_title":1473},{"job_id":11669,"job_title":1476},{"job_id":11670,"job_title":1482},{"job_id":11671,"job_title":1483},{"job_id":11672,"job_title":1484},{"job_id":11673,"job_title":1485},{"job_id":11674,"job_title":1486},{"job_id":11675,"job_title":1487},{"job_id":11676,"job_title":1499},{"job_id":11676,"job_title":1488},{"job_id":11677,"job_title":1489},{"job_id":11678,"job_title":1490},{"job_id":11679,"job_title":1491},{"job_id":11680,"job_title":1492},{"job_id":11681,"job_title":1330},{"job_id":11682,"job_title":415},{"job_id":11683,"job_title":1494},{"job_id":11684,"job_title":1495},{"job_id":11685,"job_title":1496},{"job_id":11686,"job_title":847},{"job_id":11687,"job_title":263},{"job_id":11688,"job_title":1497},{"job_id":11689,"job_title":1498}]',
                ];
                break;
        }

        return $data;
    }

    /**
     * PDF合并测试
     * @return void
     */
    public function pdfTestAction()
    {
        $pdfRes = (new FormPdfServer())->getInstance()->mergePdf([
            'https://fex-th-asset-dev-new.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1744887759-507a135d8634457283a5c0fd15a399c2.pdf',
            'https://fex-th-asset-dev-new.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1744887202-bc9e589fc3394a32a9ba989b40900768.pdf',
            'https://fex-th-asset-dev-new.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1744887874-afc337ab24394a9eaf7ef27b0036e2fe.pdf',
            'https://fex-th-asset-dev-new.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1744707035-f71c7fd0b00c44348edc6c4e3f6bca96.pdf',
            'https://fex-th-asset-dev-new.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1744684432-45c7e2fd47634e48853178c96e6ce3b4.pdf',
        ]);

        //输出合并后的数据
        dd($pdfRes);
    }

    /**
     * 初始化价值观数据 - 测试TRA使用 - 生成不执行
     * @return void
     * @throws Exception
     */
    public function initActAction()
    {
        $data = [
            [
                'id'            => 1,
                'concept_type'  => 1,
                'behavior_type' => 1,
                'description'   => '尊重他人，随时随地维护Flash形象。',
                'sort'          => 1,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 2,
                'concept_type'  => 1,
                'behavior_type' => 2,
                'description'   => '微笑面对投诉和受到的委屈，积极主动地为客户解决问题。',
                'sort'          => 2,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 3,
                'concept_type'  => 1,
                'behavior_type' => 3,
                'description'   => '面对客户，即便不是自己的责任，也不推诿，主动协同推动相关方解决客户问题。',
                'sort'          => 3,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 4,
                'concept_type'  => 1,
                'behavior_type' => 4,
                'description'   => '持续创造客户价值，持续推动提升服务质量和用户体验，达到公司和客户双赢。',
                'sort'          => 4,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 5,
                'concept_type'  => 1,
                'behavior_type' => 5,
                'description'   => '洞察客户需求，探索创新机会，并取得一定的结果。',
                'sort'          => 5,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 6,
                'concept_type'  => 2,
                'behavior_type' => 6,
                'description'   => '清晰了解自己和团队的工作职责和目标，以终为始，以目标驱动行为，遵循共同的价值观全力以赴，工作过程和交付结果均值得托付 。',
                'sort'          => 1,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 7,
                'concept_type'  => 2,
                'behavior_type' => 7,
                'description'   => '今天最好的表现是明天最低的要求，关注团队，不断超越，持续拿到超出期望的结果。',
                'sort'          => 2,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 8,
                'concept_type'  => 2,
                'behavior_type' => 8,
                'description'   => '心态开放，面对变化和困难没有任何借口，充分沟通，全力配合，正面影响团队。主动担当，在艰苦的岗位，攻坚的岗位上持之以恒地做出贡献。',
                'sort'          => 3,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 9,
                'concept_type'  => 2,
                'behavior_type' => 9,
                'description'   => '突破客观条件限制，打破边界，用前瞻的眼光规划和安排工作，提出新思路新方法，并能结合有限资源进行实践，取得成效和结果。',
                'sort'          => 4,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 10,
                'concept_type'  => 2,
                'behavior_type' => 10,
                'description'   => '深度思考，洞悉本质，沉淀方法，预见趋势，带来业务创造性突破或爆发式增长。',
                'sort'          => 5,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 11,
                'concept_type'  => 3,
                'behavior_type' => 11,
                'description'   => '积极融入团队，乐于接受帮助和帮助他人，为团队和跨团队的目标充分投入和协同。',
                'sort'          => 1,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 12,
                'concept_type'  => 3,
                'behavior_type' => 12,
                'description'   => '决策前充分参与讨论，发表建设性意见，决策后坚决执行。',
                'sort'          => 2,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 13,
                'concept_type'  => 3,
                'behavior_type' => 13,
                'description'   => '合作一视同仁，对事不对人，能主动有效协同跨团队的，不同类型的人员共同推进目标进展，善于协同团队的力量解决问题和困难。',
                'sort'          => 3,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 14,
                'concept_type'  => 3,
                'behavior_type' => 14,
                'description'   => '乐于分享，乐见伙伴和团队成长，并积极行动帮助团队和伙伴共同成长。',
                'sort'          => 4,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 15,
                'concept_type'  => 3,
                'behavior_type' => 15,
                'description'   => '有主人翁意识，为公司带来积极正面的影响，激发团队正向积极的士气和氛围，齐心协力推动公司发展及建设。',
                'sort'          => 5,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 16,
                'concept_type'  => 4,
                'behavior_type' => 16,
                'description'   => '诚实正直，言行一致，真实不装；不唯上欺下，不抢功甩锅，不能只报喜不报忧。（自己的工作诚信）',
                'sort'          => 1,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 17,
                'concept_type'  => 4,
                'behavior_type' => 17,
                'description'   => '通过正确的渠道和流程，基于事实准确表达自己的观点；表达批评意见的同时提出改进建议，直言有讳但不迂回。不传播未经证实的消息，不背后不负责任地议论事和人，并能正面引导。（同事相处）',
                'sort'          => 2,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 18,
                'concept_type'  => 4,
                'behavior_type' => 18,
                'description'   => '严于律己，求责于己，敢于担责。面对问题，积极反思及行动改进，并取得结果。即使没有承诺，本职应当履行的责任也要做到最好结果。（对待客户）',
                'sort'          => 3,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 19,
                'concept_type'  => 4,
                'behavior_type' => 19,
                'description'   => '为人坦荡，敢于把自己的后背交给伙伴，能够在公司里赢得所有伙伴的信任。（公司影响力）',
                'sort'          => 4,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 20,
                'concept_type'  => 4,
                'behavior_type' => 20,
                'description'   => '客观反映问题，对损害公司利益的不诚信行为严厉制止，在公司外部带来对公司的美誉度。（公司外的旗帜）',
                'sort'          => 5,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 21,
                'concept_type'  => 5,
                'behavior_type' => 21,
                'description'   => '认真踏实，有强烈的责任感，高效优质完成本职工作。今天的事不推到明天，遵循必要的工作流程，没有因工作失职而造成重复错误或损失。',
                'sort'          => 1,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 22,
                'concept_type'  => 5,
                'behavior_type' => 22,
                'description'   => '保持好奇，持续学习，学以致用。面对工作不断提升和优化自己，碰到任何困难和挫折绝不退缩，越挫越勇，直到拿到结果。',
                'sort'          => 2,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 23,
                'concept_type'  => 5,
                'behavior_type' => 23,
                'description'   => '能根据轻重缓急来正确安排工作优先级，持续改进工作方式，提升个人和团队工作绩效与效能 。',
                'sort'          => 3,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 24,
                'concept_type'  => 5,
                'behavior_type' => 24,
                'description'   => '管控好自身情绪，理性思考和解决问题，不因情绪影响工作开展。不为失败找借口，用尽全力为成功找方法。',
                'sort'          => 4,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
            [
                'id'            => 25,
                'concept_type'  => 5,
                'behavior_type' => 25,
                'description'   => '在需要的时候不计较个人得失挺身而出，7*24h时刻准备响应和解决问题，为业务发展保驾护航 。',
                'sort'          => 5,
                'is_deleted'    => 0,
                'version'       => 1,
            ],
        ];

        // 删除已经处理的数据
        $hrProbationActValuesModel = (new \app\models\backyard\HrProbationActValuesModel());
        $hrProbationActValuesModel->delete();

        $res = $hrProbationActValuesModel->batch_insert($data);

        if ($res) {
            echo 'ERROR';
        }

        echo 'SUCCESS';
    }

}