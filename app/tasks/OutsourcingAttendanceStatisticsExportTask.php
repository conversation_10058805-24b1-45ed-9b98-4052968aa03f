<?php


use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\FlashOss;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AttendanceStatisticsService;
use App\Services\ExcelService;

class OutsourcingAttendanceStatisticsExportTask extends BaseTask
{
    //外协出勤统计 每日纵向导出
    public function mainAction($input){
        try {
            //获取任务参数
            $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
            if(empty($input[1])){
                return $this->echoErrorLog('task not isset :'.$input[1],true);
            }

            $this->echoInfoLog('OutsourcingAttendanceStatisticsExportTask start',true);
            //TODO 整理参数
            ini_set('memory_limit', '-1');
            set_time_limit(0);      // 设置脚本最大执行时间 为0 永不过期

            $params = json_decode(base64_decode($input[0]),true);
            BaseService::setLanguage($params['lang']);

            $task_id = $input[1];
            $excelTask = HcmExcelTackModel::findFirst($task_id);
            if(empty($excelTask)){
                return $this->echoErrorLog('task not isset :'.$task_id,true);
            }

            $attendanceStatisticsService =  new AttendanceStatisticsService();
            $attendanceStatisticsService = reBuildCountryInstance($attendanceStatisticsService);
            $result = [];
            $params['page'] = 1;
            $params['size'] = 500;
            while (true){
                $data = $attendanceStatisticsService->handleOutsourcingAttendance($params);
                if(empty($data)){
                    break;
                }
                $result = array_merge($result,$data);
                $params['page']++;
            }
            $config = [
                'path' => sys_get_temp_dir(),
            ];
            $excel_data = [];
            foreach ($result as $k => $l){
                foreach($l['attend_data'] as $v){
                    if (empty($v['value'])) {
                        continue;
                    }
                    $start = '';
                    $end = '';
                    if($v['value'] == 'AB'){
                        $start = 'AB';
                        $end = 'AB';
                    }

                    if (!empty($v['operate'])) {
                        $v['value'] = str_replace(['in:', 'out:'], '', $v['value']);
                        $operate_arr = explode(' ', $v['value']);
                        $start = $operate_arr[0];
                        $end = $start == 'AB' || $start == 'AB(h)' ? $start : $operate_arr[1];
                    }

                    $companyName = $v['stat_date'] == date('Y-m-d') ? $l['company_name_ef'] : $v['company_name_ef'];
                    $excel_data[] = [
                        $l['staff_info_id'],
                        $l['name'],
                        $l['region'],
                        $l['piece'],
                        $v['store_name']?:$l['store_name'],
                        $l['outsourcing_type'],
                        $companyName,
                        $v['stat_date'],
                        $start,
                        $end,
                        $v['working_duration'],
                        $v['shift'],
                    ];
                }
            }
            $result = null;
            $excel = new \Vtiful\Kernel\Excel($config);
            $t = BaseService::getTranslation($params['lang']);

            $header = [
                $t['staff_info_id'],
                $t['hr_probation_field_name'],  //'姓名',
                'Area',
                'District',
                $t['dot'],
                $t['backyardattendance_flashout_type'],
                $t['backyardattendance_flashout_company'],
                $t['fleet_date_select'],
                $t['punch_time_working_time'],
                $t['punch_time_after_work'],
                $t['outsourcing_attendance_working_minutes'],//上班时长 分钟
                $t['shift'],
            ];
            $filePath = $excel->fileName($excelTask->file_name)->header($header)->data($excel_data)->output();

            $flashOss = new FlashOss();
            $ossObject = 'attendance/'.date('Y-m-d').'/'.$excelTask->file_name;
            $flashOss->uploadFile($ossObject,$filePath);

            if(!empty($params['From']) && $params['From'] == 'fbi') {
                $url = $flashOss->signUrl($ossObject , 20 * 24 * 60 * 60);
                $dowLoadTaskServer = new \App\Services\DownLoadTaskService();
                $dowLoadTaskServer->updateExportManageInfo('outsourcing_attendance_statistics_export_main_' . $task_id , $url, 3,$task_id);
                $excelTask->is_delete = 1;
            }

            $excelTask->path= $ossObject;
            $excelTask->status = 1;
            $excelTask->finish_at = gmdate('Y-m-d H:i:s',time()+$this->config->application->add_hour*3600);
            $excelTask->save();
            @unlink($filePath);

        }catch (Exception $e){
            $this->logger->error($e->getMessage() . $e->getTraceAsString());
            throw $e;
        }
        $this->echoInfoLog('OutsourcingAttendanceStatisticsExportTask end',true);

    }

    /**
     * 页面结构导出 下载中心任务
     * @description: 外协员工考勤统计导出，优化数据与Excel头信息匹配问题
     * @author: AI
     * @date: 2025-01-25 10:30:00
     *
     * 优化内容：
     * 1. 修复数据列与日期头信息不匹配的问题
     * 2. 使用日期映射确保数据按正确顺序排列
     * 3. 对缺失日期数据进行空值填充
     * 4. 优化颜色处理逻辑，只处理日期列数据
     *
     * @param array $input 任务参数
     * @return void
     */
    public function export_osAction($input){

        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        $this->echoInfoLog('OutsourcingAttendanceStatisticsExportTask start', true);
        ini_set('memory_limit', '-1');
        set_time_limit(0);      // 设置脚本最大执行时间 为0 永不过期
        $params = json_decode(base64_decode($input[0]), true);
        BaseService::setLanguage($params['lang']);
        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);
        if (empty($excelTask)) {
            $this->logger->error('task not isset :' . $task_id);
            return;
        }
        $attendanceStatisticsService = new AttendanceStatisticsService();
        $attendanceStatisticsService = reBuildCountryInstance($attendanceStatisticsService);
        $result                      = [];
        $params['page']              = 1;
        $params['size']              = 500;
        while (true) {
            $data = $attendanceStatisticsService->handleOutsourcingAttendance($params);
            if (empty($data)) {
                break;
            }
            $result = array_merge($result, $data);
            $params['page']++;
        }
        //拼head - 生成日期范围数组，确保与数据完全匹配
        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        $t          = BaseService::getTranslation($params['lang']);
        $header     = [
            $t['staff_info_id'],
            $t['name'],  //'姓名',
            $t['job_name'],
            $t['staff_state'],//在职状态
            $t['store_area'],//大区
            $t['store_district'],//片区
            $t['dot'],
            $t['store_category_name'],//网点类型
            $t['backyardattendance_flashout_type'],//外协员工类型
            $t['backyardattendance_flashout_company'],
            $t['absenteeism_num'],//旷工天数
            $t['late_num'],//迟到次数
            $t['late_time'],//迟到时长
            $t['leave_early_num'],//早退次数
            $t['leave_early_time'],//早退时长
        ];
        // 添加日期列到头信息
        foreach ($date_range as $date) {
            $header[] = $date;
        }

        $config     = [
            'path' => sys_get_temp_dir(),
        ];
        $excel      = new \Vtiful\Kernel\Excel($config);
        $fileObject = $excel->fileName($excelTask->file_name);
        $fileHandle = $fileObject->getHandle();

        // 定义字体颜色格式
        $redFormat    = (new \Vtiful\Kernel\Format($fileHandle))->fontColor(\Vtiful\Kernel\Format::COLOR_RED)->toResource();
        $yellowFormat = (new \Vtiful\Kernel\Format($fileHandle))->fontColor(0xe6a23c)->toResource();
        $grayFormat   = (new \Vtiful\Kernel\Format($fileHandle))->fontColor(\Vtiful\Kernel\Format::COLOR_GRAY)->toResource();

        //整excel data - 优化数据处理，确保与头信息完全匹配
        $excel_data = [];
        if (!empty($result)) {
            foreach ($result as $da) {
                // 初始化行数据，包含基础字段
                $row = [
                    $da['staff_info_id'],
                    $da['name'],
                    $da['job_name'],
                    $da['state_text'],
                    $da['region'],
                    $da['piece'],
                    $da['store_name'],
                    $da['store_category'],
                    $da['outsourcing_type'],
                    $da['company_name_ef'],
                    //新增字段
                    $da['ab_days'],
                    $da['late_num'],
                    $da['late_duration'],
                    $da['early_num'],
                    $da['early_duration'],
                ];

                // 创建考勤数据映射，以日期为键
                $attend_data_map = [];
                if (!empty($da['attend_data'])) {
                    foreach ($da['attend_data'] as $att_data) {
                        $attend_data_map[$att_data['stat_date']] = $att_data;
                    }
                }

                // 按照日期范围顺序添加考勤数据，确保与头信息完全匹配
                foreach ($date_range as $date) {
                    if (isset($attend_data_map[$date])) {
                        // 存在该日期的考勤数据
                        $att_data = $attend_data_map[$date];
                        $excel_text = $att_data['excel_text'] ?? '';
                        $background = $att_data['background'] ?? 0;
                        if(empty($excel_text)){
                            $row[] = "";
                        }else{
                            $row[] = "{$background}|{$excel_text}";
                        }

                    } else {
                        // 不存在该日期的考勤数据，填充空值
                        $row[] = "";
                    }
                }

                $excel_data[] = $row;
            }
        }
        $result = null;
//        var_dump($excel_data);exit;
        $excel->header($header)->data($excel_data);

        //改颜色 - 优化颜色处理逻辑，确保只处理日期列的数据
        $base_column_count = 15; // 基础字段列数（员工信息、统计数据等）
        foreach ($excel_data as $rowIndex => $rowData) {
            foreach ($rowData as $colIndex => $cellValue) {
                // 只处理日期列的数据（从第16列开始）
                if ($colIndex >= $base_column_count && strstr($cellValue, '|')) {
                    $cellValue = explode('|', $cellValue);
                    $background = $cellValue[0];
                    $text = $cellValue[1] ?? '';

                    // 如果是空值数据（0|），显示为空白
                    if ($background == 0 && empty($text)) {
                        $fileObject->insertText($rowIndex + 1, $colIndex, '');
                    } elseif ($background == 2) {//迟到早退
                        $fileObject->insertText($rowIndex + 1, $colIndex, $text, null, $yellowFormat);
                    } elseif ($background == 3) {//缺卡
                        $fileObject->insertText($rowIndex + 1, $colIndex, $text, null, $redFormat);
                    } elseif ($background == 1) {//休息日
                        $fileObject->insertText($rowIndex + 1, $colIndex, $text, null, $grayFormat);
                    } else {
                        // 正常情况，使用默认格式
                        $fileObject->insertText($rowIndex + 1, $colIndex, $text);
                    }
                }
            }
        }

        $filePath  = $excel->output();
        $flashOss  = new FlashOss();
        $ossObject = 'attendance/' . date('Y-m-d') . '/' . $excelTask->file_name;
        $flashOss->uploadFile($ossObject, $filePath);

        if (!empty($params['From']) && $params['From'] == 'fbi') {
            $flashOss          = new FlashOss();
            $url               = $flashOss->signUrl($ossObject, 20 * 24 * 60 * 60);
            $dowLoadTaskServer = new \App\Services\DownLoadTaskService();
            $dowLoadTaskServer->updateExportManageInfo('outsourcing_attendance_statistics_export_main_' . $task_id,
                $url, 3, $task_id);
            $excelTask->is_delete = 1;
        }

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        @unlink($filePath);
    }


}
