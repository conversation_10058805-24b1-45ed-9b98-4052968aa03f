<?php

use App\Models\backyard\HrStoreApplySupportModel;
use App\Services\StaffSupportService;

/**
 * 更新表 hr_store_apply_support 的support_status字段
 *
 * 跑生效数据 【每日凌晨5分跑】
 * php app/cli.php staff_store_apply_support modifySupportStatus 1
 *
 * 跑失效数据 【每日凌晨5分跑】
 * php app/cli.php staff_store_apply_support modifySupportStatus 2
 *
 * 跑全部数据 【上线时处理历史数据，运行一次即可】
 * php app/cli.php staff_store_apply_support modifySupportStatus 3
 *
 */
class StaffStoreApplySupportTask extends BaseTask
{
    // 是否终止while 循环
    private $isStopWhile = true;
    private $lastId      = 1;
    private $pageSize    = 50;  // 每页多少条
    private $curPage     = 1;   // 当前页
    private $logger      = null;

    private $type_take_effect = 1; // 生效
    private $type_fail        = 2; // 失效
    private $type_all         = 3; // 全部

    /**
     * 获取db的连接
     * @return mixed
     */
    private function getBackyardDB()
    {
        return $this->getDI()->get('db_backyard');
    }

    /**
     * 参数 1 type：1表示生效 2表示失效 3 表示全部
     * 生效 OR 失效
     * @return void
     * @throws Exception
     */
    public function modifySupportStatusAction($params)
    {
        $this->logger = $this->getDi()->get('logger');
        $this->printLog("begin:".date("Y-m-d h:i:s"));

        // 1 跑生效数据 2 跑失效数据 3 跑所有数据
        $type = $params[0] ?? $this->type_take_effect;

        // 跑指定日期
        $today = date("Y-m-d");
        if (isset($params[1])) {
            $today = $params[1];
        }

        // 跑指定工号
//        $staff_info_ids = [];
//        if (isset($params[2])) {
//            $staff_info_ids = explode(',', $params[2]);
//        }

        echo "type = 1：更新支援需求状态生效, 审批通过 每天00:05 支援开始日期等于或早于今天，且支援结束日期等于或晚于今天".PHP_EOL;
        echo "type = 2：更新支援需求状态失效, 审批通过 每天00:05 支援结束日期早于今天".PHP_EOL;
        echo '执行类型 type:'.$type;
        echo "执行日期：".$today.PHP_EOL;

        while ($this->isStopWhile) {
            $this->printLog("+++++++++处理开始第【".$this->curPage."】页数据，时间：【".gmdate("Y-m-d H:i:s")."】+++++++");

            // 获取有效数据
            $result = $this->getValidData($this->lastId, $this->pageSize + 1, $type, $today, $this->curPage);

            // 处理翻页
            if (count($result) > $this->pageSize) {
                $lastResult   = array_pop($result);
                $this->lastId = intval($lastResult['id']);
            } else {
                // todo 如果当前页的数据正好等于 pageSize 或者 小于 pageSize 说明已经没有下一页了，需要退出while
                $this->printLog("++++++++++++++++++++++++ 没有下一页数据了，退出While +++++++++++++++++++++++++++++++");
                $this->isStopWhile = false;
            }

            if (empty($result)) {
                $this->printLog("============ 无更新数据 ===============");
                exit;
            }

            if ($this->type_take_effect == $type) {
                $ids = array_column($result, 'id');
                // 跑生效数据
                $this->updateSupportStatus(2, $ids);
            }

            if ($this->type_fail == $type) {
                $ids = array_column($result, 'id');
                // 跑失效数据
                $this->updateSupportStatus(3, $ids);
            }

            if ($this->type_all == $type) {
                // 跑全部数据
                $this->updateAll($result);
            }

            // 当前页 + 1
            $this->curPage++;
        }

        $this->printLog('============== 脚本执行完毕===============');
    }

    /**
     * 输出日志
     * @param string $logInfo
     * @return void
     */
    public function printLog(string $logInfo)
    {
        $logInfo .= PHP_EOL;
        $this->logger->info($logInfo);
        echo $logInfo;
    }

    /**
     * 获取有效数据
     * @param int $lastId
     * @param int $pageSize
     * @param int $type
     * @param string $date
     * @param int $cur_page
     * @return void
     */
    public function getValidData(int $lastId, int $pageSize, int $type, string $date, int $cur_page)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("id, employment_begin_date, employment_end_date, status, support_status");
        $builder->from(HrStoreApplySupportModel::class);

        // 跑生效数据
        if ($this->type_take_effect == $type) {
            /**
             * 招募中变为生效
             * 支援开始日期晚于今天：招募中
             * 支援开始日期等于或早于今天，且支援结束日期等于或晚于今天：已生效
             */
            // 审批通过
            $builder->andWhere("status = :status:", ['status' => 2]);
            $builder->andWhere("support_status = :support_status:", ['support_status' => 5]);
            $builder->andWhere("employment_begin_date <= :employment_begin_date: AND employment_end_date >= :employment_end_date:",
                ['employment_begin_date' => $date, 'employment_end_date' => $date]);
        }

        // 跑失效数据
        if ($this->type_fail == $type) {
            /**
             * 生效中变为失效
             * 支援结束日期早于今天：已失效
             */
            // 审批通过
            $builder->andWhere("status = :status:", ['status' => 2]);
            $builder->andWhere("support_status = :support_status:", ['support_status' => 2]);
            $builder->andWhere("employment_end_date < :employment_end_date:", ['employment_end_date' => $date]);
        }

        // 跑全部数据
        if ($this->type_all == $type) {
            // 所有数据，代码初次发布后，需要对历史所有数据生成对应的状态
            $builder->andWhere("support_status = :support_status:", ['support_status' => 1]);
        }

        $builder->andWhere("id >= :id:", ['id' => $lastId]);
        $builder->orderBy('id ASC');
        $builder->limit($pageSize);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 更新数据状态
     * @param int $support_status
     * @param array $ids
     * @return void
     */
    public function updateSupportStatus(int $support_status, array $ids)
    {
        $this->getBackyardDB()->begin();
        $updateResult = $this->getBackyardDB()->updateAsDict(
            (new HrStoreApplySupportModel())->getSource(),
            [
                'support_status' => $support_status,
            ],
            'id IN ('.implode(',', $ids).')'
        );
        if (!$updateResult) {
            $this->getBackyardDB()->rollback();
            $this->printLog('更新失败-id:'.implode(',', $ids).' support_status:'.$support_status);
        }
        $this->printLog('更新成功-id:'.implode(',', $ids).' support_status:'.$support_status);
        $this->getBackyardDB()->commit();
    }

    /**
     * 处理所偶数据
     * @param array $list
     * @return void
     * @throws Exception
     */
    public function updateAll(array $list)
    {
        $sql = "UPDATE hr_store_apply_support SET support_status = CASE id";
        $ids = [];
        foreach ($list as $key => $value) {
            // 待审批状态的数据 不做更新处理
            if (1 == $value['status']) {
                continue;
            }
            $support_status = (new StaffSupportService())->getSupportStatus($value['employment_begin_date'], $value['employment_end_date'],
                $value['status']);
            $sql            .= " WHEN {$value['id']} THEN '{$support_status}'";
            $ids[]          = $value['id'];
        }

        if (empty($ids)) {
            $this->printLog("++++++++++++++++ 无更新数据 ++++++++++++++++++++");
            return false;
        }
        $sql .= " END WHERE id IN (".implode(',', $ids).")";
        $this->updateAllSupportStatus($sql);
    }

    /**
     * 更新所有数据
     * @return void
     * @throws Exception
     */
    private function updateAllSupportStatus($sql)
    {
        $this->printLog($sql);
        $this->getBackyardDB()->begin();
        $result = $this->getBackyardDB()->execute($sql);
        if (!$result) {
            $this->getBackyardDB()->rollback();
            $this->printLog("更新失败! sql => $sql");
        }
        $this->printLog("更新成功! sql => $sql");
        $this->getBackyardDB()->commit();
    }

}
