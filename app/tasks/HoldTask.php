<?php

use App\Library\Enums\ApprovalEnums;
use App\Library\BaseService;
use App\Library\Enums\RedisListEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HoldHistoryModel;
use App\Models\bi\RoleStaffMenusModel;
use App\Models\bi\RoleMenusModel;
use App\Services\BllService;
use App\Services\ExcelService;
use App\Services\HoldManageService;
use App\Services\LeaveManagerService;
use App\Models\backyard\HcmPermissionModel;
use App\Models\backyard\HcmRolePermissionModel;
use App\Models\backyard\HcmStaffPermissionModel;

class HoldTask extends BaseTask
{

    const EXPORT_HOLD_LIST_ACTION = 'hold' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'exportHoldList';


    public function exportHoldListAction()
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :' . $input[1], true);
        }

        $params = json_decode($input[0], true);
        $this->echoInfoLog('params :' . json_encode($params, JSON_UNESCAPED_UNICODE), true);

        //设置语言
        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);
        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :' . $task_id, true);
        }
        $file_name = $excelTask->file_name;
        $params['file_name'] = $file_name;
        $service = reBuildCountryInstance(new HoldManageService());
        /**
         * @see HoldManageService::exportHoldList()
         */
        $result = $service->exportHoldList($params);
        (new ExcelService())->updateTask($task_id, $file_name, $result['file_path']);
    }

    /**
     * @Single
     * 系统自动处理提成hold
     * 每一个月1号1点 0 1 1 *\/1 * php app/cli.php Hold main
     * @param array $params
     */
    public function mainAction(array $params)
    {
        try {
            if ($params && !empty($params[0])) {
                $month_end = date("Y-m", strtotime($params[0] . " -2 month"));
            } else {
                $month_end = date("Y-m", strtotime(" -2 month"));
            }
            $month_end .= "-" . date("t", strtotime($month_end));
            $handle_result = '[{"key":"no_punishment","value":""}]';
            $hold_attachments = '[{"file_name":"system_auto.jpeg","object_url":"https:\/\/sai.flashexpress.com\/workOrder\/1589542185-232b8cc3a4ec473e81408f30a36dee50.jpeg","object_key":"workOrder\/1589542185-232b8cc3a4ec473e81408f30a36dee50.jpeg"}]';
            $hold_remark = "system auto";

            $holdList = HoldStaffManageModel::find([
                'conditions' => "type = 2 and handle_progress = 1 and month_end = :month_end: and is_delete = 0",
                'bind' => [
                    'month_end' => $month_end,
                ],
            ])->toArray();

            if ($holdList) foreach ($holdList as $item) {
                $pdo = $this->getDI()->get('db_backyard');
                $this->getDI()->get('logger')->write_log("holdTask 处理 " . $item['id'] .  " " . json_encode($item, JSON_UNESCAPED_UNICODE), 'info');
                if ($item['hold_reason'] != 'off_3_days') { // 不处理 旷工三天的 已处理
                    $sql = "--
                            update 
                                hold_staff_manage 
                            set 
                                handle_result = :handle_result, 
                                hold_attachments = :hold_attachments, 
                                hold_remark = :hold_remark,
                                handle_progress = :hold_progress 
                            where 
                                id = :id ";
                    $stmt = $pdo->prepare($sql);
                    $stmt->bindValue(":handle_result", $handle_result, \PDO::PARAM_STR);
                    $stmt->bindValue(":hold_attachments", $hold_attachments, \PDO::PARAM_STR);
                    $stmt->bindValue(":hold_remark", $hold_remark, \PDO::PARAM_STR);
                    $stmt->bindValue(":hold_progress", 3, \PDO::PARAM_INT);
                    $stmt->bindValue(":id", $item['id'], \PDO::PARAM_INT);
                    $stmt->execute();
                    $rowCount = $stmt->rowCount();
                    $this->getDI()->get('logger')->write_log("holdTask 处理已完成" . $item['id'], 'info');

                    (new HoldHistoryModel())->insert_record([
                        'hold_no' => $item['id'],
                        'staff_submit_id' => 10003,
                        'handle_progress' => 3,
                        'handle_result' => $handle_result,
                        'hold_attachments' => $hold_attachments,
                        'hold_remark' => $hold_remark,
                        'hold_time'=>date('Y-m-d H:i:s'),
                    ]);
                    if ($rowCount) {
                        $this->getDI()->get('logger')->write_log("更改数据: id : " . json_encode($item, JSON_UNESCAPED_UNICODE), 'info');
                    }
                }
            }

            $this->releaseType2();
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log("HoldTask error" . $e->getMessage() . $e->getTraceAsString(), "error");
        }
    }

    /**
     * @Single
     * 每个月的 6 号 和 16号 分别根据离职日期同步,当月的(6号到下个月6号 与 16号到下个月的16号) 离职申请人hold
     * @param array $params
     * @return true|void
     * @throws BusinessException
     */
    public function syncHoldOnMonthAction(array $params)
    {
        if (!empty($params[0])) {
            $dateType = date('d',strtotime($params[0]));
            [$currentDate, $nextMonthDate] = $this->getCurrentDataAndNextMonthdatecli($params[0]);
        } else {
            $dateType = date("d");
            [$currentDate, $nextMonthDate] = $this->getCurrentDataAndNextMonthdate($dateType);
        }

        $this->getDI()->get("logger")->write_log(['date'=>[$currentDate, $nextMonthDate]] ,"info");
        $sub_where = '';

        if (!empty($params[1])) {
            $sub_where = " and submitter_id = " . !empty($params[1]);
        }

        $sql = "
                    select 
                        submitter_id, 
                        group_concat(reason) as reason,
                        group_concat(source) as source,
                        group_concat(created_at) as created_at,
                        group_concat(leave_date) as leave_date,
                        group_concat(status) as status
                    from 
                        staff_resign 
                    where leave_date >= '{$currentDate}' and leave_date <= '{$nextMonthDate}' and created_at >= '2020-06-11'  $sub_where group by submitter_id ";

        $list = $this->getDI()->get("db_rby")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $this->getDI()->get("logger")->write_log("syncHoldOnMonth data " . json_encode($list, JSON_UNESCAPED_UNICODE),
            "info");
        if (empty($list)) {
            return true;
        }

        $staff_ids = array_column($list, 'submitter_id');

        $staffInfo         = (new \App\Services\StaffInfoService())->getStaffInfoListByStaffIds($staff_ids);
        $staffInfoHireType = array_column($staffInfo, 'hire_type', 'staff_info_id');
        $add_hour          = $this->config->application->add_hour;

        foreach ($list as $item) {
            $reasons    = explode(",", $item['reason']);
            $createdAts = explode(",", $item['created_at']);
            $leaveDates = explode(",", $item['leave_date']);
            $sources    = explode(",", $item['source']);
            $status     = explode(",", $item['status']);

            foreach ($reasons as $k => $reason) {
                if (!in_array($status[$k], [ApprovalEnums::APPROVAL_STATUS_REJECTED, ApprovalEnums::APPROVAL_STATUS_CANCEL])) {
                    $params = [
                        "staff_info_id" => $item['submitter_id'],
                        "type"          => "",
                        'hold_reason'   => 'incomplete_resignation_procedures',
                        //hold原因(BY申请离职)
                        'hold_remark'   => $reason,
                        //hold备注
                        'hold_time'     => date('Y-m-d H:i:s', strtotime($createdAts[$k]) + ($add_hour * 3600)),
                        //hold时间操作时间
                        'hold_source'   => HoldStaffManageModel::HOLD_SOURCE_BY_APPLY,
                        //来源->离职管理
                    ];
                    //批量导入
                    if ($sources[$k] == 1) {
                        $params['hold_reason'] = 'incomplete_resignation_procedures1';//来源-批量导入
                    }

                    if ($dateType == 6) {
                        //如果提交的 离职日期 在 提交日期下月的16号以后
                        $params['type'] = "1";
                        if (in_array($staffInfoHireType[$item['submitter_id']],\App\Models\backyard\HrStaffInfoModel::$agentTypeTogether)) {
                            $params['type'] = "2";
                        }
                        if ($leaveDates[$k] >= date("Y-m-16", strtotime($createdAts[$k] . " +1 month"))) {
                            //如果提交的 离职日期 在 提交日期下月的6号以后
                            $this->getDI()->get("logger")->write_log("syncHoldOnMonth data info:" . $params['staff_info_id'] . json_encode($params,
                                    JSON_UNESCAPED_UNICODE), "info");
                            (new HoldManageService())->synchronizeHoldStaff($params);
                        } else {
                            if ($leaveDates[$k] >= date("Y-m-06", strtotime($createdAts[$k] . " +1 month"))) {
                                $this->getDI()->get("logger")->write_log("syncHoldOnMonth data info:" . $params['staff_info_id'] . json_encode($params,
                                        JSON_UNESCAPED_UNICODE), "info");
                                (new HoldManageService())->synchronizeHoldStaff($params);
                            }
                        }
                    } else {
                        if ($dateType == 16) {
                            //如果提交的 离职日期 在 提交日期下月的16号以后
                            echo $item['submitter_id'] . '-----' . $leaveDates[$k] . '-----' . date("Y-m-16",
                                    strtotime($createdAts[$k] . " +1 month")) . PHP_EOL;
                            if ($leaveDates[$k] >= date("Y-m-16", strtotime($createdAts[$k] . " +1 month"))) {
                                $params['type'] = "2";
                                //如果提交的 离职日期 在 提交日期下月的6号以后
                                $this->getDI()->get("logger")->write_log("syncHoldOnMonth data info:" . $params['staff_info_id'] . json_encode($params,
                                        JSON_UNESCAPED_UNICODE), "info");
                                (new HoldManageService())->synchronizeHoldStaff($params);
                            }
                        }
                    }
                }
            }
            (new LeaveManagerService())->syncHold($item['submitter_id']);
        }
    }

    private function releaseType2()
    {
        $month_end = date("Y-m", strtotime(" -2 month"));
        $month_end .= "-" . date("t", strtotime($month_end));
        $sql = "
                -- 获取所有提成hold
                select
                    count(*) as count,
                    group_concat(id) as id,
                    group_concat(hold_reason) as hold_reason,
                    staff_info_id,
                    group_concat(handle_progress) as handle_progress
                from
                    hold_staff_manage
                where 
                    type = 2 and staff_info_id not in (select DISTINCT(staff_info_id) from hold_staff_manage where ( month_end > '" . $month_end . "'  or month_end in ('0000-00-00', '1970-01-01')) and type = 2 and staff_info_id is not null)
                    group by staff_info_id
                ";
        $groupList = $this->getDI()->get("db_backyard")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $handleReason1 = "received_warning_letters";
        $handleReason2 = "off_3_days";
        foreach ($groupList as $item) {
            $handleProgress = explode(",", $item['handle_progress']);
            $handleProgress = array_unique($handleProgress);
            if (count($handleProgress) == 1 && $handleProgress[0] == 3) {
                $holdReasons = explode(",", $item['hold_reason']);
                $holdReasons = array_unique($holdReasons);
                if (
                    count($holdReasons) == 2 && in_array($handleReason1, $holdReasons) && in_array($handleReason2, $holdReasons)
                    || count($holdReasons) == 1 && (in_array($handleReason2, $holdReasons) || in_array($handleReason1, $holdReasons))
                ) {
                    //hold 原因 都是 收到警告书
                    // 和 旷工三天的hold
                    $handleProgress = explode(",", $item['handle_progress']);
                    $handleProgress = array_unique($handleProgress);
                    if (count($handleProgress) == 1 && $handleProgress[0] == 3) {
                        // 所有处理状态都是 已处理
                        $this->release_hold($item);
                    }
                } else {
                    // 都是已处理  判断处理意见是否都是 收到口头警告
                    $holdHistory = $this->getDI()->get("db_rbi")->fetchAll("--
                                select * from hold_history where id in (
                                        select max(id) from hold_history where hold_no in (" . $item['id'] .  ") group by hold_no
                        )", \Phalcon\Db::FETCH_ASSOC);
                    $allKeys = [];
                    foreach ($holdHistory as $value) {
                        $handleResult = json_decode($value['handle_result'], true);
                        $keys = array_unique(array_column($handleResult, "key"));
                        $allKeys = array_merge($allKeys, $keys);
                    }
                    $allKeys = array_unique($allKeys);
                    if (count($allKeys) == 1 && $allKeys[0] == 'verbal_warning_received') {
                        $this->release_hold($item);
                    }
                }
            }
        }
    }

    private function release_hold($item) {
        $release_time = gmdate('Y-m-d H:i:s',time() + $this->config->application->add_hour*3600);
        $ids = $item['id'];
        $sql = "
                    --
                    update 
                        hold_staff_manage 
                    set 
                        release_state = 2,
                        release_time = '{$release_time}',
                        update_at = '{$release_time}',
                        release_submit_id = 10003 
                    where
                        id in ({$ids}) ";

        //echo "holdTask: 释放 " . $item['staff_info_id']  . " " . $sql . " \n";
        $result = $this->getDI()->get("db_backyard")->execute($sql);
        $this->getDI()->get("logger")->write_log(['sql' => $sql, 'message' => 'holdTask-释放', 'staff_info_id' => $item['staff_info_id'], 'result' => $result], "info");

        //用于同步hirs
        $params = [
            'operator_id' => 10003,
            'type' => 2,
            'staff_info_id' => $item['staff_info_id'],//员工id
            'payment_markup' => "101",
            'stop_payment_type' => 2,//工资阻止发放类型 1,2 ；(1工资；2提成)
        ];
        $staff_hold_result = (new HoldManageService())->syncHrisStaffHoldSvc($params);
        $this->getDI()->get("logger")->write_log(['message' => 'release_hold_type_2', 'result' => $staff_hold_result, 'params' => $params], "info");
    }

    private function getCurrentDataAndNextMonthdate($day)
    {
        if ($day == 6) {
            return [
                date("Y-m-06"),
                date("Y-m-05", strtotime("+1 month")),
            ];
        } else if ($day == 16) {
            return [
                date("Y-m-16"),
                date("Y-m-15", strtotime("+1 month")),
            ];
        } else {
            throw new Exception("日期错误" . date("d"));
        }

    }

    /**
     * @throws BusinessException
     */
    private function getCurrentDataAndNextMonthdatecli($day)
    {
        if (!in_array(date('d', strtotime($day)), [6, 16])) {
            throw new BusinessException("日期错误" . date("d"));
        }

        return [$day, date("Y-m-d", strtotime($day . " +1 month -1 day"))];
    }

    /**
     * 修hold数据 只执行一次
     * @param $params
     */
    public function fix_holdAction($params)
    {

        $holds = HoldStaffManageModel::find([
            'conditions' => "type = 2 and release_state = 2 and release_submit_id = 10003 and release_time between '2022-04-08 12:00:00' and '2022-04-08 16:30:00' and month_end in ('0000-00-00', '1970-01-01') and is_delete = 0",
            'order' => 'hold_time asc',
        ])->toArray();

        $holdManageService = new \App\Services\HoldManageService();
        foreach ($holds as $hold) {
            $holdStaffModel = HoldStaffManageModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $hold['id']],
            ]);

            $hold_reason = $holdStaffModel->hold_reason;

            $holdStaffModel->release_state = 1;
            $holdStaffModel->release_submit_id = '';
            $holdStaffModel->save();

            if($hold_reason == 'off_3_days'){
                //3天未出勤停职员工->连续旷工三天以上
                $reason_id = 8;
            }elseif($hold_reason == 'fail_to_submit_public_funds'){
                //未回公款->未缴纳公款
                $reason_id = 9;
            }elseif($hold_reason == 'incomplete_resignation_procedures'){
                //BY申请离职->离职手续不全
                $reason_id = 10;
            }elseif($hold_reason == 'incomplete_resignation_procedures1'){
                $reason_id = 10;
            }elseif($hold_reason == 'incomplete_resignation_procedures2'){
                $reason_id = 10;
            }elseif ($hold_reason == 'received_warning_letters') {
                $reason_id = 18;
            } elseif ($hold_reason == 'suspicion_of_corruption/theft_of_public_property') {
                $reason_id = 5;
            }

            $params_flash = [
                'operator_id' => -1,//系统同步
                'type' => 1,//(1 hold ; 2 释放)
                'staff_info_id' => $hold['staff_info_id'],//员工id
                'payment_markup' => $reason_id,
                'stop_payment_type' => 2,//工资阻止发放类型 1,2 ；(1工资；2提成)
            ];
//                $svc_call = env('hr_rpc', 'http://192.168.0.230:8090/hr-svc');
            $ret = $holdManageService->getApiDatass('hris','','staff_hold', 'th', $params_flash);
            $this->logger->info("add hold api err " . json_encode($params_flash, JSON_UNESCAPED_UNICODE) .  " " .  json_encode($ret, JSON_UNESCAPED_UNICODE));
            echo "hold " . $hold['staff_info_id'] . " " . json_encode($params_flash, JSON_UNESCAPED_UNICODE). " " . json_encode($ret, JSON_UNESCAPED_UNICODE) .  "\r\n";

        }

    }

    //临时脚本，同步hold 菜单权限
    public function hold_permissionAction() {
        echo 'begin' . PHP_EOL;
        $list = HcmPermissionModel::find([
            'conditions' => "ancestry = :ancestry:",
            'bind' => [
                'ancestry' => 3013,
            ],
        ])->toArray();

        $hcm_hold_permission_arr = array_map('intval', array_column($list, 'id'));
        array_push($hcm_hold_permission_arr, 3013);

        echo '===========================给角色配置菜单开始===========================' . PHP_EOL;
        $fbi_role_list = RoleMenusModel::find([
            'conditions' => "menu_id = :menu_id:",
            'bind' => [
                'menu_id' => 228,
            ],
        ])->toArray();

        if(!empty($fbi_role_list)) {
            foreach ($fbi_role_list as $key => $value) {
                $hcm_role_permission = HcmRolePermissionModel::findFirst([
                    'conditions' => "role_id = :role_id:",
                    'bind' => [
                        'role_id' => $value['role_id'],
                    ],
                ]);
                if(!empty($hcm_role_permission)) {
                    $permission_ids = explode(',', $hcm_role_permission->permission_ids);
                    echo $value['role_id'] . '-----角色id原菜单id：' . implode(',', $permission_ids) . PHP_EOL;
                    $permission_ids = array_unique(array_merge($permission_ids, $hcm_hold_permission_arr));
                    $hcm_role_permission->permission_ids = implode(',', $permission_ids);
                    echo $value['role_id'] . '-----角色id追加后菜单id：' . $hcm_role_permission->permission_ids . PHP_EOL;
                    $r = $hcm_role_permission->save();
                    echo $value['role_id'] . '-----角色id增加菜单，保存结果:'. $r . PHP_EOL;
                } else {
                    echo $value['role_id'] . '角色新增菜单' . PHP_EOL;
                    $hcm_role_permission = new HcmRolePermissionModel();
                    $hcm_role_permission->role_id = $value['role_id'];
                    $hcm_role_permission->permission_ids = implode(',', $hcm_hold_permission_arr);
                    $r = $hcm_role_permission->save();
                    echo $value['role_id'] . '-----角色id新增菜单,保存结果:' . $r . PHP_EOL;
                }
            }
        } else {
            echo 'fbi 没有找到角色配置的hold菜单' . PHP_EOL;
        }
        echo '===========================给角色配置菜单结束===========================' . PHP_EOL;
        echo PHP_EOL;
        echo PHP_EOL;
        echo '===========================给工号配置菜单开始===========================' . PHP_EOL;
        $fbi_staff_menu_list = RoleStaffMenusModel::find([
            'conditions' => "menu_id = :menu_id:",
            'bind' => [
                'menu_id' => 228,
            ],
        ])->toArray();
        if(!empty($fbi_staff_menu_list)) {
            foreach ($fbi_staff_menu_list as $key => $value) {
                $staff_permission = HcmStaffPermissionModel::findFirst([
                    'conditions' => "staff_id = :staff_id:",
                    'bind' => [
                        'staff_id' => $value['staff_info_id'],
                    ],
                ]);
                if(!empty($staff_permission)) {
                    $permission_ids = explode(',', $staff_permission->permission_ids);
                    echo $value['staff_info_id'] . '-----原菜单id：' . implode(',', $permission_ids) . PHP_EOL;
                    $permission_ids = array_unique(array_merge($permission_ids, $hcm_hold_permission_arr));
                    $staff_permission->permission_ids = implode(',', $permission_ids);
                    echo $value['staff_info_id'] . '-----追加后菜单id：' . $staff_permission->permission_ids . PHP_EOL;
                    $r = $staff_permission->save();
                    echo $value['staff_info_id'] . '-----工号增加菜单,保存结果:' . $r . PHP_EOL;
                } else {
                    echo $value['staff_info_id'] . '-----在hcm没有按照工号配置菜单，跳过不配置' . PHP_EOL;
                }
            }
        } else {
            echo 'fbi 没有找到工号配置的hold菜单' . PHP_EOL;
        }
        echo '===========================给工号配置菜单结束===========================' . PHP_EOL;
        echo 'end' . PHP_EOL;
    }

    /**
     * @Single
     * 释放警告信提成hold
     * @param $params
     * @return void
     * @throws ValidationException
     */
    public function releaseWarningAction($params)
    {
        $staff_info_ids = [];
        if(isset($params[0])){
            $staff_info_ids = explode(',',$params[0]);
        }
        $service = new HoldManageService();
        $service->releaseWarningHold($staff_info_ids);
    }


    public function resignEarlyExportAction(){
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }


        $this->echoInfoLog(' exportAction start', true);
        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog(' exportAction params :'.json_encode($params, JSON_UNESCAPED_UNICODE),
            true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);
        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;
        $type      = $excelTask->type;

        $t = BaseService::getTranslation($params['lang']);

        $service = reBuildCountryInstance(new HoldManageService());
        $params['page'] = 1;
        $params['size'] = 500;
        //获取数据
        $list = [];
        while (true){
            $data = $service->getResignEarly((array)$params);
            if(empty($data['list'])){
                break;
            }
            foreach ($data['list'] as $v) {
                if(isCountry('PH')){
                    $list[] = [
                        $v['staff_info_id'],
                        $v['staff_name'],
                        $v['job_name'],
                        $v['department_name'],
                        $v['store_name'],
                        $v['work_country_text'],
                        $v['leave_source_text'],
                        $v['leave_reason_text'],
                        $v['hold_advance_resign_days'],
                        $v['leave_date'],
                        $v['apply_resign_time'],
                    ];
                }else{
                    $list[] = [
                        $v['staff_info_id'],
                        $v['staff_name'],
                        $v['job_name'],
                        $v['department_name'],
                        $v['store_name'],
                        $v['work_country_text'],
                        $v['leave_source_text'],
                        $v['leave_reason_text'],
                        $v['leave_date'],
                        $v['apply_resign_time'],
                    ];
                }
            }
            $params['page'] ++;
        }

        if (isCountry('PH')) {
            $title = [
                $t['staff_info_id'],                     //(工号)
                $t['name'],                              //姓名
                $t['staff_job_title'],                   //职位
                $t['department'],                        //部门
                $t['sys_store_name'],                    //网点
                $t['working_country'],                   //工作所在国家
                $t['leave_source'],                      //离职来源
                $t['leave_reason'],                      //离职原因
                $t['leave_standard'],                    //离职规则
                $t['leave_date'],                        //离职日期
                $t['application_date'],                  //申请日期

            ];
        } else {
            $title = [
                $t['staff_info_id'],                     //(工号)
                $t['name'],                              //姓名
                $t['staff_job_title'],                   //职位
                $t['department'],                        //部门
                $t['sys_store_name'],                    //网点
                $t['working_country'],                   //工作所在国家
                $t['leave_source'],                      //离职来源
                $t['leave_reason'],                      //离职原因
                $t['leave_date'],                        //离职日期
                $t['application_date'],                  //申请日期

            ];
        }

        $updatePath = 'hold-manage/'.date('Y-m-d').'/';
        $result     = (new BllService())->exportExcels($title, $list, $updatePath, $file_name);
        $res        = (new ExcelService())->updateTask($task_id, $file_name, $result['url'], $type);
        $this->echoInfoLog('exportAction end', true);
    }

    /**
     * 异步释放Hold
     * @return void
     */
    public function asyncReleaseHoldAction()
    {
        $redis              = $this->getDI()->get("redis");
        $countryCode        = ucfirst(env('country_code'));
        $certificateService = 'App\Modules\\' . $countryCode . '\Services\HoldManageService';
        if (class_exists($certificateService)) {
            $service = new $certificateService;
        } else {
            $service = new HoldManageService();
        }

        while ($redis_data = $redis->rpop(RedisListEnums::ASYNC_RELEASE_HOLD)) {
            if (!empty($redis_data)) {
                sleep(3);
                $this->echoInfoLog('asyncReleaseHoldAction Start '.json_encode($redis_data));
                $redis_data = json_decode($redis_data, true);

                $result = $service->syncReleaseHoldStaff($redis_data);
                $this->echoInfoLog('asyncReleaseHoldAction params '.json_encode($redis_data) . ',result:' . json_encode($result));
            }
        }
        $this->echoInfoLog('asyncReleaseHold done!');
    }

    public function testAction()
    {
        //[{
        //	"locale": "zh-CN"
        //}, {
        //	"staff_info_id": 27967,
        //	"hold_reason": "incomplete_resignation_procedures",
        //	"hold_source": 0,
        //	"handle_hold": 1,
        //	"operator_id": 17245
        //}]
        $params = [
            'staff_info_id' => 27967,
            'hold_reason' => "incomplete_resignation_procedures",
            'hold_source' => 0,
            'handle_hold' => 1,
            'operator_id' => 17245,
        ];
        $staffService = new HoldManageService();
        $staffService->pushAsyncReleaseHold($params);
    }
}

