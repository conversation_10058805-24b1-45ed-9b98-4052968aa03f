<?php

use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrProbationModel;
use App\Services\ExcelService;
use App\Services\HrProbationContractService;
use App\Services\HrProbationContractThreeService;
use App\Services\ProbationService;
use App\Services\HrProbationResignService;
use App\Models\backyard\HrProbationResignModel;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;


/**
 * 试用期员工数据导出脚本 php app/cli.php probation exportProbation
 * 月薪制合同工(PH)-导出列表  php app/cli.php probation   exportContractProbation
 * 月薪制合同工(PH)-发送转正评估 php app/cli.php probation sendConfirmationEvaluation
 * 月薪制合同工(PH)-评估即将超时提醒 php app/cli.php probation timeoutReminder
 * 月薪制合同工(PH)-未评估超时处理 php app/cli.php probation timeout
 * 月薪制合同工(PH)-估通过或评估超时 转正式员工 php app/cli.php probation changeFormal
 * 补全历史的评估状态 评估结果 php app/cli.php probation  handleHistoryStatus
 * 变更评估中处理人 （离职 更换上级） php app/cli.php probation  changeExaminationHandleStaff
 */
class ProbationTask extends BaseTask
{

    /**
     * 补全历史的评估状态 评估结果
     */
    public function handleHistoryStatusAction($params){
        $staffIds = empty($params[0]) ? [] : explode(',', $params[0]);
        (new \App\Services\ProbationService())->handleHistoryStatus($staffIds);
    }

    /**
     * @Single
     * 变更评估中处理人 （离职 更换上级）
     */
    public function changeExaminationHandleStaffAction(){
        $service = new ProbationService();
        $service->changeExaminationHandleStaff('first');
        $service->changeExaminationHandleStaff('second');
    }
    /**
     * Flash试用期员工数据导出入口
     * @return void
     * @throws Exception
     */
    public function exportProbationAction()
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('exportProbationAction start');

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog(__CLASS__.' params :'.json_encode($params, JSON_UNESCAPED_UNICODE));

        BaseService::setLanguage($params['lang']);
        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);
        if (empty($excelTask)) {
            $this->echoInfoLog('task not isset :'.$task_id);
            exit();
        }
        $params['file_name'] = $excelTask->file_name;
        // 执行数据导出
        $oss_path = reBuildCountryInstance((new ProbationService()))->export($params);

        // 更新为处理完成状态
        $excelTask->path      = $oss_path;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('exportProbationAction end');
    }

    /**
     * 导出月薪制合同工列表 ( only ph)
     * @throws Exception
     */
    public function exportContractProbationAction(): string
    {
        [$taskObj, $params] = $this->getHcmExcelTack('probation exportContractProbation');
        if (empty($taskObj)) {
            return 'not task';
        }
        $service = (new HrProbationContractService());
        $service::setLanguage($params['lang'] ?? 'en');
        $returnData = [];
        $start      = 1;
        $sizeLen    = 500;
        while ($start) {
            $params['page']     = $start;
            $params['pagesize'] = $sizeLen;
            $data               = $service->getList($params);
            if (empty($data['rows'])) {
                break;
            }
            $this->echoInfoLog("handing $start $sizeLen");
            ++$start;
            $tmpData = [];
            foreach ($data['rows'] as $row) {
                $tmpData[] = [
                    $row['staff_info_id'],
                    $row['name'],
                    $row['node_department_name'],
                    $row['job_title_name'],
                    $row['state_name'],
                    $row['working_country'],
                    $row['region_name'],
                    $row['piece_name'],
                    $row['sys_store_name'],
                    $row['manage_staff_name'],
                    $row['hire_date'],
                    $row['formal_at'],
                    $row['status_text'],
                    $row['first_deadline_date'],//评估期限
                    $row['first_audit_status_name'],//评估状态
                    $row['first_status_name'],//评估结果
                ];
            }
            $returnData = array_merge($returnData, $tmpData);
        }
        $this->echoInfoLog("total" . count($returnData));
        $header = $service->getExportHeader();
        $return = $service->exportExcel($header, $returnData, $taskObj->file_name);
        if (empty($return['data'])) {
            return 'error';
        }
        $flashOss = new FlashOss();
        $object   = 'probation/' . date('ymd') . '/' . $taskObj->file_name;
        $flashOss->uploadFile($object, $return['data']);
        $taskObj->path = $object;
        $taskObj->update();
        echo 'END' . PHP_EOL;
        return 'success';
    }


    /**
     * @Single
     * 月薪制合同工PH 发送转正评估
     * 每天凌晨 20 分
     * @return void
     * @throws \Exception
     */
    public function sendConfirmationEvaluationAction($params)
    {
        $service                              = (new HrProbationContractService());
        $service->currentProbationChannelType = HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT;
        $hireDate                             = date('Y-m-d', strtotime('-' . ($service->hireDate - 1) . ' days'));
        if (!empty($params[0])) {
            $hireDate = $params[0];
        }
        $staffIds = [];
        if (!empty($params[1])) {
            $staffIds = explode(',', $params[1]);
        }
        $hrProbations = $service->getConfirmationEvaluationStaffs($hireDate,
            HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT, $staffIds);
        if (empty($hrProbations)) {
            echo '无需要处理的数据' . PHP_EOL;
            return;
        }
        $this->echoInfoLog($hireDate . ' probation sendConfirmationEvaluation 转正评估员工 ' . implode(',',
                array_column($hrProbations, 'staff_info_id')));
        $service->sendConfirmationEvaluation($hrProbations, $service->hireDate);
    }

    /**
     * @Single
     * 月薪制合同工ph 评估即将超时提醒
     * 早上9:00
     * @param $params
     * @return void
     */
    public function timeoutReminderAction($params)
    {
        $deadline = date('Y-m-d', strtotime('+1 day'));
        if (!empty($params[0])) {
            $deadline = $params[0];
        }
        $service = (new HrProbationContractService());
        $service->timeoutReminder($deadline);
    }

    /**
     * @Single
     * 月薪制合同工ph 未评估超时处理
     * 每天凌晨 1点
     * @param $params
     * @return void
     */
    public function timeoutAction($params)
    {
        $timeoutTime = date('Y-m-d');
        if (!empty($params[0])) {
            $timeoutTime = $params[0];
        }
        $service    = (new HrProbationContractService());
        $staffIds   = $service->timeoutHandle($timeoutTime, HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT);
        $staffIdsV3 = $service->timeoutHandle($timeoutTime, HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3);
        $this->echoInfoLog('probation timeoutAction 评估超时处理 ' . json_encode([$timeoutTime, $staffIds]));
        $this->echoInfoLog('probation v3 timeoutAction 评估超时处理 ' . json_encode([$timeoutTime, $staffIdsV3]));
    }

    /**
     * @Single
     * 月薪制合同工ph 每月1号和16号 评估通过转正式员工
     * @return void
     */
    public function changeFormalAction($params)
    {
        $D = (int)date('d');
        if (RUNTIME == 'pro' && !in_array($D, [1, 16]) && empty($params[0])) {
            echo '不是合理的执行日期' . PHP_EOL;
            return;
        }
        $staffIds = [];
        if (!empty($params[0])) {
            $staffIds = explode(',', $params[0]);
        }
        $service  = (new HrProbationContractService());
        $contractStaffMap = $service->changeFormal($staffIds, HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT);
        $this->echoInfoLog(' probation changeFormalAction 处理通过的评估员工 ' . implode(',', array_keys($contractStaffMap)));

        $staffV3Map = $service->changeFormal($staffIds, HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3);
        $staffIdsV3 = (new HrProbationContractThreeService())->createToFormal($staffV3Map);
        $this->echoInfoLog(' probation v3 changeFormalAction 处理通过的评估员工 ' . implode(',', $staffIdsV3));
    }
    /**
     * 发送离职信函 MY
     */
    public function sendResignAction()
    {
        $service =  reBuildCountryInstance(new HrProbationResignService());

        $resignList = $service->getResignList([
            'status' => HrProbationResignModel::STATUS_WAIT,
        ]);

        if (empty($resignList)) {
            $this->echoInfoLog('data Empty Success');
            return;
        }

        foreach ($resignList as $info) {
            $res = $service->sendResignMsg($info);
            if ($res == ErrCode::SUCCESS) {
                $this->echoInfoLog('Success-'.$info['staff_info_id']);
            } else if ($res == ErrCode::SYSTEM_ERROR) {
                $this->echoInfoLog('Send System Error-'.$info['staff_info_id']);
            } else {
                $this->echoInfoLog('Send Business Error-'.$info['staff_info_id']);
                //调整记录
                $resignInfoObj = HrProbationResignModel::findFirst(
                    [
                        'conditions' => 'id = :id: AND is_deleted = :is_deleted:',
                        'bind'       => [
                            'id'         => $info['id'],
                            'is_deleted' => GlobalEnums::NO_DELETED,
                        ],
                    ]
                );

                if ($resignInfoObj) {
                    $resignInfoObj->status = $res;
                    $resignInfoObj->save();
                }
            }
        }

        $this->echoInfoLog('Success');
    }

    public function fixFormalAction($params)
    {
        $staff = array_filter(explode(',',$params[0]));
        $s     = new HrprobationContractService();
        $s->fixFormal($staff);
    }

    /**
     * 检测转正评估数据异常
     * @return void
     */
    public function checkDataAction()
    {
        $service = new \App\Services\ProbationService();
        if (isCountry('PH')) {
            $service->checkData(true);
        }
        $service->checkData();
    }

    /**
     * 补发非一线一阶段和二阶段完成确认消息  th
     * @return void
     */
    
    public function compensateSendStageDoneMessageAction($params)
    {
        if (empty($params[0])) {
            echo '请输入员工ID';
            return;
        }
        (new \App\Services\ProbationService())->compensateSendStageDoneMessage($params[0]);
    }
    
    /**
     * 刷试用期考核权限 - 同步上级权限
     * @return void
     */
    public function syncPermissionAction()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['p' => HrProbationModel::class]);
        $builder->join(\App\Models\backyard\HrStaffInfoModel::class, 'p.staff_info_id = staff.staff_info_id', 'staff');
        $builder->columns([
            'staff.manger as manger',
        ]);
        $builder->andWhere('staff.manger > 0 and p.probation_channel_type = 2');
        $builder->groupBy('staff.manger');
        $res =  $builder->getQuery()->execute()->toArray();
        $staffIds = $res ? array_column($res , 'manger') : [];
        if (empty($staffIds)) {
            return false;
        }
        // SELECT * from hcm_permission WHERE id in (4, 90029, 90060, 90066, 90065, 90064, 90063, 90062, 90061)
        $permissionIds = [4, 90029, 90060, 90065, 90064, 90062, 90061];
        foreach ($staffIds as $staffId) {
            $staffPermission = \App\Models\backyard\HcmStaffPermissionInfoModel::find(
                [
                    'conditions' => 'is_deleted = :is_deleted: and staff_info_id = :staff_info_id:',
                    'bind'       => [
                        'staff_info_id' => $staffId,
                        'is_deleted'    => GlobalEnums::NO_DELETED,
                    ],
                ]
            )->toArray();
            $staffPermissionIds = array_column($staffPermission, 'permission_id');
            $missingPermissions = array_diff($permissionIds, $staffPermissionIds);
            $data = [];
            foreach ($missingPermissions as $permissionId) {
                $data[] = [
                    'staff_info_id' => $staffId,
                    'permission_id' => $permissionId,
                    'operator_id'   => 0
                ];
            }
            if ($data) {
                (new \App\Models\backyard\HcmStaffPermissionInfoModel())->batch_insert($data);
            }
            // 记录权限变更日志
            $this->logger->info(
                [
                    'func'  => 'syncPermission',
                    'title' => '同步上级试用期考核权限成功',
                    'params' => [
                        'staff_info_id' => $staffId,
                        'permission_ids' => $missingPermissions,
                    ],
                ]
            );
        }
    }

}