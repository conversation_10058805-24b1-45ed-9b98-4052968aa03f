<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\CacheListKeyEnums;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\RedisListEnums;
use App\Library\Enums\StaffEnums;
use App\Library\Enums\LangEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\PasswordHash;
use App\Library\RedisClient;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AttendanceDataV2Model;
use App\Models\backyard\AttendanceWhiteListModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\FranchiseeManagePieceModel;
use App\Models\backyard\FranchiseeManageRegionModel;
use App\Models\backyard\HrHcModel;
use App\Models\backyard\HrInterviewModel;
use App\Models\backyard\HrInterviewOfferModel;
use App\Models\backyard\HrInterviewSubscribeModel;
use App\Models\backyard\HrJdModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrResumeModel;
use App\Models\backyard\HrSmsLogModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffFranchiseeManagePieceModel;
use App\Models\backyard\HrStaffFranchiseeManageRegionModel;
use App\Models\backyard\HrStaffInfoExtendModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\HrVillageModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffDefaultRestDayModel;
use App\Models\backyard\StaffInfoAuditCheckModel;
use App\Models\backyard\StaffInfoStagingModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffShiftModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\VehicleInfoModel;
use App\Models\bi\RoleMenusModel;
use App\Models\bi\RoleStaffMenusModel;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysDistrictModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\fle\StaffAccountModel;
use App\Models\fle\StaffInfoModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffManageRegionModel;
use App\Models\backyard\HrStaffManagePieceModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\HrStaffManageStoreCategoryModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysManagePieceModel;
use App\Repository\HrStaffInfoPositionRepository;
use App\Repository\HrStaffInfoRepository;
use App\Repository\StaffInfoAuditCheckRepository;
use App\Repository\SysDepartmentRepository;
use App\Repository\SysStoreRepository;
use SchedulingSuggestionTask;
use App\Models\backyard\RolesModel;


class StaffService extends BaseService
{
    public static $s_v_param = [
        'staff_info_id'   => 'Required|IntGt:0',
        'department_id'   => 'Required|IntGt:0',
        'job_title_id'    => 'Required|IntGt:0',
        'manger'          => 'Required|IntGt:0',
        'nationality'     => 'Required|IntGt:0',
        'working_country' => 'Required|IntGt:0',
        'is_whitelist'    => 'Required|IntIn:0,1',
        'shift_id'        => 'Required|IntGt:0', //班次ID
    ];

    const RESUME_CALL_NAME_MR = 1; //简历称呼-先生
    const RESUME_SEX_MALE = 1; //简历性别-男性
    const RESUME_SEX_FEMALE = 2; //简历性别-女性
    const RESUME_SEX_MISS = 3; //性别称呼-小姐

    // tpl
    const TPL_1 = [
        "th_applicant_email"        => "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช เอ็กเพรส \r\n คุณ :  %s %s \r\n โทร : %s\r\n ตำแหน่ง : %s \r\n แผนก :  %s \r\n"
            . "ประจำสาขา : %s \r\n วันที่นัดสัมภาษณ์ : %s \r\n สถานที่นัดสัมภาษณ์ : %s \r\n  ติดต่อผู้รับผิดชอบสาขา : %s \r\n",
        "en_applicant_online_email" => "Dear %s: [%s %s] \r\n \r\n Good day. We are pleased to inform that you have been shortlisted for an interview for [%s] with [%s]\r\n \r\n"
            . "Please find the appended information below for your perusal:- \r\n \r\n Flash Express Co., Ltd：%s \r\n 【Date / Time】%s \r\n \r\n"
            . "【Interview Platform】 \r\n 【Interview Access】 \r\n 【Contact Person】%s：%s \r\n 【Interviewers】%s \r\n 【Registration Link】%s \r\n \r\n"
            . "Please note that you would be able to access to the meeting via the website, or you could consider downloading the application as well. \r\n \r\n"
            . "Should you require further clarification, please feel free to contact us.\r\n \r\n"
            . "Thank you and we look forward to meeting you soon.\r\n \r\n"
            . "Best regards,\r\n",
        "en_applicant_email"        => "Dear %s  [%s %s] \r\n \r\n Good day. We are pleased to inform that you have been shortlisted for an interview for [%s] with [%s] \r\n \r\n"
            . "Please find the appended information below for your perusal:- \r\n \r\n 【Flash Express Co., Ltd】%s \r\n 【Location】%s \r\n"
            . "【Date / Time】%s \r\n 【Interview Venue】 \r\n 【Contact Person】%s %s \r\n 【Interviewers】%s \r\n 【Documents to bring along】\r\n 1. Identification Card / Passport \r\n 2. Testimonials (if any) \r\n 3. Latest Payslip \r\n"
            . "【Registration Link】%s \r\n \r\n Please kindly contact the contact person once you have reached our office; do note that you will be required to register yourself at our security section (located at Level 7) prior to the interview session. \r\n \r\n"
            . "Should you require further clarification, please feel free to contact us.\r\n \r\n Thank you and we look forward to meeting you soon.\r\n \r\n Best regards,\r\n",
    ];

    const TPL_2 = [
        "th_applicant_email"        => "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช เอ็กเพรส \r\n คุณ :  %s %s \r\n โทร : %s\r\n ตำแหน่ง : %s \r\n แผนก :  %s \r\n"
            . "ประจำสาขา : %s \r\n วันที่นัดสัมภาษณ์ : %s \r\n สถานที่นัดสัมภาษณ์ : %s \r\n  ติดต่อผู้รับผิดชอบสาขา : %s \r\n",
        "en_applicant_online_email" => "Dear %s [%s %s] \r\n \r\nGood day. We are pleased to inform you that you are shortlisted for an interview for the position of [%s] with [%s]\r\n \r\n"
            . "Please find the appended information below for your perusal:- \r\n \r\nFlash Express Co., Ltd：%s \r\n【Date / Time】%s \r\n"
            . "【Interview Platform】 \r\n【Interview Link】 \r\n【Contact Person】%s：%s \r\n【Interviewer】%s\r\n"
            . "%s\r\n"
            . "Kindly download VOOV app in advance. \r\n \r\n"
            . "Should you require further clarification, please feel free to contact us.\r\n \r\n"
            . "Thank you and we are looking forward to meeting you soon.",
        "en_applicant_email"        => "Dear %s  [%s %s] \r\n \r\nGood day. We are pleased to inform you that you are shortlisted for an interview for the position of [%s] with [%s] \r\n \r\n"
            . "Please find the appended information below for your perusal:- \r\n \r\n【Flash Express Co., Ltd】%s \r\n【Location】%s \r\n"
            . "【Date / Time】%s \r\n【Interview Venue】\r\n【Contact Person】%s %s \r\n【Interviewer】%s \r\n【Documents to bring along】\r\n 1. Identification Card / Passport \r\n 2. Testimonials (if any) \r\n 3. Latest Payslip \r\n"
            . "%s\r\n \r\n" .
            "Kindly contact the contact person once you have reached our office; do note that you will be required to register yourself prior to the interview session. \r\n \r\n"
            . "Should you require further clarification, please feel free to contact us.\r\n \r\nThank you and we are looking forward to meeting you soon.",

    ];

    /**
     * 更新密码
     * @param $locale
     * @param array $inputData
     * @return false
     * @throws BusinessException
     */
    public function updateStaffPassword($locale, array $inputData)
    {
        BaseService::setLanguage($locale['locale']??getCountryDefaultLang());
        if (!env('break_away_from_ms')) {
            throw new BusinessException('break_away_from_ms is close !');
        }
        if (empty($inputData['staff_id'])) {
            throw new BusinessException('need staff_id ');
        }
        if (empty($inputData['new_password'])) {
            throw new BusinessException('need new_password ');
        }

        if (!is_numeric($inputData['new_password']) || strlen($inputData['new_password']) != 6) {
            throw new BusinessException(' new password wrong format ');
        }

        $hash           = new PasswordHash(10, false);
        $password       = $hash->HashPassword($inputData['new_password']);
        $staffInfoModel = StaffInfoModel::findFirst($inputData['staff_id']);
        if (empty($staffInfoModel)) {
            return false;
        }
        $staffInfoModel->encrypted_password = $password;
        $staffInfoModel->save();
        
        //更新表信息
        $up_sql = "update staff_account set clientid = '' where staff_info_id = :staff_id ";
        $this->getDI()->get('db_fle')->execute($up_sql,['staff_id'=>$inputData['staff_id']]);

        //通知ms员工合同未签署
        $sendData['jsonCondition']    = json_encode(['staff_id' => intval($inputData['staff_id'])]);
        $sendData['handleType']       = RocketMQ::TAG_STAFF_UPDATE_PASSWORD;
        $sendData['shardingOrderKey'] = $inputData['staff_id'];
        $rmq                          = new RocketMQ('staff-update-password');
        $rmq->setShardingKey($inputData['staff_id']);
        return $rmq->sendOrderlyMsg($sendData);//有序
    }
    //
    public function doSendSmsAndMail($params_staff_info_id = 0)
    {
        if (empty($params_staff_info_id)) {
            $data = $this->getDI()->get('redis')->rpop(CacheListKeyEnums::RESET_PASSWORD);
            if (empty($data)) {
                return false;
            }
            $staffData     = json_decode($data, true);
            $staff_info_id = $staffData['staff_info_id'];
        } else {
            $staff_info_id = $params_staff_info_id;
        }

        $passwordNumber = mt_rand(100000, 999999);

        if (RUNTIME == 'dev') {
            $passwordNumber = 123456;
        }
        if (RUNTIME == 'tra') {
            $passwordNumber = 666666;
        }

        $hash           = new PasswordHash(10, false);
        $password       = $hash->HashPassword($passwordNumber);
        $staffInfoModel = StaffInfoModel::findFirst($staff_info_id);
        if (empty($staffInfoModel)) {
            return false;
        }
        $staffInfoModel->encrypted_password = $password;
        $staffInfoModel->save();


        $lang = $this->getAcceptLanguage($staff_info_id);
        //发送邮件
        $t       = self::getTranslation($lang);
        $title   = $t->_('reset_password_title');
        $content = $t->_('reset_password_content', ['staff_info_id' => $staff_info_id, 'password' => $passwordNumber]);
        $email   = [];
        if (!empty($staffInfoModel->email)) {
            $email[] = $staffInfoModel->email;
        }
        if (!empty($staffInfoModel->personal_email)) {
            $email[] = $staffInfoModel->personal_email;
        }
        if (!empty($email)) {
            //发送邮件
            $mail_result = (new \App\Library\Mailer((new BiMail)->initConfig()))->send($email, $title, $content);
        }
        if (!empty($data['mobile'])) {
            $ret      = new ApiClient('sms_rpc', '', 'send', 'th');
            $rpcParam = [
                'src'    => 'hcm_password',
                'mobile' => $staffInfoModel->mobile,
                'msg'    => $content,
                'type'   => 0,
                'code'   => 'th',
                'delay'  => 0,
            ];
            //viber 发送
            if (isCountry('PH')) {
                $rpcParam['nation'] = 'PH';
                $rpcParam['type'] =  0;// 文本消息， 固定类型
                $rpcParam['service_provider'] =  9;// 服务商 Viber 固定值
            }
            $ret->setParams([$rpcParam]);
            $sms_result = $ret->execute();
        }
        $this->logger->info([$staff_info_id, $mail_result ?? false, $sms_result ?? false]);
        return $passwordNumber;
    }


    public function resetPassword($staff_info_id)
    {
        return $this->getDI()->get('redis')->lpush(CacheListKeyEnums::RESET_PASSWORD, json_encode(['staff_info_id'=>$staff_info_id]));
    }

    /**
     * 颁发工号
     * @param $type
     * @param $formal
     * @return mixed
     */
    public function generateId($type, $formal)
    {
        $cacheKey = Enums\RedisEnums::FORMAL_EMPLOYEES_GENERATE_ID_KEY;
        //网点的 && 非正式的 走外协
        if ($type == 1 && $formal == 0) {
            $cacheKey = Enums\RedisEnums::OUTSOURCED_EMPLOYEES_GENERATE_ID_KEY;
        }

        $this->checkAndSetCurrentMaxStaff($cacheKey);

        return RedisClient::getInstance()->getClient()->incr($cacheKey);
    }

    /**
     * 获取staff_info表对应类型工号最大ID
     * @param $cacheKey
     * @return void
     */
    private function checkAndSetCurrentMaxStaff($cacheKey): void
    {
        if ($this->getCache($cacheKey)) {
            return;
        }
        $sql = "select max(id) as id  from staff_info where formal =:formal";
        //todo 改成主库
        $model = $this->getDI()->get('db_fle')->query($sql,
            ['formal' => $cacheKey == Enums\RedisEnums::OUTSOURCED_EMPLOYEES_GENERATE_ID_KEY ? 0 : 1]);
        $info  = $model->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($cacheKey == Enums\RedisEnums::OUTSOURCED_EMPLOYEES_GENERATE_ID_KEY) {
            $info['id'] = max(300000, $info['id']);
        }
        RedisClient::getInstance()->getClient()->set($cacheKey, $info['id']);
    }

    /**
     * fle库 员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getFleStaffInfo($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staff_info = StaffInfoModel::findFirst([
            'conditions' => 'id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ]);
        if (empty($staff_info)){
            return [];
        }
        $staff_info = $staff_info->toArray();
        $staff_info['department_name'] = $this->showDepartmentName($staff_info['department_id']);
        $staff_info['job_title_name'] = $this->showJobTitleName($staff_info['job_title']);
        return $staff_info;
    }

    /**
     * 员工base信息
     * @param $staff_info_id
     * @return array
     */
    public function getHrStaffInfo($staff_info_id): array
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ]);
        if(empty($staff_info)){
            return [];
        }

        $positions = HrStaffInfoPositionModel::find(
            [
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                ],
                'order'      => 'position_category',
            ]
        )->toArray();
        $info      = $staff_info->toArray();

        $info['position_category'] = array_column($positions, 'position_category');
        return $info;
    }

    /**
     * 员工items信息list
     * @param $staff_info_id
     * @return array
     */
    public function getHrStaffItems($staff_info_id)
    {
        $items = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ])->toArray();
        $items = array_column($items, 'value', 'item');
        return $items;
    }

    /**
     * 员工角色list
     * @param $staff_info_id
     * @return array
     */
    public function getHrStaffRoles($staff_info_id)
    {
        $position = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ])->toArray();
        return $position;
    }

    /**
     * 员工身份证附件
     * @param $staff_info_id
     * @return array
     */
    public function getHrStaffIdentityAnnex($staff_info_id)
    {
        $annex = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1 and type = :type:',
            'bind'       => [
                1 => $staff_info_id,
                'type' => HrStaffAnnexInfoModel::TYPE_ID_CARD,
            ],
        ]);
        return empty($annex) ? [] : $annex->toArray();
    }

    /**
     * 获取员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffView($staff_info_id)
    {
        $info = $this->getHrStaffInfo($staff_info_id);
        if (empty($info)) {
            return [];
        }

        $sysService = new SysService();
        $items      = $this->getHrStaffItems($staff_info_id);
        $role_list  = $this->getHrStaffRoles($staff_info_id);

        $info['bank_no_name'] = $items['BANK_NO_NAME'] ?? '';
        $info['sex_name']     = self::$t->_(HrStaffInfoModel::$sex_list[$info['sex']]);
        $info['state_name']   = self::$t->_("staff_state_{$info['state']}");
        //职级
        $jobTitleGrade                = array_column($sysService->JobTitleGrade(), 'label', 'value');
        $info['job_title_grade_name'] = isset($jobTitleGrade[$info['job_title_grade_v2']]) ? $jobTitleGrade[$info['job_title_grade_v2']] : '';
        $info['bank_name']            = $items['BANK_NAME'] ?? 'TMB';
        $info['staff_car_type']       = $items['CAR_TYPE'] ?? null;
        $info['staff_car_no']         = $items['CAR_NO'] ?? null;
        $info['payment_state']        = $info['payment_state'] ?? 1;
        $info['oil_card_deposit']     = $info['oil_card_deposit'] ?? 0;
        $info['driver_license']       = $items['DRIVER_LICENSE'] ?? '';
        $info['outsourcing_type']     = $items['OUTSOURCING_TYPE'] ?? '';
        $info['equipment_cost']       = $items['EQUIPMENT_COST'] ?? '';

        $info['patment_markup_other'] = $items['PAYMENT_MARKUP_OTHER'] ?? '';
        $info['graduate_school']      = $items['GRADUATE_SCHOOL'] ?? '';
        $info['major']                = $items['MAJOR'] ?? '';

        if ($info['sys_store_id'] == '-1') {
            $info['store_name'] = GlobalEnums::HEAD_OFFICE;
        } else {
            $store              = (new SysStoreService())->getStoreById($info['sys_store_id']);
            $info['store_name'] = $store['name'] ?? '';
        }

        $job_title              = (new HrJobTitleService())->getJobTitleDetail($info['job_title']);
        $info['job_title_name'] = $job_title['job_name'] ?? ''; //职位名称

        $department                            = (new SysDepartmentService())->getDepartmentDetail($info['node_department_id']);
        $info['node_department_name']          = $department['name'] ?? ''; //网点名称
        $info['birthday']                      = $items['BIRTHDAY'] ?? '';

        $identity_annex_result = (new HrStaffAnnexInfoService())->getHrStaffAnnexByParams([
            'staff_info_id' => $staff_info_id,
        ]);
        $identity_annex        = array_column($identity_annex_result, null, 'type');

        if (empty($identity_annex[HrStaffAnnexInfoModel::TYPE_ID_CARD])) {
            $info['audit_state'] = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
        }

        $info['identity_annex_url']            = $identity_annex[HrStaffAnnexInfoModel::TYPE_ID_CARD]['annex_path_front'] ?? '';   //身份证附件地址
        $info['identity_annex_ai_audit_state'] = $identity_annex[HrStaffAnnexInfoModel::TYPE_ID_CARD]['ai_audit_state'] ?? HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
        $info['identity_audit_state']          = $identity_annex[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'] ?? HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
        $info['identity_audit_state_text']     = self::$t->_(HrStaffAnnexInfoModel::$audit_state_key[$info['identity_audit_state']]);

        //户口簿信息
        $info['residence_booklet_file_url_first']  = $identity_annex[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_front'] ?? '';   //户口簿第一页
        $info['residence_booklet_file_url_second'] = $identity_annex[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_rear'] ?? '';   //户口簿个人信息页
        $info['residence_booklet_audit'] = $identity_annex[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'] ?? HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
        $info['residence_booklet_audit_text']      = self::$t->_(HrStaffAnnexInfoModel::$audit_state_key[$info['residence_booklet_audit']]);


        //工作天数&轮休规则
        //week_working_day 每周工作天数
        //rest_type 轮休规则
        if (!empty($info['week_working_day']) && !empty($info['rest_type'])) {
            $working_day_rest_type              = $info['week_working_day'] . $info['rest_type'];
            $info['working_day_rest_type']      = $working_day_rest_type;
            $info['working_day_rest_type_text'] = self::$t->_('working_day_rest_type_' . $working_day_rest_type);
        } else {
            $info['working_day_rest_type']      = '';
            $info['working_day_rest_type_text'] = '';
        }

        $countryCode = strtoupper(env('country_code', 'th'));

        switch ($countryCode) {
            case 'PH': //菲律宾
                $default_country_value = '4';
                break;
            case 'LA': //老挝
                $default_country_value = '6';
                break;
            case 'ID': //印尼
                $default_country_value = '7';
                break;
            case 'MY': //马来
                $default_country_value = '3';
                break;
            case 'VN':
                $default_country_value = '5';
                break;
            default: //默认泰国
                $default_country_value = '1';
                break;
        }

            $graduate_time = $items['GRADUATE_TIME'] ?? '';
            if(!empty($graduate_time)) {
                $graduate_time = strtotime($graduate_time) === false ? '' : $graduate_time;
            }

            $info['graduate_time']= $graduate_time;
            $info['education']= $items['EDUCATION'] ?? '';
            $info['nationality']= $items['NATIONALITY'] ?? '';

            $birthday = $items['BIRTHDAY'] ?? '';
            if(!empty($birthday)) {
                $birthday = strtotime($birthday) === false ? '' : $birthday;
            }
            $info['birthday']= $birthday;

        $province_list = $this->provinceList();
        $city_list     = $this->cityList();
        $district_list = $this->districtList();

            //家庭信息
            $info['dad_first_name']= $items['DAD_FIRST_NAME'] ?? '';
            $info['dad_last_name']= $items['DAD_LAST_NAME'] ?? '';
            $info['mum_first_name']= $items['MUM_FIRST_NAME'] ?? '';
            $info['mum_last_name']= $items['MUM_LAST_NAME'] ?? '';
            $info['relatives_relationship']= $items['RELATIVES_RELATIONSHIP'] ?? '';
            $info['relatives_first_name']= $items['RELATIVES_FIRST_NAME'] ?? '';
            $info['relatives_last_name']= $items['RELATIVES_LAST_NAME'] ?? '';
            $info['relatives_call_name']= $items['RELATIVES_CALL_NAME'] ?? '';
            $info['relatives_mobile']= $items['RELATIVES_MOBILE'] ?? '';
            $info['working_country'] = $items['WORKING_COUNTRY'] ?? ''; //工作所在国家

            //国籍、工作所在国家解析
            $dictionaryService = new DictionaryService();
            $nationalityName = $dictionaryService->getDictionaryLabelByItemId('nationality_region', $info['nationality']);
            $workingCountryName = $dictionaryService->getDictionaryLabelByItemId('working_country', $info['working_country']);

            $info['nationality_name'] = $nationalityName;
            $info['working_country_name'] = $workingCountryName;
            $race = $items['RACE'] ?? '';//种族
            $religion = $items['RELIGION'] ?? '';//宗教
            $info['race'] = empty($race) ? '' : (int)$race;//种族
            $info['religion'] = empty($religion) ? '' : (int)$religion;//宗教
            $info['staff_province_code'] =  $items['STAFF_PROVINCE_CODE'] ?? ''; //工作所在洲 只有马来 网点 为-1 的时候  有值
            $info['bank_branch_name'] = $items['BANK_BRANCH_NAME'] ?? '';//银行分行名称
            $info['household_registration'] = $items['HOUSEHOLD_REGISTRATION'] ?? '';//户籍照号
            $info['ptkp_state'] = $items['PTKP_STATE'] ?? '';//PTKP状态
            $info['tax_card'] = $items['TAX_CARD'] ?? '';//税卡号

        if (in_array($info['formal'], [1, 4])) {
            $info['hire_type_text'] = self::$t->_('hire_type_' . $info['hire_type']);
        } else {
            $info['hire_type_text'] = '';
        }

        //地址国家/地区
        $dictionaryService = new DictionaryService();
        $nationalitylist   = $dictionaryService->getTotalDictionarylabelByDictCode('address_country_region');
        $nationalityList   = array_column($nationalitylist, 'label', 'value');

        $info['register_country']      = $items['REGISTER_COUNTRY'] ?? ''; //户口所在国家
        $info['register_country_name'] = $items['REGISTER_COUNTRY'] ? $nationalityList[$items['REGISTER_COUNTRY']] : ''; //户口所在国家
        $info['register_province']     = $items['REGISTER_PROVINCE'] ?? ''; //户口所在省
        $info['register_city']         = $items['REGISTER_CITY'] ?? ''; //户口所在市
        $info['register_district']     = $items['REGISTER_DISTRICT'] ?? ''; //户口所在乡

        $info['register_postcodes']   = $items['REGISTER_POSTCODES'] ?? ''; //户口所在邮编
        $info['register_house_num']   = $items['REGISTER_HOUSE_NUM'] ?? ''; //户口所在门牌号
        $info['register_village_num'] = $items['REGISTER_VILLAGE_NUM'] ?? ''; //户口所在村号
        $info['register_village']     = $items['REGISTER_VILLAGE'] ?? ''; //户口所在村
        $info['register_alley']       = $items['REGISTER_ALLEY'] ?? ''; //户口所在巷
        $info['register_street']      = $items['REGISTER_STREET'] ?? ''; //户口所在街道
        $info['register_detail_address'] = $items['REGISTER_DETAIL_ADDRESS'] ?? ''; //户口所在地详情

        if ($info['register_country'] == $default_country_value) {
            $info['register_province_name'] = !empty($info['register_province']) ? ($province_list[$info['register_province']]['name'] ?? '') : '';
        } else {
            $info['register_province_name'] = $info['register_province'] ?? '';
        }

        $info['residence_country']      = $items['RESIDENCE_COUNTRY'] ?? ''; //居住地址所在国家
        $info['residence_country_name'] = $items['RESIDENCE_COUNTRY'] ? $nationalityList[$items['RESIDENCE_COUNTRY']] : ''; //居住地址所在国家
        $info['residence_province']     = $items['RESIDENCE_PROVINCE'] ?? ''; //居住地址所在省
        $info['residence_city']         = $items['RESIDENCE_CITY'] ?? ''; //居住地址所在市
        $info['residence_district']     = $items['RESIDENCE_DISTRICT'] ?? ''; //居住地址所在乡
        $info['residence_postcodes']    = $items['RESIDENCE_POSTCODES'] ?? ''; //居住地址所在邮编
        $info['residence_house_num']    = $items['RESIDENCE_HOUSE_NUM'] ?? ''; //居住地址所在门牌号
        $info['residence_village_num']  = $items['RESIDENCE_VILLAGE_NUM'] ?? ''; //居住地址所在村号
        $info['residence_village']      = $items['RESIDENCE_VILLAGE'] ?? ''; //居住地址所在村
        $info['residence_alley']        = $items['RESIDENCE_ALLEY'] ?? ''; //居住地址所在巷
        $info['residence_street']       = $items['RESIDENCE_STREET'] ?? ''; //居住地址所在街道
        $info['residence_detail_address'] = $items['RESIDENCE_DETAIL_ADDRESS'] ?? ''; //居住地详情

        if ($info['residence_country'] == $default_country_value) {
            $info['residence_province_name'] = !empty($info['residence_province']) ? ($province_list[$info['residence_province']]['name'] ?? '') : '';
        } else {
            $info['residence_province_name'] = $info['residence_province'] ?? '';
        }

        //马来只有市信息，且是输入框，所以直接展示市信息，不要乡字段
        if ($countryCode == 'MY') {
            $info['register_city_name']      = $info['register_city'] ?? '';
            $info['residence_city_name']     = $info['residence_city'] ?? '';
            $info['register_district_name']  = $info['register_district'] ?? '';
            $info['residence_district_name'] = $info['residence_district'] ?? '';
            $info['vehicle_type_category']   = $items['VEHICLE_TYPE_CATEGORY'] ?? '';//马来 车类型
        } else {
            if ($info['register_country'] == $default_country_value) {
                $info['register_city_name']     = !empty($info['register_city']) ? ($city_list[$info['register_city']]['name'] ?? '') : '';
                $info['register_district_name'] = !empty($info['register_district']) ? ($district_list[$info['register_district']]['name'] ?? '') : '';
            } else {
                $info['register_city_name']     = $info['register_city'] ?? '';
                $info['register_district_name'] = $info['register_district'] ?? '';
            }


            if ($info['residence_country'] == $default_country_value) {
                $info['residence_city_name']     = !empty($info['residence_city']) ? ($city_list[$info['residence_city']]['name'] ?? '') : '';
                $info['residence_district_name'] = !empty($info['residence_district']) ? ($district_list[$info['residence_district']]['name'] ?? '') : '';
            } else {
                $info['residence_city_name']     = $info['residence_city'] ?? '';
                $info['residence_district_name'] = $info['residence_district'] ?? '';
            }
        }

        //印尼增加村级别选择
        if ($countryCode == "ID") {
            $info['register_rt']  = $items['REGISTER_RT'] ?? '';  //户口所在地邻组
            $info['register_rw']  = $items['REGISTER_RW'] ?? '';  //户口所在地居委会
            $info['residence_rt'] = $items['RESIDENCE_RT'] ?? ''; //居住地邻组
            $info['residence_rw'] = $items['RESIDENCE_RW'] ?? ''; //居住地居委会

            $villageCodes = $villageListKy = [];

            //开户地村庄特殊处理
            if ($info['register_country'] == $default_country_value) {
                $villageCodes[] = $info['register_village'];
            } else {
                $info['register_village_name'] = $info['residence_village'] ?? '';
            }

            //居住地村庄特殊处理
            if ($info['residence_country'] == $default_country_value) {
                $villageCodes[] = $info['residence_village'];
            } else {
                $info['residence_village_name'] = $info['residence_village'] ?? '';
            }

            if (!empty($villageCodes)) {
                $villageListKy = $this->villageList();
            }

            if (empty($info['register_village_name'])) {
                $info['register_village_name'] = $villageListKy[$info['register_village']] ?? '';
            }

            if (empty($info['residence_village_name'])) {
                $info['residence_village_name'] = $villageListKy[$info['residence_village']] ?? '';
            }
        }

        $info['role_ids'] = array_column($role_list, 'position_category');

        $info['hire_date']        = date('Y-m-d', strtotime($info['hire_date']));
        $info['leave_date']       = !empty($info['leave_date']) ? date('Y-m-d', strtotime($info['leave_date'])) : '';
        $info['stop_duties_date'] = !empty($info['stop_duties_date']) ? date('Y-m-d',
            strtotime($info['stop_duties_date'])) : '';

        unset($info['created_at']);
        //银行类型名称
        $info['bank_type_name'] = $this->showBankTypeName($info['bank_type']);

        if (isCountry(['TH','MY'])) {  // th my时才会返回该信息
            $info['bank_name']       = ((0 == $info['bank_type']) || empty($info['bank_type'])) ? "SCB" : $this->showBankTypeName($info['bank_type']); // 银行名称，
            $info['bank_no_name']    = !empty($items['BANK_NO_NAME']) ? trim($items['BANK_NO_NAME']) : trim($info['name']); // 持卡人信息为空时，默认展示员工姓名
            $info['bank_card_photo'] = $identity_annex[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front'] ?? ""; // 银行卡照片oss地址

            // 有号码 有图片 附件状态不为null 附件状态是啥就是啥
            if (is_numeric($identity_annex[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'])) {
                $info['bank_card_audit_state']      = (int)$identity_annex[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'];
                $info['bank_card_audit_state_text'] = self::$t->_(HrStaffAnnexInfoModel::$audit_state_key[$identity_annex[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state']]);
            }else{
                // 默认待上传
                $info['bank_card_audit_state']      = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
                $info['bank_card_audit_state_text'] = self::$t->_(HrStaffAnnexInfoModel::$audit_state_key[HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD]);
            }
        }
        $checkData = $this->getAiAuditCheck($staff_info_id);
        
        $info = $this->getAiAuditCheckBankCard($checkData, $info);

        // ai 身份证识别信息 定制返回结构
        $aiRecognitionEndDataIdentityArr = !empty($checkData[StaffInfoAuditCheckModel::TYPE_IDENTITY]['ai_recognition_end_data']) ? json_decode($checkData[StaffInfoAuditCheckModel::TYPE_IDENTITY]['ai_recognition_end_data'],true) : [];
        $info['ai_compare']['id_card'] = $this->ai_compare([
            'ai_data' => $aiRecognitionEndDataIdentityArr['ai_data'] ?? [],
            'staff_info' => $info,
            'method' => 'aiCompareIdCard',
        ]);
        foreach ($info['ai_compare']['id_card'] as $v){
            $info['ai_id_card_'.$v['key'].'_flag'] = $v['is_pass'] ?? true;
        }

        return $info;
    }

    /**
     * 获取AI识别的数据+ 跟员工信息 对比的结果
     * @param $staffInfoId
     * @param $info
     * @return array
     */
    public function getAiAuditCheck($staffInfoId)
    {
        $conditions            = 'staff_info_id = :staff_info_id:';
        $bind['staff_info_id'] = $staffInfoId;
        $auditInfo             = StaffInfoAuditCheckRepository::getStaffAuditCheckData('*', $conditions, $bind);

        return !empty($auditInfo) ? array_column($auditInfo, null, 'type') : [];
    }
    
    /**
     * @param $ai_data
     * @param $info
     * @return array
     */
    public function ai_compare($params): array
    {
        $return_data = [];
        $method =  $params['method'] ?? '';
        if (empty($method)){
            return [];
        }
        $rpc_param = [
            'ai_data'    => $params['ai_data'] ?? [],
            'staff_info' => $params['staff_info'] ?? [],
        ];
        // method：
        // aiCompareIdCard = 身份证，aiCompareSocialSecuritySystemCard = 社保卡 ，aiCompareTaxIdCard = 税卡
        // aiComparePhilHealthCard = 医保卡,  aiComparePagIbigCard = 公积金
        $ret       = new ApiClient('winhr_rpc', '', $method);
        $ret->setParams([$rpc_param]);
        $res = $ret->execute();
        $compare_id_card = [];
        if (!empty($res) && $res['code'] == ErrCode::SUCCESS && !empty($res['data'])) {
            $compare_id_card = $res['data']['compare'] ?? [];
        }
        // ai_value 为空也不显示感叹号
        foreach ($compare_id_card as $k=>$v){
            $return_data[] = [
                'value'   => $v['ai_value'] ?? '',
                'label'   => self::$t->_('staff_audit_' . $k),
                'key'     => $k,
                'is_pass' => !empty($v['ai_value']) ? ($v['is_pass'] ?? true) :true,
            ];
        }
        return $return_data;
    }

    /**
     * 比对 ai 识别身份证信息 与 员工信息
     * @param $auditInfoToType
     * @param $info
     * @return mixed
     */
    public function getAiAuditCheckBankCard($auditInfoToType, $info)
    {
        $aiRecognitionEndDataBank    = isset($auditInfoToType[StaffInfoAuditCheckModel::TYPE_BANK_CAR]) ? $auditInfoToType[StaffInfoAuditCheckModel::TYPE_BANK_CAR]['ai_recognition_end_data'] : '';
        $aiRecognitionEndDataBankArr = !empty($aiRecognitionEndDataBank) && !empty($info['bank_no']) ? json_decode($aiRecognitionEndDataBank,
            true) : [];

        //银行名称
        $info['ai_bank_name']      = $aiRecognitionEndDataBankArr['bank_name'] ?? '';
        $aiBankName                = $aiRecognitionEndDataBankArr['bank_name_after'] ?? '';//ai 识别出来，处理后的数据， 用来跟员工信息中的银行类型做对比

        $hireType                  = $aiRecognitionEndDataBankArr['hire_type'] ?? '';//员工雇佣类型
        $bankType                  = $aiRecognitionEndDataBankArr['bank_type'] ?? '';//ai 识别出来，个人代理的银卡卡类型
        
        //个人代理是通过 银行卡类型 来判断，识别结果
        $info['ai_bank_name_flag'] = false;
        if($hireType == HrStaffInfoModel::HIRE_TYPE_UN_PAID && $bankType == $info['bank_type']) {
            $info['ai_bank_name_flag'] = true;
        } else {
            $info['ai_bank_name_flag'] = $this->checkString($aiBankName, $info['bank_name']);
        }

        //持卡人
        $info['ai_account_name']      = $aiRecognitionEndDataBankArr['account_name'] ?? "";
        /**
         * 返回的name_th==员工信息的姓名
        AI识别结果的特殊情况：开头น.ส.视作นางสาว和HCM的员工信息进行对比
        AI：น.ส. == HCM：นางสาว
        AI：น.ส. == HCM：น.ส.
         */
        $nameFlag1 = $this->checkString(nameSpecialCharsReplace($info['ai_account_name']), $info['bank_no_name']);

        //兼容泰文 女
        $thPrefixString = !empty($info['ai_account_name']) ? substr($info['ai_account_name'], 0, 8) : '';
        $aiName = strcasecmp($thPrefixString, 'น.ส.') === 0 ?  'นางสาว' . substr($info['ai_account_name'], 8) : $info['ai_account_name'];
        $nameFlag2 = $this->checkString(nameSpecialCharsReplace($aiName), $info['bank_no_name']);

        $name_th_flag = ($nameFlag1 || $nameFlag2) ? true : false;

        $info['ai_account_name_flag'] = $name_th_flag;
        //卡号
        $info['ai_account_no']      = $aiRecognitionEndDataBankArr['account_no'] ?? "";
        $info['ai_account_no_flag'] = $this->checkString($info['ai_account_no'], $info['bank_no']);

        if(empty($info['ai_bank_name']) && empty($info['ai_account_name']) && empty($info['ai_account_no'])) {
            $info['ai_bank_name_flag'] = $info['ai_account_name_flag'] = $info['ai_account_no_flag'] = true;
        }

        return $info;
    }

    /**
     * 比较两个字符串
     * @param $aiString
     * @param $staffString
     * @return bool
     */
    public function checkString($aiString, $staffString)
    {
        $aiString = AiServer::stringSpecialCharsReplace($aiString);
        $staffString = AiServer::stringSpecialCharsReplace($staffString);

        if (empty($aiString) || empty($staffString) || !is_string($aiString) || !is_string($staffString)) {
            return false;
        }
        if (strcasecmp($aiString, $staffString) === 0) {
            return true;
        }
        return false;
    }

    /**
     * 格式化地址
     * @param $country      string
     * @param $province     string
     * @param $city         string
     * @param $district     string
     * @param $otherItems   array
     * @return string       string
     */
    public function getAddress(
        string $country,
        string $province,
        string $city,
        string $district,
        array $otherItems
    ): string {
        $strings = [];

        //获取地址国家/地区
        $addressCountryRegionName = (new DictionaryService())->getDictionaryLabelByItemId('address_country_region',
            $country);

        // 居住地国家
        $strings['COUNTRY'] = "";
        if (!empty($country)) {
            $strings['COUNTRY'] = $addressCountryRegionName;
        }
        // 获取省
        $strings['PROVINCE'] = "";
        if (!empty($province)) {
            $provinceList        = $this->provinceList();
            $strings['PROVINCE'] = !empty($provinceList[$province]['name']) ? $provinceList[$province]['name'] : $province;
        }

        // 获取市
        $strings['CITY'] = "";
        if (!empty($city)) {
            $cityList        = $this->cityList();
            $strings['CITY'] = !empty($cityList[$city]['name']) ? $cityList[$city]['name'] : $city;
        }

        // 获取乡
        $strings['DISTRICT'] = "";
        if (!empty($district)) {
            $districtList        = $this->districtList();
            $strings['DISTRICT'] = !empty($districtList[$district]['name']) ? $districtList[$district]['name'] : $district;
        }

        // 详细地址
        foreach ($otherItems as $key => $item) {
            $strings[$key] = !empty($item) ? $item : "";
        }
        return implode(' ', array_values($strings));
    }

    /**
     * 同步员工信息 从ms 同步到fbi
     * @param $param
     * @return bool
     * @throws \Exception
     */
    public function syncHrStaffInfo($param)
    {
        $db = BackyardBaseModel::beginTransaction($this);
        try {
            //if(!empty($param['week_working_day']) && ! in_array($param['week_working_day'], [StaffEnums::WEEK_WORKING_DAY_FIVE, StaffEnums::WEEK_WORKING_DAY_SIX])){
            //    throw new ValidationException('工作天数不正确');
            //}

            $fle_staff_info = $this->getFleStaffInfo($param['staff_info_id']);
            if (empty($fle_staff_info)) {
                throw new ValidationException('MS 工号不存在');
            }

            $staff_info = $this->getHrStaffInfo($param['staff_info_id']);
            if (!empty($staff_info)) {
                throw new ValidationException('FBI工号已存在');
            }

            $manger_staff_info = $this->getHrStaffInfo($param['manger']);
            if (empty($manger_staff_info)) {
                throw new ValidationException('直线上级不存在');
            }

            $sys_department_id = (new SysDepartmentService())->SearchSysDeptId($param['department_id']);
            if (empty($sys_department_id)) {
                throw new ValidationException('部门id错误');
            }

            $job_title_detail = (new HrJobTitleService())->getJobTitleDetail($param['job_title_id']);
            if (empty($job_title_detail)) {
                throw new ValidationException('职位id不正确');
            }

            //获取国籍
            $dictionaryService = new DictionaryService();
            $nationalityList   = $dictionaryService->getDictionItemsListByCode('nationality_region');
            $nationalityList   = array_column($nationalityList, 'value');
            if (!in_array($param['nationality'], $nationalityList)) {
                throw new ValidationException('国籍参数不正确');
            }

            //获取工作所在国家
            $workingCountryList = $dictionaryService->getDictionItemsListByCode('working_country');
            $workingCountryList = array_column($workingCountryList, 'value');
            if (!in_array($param['working_country'], $workingCountryList)) {
                throw new ValidationException('工作所在国家参数不正确');
            }
            //班次
            $shift = (new HrShiftService())->getList();
            if (!empty($shift)) {
                if (!in_array($param['shift_id'], array_column($shift, 'id'))) {
                    throw new ValidationException('班次参数不正确');
                }
            }

            //hr_staff_info
            $insert_staff_param = [
                'staff_info_id'      => $param['staff_info_id'],
                'name'               => $fle_staff_info['name'],//和fle库一致
                'email'              => $param['email'] ?? '',//和fle库一致
                'identity'           => '0000000000',
                'mobile'             => '000000000',
                'sys_store_id'       => '-1',
                'sys_department_id'  => $sys_department_id,
                'node_department_id' => $param['department_id'],
                'formal'             => 1,
                'state'              => 1,
                'hire_date'          => $fle_staff_info['hire_date'],//和fle库一致
                'job_title'          => $param['job_title_id'],
                'hire_type'          => 1,
                'job_title_grade_v2' => $param['job_title_grade'] ?? 0,
                'created_at'         => $fle_staff_info['created_at'],
                'manger'             => $param['manger'],
                'working_country'    => $param['working_country'],
                'nationality'        => $param['nationality'],
                'education'          => $param['education'] ?? 0,
            ];
            //if($param['week_working_day']){
            //    $insert_staff_param['week_working_day'] = $param['week_working_day'];
            //}
            switch ($param['working_day_rest_type']) {
                case Enums::WORKING_DAY_REST_TYPE_51:
                    $insert_staff_param['week_working_day'] = 5;
                    $insert_staff_param['rest_type']        = 1;
                    break;
                case Enums::WORKING_DAY_REST_TYPE_52:
                    $insert_staff_param['week_working_day'] = 5;
                    $insert_staff_param['rest_type']        = 2;
                    break;
                case Enums::WORKING_DAY_REST_TYPE_61:
                    $insert_staff_param['week_working_day'] = 6;
                    $insert_staff_param['rest_type']        = 1;
                    break;
                case Enums::WORKING_DAY_REST_TYPE_62:
                    $insert_staff_param['week_working_day'] = 6;
                    $insert_staff_param['rest_type']        = 2;
                    break;
            }
            $this->getDI()->get('db_backyard')->insertAsDict('hr_staff_info', $insert_staff_param);

            $hr_items = [
                'MANGER'          => $param['manger'],
                'NATIONALITY'     => $param['nationality'],
                'WORKING_COUNTRY' => $param['working_country'],
            ];
            foreach ($hr_items as $key => $value) {
                //直线上级
                $insert_item = [
                    'staff_info_id' => $param['staff_info_id'],
                    'item'          => $key,
                    'value'         => $value,
                ];
                $this->getDI()->get('db_backyard')->insertAsDict('hr_staff_items', $insert_item);
            }

            if (!empty($param['email'])) {
                //hr_email
                $insert_staff_email_param = [
                    'staff_info_id' => $param['staff_info_id'],
                    'email'         => $param['email'],
                    'state'         => 1,
                ];
                $this->getDI()->get('db_backyard')->insertAsDict('hr_emails', $insert_staff_email_param);
            }

            $exist = HrStaffInfoExtendModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => ['staff_id' => $param['staff_info_id']],
            ]);

            if (empty($exist)) {
                $hrStaffInfoExtendModel                = new HrStaffInfoExtendModel();
                $hrStaffInfoExtendModel->staff_info_id = $param['staff_info_id'];
                $hrStaffInfoExtendModel->save();
            }

            //角色  脚本用一次
            if (!empty($param['roles'])) {
                $name_arr = explode(',', $param['roles']);
                $name_str = "'" . implode("','", $name_arr) . "'";
                $roles    = RolesModel::find("name in ($name_str)")->toArray();
                foreach ($roles as $role) {
                    $insert_staff_roles[] = [
                        'staff_info_id'     => $param['staff_info_id'],
                        'position_category' => $role['id'],
                    ];
                }
                if (!empty($insert_staff_roles)) {
                    (new BaseModel())->table_batch_insert($insert_staff_roles, 'db_backyard', 'hr_staff_info_position');
                }
            }

            //加入考勤白名单
            if ($param['is_whitelist'] == 1) {
                //不在当地发新不打卡白名单
                $flag = (new AttendanceWhiteListService())->add([
                    'staff_info_id' => $param['staff_info_id'],
                    'staff_id'      => $param['operator'],
                    'start_at'      => empty($param['whitelist_start_at']) ? date("Y-m-d") : $param['whitelist_start_at'],
                    'type'          => AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY,
                    'path'          => 'StaffService_syncHrStaffInfo',
                ]);
                if (!$flag) {
                    $this->logger->error('StaffService_syncHrStaffInfo AttendanceWhiteListService-add ' . $flag);
                }
            }
            //加入转正白名单
            if ($param['is_work_ignore'] == 1) {
                $param_data['param_data'] = $param;
                $param_data['set_val']    = $param['staff_info_id']; // 工号
                $param_data['staff_id']   = $param['operator'];//操作人
                $res                      = (new RemoveHrStaffInfoService)->HrProbationSave($param_data, 2);
            }

            $shift_info           = array_column($shift, null, 'id');
            $model                = new HrStaffShiftModel();
            $model->staff_info_id = $param['staff_info_id'];
            $model->start         = $shift_info[$param['shift_id']]['start'];
            $model->end           = $shift_info[$param['shift_id']]['end'];
            $model->shift_type    = $shift_info[$param['shift_id']]['type'];;
            $model->shift_id        = $param['shift_id'];
            $model->preset_shift_id = $param['shift_id'];
            $model->effective_date  = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
            if (!$model->save()) {
                throw new \Exception("{$param['staff_info_id']} 班次信息保存失败");
            }
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
    }


    //返回 fbi 用户信息结构
    public function get_fbi_user_info($staff_id)
    {
        $sql   = " select si.id,si.name , si.organization_id , si.organization_type , si.mobile , si.encrypted_password
                                , GROUP_CONCAT(sip.position_category) position_category
                                , si.state
                                , si.department_id
                                , si.job_title
                                , si.is_sub_staff
                                , si.formal
                 from staff_info  as si
                 join staff_info_position as sip on si.id=sip.staff_info_id
                 where si.id='{$staff_id}' ;
                ";
        $model = $this->getDI()->get('db_fle')->query($sql);
        $info  = $model->fetch(\Phalcon\Db::FETCH_ASSOC);

        if (empty($info)) {
            return null;
        }

        //查询 网点 或者总部 组织信息
        if ($info['organization_type'] == 1 && !empty($info['organization_id'])) {//网点
            $sql                    = "select lat,lng,category ,`name` from sys_store where id = '{$info['organization_id']}'";
            $res                    = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $info['store_category'] = empty($res) ? '' : $res['category'];
            $info['store_lat']      = empty($res) ? '' : $res['lat'];
            $info['store_lng']      = empty($res) ? '' : $res['lng'];
        }

        if ($info['organization_type'] == 2 && !empty($info['department_id'])) {//总部
            $sql                       = " select `name` from sys_department where id = {$info['department_id']}";
            $res                       = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $info['organization_name'] = empty($res) ? '' : $res['name'];
        }

        return $info;
    }


    /**
     * 获取单个员工基础信息
     * 员工id
     * @param  $staffInfoId
     * @return array
     */
    public function staffInfo($staffInfoId)
    {
        $sql = "--
                SELECT
                    hsi.staff_info_id,
                    hsi.`name`,
                    hsi.name_en,
                    hsi.mobile,
                    hsi.mobile_company,
                    hsi.state,
                    hsi.job_title,
                    hsi.sys_store_id,
                    hsi.leave_date,
                    hsi.hire_date,
                    job.job_name,
                    d.`name` AS department_name,
                    hsi.identity
                FROM
                    hr_staff_info AS hsi
                    LEFT JOIN hr_job_title AS job ON job.id = hsi.job_title
                    LEFT JOIN sys_department AS d ON d.id = hsi.sys_department_id
                WHERE
                    staff_info_id = :staff_info_id";

        $staff     = $this->_querySqlOne($sql, "db_rby", ["staff_info_id" => $staffInfoId]);
        $staffInfo = [];
        if ($staff) {
            $storelist = (new SysStoreService())->getStoreList();
            $storelist = array_column($storelist, null, 'id');
            if ($staff['sys_store_id'] == -1) {
                $sys_store_name = GlobalEnums::HEAD_OFFICE;
            } else {
                $sys_store_name = isset($storelist[$staff['sys_store_id']]) ? $storelist[$staff['sys_store_id']]['name'] : '';
            }
            $areaInfo  = (new LeaveManagerService())->getAreaByStore($staff['sys_store_id']);
            $staffInfo = [
                'staff_info_id'   => $staff['staff_info_id'],
                'name'            => $staff['name'],
                'job_title'       => $staff['job_title'],
                'name_en'         => (string)$staff['name_en'],
                'mobile'          => $staff['mobile'],
                'mobile_company'  => $staff['mobile_company'],//公司手机
                'state'           => $staff['state'],
                'leave_date'      => $staff['leave_date'] ? date("Y-m-d", strtotime($staff['leave_date'])) : '',
                'hire_date'       => $staff['hire_date'] ? date("Y-m-d", strtotime($staff['hire_date'])) : '',
                'sys_store_name'  => $sys_store_name,
                'job_title_name'  => $staff['job_name'] ?? '',
                'sys_store_id'    => $staff['sys_store_id'],
                'store_area_id'   => $areaInfo ? $areaInfo['id'] : '',
                'state_name'      => self::$t->_('staff_state_' . $staff['state']),
                'department_name' => $staff['department_name'] ?? '',
                'identity'        => $staff['identity'] ?? '',
            ];
        }

        return $staffInfo;
    }

    public function provinceList()
    {
        $province = SysProvinceModel::find([
            'columns'    => ' code, name ',
            'conditions' => ' deleted = 0 ',
        ])->toArray();

        return array_column($province, null, 'code');
    }

    public function cityList()
    {
        $city = SysCityModel::find([
            'columns'    => ' code, name, province_code ',
            'conditions' => ' deleted = 0 ',
        ])->toArray();
        return array_column($city, null, 'code');
    }

    public function districtList()
    {
        $districts = SysDistrictModel::find([
            'columns'    => ' code, name, city_code, province_code ',
            'conditions' => ' deleted = 0 ',
        ])->toArray();

        return array_column($districts, null, 'code');
    }

    public function villageList()
    {
        $districts = HrVillageModel::find([
            'columns'    => ' code, name',
            'conditions' => ' is_deleted = 0 ',
        ])->toArray();

        return array_column($districts, 'name', 'code');
    }


    public function resumeInfo($resumeId)
    {
        $resumeInfo = HrResumeModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => [
                'id' => $resumeId,
            ],
        ]);
        $resultInfo = [];
        if ($resumeInfo) {
            $resumeInfo               = $resumeInfo->toArray();
            $resultInfo['first_name'] = $resumeInfo['first_name'];
            $resultInfo['last_name']  = $resumeInfo['last_name'];
            $resultInfo['phone']      = $resumeInfo['phone'];

            $hrJdInfo = HrJdModel::findFirst([
                'conditions' => ' job_id = :job_id: ',
                'bind'       => [
                    'job_id' => $resumeInfo['job_id'],
                ],
            ]);
            if ($hrJdInfo) {
                $hrJdInfo               = $hrJdInfo->toArray();
                $resultInfo['job_name'] = $hrJdInfo['job_name'];
            }

            $interviewInfo = HrInterviewModel::findFirst([
                'conditions' => ' resume_id = :resume_id: ',
                'bind'       => [
                    'resume_id' => $resumeInfo['id'],
                ],
            ]);
            if ($interviewInfo) {
                $interviewInfo = $interviewInfo->toArray();
                $hcInfo        = HrHcModel::findFirst([
                    'conditions' => ' hc_id = :hc_id: ',
                    'bind'       => [
                        'hc_id' => $interviewInfo['hc_id'],
                    ],
                ]);
                if ($hcInfo) {
                    $hcInfo     = $hcInfo->toArray();
                    $department = SysDepartmentModel::findFirst([
                        'conditions' => ' id = :id: ',
                        'bind'       => [
                            'id' => $hcInfo['department_id'],
                        ],
                    ]);
                    if ($department) {
                        $department = $department->toArray();

                        $resultInfo['department_name'] = $department['name'];
                    }

                    if (!empty($hcInfo['job_title'])) {
                        $hrJobTitle = HrJobTitleModel::findFirst([
                            'conditions' => ' id =  :id: ',
                            'bind'       => [
                                'id' => $hcInfo['job_title'],
                            ],
                        ]);
                        if ($hrJobTitle) {
                            $hrJobTitle             = $hrJobTitle->toArray();
                            $resultInfo['job_name'] = $hrJobTitle['job_name'];
                        }
                    }

                    $sysStore = SysStoreModel::findFirst([
                        'conditions' => ' id =  :id: ',
                        'bind'       => [
                            'id' => $hcInfo['worknode_id'],
                        ],
                    ]);
                    if ($sysStore) {
                        $sysStore                 = $sysStore->toArray();
                        $resultInfo['store_name'] = $sysStore['name'];
                    }

                    $interviewSubscribeInfo = HrInterviewSubscribeModel::findFirst([
                        'conditions' => ' hc_id = :hc_id: and interview_id = :interview_id: ',
                        'bind'       => [
                            'hc_id'        => $hcInfo['hc_id'],
                            'interview_id' => $interviewInfo['interview_id'],
                        ],
                    ]);
                    if ($interviewSubscribeInfo) {
                        $interviewSubscribeInfo       = $interviewSubscribeInfo->toArray();
                        $resultInfo['interview_time'] = gmdate("Y-m-d H:i:s",
                            strtotime($interviewSubscribeInfo['interview_time']) + $this->timeOffset * 3600);
                        $resumeInfo['detail_address'] = $interviewSubscribeInfo['detail_address'];

                        $staffInfo = HrStaffInfoModel::findFirst([
                            'conditions' => ' staff_info_id = :staff_id: ',
                            'bind'       => [
                                'staff_id' => $interviewSubscribeInfo['interviewer_id'],
                            ],
                        ]);
                        if ($staffInfo) {
                            $staffInfo                 = $staffInfo->toArray();
                            $resultInfo['interviewer'] = !empty($staffInfo['name']) ? $staffInfo['name'] : $staffInfo['name_en'];
                        }

                        $hr = StaffInfoModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind'       => [
                                'id' => $interviewSubscribeInfo['staff_id'],
                            ],
                        ]);
                        if ($hr) {
                            $hr                      = $hr->toArray();
                            $resultInfo['hr']        = $hr['name'];
                            $resultInfo['hr_mobile'] = $hr['hr_mobile'];
                        }
                    }

                    $storeManager = $this->storeIdGetLeader($hcInfo['worknode_id']);
                    //网点管理人
                    $manager                     = $storeManager ? $storeManager['name'] . "(" . $storeManager['mobile'] . ")" : "";
                    $resultInfo['store_manager'] = $manager;
                }
            }

            $sex                       = $this->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
            $appellation               = $sex == 1 ? 'Mr' : 'Ms';
            $resultInfo['appellation'] = $appellation;
        }

        //企业官网
        $flash_express_co_ltd = (new SettingEnvService())->getSetVal('flash_express_co_ltd');
        //登记link
        $registration_link                  = (new SettingEnvService())->getSetVal('registration_link');
        $resultInfo['flash_express_co_ltd'] = $flash_express_co_ltd;
        $resultInfo['registration_link']    = $registration_link;

        return $resultInfo;
    }

    /**
     * 预约面试简历模板
     *
     *
     * @param $resumeId
     * @param $countryCode
     * @param $type
     * @param $id
     *
     */
    public function reserveSmsTpl($resumeId, $countryCode, $type, $id)
    {
        $tmp = $this->getSmsContentById($id);
        if ($tmp) {
            return $tmp;
        }

        // 老版本短信兼容
        $resumeInfo = $this->resumeData($resumeId);
        if (in_array(strtoupper($countryCode), ['TH', 'ID'])) {
            $tpl = self::TPL_1;
        } else {
            $tpl = self::TPL_2;
        }

        //企业官网
        $flash_express_co_ltd = (new SettingEnvService())->getSetVal('flash_express_co_ltd');
        //登记link
        $registration_link = (new SettingEnvService())->getSetVal('registration_link');

        $jobName = isset($resumeInfo['hr_jd']['job_name']) ? $resumeInfo['hr_jd']['job_name'] : '';
        if (isset($resumeInfo['hr_hc']['job_name'])) {
            $jobName = $resumeInfo['hr_hc']['job_name'];
        }

        if ($type == 'TH') {
            $tmp = sprintf($tpl['th_applicant_email'],
                $resumeInfo['first_name'],
                $resumeInfo['last_name'],
                $resumeInfo['phone'],
                $jobName,
                isset($resumeInfo['hr_hc']['department_name']) ? $resumeInfo['hr_hc']['department_name'] : '',
                isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '',
                isset($resumeInfo['hr_interview_subscribe']['interview_time']) ? $resumeInfo['hr_interview_subscribe']['interview_time'] : '',
                isset($resumeInfo['hr_interview_subscribe']['detail_address']) ? $resumeInfo['hr_interview_subscribe']['detail_address'] : '',
                isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : ''
            );
        } else {
            if ($type == 'EN_ONLINE') {
                $sex         = $this->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                $appellation = $sex == 1 ? 'Mr' : 'Ms';
                $staff       = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";

                $tmp = sprintf($tpl['en_applicant_online_email'],
                    $appellation,
                    $resumeInfo['first_name'],
                    $resumeInfo['last_name'],
                    $jobName,
                    isset($resumeInfo['hr_hc']['department_name']) ? $resumeInfo['hr_hc']['department_name'] : '',
                    $flash_express_co_ltd,
                    isset($resumeInfo['hr_interview_subscribe']['interview_time']) ? $resumeInfo['hr_interview_subscribe']['interview_time'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['name']) ? $resumeInfo['hr_interview_subscribe']['hr']['name'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['mobile']) ? $resumeInfo['hr_interview_subscribe']['hr']['mobile'] : '',
                    $staff,
                    $registration_link
                );
            } else {
                $sex         = $this->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                $appellation = $sex == 1 ? 'Mr' : 'Ms';
                $staff       = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";

                $tmp = sprintf($tpl['en_applicant_email'],
                    $appellation,
                    $resumeInfo['first_name'],
                    $resumeInfo['last_name'],
                    $jobName,
                    isset($resumeInfo['hr_hc']['department_name']) ? $resumeInfo['hr_hc']['department_name'] : '',
                    $flash_express_co_ltd,
                    isset($resumeInfo['hr_interview_subscribe']['detail_address']) ? $resumeInfo['hr_interview_subscribe']['detail_address'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['interview_time']) ? $resumeInfo['hr_interview_subscribe']['interview_time'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['name']) ? $resumeInfo['hr_interview_subscribe']['hr']['name'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['mobile']) ? $resumeInfo['hr_interview_subscribe']['hr']['mobile'] : '',
                    $staff,
                    $registration_link
                );
            }
        }

        return $tmp;
    }


    public function getSmsContentById($id)
    {
        $tmp = "";
        if ($id) {
            $smsLog = HrSmsLogModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => intval($id),
                ],
            ]);
            $smsLog = $smsLog ? $smsLog->toArray() : [];
            if ($smsLog) {
                $tmp = str_replace("/r/n", "<br/>", $smsLog['content']);
            }
        }

        return $tmp;
    }

    /**
     * 发送offer 短信模板
     *
     * @param $resumeId
     * @param $countryCode
     * @param $id
     *
     */
    public function sendOfferSmsTpl($resumeId, $countryCode, $id)
    {
        $tmp = $this->getSmsContentById($id);
        if ($tmp) {
            return $tmp;
        }

        $resumeInfo = $this->resumeData($resumeId);
        if (strtolower($countryCode) == 'my') {
            // 马来
            $tmp .= "Employment Confirmation Message\r\n\r\n";
            $tmp .= "Welcome to join Flash Malaysia Express family!\r\n";
            $tmp .= "Flash sincerely invites : " . $resumeInfo['first_name'] . "\r\n";

            $tmp     .= "Working Location : " . (isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '') . "\r\n";
            $tmp     .= "Temporary Location（For branches that not yet open）: - \r\n";
            $tmp     .= "Onboard date : " . (isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                    strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '') . "\r\n";
            $tmp     .= "Position : " . (isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '') . "\r\n";
            $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
            $tmp     .= "Branch Contact Person : " . $manager . "\r\n";
            $staff   = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";
            $tmp     .= "Contact Person : " . $staff . "\r\n\r\n";
            $tmp     .= "Have a nice day!\r\n";
        } else {
            if (strtolower($countryCode) == 'ph') {
                //  菲律宾
                $tmp .= "SMS Job Confirmation\r\n";
                $tmp .= "Welcome to the Flash Express family!\r\n";
                $tmp .= "We would like to invite : " . $resumeInfo['first_name'] . " " . $resumeInfo['last_name'] . "\r\n";

                $tmp     .= "Work Place :" . (isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '') . "\r\n";
                $tmp     .= "Training Branch (for Branch haven't opened) : - \r\n";
                $tmp     .= "Start Date : " . (isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                        strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '') . "\r\n";
                $tmp     .= "Position : " . (isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '') . "\r\n";
                $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                $tmp     .= "Branch Supervisor contact :" . $manager . "\r\n";
                $staff   = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";
                $tmp     .= "HR contact :" . $staff . "\r\n";
                $tmp     .= "Wishing you good luck with your new job!\r\n";
            } else {
                $tmp .= "SMS ยืนยันการจ้างงาน\r\n";
                $tmp .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัว แฟลช เอ็กซ์เพรส\r\n";
                $tmp .= "ทางบริษัทฯ ขอนัดหมายคุณ : " . $resumeInfo['first_name'] . " " . $resumeInfo['last_name'] . "\r\n";

                $tmp     .= "เริ่มงานที่สาขา :" . (isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '') . "\r\n";
                $tmp     .= "ฝึกงานสาขา (สำหรับสาขาที่ยังไม่เปิดเท่านั้น) : - \r\n";
                $tmp     .= "วันที่เริ่มงาน : " . (isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                        strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '') . "\r\n";
                $tmp     .= "ตำแหน่ง : " . (isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '') . "\r\n";
                $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                $tmp     .= "ติดต่อผู้รับผิดชอบสาขา :" . $manager . "\r\n";
                $staff   = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";
                $tmp     .= "เบอร์โทรผู้สรรหา :" . $staff . "\r\n";
                $tmp     .= "ขอให้มีความสุขกับการทำงานค่ะ\r\n";
            }
        }


        return $tmp;
    }


    /**
     * 聚合相关的简历数据
     *
     * @param $resumeId
     * @return array
     */
    public function resumeData($resumeId)
    {
        $resumeInfo = HrResumeModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => [
                'id' => $resumeId,
            ],
        ]);
        $resumeInfo = $resumeInfo ? $resumeInfo->toArray() : [];
        if ($resumeInfo) {
            $hrJdInfo            = HrJdModel::findFirst([
                'conditions' => ' job_id = :job_id: ',
                'bind'       => [
                    'job_id' => $resumeInfo['job_id'],
                ],
            ]);
            $hrJdInfo            = $hrJdInfo ? $hrJdInfo->toArray() : [];
            $resumeInfo['hr_jd'] = $hrJdInfo;
            //5.3群反馈，查不到入职日期和职位，露森给出sql增加hc_id条件查询
            $interviewInfo              = HrInterviewModel::findFirst([
                'conditions' => ' resume_id = :resume_id: AND hc_id = :hc_id: ',
                'bind'       => [
                    'resume_id' => $resumeInfo['id'],
                    'hc_id'     => $resumeInfo['hc_id'],
                ],
            ]);
            $interviewInfo              = $interviewInfo ? $interviewInfo->toArray() : [];
            $resumeInfo['hr_interview'] = $interviewInfo;

            if ($interviewInfo) {
                $hcInfo = HrHcModel::findFirst([
                    'conditions' => ' hc_id = :hc_id: ',
                    'bind'       => [
                        'hc_id' => $interviewInfo['hc_id'],
                    ],
                ]);
                $hcInfo = $hcInfo ? $hcInfo->toArray() : [];

                if ($hcInfo) {
                    $department = SysDepartmentModel::findFirst([
                        'conditions' => ' id = :id: ',
                        'bind'       => [
                            'id' => $hcInfo['department_id'],
                        ],
                    ]);
                    if ($department) {
                        $department = $department->toArray();

                        $hcInfo['department_name'] = $department['name'];
                    }

                    $storeManager = $this->storeIdGetLeader($hcInfo['worknode_id']);
                    //网点管理人
                    $manager                 = $storeManager ? $storeManager['name'] . "(" . $storeManager['mobile'] . ")" : "";
                    $hcInfo['store_manager'] = $manager;

                    if (!empty($hcInfo['job_title'])) {
                        $hrJobTitle = HrJobTitleModel::findFirst([
                            'conditions' => ' id =  :id: ',
                            'bind'       => [
                                'id' => $hcInfo['job_title'],
                            ],
                        ]);
                        if ($hrJobTitle) {
                            $hrJobTitle         = $hrJobTitle->toArray();
                            $hcInfo['job_name'] = $hrJobTitle['job_name'];
                        }
                    }

                    if ($hcInfo['worknode_id'] == '-1') {
                        $hcInfo['store_name'] = 'head office';
                    } else {
                        $sysStore = SysStoreModel::findFirst([
                            'conditions' => ' id =  :id: ',
                            'bind'       => [
                                'id' => $hcInfo['worknode_id'],
                            ],
                        ]);
                        if ($sysStore) {
                            $sysStore             = $sysStore->toArray();
                            $hcInfo['store_name'] = $sysStore['name'];
                        }
                    }

                    $interviewSubscribeInfo = HrInterviewSubscribeModel::findFirst([
                        'conditions' => ' hc_id = :hc_id: and interview_id = :interview_id: ',
                        'bind'       => [
                            'hc_id'        => $hcInfo['hc_id'],
                            'interview_id' => $interviewInfo['interview_id'],
                        ],
                    ]);
                    $interviewSubscribeInfo = $interviewSubscribeInfo ? $interviewSubscribeInfo->toArray() : [];
                    if ($interviewSubscribeInfo) {
                        $interviewSubscribeInfo['interview_time'] = date("Y-m-d H:i:s",
                            strtotime($interviewSubscribeInfo['interview_time'] . " + " . $this->timeOffset . " hours"));
                        $staffInfo                                = HrStaffInfoModel::findFirst([
                            'conditions' => ' staff_info_id = :staff_id: ',
                            'bind'       => [
                                'staff_id' => $interviewSubscribeInfo['interviewer_id'],
                            ],
                        ]);
                        if ($staffInfo) {
                            $staffInfo                             = $staffInfo->toArray();
                            $interviewSubscribeInfo['interviewer'] = $staffInfo;
                        }

                        $hr = StaffInfoModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind'       => [
                                'id' => $interviewSubscribeInfo['staff_id'],
                            ],
                        ]);

                        if ($hr) {
                            $hr                           = $hr->toArray();
                            $interviewSubscribeInfo['hr'] = $hr;
//                            $interviewSubscribeInfo['hr_mobile'] = $hr['hr_mobile'];
                        }

                        $resumeInfo['hr_interview_subscribe'] = $interviewSubscribeInfo;
                    }

                    $hrInterviewOffer = HrInterviewOfferModel::findFirst([
                        'conditions' => 'hc_id = :hc_id: and interview_id = :interview_id: and resume_id = :resume_id: ',
                        'bind'       => [
                            'hc_id'        => $hcInfo['hc_id'],
                            'interview_id' => $interviewInfo['interview_id'],
                            'resume_id'    => $resumeInfo['id'],
                        ],
                    ]);
                    $hrInterviewOffer = $hrInterviewOffer ? $hrInterviewOffer->toArray() : [];
                    if ($hrInterviewOffer) {
                        $hrJobTitleModel                  = HrJobTitleModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind'       => [
                                'id' => $hrInterviewOffer['position_id'],
                            ],
                        ]);
                        $hrJobTitle                       = $hrJobTitleModel ? $hrJobTitleModel->toArray() : [];
                        $hrInterviewOffer['hr_job_title'] = $hrJobTitle;
                    }
                    $resumeInfo['hr_interview_offer'] = $hrInterviewOffer;
                }

                $resumeInfo['hr_hc'] = $hcInfo;
            }
        }

        return $resumeInfo;
    }


    /**
     * 通过网点获取网点负责人
     * HUB    [15] Branch Manager
     *    [272] HUB Supervisor
     *
     * SHOP    [101] Shop Supervisor
     *        [296] Store Supervisor
     *
     * DC/SP [16]   Branch Supervisor
     *         [451] Assistant Branch Supervisor
     * @desc 按照职位ID以及工号找，顺序 都取最小的
     */
    public function storeIdGetLeader($store_id)
    {
        $job_title = '(15,16,101,272,296,451)';
        $sql       = "select id,job_title,name,mobile,organization_id from staff_info where 
			organization_id = '{$store_id}'
			and state = 1 -- 取在职员工
			and job_title in {$job_title}
			order by
			job_title asc,
			id asc 
			limit 1
			";
        $ret       = $this->getDI()->get("db_fle")->query($sql)->fetch(\PDO::FETCH_ASSOC);
        return $ret;
    }

    /**
     * 获取性别
     * @param int $sex 简历中的性别
     * @param int $callName 简历中的称呼
     * @return int
     */
    public function getResumeSex($sex, $callName): int
    {
        if (isset($sex) && $sex) { //如果性别字段有值
            return $sex;
        } else { //性别字段无值，根据称呼字段判断
            if ($callName == self::RESUME_CALL_NAME_MR) {
                return self::RESUME_SEX_MALE;
            } elseif (in_array($callName, [self::RESUME_SEX_MISS, self::RESUME_SEX_FEMALE])) {
                return self::RESUME_SEX_FEMALE;
            } else {
                return 0;
            }
        }
    }

    /**
     * 获取fbi中，存在指定菜单id的在职员工及相应的菜单信息
     * @param $menu_ids
     * @return mixed
     */
    public function getFbiStaffMenusInfo($menu_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('rsm.staff_info_id ,GROUP_CONCAT(menu_id)  menu_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->join(RoleStaffMenusModel::class, 'hsi.staff_info_id = rsm.staff_info_id', 'rsm');
        $builder->where('hsi.state = 1');
        $builder->inWhere('rsm.menu_id', $menu_ids);
        $builder->groupBy("hsi.staff_info_id");

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取fbi中，存在指定菜单id的角色信息
     * @param $menu_ids
     * @return mixed
     */
    public function getFbiRoleMenusInfo($menu_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('role_id ,GROUP_CONCAT(menu_id)  menu_id');
        $builder->from(RoleMenusModel::class);
        $builder->inWhere('menu_id', $menu_ids);
        $builder->groupBy("role_id");

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取员工的语言环境
     * @param $staff_info_id
     * @return mixed|string
     */
    public function getAcceptLanguage($staff_info_id)
    {
        $staffAccount = StaffAccountModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and equipment_type in ({equipment_types:array})',
            'bind'       => [
                'staff_id'        => $staff_info_id,
                'equipment_types' => [
                    LangEnums::$equipment_type['kit'],
                    LangEnums::$equipment_type['backyard'],
                ],
            ],
            'order'      => 'updated_at desc',
            'columns'    => 'accept_language',
        ]);
        if (empty($staffAccount)) {
            return getCountryDefaultLang();
        }
        $staffAccount = $staffAccount->toArray();
        $language     = $staffAccount['accept_language'] ?? getCountryDefaultLang();
        return substr($language, 0, 2);
    }

    /**
     * 重置员工未来的的off 和 ph
     * 入队
     * @param $data
     * @return mixed
     */
    public function pushRestStaffOffDayPublicHoliday($data)
    {
        $data = json_encode($data);
        $this->getDI()->get('logger')->write_log('pushRestStaffOffDayPublicHoliday  ' . $data, 'info');
        return $this->getDI()->get('redis')->lpush(RedisListEnums::REST_STAFF_OFF_DAY_PH_DAY, $data);
    }

    /**
     * 重置员工未来的的off 和 ph
     * 出队
     * @return mixed
     * @throws ValidationException
     */
    public function popRestStaffOffDayPublicHoliday()
    {
        $redis  = $this->getDI()->get('redis');
        $params = $redis->rpop(RedisListEnums::REST_STAFF_OFF_DAY_PH_DAY);
        //$this->getDI()->get('logger')->write_log(['popRestStaffOffDayPublicHoliday' => $params], 'info');
        if (empty($params)) {
            throw new ValidationException(RedisListEnums::REST_STAFF_OFF_DAY_PH_DAY . ' is  empty !!!');
        }
        sleep(4);
        return json_decode($params, true);
    }

    /**
     * 重置员工的 off day  ph day
     * @throws BusinessException
     * @throws \Exception
     */
    public function restStaffOffDayPublicHolidayDay($staffId = 0): bool
    {
        if(!empty($staffId)){
            $params['staff_info_id'] = $staffId;
        }else{
            $params = $this->popRestStaffOffDayPublicHoliday();
        }

        if (empty($params['staff_info_id'])) {
            throw new BusinessException('staff_info_id empty!!');
        }
        echo 'deal staff_id :'.$params['staff_info_id'].PHP_EOL;

        $staffInfo = $this->getHrStaffInfo($params['staff_info_id']);

        $params['date_at'] = date('Y-m-d');

        //echo 'restStaffOffDayPublicHolidayDay:' .json_encode($params).PHP_EOL;
        /**
         * 清理未来的休息日
         */
        $db = BackyardBaseModel::beginTransaction($this);
        try {
            $staffOffDayService = new StaffOffDayService();
            //删除 src_week = 0 的 未来日期的off
            $staffOffDayService->deleteStaffData($params['staff_info_id'], $params['date_at']);
            $staffPublicHolidayService = new StaffPublicHolidayService();
            $staffPublicHolidayService->deleteStaffData($params['staff_info_id'],$params['date_at'],$params['remark']??'');
            $staffVacationService = new StaffVacationService();
            $staffVacationService->cancelOffDayLeave($params['staff_info_id'],$params['date_at'],$params['remark']??'system cancel');
            $db->commit();
        } catch (\Exception $exception) {
            $db->rollback();
            throw $exception;
        }
        //固定休的人
        if ($staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_2) {
            /**
             * 重新设置未来的休息日
             */
            $deal_params             = [
                'staff_info_ids' => [$staffInfo['staff_info_id']],
            ];
            $StaffFixedDayOffService = new StaffFixedDayOffService();
            if (isCountry('my')) {
                $StaffFixedDayOffService = new \App\Modules\My\Services\StaffFixedDayOffService;
            }
            $result = $StaffFixedDayOffService->execute([$staffInfo['staff_info_id']]);
            $this->logger->info(['restStaffOffDayPublicHolidayDay' => $result]);
            //查询 员工私有ph 加off
            if(!isCountry('PH')){
                // 这个是查公共假期ph 根据员工信息
                $holidayServer = new HolidayService();
                $holidayServer = reBuildCountryInstance($holidayServer);
                $staffPhData = $holidayServer->getHolidayByStaffInfo($staffInfo, $params['date_at']);
                $extendParam['staff_ids'] = [$params['staff_info_id']];
                //给系统的公共假期补off
                (new HolidayService())->addDefaultOff($staffPhData,$extendParam);
            }

            if (!(isCountry('PH') || isCountry('ID'))) {
                /**
                 * 补未来的ph
                 */
                $start_date                = date('Y-m-d');
                $end_date                  = date('Y-m-t', strtotime('+1 month'));
                $countryCode               = ucfirst(env('country_code'));
                $staffPublicHolidayService = 'App\Modules\\' . $countryCode . '\Services\StaffPublicHolidayService';
                if (class_exists($staffPublicHolidayService)) {
                    $service = new $staffPublicHolidayService;
                } else {
                    $service = new StaffPublicHolidayService();
                }
                //新增需求 员工补的ph  对应日期补off
                $service->addPublicHolidayIntersectOffDay($start_date, $end_date, $deal_params['staff_info_ids']);
            }
        }
        $defaultRestDayService = new DefaultRestDayService;
        //不是新创建的用户变更需要删除
        if (!$params['is_create']) {
            $defaultRestDayService->delete($params['staff_info_id']);
        } elseif ($staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1) {
            if (!empty($params['default_rest_day_date'])) {
                $this->createDefaultRestDay($params['staff_info_id'], $params['default_rest_day_date']);
            }
            $db = $this->db_backyard;
            try{
                $db->begin();
                if(isset($params['is_create']) && $params['is_create'] && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_SIX) {
                    (new SchedulingSuggestionService())->edit_suggest_number_by_staff_change([$staffInfo['sys_store_id']]);
                }
                $defaultRestDayService->setALl([$params['staff_info_id']]);
                $db->commit();
            }catch (\Throwable $e) {
                $db->rollback();
                $this->logger->notice($e->getTraceAsString());
            }
        }
        //新入职的员工 初始化班次
        if (!isCountry('MY') && isset($params['is_create']) && $params['is_create']) {
            (new StaffShiftSettingService())->createShiftAfterEntry($params['staff_info_id']);
        }

        return true;
    }

    /**
     * 获取待完善列表--除泰国，其他国家有
     * @param $params
     * @return mixed
     */
    public function getStaffList($params)
    {
        try {
            $params['page']      = empty($params['page']) ? 1 : $params['page'];
            $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
            $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

            $total = $this->getSyncStaffQuery($params, true);
            $list  = $this->getSyncStaffQuery($params);

            $data['total'] = !empty($total) ? intval($total['count']) : 0;
            $data['list']  = $list;

            return $data;
        } catch (\Exception $e) {
            $this->logger->write_log('获取处理详情信息，出现异常, 原因是' . $e->getMessage(), 'error');
        }
    }

    /**
     * 获取待完善列表
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getSyncStaffQuery($params, $isCount = false)
    {
        $columns = "staff_info_id, name, DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as created_at";
        if ($isCount) {
            $columns = 'count(*) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(StaffInfoStagingModel::class);
        $builder->where("status = :state:", ['state' => StaffInfoStagingModel::STATUS_0]);

        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        $builder->orderBy('id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description: 获取员工管辖范围 这里是处理好的数据  部门包含了子部门 返回departments=>[1,2,3],大区 片区 都把 -2 转成实际数据
     * @param int $staff_info_id
     * @param int $type 1 管辖范围 2 职级白名单管辖范围
     * @return   array  :[
     *                     'departments'      => [1,2,4],
     *                     'stores'           => [-2],
     *                     'regions'          =>  [1,2,4],
     *                     'pieces'           =>  [1,2,4],
     *                     'store_categories' => [1,2,4],
     *                     ];
     * <AUTHOR> L.J
     * @time       : 2022/10/10 16:37
     */
    public function getStaffJurisdiction(int $staff_info_id, int $type = 1): array
    {
        if (empty($staff_info_id) || !in_array($type, [
                HrStaffManageDepartmentModel::TYPE_STAFF,
                HrStaffManageDepartmentModel::TYPE_WHITELIST,
                HrStaffManageDepartmentModel::TYPE_FLASH_BOX,
            ])) {
            return [];
        }
        $all_id      = HrStaffManageStoreModel::$all_id; // 勾选了 all  大区  片区 网点
        $departments = $this->getStaffManageDepartment($staff_info_id, $type);


        //查询大区
        $staff_regions = HrStaffManageRegionModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,region_id',
        ])->toArray();
        $regions       = !empty($staff_regions) ? array_column($staff_regions, 'region_id') : [];
        if (in_array($all_id, $regions)) {
            //all  所有大区
            $region  = SysManageRegionModel::find([
                'columns'    => 'id,name',
            ])->toArray();
            $regions = array_column($region, 'id');
        }

        //查询片区
        $staff_pieces = HrStaffManagePieceModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,piece_id',
        ])->toArray();
        $pieces       = !empty($staff_pieces) ? array_column($staff_pieces, 'piece_id') : [];
        if (in_array($all_id, $pieces)) {
            //all  所有片区
            $piece  = SysManagePieceModel::find([
                'columns'    => 'id,name',
            ])->toArray();
            $pieces = array_column($piece, 'id');
        }
        //查询网点
        $staff_stores = HrStaffManageStoreModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,store_id',
        ])->toArray();
        $stores       = !empty($staff_stores) ? array_column($staff_stores, 'store_id') : [];
        //查询网点类型
        $store_categories = HrStaffManageStoreCategoryModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,store_category',
        ])->toArray();

        $store_categories = !empty($store_categories) ? array_column($store_categories, 'store_category') : [];

        //加盟商大区
        $staff_franchisee_regions = HrStaffFranchiseeManageRegionModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,region_id',
        ])->toArray();
        $franchisee_regions = !empty($staff_franchisee_regions) ? array_column($staff_franchisee_regions, 'region_id') : [];
        if (in_array($all_id, $franchisee_regions)) {
            $franchisee_region_list = FranchiseeManageRegionModel::find([
                'columns'    => 'id,name',
            ])->toArray();
            $franchisee_regions     = array_column($franchisee_region_list, 'id');
        }

        //加盟商片区
        $staff_franchisee_pieces = HrStaffFranchiseeManagePieceModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,piece_id',
        ])->toArray();
        $franchisee_pieces = !empty($staff_franchisee_pieces) ? array_column($staff_franchisee_pieces, 'piece_id') : [];
        if (in_array($all_id, $franchisee_pieces)) {
            $franchisee_piece_list = FranchiseeManagePieceModel::find([
                'columns'    => 'id,name',
            ])->toArray();
            $franchisee_pieces     = array_column($franchisee_piece_list, 'id');
        }

        return [
            'departments'        => $departments,
            'stores'             => array_values(array_unique(array_filter($stores))),
            'regions'            => array_values(array_unique(array_filter($regions))),
            'pieces'             => array_values(array_unique(array_filter($pieces))),
            'store_categories'   => array_values(array_unique(array_filter($store_categories))),
            'franchisee_regions' => array_values(array_unique(array_filter($franchisee_regions))),
            'franchisee_pieces'  => array_values(array_unique(array_filter($franchisee_pieces))),
        ];
    }

    /**
     * @description:获取管辖部门
     * @author: L.J
     * @time: 2022/10/25 11:49
     */
    public function getStaffManageDepartment(int $staff_info_id, int $type = 1): array
    {
        if (empty($staff_info_id) || !in_array($type,
                [
                    HrStaffManageDepartmentModel::TYPE_STAFF,
                    HrStaffManageDepartmentModel::TYPE_WHITELIST,
                    HrStaffManageDepartmentModel::TYPE_FLASH_BOX,
                ])) {
           return [];
        }
        $departments = [];
        //查询管辖部门
        // -- sql select department_id,is_include_sub from hr_staff_manage_department where staff_info_id = '56780' and and deleted = 0 and type = 1;
        $staff_departments = HrStaffManageDepartmentModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'department_id,is_include_sub',
        ])->toArray();

        $departments         = []; // 部门 id
        $is_include_sub_deps = []; //需要找子部门的  id
        //查询出来哪些需要找子部门
        foreach ($staff_departments as $v) {
            $departments[] = $v['department_id'];
            if (!empty($v['is_include_sub'])) {
                //需要查询子部门的 id
                $is_include_sub_deps[] = $v['department_id'];
            }
        }
        if ($is_include_sub_deps) {
            //查询子部门
            $dept_lists = [];

            //获取需要查询子部门的部门
            // -- sql select id,ancestry_v3 from sys_department where id in (32,125) and deleted = 0;
            $is_include_sub_departments = SysDepartmentModel::find([
                'conditions' => ' id IN ({ids:array})',
                'bind'       => ['ids' => $is_include_sub_deps],
                'columns'    => 'id,ancestry_v3',
            ])->toArray();
            //查询子部门
            foreach ($is_include_sub_departments as $ancestry_v3) {
                // -- sql select id,ancestry_v3 from sys_department where ancestry_v3 like '999/%' and deleted = 0;
                $dept_list = SysDepartmentModel::find([
                    'conditions' => ' ancestry_v3 like :chain: ',
                    'bind'       => [
                        'chain' => "{$ancestry_v3['ancestry_v3']}/%",
                    ],
                    'columns'    => 'id,ancestry_v3',
                ])->toArray();
                //这样子的结构 [[1,3],[3,4]]
                $dept_lists[] = array_column($dept_list, 'id');
            }

            if (!empty($dept_lists)) {
                $dept_lists  = array_merge(...$dept_lists);
                $departments = array_merge($departments, $dept_lists);
            }
        }
        return array_values(array_unique(array_filter($departments)));
    }

    /**
     * @description: 和获取管辖范围配合使用 获取 管辖大区 片区 网点类型 + 网点的 的网点
     * @param array  [
     *                     'departments'      => [1,2,4],
     *                     'stores'           => [-2],
     *                     'regions'          =>  [1,2,4],
     *                     'pieces'           =>  [1,2,4],
     *                     'store_categories' => [1,2,4],
     *                     ];
     * @return     : [1,2,3]
     * <AUTHOR> L.J
     * @time       : 2022/10/13 19:11
     * -- sql SELECT * from sys_store where state = 1  and ( manage_region IN (1,2) or manage_piece IN (1,2) or category IN (1,2));
     */
    public function getStaffJurisdictionStore($jurisdiction = [])
    {
        $stores = $jurisdiction['stores'] ?? []; // 这是管辖网点
        //查询大区域查询出网点
        if (!empty($jurisdiction['regions'])) {
            $builder_store_sql[]                  = 'manage_region IN ({manage_regions:array})';
            $builder_store_bind['manage_regions'] = $jurisdiction['regions'];
        }
        //查询片区域查询出网点
        if (!empty($jurisdiction['pieces'])) {
            $builder_store_sql[]                 = 'manage_piece IN ({manage_pieces:array})';
            $builder_store_bind['manage_pieces'] = $jurisdiction['pieces'];
        }

        //根据网点类型查询出网点
        if (!empty($jurisdiction['store_categories'])) {
            $builder_store_sql[]                   = 'category IN ({manage_category:array})';
            $builder_store_bind['manage_category'] = $jurisdiction['store_categories'];
        }
        //查询出所有符合的网点
        if (!empty($builder_store_sql) && !empty($builder_store_bind)) {
            $manage_stores = SysStoreModel::find([
                'conditions' => "( " . implode(' or ', $builder_store_sql) . " ) ",
                'bind'       => $builder_store_bind,
                'columns'    => ['id'],
            ])->toArray();
            $stores        = !empty($manage_stores) ? array_merge($stores,
                array_column($manage_stores, 'id')) : $stores;
        }
        return array_values(array_unique(array_filter($stores)));
    }

    /**
     * 获取指定工号的工资发放状态列表
     * @param array $staff_info_ids
     * @return array
     */
    public function getStaffPaymentStateListByStaffInfoIds($staff_info_ids = [])
    {
        $staff_list = [];
        if (!empty($staff_info_ids)) {
            $staff_info_ids_str = implode(',', $staff_info_ids);
            $sql                = " select staff_info_id,name,job_title,sys_department_id,node_department_id,sys_store_id,payment_state,payment_markup,stop_payment_type
                 from hr_staff_info
                 where staff_info_id in ({$staff_info_ids_str}) ;
                ";

            $list = $this->getDI()->get('db_backyard')->fetchAll($sql);

            $staff_list = array_column($list, null, 'staff_info_id');
        }
        return $staff_list;
    }

    /**
     * 创建轮休用户默认休息日(新用户)
     * @param $staffInfoId
     * @param $restDate
     * @return bool
     */
    public function createDefaultRestDay($staffInfoId, $restDate): bool
    {
        $this->logger->info(json_encode([$staffInfoId, $restDate]));
        try {
            if (empty($staffInfoId) || empty($restDate) || !is_array($restDate) || in_array(0, $restDate)) {
                return false;
            }
            $mName = StaffDefaultRestDayModel::class;
            $this->modelsManager->executeQuery("update {$mName} set is_deleted=1 where staff_info_id=:staff_id:",
                ['staff_id' => $staffInfoId])->success();
            $restDay              = implode('', $restDate);
            $model                = new StaffDefaultRestDayModel();
            $model->staff_info_id = $staffInfoId;
            $model->rest_day      = $restDay;
            $result               = $model->save();
            if (!$result) {
                $this->logger->notice('StaffDefaultRestDay create fail ' . json_encode([$staffInfoId, $restDate]));
                return false;
            }
        } catch (\Exception $e) {
            $this->logger->error(json_encode([$staffInfoId, $restDate, $e->getMessage()]));
            return false;
        }
        return true;
    }

    /**
     * @description:拼装管辖范围 sql
     * @author: L.J
     * @time: 2022/11/24 16:16
     * @param $staff_info_id
     * @param string $pre
     * @return array
     * @throws \Exception
     */
    public function getJurisdictionSql($staff_info_id, $pre = 'hsi')
    {
        $return_data = [
            'builder_sql'  => [],
            'builder_bind' => [],
        ];
        //查询管辖部门-管辖大区-管辖片区-管辖网点
        $getStaffData = $this->getStaffJurisdiction($staff_info_id);

        $this->getDI()->get("logger")->write_log("getJurisdictionSql-{$staff_info_id}-管辖范围:" . json_encode($getStaffData),
            'info');
        //如果没有范围返回空
        if (empty($getStaffData['departments']) && empty($getStaffData['regions']) && empty($getStaffData['pieces']) && empty($getStaffData['stores']) && empty($getStaffData['store_categories'])) {
            return $return_data;
        }

        if (isset($getStaffData['departments']) && $getStaffData['departments']) {
            $return_data['builder_sql'][]                                    = " ( {$pre}.node_department_id IN ({jurisdiction_node_department_ids:array}) and {$pre}.sys_store_id = '-1' ) ";
            $return_data['builder_bind']['jurisdiction_node_department_ids'] = $getStaffData['departments'];
        }

        //如果存在 -2 就代表要查询所有网点 就不用再找网点了
        if (in_array(HrStaffManageStoreModel::$all_id, $getStaffData['stores'])) {
            $return_data['builder_sql'][]                   = " {$pre}.sys_store_id != :no_sys_store_id: ";
            $return_data['builder_bind']['no_sys_store_id'] = GlobalEnums::HEAD_OFFICE_ID;
        } else {
            //获取大区 片区 网点类型 + 网点 的网点
            $getStaffData['stores'] = $this->getStaffJurisdictionStore($getStaffData);
            if ($getStaffData['stores']) {
                $return_data['builder_sql'][]                              = " {$pre}.sys_store_id IN ({jurisdiction_sys_store_ids:array}) ";
                $return_data['builder_bind']['jurisdiction_sys_store_ids'] = $getStaffData['stores'];
            }
        }

        return $return_data;
    }

    /**
     * 获取员工设备语言
     * @param $staffIds
     * @return array
     */
    public function getStaffEquipmentLanguage($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $staffAccountList = StaffAccountModel::find([
            'columns'    => 'staff_info_id,substr(accept_language,1,2) as accept_language',
            'conditions' => 'staff_info_id in ({ids:array}) and equipment_type=:equipment_type:',
            'bind'       => ['ids' => $staffIds, 'equipment_type' => StaffAccountModel::$equipment_type['backyard']],
        ])->toArray();
        return array_column($staffAccountList, 'accept_language', 'staff_info_id');
    }

    /**
     * 格式化地址
     * @param $province     string
     * @param $city         string
     * @param $district     string
     * @param $otherItems   array
     * @param $postCode     string
     * @return string       string
     */
    public function getStaffAddress(
        string $province,
        string $city,
        string $district,
        array $otherItems,
        string $postCode
    ): string {
        $strings = [];

        // 详细地址
        foreach ($otherItems as $key => $item) {
            $strings[$key] = !empty($item) ? $item : "";
        }

        // 获取乡
        $strings['district'] = "";
        if (!empty($district)) {
            $districtList        = $this->districtList();
            $strings['district'] = !empty($districtList[$district]['name']) ? $districtList[$district]['name'] : $district;
        }

        // 获取市
        $strings['city'] = "";
        if (!empty($city)) {
            $cityList        = $this->cityList();
            $strings['city'] = !empty($cityList[$city]['name']) ? $cityList[$city]['name'] : $city;
        }

        // 获取省
        $strings['province'] = "";
        if (!empty($province)) {
            $provinceList        = $this->provinceList();
            $strings['province'] = !empty($provinceList[$province]['name']) ? $provinceList[$province]['name'] : $province;
        }

        $strings['postcode'] = $postCode;

        return implode(' ', array_values($strings));
    }
    /**
     * @description 获取直线下级
     * @param $staff_info_id
     * @return array
     */
    public function getSubordinateStaff($staff_info_id): array
    {
        $staff_items = HrStaffInfoModel::find([
            'conditions' => "manger = :manger: ",
            'bind' => [
                'manger' => $staff_info_id,
            ],
            'columns' => 'staff_info_id',
        ])->toArray();
        return !empty($staff_items) ? array_column($staff_items, 'staff_info_id') : [];
    }

    /**
     * @param $working_day_rest_type
     * @return int[]
     */
    public function getWorkingDayRestType($working_day_rest_type): array
    {
        $week_working_day = $rest_type = 0;
        switch ($working_day_rest_type) {
            case Enums::WORKING_DAY_REST_TYPE_51:
                $week_working_day = 5;
                $rest_type        = 1;
                break;
            case Enums::WORKING_DAY_REST_TYPE_52:
                $week_working_day = 5;
                $rest_type        = 2;
                break;
            case Enums::WORKING_DAY_REST_TYPE_61:
                $week_working_day = 6;
                $rest_type        = 1;
                break;
            case Enums::WORKING_DAY_REST_TYPE_62:
                $week_working_day = 6;
                $rest_type        = 2;
                break;
        }
        return ['week_working_day' => $week_working_day, 'rest_type' => $rest_type];
    }

    /**
     * 一次性 th 刷社保离职日期 逻辑
     * @return array
     */
    public function staff_social_security_leave_date_logic()
    {
        $count_num= 0;
        $success_num = 0;
        try {
            $page = 1;
            $pagesize = 500;
            while (true){
                $data = HrStaffInfoModel::find([
                    'columns'    => 'staff_info_id,leave_date',
                    'conditions' => 'state = :state: and is_sub_staff = :is_sub_staff: and formal in ({formal:array}) and leave_date >= :leave_date: and hire_type not in ({hire_type:array})',
                    'bind'       => [
                        'state'        => HrStaffInfoModel::STATE_RESIGN,
                        'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO,
                        'formal'       => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                        'leave_date'   => '2022-01-01',
                        'hire_type'   => HrStaffInfoModel::$agentTypeTogether,
                    ],
                    'order' => 'id DESC',
                    'offset' => ($page - 1) * $pagesize,
                    'limit' => $pagesize,
                ])->toArray();
                if (empty($data)){
                    break;
                }
                $data_items = HrStaffItemsModel::find([
                    'columns'    => 'staff_info_id,value',
                    'conditions' => 'staff_info_id in ({staff_info_id:array}) and item = :item:',
                    'bind'       => [
                        'item'          => HrStaffItemsModel::ITEM_SOCIAL_SECURITY_LEAVE_DATE,
                        'staff_info_id' => array_column($data, 'staff_info_id'),
                    ],
                ])->toArray();
                $data_items_arr = $data_items ? array_column($data_items,'value','staff_info_id') : [];

                foreach ($data as $k => $v) {
                    $count_num++;
                    if (!empty($v['staff_info_id']) && !empty($v['leave_date']) && empty($data_items_arr[$v['staff_info_id']])) {
                        $success_num++;
                        $this->staffSocialSecurityLeaveDate($v['staff_info_id'],$v['leave_date']);
                    }
                }
                $page++;
            }
            
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("staff_social_security_leave_date_logic , 处理失败，失败原因:" . $e->getMessage(),
                'error');
            return [];
        }
        return ['count_num'=>$count_num,'success_num'=>$success_num];
    }


    /**
     * 计算社保离职日期
     * @param $staffInfo
     * @param $leave_date
     * @param $only_edit_leave_date
     * @return false|string
     */
    protected function calSocialSecurityLeaveDate($staffInfo, $leave_date, $only_edit_leave_date)
    {
        if ($only_edit_leave_date) {
            return formatHrDate($leave_date);
        }

        $staff_info_id = $staffInfo['staff_info_id'];
        if (!in_array($staffInfo['leave_source'],
            [LeaveManagerService::LEAVE_SOURCE_OFF3DAYS, LeaveManagerService::LEAVE_SOURCE_NOTPAY])) {
            return formatHrDate($leave_date);
        }

        $attendanceDataAll = AttendanceDataV2Model::count([
            'conditions' => 'staff_info_id = :staff_info_id: and stat_date >= :stat_date:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'stat_date' => '2022-01-01'],
            'columns'    => 'staff_info_id,AB,PH,OFF,attendance_started_at,attendance_end_at,leave_time_type',
        ]);
        // 在之前的数据就归档了 和产品沟通 之前数据就不往下找了 用入职日期
        if (empty($attendanceDataAll)) {
            return formatHrDate($staffInfo['hire_date']);
        }

        $i = 0;
        while (true) {
            $i++;
            $stat_date  = date('Y-m-d', strtotime("-" . $i . " day", strtotime($leave_date)));
            $_stat_date = date('Y-m-d', strtotime("-" . ($i + 1) . " day", strtotime($leave_date)));
            // 在之前的数据就归档了 和产品沟通 之前数据就不往下找了
            if ($stat_date < '2022-01-01') {
                $social_security_leave_date = formatHrDate($staffInfo['hire_date']);
                break;
            }
            $attendanceData = AttendanceDataV2Model::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and stat_date = :stat_date:',
                'bind'       => ['staff_info_id' => $staff_info_id, 'stat_date' => $stat_date],
                'columns'    => 'staff_info_id,AB,PH,OFF,attendance_started_at,attendance_end_at,leave_time_type',
            ]);
            if ($attendanceData) {
                $attendanceData = $attendanceData->toArray();
                // 是否有出勤 （只要有一个打卡记录就算出勤）
                if (!empty($attendanceData['attendance_started_at']) || !empty($attendanceData['attendance_end_at'])) {
                    $social_security_leave_date = date('Y-m-d', strtotime("+1 day", strtotime($stat_date)));
                    break;
                }
                // 是否有请假 （不区分半天还是全天请假）
                if (!empty($attendanceData['leave_time_type']) && $attendanceData['OFF'] < 10) {
                    $social_security_leave_date = date('Y-m-d', strtotime("+1 day", strtotime($stat_date)));
                    break;
                }
                // 是否有PH
                if ($attendanceData['PH'] != 0) {
                    $is_effective_attendance = $this->is_effective_attendance($staff_info_id, $_stat_date);
                    if (!$is_effective_attendance) {
                        continue;
                    } else {
                        $social_security_leave_date = date('Y-m-d', strtotime("+1 day", strtotime($stat_date)));
                        break;
                    }
                }
                // 是否是OFF
                if ($attendanceData['OFF'] > 0) {
                    $is_effective_attendance = $this->is_effective_attendance($staff_info_id, $_stat_date);
                    if (!$is_effective_attendance) {
                        continue;
                    } else {
                        $social_security_leave_date = date('Y-m-d', strtotime("+1 day", strtotime($stat_date)));
                        break;
                    }
                }
            }
        }
        $this->getDI()->get('logger')->write_log("staffSocialSecurityLeaveDate ,staff_info_id:" . $staff_info_id . ", social_security_leave_date:".$social_security_leave_date.", leave_date:" . $leave_date . "循环次数：".$i,'info');
        return $social_security_leave_date;
    }



    /**
     * 社保离职日期
     * @param $staff_info_id
     * @param $leave_date
     * @param $operator_id
     * @param bool $only_edit_leave_date
     * @return false|string
     */
    public function staffSocialSecurityLeaveDate($staff_info_id, $leave_date,$operator_id, bool $only_edit_leave_date = false)
    {
        if (empty($staff_info_id) || empty($leave_date) || strtotime($leave_date) < strtotime('2022-01-01')){
            return false;
        }
        $leave_date = date('Y-m-d', strtotime($leave_date));

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staff_info_id,
            ],
        ]);
        if (empty($staffInfo)){
            return false;
        }
        $staffInfo = $staffInfo->toArray();
        $social_security_leave_date = $this->calSocialSecurityLeaveDate($staffInfo,$leave_date,$only_edit_leave_date);
        $hrStaffItem = HrStaffItemsModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and item = :item: ',
            'bind'       => ['staff_id' => $staff_info_id, 'item' => HrStaffItemsModel::ITEM_SOCIAL_SECURITY_LEAVE_DATE],
        ]);
        if (empty($hrStaffItem)) {
            $hrStaffItem                = new HrStaffItemsModel();
            $hrStaffItem->staff_info_id = $staff_info_id;
            $hrStaffItem->item          = HrStaffItemsModel::ITEM_SOCIAL_SECURITY_LEAVE_DATE;
        }else{
            $before_social_security_leave_date =  $hrStaffItem->value;
        }
        $hrStaffItem->value = $social_security_leave_date;
        $hrStaffItem->save();
        //记录变更日志
        $before_data = ['social_security_leave_date' => $before_social_security_leave_date??''];
        $after_data  = ['social_security_leave_date' => $social_security_leave_date];
        $result = (new StaffUpdaterService())->saveOperateLogs($operator_id, $staff_info_id, $before_data,
            $after_data);
        return $hrStaffItem->save() && $result;
    }

    /**
     * 查询 管辖指定工号（被管辖）+ 指定角色的工号
     * @param $staffId
     * @param string $type
     * @return array|mixed
     */
    public function getRoleManageStaffList($staffId, $type = HrStaffManageDepartmentModel::TYPE_STAFF)
    {
        if (empty($staffId) || !in_array($type, [
                HrStaffManageDepartmentModel::TYPE_STAFF,
                HrStaffManageDepartmentModel::TYPE_WHITELIST,
                HrStaffManageDepartmentModel::TYPE_FLASH_BOX,
            ])) {
            return [];
        }
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($staffId);
        if (empty($staffInfo)) {
            return [];
        }

        //员工是否是 总部的人。
        if ($staffInfo['sys_store_id'] == GlobalEnums::HEAD_OFFICE_ID) {
            $managerStaffIds = $this->getManageDepartmentStaff($staffInfo['node_department_id'], $type);
        } else {
            $managerStaffIds = $this->getManageStoreStaff($staffInfo['sys_store_id'], $type);
        }

        //去除 本人
        return array_values(array_diff($managerStaffIds, [$staffId]));
    }

    /**
     * 查询 管辖指定部门id 的 部门
     * @param $departmentId
     * @param $type
     * @return array
     */
    public function getManageDepartmentStaff($departmentId, $type)
    {
        if (empty($departmentId) || !in_array($type,
                [
                    HrStaffManageDepartmentModel::TYPE_STAFF,
                    HrStaffManageDepartmentModel::TYPE_WHITELIST,
                    HrStaffManageDepartmentModel::TYPE_FLASH_BOX,
                ])) {
            return [];
        }

        $departmentInfo = SysDepartmentRepository::getDepartmentInfo($departmentId, ['ancestry_v3']);
        if (empty($departmentInfo)) {
            return [];
        }

        $departmentLink = array_values(explode('/', $departmentInfo['ancestry_v3']));

        //查询管辖部门
        // -- sql select department_id,is_include_sub from hr_staff_manage_department where staff_info_id = '56780' and and deleted = 0 and type = 1;
        $staff_departments = HrStaffManageDepartmentModel::find([
            'conditions' => ' department_id in ({department_ids:array}) and deleted = 0 and type = :type:',
            'bind'       => ['department_ids' => $departmentLink, 'type' => $type],
            'columns'    => 'staff_info_id,department_id,is_include_sub',
        ])->toArray();

        $is_include_sub_staff = []; //管辖 包括子部门的人
        //查询出来哪些需要找子部门
        foreach ($staff_departments as $v) {
            if ($v['department_id'] == $departmentId) {
                $is_include_sub_staff[] = $v['staff_info_id'];
            }
            if (!empty($v['is_include_sub'])) {
                //需要查询子部门的 id
                $is_include_sub_staff[] = $v['staff_info_id'];
            }
        }

        if (empty($is_include_sub_staff)) {
            return [];
        }

        $managerStaffIds = array_values(array_unique($is_include_sub_staff));

        if (empty($managerStaffIds)) {
            return [];
        }
        return $managerStaffIds;
    }

    /**
     * 查询 管辖指定 网点的 工号
     * @param $storeId
     * @param $type
     * @return array
     */
    public function getManageStoreStaff($storeId, $type)
    {
        if (empty($storeId) || !in_array($type,
                [
                    HrStaffManageDepartmentModel::TYPE_STAFF,
                    HrStaffManageDepartmentModel::TYPE_WHITELIST,
                    HrStaffManageDepartmentModel::TYPE_FLASH_BOX,
                ])) {
            return [];
        }

        $all_id = HrStaffManageStoreModel::$all_id; // 勾选了 all  大区  片区 网点

        $storeInfo = (new SysStoreRepository())->getStoreById($storeId);

        if (empty($storeInfo)) {
            return [];
        }
        $managerStaffIds = [];

        //查询网点
        $staff_stores = HrStaffManageStoreModel::find([
            'conditions' => ' store_id  in ({store_ids:array}) and deleted = 0 and type = :type:',
            'bind'       => ['store_ids' => [$all_id, $storeId], 'type' => $type],
            'columns'    => 'staff_info_id,store_id',
        ])->toArray();

        $managerStoreStaffIds = !empty($staff_stores) ? array_values(array_unique(array_column($staff_stores,
            'staff_info_id'))) : [];

        if (!empty($managerStoreStaffIds)) {
            $managerStaffIds = array_merge($managerStaffIds, $managerStoreStaffIds);
        }

        //查询网点类型
        $store_categories = HrStaffManageStoreCategoryModel::find([
            'conditions' => ' store_category  = :store_category: and deleted = 0 and type = :type:',
            'bind'       => ['store_category' => $storeInfo['category'], 'type' => $type],
            'columns'    => 'staff_info_id,store_category',
        ])->toArray();

        $managerStoreCategoryStaffIds = !empty($store_categories) ? array_values(array_unique(array_column($store_categories,
            'staff_info_id'))) : [];

        if (!empty($managerStoreCategoryStaffIds)) {
            $managerStaffIds = array_merge($managerStaffIds, $managerStoreCategoryStaffIds);
        }

        //查询大区
        $staffRegions           = HrStaffManageRegionModel::find([
            'conditions' => ' region_id in ({region_ids:array}) and deleted = 0 and type = :type:',
            'bind'       => ['region_ids' => [$all_id, $storeInfo['manage_region']], 'type' => $type],
            'columns'    => 'staff_info_id,region_id',
        ])->toArray();
        $managerRegionsStaffIds = !empty($staffRegions) ? array_values(array_unique(array_column($staffRegions,
            'staff_info_id'))) : [];

        if (!empty($managerRegionsStaffIds)) {
            $managerStaffIds = array_merge($managerStaffIds, $managerRegionsStaffIds);
        }

        //查询片区
        $staffPieces = HrStaffManagePieceModel::find([
            'conditions' => ' piece_id in ({piece_ids:array}) and deleted = 0 and type = :type:',
            'bind'       => ['piece_ids' => [$all_id, $storeInfo['manage_piece']], 'type' => $type],
            'columns'    => 'staff_info_id,piece_id',
        ])->toArray();

        $managerPieceStaffIds = !empty($staffPieces) ? array_values(array_unique(array_column($staffPieces,
            'staff_info_id'))) : [];

        if (!empty($managerPieceStaffIds)) {
            $managerStaffIds = array_merge($managerStaffIds, $managerPieceStaffIds);
        }

        $managerStaffIds = array_values(array_unique($managerStaffIds));

        if (empty($managerStaffIds)) {
            return [];
        }
        return $managerStaffIds;
    }

    /**
     * 是否是有效出勤或请假或PH或OFF
     * @param $staff_info_id
     * @param $stat_date
     * @return bool
     */
    public function is_effective_attendance($staff_info_id,$stat_date)
    {
        $attendanceData = AttendanceDataV2Model::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and stat_date = :stat_date:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'stat_date' => $stat_date],
            'columns'    => 'staff_info_id,AB,PH,OFF,attendance_started_at,attendance_end_at,leave_time_type',
        ]);
        if ($attendanceData) {
            $attendanceData = $attendanceData->toArray();
            // 是否有出勤 （只要有一个打卡记录就算出勤）
            if (!empty($attendanceData['attendance_started_at']) || !empty($attendanceData['attendance_end_at'])){
                return true;
            }
            // 有请假
            if (!empty($attendanceData['leave_time_type']) && $attendanceData['OFF'] < 10) {
                return true;
            }
            // 是否是PH
            if ($attendanceData['PH'] != 0) {
                return true;
            }
            // 是否是OFF
            if ($attendanceData['OFF'] > 0) {
                return true;
            }
            return false;
        }else{
            return false;
        }
    }

    /**
     * 使用中
     * @param $staffId
     * @param string $columns
     * @return array
     * @throws ValidationException
     */
    public function getManagerStore($local,$params): array
    {

        if(empty($params['staff_id'])){
            throw new ValidationException('need staff_id');
        }
        $staffId = $params['staff_id'];
        $columns = 'id store_id,category,name';
        //获取管辖范围
        $relations        = $this->getStaffJurisdiction($staffId);

        $store_id         = $relations['stores'] ?? [];           //网点
        $region_id        = $relations['regions'] ?? [];          //大区
        $piece_id         = $relations['pieces'] ?? [];           //片区
        $store_categories = $relations['store_categories'] ?? []; //网点类型



        //啥也没取着 就返回空
        if (empty($store_id) && empty($region_id) && empty($piece_id) && empty($store_categories)) {
            return [];
        }


        //先看 store 里面有没有-2 如果有 表示管辖全部
        if (!empty($store_id) && in_array('-2', $store_id)) {
            $store_infos = SysStoreModel::find([
                'columns'    => $columns,
                'conditions' => 'state = 1 ',
            ])->toArray();
        } else {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['s' => SysStoreModel::class]);

            if (!empty($store_id)) {
                $builder->orWhere('id in ({store_id:array})', ['store_id' => $store_id]);
            }
            if (!empty($region_id)) {
                $builder->orWhere('manage_region in ({manage_region:array})', ['manage_region' => $region_id]);
            }
            if (!empty($piece_id)) {
                $builder->orWhere('manage_piece in ({manage_piece:array})', ['manage_piece' => $piece_id]);
            }
            if (!empty($store_categories)) {
                $builder->orWhere('category in ({store_categories:array})', ['store_categories' => $store_categories]);
            }
            $store_infos = $builder->getQuery()->execute()->toArray();
        }
        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $store_infos];
    }

    /**
     * 简化版获取员工信息
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getStaffInfoSimplify($params)
    {
        $staff_info_id         = $params['staff_info_id'] ?? '';
        if (empty($staff_info_id)) {
            throw new ValidationException(self::$t->_('wrong_staff_id'));
        }
        $staff_info = HrStaffInfoModel::findFirst([
            'columns'    => 'staff_info_id,name,identity,mobile,email,job_title,sys_store_id,node_department_id,formal,hire_type,state,contract_company_id',
            'conditions' => 'staff_info_id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ]);
        if (empty($staff_info)) {
            return [];
        }
        return  $staff_info->toArray();
    }

    /**
     * 是否是lnt 员工
     * @param $staff_info_id
     * @return bool
     * @throws ValidationException
     */
    public function isLntStaff($staff_info_id): bool
    {
        if (!isCountry('MY')) {
            return false;
        }
        $staffInfo  = $this->getStaffInfoSimplify(['staff_info_id' => $staff_info_id]);
        $lntCompany = (new SettingEnvService())->getSetVal('lnt_company_ids', ',');
        return in_array($staffInfo['contract_company_id'], $lntCompany);
    }

    /**
     * 异步更新员工信息到HRIS
     * @param $staff_info_id
     * @param $params
     * @return mixed
     */
    public function asyncUpdateStaffInfoToHRIS($staff_info_id,$params)
    {

        $rmq                       = new RocketMQ('update-staff-info');
        $sendData['jsonCondition'] = json_encode($params, JSON_UNESCAPED_UNICODE);
        $sendData['handleType']    = RocketMQ::TAG_HR_STAFF_UPDATE;
        $rmq->setShardingKey($staff_info_id);
        return $rmq->sendOrderlyMsg($sendData, 5);
    }


}