<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\Enums\ApprovalEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\RestClient;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrImportStaffSupportExcelModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrOvertimeModel;
use App\Models\backyard\HrShiftV2Model;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffApplySupportStoreOperateLogsModel;
use App\Models\backyard\HrStaffApplySupportStoreSplitModel;
use App\Models\backyard\HrStaffShiftMiddleDateModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\MessageModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SupportOvertimeBackupModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\WorkdaySettingDailyDetailModel;
use App\Services\WorkdaySettingService;
use App\Services\SettingEnvService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class StaffSupportStoreServer extends BaseService
{
    const STAFF_SUPPORT_STORE_OSS_PATH = 'STAFF_SUPPORT_STORE';

    public static $support_job_title = [
        'car_courier' => 1199,
        'bike_courier' => 13,
        'branch_supervisor' => 16,
        'van_courier' => 110,
        'dc_officer' => 37,
        'assistant_branch_supervisor' => 451,
    ];
    public static $not_support_shift_ids = [
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        26,
        28,
        29,
        30,
        40,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        35,
    ];

    public static $support_status = [
        'pending' => 1, //待生效
        'in_force' => 2, //已生效
        'expired' => 3, //已失效
        'cancelled' => 4, //已取消
    ];

    /**
     * 把上传导入的excel 数据存入hr_import_staff_support_excel
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function addStaffSupportExcel($params)
    {
        $operator_id = $params['operator_id']; //操作人工号
        $file_name = $params['file_name']; //上传文件名称
        $excel_data = $params['excel_data'];

        $db = $this->getDI()->get('db_backyard');
        $count = HrImportStaffSupportExcelModel::count([
            'conditions' => "type = :type: and operator_id = :operator_id: and is_del = 0 and status = 1",
            'bind' => [
                'operator_id' => $operator_id,
                'type' => HrImportStaffSupportExcelModel::TYPE_SUPPORT_STAFF,
            ],
        ]);
        if ($count > 0) {
            throw new BusinessException(self::$t->_('staff_support_store_error_1'));//有正在执行导入的任务，请等待导入完成后再进行上传
        }

        try {
            $db->begin();
            $insert_arr = [
                'operator_id' => $operator_id,
                'file_name'   => $file_name,
                'status'      => 1,
                'import_path' => $params['import_path'],
            ];
            $result = $db->insertAsDict("hr_import_staff_support_excel", $insert_arr);
            if ($result) {
                $id = $db->lastInsertId();
                foreach ($excel_data as $key => $value) {
                    // 0工号,1支援网点id,2支援职位,3开始日期,4结束日期,5班次 6是否住宿
                    $item = [$value[0], $value[1], $value[2], $value[3], $value[4], $value[5]];
                    if (isCountry('MY')) {
                        $item = [$value[0], $value[1], $value[2], $value[3], $value[4], $value[5], $value[6], $value[7], $value[8]];
                    } elseif (isCountry('PH')) {
                        $item = [$value[0], $value[1], $value[2], $value[3], $value[4], $value[5], $value[6], $value[7]];
                    }

                    $insert_detail_arr = [
                        'import_excel_id' => $id,
                        'row_content' => json_encode($item),
                    ];
                    $db->insertAsDict("hr_import_staff_support_detail", $insert_detail_arr);
                }
                $db->commit();
                return true;
            } else {
                $this->logger->error([
                    'function' => 'addStaffSupportExcel',
                    'message' => 'error',
                    'params' => $params,
                ]);
                $db->rollback();
                return false;
            }
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->error([
                'function' => 'addStaffSupportExcel',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return false;
        }
    }

    /**
     * 导入员工支援网点数据
     * @param $excel_data
     * @return array
     */
    public function ImportStaffSupport($params)
    {
        BaseService::setLanguage('en');
        try {
            $excel_data = $params['excel_data'] ?? [];
            $return_data = ['error_num' => 0, 'url' => '', 'success_num' => 0];
            $staff_info_ids = array_column($excel_data, 0);
            $staff_info_ids = array_map(function ($item) {
                return str_replace(' ', '', $item);
            }, $staff_info_ids);

            $staff_list     = $this->getStaffInfoByIds($staff_info_ids);
            $staff_position = [];
            if (!empty($staff_info_ids)) {
                $staff_position_list = HrStaffInfoPositionModel::find([
                    'conditions' => "staff_info_id in ({staff_ids:array})",
                    'bind'       => [
                        'staff_ids' => $staff_info_ids,
                    ],
                ])->toArray();
                foreach ($staff_position_list as $key => $value) {
                    $staff_position[$value['staff_info_id']][] = (int)$value['position_category'];
                }
            }

            $store_ids = array_column($excel_data, 1);
            $store_list = (new SysStoreService())->getStoreList($store_ids);
            $store_list = !empty($store_list) ? array_column($store_list, null, 'id') : [];

            $shift_list = $this->getSupportShiftList();//获取所有班次


            $shift_start_list = array_column($shift_list, null, 'start');

            $shift_check = [];
            foreach ($shift_list as $item) {
                $shift_check[] = $item['start'].'-'.$item['end'];
            }

            $error_list = [];
            $db = $this->getDI()->get("db_backyard");
            $current_day = date("Y-m-d");
            $sync_ms_params = [];
            $temp_job_title = [
                'car courier' => self::$support_job_title['car_courier'],
                'bike courier' => self::$support_job_title['bike_courier'],
                'branch supervisor' => self::$support_job_title['branch_supervisor'],
                'van courier' => self::$support_job_title['van_courier'],
                'DC officer' => self::$support_job_title['dc_officer'],
            ];
            $support_job_title_arr = array_values(self::$support_job_title);

            $staffSupportService = new StaffSupportService();
            $logServer = new WorkShiftService();
            $next_year_today = date('Y-m-t', strtotime(date('Y-m-01',strtotime(date("Y-m-d"))) ." +1 month last day of"));//下月最后一天

            $t = BaseService::getTranslation('en');
            foreach ($excel_data as $key => $value) {
                //0工号,1支援网点id,2支援职位,3开始日期,4结束日期,5班次
                $error_message = [];
                $value[3] = date('Y-m-d', trim($value[3]));
                $value[4] = date('Y-m-d', trim($value[4]));
                $staff_info_id   = str_replace(' ', '', $value[0]); //工号
                $store_id = trim($value[1]); //网点id
                $excel_job_title = trim($value[2]);//职位
                $begin_date = $value[3]; //支援开始日期
                $end_date = $value[4]; //支援结束日期
                $excel_shift = trim($value[5]); //支援班次数
                $excel_is_stay = trim($value[6]);//是否住宿
                $job_title_id = $temp_job_title[$excel_job_title] ?? 0;
                $staff_job_title = $staff_list[$staff_info_id]['job_title'] ?? 0;
                if (empty($staff_info_id)) {
                    $error_message[] = $t->_('staff_support_store_import_error_11'); //'工号不能为空';
                } else {
                    $staff_state = $staff_list[$staff_info_id]['state'] ?? 0;
                    if ($staff_state != 1) {
                        $error_message[] = $t->_('staff_support_store_import_error_1'); //'工号非在职';
                    }
                    //Bike Courier 13/Van Courier  110/Branch Supervisor 16/Car Courier 1199
                    if ($staff_job_title == 0 || !in_array($staff_job_title, $support_job_title_arr)) {
                        $error_message[] = $t->_('staff_support_store_import_error_3');//'导入工号职位错误';
                    }
                }

                $store_name = $store_list[$store_id]['name'] ?? '';
                if (empty($store_name)) {
                    $error_message[] = $t->_('staff_support_store_import_error_12');//'网点不能为空';
                } else {
                    $staff_store_id = $staff_list[$staff_info_id]['sys_store_id'] ?? '';
                    if ($staff_store_id == $store_id) {
                        $error_message[] = $t->_('staff_support_store_import_error_10');//'网点错误';
                    }
                }

                if (empty($job_title_id)) {
                    $error_message[] = $t->_('staff_support_store_import_error_16');//'职位不能为空';
                } else {
                    if (!in_array($job_title_id, $support_job_title_arr)) {
                        $error_message[] = $t->_('staff_support_store_import_error_17');//'导入职位只能是 car courier/bike courier/branch supervisor/van courier';
                    }
                    if (!in_array($staff_job_title, [self::$support_job_title['branch_supervisor'], self::$support_job_title['assistant_branch_supervisor']]) && $staff_job_title != $job_title_id) {
                        $error_message[] = $t->_('staff_support_store_import_error_18');//'非branch supervisor 只能创建和工号一致的职位';
                    }
                }

                if (!$begin_date || !$end_date || $begin_date >= $next_year_today || $end_date >= $next_year_today) {
                    $error_message[] = $t->_('staff_support_store_import_error_8');//开始日期/结束日期格式错误
                } else {
                    if ($begin_date < $current_day) {
                        $error_message[] = $t->_('staff_support_store_import_error_9');//开始日期/结束日期格式错误
                    }

                    if ($begin_date > $end_date) {
                        $error_message[] = $t->_('staff_support_store_import_error_7');//支援开始日期不能大于结束日期
                    }
                }

                //验证入职日期 和 支援开始日期 是否符合配置
                $hireCheck = $this->checkHireSetting($staff_list[$staff_info_id], $begin_date);
                if($hireCheck !== true){
                    $error_message[] = $hireCheck;
                }

                $staff_shift_start = '';
                if (empty($excel_shift)) {
                    $error_message[] = $t->_('staff_support_store_import_error_14');//'班次不能为空';
                } else {
                    $staff_shift = explode('-', $excel_shift);
                    $staff_shift_start = trim($staff_shift[0]);
                    $staff_shift_end = trim($staff_shift[1]);
                    if (empty($staff_shift_start) || !in_array($staff_shift_start.'-'.$staff_shift_end, $shift_check)) {
                        $error_message[] = $t->_('staff_support_store_import_error_4');//'班次数据有误';
                    }
                }

                $is_stay = 0;
                if (empty($excel_is_stay)) {
                    $error_message[] = $t->_('staff_support_store_import_error_21');//'请选择是否在网点住宿';
                } else {
                    if (!in_array($excel_is_stay, ['Yes', 'No'])) {
                        $error_message[] = $t->_('staff_support_store_import_error_20');//'是否住宿只能选择 Yes 或者 No';
                    } else {
                        $is_stay = $excel_is_stay == 'Yes' ? 1 : 0;
                        if ($begin_date == $end_date && $is_stay == 1) {
                            $error_message[] = $t->_('staff_support_store_import_error_19');//'支援天数为1天时不支持在网点住宿';
                        }
                    }
                }

                /*
                $support_count = HrStaffApplySupportStoreModel::count([
                    'conditions' => 'staff_info_id = :staff_info_id: and status = 2 and employment_end_date >= :employment_end_date: and support_status != 4',
                    'bind' => [
                        'staff_info_id' => $staff_info_id,
                        'employment_end_date' => $begin_date
                    ]
                ]);
                */

                $support_count = $this->validateConflict($begin_date, $end_date, $staff_info_id);
                if ($support_count > 0) {
                    $error_message[] = $t->_('staff_support_store_import_error_6');//该工号已经有支援数据
                }

                $sub_staff_info_id = 0;
                $staff_info = $staff_list[$staff_info_id];
                if (empty($error_message) && $begin_date == $current_day) {
                    $staff_info['position_category'] = $staff_position[$staff_info_id] ?? [];
                    $staff_info['manager'] = $staff_list[$staff_info_id]['manger'] ?? '';
                    $sub_staff_info_id = $this->createSubStaff(
                        $staff_info,
                        [
                            'job_title' => $job_title_id,
                            'store_id' => $store_id,
                            'employment_begin_date' => $begin_date,
                        ]
                    );
                    if (empty($sub_staff_info_id)) {
                        $error_message[] = $t->_('staff_support_store_import_error_15');//子账号生成失败
                    }
                }

                if (empty($error_message)) {
                    // 计算两个网点经纬度之间的距离
                    $apart = (new SysStoreService())->calculateDistanceStore($store_id,$staff_info['sys_store_id']);
                    $insertData = [
                        'serial_no'             => $staffSupportService->getSerialNo('SASS'),
                        'staff_info_id'         => $staff_info_id,
                        'job_title_id'          => $job_title_id,
                        'store_id'              => $store_id,
                        'store_name'            => $store_name,
                        'employment_begin_date' => $begin_date,
                        'employment_end_date'   => $end_date,
                        'employment_days'       => (strtotime($end_date) - strtotime($begin_date)) / 86400 + 1,
                        'shift_id'              => $shift_start_list[$staff_shift_start]['id'] ?? 0,
                        'shift_extend_id'       => $shift_start_list[$staff_shift_start]['shift_extend_id'] ?? 0,
                        'shift_start'           => $shift_start_list[$staff_shift_start]['start'] ?? '',
                        'shift_end'             => $shift_start_list[$staff_shift_start]['end'] ?? '',
                        'status'                => 2,
                        'sub_staff_info_id'     => $sub_staff_info_id,
                        'staff_store_id'        => $staff_info['sys_store_id'] ?? '', //申请人所属网点id
                        'support_status'        => $begin_date == $current_day ? self::$support_status['in_force'] : self::$support_status['pending'],
                        'is_stay'               => $is_stay,
                        'actual_begin_date'     => $begin_date == $current_day ? $begin_date : null,
                        'data_source'           => HrStaffApplySupportStoreModel::DATA_SOURCE_2,
                        'apart'                 => $apart ?? null,
                        'create_staff_id'       => $params['operator_id'],
                    ];

                    if (empty($shift_start_list[$staff_shift_start]['shift_extend_id'])) {
                        $insertData['shift_type'] = $shift_start_list[$staff_shift_start]['type'] ?? '';
                    }

                    $res = $db->insertAsDict("hr_staff_apply_support_store", $insertData);
                    $last_id = $db->lastInsertId();
                    if ($res) {
                        //加班次变更操作日志
                        $logParam['staff_info_id'] = $staff_info_id;
                        $logParam['date_at']       = $begin_date;
                        $logParam['operate_id']    = $params['operator_id'];
                        $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
                        $extend['before']          = '';
                        $extend['after']           = "{$insertData['shift_start']}-{$insertData['shift_end']} ({$begin_date}~{$end_date})";
                        $logServer                 = new WorkShiftService();
                        $logServer->addShiftLog($logType, $logParam, $extend);

                        if ($begin_date == $current_day) {
                            $sync_ms_params = [
                                [
                                    'id'           => $last_id,
                                    'staff_id'     => $sub_staff_info_id,
                                    'master_staff' => $staff_info_id,
                                    'begin_at'     => strtotime($begin_date),
                                    'end_at'       => strtotime($end_date) + 86399,
                                ],
                            ];
                            $this->syncMsSupportApply($sync_ms_params, 'addStaffSupportInfo');
                        }

                        $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
                        //$title = '支援网点通知';
                        //$content = '支援任务：支援网点 %store_name%，支援日期为%employment_date%，支援班次为%staff_shift%，支援期间请在支援网点打卡上班。';
                        $manager_mobile = $this->getStoreManagerMobile($store_id);
                        $by_message_title = BaseService::getTranslation($lang)->_('staff_support_store_import_message_title_v2');
                        $by_message_content_key = 'staff_support_store_import_message_content_v2_sno';
                        if ($staff_info['job_title'] == self::$support_job_title['branch_supervisor'] && $job_title_id == self::$support_job_title['van_courier']) {
                            $by_message_content_key = 'staff_support_store_import_message_content_v3_sno';
                        }
                        $by_message_content = BaseService::getTranslation($lang)->_(
                            $by_message_content_key,
                            [
                                //'store_name' => $store_name,
                                //'employment_date' => $begin_date . '-' . $end_date,
                                //'staff_shift' => $excel_shift
                                'support_store' => $store_name,
                                'employment_begin_date' => $begin_date,
                                'employment_end_date' => $end_date,
                                'shift' => $excel_shift,
                                'district_manager_name' => $manager_mobile['district_manager_name'],
                                'district_manager_mobile' => $manager_mobile['district_manager_mobile'],
                                'branch_supervisor_name' => $manager_mobile['branch_supervisor_name'],
                                'branch_supervisor_mobile' => $manager_mobile['branch_supervisor_mobile'],
                                'serial_no' => $insertData['serial_no'],
                            ]
                        );
                        $by_message_params = [
                            'staff_info_id' => $staff_info_id,
                            'message_title' => $by_message_title,
                            'message_content' => $by_message_content,
                            'source' => 'import_staff_support',
                        ];

                        $message_result = $this->sendStaffBackyardMessageV2($by_message_params);
                        if ($message_result) {
                            //发送push
                            $message_title = BaseService::getTranslation($lang)->_('staff_support_store_import_message_title');
                            $message_content = BaseService::getTranslation($lang)->_(
                                'staff_support_store_import_message_content_sno',
                                [
                                    'store_name' => $store_name,
                                    'employment_date' => $begin_date . '-' . $end_date,
                                    'staff_shift' => $excel_shift,
                                    'serial_no' => $insertData['serial_no'],
                                ]
                            );
                            $message_params = [
                                'staff_info_id' => $staff_info_id,
                                'message_title' => $message_title,
                                'message_content' => $message_content,
                                'source' => 'import_staff_support',
                            ];
                            $this->sendStaffBackyardPush($message_params);
                        }
                    } else {
                        $error_message[] = $t->_('staff_support_store_import_error_5');
                        $value[] = implode(',', $error_message);
                        array_push($error_list, $value);
                    }
                } else {
                    $value[] = implode(',', $error_message);
                    array_push($error_list, $value);
                }
            }
            $return_data['success_num'] = count($excel_data);
            $return_data['error_num'] = 0;
            $return_data['url'] = '';
            if (!empty($error_list)) {
                $fileName = 'ImportSupportError_' . date('Ymdhis') . '_' . self::$language . date('Ymd-His') . '.xlsx';
                $excel_file = $this->exportExcel([
                    'Staff_id',
                    'Give Support Branch Number',
                    'Give Support Position',
                    'Start Date',
                    'End Date',
                    'Support Shift',
                    'Whether stay overnight at the support branch',
                    'Result',
                ], $error_list, $fileName);
                $flashOss = new FlashOss();
                $json_url_name = self::STAFF_SUPPORT_STORE_OSS_PATH . '/' . $fileName;
                $flashOss->uploadFile($json_url_name, $excel_file['data']);
                $url = $flashOss->signUrl($json_url_name, 86400*7);

                $return_data['error_num'] = count($error_list);
                $return_data['success_num'] = $return_data['success_num'] - $return_data['error_num'];
                $return_data['url'] = $url;
            }

            //更新
            $db->updateAsDict(
                'hr_import_staff_support_excel',
                [
                    'result_file_path' => $return_data['url'],
                    'import_success_count' => $return_data['success_num'],
                    'import_error_count' => $return_data['error_num'],
                    'status' => 2,
                ],
                'id = ' . $params['import_excel_id']
            );
        } catch (Exception $e) {
            echo $e->getMessage();
            $this->logger->error([
                'function' => 'ImportStaffSupport',
                'message' => $e->getMessage(),
                'Line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
        return $return_data;
    }

    /**
     * 导入员工支援网点历史列表
     * @param $params
     * @return array
     */
    public function getImportHistoryList($params)
    {
        $page_num  = intval($params['page_num'] ?? 1);
        $page_size = intval($params['page_size'] ?? 20);
        $offset    = $page_size * ($page_num - 1);
        $type      = $params['type'] ?? HrImportStaffSupportExcelModel::TYPE_SUPPORT_STAFF;
        try {
            $user = $params['user'];
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('
                            hi.id,
                            hi.operator_id,
                            hi.file_name,
                            hi.import_path,
                            hi.status,
                            hi.import_success_count,
                            hi.import_error_count,
                            hi.result_file_path,
                            hi.is_del,
                            hi.created_at,
                            hi.updated_at,
                            hi.type
                            ');
            $builder->from(['hi' => HrImportStaffSupportExcelModel::class]);
            $builder->where("hi.is_del = :is_del:", ['is_del' => 0]);
            $builder->andWhere("hi.operator_id = :operator_id:", ['operator_id' => $user['id']]);
            $builder->andWhere("hi.created_at >= :created_at:", ['created_at' => date("Y-m-d 00:00:00", strtotime("-7 day"))]);
            $builder->andWhere("hi.type = :type:", ['type' => $type]);
            $builder_count = $builder;
            $builder->limit($page_size, $offset);
            $list = $builder->orderBy('hi.id DESC')->getQuery()->execute()->toArray();

            foreach ($list as $key => $value) {
                $list[$key]['status_text'] = self::$t->_('staff_support_store_import_status_' . $value['status']);
                $list[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($value['created_at']) + $this->timeOffset * 3600);
                $list[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($value['updated_at']) + $this->timeOffset * 3600);
            }

            $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
            return [
                'items' => $list,
                'paginate' => [
                    'total_count' => !empty($totalCount) ? $totalCount->count : 0,
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                ],
            ];
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'getImportHistoryList',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return [
                'items' => [],
                'paginate' => [
                    'total_count' => 0,
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                ],
            ];
        }
    }

    /**
     * 删除历史数据
     * @param $params
     * @return bool
     */
    public function delImportHistory($params)
    {
        $operator_id = $params['user']['id'];
        $del_ids = $params['del_ids'] ?? '';

        if (empty($del_ids)) {
            throw new BusinessException(self::$t->_('staff_support_store_error_2')); //请选择要删除的数据
        }

        try {
            foreach ($del_ids as $key => $value) {
                $history = HrImportStaffSupportExcelModel::findFirst([
                    'conditions' => 'id = :id: and operator_id = :operator_id:',
                    'bind' => [
                        'id' => $value,
                        'operator_id' => $operator_id,
                    ],
                ]);
                if (!empty($history) && $history->is_del == 0) {
                    $history->is_del = 1;
                    if ($history->save() === false) {
                        $error = '';
                        foreach ($history->getMessages() as $log) {
                            $error .= ', ' . $log;
                        }
                        $this->logger->error([
                            'function' => 'delImportHistory',
                            'message' => '删除历史上传数据',
                            'message_error' => $error,
                            'params' => $params,
                            'history' => $history->toArray(),
                        ]);
                    }
                } else {
                    $this->logger->info([
                        'function' => 'delImportHistory',
                        'message' => '删除历史上传数据-未找到数据',
                        'params' => $params,
                        'history_id' => $value,
                    ]);
                }
            }
            return true;
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'delImportHistory',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return false;
        }
    }

    /**
     * 创建子账号
     * @param $staff_info
     * @param $apply_info
     * @return int
     */
    public function createSubStaff($staff_info, $apply_info)
    {
        $sub_staff_info_id = 0;
        try {
            //car courier/bike courier/branch supervisor/van courier
            //0分配员/1快递员/2仓管员/4网点出纳/18网点主管
            $position_category = [];
            switch ($apply_info['job_title']) {
                case self::$support_job_title['bike_courier']:
                case self::$support_job_title['van_courier']:
                case self::$support_job_title['car_courier']:
                    $position_category = [1];
                    break;
                case self::$support_job_title['branch_supervisor']:
                    $position_category = [0, 1, 2, 4, 18];
                    break;
                case self::$support_job_title['dc_officer']:
                    $position_category = [2];
                    break;
            }

            $sub_staff_arr = [
                'name'                  => $staff_info['name'],
                'sex'                   => $staff_info['sex'],
                'week_working_day'      => $staff_info['week_working_day'],
                'working_day_rest_type' => $staff_info['week_working_day'] . $staff_info['rest_type'],
                'identity'              => $staff_info['identity'],
                'hire_type'             => $staff_info['hire_type'],
                'mobile'                => $staff_info['mobile'],
                'personal_email'        => $staff_info['personal_email'],
                'job_title'             => (string)$apply_info['job_title'],//职位id
                'sys_store_id'          => $apply_info['store_id'],         //网点id
                'sys_department_id'     => $staff_info['sys_department_id'],//部门id
                'node_department_id'    => $staff_info['node_department_id'],
                'formal'                => 1,
                'state'                 => 1,
                'is_sub_staff'          => 1,
                'hire_date'             => date('Y-m-d', strtotime($apply_info['employment_begin_date'])),
                'master_staff'          => $staff_info['staff_info_id'],
                'leave_date'            => null,
                'position_category'     => !empty($apply_info['position_category']) ? $apply_info['position_category'] : $position_category,
                'manager'               => $staff_info['manager'],
            ];

            $hr_rpc = (new ApiClient('hris', '', 'create_sub_staff_info', 'zh-CN'));
            $hr_rpc->setParamss($sub_staff_arr);
            $res = $hr_rpc->execute();

            if (isset($res['code']) && $res['code'] == 1) {
                $sub_staff_info_id = $res['data']['staff_info_id'] ?? 0;
                $this->logger->info([
                    'function' => 'createSubStaff',
                    'staff_params' => $staff_info,
                    'apply_params' => $apply_info,
                    'result' => $res,
                ]);
            } else {
                $this->logger->error([
                    'function' => 'createSubStaff',
                    'staff_params' => $staff_info,
                    'apply_params' => $apply_info,
                    'result' => $res,
                ]);
            }
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'createSubStaff',
                'staff_params' => $staff_info,
                'apply_params' => $apply_info,
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
        return $sub_staff_info_id;
    }

    /**
     * 同步ms 支援信息
     * @param $params
     * @return bool
     */
    public function syncMsSupportApply($params, $method = 'addStaffSupportInfo')
    {
        if (empty($params)) {
            $this->getDI()->get('logger')->write_log(['message' => 'syncMsSupportApply-支援信息为空'], 'error');
            return false;
        }
        $api = new RestClient('nws');
        $res = $api->execute(RestClient::METHOD_POST, '/svc/staff/generate/' . $method,
            $params, ['Accept-Language' => self::$language]);
        if (isset($res['code']) && $res['code'] == 1) {
            return true;
        }
        $this->logger->error(['支援信息同步异常' => $params, 'action' => $method]);
        return false;
    }

    /**
     * 参数验证
     * @return array
     */
    public function staffSupportListParamsValidations()
    {
        return [
            'staff_info_id'      => 'Int',  //工号
            'staff_store_id'     => 'Str',  //工号所属网点
            'serial_no'          => 'Str',  //审批编号
            'support_store_id'   => 'Str',  //支援网点
            'support_begin_date_start' => 'Date', //支援开始日期-开始
            'support_begin_date_end' => 'Date', //支援开始日期-结束
            'support_end_date_start'   => 'Date', //支援结束日期-开始
            'support_end_date_end'   => 'Date', //支援结束日期-结束
            'support_status'     => 'Int',  //支援状态
            'job_title'          => 'Int',  //职位
            'sub_staff_info_id'  => 'Int|>>>:'.self::$t->_('sub_staff_info_id_error'),  //子账号
            'audit_status'       => 'Arr',  // 审批状态 1->待审批 2->审批通过 3->驳回 4->撤销 5->超时
        ];
    }

    /**
     * 员工支援网点列表
     * @param $params
     * @return array
     */
    public function getStaffSupportList($params)
    {
        $page_num  = intval($params['page_num'] ?? 1);
        $page_size = intval($params['page_size'] ?? 20);
        $offset    = $page_size * ($page_num - 1);
        $builder   = $this->getStaffSupportListBuilder($params);
        $builder->limit($page_size, $offset);
        $list  = $builder->orderBy('hs.id DESC')->getQuery()->execute()->toArray();
        $items = !empty($list) ? $this->combinationStaffSupportList($list) : [];

        //获取total count
        $builder_count = $this->getStaffSupportListBuilder($params);
        $totalCount    = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();

        return [
            'items'    => $items,
            'paginate' => [
                'total_count' => !empty($totalCount) ? $totalCount->count : 0,
                'page_num'    => $page_num,
                'page_size'   => $page_size,
            ],
        ];
    }

    /**
     * 支援员工管理中导出excel修改为异步
     * @param array $params
     * @return mixed
     * @throws ValidationException
     */
    public function addExportStaffSupport(array $params)
    {
        $action_name = "staff_support_store".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR."exportStaffSupport";
        $excelTask   = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: AND status = 0  AND action_name = :action_name: AND is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        //判断是否是fbi 操作的 导出
        $headerData     = $this->request->getHeaders();
        $params['From'] = (isset($headerData['From']) && $headerData['From'] === 'fbi') ? $headerData['From'] : '';

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir.'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        $lastTaskId = $excelTask->id;
        if (!empty($lastTaskId) && $params['From'] == 'fbi') {
            (new BllService())->addFbiExportMessage(
                $params['staff_id'],
                "staff_support_store_exportStaffSupport",
                $params['file_name'],
                $lastTaskId
            );
        }

        return $lastTaskId;
    }

    /**
     * 员工支援网点导出
     * @param $params
     * @return array
     */
    public function exportStaffSupport($params)
    {
        $page_num  = 1;
        $page_size = 1000;
        $export_row = [];
        while (true) {
            $offset  = $page_size * ($page_num - 1);
            $builder = $this->getStaffSupportListBuilder($params);
            $builder->limit($page_size, $offset);
            $list = $builder->orderBy('hs.id DESC')->getQuery()->execute()->toArray();
            $list = $this->combinationStaffSupportList($list);
            if (empty($list)) {
                break;
            }
            $page_num ++;
            foreach ($list as $key => $value) {
                $export_row[] = [
                    $value['serial_no'],
                    $value['data_source'],
                    $value['staff_info_id'],
                    $value['staff_info_name'],
                    $value['job_title_name'],
                    $value['staff_store_name'],
                    $value['store_name'],
                    $value['sub_staff_info_id'],
                    $value['employment_begin_date'],
                    $value['employment_end_date'],
                    $value['support_shift'],
                    $value['employment_days'],
                    $value['is_stay_text'],
                    $value['is_separate_text'],
                    $value['is_original_store_text'],
                    $value['is_today_punch_in_text'],
                    $value['is_today_punch_out_text'],
                    $value['support_status_text'],
                    $value['status_text'],
                    $value['operate_name'],
                    $value['create_time'],
                    $value['approval_agreed_time'],
                ];
            }
        }

        $head = [
            self::$t->_('title_serial_no'),
            self::$t->_('staff_support_store_data_source'),
            self::$t->_('staff_support_store_staff_info_id'),
            self::$t->_('staff_support_store_staff_name'),
            self::$t->_('staff_support_store_staff_job_title'),
            self::$t->_('staff_support_store_staff_store'),
            self::$t->_('staff_support_store_store'),
            self::$t->_('staff_support_store_sub_staff_info_id'),
            self::$t->_('staff_support_store_begin_date'),
            self::$t->_('staff_support_store_end_date'),
            self::$t->_('staff_support_store_shift'),
            self::$t->_('staff_support_store_employment_days'),
            self::$t->_('staff_support_store_is_stay'),
            self::$t->_('staff_support_store_is_separate'),
            self::$t->_('staff_support_store_is_original_store'),
            self::$t->_('staff_support_store_today_punch'),
            self::$t->_('staff_support_store_today_punch_out'),
            self::$t->_('staff_support_store_status'),
            self::$t->_('approve_state_name'),
            self::$t->_('creator'),
            self::$t->_('created_at'),
            self::$t->_('final_approval_time'),
        ];

        $fileName = 'staff_support_list'.date("YmdHis").rand(10000,99999) . '.xls';
        $excel_file = $this->exportExcel($head, $export_row, $fileName);

        $flashOss = new FlashOss();
        //每个国家有每个国家的名字
        $json_url_name = self::STAFF_SUPPORT_STORE_OSS_PATH . '/' . $fileName;
        $flashOss->uploadFile($json_url_name, $excel_file['data']);
        return $json_url_name;
    }

    /**
     * staff support builder
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    public function getStaffSupportListBuilder($params)
    {
        $serial_no          = $params['serial_no'] ?? '';       //申请编号
        $staff_info_id      = $params['staff_info_id'] ?? 0;       //工号
        $staff_store_id     = $params['staff_store_id'] ?? '';     //工号所属网点
        $support_store_id   = $params['support_store_id'] ?? '';   //支援网点
        $support_begin_date_start = $params['support_begin_date_start'] ?? ''; //支援开始时间-开始
        $support_begin_date_end = $params['support_begin_date_end'] ?? ''; //支援开始时间-结束
        $support_end_date_start = $params['support_end_date_start'] ?? '';   //支援结束时间
        $support_end_date_end = $params['support_end_date_end'] ?? '';   //支援结束时间
        $support_status     = $params['support_status'] ?? 0;      //状态
        $sub_staff_info_id  = $params['sub_staff_info_id'] ?? 0;   //子账号
        $job_title          = $params['job_title'] ?? 0;           //职位
        $audit_status       = $params['audit_status'] ?? [];       // 审批状态 1->待审批 2->审批通过 3->驳回 4->撤销 5->超时

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
                            hs.id,
                            hs.staff_info_id,
                            hs.serial_no,
                            hs.store_apply_support_serial_no,
                            hs.job_title_id,
                            hs.store_id,
                            hs.store_name,
                            hs.employment_begin_date,
                            hs.employment_end_date,
                            hs.employment_days,
                            hs.shift_id,
                            hs.shift_type,
                            hs.shift_start,
                            hs.shift_end,
                            hs.status,
                            hs.reject_reason,
                            hs.created_at,
                            hs.updated_at,
                            hs.sub_staff_info_id,
                            hs.is_stay,
                            hs.transportation_mode,
                            hs.is_separate,
                            hs.is_original_store,
                            hs.staff_store_id,
                            hs.support_status,
                            hs.data_source,
                            hs.created_at,
                            hs.approval_agreed_time,
                            hs.create_staff_id
                            ');
        $builder->from(['hs' => HrStaffApplySupportStoreModel::class]);

        // 申请编号
        if (!empty($serial_no)) {
            $builder->andWhere("hs.serial_no = :serial_no:", ['serial_no' => trim($serial_no)]);
        }
        if (!empty($audit_status)) {
            $builder->andWhere("hs.status in ({status:array})", ['status' => $audit_status]);
        }

        if (!empty($staff_info_id)) {
            $builder->andWhere("hs.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_info_id]);
        }

        if (!empty($staff_store_id)) {
            $builder->andWhere("hs.staff_store_id = :staff_store_id:", ['staff_store_id' => $staff_store_id]);
        }

        if (!empty($support_store_id)) {
            $builder->andWhere("hs.store_id = :store_id:", ['store_id' => $support_store_id]);
        }

        if (!empty($support_begin_date_start) && !empty($support_begin_date_end)) {
            $builder->andWhere(
                "hs.employment_begin_date >= :support_begin_date_start: and hs.employment_begin_date <= :support_begin_date_end:",
                ['support_begin_date_start' => $support_begin_date_start,'support_begin_date_end' => $support_begin_date_end]
            );
        }

        if (!empty($support_end_date_start) && !empty($support_end_date_end)) {
            $builder->andWhere(
                "hs.employment_end_date >= :support_end_date_start: and hs.employment_end_date <= :support_end_date_end:",
                ['support_end_date_start' => $support_end_date_start,'support_end_date_end' => $support_end_date_end]
            );
        }

        if (!empty($support_status)) {
            $builder->andWhere(
                "hs.support_status = :support_status:",
                ['support_status' => $support_status]
            );
        }

        if (!empty($sub_staff_info_id)) {
            $builder->andWhere(
                "hs.sub_staff_info_id = :sub_staff_info_id:",
                ['sub_staff_info_id' => $sub_staff_info_id]
            );
        }

        if (!empty($job_title)) {
            $builder->andWhere(
                "hs.job_title_id = :job_title_id:",
                ['job_title_id' => $job_title]
            );
        }

        return $builder;
    }

    /**
     * 组合员工支援网点数据列表
     * @param $list
     * @return array
     */
    public function combinationStaffSupportList($list)
    {
        //支援员工工号//支援员工姓名//职位//原所属网点//支援网点//支援开始时间//支援结束时间//支援班次//支援天数//是否在支援网点住宿//今日是否打卡//状态//操作
        $new_list = [];
        if (empty($list)) {
            return $new_list;
        }

        $support_store_ids = array_column($list, 'store_id');
        $staff_store_ids   = array_column($list, 'staff_store_id');
        $store_ids         = array_unique(array_merge($support_store_ids, $staff_store_ids));

        $sys_store_list = (new SysStoreService())->getStoreList($store_ids);
        $sys_store_list = array_column($sys_store_list, null, 'id');

        $job_title_ids  = array_column($list, 'job_title_id');
        $job_title_list = (new HrJobTitleService())->getJobTitleMapByIds($job_title_ids);

        $staff_info_ids = array_column($list, 'staff_info_id');
        $createStaffIds = array_column($list, 'create_staff_id');
        $createStaffIds = array_filter($createStaffIds);
        $staff_list     = $this->getStaffInfoByIds(array_values(array_unique(array_merge($staff_info_ids,$createStaffIds))));
        $today          = date("Y-m-d");
        $staff_attendance_list = $this->getStaffAttendanceByDate([
            'staff_info_ids'  => $staff_info_ids,
            'attendance_date' => $today,
        ]);

        $pid = array_column($list, 'id');
        if (!empty($pid)) {

            $logs = HrStaffApplySupportStoreOperateLogsModel::find([
                'conditions' => 'pid in ({pid:array}) AND type = :type:',
                'bind'       => [
                    'pid'   => $pid,
                    'type'  => HrStaffApplySupportStoreOperateLogsModel::TYPE_SUPPORT_STAFF,
                ],
                'columns'    => 'id,pid, before, after',
            ])->toArray();
            $logCount = [];
            foreach ($logs AS $key => $val) {
                $change = $this->getChange(json_decode($val['before'], true), json_decode($val['after'], true));
                if (!empty($change)) {
                    $logCount[$val['pid']] = $val;
                }
            }
        }
        $shiftV2info = [];
        if(isCountry("MY")){
            $shiftList = (new HrShiftService())->shiftListV2FromCache();
            $shiftV2info = array_column($shiftList,'shift_name','id');
        }


        foreach ($list as $key => $value) {
            if ($value['store_id'] == '-1') {
                $store_name = GlobalEnums::HEAD_OFFICE;
            } else {
                $store_name = $sys_store_list[$value['store_id']]['name'] ?? '';
            }

            if ($value['staff_store_id'] == '-1') {
                $staff_store_name = GlobalEnums::HEAD_OFFICE;
            } else {
                $staff_store_name = $sys_store_list[$value['staff_store_id']]['name'] ?? '';
            }
            $support_begin_date = date('Y-m-d', strtotime($value['employment_begin_date']));
            $support_end_date   = date('Y-m-d', strtotime($value['employment_end_date']));


            $attendance         = $staff_attendance_list[$value['staff_info_id'].'_'.$today] ?? [];
            $is_today_punch_in  = !empty($attendance) && $attendance['started_at'] ? 1 : 0;
            $is_today_punch_out = !empty($attendance) && $attendance['end_at'] ? 1 : 0;

            //已生效 支援开始日期当天且没打卡 才能修改志愿网点
            $is_edit_support_store = 0;
            if ($value['support_status'] == self::$support_status['in_force'] && $today == $support_begin_date && !$is_today_punch_in && !$is_today_punch_out) {
                $is_edit_support_store = 1;
            }

            $is_edit   = 0;
            $is_cancel = 0;

            //未生效 可以编辑网点 可取消 可编辑
            if ($value['support_status'] == self::$support_status['pending']) {
                $is_edit_support_store = 1;
                $is_edit   = 1;
                $is_cancel = 1;
            }
            //已生效 可编辑、支援开始日期当天可以取消
            if ($value['support_status'] == self::$support_status['in_force']) {
                $is_edit   = 1;
                $is_cancel = $support_begin_date == $today ? 1 : 0;
            }
            //非审核同意的 不能修改和取消
            if ($value['status'] != HrStaffApplySupportStoreModel::STATUS_PASS) {
                $is_edit   = 0;
                $is_cancel = 0;
            }

            $is_change_log = isset($logCount[$value['id']]) ? 1 : 0;
            $show_shift_name = '';
            if (isCountry('MY')) {
                $show_shift_name = ($shiftV2info[$value['shift_id']] ?? '') . ' ';
            }
            $show_shift_name .= $value['shift_start'].' - '.$value['shift_end'];
            $isSourceBy = $value['data_source'] == HrStaffApplySupportStoreModel::DATA_SOURCE_1;
            $operateStaffId = $isSourceBy ? $value['staff_info_id'] : $value['create_staff_id'];
            $approvalAgreedTime = $isSourceBy
                ? ($value['status'] == ApprovalEnums::APPROVAL_STATUS_APPROVAL ? (show_time_zone($value['approval_agreed_time'])) : '') //by渠道审批通过才展示
                : show_time_zone($value['created_at']);
            $new_list[] = [
                'id'                      => $value['id'],
                'serial_no'               => !empty($value['serial_no']) ? $value['serial_no'] : '',
                'staff_info_id'           => $value['staff_info_id'],
                'staff_info_name'         => $staff_list[$value['staff_info_id']]['name'] ?? '',
                'job_title_id'            => $value['job_title_id'],
                'job_title_name'          => $job_title_list[$value['job_title_id']] ?? '',
                'store_id'                => $value['store_id'],
                'store_name'              => $store_name,
                'staff_store_id'          => $value['staff_store_id'],
                'staff_store_name'        => $staff_store_name,
                'data_source'             => $value['data_source'] == HrStaffApplySupportStoreModel::DATA_SOURCE_1 ? self::$t->_('staff_support_source_1') : self::$t->_('staff_support_source_2'),
                'employment_begin_date'   => $support_begin_date,
                'employment_end_date'     => $support_end_date,
                'employment_days'         => $value['employment_days'],
                'support_shift_id'        => $value['shift_id'],
                'support_shift'           => $show_shift_name,
                'is_stay'                 => $value['is_stay'],
                'is_stay_text'            => $value['is_stay'] == 0 ? self::$t->_('staff_support_store_no') : self::$t->_('staff_support_store_yes'),
                'transportation_mode'      => !empty($value['transportation_mode']) ? $value['transportation_mode'] : '',
                'transportation_mode_text' => !empty($value['transportation_mode']) ? self::$t->_('transportation_mode_' . $value['transportation_mode']) : '',
                'is_separate'             => $value['is_separate'],
                'is_separate_text'        => $value['is_separate'] == 0 ? self::$t->_('staff_support_store_no') : self::$t->_('staff_support_store_yes'),
                'is_original_store'       => $value['is_original_store'],
                'is_original_store_text'  => $value['is_original_store'] == 0 ? self::$t->_('staff_support_store_no') : self::$t->_('staff_support_store_yes'),
                'is_today_punch_in'       => $is_today_punch_in,
                'is_today_punch_in_text'  => $is_today_punch_in == 0 ? self::$t->_('staff_support_store_no') : self::$t->_('staff_support_store_yes'),
                'is_today_punch_out'      => $is_today_punch_out,
                'is_today_punch_out_text' => $is_today_punch_out == 0 ? self::$t->_('staff_support_store_no') : self::$t->_('staff_support_store_yes'),
                'support_status'          => $value['support_status'],
                'status'                  => $value['status'],
                'status_text'             => self::$t->_('by_state_' . $value['status']),
                'support_status_text'     => self::$t->_('staff_support_store_support_status_' . $value['support_status']),
                'sub_staff_info_id'       => $value['sub_staff_info_id'],
                'is_edit'                 => $is_edit,
                'is_cancel'               => $is_cancel,
                'is_change_log'           => $is_change_log,
                'is_edit_support_store'   => $is_edit_support_store,
                'operate_name'            => empty($staff_list[$operateStaffId]) ? '' : "({$operateStaffId}){$staff_list[$operateStaffId]['name']}",
                'create_time'             => show_time_zone($value['created_at']),
                'approval_agreed_time'    => $approvalAgreedTime,
            ];
        }
        return $new_list;
    }


    /**
     * 获取可用班次列表
     * @return array
     */
    public function getSupportShiftList(): array
    {
        return  (new HrShiftService())->getSupportStoreShiftList();
    }

    protected function getImportTemplate()
    {
        //return 'https://fex-my-asset-pro.oss-ap-southeast-3.aliyuncs.com/workOrder/oca-1653488606-f25797c360b344bf97bb28334a090127.xlsx';
        return SettingEnvModel::get_val('import_staff_support_template');
    }
    /**
     * 静态数据
     * @return array
     */
    public function getSysInfo()
    {
        $info = [];
        try {
            $server = reBuildCountryInstance($this);
            $shift_list =$this->getSupportShiftList();
            $info['shift_list'] = $shift_list;
            $info['support_status'] = $this->getSupportStatus();
            $info['import_template'] = $server->getImportTemplate();

            $result = $this->getSupportStaffJobTitleConfig();
            $info['job_title'] = array_map(function ($job_name, $job_id) {
                return ['key' => $job_name, 'value' => $job_id];
            }, $result['support_job_title_list'], array_keys( $result['support_job_title_list']));

            $info['audit_status'] = (new SysService())->AuditStatus();

            $info['is_stay_status'] = $this->getIsStayStatus();
            $info['transportation_mode_item'] = $this->getTransportationModeItem();

        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'StaffSupportStoreServer-getSysInfo',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
        }
        return $info;
    }

    /**
     * 按照工号姓名搜索员工信息
     * @param $params
     * @return array
     */
    public function searchStaff($params)
    {
        $staff_list = [];
        $search_staff = $params['search_staff'] ?? '';
        $limit = $params['page_size'] ?? 20;
        if (!empty($search_staff)) {
            $staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name',
                'conditions' => 'formal IN (1,4) and is_sub_staff = 0 and (staff_info_id like :search_staff: or name like :search_staff:)',
                'bind' => [
                    'search_staff' => '%' . $search_staff . '%',
                ],
                'limit' => $limit,
            ])->toArray();
        }
        return $staff_list;
    }

    /**
     * 员工支援网点信息
     * @param $params
     * @return array
     */
    public function getStaffSupportDetail($params)
    {
        $staff_support_detail = [];
        try {
            $id = $params['id'] ?? 0;
            $staff_info_id = $params['staff_info_id'] ?? 0;
            $serial_no = $params['serial_no'] ?? '';

            $detail = HrStaffApplySupportStoreModel::findFirst([
                'conditions' => 'id = :id: and staff_info_id = :staff_info_id: and serial_no = :serial_no:',
                'bind' => [
                    'id' => $id,
                    'staff_info_id' => $staff_info_id,
                    'serial_no' => $serial_no,
                ],
            ]);
            $staff_support_detail = !empty($detail) ? $detail->toArray() : [];
            if (!empty($staff_support_detail)) {
                unset($staff_support_detail['created_at']);
                unset($staff_support_detail['updated_at']);
                unset($staff_support_detail['os_invalid_date']);
                unset($staff_support_detail['os_staff_info_id']);
                unset($staff_support_detail['store_region']);
                unset($staff_support_detail['store_piece']);
            }
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'getStaffSupportDetail',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
        }
        return $staff_support_detail;
    }

    /**
     * 编辑支援信息
     * @param $params
     * @return bool
     * @throws BusinessException|GuzzleException
     * @throws ValidationException
     */
    public function editStaffSupport($params)
    {
        //- 仅可编辑 待生效｜（已生效&今日是否打上班卡=否） 的支援，其他状态编辑按钮置灰。
        $id                 = $params['id'] ?? 0;
        $staff_info_id      = $params['staff_info_id'] ?? 0;
        $serial_no          = $params['serial_no'] ?? '';
        $support_begin_date = $params['support_begin_date'] ?? '';
        $support_end_date   = $params['support_end_date'] ?? '';
        $shift_id           = $params['shift_id'] ?? 0;
        $is_stay            = $params['is_stay'] ?? 0;//是否住宿
        $is_separate        = $params['is_separate'] ?? 0;//是否揽派分离
        $is_original_store  = $params['is_original_store'] ?? 0;//是否强制原网点
        $user               = $params['user'];
        $store_id           = $params['store_id'];
        $transportation_mode = !empty($params['transportation_mode']) ? $params['transportation_mode'] : 0;

        $detail = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'id = :id: and staff_info_id = :staff_info_id:',
            'bind'       => [
                'id'            => $id,
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        //员工信息
        $staffInfo = (new StaffService())->getHrStaffInfo($detail->staff_info_id);
        if (empty($detail)) {
            throw new BusinessException(self::$t->_('staff_support_store_error_3')); //未找到员工支援信息
        }
        $before = $detail->toArray();
        $before_support_end_date = $before['employment_end_date'];
        $today              = date("Y-m-d");
        $support_begin_date = date('Y-m-d', strtotime($support_begin_date));
        $support_end_date   = date('Y-m-d', strtotime($support_end_date));

        //泰国 判断是否存在出差
        if(isCountry('TH')){
            $tripServer = new BusinessTripService();
            $exist = $tripServer->checkExistTime($support_begin_date,$support_end_date,$staff_info_id);
            if (!empty($exist)) {
                throw new ValidationException(self::$t->_('support_check_trip'));
            }
        }
        if ($detail->support_status == self::$support_status['in_force']) {
            if ($detail->employment_begin_date != $support_begin_date) {
                throw new BusinessException(self::$t->_('staff_support_store_error_4')); //已生效的支援，不能修改支援开始时间
            }
        }

        if (!in_array($detail->support_status, [self::$support_status['pending'], self::$support_status['in_force']])) {
            throw new BusinessException(self::$t->_('staff_support_status_error')); 
        }

        // 是否住宿 和 交通方式可编辑校验
        if (isCountry('PH')) {
            // 不可编辑是否住宿/交通方式的状态
            $is_change_val = $is_stay != $detail->is_stay || $transportation_mode != $detail->transportation_mode;

            $no_can_change_support_status = [
                self::$support_status['in_force'],
                self::$support_status['expired'],
                self::$support_status['cancelled'],
            ];
            if ($is_change_val && in_array($detail->support_status, $no_can_change_support_status)) {
                throw new ValidationException(self::$t->_('staff_support_store_error_16'), ErrCode::VALIDATE_ERROR);
            }

            if ($transportation_mode != $detail->transportation_mode) {
                $detail->transportation_mode = intval($transportation_mode);
            }
        }

        //判断入职天数配置 限制不能申请支援逻辑  https://flashexpress.feishu.cn/wiki/Wt4vwxsiXizaNlkdl97cwFX3nGf
        if($detail->support_status == self::$support_status['pending']){
            $hireCheck = $this->checkHireSetting($staffInfo, $support_begin_date);
            if($hireCheck !== true){
                throw new BusinessException($hireCheck);
            }
        }

        $sync_sub_staff_state = false;
        //支援提前结束
        if ($detail->support_status == self::$support_status['in_force'] && $support_end_date < $today) {
            $start_date            = date('Y-m-d', strtotime($support_end_date) + 86400);
            $businessException = [];
            //  验证这段时间区间的 cod  揽派件
            if ($this->checkStaffIsHasPickup($detail->sub_staff_info_id ?: $detail->staff_info_id, $start_date,
                $detail->employment_end_date)) {
                $businessException[] = self::$t->_('modify_support_message_2',['start_date'=>$start_date,'end_date'=>$detail->employment_end_date]); //有揽件
            }
            if ($this->checkStaffIsHasDelivery($detail->sub_staff_info_id ?: $detail->staff_info_id, $start_date,
                $detail->employment_end_date)) {
                $businessException[] = self::$t->_('modify_support_message_3',['start_date'=>$start_date,'end_date'=>$detail->employment_end_date]); //有派件
            }
            if ($this->checkStaffIsHasCOD($detail->sub_staff_info_id ?: $detail->staff_info_id, $start_date,
                $detail->employment_end_date)) {
                $businessException[] = self::$t->_('modify_support_message_4',['start_date'=>$start_date,'end_date'=>$detail->employment_end_date]);
            }
            if (!empty($businessException)) {
                $msg = self::$t->_('modify_support_message_1');
                $msg .= implode(' ', $businessException);
                $msg .= self::$t->_('modify_support_message_5');
                throw new BusinessException($msg);
            }

            $sync_sub_staff_state    = true;
            $detail->support_status  = 3;
            $detail->actual_end_date = $support_end_date;
        }

        if ($detail->support_status == self::$support_status['pending'] && $support_begin_date <= $today) {
            throw new BusinessException(self::$t->_('staff_support_store_error_6')); //待生效支援开始时间仅可选择今天之后的日期
        }

        if ($support_end_date < $support_begin_date) {
            throw new BusinessException(self::$t->_('staff_support_store_error_7')); //支援结束时间仅可选择支援开始日期及其之后的日期
        }
        $shift_end_date = date('Y-m-t', strtotime(date('Y-m-01',strtotime(date("Y-m-d"))) ." +1 month last day of"));//下月最后一天

        if ($support_end_date > $shift_end_date) {
            throw new BusinessException(self::$t->_('staff_support_store_error_15')); //支援结束时间不得超过下月最后一天
        }
        if (in_array($shift_id, self::$not_support_shift_ids)) {
            throw new BusinessException(self::$t->_('staff_support_store_error_8')); //支援班次仅可选择下拉框中的班次
        }

        if (isCountry('MY') && $support_begin_date == $support_end_date && $is_stay == 1) {
            throw new BusinessException(self::$t->_('staff_support_store_error_9')); //支援天数为1天时不支持在网点住宿
        }

        $support_count = HrStaffApplySupportStoreModel::count([
            'conditions' => 'staff_info_id = :staff_info_id: and status in(1,2) and id != :id: and employment_end_date >= :employment_begin_date: and employment_begin_date <= :employment_end_date: and support_status != 4',
            'bind'       => [
                'staff_info_id'         => $staff_info_id,
                'employment_begin_date' => $support_begin_date,
                'employment_end_date'   => $support_end_date,
                'id'                    => $id,
            ],
        ]);

        if ($support_count > 0) {
            throw new BusinessException(self::$t->_('staff_support_store_import_error_6'));//该工号已经有支援数据
        }

        $sync_sub_store = false;
        if (!empty($store_id) && $detail->store_id != $store_id) { //编辑网点
            //不能支援自己的网点
            if ($staffInfo['sys_store_id'] == $store_id) {
                throw new BusinessException(self::$t->_('staff_support_store_import_error_10'));
            }

            //仅未生效
            //已生效 & 支援开始日期=今天 & 今日是否打上班卡=否  & 今日是否打下班卡=否  ）的支援
            //可编辑支援网点，其他情况下支援网点置灰
            if (!in_array($detail->support_status,
                [self::$support_status['pending'], self::$support_status['in_force']])) {
                throw new BusinessException(self::$t->_('staff_support_store_error_14')); //不能编辑网点
            }

            $staff_attendance_info = StaffWorkAttendanceModel::findFirst([
                'columns'    => 'staff_info_id,organization_id,organization_type,attendance_date,shift_start,shift_end,working_day',
                'conditions' => 'staff_info_id = :staff_info_id: and attendance_date = :attendance_date:',
                'bind'       => [
                    'staff_info_id'   => $detail->sub_staff_info_id ?: $detail->staff_info_id,
                    'attendance_date' => $today,

                ],
            ]);
            if ($detail->support_status == self::$support_status['in_force'] && (!empty($staff_attendance_info) || $detail->employment_begin_date < $today)) { //
                throw new BusinessException(self::$t->_('staff_support_store_error_14')); //不能编辑网点
            }

            $store_detail = SysStoreModel::findFirst(['conditions' => ' id = :id: ', 'bind' => ['id' => $store_id]]);
            if (!$store_detail) {
                throw new BusinessException(self::$t->_('staff_support_store_import_error_2')); //网点错误
            }

            //无法支援同仓网点
            if (isCountry('PH')) {
                $sysStoreService = new SysStoreService();
                $storeInfo       = $sysStoreService->getStoreList([$detail->staff_store_id, $store_id]);
                $storeInfo       = array_column($storeInfo, null, 'id');
                if (isset($storeInfo[$detail->staff_store_id]) && isset($storeInfo[$store_id])
                    && $storeInfo[$detail->staff_store_id]['lat'] === $storeInfo[$store_id]['lat']
                    && $storeInfo[$detail->staff_store_id]['lng'] === $storeInfo[$store_id]['lng']
                ) {
                    throw new BusinessException(self::$t->_('store_support_error_15'));
                }
            }

            $detail->store_id   = $store_id;
            $detail->store_name = $store_detail->name;
            $sync_sub_store     = true;

            // 计算两个网点经纬度之间的距离
            $apart = (new SysStoreService())->calculateDistanceStore($store_id,$detail->staff_store_id);
            $detail->apart   = $apart ?? null;
        }

        $init_employment_days =  $detail->employment_days ;

        $detail->employment_begin_date = $support_begin_date;
        $detail->employment_end_date   = $support_end_date;
        //支援天数
        $detail->employment_days       = (strtotime($support_end_date) - strtotime($support_begin_date) ) / 86400 + 1;

        //支援进行中只能【是】变【否】，不能【否】变【是】。
        if (isCountry('MY') &&  $detail->support_status == self::$support_status['in_force']) {
            if ($detail->is_stay != $is_stay && !($init_employment_days != 1 && $detail->employment_days == 1)) {
                throw new BusinessException(self::$t->_('staff_support_store_error_5')); //支援正在进行中，不可更改支援网点住宿状态
            }
        }

        if(isCountry('MY') && $detail->support_status == self::$support_status['pending'] && $detail->employment_days == 1 && $is_stay == 1){
            throw new BusinessException(self::$t->_('staff_support_store_import_error_19')); //支援天数为1天时不支持在网点住宿
        }
        if(isCountry('MY') ){
            if(($is_stay + $is_separate + $is_original_store) > 1){
                throw new BusinessException(self::$t->_('staff_support_only_one_yes')); //马来的三种类型只能选一个
            }
            if($detail->support_status == self::$support_status['in_force'] && $detail->is_separate != $is_separate){
                throw new BusinessException(self::$t->_('staff_support_is_separate_notice')); //生效中不能编辑 揽派分离
            }
            if($detail->support_status == self::$support_status['in_force'] && $detail->is_original_store != $is_original_store){
                throw new BusinessException(self::$t->_('staff_support_is_original_store_notice')); //生效中不能编辑
            }
        }

        $detail->is_stay           = intval($is_stay);
        $detail->is_separate       = intval($is_separate);
        $detail->is_original_store = intval($is_original_store);
        $shift_change = false;
        //班次是否变更
        $is_shift_change = $detail->shift_id != $shift_id;

        $supportShiftInfo = $this->getSupportShiftInfo($detail, $shift_id);
        if ($is_shift_change) {
            $shift_change = true;
            /**
             * @see StaffSupportStoreServer::getSupportShiftInfo()
             * @see \App\Modules\My\Services\StaffSupportStoreServer::getSupportShiftInfo()
             */
            if (isCountry('MY')) {
                $checkShitDuration = $this->checkStaffShiftDuration($detail->staff_info_id,
                    $detail->employment_begin_date, $supportShiftInfo['work_time']);
                if (!$checkShitDuration) {
                    throw new BusinessException(self::$t->_('staff_support_store_shift_duration_error')); //支援班次和现有班次的工作时长不一致
                }
            }
        }

        // 轮休规则按日配置校验 - 检查日期变化
        $this->validateSupportDateChange($detail, $before, $support_begin_date, $support_end_date);

        $save_result = $detail->save();
        if ($save_result === false) {
            $error = '';
            foreach ($detail->getMessages() as $log) {
                $error .= ', ' . $log;
            }
            $this->logger->error([
                'function'      => 'editStaffSupport',
                'message'       => '变更支援信息失败',
                'message_error' => $error,
                'params'        => $params,
                'operator'      => $user['id'],
            ]);
            return false;
        }

//        if (isCountry('MY')) {
//            //班次变更 撤销审批通过的OT
//            if (($shift_change && !empty($supportShiftInfo)) || $detail->employment_end_date != $before_support_end_date) {
//                $change_shift_info = [
//                    'shift_start' => $supportShiftInfo['first_start'],
//                    'shift_end'   => $supportShiftInfo['first_end'],
//                ];
//                //撤销OT
//                $this->cancelOT($detail->staff_info_id, max($today,$detail->employment_begin_date), $detail->employment_end_date, $change_shift_info);
//            }
//
//        }
        $attributes['operater'] =  $user['id'];
        //同步子账号离职
        if ($detail->sub_staff_info_id && $sync_sub_staff_state === true) {
            $attributes['state']                 = 2;                //离职
            $attributes['leave_date']            = $support_end_date;//离职日期
            $attributes['is_auto_system_change'] = 1;
            $attributes['staff_info_id']         = $detail->sub_staff_info_id;
            $hr_rpc                              = (new ApiClient('hris', '', 'update_staff_info', 'zh-CN'));
            $hr_rpc->withParam($attributes);
            $hr_rpc->execute();
        }

        if ($detail->sub_staff_info_id && $sync_sub_store === true) {
            //更新支援网点
            $attributes['sys_store_id']  = $store_id;
            $attributes['staff_info_id'] = $detail->sub_staff_info_id;
            $hr_rpc                      = (new ApiClient('hris', '', 'update_staff_info', 'zh-CN'));
            $hr_rpc->withParam($attributes);
            $hr_rpc->execute();

            $this->logger->info([
                'function' => 'editStaffSupport',
                'message'  => '同步编辑子账号支援网点信息',
                'params'   => $attributes,
            ]);
        }

        if ($detail->sub_staff_info_id && in_array($detail->status,
                [self::$support_status['pending'], self::$support_status['in_force']])) {
            $sync_ms_params = [
                'id'       => $id,
                'master_staff' => $staff_info_id,
                'begin_at' => strtotime($detail->employment_begin_date),
                'end_at'   => strtotime($detail->employment_end_date) + 86399,
            ];
            $this->logger->info([
                'function'       => 'editStaffSupport',
                'message'        => '同步编辑子账号支援信息',
                'sync_ms_params' => $sync_ms_params,
            ]);
            $this->syncMsSupportApply($sync_ms_params, 'editStaffSupportInfo');
        }

        $after = $detail->toArray();
        //保存操作记录
        $model                = new HrStaffApplySupportStoreOperateLogsModel();
        $model->pid           = $detail->id;
        $model->operator      = $user['id'];
        $model->staff_info_id = $staff_info_id;
        $model->request_body  = json_encode($params);
        $model->before        = json_encode($before);
        $model->after         = json_encode($after);
        $model->action        = 'edit';
        $model->save();

        //- 保存后，重新向员工发送通知（BY/KIT-Push+消息）
        $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
        //支援网点任务变更通知
        //您的支援任务已变更：支援网点 %store_name% ，支援日期为 %employment_date% ，支援班次为 %staff_shift% ，支援期间请在支援网点打卡上班。
        $message_title   = self::getTranslation($lang)->_('staff_support_store_edit_message_title');
        $message_content = self::getTranslation($lang)->_(
            'staff_support_store_edit_message_content_sno',
            [
                'store_name'      => $detail->store_name,
                'employment_date' => $detail->employment_begin_date . '-' . $detail->employment_end_date,
                'staff_shift'     => $detail->shift_start . '-' . $detail->shift_end,
                'serial_no'       => !empty($detail->serial_no) ? $detail->serial_no : '',
            ]
        );
        //马来的消息内容不一样
        if(isCountry('MY')){
            $p1 = self::getTranslation($lang)->_(
                'staff_support_store_edit_message_p1',
                [
                    'store_name'      => $detail->store_name,
                    'employment_date' => $detail->employment_begin_date . '-' . $detail->employment_end_date,
                    'staff_shift'     => $detail->shift_start . '-' . $detail->shift_end,
                    'serial_no'       => !empty($detail->serial_no) ? $detail->serial_no : '',
                ]
            );
            $p2 = self::getTranslation($lang)->_('staff_support_store_default_p2');
            if($is_separate){
                $p2 = self::getTranslation($lang)->_('staff_support_store_is_separate_p2');
            }
            if($is_original_store){
                $p2 = self::getTranslation($lang)->_('staff_support_store_import_is_original_store_p2');
            }
            $message_content = $p1.$p2;

            //马来特殊逻辑 写个拆分表 只能是生效 或者 生效变成失效状态才修改
            if(in_array($detail->support_status,[self::$support_status['in_force'], self::$support_status['expired']])){
                $table = HrStaffApplySupportStoreSplitModel::class;
                $this->modelsManager->executeQuery("delete from {$table} where origin_id = :id:",['id' => $detail->id]);
                $this->addSupportSplit($detail->toArray());
            }
        }

        $message_params  = [
            'staff_info_id'   => $staff_info_id,
            'message_title'   => $message_title,
            'message_content' => $message_content,
            'source'          => 'edit_staff_support',
        ];
        $message_result  = $this->sendStaffBackyardMessage($message_params);
        if ($message_result) {
            //发送push
            $this->sendStaffBackyardPush($message_params);
        }

        //追加子账号Push+消息
        if ($detail->sub_staff_info_id) {
            $sub_message_params = [
                'staff_info_id'   => $detail->sub_staff_info_id,
                'message_title'   => $message_title,
                'message_content' => $message_content,
                'source'          => 'edit_staff_support',
            ];
            $sub_message_result = $this->sendStaffBackyardMessage($sub_message_params);
            if ($sub_message_result) {
                //发送push
                $this->sendStaffBackyardPush($sub_message_params);
            }
        }

        $this->logger->info([
            'function' => 'editStaffSupport',
            'message'  => '编辑支援信息成功',
            'operator' => $user['id'],
            'params'   => $params,
            'before'   => $before,
        ]);

        //修改班次 替换或者还原 主账号班次
        $this->modifyStaffShift($before, $after, $user['id']);

        if(isCountry('PH') || isCountry('MY')){
            //修改支援自动调整加班时间 只有菲律宾 https://flashexpress.feishu.cn/wiki/FYJHwkEcfitZSWk1mhCc0nr1n0g
            $otParam['id']                = $detail->id;
            $otParam['lang']              = self::$language;
            $otParam['change_type']       = SupportOvertimeBackupModel::LOG_TYPE_EDIT;
            $otParam['origin_begin_date'] = $before['employment_begin_date'];
            $otParam['origin_end_date']   = $before['employment_end_date'];
            $otParam['origin_shift_id']   = $before['shift_id'];//原班次id  看是否需要修改交集日期里的加班
            $otParam['origin_shift_extend_id'] = $before['shift_extend_id'];
            $rmq = new RocketMQ('ot-support-shift');
            $rmq->sendToMsg($otParam);
        }
        return true;
    }

    /**
     * 修改支援的班次
     * @param $detail
     * @param $shift_id
     * @return mixed
     */
    public function getSupportShiftInfo(&$detail, $shift_id)
    {
        $shift_detail =  (new HrShiftService())->getShiftById($shift_id);
        $detail->shift_id = $shift_id;
        $detail->shift_type = $shift_detail['type'];
        $detail->shift_start = $shift_detail['start'];
        $detail->shift_end = $shift_detail['end'];
        return $shift_detail;
    }


    /**
     * @description 编辑支援通知发送网点主管和网点仓管
     * @param $params
     * @return void
     */
    public function sendPush($params)
    {
        $store_id = $params['store_id'];
        $job_title = $params['job_title'];
        $title_key = $params['title_key'];
        $title_bind = $params['title_bind'];
        $content_key = $params['content_key'];
        $content_bind = $params['content_bind'];

        $staff_info = HrStaffInfoModel::find([
            'conditions' => 'job_title in ({job_title:array}) and sys_store_id = :store_id: and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                'job_title' => $job_title,
                'store_id' => $store_id,
            ],
        ])->toArray();

        if (!empty($staff_info)) {

            //- 保存后，重新向员工发送通知（BY/KIT-Push+消息）
            $StaffService = new StaffService();

            foreach ($staff_info as $v) {
                $lang = $StaffService->getAcceptLanguage($v['staff_info_id']);

                //%员工工号%支援变更通知
                //您网点的员工 %员工工号% 支援信息有变动，以下是该员工的最新支援信息
                //%员工工号% | %员工姓名% | %员工支援网点% | %员工支援时间%

                //您网点的员工 %staff_info_id% 支援信息有变动，以下是该员工的最新支援信息 %staff_info_id_1% | %staff_name% | %support_store_name% | %support_time%
                $message_title = self::getTranslation($lang)->_($title_key, $title_bind);
                $message_content = self::getTranslation($lang)->_($content_key, $content_bind);

                $message_params = [
                    'staff_info_id' => $v['staff_info_id'],
                    'message_title' => $message_title,
                    'message_content' => $message_content,
                    'source' => 'edit_staff_support',
                ];
                $message_result = $this->sendStaffBackyardMessage($message_params);
                if ($message_result) {
                    //发送push
                    $this->sendStaffBackyardPush($message_params);
                }
            }
        }
    }

    //揽件任务

    /**
     * @throws BusinessException|GuzzleException
     */
    public function checkStaffIsHasPickup($staffInfoId,$begDate, $endDate): bool
    {
        if (empty($staffInfoId)) {
            return false;
        }
        try {
            $client = new RestClient('pms', 10);
            $res    = $client->execute(RestClient::METHOD_POST, '/svc/ticket/pickup/count_pending_pickup_tasks',
                ['staffInfoId' => $staffInfoId, 'begDate' => $begDate, 'endDate' => $endDate]);
            if (empty($res) || $res['code'] != 1) {
                throw new BusinessException($res['message']);
            }
        } catch (Exception $e) {
            throw new BusinessException(self::$t->_('server_error'));
        }

        return $res['data']['countPendingPickupTasks'] > 0 ;

    }
    //派件任务

    /**
     * @throws BusinessException|GuzzleException
     */
    public function checkStaffIsHasDelivery($staffInfoId,$begDate, $endDate): bool
    {
        if (empty($staffInfoId)) {
            return false;
        }
        $client = new RestClient('pms');
        $res = $client->execute(RestClient::METHOD_POST, '/svc/ticket/delivery/delivery_quantity', ['staffInfoId'=>$staffInfoId,'begDate'=>$begDate,'endDate'=>$endDate]);

        if(empty($res) || $res['code'] != 1){
            throw new BusinessException($res['message']);
        }
        return $res['data']['deliveryQuantity'] > 0 ;
    }

    //COD 公款

    /**
     * @throws BusinessException|GuzzleException
     */
    public function checkStaffIsHasCOD($staffInfoId,$begDate, $endDate): bool
    {
        if (empty($staffInfoId)) {
            return false;
        }
        $client = new RestClient('fau');
        $res = $client->execute(RestClient::METHOD_POST, '/svc/staff/receivable/receivable_count', ['staffInfoId'=>$staffInfoId,'begDate'=>$begDate,'endDate'=>$endDate]);
        if(empty($res) || $res['code'] != 1){
            throw new BusinessException($res['message']);
        }
        return $res['data']['receivableCount'] > 0 ;
    }

    /**
     * 取消支援信息
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function cancelStaffSupport($params): bool
    {
        //- 支援开始日期在今天之后的可以取消，即“待生效”状态的支援可以取消；
        //- 支援开始日期在今天，且 今日是否打上班卡=否 的支援可以取消。
        //其他状态，取消按钮置灰
        //其他状态的支援信息行的取消按钮置灰。取消后该条支援信息不生效。
        $id = $params['id'] ?? 0;
        $staff_info_id = $params['staff_info_id'] ?? 0;
        $serial_no = $params['serial_no'] ?? '';
        $user = $params['user'];
        $today = date("Y-m-d");
        $detail = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'id = :id: and staff_info_id = :staff_info_id:',
            'bind' => [
                'id' => $id,
                'staff_info_id' => $staff_info_id,
            ],
        ]);

        if (empty($detail)) {
            throw new BusinessException(self::$t->_('staff_support_store_error_3')); //未找到数据 参数异常
        }
        $support_status = $detail->support_status;


        if (!in_array($detail->support_status, [self::$support_status['in_force'], self::$support_status['pending']])) {
            throw new BusinessException(self::$t->_('staff_support_store_error_11')); //非待生效已生效状态不能取消
        }

        if ($detail->support_status ==self::$support_status['in_force'] &&  $detail->employment_begin_date == $today) {
            $businessException = [];

            $setting = new SettingEnvService();
            $is_close = $setting->getSetVal('staff_support_close_Check_Pickup_Delivery_COD');
            if (!$is_close && $this->checkStaffIsHasPickup($detail->sub_staff_info_id?:$detail->staff_info_id, $today,$today)) {
                $businessException[] = self::$t->_('cancel_support_message_2'); //有揽件
            }
            if (!$is_close && $this->checkStaffIsHasDelivery($detail->sub_staff_info_id?:$detail->staff_info_id, $today,$today)) {
                $businessException[] = self::$t->_('cancel_support_message_3');; //有派件
            }
            if (!$is_close && $this->checkStaffIsHasCOD($detail->sub_staff_info_id?:$detail->staff_info_id, $today,$today)) {
                $businessException[] = self::$t->_('cancel_support_message_4'); //cod有公款
            }
            if (!empty($businessException)) {
                $msg = self::$t->_('cancel_support_message_1');
                $msg .= implode(',', $businessException);
                $msg .= self::$t->_('cancel_support_message_5');
                throw new BusinessException($msg);
            }
        }

        if ($today > $detail->employment_begin_date) {
            throw new BusinessException(self::$t->_('staff_support_store_error_10')); //如果当前时间大于支援开始时间的不能取消
        }

        $detail->support_status = self::$support_status['cancelled'];
        $save_result = $detail->save();
        if ($save_result === false) {
            $error = '';
            foreach ($detail->getMessages() as $log) {
                $error .= ', ' . $log;
            }
            $this->logger->error([
                'function' => 'cancelStaffSupport',
                'message' => '取消支援信息失败',
                'message_error' => $error,
                'params' => $params,
            ]);
            return false;
        }
        if(isCountry('PH') || isCountry('MY')){
            //新增需求 取消支援 还原加班时间 只有菲律宾
            $otParam['id']          = $detail->id;
            $otParam['lang']        = self::$language;
            $otParam['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_CANCEL;
            $rmq                    = new RocketMQ('ot-support-shift');
            $rmq->sendToMsg($otParam);
        }

        //- 取消后向员工发送通知（BY/KIT-Push+消息）
        $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
        //支援网点取消通知
        //支援网点 %store_name% ，支援日期为 %employment_date% ，支援班次为 %staff_shift% ，该项任务已取消
        $message_title = self::getTranslation($lang)->_('staff_support_store_cancel_message_title');
        $message_content = self::getTranslation($lang)->_(
            'staff_support_store_cancel_message_content_sno',
            [
                'store_name' => $detail->store_name,
                'employment_date' => $detail->employment_begin_date . '-' . $detail->employment_end_date,
                'staff_shift' => $detail->shift_start . '-' . $detail->shift_end,
                'serial_no'       => !empty($detail->serial_no) ? $detail->serial_no : '',
            ]
        );
        $message_params = [
            'staff_info_id' => $staff_info_id,
            'message_title' => $message_title,
            'message_content' => $message_content,
            'source' => 'cancel_staff_support',
        ];
        $message_result = $this->sendStaffBackyardMessage($message_params);
        if ($message_result) {
            $this->sendStaffBackyardPush($message_params);
        }

        if($detail->sub_staff_info_id){
            //立即离职
            $attributes['state'] = HrStaffInfoModel::STATE_RESIGN;
            $attributes['leave_date'] = $today;
            $attributes['is_auto_system_change'] = 1;
            $attributes['staff_info_id'] = $detail->sub_staff_info_id;
            $hr_rpc = (new ApiClient('hris', '', 'update_staff_info', 'zh-CN'));
            $hr_rpc->setParamss($attributes);
            $hr_rpc->execute();

            //取消同步ms信息 把支援日期统一改到昨天
            $sync_ms_params = [
                'id' => $id,
                'master_staff' => $staff_info_id,
                'begin_at' => strtotime('2022-01-01'),//写死历史
                'end_at' => strtotime('2022-01-01') + 86399,
            ];
            $this->logger->info([
                'function' => 'editStaffSupport',
                'message' => '同步取消子账号支援信息',
                'sync_ms_params' => $sync_ms_params,
            ]);
            $this->syncMsSupportApply($sync_ms_params, 'editStaffSupportInfo');
        }

        //保存操作记录
        $model = new HrStaffApplySupportStoreOperateLogsModel();
        $model->pid = $detail->id;
        $model->operator = $user['id'];
        $model->staff_info_id = $staff_info_id;
        $model->request_body = json_encode($params);
        $model->before = json_encode(['support_status' => $support_status]);
        $model->after = json_encode(['support_status' => self::$support_status['cancelled']]);
        $model->action = 'cancel';
        $model->save();

        $server = reBuildCountryInstance($this);
        $server->cancelStaffSupportShift($detail->toArray());

        //记录轮休日志列表
        $logParam['staff_info_id'] = $staff_info_id;
        $logParam['date_at']       = $detail->employment_begin_date;
        $logParam['operate_id']    = $user['id'];
        $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
        $extend['before']          = "support cancel ({$detail->employment_begin_date}~{$detail->employment_end_date})";
        $extend['after']           = "";
        $logServer                 = new WorkShiftService();
        $logServer->addShiftLog($logType, $logParam, $extend);

        $this->logger->info([
            'function' => 'cancelStaffSupport',
            'message' => '取消支援信息成功',
            'operator' => $user['id'],
            'params' => $params,
        ]);
        return true;
    }

    /**
     * 获取指定工号指定日期打卡信息
     * @param $params
     * @return array
     */
    protected function getStaffAttendanceByDate($params)
    {
        $staff_info_ids = $params['staff_info_ids'] ?? [];
        $attendance_date = $params['attendance_date'] ?? '';
        $list = [];
        try {
            $staff_attendance_list = StaffWorkAttendanceModel::find([
                'columns' => 'staff_info_id,organization_id,organization_type,attendance_date,shift_start,shift_end,working_day,started_at,end_at',
                'conditions' => 'staff_info_id IN ({staff_info_ids:array}) and attendance_date = :attendance_date:',
                'bind' => [
                    'staff_info_ids' => $staff_info_ids,
                    'attendance_date' => $attendance_date,
                ],
            ])->toArray();
            if (!empty($staff_attendance_list)) {
                foreach ($staff_attendance_list as $key => $value) {
                    $list[$value['staff_info_id'] . '_' . $value['attendance_date']] = $value;
                }
            }
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'getStaffAttendanceByDate',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
        }
        return $list;
    }

    /**
     * 发送backyard 消息
     * @param $params
     * @return bool
     */
    public function sendStaffBackyardMessage($params)
    {
        $staff_info_id   = $params['staff_info_id'];
        $message_title   = $params['message_title'];
        $message_content = $params['message_content'];
        $source          = $params['source'] ?? 'import_staff_supprot';
        $message_params  = [
            'staff_users'        => [$staff_info_id],
            'message_title'      => $message_title,  //标题
            'message_content'    => addslashes("<div style='font-size: 30px'>" . $message_content . "</div>"),
            'staff_info_ids_str' => $staff_info_id,
            'category'           => -1,
            'id'                 => time() . $staff_info_id . rand(1000000, 9999999),
        ];
        $result          = (new MessagesService())->add_kit_message($message_params);
        if ($result[1] == 1) {
            return true;
        }
        $this->logger->error([
            'function' => 'sendBackyardMessage',
            'message'  => 'send add_kit_message error',
            'result'   => $result,
            'params'   => $message_params,
            'source'   => $source,
        ]);
        return false;
    }

    /**
     * 签字消息
     * @param $params
     * @param int $category
     * @return bool
     */
    protected function sendStaffBackyardMessageV2($params)
    {
        try {
            $staff_info_id = $params['staff_info_id'];
            $message_title = $params['message_title'];
            $message_content = $params['message_content'];
            $source = $params['source'] ?? 'import_staff_supprot';
            $message_params = [
                'staff_users' => [['id' => $staff_info_id]],
                'message_title' => $message_title,  //标题
                'message_content' => addslashes("<div style='font-size: 30px'>" . $message_content . "</div>"),
                'staff_info_ids_str' => $staff_info_id,
                'category' => 33,
                'category_code' => 5,
                //'id' => time() . $staff_info_id . rand(1000000, 9999999)
            ];
            $res = (new MessagesService())->add_kit_message($message_params);

            if ($res[1] == 1) {
                return true;
            } else {
                $this->logger->error([
                    'function' => 'sendBackyardMessage',
                    'message' => 'send add_kit_message error',
                    'result' => $res,
                    'params' => $message_params,
                    'source' => $source,
                ]);
                return false;
            }
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'sendBackyardMessage',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return false;
        }
    }


    /**
     * 发送push
     * @param $params
     * @return bool
     */
    public function sendStaffBackyardPush($params)
    {
        $staff_info_id = $params['staff_info_id'];
        $push_title = $params['message_title'];
        $push_content = $params['message_content'];
        $source = $params['source'] ?? 'import_staff_supprot';
        $data = [
            "staff_info_id" => $staff_info_id,  //推送人员工ID
            "src" => "backyard",      //1:'kit'; 2:'backyard','c';
            "message_title" => $push_title,  //标题
            "message_content" => $push_content,//内容
            'message_scheme' => "flashbackyard://fe/tab?index=message",
        ];
        $client = new ApiClient('bi_rpc', '', 'push_to_staff');
        $client->setParams([$data]);
        $res1 = $client->execute();

        $data = [
            "staff_info_id" => $staff_info_id,  //推送人员工ID
            "src" => "kit",      //1:'kit'; 2:'backyard','c';
            "message_title" => $push_title,  //标题
            "message_content" => $push_content,//内容
            'message_scheme' => "flashbackyard://fe/tab?index=message",
        ];
        $client = new ApiClient('bi_rpc', '', 'push_to_staff');
        $client->setParams([$data]);
        $res2 = $client->execute();
        return !isset($res1['error']) && !isset($res2['error']);
    }

    /**
     * task 更新支援状态
     * @param $params
     * @return bool
     */
    public function updateSupportStatusTask($params)
    {
        try {
            $today = $params['today'] ?? date("Y-m-d");
            $type = $params['type'] ?? 1; //1执行生效 2执行失效
            switch ($type) {
                case 1:
                    $conditions = 'status = 2 and employment_begin_date = :employment_begin_date: and support_status = 1';
                    $bind = ['employment_begin_date' => $today];
                    $support_status = self::$support_status['in_force'];
                    break;
                case 2:
                    $conditions = 'status = 2 and employment_end_date = :employment_end_date: and support_status = 2';
                    $bind = ['employment_end_date' => $today];
                    $support_status = self::$support_status['expired'];
                    break;
                default:
                    $conditions = 'status = 2 and employment_begin_date = :employment_begin_date: and support_status = 1';
                    $bind = ['employment_begin_date' => $today];
                    $support_status = self::$support_status['in_force'];
            }

            if (!empty($params['staff_info_ids'])) {
                $conditions .= " and staff_info_id in ({staff_info_ids:array})";
                $bind['staff_info_ids'] = $params['staff_info_ids'];
            }


            $list = HrStaffApplySupportStoreModel::find(['conditions' => $conditions, 'bind' => $bind])->toArray();
            $ids = [];
            if (!empty($list)) {
                $ids = array_column($list, 'id');
                $table_name = HrStaffApplySupportStoreModel::class;
                $this->logger->info([
                    'function' => 'updateSupportStatusTask',
                    'ids' => $ids,
                    'support_status' => $support_status,
                ]);

                if ($support_status == self::$support_status['in_force']) {
                    $update_result = $this->modelsManager->executeQuery(
                        "update {$table_name} set support_status = :support_status:, actual_begin_date = :actual_begin_date: where id in ({ids:array}) ",
                        ['support_status' => $support_status, 'actual_begin_date' => date("Y-m-d"), 'ids' => $ids]
                    );
                } else {
                    $update_result = $this->modelsManager->executeQuery(
                        "update {$table_name} set support_status = :support_status:, actual_end_date =:actual_end_date: where id in ({ids:array}) ",
                        ['support_status' => $support_status, 'actual_end_date' => $today, 'ids' => $ids]
                    );
                }
                //新增 支援 日期拆分表数据 生效任务才写
                if($type == 1){
                    foreach ($list as $li){
                        $this->addSupportSplit($li);
                    }
                }

                if ($update_result->success() === false) {
                    $messages = $update_result->getMessages();
                    $error = '';
                    foreach ($messages as $message) {
                        $error .= ',' . $message->getMessage();
                    }

                    $this->logger->error([
                        'function' => 'updateSupportStatusTask',
                        'message' => 'save_error',
                        'error' => $error,
                        'params' => $params,
                    ]);
                    return false;
                }

                return true;
            } else {
                $this->logger->info([
                    'function' => 'updateSupportStatusTask',
                    'message' => '没有变更的数据',
                    'params' => $params,
                ]);
            }

            $this->logger->info([
                'function' => 'updateSupportStatusTask',
                '执行总数' => count($list),
                '执行ids' => $ids,
                'params' => $params,
            ]);
            return true;
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'updateSupportStatusTask',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return false;
        }
    }

    /**
     * 获取指定工号信息
     * @param $staff_info_ids
     * @return array
     */
    public function getStaffInfoByIds($staff_info_ids): array
    {
        $staff_list = [];
        if (empty($staff_info_ids)) {
            return $staff_list;
        }
        $staff_list = HrStaffInfoModel::find([
            'conditions' => "formal = 1 and is_sub_staff = 0 and staff_info_id in ({staff_ids:array})",
            'bind'       => [
                'staff_ids' => $staff_info_ids,
            ],
            'columns'    => 'manger,staff_info_id,name,sex,week_working_day,identity,mobile,mobile_company
            ,personal_email,job_title,sys_store_id,sys_department_id,node_department_id,state,hire_type,rest_type
            ,hire_date',
        ])->toArray();
        return !empty($staff_list) ? array_column($staff_list, null, 'staff_info_id') : [];
    }

    //根据网点id获取组织架构上网点负责人姓名和手机号
    public function getStoreManagerMobile($store_id)
    {
        $store = SysStoreModel::findFirst([
            'columns' => 'id, name, manager_id, manage_region, manage_piece',
            'conditions' => 'id = :storeId:',
            'bind' => ['storeId' => $store_id],
        ]);
        $staff_name = '';
        $mobile = '';
        $mobile_company = '';
        $district_manager = [
            'district_manager_name' => '',
            'district_manager_mobile' => '',
        ];
        $store = !empty($store) ? $store->toArray() : [];
        if (!empty($store)) {
            $staff_info = $staff_info = $this->getStaffMobile($store['manager_id']);
            $mobile = $staff_info['mobile'] ?? '';
            $mobile_company = $staff_info['mobile_company'] ?? '';
            $staff_name = $staff_info['name'] ?? '';

            $district_manager = $this->getDistrictManagerMobile($store['manage_piece']);
        }

        return [
            'district_manager_name' => $district_manager['district_manager_name'] ?? '',
            'district_manager_mobile' => $district_manager['district_manager_mobile'] ?? '',
            'branch_supervisor_name' => $staff_name,
            'branch_supervisor_mobile' => !empty($mobile_company) ? $mobile_company : $mobile,
        ];
    }

    //获取片区负责人姓名手机号
    public function getDistrictManagerMobile($piece_id)
    {
        if (empty($piece_id)) {
            return [
                'district_manager_name' => '',
                'district_manager_mobile' => '',
            ];
        }
        $piece_info = SysManagePieceModel::findFirst([
            'columns' => 'id, name, manage_region_id, manager_id',
            'conditions' => 'id = :piece_id:',
            'bind' => ['piece_id' => $piece_id],
        ]);
        $staff_name = '';
        $mobile = '';
        $mobile_company = '';
        $piece_info = $piece_info ? $piece_info->toArray() : [];
        if (!empty($piece_info)) {
            $staff_info = $this->getStaffMobile($piece_info['manager_id']);
            $mobile = $staff_info['mobile'] ?? '';
            $mobile_company = $staff_info['mobile_company'] ?? '';
            $staff_name = $staff_info['name'] ?? '';
        }

        return [
            'district_manager_name' => $staff_name,
            'district_manager_mobile' => !empty($mobile_company) ? $mobile_company : $mobile,
        ];
    }

    //根据工号获取手机号
    public function getStaffMobile($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staff_info = HrStaffInfoModel::findFirst([
            'columns' => 'staff_info_id, name, mobile, mobile_company, state',
            'conditions' => 'staff_info_id = :staff_info_id: and state = 1',
            'bind' => ['staff_info_id' => $staff_info_id],
        ]);
        $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];

        return $staff_info;
    }

    //判断是否与其他工单日期冲突
    public function validateConflict($employment_begin_date, $employment_end_date, $staff_info_id)
    {
        $support_count = HrStaffApplySupportStoreModel::count([
            'conditions' => 'staff_info_id = :staff_info_id: and status in(1,2) and employment_end_date >= :employment_begin_date: and employment_begin_date <= :employment_end_date: and support_status != 4',
            'bind' => [
                'staff_info_id' => $staff_info_id,
                'employment_begin_date' => $employment_begin_date,
                'employment_end_date' => $employment_end_date,
            ],
        ]);
        return $support_count;
    }

    //判断是否与其他工单日期冲突
    public function validateConflictBatch($employment_begin_date, $employment_end_date, $staffs)
    {
        $support_count = HrStaffApplySupportStoreModel::find([
            'columns' => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array}) and status in(1,2) 
            and employment_end_date >= :employment_begin_date: and employment_begin_date <= :employment_end_date: and support_status != 4',
            'bind'       => [
                'ids'                   => $staffs,
                'employment_begin_date' => $employment_begin_date,
                'employment_end_date'   => $employment_end_date,
            ],
        ])->toArray();
        if(empty($support_count)){
            return [];
        }
        return array_count_values(array_column($support_count,'staff_info_id'));
    }

    /**
     * 获取操作记录
     * @return array
     */
    public function getOperateLogs($params = [])
    {
        $id   = $params['id'];
        $type = $params['type'] ?? HrStaffApplySupportStoreOperateLogsModel::TYPE_SUPPORT_STAFF;

        if (empty($id)) {
            return [];
        }

        $logs = HrStaffApplySupportStoreOperateLogsModel::find([
            'conditions' => 'pid = :pid: AND type = :type:',
            'bind'       => [
                'pid'  => $id,
                'type' => $type,
            ],
            'order'      => 'id desc',
        ])->toArray();
        if (empty($logs)) {
            return [];
        }

        $staff_ids = array_column($logs, 'operator');
        $nameList  = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staffs:array})',
            'bind'       => [
                'staffs' => $staff_ids,
            ],
            'columns'    => 'staff_info_id,name',
        ])->toArray();
        $nameList  = array_column($nameList, 'name', 'staff_info_id');

        $result = [];
        foreach ($logs as $log) {
            $change = $this->getChange(json_decode($log['before'], true), json_decode($log['after'], true));

            if (!empty($change)) {
                $result[] = [
                    'operator'      => $log['operator'],
                    'operator_name' => $nameList[$log['operator']] ?? '',
                    'change'        => $change,
                    'operate_time'  => show_time_zone($log['created_at']),
                ];
            }
        }

        return $result;
    }

    public function getShiftListByChange () {
        return (new HrShiftService())->getListFromCache();
    }

    /**
     * 获取变更
     * @param $before
     * @param $after
     * @return array
     */
    public function getChange($before, $after)
    {
        $result = [];
        $columns = [
            'store_id' => 'staff_support_store_store',
            'shift_id' => 'staff_support_store_shift',
            'employment_begin_date' => 'staff_support_store_begin_date',
            'employment_end_date'  => 'staff_support_store_end_date',
            'is_stay' => 'staff_support_store_is_stay',
            'is_separate' => 'staff_support_store_is_separate',
            'is_original_store' => 'staff_support_store_is_original_store',
            'support_status' => 'staff_support_store_support_status',
            'remark' => 'remarks',
            "final_audit_num" => "title_demand_num",
            'transportation_mode' => 'staff_support_store_transportation_mode',
        ];

        //网点
        $store_ids = [];
        if (isset($before['store_id'])) {
            $store_ids = array_merge($store_ids,[$before['store_id']]);
        }
        if (isset($after['store_id'])) {
            $store_ids = array_merge($store_ids,[$after['store_id']]);
        }
        $sys_store_list = (new SysStoreService())->getStoreList($store_ids);
        $sys_store_list = array_column($sys_store_list, 'name', 'id');

        //所有班次信息 note 注意差异化
        $shift_list = $this->getShiftListByChange();
        $shift_list = array_column($shift_list, null, 'id');

        //支援状态
        $support_status_list = $this->getSupportStatus();
        $support_status_list = array_column($support_status_list, 'value', 'key');

        foreach ($columns as $column => $key) {
            $beforeColumn = $before[$column] ?? "";
            $afterColumn = $after[$column] ?? "";

            if ($column == 'store_id') {
                $beforeColumn = $sys_store_list[$beforeColumn] ?? '';
                $afterColumn = $sys_store_list[$afterColumn] ?? '';
            }

            if ($column == 'shift_id') {
                $beforeColumn = isset($shift_list[$beforeColumn])
                    ? sprintf("%s~%s", $shift_list[$beforeColumn]['start'], $shift_list[$beforeColumn]['end'])
                    :  '';
                $afterColumn = isset($shift_list[$afterColumn])
                    ? sprintf("%s~%s", $shift_list[$afterColumn]['start'], $shift_list[$afterColumn]['end'])
                    :  '';
            }

            if ($column == 'support_status') {
                $beforeColumn = $support_status_list[$beforeColumn] ?? '';
                $afterColumn = $support_status_list[$afterColumn] ?? '';
            }

            if ($column == 'is_stay') {
                $beforeColumn = $beforeColumn == 1 ? static::$t->t('staff_support_store_yes') : static::$t->t('staff_support_store_no');
                $afterColumn = $afterColumn == 1 ? static::$t->t('staff_support_store_yes') : static::$t->t('staff_support_store_no');
            }
            if ($column == 'is_separate') {
                $beforeColumn = $beforeColumn == 1 ? static::$t->t('staff_support_store_yes') : static::$t->t('staff_support_store_no');
                $afterColumn = $afterColumn == 1 ? static::$t->t('staff_support_store_yes') : static::$t->t('staff_support_store_no');
            }
            if ($column == 'is_original_store') {
                $beforeColumn = $beforeColumn == 1 ? static::$t->t('staff_support_store_yes') : static::$t->t('staff_support_store_no');
                $afterColumn = $afterColumn == 1 ? static::$t->t('staff_support_store_yes') : static::$t->t('staff_support_store_no');
            }

            if ($column == 'remark') {
                $beforeColumn = $before['remark'] ?? "";
                $afterColumn = isset($after['remark']) ? static::$t->t($after['remark']) : '';
            }

            if ($column == 'transportation_mode') {
                $beforeColumn = !empty($beforeColumn) ? static::$t->t('transportation_mode_' . $beforeColumn) : '';
                $afterColumn  = !empty($afterColumn) ? static::$t->t('transportation_mode_' . $afterColumn) : '';
            }

            if (isset($beforeColumn) && isset($afterColumn) && $beforeColumn != $afterColumn) {
                $result[] = sprintf("%s: %s => %s", static::$t->t($key), $beforeColumn, $afterColumn);
            }
        }

        return $result;
    }

    /**
     * 返回支援状态
     * @return array[]
     */
    public function getSupportStatus(): array
    {
        return [
            [
                'key' => self::$support_status['pending'],
                'value' => self::$t->_('staff_support_store_support_status_' . self::$support_status['pending']),
            ],
            [
                'key' => self::$support_status['in_force'],
                'value' => self::$t->_('staff_support_store_support_status_' . self::$support_status['in_force']),
            ],
            [
                'key' => self::$support_status['expired'],
                'value' => self::$t->_('staff_support_store_support_status_' . self::$support_status['expired']),
            ],
            [
                'key' => self::$support_status['cancelled'],
                'value' => self::$t->_('staff_support_store_support_status_' . self::$support_status['cancelled']),
            ],
        ];
    }

    /**
     * 返回交通方式
     * @return array[]
     */
    public function getTransportationModeItem(): array
    {
        $item = [];
        foreach (HrStaffApplySupportStoreModel::$transportation_mode_item as $mode) {
            $item[] = [
                'value' => (string)$mode,
                'label' => self::$t->_('transportation_mode_' . $mode),
            ];
        }

        return $item;
    }

    /**
     * 返回是否住宿枚举
     * @return array[]
     */
    public function getIsStayStatus(): array
    {
        return [
            [
                'value' => (string)HrStaffApplySupportStoreModel::IS_STAY_NO_ENUMS,
                'label' => self::$t->_('staff_support_store_no'),
            ],
            [
                'value' => (string)HrStaffApplySupportStoreModel::IS_STAY_YES_ENUMS,
                'label' => self::$t->_('staff_support_store_yes'),
            ],
        ];
    }


    public function modifyStaffShift($beforeSupportData, $afterSupportData, $operate_id = 10000): bool
    {
        $subFlag = false;//申请是否已生效 判断是否操作 子账号 没生效 没有子账号
        if ($beforeSupportData['support_status'] == self::$support_status['in_force']) {
            $subFlag = true;
        }
        $shiftServer = new StaffShiftService();

        //更新 交集的区间
        $today = date('Y-m-d');
        $start = max($today, $beforeSupportData['employment_begin_date'], $afterSupportData['employment_begin_date']);
        $end   = min($beforeSupportData['employment_end_date'], $afterSupportData['employment_end_date']);
        if ($start > $end) {//没有交集说明 没生效 也没有子账号
            $beforeDateList = DateHelper::DateRange(strtotime($beforeSupportData['employment_begin_date']),
                                                    strtotime($beforeSupportData['employment_end_date']));
            $afterDateList  = DateHelper::DateRange(strtotime($afterSupportData['employment_begin_date']),
                                                    strtotime($afterSupportData['employment_end_date']));
            $shiftServer->shiftReBack($afterSupportData, $beforeDateList);//还原之前
            $shiftServer->shiftBackup($afterSupportData, $afterDateList);//备份修改后的
            return true;
        }


        $whereStaff[] = $beforeSupportData['staff_info_id'];
        if ($subFlag) {//看 带不带子账号
            $whereStaff[] = $beforeSupportData['sub_staff_info_id'];
        }
        //更新 新支援区间 班次
        if ($beforeSupportData['shift_id'] != $afterSupportData['shift_id']) {
            //看今天打没打卡 如果打卡了 从明天开始 产品说只要有一个账号打卡就不改
            if ($start == $today) {
                $attendanceInfo = StaffWorkAttendanceModel::find([
                    'conditions' => 'staff_info_id in ({ids:array}) and attendance_date = :date_at:',
                    'bind'       => ['ids' => $whereStaff, 'date_at' => $today],
                ])->toArray();
                if (!empty($attendanceInfo)) {
                    $start = date('Y-m-d', strtotime('+1 day'));
                }
            }
            HrStaffShiftMiddleDateModel::find([
                'conditions' => 'staff_info_id in ({ids:array}) and shift_date between :start_date: and :end_date:',
                'bind'       => ['ids' => $whereStaff, 'start_date' => $start, 'end_date' => $end],
            ])->update([
                'shift_id'    => $afterSupportData['shift_id'],
                'shift_type'  => $afterSupportData['shift_type'],
                'shift_start' => $afterSupportData['shift_start'],
                'shift_end'   => $afterSupportData['shift_end'],
            ]);
            //加操作日志表 20YY-MM-DD->20YY-MM-DD期间：HH:mm(before) → HH:mm(after)
            $logStart = $start;
            if($afterSupportData['employment_begin_date'] > $today && $afterSupportData['employment_begin_date'] < $beforeSupportData['employment_begin_date']){
                $logStart = $afterSupportData['employment_begin_date'];
            }
            $logParam['staff_info_id'] = $beforeSupportData['staff_info_id'];
            $logParam['date_at']       = $logStart;
            $logParam['operate_id']   = $operate_id;
            $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
            $extend['before']          = "{$logStart}~{$afterSupportData['employment_end_date']}：{$beforeSupportData['shift_start']}-{$beforeSupportData['shift_end']}";
            $extend['after']           = "{$afterSupportData['shift_start']}-{$afterSupportData['shift_end']}";
            $logServer = new WorkShiftService();
            $logServer->addShiftLog($logType, $logParam, $extend);
        }

        //结束时间 提前 需要恢复
        if ($afterSupportData['employment_end_date'] < $beforeSupportData['employment_end_date']) {
            $start = $afterSupportData['employment_end_date'];//3号
            $end = $beforeSupportData['employment_end_date'];//5号
            //需要 恢复 4，5
            $dateList = DateHelper::DateRange(strtotime("{$start} +1 day"),strtotime($end));
            //删除子账号 班次
            if($subFlag){
                HrStaffShiftMiddleDateModel::find([
                    'conditions' => 'shift_date in ({dates:array}) and staff_info_id = :sub_id:',
                    'bind'       => [
                        'sub_id'  => $beforeSupportData['sub_staff_info_id'],
                        'dates' => $dateList,
                    ],
                ])->update(['deleted' => 1]);
            }
            $shiftServer->shiftReBack($afterSupportData,$dateList);
        }

        //结束时间 延后 需要备份新增
        if($afterSupportData['employment_end_date'] > $beforeSupportData['employment_end_date']){
            $start = $beforeSupportData['employment_end_date'];//5号
            $end = $afterSupportData['employment_end_date'];//7号
            //需要新增 6，7
            $dateList = DateHelper::DateRange(strtotime("{$start} +1 day"),strtotime($end));
            $shiftServer->shiftBackup($afterSupportData,$dateList);
        }

        //开始时间 延后  需要恢复
        if($afterSupportData['employment_begin_date'] > $beforeSupportData['employment_begin_date']){
            $start = $beforeSupportData['employment_begin_date'];//3号
            $end = $afterSupportData['employment_begin_date'];//5号
            //恢复3，4
            $dateList = DateHelper::DateRange(strtotime($start),strtotime("{$end} -1 day"));
            $shiftServer->shiftReBack($afterSupportData,$dateList);
        }

        //开始时间提前 需要备份新增
        if($afterSupportData['employment_begin_date'] < $beforeSupportData['employment_begin_date']){
            $start = $afterSupportData['employment_begin_date'];//1号
            $end = $beforeSupportData['employment_begin_date'];//3号
            //新增 1，2
            $dateList = DateHelper::DateRange(strtotime($start),strtotime("{$end} -1 day"));
            $shiftServer->shiftBackup($afterSupportData,$dateList);
        }
        return true;
    }

    //取消订单 还原主账号 只有没生效的才能取消
    public function cancelStaffSupportShift($supportData): bool
    {
        //是否需要操作子账号
        if ($supportData['support_status'] == self::$support_status['in_force'] && !empty($supportData['sub_staff_info_id'])) {
            //删除子账号班次信息
            HrStaffShiftMiddleDateModel::find([
                'conditions' => 'shift_date >= :start_date: and shift_date <= :end_date: and staff_info_id = :sub_id:',
                'bind'       => [
                    'sub_id'     => $supportData['sub_staff_info_id'],
                    'start_date' => $supportData['employment_begin_date'],
                    'end_date'   => $supportData['employment_end_date'],
                ],
            ])->update(['deleted' => 1]);
        }

        $shiftServer = new StaffShiftService();
        $dateList    = DateHelper::DateRange(strtotime($supportData['employment_begin_date']), strtotime($supportData['employment_end_date']));
        $shiftServer->shiftReBack($supportData, $dateList);
        return true;
    }

    //马来重写 菲律宾没子账号
    public function createSupportStaffShift($supportDetail, $isImport = false):bool
    {
        $sub_staff_info_id = $supportDetail['sub_staff_info_id'];
        $shift_start_date  = $supportDetail['shift_start_date'];
        $shift_end_date    = $supportDetail['shift_end_date'];
        $days              = DateHelper::DateRange(strtotime($shift_start_date), strtotime($shift_end_date));
        //非导入 任务跑的 只操作子账号
        if ($isImport === false && !empty($supportDetail['sub_staff_info_id'])) {
            $subStaffShit = [];
            foreach ($days as $day) {
                $subStaffShit[] = [
                    'staff_info_id' => $sub_staff_info_id,
                    'shift_date'    => $day,
                    'shift_id'      => $supportDetail['shift_id'],
                    'shift_type'    => $supportDetail['shift_type'],
                    'shift_start'   => $supportDetail['shift_start'],
                    'shift_end'     => $supportDetail['shift_end'],
                    'operator_id'   => $supportDetail['operator_id']??'-1',
                ];
            }
            return (new HrStaffShiftMiddleDateModel())->batch_insert($subStaffShit, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        }

        $shiftServer = new StaffShiftService();
        return $shiftServer->shiftBackup($supportDetail, $days);
    }

    /**
     * 获取导入员工职位-支援职位-角色配置
     * @return array
     */
    public function getSupportStaffJobTitleConfig(): array
    {
        $env = SettingEnvModel::getMultiEnvByCode([
            'support_staff_apply_job_title_config',
            'support_job_title_role_config',
        ]);

        $support_staff_apply_job_title_config = json_decode($env['support_staff_apply_job_title_config'], true); //员工职位对应可支援职位列表
        $support_job_title_role_config        = json_decode($env['support_job_title_role_config'], true); //支援职位对应角色列表

        $support_staff_apply_job_title_ids = array_keys($support_staff_apply_job_title_config); //员工可支援职位ids
        $support_job_title_ids             = array_keys($support_job_title_role_config);        //支援职位ids

        $job_title_ids  = array_merge($support_staff_apply_job_title_ids, $support_job_title_ids);

        $hr_job_title_service   = new HrJobTitleService();
        $job_title_list         = $hr_job_title_service->getJobTitleMapByIds($job_title_ids);
        $support_job_title_list = $hr_job_title_service->getJobTitleMapByIds($support_job_title_ids);

        return [
            'support_staff_apply_job_title_ids' => $support_staff_apply_job_title_ids,    //员工可去支援的职位ids
            'support_job_title_ids'             => $support_job_title_ids,                //支援的职位ids
            'support_staff_job_title_config'    => $support_staff_apply_job_title_config, //员工职位和支援职位关联
            'support_job_title_role_config'     => $support_job_title_role_config,        //支援职位和角色关系配置
            'support_job_title_list'            => $support_job_title_list,               //支援职位名称列表
            'job_title_list'                    => $job_title_list,                       //职位名称
        ];
    }


    //根据区间获取支援信息 只有主账号
    public function getSupportDataBetween($staffIds, $start, $end){
        if(empty($staffIds) || empty($start) || empty($end)){
            return [];
        }
        $conditions = 'status = 2 and support_status in (1,2,3) ';
        $conditions .= ' and staff_info_id in ({ids:array})';
        $bind['ids'] = $staffIds;
        $conditions .= ' and !(employment_end_date < :start_date: or employment_begin_date > :end_date:)';
        $bind['start_date'] = $start;
        $bind['end_date'] = $end;

        $data = HrStaffApplySupportStoreModel::find([
            'columns' => 'id, staff_info_id, employment_begin_date, employment_end_date',
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();
        return $data;
    }

    //按工号日期 整理成每天
    public function formatSupportData($data, $start, $end){
        if(empty($data) || empty($start) || empty($end)){
            return [];
        }
        $date_range = DateHelper::DateRange(strtotime($start), strtotime($end));
        $return = [];
        foreach ($date_range as $date){
            foreach ($data as $da){
                $key = "{$da['staff_info_id']}-{$date}";
                $return[$key] = false;
                if($da['employment_begin_date'] <= $date && $da['employment_end_date'] >= $date){
                    $return[$key] = true;
                }
            }
        }
        return $return;

    }

    //支援日期拆分表 写数据 包括子账号和主账号
    public function addSupportSplit($supportInfo){
        $startDate = $supportInfo['employment_begin_date'];
        $endDate   = $supportInfo['employment_end_date'];
        if (empty($startDate) || empty($endDate)) {
            return;
        }
        $insert   = [];
        $dateList = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        foreach ($dateList as $date) {
            $row['staff_info_id']   = $supportInfo['staff_info_id'];
            $row['origin_id']       = $supportInfo['id'];
            $row['employment_date'] = $date;
            $row['is_separate']     = $supportInfo['is_separate'];
            $insert[]               = $row;
        }
        $model = new HrStaffApplySupportStoreSplitModel();
        return $model->batch_insert($insert);
    }
    /**
     * 验证支援班次与原本次时长是否一致
     * @param $staff_id
     * @param $support_begin_date
     * @param $shift_duration
     * @return bool
     */
    protected function checkStaffShiftDuration($staff_id, $support_begin_date, $shift_duration): bool
    {
        return false;
    }

    protected function cancelOT($staff_info_id, $start_date, $end_date, array $support_shift_info=[]):bool
    {
        return false;
    }

    //验证 支援开始日期 和 员工入职日期 是否符合配置 https://flashexpress.feishu.cn/wiki/Wt4vwxsiXizaNlkdl97cwFX3nGf
    public function checkHireSetting($staffInfo, $supportBeginDate){
        if(empty($staffInfo) || empty($staffInfo['hire_date']) || empty($staffInfo['job_title'])){
            return true;
        }
        if(empty($supportBeginDate)){
            return true;
        }

        $setting = (new SettingEnvService())->getSetVal('support_staff_limit_join_days');
        $this->logger->info(['checkHireSetting' => ['setting' => $setting]]);
        if(empty($setting)){
            return true;
        }
        $setting = preg_replace('/\s+/', '', strtr(trim($setting), ['，'=>',', '｜'=>'|', '【'=>'[', '】'=>']']));

        // 使用正则表达式匹配所有 [数字列表]|数字 的模式
        preg_match_all('/\[([^\]]+)\]\|(\d+)/', $setting, $matches, PREG_SET_ORDER);
        $result = array();
        foreach ($matches as $match) {
            $keys = explode(',', $match[1]); // 分割键列表
            $value = (int)$match[2];        // 获取值
            foreach ($keys as $key) {
                $result[trim($key)] = $value; // 为每个键分配值
            }
        }

        //职位匹配的天数 如果没有匹配或者 匹配上是0 不限制
        $days = $result[$staffInfo['job_title']] ?? 0;
        if(empty($days)){
            return true;
        }

        $limitDate = date('Y-m-d', strtotime("{$staffInfo['hire_date']} +{$days} day"));
        $limitStart = strtotime($limitDate);
        $this->logger->info(['checkHireSetting' => ['staff_info' => $staffInfo, 'limit_date' => $limitDate, 'support_date' => $supportBeginDate]]);
        if(strtotime($supportBeginDate) < $limitStart){
            //新员工入职%days%天内不能支援
            return self::$t->_('staff_support_store_hire_days_notice', ['days' => $days]);
        }
        return true;
    }


    /**
     * 获取申请支援的员工信息--主账号查子账号
     * @param $staff_info_id
     * @param $date
     * @return array
     */
    public function getSupportOsStaffInfo($staff_info_id, $date = ''): array
    {
        //到期时间是当地时间
        $today = date('Y-m-d');
        if (!empty($date)) {
            $today = $date;
        }
        $bind             = [
            'staff_info_id'         => $staff_info_id,
            'status'                => Enums::audit_status_2,
            'employment_begin_date' => $today,
            'employment_end_date'   => $today,
        ];
        $supportStaffInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and status = :status: and employment_begin_date <= :employment_begin_date: and employment_end_date >= :employment_end_date: and support_status != 4',
            'bind'       => $bind,
        ]);

        return $supportStaffInfo ? $supportStaffInfo->toArray() : [];
    }

    /**
     * 获取申请支援的员工信息
     * @param $staff_info_id
     * @param $date
     * @return array
     */
    public function getSupportInfoBySubStaff($staff_info_id, $date = ''): array
    {
        //到期时间是当地时间
        $today = date('Y-m-d');
        if (!empty($date)) {
            $today = $date;
        }
        $bind             = [
            'sub_staff_info_id'     => $staff_info_id,
            'status'                => Enums::audit_status_2,
            'employment_begin_date' => $today,
            'employment_end_date'   => $today,
        ];
        $supportStaffInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'sub_staff_info_id = :sub_staff_info_id: and status = :status: and employment_begin_date <= :employment_begin_date: and employment_end_date >= :employment_end_date: and support_status != 4',
            'bind'       => $bind,
        ]);

        return $supportStaffInfo ? $supportStaffInfo->toArray() : [];
    }

    /**
     * 验证支援日期变化的轮休规则配置
     * @description: 检查修改前后的日期变化，如果有变化则校验新日期区间的配置规则
     * @author: AI
     * @date: 2025-07-30 16:55:21
     * @param object $detail 支援详情对象
     * @param array $before 修改前的数据
     * @param string $newBeginDate 新的开始日期
     * @param string $newEndDate 新的结束日期
     * @throws ValidationException
     */
    private function validateSupportDateChange($detail, $before, $newBeginDate, $newEndDate)
    {
        // 检查开始和结束日期是否有变动
        $originalBeginDate = $before['employment_begin_date'];
        $originalEndDate   = $before['employment_end_date'];

        $hasDateChange = ($originalBeginDate !== $newBeginDate) || ($originalEndDate !== $newEndDate);

        // 如果没有日期变化，跳过验证
        if (!$hasDateChange) {
            return;
        }

        $suggestionMode = (new SettingEnvService())->getSetVal('suggestion_mode');

        // 如果不是按日配置模式，跳过验证
        if ($suggestionMode != 1) {
            return;
        }
        $workdayService  = new WorkdaySettingService();
        $jobTypeToJobIds = $workdayService->getJobMap();

        // 找到职位对应的职位类型
        $jobType = null;
        foreach ($jobTypeToJobIds as $type => $jobIds) {
            if (in_array($detail->job_title_id, $jobIds)) {
                $jobType = $type;
                break;
            }
        }

        // 如果职位不在4个职位类型之内，跳过验证
        if ($jobType === null) {
            return;
        }

        // 一次性获取所有日期的配置数据
        $allDailyConfigs = WorkdaySettingDailyDetailModel::find([
            'conditions' => 'store_id = :store_id: AND date_at between :start_date: and :end_date: AND job_type = :job_type: and is_delete = 0',
            'bind'       => [
                'store_id'   => $detail->store_id,
                'start_date' => $newBeginDate,
                'end_date'   => $newEndDate,
                'job_type'   => $jobType,
            ],
            'columns'    => 'store_id, min_num, max_num',
        ])->toArray();
        if (empty($allDailyConfigs)) {
            return;
        }
        $allDailyConfigs = array_column($allDailyConfigs, null, 'date_at');
        $actualStaffList = $workdayService->getAllActualStaffCount($detail->store_id, $newBeginDate, $newEndDate,
            [$jobType], $jobTypeToJobIds);

        // 使用 DateHelper::DateRange 生成修改后的支援期间所有日期
        $supportDates = DateHelper::DateRange(strtotime($newBeginDate), strtotime($newEndDate));
        // 检查每个支援日期
        $logDate = [];
        foreach ($supportDates as $date) {
            // 如果该日期没有配置，跳过
            if (!isset($allDailyConfigs[$date])) {
                continue;
            }
            $dailyConfig = $allDailyConfigs[$date];
            // 获取当前已排班人数
            $currentActualStaff = $actualStaffList[$date][$jobType] ?? [];
            $currentActualCount = count($currentActualStaff);

            // 解析配置数据，获取最大值
            $maxAllowed = $workdayService->getMaxAllowedStaff($dailyConfig);

            // 如果没有最大值限制，跳过验证
            if ($maxAllowed === null) {
                continue;
            }

            // 检查：当前人数 + 1（当前支援记录） 是否超过最大值
            if (($currentActualCount) > $maxAllowed) {
                $logDate[] = $date;
            }
        }
        if (empty($logDate)) {
            return;
        }
        throw new ValidationException(self::$t->_('workday_daily_support_notice',
            ['date_list' => implode('|', $logDate)]));
    }


}
