<?php
/**
 * HUB,B-HUB网点外协员工管理
 */

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\Enums\HubOutSourcingStaffEnums;
use App\Library\Enums\OutSourcingStaffEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\HikvisionClient;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AttendanceHikStoreSettingModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrOutsourcingOrderDetailModel;
use App\Models\backyard\HrOutsourcingOrderModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\HikvisionSyncTaskModel;
use App\Models\backyard\HrStaffInfoReadModel as HrStaffInfoModel;
use App\Models\backyard\OutsourcingCompanyModel;
use App\Models\backyard\StaffHikvisionLogModel;
use App\Models\backyard\StaffHikvisionModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\OutsourcingStaffStoreOrderNumModel;
use App\Models\backyard\SysStoreModel;
use App\Repository\SysStoreRepository;
use OutsourcingAttendanceStatisticsExportTask;

class HubOutSourcingStaffService extends BaseService
{
    const REDIS_ORG_CACHE     = 'get_hik_org_list_info';//班组信息缓存,组织树
    const REDIS_HIK_ORG_CACHE_1 = 'hik_org_list_data_1';//班组信息缓存，一级缓存
    const REDIS_HIK_ORG_CACHE_2 = 'hik_org_list_data_2';//班组信息缓存，二级缓存
    /**
     * 枚举接口
     * @return array
     */
    public function getSysInfo()
    {
        try {
            //外协公司
            $outsourcing_company = (new OutsourcingCompanyModel())->getList();
            foreach ($outsourcing_company as $key => $val) {
                $data['outsourcing_company'][] = [
                    'code' => (int)$val['id'],
                    'name' => $val['company_name'],
                ];
            }
            //网点
            //根据网点找部门
            $store_department_relation = (new SettingEnvService())->getSetVal('hub_store_department_id_relation');
            if (!empty($store_department_relation)) {
                $store_department_relation = json_decode($store_department_relation, true);
            }
            $departIds = array_values(array_unique(array_values($store_department_relation)));
            $departInfo = (new DepartmentService())->getDepartmentInfo($departIds);
            $departInfoToId = [];
            if($departInfo) {
                $departInfoToId = array_column($departInfo, 'name','id');
            }

            $store_list = (new SysStoreModel())->getStoreListByCategory(array_keys(OutSourcingStaffEnums::$store_category));

            if(isCountry('TH')) {
                $store_list = array_merge($store_list, (new SysStoreRepository())->getPdcHikStore());
            }

            foreach ($store_list as $key => $val) {
                $departmentName = '';
                if(!empty($store_department_relation[$val['id']])) {
                    $departmentName = $departInfoToId[$store_department_relation[$val['id']]] ?? '';
                }
                $data['store_list'][] = [
                    'code' => $val['id'],
                    'name' => $val['name'],
                    'department_name' => $departmentName
                ];
            }
            //在职状态
            foreach (OutSourcingStaffEnums::$staff_state as $key => $val) {
                $data['staff_state'][] = [
                    'code' => $key,
                    'name' => self::$t->_($val),
                ];
            }
            //同步状态
            foreach (StaffHikvisionModel::$sync_list as $key => $val) {
                $data['sync_state'][] = [
                    'code' => $key,
                    'name' => self::$t->_($val),
                ];
            }
            //性别
            foreach (HubOutSourcingStaffEnums::$sex as $key=>$val){
                $data['sex'][] = [
                    'code' => $key,
                    'name' => self::$t->_($val),
                ];
            }
            //国籍
            if (isCountry('MY')){
                $_nationality_list = HubOutSourcingStaffEnums::$nationality_list_my;
            }else{
                $_nationality_list = HubOutSourcingStaffEnums::$nationality_list;
            }
            foreach ($_nationality_list as $key=>$val){
                $data['nationality'][] = [
                    'code' => $key,
                    'name' => self::$t->_($val),
                ];
            }
            //职位
            $getJobTitleMapByIds = (new SettingEnvService())->getSetVal('hub_os_select_roles',',');
            $jobTitleMap = (new HrJobTitleService())->getJobTitleMapByIds($getJobTitleMapByIds);

            $data['job'] = [];
            foreach($jobTitleMap as $key=>$val ){
                $data['job'][] = [
                    'code' => $key,
                    'name' => $val,
                ];
            }


            $result['code'] = ErrCode::SUCCESS;
            $result['msg'] = 'Success';
            $result['data'] = $data;
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("get-hub-out-sourcing-staff-sys-info-fail", 'error');
            $result['code'] = ErrCode::SYSTEM_ERROR;
            $result['msg'] = 'server error';
            $result['data'] = [];
        }
        return $result;
    }


    /**
     * 外协公司
     *
     * @return array
     */
    public function companyInfo()
    {
        $data                = [];
        $outsourcing_company = OutsourcingCompanyModel::find()->toArray();
        foreach ($outsourcing_company as $key => $val) {
            $data['outsourcing_company'][] = [
                'value' => (int)$val['id'],
                'label' => $val['company_name'],
            ];
        }

        return $data;
    }

    /**
     * 获取列表
     * @param $params
     * @return array
     */
    public function getList($params)
    {
        $data['count'] = 0;
        $data['list'] = [];
        try {
            //获取列表总数
            $count = $this->getListCount($params);
            if ($count <= 0) {
                return [
                    'code' => ErrCode::SUCCESS,
                    'msg' => 'Success',
                    'data' => $data,
                ];
            }
            $data['count'] = $count;
            $list = $this->getListByLimit($params);
            $data['list'] = $this->formatListData($list);
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("get-hub-out-sourcing-staff-fail" . json_encode($params), 'info');
        }
        $result = [
            'code' => ErrCode::SUCCESS,
            'msg' => 'Success',
            'data' => $data,
        ];
        return $result;
    }

    /**
     * 获取列表总数
     * @param $params
     * @return int
     */
    public function getListCount($params)
    {
        //非正式员工
        $params['formal'] = StaffHikvisionModel::FORMAL_0;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['COUNT(1) as total_count']);
        $builder->from(['ocsh' => StaffHikvisionModel::class]);
        $builder->leftJoin(HrStaffInfoReadModel::class, 'hsi.staff_info_id = ocsh.staff_info_id', 'hsi');
        $builder = $this->createListWhere($builder, $params);
        $count = $builder->getQuery()->getSingleResult()->total_count;
        //print_r( $builder->getQuery()->getSql());die;
        return $count ? intval($count) : 0;
    }

    /**
     * Notes: 列表&导出，获取数据
     * @param $params
     * @return mixed
     */
    private function getListByLimit($params)
    {
        //非正式员工
        $params['formal'] = StaffHikvisionModel::FORMAL_0;

        //获取列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hsi.staff_info_id, hsi.name, hsi.sex, hsi.job_title, hsi.node_department_id, hsi.state, hsi.hire_date, hsi.leave_date, hsi.sys_store_id, ocsh.outsourcing_company_id, ocsh.hikvision_org_index_code, ocsh.hikvision_org_name, ocsh.is_sync']);
        $builder->from(['ocsh' => StaffHikvisionModel::class]);
        $builder->leftJoin(HrStaffInfoReadModel::class, 'hsi.staff_info_id = ocsh.staff_info_id', 'hsi');
        $builder = $this->createListWhere($builder, $params);
        // 加一个id的排序，防止created_at相同时排序不稳定
        $builder->orderBy('ocsh.created_at DESC,ocsh.id ASC');
        $builder->limit($params['pagesize'], ($params['page'] - 1) * $params['pagesize']);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * Notes: 导出hub外协员工列表
     * @param $params
     * @return array
     */
    public function exportList($params): array
    {
        $count = $this->getListCount($params);
        // 分批获取
        $pageSize = 500;
        $pageNum = ceil($count / $pageSize);
        $data = [];
        for ($i = 1; $i <= $pageNum; $i++) {
            $params['page'] = $i;
            $params['pagesize'] = $pageSize;
            $list = $this->getListByLimit($params);
            $data = array_merge($data, $this->formatExportData($list));
        }

        // 定义header
        $header = [
            // 工号
            'staff_info_id' => static::$t->_('staff_info_id'),
            // 员工姓名
            'staff_info_name' => static::$t->_('name'),
            // 性别
            'sex' => static::$t->_('sex'),
            // 国籍
            'nationality' => static::$t->_('nationality'),
            // 外协公司
            'backyardattendance_flashout_company' => static::$t->_('backyardattendance_flashout_company'),
            // 职位
            'job_title' => static::$t->_('job_title'),
            // 在职状态
            'staff_state' => static::$t->_('staff_state'),
            // 入职日期
            'hire_date' => static::$t->_('hire_date'),
            // 离职日期
            'leave_date' => static::$t->_('leave_date'),
            // 部门
            'department' => static::$t->_('department'),
            // 所属网点
            'hr_probation_field_sys_store_name' => static::$t->_('hr_probation_field_sys_store_name'),
            // 班次分组
            'shift_group' => static::$t->_('shift_group'),
            // 同步状态
            'sync_status' => static::$t->_('sync_status'),
        ];
        return [$header, $data];
    }

    /**
     * 生成列表where条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function createListWhere($builder, $params)
    {
        $builder->Where('hsi.formal=:formal: AND staff_type in ({staff_types:array})', ['formal' => HrStaffInfoModel::FORMAL_0, 'staff_types' => [HrStaffInfoModel::STAFF_TYPE_4, HrStaffInfoModel::STAFF_TYPE_1]]);
        if (!empty($params['company_id'])) {
            $builder->andWhere('ocsh.outsourcing_company_id=' . $params['company_id']);
        }
        if (!empty($params['start_hire_date']) && !empty($params['end_hire_date'])) {
            $builder->betweenWhere('hsi.hire_date', $params['start_hire_date'] . ' 00:00:00', $params['end_hire_date'] . ' 23:59:59');
        }
        if (!empty($params['name'])) {
            $builder->andWhere('(hsi.name LIKE :name: or hsi.staff_info_id LIKE :name: )', ['name' => '%' . $params['name'] . '%']);
        }
        if (!empty($params['sys_store_id'])) {
            $builder->andWhere('hsi.sys_store_id=:sys_store_id:', ['sys_store_id' => $params['sys_store_id']]);
        }
        if (!empty($params['state'])) {
            $builder->andWhere('hsi.state=:state:', ['state' => $params['state']]);
        }
        if (isset($params['sync_state']) && $params['sync_state'] != '') {
            $builder->andWhere('ocsh.is_sync=:is_sync:', ['is_sync' => $params['sync_state']]);
        }

        if (isset($params['formal']) && $params['formal'] != '') {
            $builder->andWhere('ocsh.formal = :hik_formal:', ['hik_formal' => $params['formal']]);
        }

        return $builder;
    }

    /**
     * 格式化列表数据
     * @param $list
     * @return mixed
     */
    public function formatListData($list)
    {
        [$nationality_list, $job_list, $outsourcing_company_list, $store_list] = $this->getExtendData($list);
        foreach ($list as $key => $val) {
            $list[$key]['sex'] = (int)$val['sex'];
            $list[$key]['sex_name'] = self::$t->_(OutSourcingStaffEnums::$sex_list[$val['sex']]);
            $list[$key]['hire_date'] = !empty($val['hire_date']) ? substr($val['hire_date'], 0, 10) : '';
            $list[$key]['leave_date'] = !empty($val['leave_date']) ? substr($val['leave_date'], 0, 10) : '';
            $list[$key]['state'] = (int)$val['state'];
            $list[$key]['state_name'] = self::$t->_(OutSourcingStaffEnums::$staff_state[$val['state']]);
            $list[$key]['nationality'] = !empty($nationality_list[$val['staff_info_id']]) ? (int)$nationality_list[$val['staff_info_id']]['value'] : 0;
            $list[$key]['nationality_name'] = !empty($nationality_list[$val['staff_info_id']]) ? self::$t->_(HrStaffItemsModel::$nationality_list[$nationality_list[$val['staff_info_id']]['value']]) : '';
            $list[$key]['outsourcing_company_id'] = (int)$val['outsourcing_company_id'];
            $list[$key]['outsourcing_company_name'] = !empty($outsourcing_company_list[$val['outsourcing_company_id']]) ? $outsourcing_company_list[$val['outsourcing_company_id']]['company_name'] : '';
            $list[$key]['job_id'] = (int)$val['job_title'];
            $list[$key]['job_name'] = !empty($job_list[$val['job_title']]) ? $job_list[$val['job_title']] : '';
            $list[$key]['sys_store_name'] = !empty($store_list[$val['sys_store_id']]) ? $store_list[$val['sys_store_id']]['name'] : '';
            $list[$key]['is_sync'] = (int)$val['is_sync'];
            $list[$key]['is_sync_name'] = self::$t->_(StaffHikvisionModel::$sync_list[$val['is_sync']]);
            $list[$key]['hikvision_org_name'] = $this->getHikvisionOrgName($val);
        }
        return $list;
    }

    /**
     * Notes: 列表和导出需要的一些公共数据
     * @param $list
     * @return array
     */
    private function getExtendData($list): array
    {
        if (empty($list)) {
            return $list;
        }
        $staff_ids = array_values(array_unique(array_column($list, 'staff_info_id')));
        $item_model = new HrStaffItemsModel();

        //国籍
        $nationality_list = $item_model->getListByStaffIds($staff_ids, HrStaffItemsModel::ITEM_NATIONALITY);
        $nationality_list = array_column($nationality_list, NULL, 'staff_info_id');
        //职位
        $job_ids = array_values(array_unique(array_column($list, 'job_title')));
        $job_list = (new HrJobTitleService())->getJobTitleMapByIds($job_ids);
        //print_r($job_list);die;
        //外协公司
        $outsourcing_company_ids = array_values(array_unique(array_column($list, 'outsourcing_company_id')));
        $outsourcing_company_list = (new OutsourcingCompanyModel())->getListByIds($outsourcing_company_ids);
        $outsourcing_company_list = array_column($outsourcing_company_list, NULL, 'id');
        //网点
        $store_ids = array_values(array_unique(array_column($list, 'sys_store_id')));
        $store_list = (new SysStoreService())->getStoreList($store_ids);
        $store_list = array_column($store_list, NULL, 'id');
        return [$nationality_list, $job_list, $outsourcing_company_list, $store_list];
    }

    /**
     * Notes: 格式话导出数据
     * @param $list
     * @return array
     */
    private function formatExportData($list): array
    {
        [$nationality_list, $job_list, $outsourcing_company_list, $store_list] = $this->getExtendData($list);
        // 导出多一个部门字段
        $sysDepIds = array_values(array_unique(array_column($list, 'node_department_id')));
        $sysDepList = (new DepartmentService())->getDepartmentInfo($sysDepIds);
        $sysDepList = array_column($sysDepList, 'name', 'id');
        $return = [];
        foreach ($list as $val) {
            // 工号
            $temp = [];
            $temp[] = $val['staff_info_id'];
            // 员工姓名
            $temp[] = $val['name'];
            // 性别
            $temp[] = self::$t->_(OutSourcingStaffEnums::$sex_list[$val['sex']]);
            // 国籍
            $temp[] = !empty($nationality_list[$val['staff_info_id']]) ? self::$t->_(HrStaffItemsModel::$nationality_list[$nationality_list[$val['staff_info_id']]['value']]) : '';;
            // 外协公司
            $temp[] = !empty($outsourcing_company_list[$val['outsourcing_company_id']]) ? $outsourcing_company_list[$val['outsourcing_company_id']]['company_name'] : '';;
            // 职位
            $temp[] = !empty($job_list[$val['job_title']]) ? $job_list[$val['job_title']] : '';
            // 在职状态
            $temp[] = self::$t->_(OutSourcingStaffEnums::$staff_state[$val['state']]);
            // 入职日期
            $temp[] = !empty($val['hire_date']) ? substr($val['hire_date'], 0, 10) : '';
            // 离职日期
            $temp[] = !empty($val['leave_date']) ? substr($val['leave_date'], 0, 10) : '';
            // 部门
            $temp[] = !empty($sysDepList[$val['node_department_id']]) ? $sysDepList[$val['node_department_id']] : '';
            // 所属网点
            $temp[] = !empty($store_list[$val['sys_store_id']]) ? $store_list[$val['sys_store_id']]['name'] : '';
            // 班次分组
            $temp[] = $this->getHikvisionOrgName($val);
            // 同步状态
            $temp['is_sync_name'] = self::$t->_(StaffHikvisionModel::$sync_list[$val['is_sync']]);
            $return[] = $temp;
        }
        return $return;
    }

    /**
     * Notes: 获取班次分组名称(未同步：都展示，已同步：在职展示，离职不展示)
     * @param $info
     * @return string
     */
    private function getHikvisionOrgName($info): string
    {
        if ($info['is_sync'] == StaffHikvisionModel::SYNC_NO) {
            return $info['hikvision_org_name'];
        }
        return (int)$info['state'] == 2 ? '' : $info['hikvision_org_name'];
    }

    /**
     * 获取外协员工详情
     * @param $params
     * @return array
     */
    public function getOneOutSourcingStaff($params)
    {
        try {
            $staff_info = (new HrStaffInfoModel())->getOneByStaffId($params['staff_info_id']);
            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException(self::$t->_('outsourcing_staff_no_outsourcing'), ErrCode::VALIDATE_ERROR);
            }

            $staff_ids = [$staff_info['staff_info_id']];
            $item_model = new HrStaffItemsModel();

            //国籍
            $nationality_list = $item_model->getListByStaffIds($staff_ids, HrStaffItemsModel::ITEM_NATIONALITY);
            $nationality_list = array_column($nationality_list, NULL, 'staff_info_id');
            //身份证正面照
            $identity_front = $item_model->getListByStaffIds($staff_ids, HrStaffItemsModel::ITEM_IDENTITY_FRONT_KEY);
            $identity_front = array_column($identity_front, NULL, 'staff_info_id');
            //部门
            $department = (new SysDepartmentService())->getDepartmentDetail($staff_info['node_department_id']);
            //网点
            $store_ids = [$staff_info['sys_store_id']];
            $store_list = (new SysStoreService())->getStoreList($store_ids);
            $store_list = array_column($store_list, NULL, 'id');
            //职位
            $job_ids = [$staff_info['job_title']];
            $job_list = (new HrJobTitleService())->getJobTitleMapByIds($job_ids);
            //外协公司
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['ocsh.outsourcing_company_id, oc.company_name, ocsh.hikvision_org_index_code, ocsh.hikvision_org_name, ocsh.face_img_path']);
            $builder->from(['ocsh' => StaffHikvisionModel::class]);
            $builder->leftJoin(OutsourcingCompanyModel::class, 'ocsh.outsourcing_company_id = oc.id', 'oc');
            $builder->Where('ocsh.staff_info_id=:staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
            $outsourcing_company = $builder->getQuery()->execute()->toArray();

            //组装数据
            $staff_info['sex'] = (int)$staff_info['sex'];
            $staff_info['sex_name'] = self::$t->_(OutSourcingStaffEnums::$sex_list[$staff_info['sex']]);
            $staff_info['hire_date'] = !empty($staff_info['hire_date']) ? substr($staff_info['hire_date'], 0, 10) : '';
            $staff_info['leave_date'] = !empty($staff_info['leave_date']) ? substr($staff_info['leave_date'], 0, 10) : '';
            $staff_info['nationality'] = !empty($nationality_list[$staff_info['staff_info_id']]) ? (int)$nationality_list[$staff_info['staff_info_id']]['value'] : 0;
            $staff_info['nationality_name'] = !empty($nationality_list[$staff_info['staff_info_id']]) ? self::$t->_(HrStaffItemsModel::$nationality_list[$nationality_list[$staff_info['staff_info_id']]['value']]) : '';
            $staff_info['sys_department_name'] = !empty($department) ? $department['name'] : '';
            $staff_info['sys_store_name'] = !empty($store_list[$staff_info['sys_store_id']]) ? $store_list[$staff_info['sys_store_id']]['name'] : '';
            $staff_info['job_title'] = (int)$staff_info['job_title'];
            $staff_info['job_name'] = !empty($job_list[$staff_info['job_title']]) ? $job_list[$staff_info['job_title']] : '';
            $staff_info['outsourcing_company_id'] = !empty($outsourcing_company[0]) ? (int)$outsourcing_company[0]['outsourcing_company_id'] : 0;
            $staff_info['outsourcing_company_name'] = !empty($outsourcing_company[0]) ? $outsourcing_company[0]['company_name'] : '';
            $staff_info['hikvision_org_index_code'] = !empty($outsourcing_company[0]) ? $outsourcing_company[0]['hikvision_org_index_code'] : '';
            $staff_info['hikvision_org_name'] = !empty($outsourcing_company[0]) ? $outsourcing_company[0]['hikvision_org_name'] : '';
            $staff_info['face_img_path'] = !empty($outsourcing_company[0]) ? $outsourcing_company[0]['face_img_path'] : '';
            $staff_info['identity_file_a'] = !empty($identity_front[$staff_info['staff_info_id']]) ? env('img_prefix', '') . $identity_front[$staff_info['staff_info_id']]['value'] : '';

            $result = [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $staff_info,
            ];
        } catch (ValidationException $e) {
            $this->logger->info('getOneOutSourcingStaff-fail-info：' . $e->getMessage());
            $result = [
                'code' => $e->getCode(),
                'msg' => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e) {
            $this->logger->error('getOneOutSourcingStaff-fail-info：' . $e->getMessage());
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }
    /**
     * 更新员工信息
     * @param $params
     * @return array
     */
    public function updateOutSourcingStaff($params)
    {
        $db = $this->getDI()->get('db_backyard');
        try {
            $staff_info = (new HrStaffInfoModel())->getOneByStaffId($params['staff_info_id']);

            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException(self::$t->_('outsourcing_staff_no_outsourcing'), ErrCode::VALIDATE_ERROR);
            }
            $hub_store_department_relation = (new SettingEnvService())->getSetVal('hub_store_department_id_relation');
            if (!empty($hub_store_department_relation)) {
                $hub_store_department_relation = json_decode($hub_store_department_relation, true);
            }
            if (empty($hub_store_department_relation) || empty($hub_store_department_relation[$params['sys_store_id']])) {
                throw new ValidationException(self::$t->_('outsourcing_company_no_store_department'),
                    ErrCode::VALIDATE_ERROR);
            }

            //获取员工海康附属信息
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision       = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);

            //截取图片地址
            $identity_file_a_url       = parse_url($params['identity_file_a']);
            $params['identity_file_a'] = ltrim($identity_file_a_url['path'], '/');

            //ai 人脸质量检测, 变更 照片，再进行 人脸质量检测（老照片 不符合 新的 质量规则）
            if($staff_hikvision['face_img_path'] != $params['face_img_path']) {
                $this->checkFaceImageQuality(trim($params['face_img_path']), (string)$params['staff_info_id']);
            }

            // 编辑离职状态的员工时 不做ai人脸查重
            if ($staff_info['state'] != HrStaffInfoModel::STATE_RESIGN) {
                // ai 人脸查重
                $this->checkFaceImageExists(trim($params['face_img_path']), (string)$params['staff_info_id']);
            }

            //2.2.3调用hris-rpc更新员工信息
            $rpc_data = [
                'staff_info_id'      => $params['staff_info_id'],
                'name'               => $params['name'],
                'sex'                => $params['sex'],
                'identity'           => $params['identity'],
                'job_title'          => strval($params['job_title']),
                'sys_store_id'       => $params['sys_store_id'],
                'company_name_ef'    => $params['outsourcing_company_name'],
                'node_department_id' => $hub_store_department_relation[$params['sys_store_id']],
                'state'              => $staff_info['state'],
                'staff_type'         => HrStaffInfoModel::STAFF_TYPE_4,
                'identity_front_key' => $params['identity_file_a'],
                'nationality'        => $params['nationality'],
                'position_category'  => [],
            ];

            if(isCountry('TH')) {
                $rpc_data['company_item_id'] = $params['outsourcing_company_id'];
            }

            $hris_rpc = new ApiClient('hris', '', 'create_hub_os_staff', self::$language);
            $hris_rpc->setParams([$rpc_data]);
            $res = $hris_rpc->execute();
            if (isset($res['code']) && $res['code'] == 1) {
                $result = [
                    'code' => ErrCode::SUCCESS,
                    'msg'  => 'Success',
                ];
                $db->begin();
                // 编辑离职状态的员工时 不做ai人脸查重
                $ai_emb = '';
                if ($staff_info['state'] != HrStaffInfoModel::STATE_RESIGN) {
                    // 向ai同步数据
                    $face_image_update_ret = $this->faceImageUpdate($params['face_img_path'],
                        (int)$params['staff_info_id']);
                    if (is_bool($face_image_update_ret) && !$face_image_update_ret) {
                        $db->rollback();
                        $result['code'] = ErrCode::FAIL;
                        $result['msg']  = 'please try again';
                        return $result;
                    }
                    $ai_emb = $face_image_update_ret;
                }

                //更新海康附属信息
                $update_data = [
                    'outsourcing_company_id'   => $params['outsourcing_company_id'],
                    'face_img_path'            => $params['face_img_path'],
                    'hikvision_org_index_code' => $params['hikvision_org_index_code'],
                    'hikvision_org_name'       => $params['hikvision_org_name'],
                    'is_sync'                  => StaffHikvisionModel::SYNC_NO,
                    'ai_emb'                   => $ai_emb,
                ];
                if ($db->updateAsDict('staff_hikvision', $update_data,
                    'staff_info_id='.$params['staff_info_id'])) {
                    //写入log
                    $log_data = $staff_hikvision;
                    unset($log_data['id'], $log_data['created_at'], $log_data['updated_at']);
                    $log_data['outsourcing_company_id']   = $params['outsourcing_company_id'];
                    $log_data['face_img_path']            = $params['face_img_path'];
                    $log_data['hikvision_org_index_code'] = $params['hikvision_org_index_code'];
                    $log_data['hikvision_org_name']       = $params['hikvision_org_name'];
                    $log_data['is_sync']                  = StaffHikvisionModel::SYNC_NO;
                    $log_data['ai_emb']                   = $ai_emb;
                    $db->insertAsDict('staff_hikvision_log', $log_data);
                    $db->commit();
                } else {
                    $db->rollback();
                    $result = [
                        'code' => ErrCode::FAIL,
                        'msg'  => 'Fail',
                    ];
                }
            } else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg'  => $res['msg'],
                ];
            }
        } catch (ValidationException $e) {
            $this->logger->info('updateOutSourcingStaff-fail-info：'.json_encode($params));
            $result = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('updateOutSourcingStaffState-fail：'.json_encode($params)).$e->getMessage();
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }

    /**
     * 更新员工在职状态
     * @param $params
     * @param $user_info
     * @return array
     */
    public function updateOutSourcingStaffState($params, $user_info)
    {
        $db = $this->getDI()->get('db_backyard');
        try {
            $staff_info = (new HrStaffInfoModel())->getOneByStaffId($params['staff_info_id']);
            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException(self::$t->_('outsourcing_staff_no_outsourcing'), ErrCode::VALIDATE_ERROR);
            }
            if ($params['state'] == HrStaffInfoModel::STATE_ON_JOB && $staff_info['state'] == $params['state']) {
                throw new ValidationException(self::$t->_('outsourcing_staff_on_job'), ErrCode::VALIDATE_ERROR);
            }
            if ($params['state'] == HrStaffInfoModel::STATE_RESIGN && $staff_info['state'] == $params['state']) {
                throw new ValidationException(self::$t->_('outsourcing_staff_resign'), ErrCode::VALIDATE_ERROR);
            }
            //获取员工海康附属信息
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision       = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);

            if ($params['state'] == HrStaffInfoModel::STATE_ON_JOB) {
                // 恢复在职时 需要走人脸查重接口
                $this->checkFaceImageExists(trim($staff_hikvision['face_img_path']), (string)$params['staff_info_id']);
            }

            //调用hris-rpc更新员工在职状态
            $rpc_data = [
                'staff_info_id' => $params['staff_info_id'],
                'state'         => $params['state'],
            ];
            if ($params['state'] == HrStaffInfoModel::STATE_ON_JOB) {
                $rpc_data['hire_date'] = date('Y-m-d');
            } elseif ($params['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $rpc_data['leave_date'] = date('Y-m-d');
            }
            $hris_rpc = new ApiClient('hris', '', 'update_hub_os_staff_state', self::$language);
            $hris_rpc->setParams([$rpc_data]);
            $res = $hris_rpc->execute();

            $result = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
            ];
            if (isset($res['code']) && $res['code'] == 1) {
                $db->begin();
                if ($params['state'] == HrStaffInfoModel::STATE_ON_JOB) {
                    // 离职变为在职
                    $update_data['deleted'] = StaffHikvisionModel::DELETED_NO;
                    $log_data               = $staff_hikvision;
                    $log_data['deleted']    = StaffHikvisionModel::DELETED_NO;
                    unset($log_data['id'], $log_data['created_at'], $log_data['updated_at']);
                    // 同步ai 新增底片
                    $ret = $this->faceImageUpdate($staff_hikvision['face_img_path'], $params['staff_info_id']);
                    if (is_bool($ret) && !$ret) {
                        $db->rollback();
                        $result['code'] = ErrCode::FAIL;
                        $result['msg']  = 'please try again';
                        return $result;
                    }
                    $update_data['ai_emb']  = $ret;
                    $log_data['ai_emb']     = $ret;
                } elseif ($params['state'] == HrStaffInfoModel::STATE_RESIGN) {
                    // 在职变离职
                    $update_data['deleted'] = StaffHikvisionModel::DELETED_YES;
                    $log_data               = $staff_hikvision;
                    $log_data['deleted']    = StaffHikvisionModel::DELETED_YES;
                    unset($log_data['id'], $log_data['created_at'], $log_data['updated_at']);
                    // 同步ai清除底片
                    $ret = $this->faceImageDelete((string) $params['staff_info_id']);
                    if (!$ret) {
                        $db->rollback();
                        $result['code'] = ErrCode::FAIL;
                        $result['msg'] = 'please try again';
                        return  $result;
                    }
                    $update_data['ai_emb']  = '';
                    $log_data['ai_emb']     = '';
                }
                $update_data['is_sync']     = $log_data['is_sync'] = StaffHikvisionModel::SYNC_NO;
                $update_data['editor_type'] = $log_data['editor_type'] = StaffHikvisionModel::EDITOR_TYPE_2;
                $update_data['editor_id']   = $log_data['editor_id'] = $user_info['id'];

                if ($db->updateAsDict('staff_hikvision', $update_data,
                    'staff_info_id='.$params['staff_info_id'])) {
                    $db->insertAsDict('staff_hikvision_log', $log_data);
                    $db->commit();
                } else {
                    $db->rollback();
                    $result = [
                        'code' => ErrCode::FAIL,
                        'msg'  => 'Fail',
                    ];
                }
            } else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg'  => 'Fail',
                ];
            }
        } catch (ValidationException $e) {
            $this->logger->info('updateOutSourcingStaffState-fail-info：'.json_encode($params));
            $result = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('updateOutSourcingStaffState-fail：'.json_encode($params)).$e->getMessage();
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }

    /**
     * 人脸质量检测
     * @param $img
     * @param $staff_info_id
     * @return void
     * @throws ValidationException
     */
    private function checkFaceImageQuality($img, $staff_info_id)
    {
        $imgArrayUrl = explode('?', $img);//地址有参数
        $imgArray = explode('.', $imgArrayUrl[0]);
        $file_type = end($imgArray);
        if(!in_array(strtolower($file_type), ['jpg', 'jpeg', 'png'])) {
            throw new ValidationException(self::$t->_('os_staff_face_check_file_type_error'));
        }
        $params = ['url' => $img, 'face_attributes' => 'quality,mask', 'max_face_num' => 2, 'min_pupillary_distance' => OutSourcingStaffEnums::FACE_IMG_PUPILLARY_DISTANCE];
        $params = http_build_query($params);
        $result = AiServer::getInstance()->setConfig(AiServer::IDENTIFY_ANALYZE_FACE_QUALITY)->send($params,
            $staff_info_id);
        //无返回结果
        $this->logger->write_log(['checkFaceImageQualityResult' => $result], 'info');

        if (empty($result)) {
            $this->logger->write_log("quality check error:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            throw new ValidationException(self::$t->_('os_staff_face_check_msg_1'));//接口调用失败
        }
        if (isset($result['error'])) {
            $this->logger->write_log("quality check error:".$result['error']['message'], "info");
            throw new ValidationException(self::$t->_('os_staff_face_check_msg_2'));//识别失败，或者没有检测到人脸
        }
        if (isset($result['result'])) {
            if ($result['result']['face_num'] > 1) {
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_3'));//检测到多个人脸
            }

            if ($result['result']['face_list'][0]['code'] == OutSourcingStaffEnums::FACE_WITH_MASK_DETECTED){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_mask'));//请勿佩戴口罩
            }

            if ($result['result']['face_list'][0]['code'] == OutSourcingStaffEnums::BRIGHTNESS_NOT_ACCEPTABLE){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_brightness'));//亮度不合格，图片太亮或太暗
            }

            if (in_array($result['result']['face_list'][0]['code'], [OutSourcingStaffEnums::LOW_IMAGE_QUALITY, OutSourcingStaffEnums::SHARPNESS_NOT_ACCEPTABLE])){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_low'));//图像质量评分太低
            }

            if ($result['result']['face_list'][0]['code'] == OutSourcingStaffEnums::FACE_INCOMPLETE_DETECTED){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_incomplete'));//图片太模糊
            }

            //瞳间距
            if ($result['result']['face_list'][0]['code'] == OutSourcingStaffEnums::FACE_PUPILLARY_DETECTED){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_pupillary_distance'));//
            }

            if ($result['result']['face_list'][0]['code'] > OutSourcingStaffEnums::HIGH_QUALITY && $result['result']['face_list'][0]['code'] != OutSourcingStaffEnums::SHARPNESS_NOT_ACCEPTABLE) {
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_4'));//人脸质量不合格
            }

            //图片高度。 640 - 1920
            if ($result['result']['image_height'] < OutSourcingStaffEnums::FACE_IMG_FILE_HEIGHT_MIN || $result['result']['image_height'] > OutSourcingStaffEnums::FACE_IMG_FILE_HEIGHT_MAX){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_img_height_limit'));
            }

            //图片宽度。480 - 1080
            if ($result['result']['image_width'] < OutSourcingStaffEnums::FACE_IMG_FILE_WIDTH_MIN || $result['result']['image_width'] > OutSourcingStaffEnums::FACE_IMG_FILE_WIDTH_MAX){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_img_width_limit'));
            }
        }

        if (isset($result['debug'])){
            //图片大小。最小 60kb
            if ($result['debug']['image_size'] < OutSourcingStaffEnums::FACE_IMG_FILE_SIZE_MIN){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_img_size_min'));
            }

            //图片大小。最大 200kb
            if ($result['debug']['image_size'] > OutSourcingStaffEnums::FACE_IMG_FILE_SIZE_MAX){
                throw new ValidationException(self::$t->_('os_staff_face_check_msg_img_size_max'));
            }
        }
    }

    /**
     * 人脸查重接口
     * @param string $img
     * @param string $staff_info_id
     * @return void
     * @throws ValidationException
     */
    public function checkFaceImageExists(string $img, string $staff_info_id)
    {
        $params = ['url' => $img];
        $result = AiServer::getInstance()->setConfig(AiServer::IDENTIFY_FACE_CHECK)->sendPostRequest($params,
            $staff_info_id);
        //无返回结果
        if (empty($result)) {
            $this->logger->write_log("checkFaceImageExists:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            throw new ValidationException(self::$t->_('os_staff_face_check_msg_1'));//接口调用失败
        }
        if ($result['status'] != 'OK') {
            $this->logger->write_log("checkFaceImageExists:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            throw new ValidationException(self::$t->_('os_staff_face_check_msg_1'));//接口调用失败
        }

        // 命中了重复的数据
        if (($result['result']['flash_hr_exist'] === true) && !empty($result['result']['person_id']) && ($result['result']['person_id'] != $staff_info_id)) {
            $this->logger->write_log("checkFaceImageExists:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            //操作失败，人脸信息与 %staff_info_id% 重复
            throw new ValidationException(self::$t->_('os_staff_face_check_msg_5',
                ['staff_info_id' => $result['result']['person_id']]));
        }
    }

    /**
     * 更新信息时 向ai同步
     * @param string $img
     * @param int $staff_info_id
     * @return false|string
     * @throws ValidationException
     */
    public function faceImageUpdate(string $img, int $staff_info_id)
    {
        $params = ['url' => $img, 'person_id' => $staff_info_id];
        $result = AiServer::getInstance()->setConfig(AiServer::IDENTIFY_FACE_DATA_SYNCHRONIZATION)->sendPostRequest($params,
            $staff_info_id);
        if ($result['status'] == 'ERROR') {
            $this->logger->write_log("faceImageUpdate - error:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            return false;
        }

        if ($result['status'] == 'OK') {
            return json_encode(['emb' => $result['result']['emb'], 'version' => $result['result']['version']],
                JSON_UNESCAPED_UNICODE);
        }
        return false;
    }

    /**
     * 删除信息时 向ai同步
     * @param string $staff_info_id
     * @return bool|false
     * @throws ValidationException
     */
    public function faceImageDelete(string $staff_info_id): bool
    {
        $params = ['person_id' => $staff_info_id];
        $result = AiServer::getInstance()->setConfig(AiServer::IDENTIFY_FACE_DATA_SYNCHRONIZATION)->sendDeleteRequest($params,
            $staff_info_id);

        if ($result['status'] != 'OK') {
            $this->logger->write_log("faceImageDelete - error:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            return false;
        }
        return true;
    }

    /**
     * 设置外协员工班组
     * @param $params
     * @param $is_sync
     * @return array
     */
    public function updateHikOrg($params, $is_sync = StaffHikvisionModel::SYNC_NO)
    {
        $db = $this->getDI()->get('db_backyard');
        try {
            //获取员工海康附属信息
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);

            $db->begin();
            //更新员工海康附属信息
            $update_data = [
                'hikvision_org_index_code' => $params['hikvision_org_index_code'],
                'hikvision_org_name' => $params['hikvision_org_name'],
                'is_sync' => StaffHikvisionModel::SYNC_NO,
            ];
            if ($db->updateAsDict('staff_hikvision', $update_data, 'staff_info_id=' . $params['staff_info_id'])) {
                //写入日志表
                $log_data = $staff_hikvision;
                unset($log_data['id'], $log_data['created_at'], $log_data['updated_at']);
                $log_data['hikvision_org_index_code'] = $params['hikvision_org_index_code'];
                $log_data['hikvision_org_name'] = $params['hikvision_org_name'];
                $log_data['is_sync'] = $is_sync;
                $db->insertAsDict('staff_hikvision_log', $log_data);
                $db->commit();
                $result = [
                    'code' => ErrCode::SUCCESS,
                    'msg' => 'Success',
                ];
            } else {
                $db->rollback();
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg' => 'Fail',
                ];
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('updateHikOrg-fail：' . json_encode($params)) . $e->getMessage();
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }

    /**
     * Notes: 批量更新HUB外协员工班组
     * @param array $params
     * @return bool
     * @throws BusinessException
     */
    public function batchUpdateHikOrg(array $params): bool
    {
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        $staffHikvisionModel = new StaffHikvisionModel();
        $staffHikvisions = $staffHikvisionModel->getListModelByStaffIds($params['staff_info_ids']);

        foreach ($staffHikvisions as $staffHikvision) {
            // 更新主表
            /**
             * @var $staffHikvision StaffHikvisionModel
             */
            $staffHikvision->hikvision_org_index_code = $params['hikvision_org_index_code'];
            $staffHikvision->hikvision_org_name = $params['hikvision_org_name'];
            $staffHikvision->is_sync = StaffHikvisionModel::SYNC_NO;
            $res = $staffHikvision->save();
            if (!$res) {
                $this->logger->error('批量更新HUB外协员工班组失败：' . json_encode($params) . $staffHikvision->getErrorMessagesJson());
                $db->rollback();
                throw new BusinessException(self::$t->_('db_update_fail'));
            }

            // 日志添加数据
            $logModel = new StaffHikvisionLogModel();
            $logModel->staff_info_id = $staffHikvision->staff_info_id;
            $logModel->outsourcing_company_id = $staffHikvision->outsourcing_company_id;
            $logModel->hikvision_person_id = $staffHikvision->hikvision_person_id;
            $logModel->hikvision_face_id = $staffHikvision->hikvision_face_id;
            $logModel->hikvision_pic_uri = $staffHikvision->hikvision_pic_uri;
            $logModel->face_img_path = $staffHikvision->face_img_path;
            $logModel->editor_id = $staffHikvision->editor_id;
            $logModel->editor_type = $staffHikvision->editor_type;
            $logModel->hikvision_org_index_code = $params['hikvision_org_index_code'];
            $logModel->hikvision_org_name = $params['hikvision_org_name'];
            $logModel->is_sync = StaffHikvisionModel::SYNC_NO;
            $res = $logModel->create();
            if (!$res) {
                $this->logger->error('批量更新HUB外协员工班组添加日志失败：' . json_encode($params) . ',error:' . $logModel->getErrorMessagesJson());
                $db->rollback();
                throw new BusinessException(self::$t->_('db_update_fail'));
            }
        }
        $db->commit();
        return true;
    }

    /**
     * 创建同步海康任务
     * @param $params
     * @param $user_info
     * @return array
     */
    public function syncHikTask($params, $user_info)
    {
        try {
            $staff_hik_list = (new StaffHikvisionModel())->getListByStaffIds($params['staff_info_ids']);
            //没有对应的外协员工
            if (empty($staff_hik_list)) {
                throw new ValidationException(self::$t->_('outsourcing_staff_is_null'), ErrCode::VALIDATE_ERROR);
            }
            //如果只选择了一个员工且未设置分组
            if (count($staff_hik_list) == 1 && empty($staff_hik_list[0]['hikvision_org_index_code'])) {
                throw new ValidationException(self::$t->_('outsourcing_sync_org_is_null'), ErrCode::VALIDATE_ERROR);
            }
            //如果只选择了一个员工且已同步
            if (count($staff_hik_list) == 1 && $staff_hik_list[0]['is_sync'] == StaffHikvisionModel::SYNC_YES) {
                throw new ValidationException(self::$t->_('outsourcing_sync_yes'), ErrCode::VALIDATE_ERROR);
            }
            //有员工未设置班组
            $task_data = [];
            foreach ($staff_hik_list as $staff_hik) {
                if (empty($staff_hik['hikvision_org_index_code'])) {
                    throw new ValidationException(self::$t->_('outsourcing_sync_orgs_is_null'), ErrCode::VALIDATE_ERROR);
                }
                if ($staff_hik['is_sync'] == StaffHikvisionModel::SYNC_NO) {
                    $task_data[] = [
                        'staff_hikvision_id' => $staff_hik['id'],
                        'staff_info_id' => $staff_hik['staff_info_id'],
                        'creater_id' => $user_info['id'],
                        'creater_name' => $user_info['name'],
                    ];
                }
            }
            if (empty($task_data)) {
                throw new ValidationException(self::$t->_('outsourcing_sync_staff_is_null'), ErrCode::VALIDATE_ERROR);
            }

            //未同步的数据中，是否有，已创建未执行或进行中的任务
            $noFixData = [];//没有处理的数据
            if($task_data) {
                $staffIds = array_column($task_data, 'staff_info_id');
                $staffInfo = (new HikvisionSyncTaskModel())->getListByStaffIds($staffIds);
                if($staffInfo) {
                    $noFixData = array_column($staffInfo, 'staff_info_id');
                }
            }

            //去除正在进行中或未执行的任务。
            foreach ($task_data as $key => $value) {
                if(in_array($value['staff_info_id'], $noFixData)) {
                    unset($task_data[$key]);
                }
            }

            if(empty($task_data)) {
                throw new ValidationException(self::$t->_('outsourcing_sync_staff_is_pending'), ErrCode::VALIDATE_ERROR);

            }

            //添加同步任务
            $model = new BaseModel();
            if ($model->table_batch_insert($task_data, 'db_backyard', 'hikvision_sync_task')) {
                $result = [
                    'code' => ErrCode::SUCCESS,
                    'msg' => 'Success',
                ];
            } else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg' => 'Fail',
                ];
            }
        } catch (ValidationException $e) {
            $this->logger->info('syncHikTask-fail-info：' . json_encode($params));
            $result = [
                'code' => $e->getCode(),
                'msg' => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e) {
            $this->logger->error('syncHikTask-fail：' . json_encode($params)) . $e->getMessage();
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }

    /**
     * 获取海康同步列表
     * @param $params
     * @return array
     */
    public function syncHikResult($params)
    {
        $result = [
            'code' => ErrCode::SUCCESS,
            'msg' => 'Success',
            'data' => [
                'count' => 0,
                'list' => [],
            ],
        ];
        try {
            $task_model = new HikvisionSyncTaskModel();
            $count = $task_model->getListCount();
            if ($count <= 0) {
                return $result;
            }
            $list = $task_model->getList($params['page'], $params['pagesize']);
            foreach ($list as $key => $val) {
                $list[$key]['status_text'] = self::$t->_(HikvisionSyncTaskModel::$status[$val['status']]);
                if ($val['fail_reason'] > 0) {
                    $list[$key]['fail_reason_text'] = self::$t->_(HikvisionSyncTaskModel::$fail_reason[$val['fail_reason']]) . ';' . $val['hik_error_info'];
                }
                $list[$key]['created_at'] = date("Y-m-d H:i:s", strtotime($val['created_at']) + ($this->timeOffset) * 3600);
                $list[$key]['updated_at'] = date("Y-m-d H:i:s", strtotime($val['updated_at']) + ($this->timeOffset) * 3600);
            }
            $result['data']['count'] = intval($count);
            $result['data']['list'] = $list;
        } catch (\Exception $e) {
            $this->logger->error('syncHikResult-fail：' . json_encode($params)) . $e->getMessage();
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }

    /**
     * 获取hik数据
     * @return mixed
     * @throws \Exception
     */
    public function getHikOrgListInfo()
    {
        try {
            $key  = self::REDIS_ORG_CACHE;
            $redis = $this->getDI()->get('redis');
            $list = $redis->get($key);
            if(!$list || empty($list) || $list == 'null') {
                //海康班组列表接口
                $hik_client = new HikvisionClient();
                $post_data = ['pageNo' => 1, 'pageSize' => 1000];
                $hik_org_list = $hik_client->getOrgTreeData($post_data, $hik_client->hik_org_list_url);
                $list = array_values($hik_org_list[0]);
                $redis->set($key, json_encode($list, JSON_UNESCAPED_UNICODE), 1800);
            } else {
                $list = json_decode($list, true);
            }

            $data['hik_org_list'] = $list;
            return $data;
        } catch (\Exception $e) {
            $this->logger->write_log("获取海康班组组织列表信息异常: " . $e->getMessage(),'notice');
            throw $e;
        }
    }

    /**
     * 更新hik班组信息
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function hikOrgUpdateInfo($params)
    {
        try {
            //海康班组更新接口
            $hik_client = new HikvisionClient();
            $result = $hik_client->execute($params, $hik_client->hik_org_update_url);
            if(isset($result['code']) && !empty($result['code'])) {
                throw new ValidationException($result['msg']);
            }

            if(isset($result['code']) && $result['code'] == 0) {
                //更新名字，将使用中的班组名称同步修改
                $db = $this->getDI()->get('db_backyard');
                $update_data = ['hikvision_org_name' => $params['orgName']];
                $db->updateAsDict('staff_hikvision', $update_data, ["conditions" => "hikvision_org_index_code= ?", 'bind' => $params['orgIndexCode']]);

                $redis = $this->getDI()->get('redis');
                $list = $redis->get(self::REDIS_ORG_CACHE);
                if($list) {
                    $redis->del(self::REDIS_ORG_CACHE);
                }
                return [];
            }
            $this->logger->write_log("更新海康班组组织信息失败: " . json_encode($result, JSON_UNESCAPED_UNICODE),'notice');
            throw new ValidationException(self::$t->_('request_timeout'));

        } catch (\Exception $e) {
            $this->logger->write_log("更新海康班组组织信息异常: " . $e->getMessage(),'notice');
            throw $e;
        }
    }

    /**
     * 添加hik班组信息
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function hikOrgAddInfo($params)
    {
        try {
            //海康班组更新接口
            $hik_client = new HikvisionClient();
            $postData = [$params];
            $result = $hik_client->execute($postData, $hik_client->hik_org_add_url);

            if(isset($result['code']) && !empty($result['code'])) {
                throw new ValidationException($result['msg']);
            }

            if(isset($result['code']) && $result['code'] == 0 && empty($result['data']['failures']) && !empty($result['data']['successes'])) {
                $redis = $this->getDI()->get('redis');
                $list = $redis->get(self::REDIS_ORG_CACHE);
                if($list) {
                    $redis->del(self::REDIS_ORG_CACHE);
                }
                return $result['data']['successes']['orgIndexCode'];
            }
            $this->logger->write_log("添加海康班组组织信息失败: " . json_encode($result, JSON_UNESCAPED_UNICODE),'notice');
            if(isset($result['data']) && !empty($result['data']['failures'])) {
                throw new ValidationException($result['data']['failures'][0]['message']);//抛出失败原因
            }
            throw new ValidationException(self::$t->_('request_timeout'));

        } catch (\Exception $e) {
            $this->logger->write_log("添加海康班组组织信息异常: " . $e->getMessage(),'notice');
            throw $e;
        }
    }

    /**
     * 删除hik班组信息
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function hikOrgDeleteInfo($params)
    {
        $db = $this->getDI()->get('db_backyard');
        try {
            $db->begin();
            //海康班组更新接口
            $hik_client = new HikvisionClient();
            $postData = ['indexCodes' => [$params['indexCode']]];
            $result = $hik_client->execute($postData, $hik_client->hik_org_delete_url);

            if(isset($result['code']) && !empty($result['code'])) {
                throw new ValidationException($result['msg']);
            }
            $this->logger->write_log("删除海康班组组织信息params: " . json_encode($params, JSON_UNESCAPED_UNICODE),'info');


            if(isset($result['code']) && $result['code'] == 0 && empty($result['data'])) {
                $staffInfo = StaffHikvisionModel::find([
                    'columns' => 'staff_info_id',
                    'conditions' => 'hikvision_org_index_code = :index_code: and formal = :formal: ',
                    'bind' => [
                        'index_code' => $params['indexCode'],
                        'formal'     => StaffHikvisionModel::FORMAL_0,
                    ],
                ])->toArray();

                if($staffInfo) {
                    (new BaseModel())->table_batch_insert($staffInfo, 'db_backyard', 'outsourcing_company_staff_leave');
                    //删除成功，则将所有有此班组信息的员工，置空
                    $update_data = ['hikvision_org_index_code' => '', 'hikvision_org_name' => '', 'is_sync' => 1];
                    $db->updateAsDict('staff_hikvision', $update_data, ["conditions" => "hikvision_org_index_code= ?", 'bind' => $params['indexCode']]);
                }

                //清空缓存
                $redis = $this->getDI()->get('redis');
                $list = $redis->get(self::REDIS_ORG_CACHE);
                if($list) {
                    $redis->del(self::REDIS_ORG_CACHE);
                }
                $db->commit();
                return $result['data'];
            }
            if(isset($result['data']) && !empty($result['data'])) {
                $this->logger->write_log("删除海康班组组织信息失败: " . json_encode($result, JSON_UNESCAPED_UNICODE),'notice');
                throw new ValidationException($result['data'][0]['msg']);//抛出失败原因
            }
            throw new ValidationException(self::$t->_('request_timeout'));
        } catch (\Exception $e) {
            $db->rollback();

            $this->logger->write_log("删除海康班组组织信息异常: " . $e->getMessage(),'notice');
            throw $e;
        }
    }


    /**
     * 员工指定时间区间内的有效工单次数
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function dealStaffStoreOrderData($params): bool
    {
        //获取指定区间的工单与员工数据
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'COUNT(1) as use_num',
            'od.staff_info_id',
            'o.store_id',
            " '{$params['start_date']}' as start_date",
            "'{$params['end_date']}' as end_date",
        ]);
        $builder->from(['o' => HrOutsourcingOrderModel::class]);
        $builder->leftJoin(HrOutsourcingOrderDetailModel::class, 'o.serial_no = od.serial_no', 'od');
        $builder->where('o.employment_date >= :start_date: and o.employment_date <= :end_date: and o.status in (2,3) and o.out_company_id > 0 and od.is_del = 0 and od.is_effective = 1');
        $builder->groupBy('od.staff_info_id,o.store_id');
        $data = $builder->getQuery()->execute(['start_date' => $params['start_date'], 'end_date' => $params['end_date']])->toArray();

        if ($data) {
            echo 'num '.count($data);
            $db = OutsourcingStaffStoreOrderNumModel::beginTransaction($this);
            //清除数据
            $table = (new OutsourcingStaffStoreOrderNumModel())->getSource();
            $this->getDI()->get(BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME)->execute("delete from {$table}");
            $data = array_chunk($data, 200);
            foreach ($data as $datum) {
                (new OutsourcingStaffStoreOrderNumModel())->batch_insert($datum);
            }
            $db->commit();
        }
        return true;
    }

    /**
     * 将新增员工 需要同步hik 的信息 写入redis
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function syncToHikData($params)
    {
        if(empty($params)) {
            throw new ValidationException('params error');
        }
        $params['num'] = 0;//重试次数
        $content = json_encode($params, JSON_UNESCAPED_UNICODE);

        $redis = $this->getDI()->get("redis");
        $res = $redis->lpush(HubOutSourcingStaffEnums::SYNC_TO_HIK_STAFF, $content);

        if(!$res) {
            $this->getDi()->get('logger')->write_log(['syncToHikData' => $params], "notice");
            throw new ValidationException(self::$t->_('server_error') . ": syncToHikData redis lpush 失败");
        }

        return true;
    }

    public function getAllHikOrgInfo()
    {
        //海康班组列表接口
        $hik_client   = new HikvisionClient();
        $post_data    = ['pageNo' => 1, 'pageSize' => 1000];
        $hik_org_list = [];
        $retry_num = 0;
        while (true) {
            $result = $hik_client->getOrgList($post_data, $hik_client->hik_org_list_url);
            if (!empty($result['data']['list'])) {
                $hik_org_list = $result['data']['list'];
            } else {
                $retry_num++;
                if ($retry_num < 3) {
                    sleep(20);
                    continue;
                }
            }
            break;
        }


        return $hik_org_list;
    }

    /**
     * 查找 一级组织下，outsource 和 security 组织信息
     * @param $hik_org_list
     * @param $parentOrg
     * @return mixed
     */
    public function getChildOrg($hik_org_list, $parentOrg)
    {
        foreach ($hik_org_list as $oneOrg) {
            if($oneOrg['parentOrgIndexCode'] == $parentOrg['orgIndexCode'] && strtolower($oneOrg['orgName']) == 'outsource') {
                $parentOrg['sub']['outsource'] = $oneOrg;
            }

            if($oneOrg['parentOrgIndexCode'] == $parentOrg['orgIndexCode'] && strtolower($oneOrg['orgName']) == 'security') {
                $parentOrg['sub']['security'] = $oneOrg;
            }
        }

        return $parentOrg;
    }

    /**
     * 将hik 组织信息 设置到 缓存中
     * 10分钟执行一次
     * @return bool
     */
    public function setCacheOrgInfo()
    {
        $this->logger->info('getAllHikOrgInfo setCacheOrgInfo start');
        $hik_org_list = $this->getAllHikOrgInfo();
        if(empty($hik_org_list)) {
            $this->logger->notice('海康 组织架构 getCacheOrgInfo 1级缓存过期,接口拉取数据失败！！');
            return false;
        }

        $redis = $this->getDI()->get("redis");

        $hik_org_list_json = json_encode($hik_org_list, JSON_UNESCAPED_UNICODE);

        $redis->set(self::REDIS_HIK_ORG_CACHE_2, $hik_org_list_json, 864000);//缓存1天

        $redis->set(self::REDIS_HIK_ORG_CACHE_1, $hik_org_list_json, 600);//缓存10分钟

        return true;
    }

    /**
     * 从 缓存中，获取HIK 组织信息
     * @return mixed
     */
    public function getCacheOrgInfo()
    {
        $redis = $this->getDI()->get("redis");

        $orgInfo = $redis->get(self::REDIS_HIK_ORG_CACHE_1);//缓存10分钟
        if(empty($orgInfo)) {
            //如果 1级缓存 没有，则重新获取数据 并设置 。
            $this->setCacheOrgInfo();
            $orgInfo = $redis->get(self::REDIS_HIK_ORG_CACHE_2);//缓存1天
        }
        return json_decode($orgInfo, true);
    }

}