<?php


namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\RedisListEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HireTypeImportListModel;
use App\Models\backyard\HoldManageBatchReleaseDetailModel;
use App\Models\backyard\HoldManageBatchReleaseModel;
use App\Models\backyard\HrImportStaffSupportExcelModel;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\LeaveManagerModel;
use App\Models\backyard\SettingEnvModel as BySettingEnvModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\HoldHistoryModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SettingEnvModel AS SettingEnvModelBy;
use App\Repository\HoldStaffManageRepository;
use Exception;
use HoldTask;
use OSS\Core\OssException;
use PDO;

class HoldManageService extends BaseService
{
    const HOLD_MANAGER_OSS_PATH = 'HOLD_MANAGER';
    protected $holdAdvanceResignPosition = [];
    protected $holdAdvanceResignPositionDays = 30;
    public $staffInfo;
    public $masterStaff = 0;//记录当前子账号信息的主账号
    //hold原因
    public static $hold_reasons = [
        '1'     => 'unable_to_confirm_employment', //无法确认是否在职，无工作记录ิ
        '2'     => '', //[该类型已被拆分，已废弃]离职手续不齐全或有公共财产未归还
        '3'     => 'fights_with_employees', //发生与员工/客户争执打架斗殴的行为
        '4'     => 'bad_for_the_company', //散布谣言或对公司造成不利影响
        '5'     => 'suspicion_of_corruption/theft_of_public_property',  //怀疑贪污腐败/偷窃公有财物
        '6'     => 'violation_of_law',  //违法乱纪，违反法律法规
        '7'     => 'other', //其他
        '8'     => 'off_3_days', //连续旷工三天以上
        '9'     => 'fail_to_submit_public_funds', //未缴纳公款
        '10'    => 'incomplete_resignation_procedures', //离职手续不全
        '11'    => 'fail_to_return_public_property', //公共财产未归还
        '12'    => 'work_for_lalamove', //工作时间跑 lalamove及其他公司的兼职
        '13'    => 'report_false_mileage', //虚报里程
        '14'    => 'corruption/theft_of_public_property', //贪污腐败/偷窃公有财务
        '15'    => 'fail_to_submit_public_funds_on_time', //未按时缴纳公款
        '16'    => 'less_than_12_days', //未通过试用期后离职且当月出勤少于12天
        '17'    => 'off_8_days', //请假和旷工超过8天
        '18'    => 'received_warning_letters', //收到警告书
        '19'    => 'false_finished_del_v1', //虚假妥投
        '20'    => 'provide_false_information', // 向公司提供虚假信息（待确认）
        '21'    => 'investigate_the_impersonation', // 调查冒名顶替
    ];

    // "薪酬管理员"
    const POSITION_SAlALY_MANAGE = 42;

    /**
     * 区域列表
     * @var array
     */
    public static $manage_areas = [
        1 => 'B', //BKK
        2 => 'CE', //Cen+East
        3 => 'CW', //Cen+West
        4 => 'N', //North
        5 => 'NE', //North East
        6 => 'S', //South
        7 => 'C', //Cen
    ];

    /**
     * hold 来源
     * @var array
     */
    public static $hold_source = [
        1 => 'add_new_hold_case',               //新增
        2 => 'HRIS',                            //
        3 => 'stay_away_from_work',             //连续旷工
        4 => 'fail_to_submit_public_funds',     //未缴纳公款
        5 => 'apply_for_resignation_in_by',     //BY申请离职
        6 => 'leave_source_6',                  //批量导入
        7 => 'warning_menu',                    //电子警告书
        8 => 'criminal_record',                 //犯罪记录
        9 => 'hold_source_9',                   //试用期未通过员工
        10 => 'hold_source_10',                 //EHS贪污案件
        11 => 'hold_source_11',                 //合同到期
        13 => 'hold_source_13',                 //公司解约个人代理
        14 => 'hold_source_14',                 //人脸黑名单
        HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT => 'hold_source_15',                 //停职申请
    ];

    const HOLD_SOURCE_STAY_AWAY_FROM_WORK = 3;//hold来源 连续旷工

    /**
     * 处理状态(1:未处理,2:处理中,3:已处理)
     * @var array
     */
    public static $handle_progress = [
        1 => 'un_deal',
        2 => 'difficulty_delivery_state_processing',
        3 => 'csdashboard_datatable_field_18',
    ];

    const HANDLE_PROGRESS_UNPROCESSED = 1;//未处理
    const HANDLE_PROGRESS_PROCESSING = 2;//处理中
    const HANDLE_PROGRESS_PROCESSED = 3;//已处理

    /**
     * 处理意见
     * @var array
     */
    public static $handle_opinion = [
        1 => 'suspend',
        2 => 'dimission',
        3 => 'expel',
        4 => 'warning_letter',
        5 => 'verbal_warning_received',
        6 => 'no_punishment',
        7 => 'deduct_salary',
        8 => 'deduct_incentive',
        9 => 'deduct_by_stages',
    ];

    /**
     * 处理人列表
     * @var array
     */
    public static $handle_people = [
        1 => 'HRBP_ER',
        2 => 'HRO',
        3 => 'HRO_Purchase',
        4 => 'security_HRBP_ER',
        5 => 'HRBP',

    ];

    //当前最新的数据版本
    //用于区分不同版本数据，按钮显示问题
    public static $version = 2;

    /**
     * hold新增
     * @param $params
     * @return bool
     */
    public function addHold($params){
        try{
            $hold_model = new HoldStaffManageModel();
            $result_insert = $hold_model->batch_insert($params);
            $this->getDI()->get('logger')->write_log('addHold:' . json_encode($params) . '; 结果：'. json_encode($result_insert), 'info');
            if(!$result_insert) {
                return false;
            }else {
                $staff_info_id = $params[0]['staff_info_id'];
                //如果某员工新增了一条，那么该员工的释放状态都更改为待确认
                $db = $this->getDI()->get('db_backyard');
                foreach ($params as $v){
                    $db->updateAsDict(
                        "hold_staff_manage",
                        [ 'release_state'=> HoldStaffManageModel::HOLD_RELEASE_STATE_TODO],
                        [ 'conditions' => "staff_info_id = $staff_info_id and type = {$v['type']}" ]
                    );
                }
                
                return true;
            }
        } catch(Exception $e) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'addHold-Exception',
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'message' => $e->getMessage(),
            ]);
            return false;
        }
    }


    /**
     * 组合hold插入数据
     * @param $params
     * @return array
     */
    public function combinationAddHoldParams($params) {
        $current_time = gmdate('Y-m-d H:i:s',time() + ($this->timeOffset)*3600);

        $staff_info_id      = $params['staff_info_id'];          //hold 工号
        $type               = $params['type'] ?? 1;              //hold类型(1.工资hold,2.提成hold)
        $hold_reason        = $params['hold_reason'];            //hold原因
        $hold_remark        = $params['hold_remark'] ?? '';      //hold备注
        $hold_attachments   = $params['hold_attachments'] ?? ''; //上传的附件
        $handle_people      = $params['handle_people'];          //处理人
        $staff_submit_id    = $params['staff_submit_id'];        //提交人id
        $hold_source        = $params['hold_source'] ?? 1;       //hold来源(1.hold 新增,2.hirs)
        $month_begin        = $params['month_begin'] ?? '';      //hold 周期 开始
        $month_end          = $params['month_end'] ?? '';        //hold 周期 结束
        $handle_progress    = $params['handle_progress'] ?? 1;   // 新增默认1 处理状态(1:未处理,2:处理中,3:已处理)
        $hold_time          = $current_time;                     //hold 时间
        $hold_id            = date('Ymd', strtotime($current_time));
        $created_at         = $current_time;
        $update_at          = $current_time;

        $insert_params = [
            'hold_id'           => $hold_id,
            'type'              => $type,
            'staff_info_id'     => $staff_info_id,
            'hold_reason'       => $hold_reason,
            'hold_remark'       => $hold_remark,
            'hold_attachments'  => !empty($hold_attachments) ? json_encode($hold_attachments) :'',
            'hold_time'         => $hold_time,
            'handle_people'     => $handle_people,
            'staff_submit_id'   => $staff_submit_id,
            'handle_progress'   => $handle_progress,
            'hold_source'       => $hold_source,
            'created_at'        => $created_at,
            'update_at'         => $update_at,
        ];

        $add_params = [];
        if($type == 3) {
            $insert_params['type'] = 1;
            $add_params[] = $insert_params;
            $insert_params['type'] = 2;
            $add_params[] = $insert_params;
        } else {
            if($type == 2 && !empty($month_begin) && !empty($month_end)) {
                $insert_params['month_begin'] = date('Y-m-d', strtotime($month_begin . "-01"));
                $insert_params['month_end'] = date('Y-m-d', strtotime($month_end . "-" . date("t", strtotime($month_end))));
            }
            $add_params[] = $insert_params;
        }

        return $add_params;
    }

    /**
     * 根据员工ID 判断是否需要释放当前的hold
     * 工资 提成都是已处理时 释放hold 从fbi项目复制过来
     *
     * @param $staffId
     * @param $type 1 工资 2 提成
     */
    public function syncReleaseByStaffId($staffId, $type = 1)
    {
        $holds = HoldStaffManageModel::find([
            'conditions' => ' staff_info_id = :staff_id: and type = :type: and handle_progress != 3 and is_delete = 0',
            'bind' => [
                'staff_id' => $staffId,
                'type' => $type,
            ],
        ])->toArray();

        if (!$holds) {
            // 所有的type类型hold都是已处理
            // 找到未释放的 释放
            $holds = HoldStaffManageModel::find([
                'conditions' => ' staff_info_id = :staff_id: and type = :type: and hold_source != 5 and is_delete = 0',
                'bind' => [
                    'staff_id' => $staffId,
                    'type' => $type,
                ],
            ])->toArray();

            if (!$holds) {
                // hold 来源都是离职申请过来的

                $holds = HoldStaffManageModel::find([
                    'conditions' => ' staff_info_id = :staff_id: and type = :type: and release_state != 2 and is_delete = 0',
                    'bind' => [
                        'staff_id' => $staffId,
                        'type' => $type,
                    ],
                ]);
                // 存在未释放的 释放
                foreach ($holds as $hold) {
                    $hold->release_state = 2;
                    $hold->release_submit_id = 10003;
                    $hold->update_at = date('Y-m-d H:i:s');
                    $hold->release_time = date('Y-m-d H:i:s');
                    $hold->save();
                }
                $params_flash = [
                    'operator_id' => 10003,//系统同步
                    'type' => 2,//(1 hold ; 2 释放)
                    'staff_info_id' => $staffId,//员工id
                    'payment_markup' => '102',
                    'stop_payment_type' => $type,//工资阻止发放类型 1,2 ；(1工资；2提成)
                ];
                //$svc_call = env('hr_rpc', 'http://192.168.0.230:8090/hr-svc');
                //$ret = $this->getApiDatass('staff_hold', $svc_call, $params_flash);

                $bi_rpc = (new ApiClient('hris', '', 'staff_hold'));
                $bi_rpc->setParams([$params_flash]);
                $data = $bi_rpc->execute();

                $this->getDI()->get('logger')->write_log('release_hold ' . $staffId . ' '  . $type . '' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            }

        }

    }

    /**
     * 彭刚,彦军调用的接口
     * @param $params
     * @return int
     */
    public function synchronizeHoldStaff($params){
        try{
            $hold_staff_list = [];
            $hold_reason = array_flip(self::$hold_reasons);
            $reason_id = intval($hold_reason[$params['hold_reason']] ?? 0);
            $hold_reason = $params['hold_reason'];
            //员工信息
            $staffId = $params['staff_info_id'];
            $staff_bll = new StaffInfoService();
            $this->staffInfo = $staff_bll->getStaffInfo_hr($staffId);

            $typeArr =  explode(',',$params['type']);
            $realType = [];//同步 hris 表字段
            foreach ($typeArr as $v){
                //！！ 覆盖type 参数了
                $params['type'] = $v; //hold类型(1.工资hold,2.提成hold)
                $params['hold_id'] = gmdate('Ymd',time() + ($this->timeOffset)*3600);
                $params['created_at'] = gmdate('Y-m-d H:i:s',time() + ($this->timeOffset)*3600);
                $params['update_at'] = gmdate('Y-m-d H:i:s',time() + ($this->timeOffset)*3600);
                $params['handle_progress'] = 1;
                $params['release_state'] = 1;
                $params['version'] = self::$version;

                if($hold_reason == 'off_3_days'){
                    //3天未出勤停职员工->连续旷工三天以上
                    $params['hold_source'] = !empty($params['hold_source']) && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT ? $params['hold_source'] : 3;//为了兼容来源是 停职申请 下同
                    $params['handle_people'] = 'HRBP_ER';
                    $reason_id = 8;
                }elseif($hold_reason == 'fail_to_submit_public_funds'){
                    //未回公款->未缴纳公款
                    $params['hold_source'] = !empty($params['hold_source']) && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT ? $params['hold_source'] : 4;
                    $params['handle_people'] = 'security_HRBP_ER';
                    $reason_id = 9;
                }elseif($hold_reason == 'incomplete_resignation_procedures'){
                    //BY申请离职->离职手续不全
                    $params['hold_source'] = $params['hold_source'] ?? 5;
                    $params['handle_people'] = 'HRO';
                    $reason_id = 10;
                }elseif($hold_reason == 'incomplete_resignation_procedures1'){
                    $params['hold_source'] = !empty($params['hold_source']) && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT ? $params['hold_source'] : 6;
                    $params['handle_people'] = 'HR Service';
                    $params['hold_reason'] = 'incomplete_resignation_procedures';
                    $reason_id = 10;
                }elseif($hold_reason == 'incomplete_resignation_procedures2'){
                    $params['hold_source'] = !empty($params['hold_source']) && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT ? $params['hold_source'] : 1;
                    $params['handle_people'] = 'HR Service';
                    $params['hold_reason'] = 'incomplete_resignation_procedures';
                    $reason_id = 10;
                }elseif($hold_reason == 'incomplete_resignation_procedures3'){
                    $params['hold_source'] = !empty($params['hold_source']) && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT ? $params['hold_source'] : 9;
                    $params['handle_people'] = 'HR Service';
                    $params['hold_reason'] = 'incomplete_resignation_procedures';
                    $reason_id = 10;
                }elseif($hold_reason == 'incomplete_resignation_procedures4'){
                    $params['hold_source'] = !empty($params['hold_source']) && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT ? $params['hold_source'] : 11;
                    $params['handle_people'] = 'HRO';
                    $params['hold_reason'] = 'incomplete_resignation_procedures';
                    $reason_id = 10;
                } elseif ($hold_reason == 'received_warning_letters') {
                    $reason_id = 18;
                    $hold_count = HoldStaffManageModel::count([
                        'conditions' => 'staff_info_id = :staff_info_id: and release_state != 2 and type = :type: and is_delete = 0',
                        'bind' => [
                            'staff_info_id' => $params['staff_info_id'],
                            'type' => $v,
                        ],
                    ]);
                    $params['history_release_state'] = $hold_count > 0 ? 1 : 0;
                }elseif($hold_reason == 'suspicion_of_corruption/theft_of_public_property'){//贪污 有可能是子账号或者个人代理
                    //个人代理 没有工资hold 把type 里面的 1 去掉不管
                    if(in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether) && $v == HoldStaffManageModel::HOLD_TYPE_WAGES){
                        continue;
                    }
                    //替换 子账号为主账号 并且 记录子账号
                    $params = $this->getSupportInfo($params);
                }
                $hold_staff_list[] = $params;
                $realType[] = $v;//真实type  staff info 表stop_payment_type
            }
            $result = $this->addHold($hold_staff_list);

            //用于同步flash员工管理
            if($result){
                $params_flash = [
                    'operator_id' => -1,//系统同步
                    'type' => 1,//(1 hold ; 2 释放)
                    'staff_info_id' => $params['staff_info_id'],//员工id
                    'payment_markup' => $reason_id,
                    'stop_payment_type' => implode(',', $realType), //工资阻止发放类型 1,2 ；(1工资；2提成)
                ];
                //替换主账号 标记子账号
                if(!empty($this->masterStaff)){
                    $params_flash['staff_info_id'] = $this->masterStaff;
                    $params_flash['sub_staff_info_id'] = $staffId;
                }
                $ret = $this->syncHrisStaffHoldSvc($params_flash);

                if ($ret==true) {
                    return 1;
                } else {
                    $this->logger->error(['function' => 'synchronizeHoldStaff', 'message' => 'add hold api err', 'params' => $params, 'result' => $ret]);
                    return 0;
                }
            }else{
                $this->logger->error(['function' => 'synchronizeHoldStaff', 'message' => 'add hold err', 'params' => $params, 'result' => $result]);
                return 0;
            }

        } catch (\Exception $e) {
            $this->logger->error(['function' => 'synchronizeHoldStaff', 'message' => $e->getMessage(), 'Line' => $e->getLine(), 'params' => $params]);
            return 0;
        }
    }

    //贪污原因 fbi 过来的 有可能是 个人代理或者是子账号
    public function getSupportInfo($params)
    {
        if ($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_NO) {
            return $params;
        }
        //子账号 需要把记录主账号
        $supportInfo = HrStaffApplySupportStoreModel::findFirst([
            'columns'    => 'staff_info_id,sub_staff_info_id',
            'conditions' => 'sub_staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $this->staffInfo['id']],
        ]);
        //用主账号替换 记录子账号
        $params['staff_info_id']     = $supportInfo->staff_info_id;
        $params['sub_staff_info_id'] = $this->staffInfo['id'];
        $params['hold_remark'] = 'sub_staff_id ' . $this->staffInfo['id'];
        $this->masterStaff = $supportInfo->staff_info_id;
        return $params;
    }

    /**
     * 同步释放hold 离职申请撤销驳回、恢复在职申请时调用
     * @param $params
     * @return bool
     */
    public function syncReleaseHoldStaff($params)
    {
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '处理释放hold', 'params' => $params]);
            $staff_info_id = $params['staff_info_id'];
            $hold_source = $params['hold_source'] ?? 5;
            $hold_reason = $params['hold_reason'] ?? 'incomplete_resignation_procedures';
            $handle_hold = $params['handle_hold'] ?? 0;
            $operator_id = $params['operator_id'] ?? $staff_info_id;
            $switch = $this->handleHoldSwitch($params);
            //release_state 1待释放 2已释放
            //hold_reason hold 原因  incomplete_resignation_procedures 离职手续不全
            //hold_source 5 by申请离职
            //如果hold原因不是参数所传原因的话 还有hold 数据 不进行释放操作
            $others = $this->getOhterHold($staff_info_id, $hold_source, $hold_reason);
            if (count($others->toArray()) > 0){
                $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '有其他hold', 'params' => $params, 'hold_id' => $others]);
                if (!$handle_hold){
                    throw new ValidationException('有其他hold，不处理');
                }else {
                    $these = $this->getTheseHold($staff_info_id, $hold_source, $hold_reason);
                    $current_time = gmdate('Y-m-d H:i:s', time() + $this->timeOffset * 3600);
                    $handle_result_arr = [["key" => "", "value" => ""]];
                    $handle_result_json = json_encode($handle_result_arr);

                    if ($hold_reason == 'incomplete_resignation_procedures' && $switch) {

                        $handleProgress = array_column($others->toArray(), 'handle_progress');
                        if (in_array(HoldStaffManageModel::HANDLE_PROGRESS_UNPROCESSED, $handleProgress)) {
                            //存在未处理、未释放
                            $processData = [
                                'handle_progress' => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                                'handle_result'   => $handle_result_json,
                                'update_at'       => $current_time,
                            ];
                            $update_result = $these->update($processData);
                        } else {
                            $processData = [
                                'handle_progress' => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                                'handle_result'   => $handle_result_json,
                                'update_at'       => $current_time,
                                'release_state'   => HoldStaffManageModel::RELEASE_STATE_DONE,
                                'release_time'    => $current_time,
                            ];
                            $others->update($processData);
                            $these->update($processData);

                            //全部释放了
                            $api_params = [
                                'type'              => '2',
                                'staff_info_id'     => $staff_info_id,
                                'payment_markup'    => '101',
                                'operator_id'       => $operator_id,
                                'stop_payment_type' => '1,2',
                            ];
                            $sync_hold_result = $this->syncHrisStaffHoldSvc($api_params);
                            $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '释放hold-同步员工管理', 'params' => $api_params, 'result' => $sync_hold_result]);
                        }
                    } else {
                        $processData = [
                            'handle_progress' => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                            'handle_result'   => $handle_result_json,
                            'update_at'       => $current_time,
                        ];
                        $update_result = $these->update($processData);
                        $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '有其他hold，处理本原因的', 'params' => $params, 'result' => $update_result]);
                    }

                }
            }else {
                $these = $this->getTheseHold($staff_info_id, $hold_source, $hold_reason);
                $current_time = date('Y-m-d H:i:s');
                $handle_result_arr = [["key" => "no_punishment", "value" => ""]];
                $handle_result_json = json_encode($handle_result_arr);
                $update_result = $these->update(
                    [
                        'handle_progress' => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                        'handle_result'   => $handle_result_json,
                        'release_state'   => HoldStaffManageModel::RELEASE_STATE_DONE,
                        'release_time'    => $current_time,
                        'update_at'       => $current_time,
                    ]
                );
                $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '没有其他hold，处理本原因的', 'params' => $params, 'result' => $update_result]);

                $api_params = [
                    'type'              => '2',
                    'staff_info_id'     => $staff_info_id,
                    'payment_markup'    => '101',
                    'operator_id'       => $operator_id,
                    'stop_payment_type' => '1,2',
                ];
                $sync_hold_result = $this->syncHrisStaffHoldSvc($api_params);
                $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '释放hold-同步员工管理', 'params' => $api_params, 'result' => $sync_hold_result]);
            }
            $db->commit();
        } catch (ValidationException $ve) {
            $db->rollback();
            $this->logger->info(['function' => 'syncReleaseHoldStaff', 'message' => '有其他hold，不处理', 'params' => $params, 'hold_id' => $others]);
            return false;
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->error(['function' => 'syncReleaseHoldStaff', 'message' => $e->getMessage(), 'Line' => $e->getLine(), 'params' => $params]);
            return false;
        }
        return true;
    }

    /**
     * 是否走 hold_reason 为 incomplete_resignation_procedures(离职手续不全) 的 公共逻辑
     * @param $params
     * $params['common'] 是否走公共逻辑
     * @return bool
     */
    public function handleHoldSwitch($params)
    {
        if(isCountry('PH')
            && $params['hold_source'] == HoldStaffManageModel::HOLD_SOURCE_CONTRACT_EXPIRE
            && $params['hold_reason'] == 'incomplete_resignation_procedures'
        ) {
            if(isset($params['common'])) {
                return $params['common'];
            }
        }

        return true;
    }

    /**
     * @param $staffId
     * @param $holdSource
     * @param $holdReason
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    private function getOhterHold($staffId, $holdSource, $holdReason)
    {
        if (empty($holdSource)) {
            $conditions = 'staff_info_id = :staff_info_id: and release_state = 1 and hold_reason != :hold_reason: and is_delete = 0';
            $bind       = [
                'staff_info_id' => $staffId,
                'hold_reason'   => $holdReason,
            ];
        } else {
            $conditions = 'staff_info_id = :staff_info_id: and release_state = 1 and (hold_reason != :hold_reason: or hold_source != :hold_source:) and is_delete = 0';
            $bind       = [
                'staff_info_id' => $staffId,
                'hold_reason'   => $holdReason,
                'hold_source'   => $holdSource,
            ];
        }
        return HoldStaffManageModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @param $staffId
     * @param $holdSource
     * @param $holdReason
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getTheseHold($staffId, $holdSource, $holdReason, $type = 0)
    {
        if (empty($holdSource)) {
            $conditions = 'staff_info_id = :staff_info_id: and release_state = 1 and hold_reason = :hold_reason: and is_delete = 0';
            $bind       = [
                'staff_info_id' => $staffId,
                'hold_reason'   => $holdReason,
            ];
        } else {
            $conditions = 'staff_info_id = :staff_info_id: and release_state = 1 and hold_reason = :hold_reason: and hold_source = :hold_source: and is_delete = 0';
            $bind       = [
                'staff_info_id' => $staffId,
                'hold_reason'   => $holdReason,
                'hold_source'   => $holdSource,
            ];
        }

        if(!empty($type)) {
            $conditions .= ' and type = :type: ';
            $bind['type'] = $type;
        }

        return HoldStaffManageModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description:1. 资产操作权限：属于Asset Recycle 小组的Asset Support Officer/Asset Data Supervisor/Asset Support Supervisor
     * @return     : array []
     * <AUTHOR> L.J
     * @time       : 2021/8/12 15:18
     */
    public function assetsManagerStaffsJob()
    {
        $cache = $this->getDI()->get('redis');
        $key = 'setting_env_assets_manager_staffs_job';
        $cache_data = $cache->get($key);
        if(empty($cache_data)) {
            $settings= SettingEnvModelBy::get_val("assets_manager_staffs_job");
            $cache_data = 	!empty($settings) ? $settings : '';
            $cache->save($key, $cache_data,10*60);
            return explode(',', $cache_data);
        }
        return empty($cache_data) ? [] : explode(',', $cache_data);
    }

    /**
     * @description:1. 资产操作权限：属于Asset Recycle 小组的Asset Support Officer
     * @return     : array []
     * <AUTHOR> L.J
     * @time       : 2021/8/12 15:18
     */
    public function assetsManagerStaffsDdepartment()
    {
        $cache = $this->getDI()->get('redis');
        $key = 'setting_env_assets_manager_staffs_department';
        $cache_data = $cache->get($key);
        if(empty($cache_data)) {
            $settings=SettingEnvModelBy::get_val("assets_manager_staffs_department");
            $cache_data = 	!empty($settings) ? $settings : '';
            $cache->save($key, $cache_data,10*60);
            return explode(',', $cache_data);
        }
        return empty($cache_data) ? [] : explode(',', $cache_data);
    }

    /**
     * @description:"Asset Support Supervisor/Senior Asset Support Officer/Asset Data Supervisor（新增）、
     * @return     : array []
     * <AUTHOR> L.J
     * @time       : 2021/9/3 12:12
     */
    public function assetsNotDepartmentJob(){

        return [];
        $cache = $this->getDI()->get('redis');[];
        $key = 'setting_env_assets_not_department_job';
        $cache_data = $cache->get($key);
        if(empty($cache_data)) {
            $settings=SettingEnvModel::get_val("assets_not_department_job");
            $cache_data = 	!empty($settings) ? $settings : '';
            $cache->save($key, $cache_data,10*60);
            return explode(',', $cache_data);
        }
        return empty($cache_data) ? [] : explode(',', $cache_data);
    }

    /**
     * hcm setting evn 配置权限
     * @return array
     */
    public function assetsManagerStaffs()
    {
        $settingEnvModel = SettingEnvModelBy::findFirst([
            'conditions' => 'code = :code:',
            'bind' => [
                'code' => 'assets_manager_staffs',
            ],
        ]);
        $staffIds = [];
        if ($settingEnvModel) {
            $settings = $settingEnvModel->toArray();
            return explode(',', $settings['set_val']);
        }
        return $staffIds;
    }

    /**
     * 判断工号是否有指定角色
     * @param $staffId
     * @param $positionId
     * @return bool
     */
    public function isHasPositionPermission($staffId, $positionId)
    {
        if (in_array($staffId, explode(',', env('sa_id')))) {
            return true;
        }

        return (bool) $this->getDI()->get("db_rby")->fetchOne("select * from hr_staff_info_position where staff_info_id = {$staffId} and position_category = {$positionId} ");
    }


    /**
     * 处理员工申请和公司解约的hold
     * @param $staff_info_id
     * @return mixed
     */
    public function processHoldAfterMoneyAssetProcessed($staff_info_id)
    {
        //光辉需求 https://flashexpress.feishu.cn/docx/SUocdjGI2o419uxb38qcSjulnJf
        //hold来源不是「手动新增」的「离职手续不全」原因hold，
        //hold状态默认为"未处理"，当离职管理中资产状态和钱款状态变更为已处理，hold状态变更为已处理

        $sql = "update hold_staff_manage set handle_progress = ?,update_at = ? 
                         where staff_info_id = ? and 
                               hold_reason = 'incomplete_resignation_procedures' and 
                               hold_source != " . HoldStaffManageModel::HOLD_SOURCE_ADD_MANUALLY . " and handle_progress = 1";
        return $this->getDI()->get('db_backyard')->execute($sql,
            [HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED, date('Y-m-d H:i:s'), $staff_info_id]);
    }


    /**
     * LeaveManagerService - syncHold 同步hold状态
     * @param $params
     * @return int
     */
    public function synchronizeHoldProgress($params)
    {
        //员工id
        $staff_info_id = $params['staff_info_id'];
        //处理状态(1:未处理,2:处理中,3:已处理)
        $handle_progress = $params['handle_progress'];

        //hold来源是BY
        $hold_source = HoldStaffManageModel::HOLD_SOURCE_BY_APPLY;
        if (!empty($params['leave_source']) && in_array($params['leave_source'],[
                HoldStaffManageModel::HOLD_SOURCE_BY_APPLY, HoldStaffManageModel::HOLD_SOURCE_COMPANY_TERMINATION])) {
            $hold_source = $params['leave_source'];
        }

        $sql    = "update hold_staff_manage set handle_progress = ?,update_at = ? where staff_info_id = ? and hold_source  = ? and handle_progress = ?";
        return $this->getDI()->get('db_backyard')->execute($sql, [
            $handle_progress,
            date('Y-m-d H:i:s'),
            $staff_info_id,
            $hold_source,
            HoldStaffManageModel::HANDLE_PROGRESS_UNPROCESSED,
        ]);
    }

    /**
     * 同步hold原因为“离职手续不全”的hold为已处理状态
     * @param $params
     * @return int
     */
    public function syncIncompleteResignationHoldProgress($params)
    {
        try {
            //员工id
            $staff_info_id = $params['staff_info_id'];
            //处理状态(1:未处理,2:处理中,3:已处理)
            $handle_progress = $params['handle_progress'];

            //hold来源是BY
            $hold_source = HoldStaffManageModel::HOLD_SOURCE_BY_APPLY;
            if (!empty($params['leave_source']) && in_array($params['leave_source'],[
                    HoldStaffManageModel::HOLD_SOURCE_BY_APPLY, HoldStaffManageModel::HOLD_SOURCE_COMPANY_TERMINATION])) {
                $hold_source = $params['leave_source'];
            }

            $sql    = "update hold_staff_manage set handle_progress = ?,update_at = ? where staff_info_id = ? and hold_source  = ? and handle_progress = ?";
            $result = $this->getDI()->get('db_backyard')->execute($sql, [
                $handle_progress,
                date('Y-m-d H:i:s'),
                $staff_info_id,
                $hold_source,
                HoldStaffManageModel::HANDLE_PROGRESS_UNPROCESSED,
            ]);
            if ($result) {
                return 1;
            } else {
                $this->logger->error([
                    'function' => 'synchronizeHoldProgress',
                    'message'  => '保存失败',
                    'params'   => $params,
                ]);
                return 0;
            }
        } catch (\Exception $e) {
            $this->logger->error(['function' => 'synchronizeHoldProgress', 'message' => $e->getMessage(), 'Line' => $e->getLine(), 'params' => $params]);
            return 0;
        }
    }

    /**
     * 连续旷工hold_source=连续旷工(3) 处理状态：已处理；处理意见：不惩罚；附件：空；备注：leave management sync
     * @param $params
     * @return false
     * @throws Exception
     */
    public function syncAbsenteeismHoldHandle($staff_info_id,$handle_progress = HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED): bool
    {
        //处理状态(1:未处理,2:处理中,3:已处理)
        //连续旷工hold_source=3 处理状态：已处理；处理意见：不惩罚；附件：空；备注：leave management sync
        $handle_result    = '[{"key":"no_punishment","value":""}]';
        $hold_remark      = 'leave management sync';
        $hold_attachments = '[{"file_name":"system_auto.jpeg","object_url":"https://sai.flashexpress.com/workOrder/1589542185-232b8cc3a4ec473e81408f30a36dee50.jpeg","object_key":"workOrder/1589542185-232b8cc3a4ec473e81408f30a36dee50.jpeg"}]';

        $hold_list_obj = HoldStaffManageModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and hold_source = :hold_source: and handle_progress != :handle_progress:',
            'bind'       => [
                'staff_info_id'   => $staff_info_id,
                'hold_source'     => self::HOLD_SOURCE_STAY_AWAY_FROM_WORK,
                'handle_progress' => self::HANDLE_PROGRESS_PROCESSED,
            ],
        ]);
        $hold_list_arr = $hold_list_obj->toArray();

        if (empty($hold_list_arr)) {
            return true;
        }
        $hold_list_obj->update([
            'handle_progress' => $handle_progress,
            'handle_result'   => $handle_result,
        ]);
        $insert_history_param = [];
        foreach ($hold_list_arr as  $value) {
            $insert_history_param[] = [
                'hold_no'          => $value['id'],
                'staff_submit_id'  => 10003,
                'handle_progress'  => $handle_progress,
                'handle_result'    => $handle_result,
                'hold_attachments' => $hold_attachments,
                'hold_remark'      => $hold_remark,
                'hold_time'        => date('Y-m-d H:i:s'),
            ];
        }
        (new HoldHistoryModel())->batch_insert($insert_history_param);
        return true;
    }

    /**
     * hold list
     * @param $params
     * @return array
     */
    public function getHoldList($params) {
        $page_num  = intval($params['page_num'] ?? 1);
        $page_size = intval($params['page_size'] ?? 20);
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->getHoldListBuilder($params);
        $builder_count = $this->getHoldListBuilder($params);
        $builder->limit($page_size, $offset);
        $list = $builder->orderBy('hm.handle_progress ASC')->getQuery()->execute()->toArray();

        $items = !empty($list) ? $this->combinationHoldList($list) : [];

        //获取total count
        $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();

        return [
            'items' => $items,
            'paginate' => [
                'total_count' => !empty($totalCount) ? $totalCount->count : 0,
                'page_num' => $page_num,
                'page_size' => $page_size,
            ],
        ];
    }

    /**
     * hold 查询参数验证
     * @return array
     */
    public function HoldListParamsValidations() {
        $v_handle_people    = implode(',', array_values(self::$handle_people));
        $v_handle_progress  = implode(',', array_keys(self::$handle_progress));
        $v_hold_source      = implode(',', array_keys(self::$hold_source));
        $v_hold_reason      = implode(',', array_values(self::$hold_reasons));
        $validations = [
            'type'                   => 'Required|IntIn:1,2',              //1工资 2提成
            'staff_info_id'          => 'Int',                             //工号
            'store_id'               => 'Str',                             //所属网点
            'area_id'                => 'Int',                             //所属区域
            'department_id'          => 'Int',                             //部门
            'handle_people'          => 'StrIn:' . $v_handle_people,       //处理人
            'handle_progress'        => 'IntIn:' . $v_handle_progress,     //处理状态
            'hold_reason'            => 'StrIn:' . $v_hold_reason,         //原因
            'hold_source'            => 'IntIn:' . $v_hold_source,         //来源
            'hold_date_begin'        => 'Date',
            'hold_date_end'          => 'Date',
            'last_update_date_begin' => 'Date',
            'last_update_date_end'   => 'Date',
            'region_id'              => 'Int',
            'piece_id'               => 'Int',
            'hire_type'              => 'Arr',
        ];
        return $validations;
    }

    /**
     * hold list builder
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    public function getHoldListBuilder($params) {
        $type                   = $params['type'] ?? 1;                    //hold类型
        $staff_info_id          = $params['staff_info_id'] ?? '';          //工号
        $department_id          = $params['department_id'] ?? '';          //部门id
        $store_id               = $params['store_id'] ?? '';               //网点id
        $handle_people          = $params['handle_people'] ?? '';          //处理人列表
        $handle_progress        = $params['handle_progress'] ?? '';        //处理状态
        $hold_source            = $params['hold_source'] ?? '';            //hold来源
        $hold_reason            = $params['hold_reason'] ?? '';            //hold原因
        $last_update_date_begin = $params['last_update_date_begin'] ?? ''; //最后更新开始时间
        $last_update_date_end   = $params['last_update_date_end'] ?? '';   //最后更新结束时间
        $hold_date_begin        = $params['hold_date_begin'] ?? '';        //hold 开始时间
        $hold_date_end          = $params['hold_date_end'] ?? '';          //hold 结束时间
        $area_id                = $params['area_id'] ?? '';                //所属区域
        $region_id              = $params['region_id'] ?? '';              //大区id
        $piece_id               = $params['piece_id'] ?? '';               //片区id
        $hire_type              = $params['hire_type'] ?? [];               //雇佣类型

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
                            hm.id,
                            hm.hold_id,
                            hm.staff_info_id,
                            hm.hold_reason,
                            hm.hold_source,
                            hm.hold_time,
                            hm.handle_people,
                            hm.staff_submit_id,
                            hm.handle_progress,
                            hm.approval_time,
                            hm.approval_status,
                            hm.handle_result,
                            hm.hold_remark,
                            hm.hold_attachments,
                            hm.type,
                            hm.month_begin,
                            hm.month_end,
                            hm.release_state,
                            hm.release_time,
                            hm.created_at,
                            convert_tz(hm.updated_at, \'+00:00\', \''. $this->timeZone . '\') as update_at,
                            hm.last_hold_time,
                            hm.release_submit_id,
                            hs.name as staff_info_name,
                            hs.sys_store_id,
                            hs.state as staff_state,
                            hs.wait_leave_state,
                            hs.leave_date,
                            hs.sys_department_id,
                            hs.node_department_id,
                            hs.job_title,
                            hs.hire_type,
                            store.manage_region,
                            store.manage_piece,
                            hm.version
                            ');
        $builder->from(['hm' => HoldStaffManageModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hm.staff_info_id = hs.staff_info_id', 'hs');
        $builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
        $builder->where("type = :type:", ['type' => $type]);
        $builder->andWhere("hm.is_delete = 0");
        if(!empty($staff_info_id)) {
            $builder->andWhere("hm.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_info_id]);
        }

        if (!empty($hire_type)) {
            $builder->inWhere("hs.hire_type", $hire_type);
        }

        if(!empty($department_id)) {
            $builder->andWhere("hs.node_department_id = :node_department_id:", ['node_department_id' => $department_id]);
        }

        if(!empty($store_id)) {
            $builder->andWhere("hs.sys_store_id = :store_id:", ['store_id' => $store_id]);
        }

        if(!empty($handle_people)) {
            $builder->andWhere("hm.handle_people = :handle_people:", ['handle_people' => $handle_people]);
        }

        if(!empty($handle_progress)) {
            $builder->andWhere("hm.handle_progress = :handle_progress:", ['handle_progress' => $handle_progress]);
        }

        if(!empty($hold_reason)) {
            $builder->andWhere("hm.hold_reason = :hold_reason:", ['hold_reason' => $hold_reason]);
        }

        if(!empty($hold_source)) {
            $builder->andWhere("hm.hold_source = :hold_source:", ['hold_source' => $hold_source]);
        }

        if (!empty($last_update_date_begin)) {
            $builder->andWhere("hm.updated_at >= :last_update_date_begin:",
                ['last_update_date_begin' => gmdate('Y-m-d H:i:s', strtotime($last_update_date_begin . ' 00:00:00'))]);
        }

        if (!empty($last_update_date_end)) {
            $builder->andWhere("hm.updated_at <= :last_update_date_end:",
                ['last_update_date_end' => gmdate('Y-m-d H:i:s', strtotime($last_update_date_end . ' 23:59:59'))]);
        }

        if(!empty($hold_date_begin)) {
            $builder->andWhere("hm.hold_time >= :hold_date_begin:", ['hold_date_begin' => $hold_date_begin.' 00:00:00']);
        }

        if(!empty($hold_date_end)) {
            $builder->andWhere("hm.hold_time <= :hold_date_end:", ['hold_date_end' => $hold_date_end.' 23:59:59']);
        }

        if(!empty($area_id)) {
            //$builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
            $builder->leftJoin(SysProvinceModel::class, 'store.province_code = sp.code', 'sp');
            $builder->andWhere("sp.manage_geography_code = :area_id:", ['area_id' => $area_id]);
        }

        if(!empty($region_id)) {
            $builder->andWhere("store.manage_region = :manage_region:", ['manage_region' => $region_id]);
        }

        if(!empty($piece_id)) {
            $builder->andWhere("store.manage_piece = :manage_piece:", ['manage_piece' => $piece_id]);
        }

        if (!empty($params['max_id'])) {
            $builder->andWhere('hm.id > :max_id:', ['max_id' => $params['max_id']]);
        }
        
        return $builder;
    }

    /**
     * 组合hold 数据
     * @param $list
     * @return mixed
     */
    public function combinationHoldList($list) {
        $store_ids        = [];
        $staff_submit_ids = [];
        foreach ($list as $key => $value) {
            $store_ids[]        = $value['sys_store_id'];
            $staff_submit_ids[] = $value['staff_submit_id'];
        }

        $sys_store_list = (new SysStoreService())->getStoreList($store_ids);
        $sys_store_list = array_column($sys_store_list, null, 'id');

        $submit_staff_list = $this->getStaffList($staff_submit_ids);
        $submit_staff_list = array_column($submit_staff_list, null, 'staff_info_id');

        $province_list = SysProvinceModel::find()->toArray();
        $province_list = array_column($province_list, null, 'code');

        $hire_type_list = array_column((new \App\Services\SysService())->getHireTypeList(), 'label', 'value');

        foreach ($list as &$value) {
            $value['staff_name']           = $value['staff_info_name'] . ' ' . $value['staff_info_id'];
            $value['hold_source_name']     = self::$t->_(self::$hold_source[$value['hold_source']]);
            $value['handle_progress_name'] = self::$t->_(self::$handle_progress[$value['handle_progress']]);
            $value['hold_id']              = $value['hold_id'] . '000' . $value['id'];
            $value['handle_people']        = self::$t->_($value['handle_people']);
            $handle_result                 = json_decode($value['handle_result'], true);
            $value['handle_result']        = $handle_result;
            $value['hold_attachments']     = json_decode($value['hold_attachments'], true);
            $value['area_name']            = '';
            if ($value['sys_store_id'] == '-1') {
                $value['store_name'] = GlobalEnums::HEAD_OFFICE;
            } else {
                $value['store_name'] = $this->showStoreName($value['sys_store_id']);
                $province            = $province_list[$sys_store_list[$value['sys_store_id']]['province_code']] ?? [];
                if (!empty($province)) {
                    $value['area_name'] = self::$manage_areas[$province['manage_geography_code']] ?? '';
                }
            }
            $value['staff_submit_name'] = '';
            if ($value['staff_submit_id'] == 10000) {
                $value['staff_submit_name'] = '超管10000';
            } else {
                $submit_name                = $submit_staff_list[$value['staff_submit_id']]['name'] ?? '';
                $value['staff_submit_name'] = !empty($submit_name) ? ($submit_name . ' ' . $value['staff_submit_id']) : '';
            }
            $value['hold_month'] = ($value['month_begin'] != '0000-00-00' && $value['month_end'] != '0000-00-00' && $value['month_begin'] != "1970-01-01" && $value['month_end'] != "1970-01-01") ? date("Y-m",
                    strtotime($value['month_begin'])) . " - " . date("Y-m", strtotime($value['month_end'])) : "";

            $value['department_name'] = $this->showDepartmentName($value['node_department_id']);
            $value['job_title_name']  = $this->showJobTitleName($value['job_title']);

            $handle_result_str = [];
            if (!empty($handle_result)) {
                foreach ($handle_result as $k => $v) {
                    $handle_result_str[] = self::$t->_($v['key']) . $v['value'];
                }
            }
            $value['handle_result_str'] = !empty($handle_result_str) ? implode(',', $handle_result_str) : '';

            $value['manage_region_name'] = $this->showRegionName($value['manage_region']);
            $value['manage_piece_name']  = $this->showPieceName($value['manage_piece']);
            $value['hire_type_text']     = $hire_type_list[$value['hire_type']] ?? '';
        }
        return $list;
    }

    /**
     * hold导出
     * @param $params
     * @return array
     * @throws BusinessException|Exception
     * @throws ValidationException
     */
    public function doExportHoldList($params): array
    {
        $service    = reBuildCountryInstance(new HoldManageService());
        $max_limit  = get_runtime() == 'dev' ? 10 : 10000;
        $builder    = $service->getHoldListBuilder($params);
        $totalCount = $builder->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
        if ($totalCount->count > $max_limit) {
            //入 task 表 走队列导出
            $result = (new ExcelService())->insertTask($params['user_id'], HoldTask::EXPORT_HOLD_LIST_ACTION,
                $params,
                HcmExcelTackModel::TYPE_DEFAULT, uniqid('hold_list_' . date('YmdHis')) . '.xlsx', false);
            if ($result['code'] != ErrCode::SUCCESS) {
                throw new BusinessException($result['message']);
            }
            return ['url' => '', 'message' => self::$t->_('file_download_tip')];
        }
        /**
         * @see HoldManageService::exportHoldList()
         */
        return $service->exportHoldList($params);
    }

    /**
     * hold 下载
     * @param $params
     * @return array
     * @throws OssException
     */
    public function exportHoldList($params): array
    {
        $limit = 500;
        if (get_runtime() == 'dev') {
            $limit = 50;
        }
        $export_row = [];

        while (true) {

            $builder    = $this->getHoldListBuilder($params);
            $builder->orderBy('hm.id ASC')->limit($limit);
            $list       = $builder->getQuery()->execute()->toArray();
            if (empty($list)) {
                break;
            }
            $list       = $this->combinationHoldList($list);
            foreach ($list as $key => $value) {
                //处理最大值
                $params['max_id']  = $value['id'];
                $row = [];
                $staff_state_text  = ($value['staff_state'] == 1 && $value['wait_leave_state'] == 1) ? self::$t->_('wait_leave_state_' . $value['wait_leave_state']) : self::$t->_('staff_state_' . $value['staff_state']);
                $staff_leave_date  = $value['staff_state'] == 2 || ($value['staff_state'] == 1 && $value['wait_leave_state'] == 1) ? date("Y-m-d",
                    strtotime($value['leave_date'])) : '';
                $release_state     = $value['release_state'] == 2 ? self::$t->_("paid") : '';
                $hold_reason       = self::$t->_($value['hold_reason']);
                $hold_source       = self::$t->_(self::$hold_source[$value['hold_source']]);
                $staff_submit_name = $value['staff_submit_name'];
                if (empty($staff_submit_name)) {
                    $staff_submit_name = 'system auto';
                }
                if(isCountry('TH')){
                    $subStaffId = '';
                    if(strstr($value['hold_remark'], 'sub_staff_id')){
                        $arr = explode(' ', $value['hold_remark']);
                        $subStaffId = end($arr);
                    }
                }
                $row[] = $value['hold_id'];              //hold ID
                $row[] = $hold_reason;                   //hold原因
                $row[] = $hold_source;                   //hold 来源
                if(isCountry('TH')){//只有泰国和马来
                    $row[] = $subStaffId ?? '';//如果备注有子账号 显示子账号
                }
                $row[] = $value['staff_info_id'];        //员工工号
                $row[] = $value['staff_name'];           //员工姓名
                $row[] = $value['store_name'];           //所属网点
                $row[] = $value['manage_region_name'];   //大区
                $row[] = $value['manage_piece_name'];    //片区
                $row[] = $value['job_title_name'];       //职位
                $row[] = $value['department_name'];      //部门
                $row[] = $value['hire_type_text'];       //雇佣类型
                $row[] = $value['handle_people'];        //处理人
                $row[] = $staff_submit_name;             //提交人
                $row[] = $value['handle_progress_name']; //处理状态
                $row[] = $value['hold_time'];            //hold时间
                $row[] = $value['update_at'];            //最后更新时间
                $row[] = $value['area_name'];            //所属区域
                $row[] = $value['handle_result_str'];    //处理结果
                $row[] = $staff_state_text;              //员工在职状态
                $row[] = $staff_leave_date;              //离职日期
                $row[] = $value['hold_month'];           //hold 月份
                $row[] = $release_state;                 //是否已释放
                $export_row[] = $row;
            }
        }

        $head[] = self::$t->_('hold_id');                             //hold ID;
        $head[] = self::$t->_('hold_reason');                         //hold原因;
        $head[] = self::$t->_('hold_source');                         //hold来源
        if(isCountry('TH')){
            $head[] = self::$t->_('staff_support_store_sub_staff_info_id');
        }
        $head[] = self::$t->_('staff_info_id');                       //员工工号;
        $head[] = self::$t->_('staff_info_name');                     //员工姓名;
        $head[] = self::$t->_('dot');                                 //所属网点;
        $head[] = self::$t->_('store_area');                          //所属大区
        $head[] = self::$t->_('store_district');                      //所属片区
        $head[] = self::$t->_('job_name');                            //职位;
        $head[] = self::$t->_('headquarters_department');             //部门;
        $head[] = self::$t->_("hire_type");                           //雇佣类型
        $head[] = self::$t->_('submit_name');                         //处理人;
        $head[] = self::$t->_('sender');                              //提交人;
        $head[] = self::$t->_('handle_progress');                     //处理状态;
        $head[] = self::$t->_('hold_time');                           //hold时间;
        $head[] = self::$t->_('latest_update_time');                  //最后更新时间;
        $head[] = self::$t->_('hr_probation_field_store_area_text');  //所属区域;
        $head[] = self::$t->_('handle_result');                       //处理结果;
        $head[] = self::$t->_('staff_state');                         //在职状态;
        $head[] = self::$t->_('staff_leave_date');                    //离职日期;
        $head[] = self::$t->_('staff_hold_month');                    //hold 月份;
        $head[] = self::$t->_("hold_release_state");                  //是否已释放

        return $this->makeHoldExportResult($head,$export_row,$params);
    }

    /**
     * @param $head
     * @param $export_row
     * @param $params
     * @return array
     * @throws OssException
     */
    protected function makeHoldExportResult($head, $export_row, $params): array
    {
        $fileName   = $params['file_name'] ?? uniqid('hold_list_' . date('YmdHis')) . '.xlsx';
        $excel_file = $this->exportExcel($head, $export_row, $fileName);
        $flashOss   = new FlashOss();
        //每个国家有每个国家的名字
        $file_path = self::HOLD_MANAGER_OSS_PATH . '/' . date('Ymd') . '/' . $fileName;
        $flashOss->uploadFile($file_path, $excel_file['data']);
        $download_url = $flashOss->signUrl($file_path);
        return ['url' => $download_url, 'file_path' => $file_path];
    }


    /**
     * hold 修改
     * @param $params
     * @return bool
     */
    public function uploadHold($params) {
        $id = $params['id'];
        $handle_progress = $params['handle_progress'];
        $handle_result = $params['handle_result'];
        $month_begin = $params['month_begin'] ?? '';
        $month_end = $params['month_end'] ?? '';
        $hold_attachments_up = $params['hold_attachments_up'];
        $hold_attachments = $params['hold_attachments'];
        $staff_submit_id = $params['staff_submit_id'];
        $hold_remark = $params['hold_remark'];

        $current_time = gmdate('Y-m-d H:i:s',time() + $this->timeOffset * 3600);

        $by_db = $this->getDI()->get("db_backyard");
        $by_db->begin();
        try {
            //增加hold history
            $handle_result = !empty($handle_result) ? json_encode($handle_result) : '';
            $insert_history_param = [
                'hold_no' => $id,
                'staff_submit_id' => $staff_submit_id,
                'handle_progress' => $handle_progress,
                'handle_result' => $handle_result,
                'hold_attachments' => !empty($hold_attachments) ? json_encode($hold_attachments) : '',
                'hold_remark' => $hold_remark,
                'hold_time' => $current_time,
            ];
            $insert_result = $by_db->insertAsDict("hold_history", $insert_history_param);
            if(!$insert_result) {
                $this->logger->error(['function' => 'HoldManageService-uploadHold','message' => '插入hold_history', 'params' => $params, 'result' => $insert_result]);
                $by_db->rollback();
                return false;
            }

            //修改hold详情
            if(!empty($month_begin) && !empty($month_end)) {
                $month_begin = date('Y-m-d', strtotime("{$month_begin}-01"));
                $month_end = date('Y-m-d', strtotime($month_end . "-" . date("t", strtotime($month_end))));
            } else {
                $month_begin = '1970-01-01';
                $month_end = '1970-01-01';
            }
            $hold_attachments = !empty($hold_attachments_up) ? json_encode($hold_attachments_up) : '';
            $update_hold_result = $by_db->updateAsDict(
                                    "hold_staff_manage",
                                    [
                                        'handle_progress' => $handle_progress,
                                        'handle_result' => $handle_result,
                                        'hold_attachments' => $hold_attachments,
                                        'month_begin' => $month_begin,
                                        'month_end' => $month_end,
                                        'update_at' => $current_time,
                                        'last_hold_time' => $current_time,
                                    ],
                                    [
                                        "conditions" => 'id = ?',
                                        'bind'       => [$params['id']],
                                        'bindTypes'  => [PDO::PARAM_INT],
                                    ]
                                );
            if(!$update_hold_result) {
                $this->logger->error(['function' => 'HoldManageService-uploadHold','message' => '更新hold信息', 'params' => $params, 'result' => $update_hold_result]);
                $by_db->rollback();
                return false;
            }

            //注释原因：hold_staff_manage追加新updated_at字段，字段更新该时间字段即更新
            //更新指定工号所有hold 最后hold时间
            //$update_sql = "update hold_staff_manage set last_hold_time = '{$current_time}' where staff_info_id = {$params['staff_info_id']}";
            //$update_staff_result = $by_db->execute($update_sql);
            //if(!$update_staff_result) {
            //    $this->logger->error(['function' => 'HoldManageService-uploadHold','message' => '更新指定工号所有hold 最后hold时间:', 'params' => $params, 'result' => $update_staff_result]);
            //    $by_db->rollback();
            //    return false;
            //}

            $by_db->commit();
            $this->logger->info(['function' => 'HoldManageService-uploadHold','最后:' => $current_time, 'params' => $params]);
            return true;
        } catch (Exception $e) {
            $by_db->rollback();
            $this->logger->error(['function' => 'HoldManageService-uploadHold','message' =>$e->getMessage(),'line' => $e->getLine(),'params' => $params]);
            return false;
        }
    }

    /**
     * hold 详情
     * @param $params
     * @return array
     */
    public function getHoldInfo($params) {
        $id = $params['id'] ?? 0;
        $hold_detail = $this->modelsManager->createBuilder()
            ->columns("hm.id,
                                concat(hm.hold_id,'000',hm.id) as hold_id,
                                hm.staff_info_id,
                                hm.hold_reason,
                                hm.hold_time,
                                hm.staff_submit_id,
                                hm.handle_result,
                                hm.hold_remark,
                                hm.hold_attachments,
                                hm.type,
                                hm.handle_progress,
                                hm.month_begin,
                                hm.month_end,
                                hs.name as staff_submit_name")
            ->from(['hm' => HoldStaffManageModel::class])
            ->leftJoin(HrStaffInfoModel::class, 'hm.staff_submit_id = hs.staff_info_id', 'hs')
            ->where("hm.id = :id:", ['id' => $id])
            ->getQuery()->execute()->getFirst();
        if(!empty($hold_detail)) {
            $hold_detail = $hold_detail->toArray();
            if (!empty($hold_detail['hold_attachments'])) {
                $hold_detail['hold_attachments'] = json_decode($hold_detail['hold_attachments'], true);
            }

            if($hold_detail['staff_submit_id'] == 10000){
                $hold_detail['staff_submit_name'] = '超管10000';
            } else if ($hold_detail['staff_submit_id'] == 10003) {
                $hold_detail['staff_submit_name'] = '超管10003';
            }

            if(empty($hold_detail['staff_submit_name'])) {
                $hold_detail['staff_submit_name'] = 'system auto';
            } else {
                $hold_detail['staff_submit_name'] = $hold_detail['staff_submit_name'] . ' ' . $hold_detail['staff_submit_id'];
            }

            $handle_result = json_decode($hold_detail['handle_result'], true);
            $hold_detail['handle_result'] = $handle_result;

            $hold_detail['month_begin'] = $hold_detail['month_begin'] != '0000-00-00' && $hold_detail['month_begin'] != '1970-01-01' ? date("Y-m", strtotime($hold_detail['month_begin'])) : '';
            $hold_detail['month_end'] = $hold_detail['month_end'] != '0000-00-00'  && $hold_detail['month_end'] != '1970-01-01' ? date("Y-m", strtotime($hold_detail['month_end'])) : '';

            $hold_detail['hold_reason'] = self::$t->_($hold_detail['hold_reason']);

            $hold_history = $this->modelsManager->createBuilder()
                ->columns("hh.staff_submit_id,
                                            hh.handle_progress,
                                            hh.handle_result,
                                            hh.hold_attachments,
                                            hh.hold_remark,
                                            hh.hold_time,
                                            hs.name as staff_submit_name")
                ->from(['hh' => HoldHistoryModel::class])
                ->leftJoin(HrStaffInfoModel::class, 'hh.staff_submit_id = hs.staff_info_id', 'hs')
                ->where('hh.hold_no = :hold_no:', ['hold_no' => $id])
                ->orderBy('hh.hold_time DESC')
                ->getQuery()->execute()->toArray();

            foreach ($hold_history as $key => $value) {
                $history_handle_result = json_decode($value['handle_result'], true);
                $history_hold_attachments = json_decode($value['hold_attachments'], true);
                $hold_history[$key]['handle_result'] = $history_handle_result;
                $hold_history[$key]['hold_attachments'] = $history_hold_attachments;
                $hold_history[$key]['staff_submit_name'] = $value['staff_submit_name'] . $value['staff_submit_id'];
                if($value['staff_submit_id'] == 10000){
                    $hold_history[$key]['staff_submit_name'] = '超管10000';
                }
            }

            $hold_detail['history'] = $hold_history;
            $info = $hold_detail;
        }
        return $info??[];
    }

    /**
     * 同步员工管理hold 工资发放状态 阻止原因
     * @param $params
     * @return bool
     */
    public function syncHrisStaffHoldSvc($params) {
        $api_params = [
            'type'              => $params['type'],             //(1 hold ; 2 释放)
            'staff_info_id'     => $params['staff_info_id'],    //员工id
            'payment_markup'    => $params['payment_markup'],   //阻止原因
            'operator_id'       => $params['operator_id'],      //操作人
            'stop_payment_type' => $params['stop_payment_type'],//工资阻止发放类型 1,2 ；(1工资；2提成)
        ];
        if(!empty($params['sub_staff_info_id'])){
            $api_params['sub_staff_info_id'] = $params['sub_staff_info_id'];
        }
        $bi_rpc = (new ApiClient('hris', '', 'staff_hold'));
        $bi_rpc->setParams([$api_params]);
        $data = $bi_rpc->execute();
        if($data === true) {
            $this->logger->info(['function' => 'syncHrisStaffHoldSvc', 'method' => 'staff_hold', 'params' => $params, '结果' => $data]);
            return true;
        } else {
            $this->logger->error(['function' => 'syncHrisStaffHoldSvc', 'method' => 'staff_hold', 'params' => $params, '结果' => $data]);
            return false;
        }
    }

    /**
     * 释放列表
     * @param $params
     * @return array
     */
    public function getReleaseList($params) {
        $page_num  = intval($params['page_num'] ?? 1);
        $page_size = intval($params['page_size'] ?? 20);
        $offset    = $page_size * ($page_num - 1);
        $builder = $this->getReleaseListBuilder($params);
        $builder_count = $this->getReleaseListBuilder($params);
        $builder->groupBy('hm.staff_info_id');
        $builder->orderBy('hm.handle_progress ASC');
        $builder->limit($page_size, $offset);
        $list = $builder->getQuery()->execute()->toArray();

        $items = !empty($list) ? $this->combinationReleaseList($list) : [];

        //获取total count
        $totalCount = $builder_count->groupBy('hm.staff_info_id')->getQuery()->execute()->toArray();

        return [
            'items' => $items,
            'paginate' => [
                'total_count' => count($totalCount),
                'page_num' => $page_num,
                'page_size' => $page_size,
            ],
        ];
    }

    /**
     * release 查询参数验证
     * @return array
     */
    public function releaseListParamsValidations()
    {
        $validations = [
            'type'                   => 'Required|IntIn:1,2', //1工资 2提成
            'staff_info_id'          => 'Int',                //工号
            'store_id'               => 'Str',                //所属网点
            'area_id'                => 'Int',                //所属区域
            'release_state'          => 'IntIn:1,2',          //释放状态 1待确认 2已释放
            'hold_date_begin'        => 'Date',
            'hold_date_end'          => 'Date',
            'last_update_date_begin' => 'Date',
            'last_update_date_end'   => 'Date',
            'region_id'              => 'Int',
            'piece_id'               => 'Int',
            'hire_type'              => 'Arr',
        ];
        return $validations;
    }

    /**
     * release list builder
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    public function getReleaseListBuilder($params)
    {

        $type                   = $params['type'] ?? 1;                    //hold类型
        $staff_info_id          = $params['staff_info_id'] ?? '';          //工号
        $store_id               = $params['store_id'] ?? '';               //网点id
        $release_state          = $params['release_state'] ?? '';          //处理状态
        $last_update_date_begin = $params['last_update_date_begin'] ?? ''; //最后更新开始时间
        $last_update_date_end   = $params['last_update_date_end'] ?? '';   //最后更新结束时间
        $hold_date_begin        = $params['hold_date_begin'] ?? '';        //hold 开始时间
        $hold_date_end          = $params['hold_date_end'] ?? '';          //hold 结束时间
        $area_id                = $params['area_id'] ?? '';                //所属区域
        $region_id              = $params['region_id'] ?? '';              //大区id
        $piece_id               = $params['piece_id'] ?? '';               //片区id
        $hire_type              = $params['hire_type'] ?? [];               //雇佣类型

        $builder = $this->modelsManager->createBuilder();

        $builder->columns("
                            GROUP_CONCAT(hm.id) as id,
			                GROUP_CONCAT(concat(hm.hold_id,'000',hm.id)) as hold_id,
			                GROUP_CONCAT(hm.hold_reason) as hold_reasons,
			                GROUP_CONCAT(hm.hold_source) as source,
			                GROUP_CONCAT(hm.version) as version,
                            hm.staff_info_id,
                            hm.type,
                            hm.release_time,
                            hm.release_state,
                            hm.release_submit_id,
                            hs.name as staff_info_name,
                            hs.sys_store_id,
                            hs.state as staff_state,
                            hs.wait_leave_state,
                            hs.leave_date,
                            hs.sys_department_id,
                            hs.node_department_id,
                            hs.job_title,
                            hs.hire_type,
                            store.manage_region,
                            store.manage_piece
                            ");
        $builder->from(['hm' => HoldStaffManageModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hm.staff_info_id = hs.staff_info_id', 'hs');
        $builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
        $builder->where("type = :type:", ['type' => $type]);
        $builder->andWhere("hm.is_delete = 0");
        $builder->andWhere('not exists (select mm.staff_info_id from App\Models\backyard\HoldStaffManageModel mm where mm.handle_progress != 3 and mm.type = :type: and hm.staff_info_id = mm.staff_info_id and mm.is_delete = 0)',
            ['type' => $type]);

        if (!empty($staff_info_id)) {
            $builder->andWhere("hm.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_info_id]);
        }

        if (!empty($hire_type)) {
            $builder->inWhere("hs.hire_type", $hire_type);
        }

        if (!empty($store_id)) {
            $builder->andWhere("hs.sys_store_id = :store_id:", ['store_id' => $store_id]);
        }

        if (!empty($release_state)) {
            $builder->andWhere("hm.release_state = :release_state:", ['release_state' => $release_state]);
        }

        if (!empty($last_update_date_begin)) {
            $builder->andWhere("hm.updated_at >= :last_update_date_begin:",
                ['last_update_date_begin' => gmdate('Y-m-d H:i:s', strtotime($last_update_date_begin . ' 00:00:00'))]);
        }

        if (!empty($last_update_date_end)) {
            $builder->andWhere("hm.updated_at <= :last_update_date_end:",
                ['last_update_date_end' => gmdate('Y-m-d H:i:s', strtotime($last_update_date_end . ' 23:59:59'))]);
        }

        if (!empty($hold_date_begin)) {
            $builder->andWhere("hm.release_time >= :hold_date_begin:",
                ['hold_date_begin' => $hold_date_begin . ' 00:00:00']);
        }

        if (!empty($hold_date_end)) {
            $builder->andWhere("hm.release_time <= :hold_date_end:", ['hold_date_end' => $hold_date_end . ' 23:59:59']);
        }

        if (!empty($area_id)) {
            //$builder->leftJoin(SysStoreModel::class, 'hs.sys_store_id = store.id', 'store');
            $builder->leftJoin(SysProvinceModel::class, 'store.province_code = sp.code', 'sp');
            $builder->andWhere("sp.manage_geography_code = :area_id:", ['area_id' => $area_id]);
        }

        if (!empty($region_id)) {
            $builder->andWhere("store.manage_region = :manage_region:", ['manage_region' => $region_id]);
        }

        if (!empty($piece_id)) {
            $builder->andWhere("store.manage_piece = :manage_piece:", ['manage_piece' => $piece_id]);
        }

        return $builder;
    }

    /**
     * 释放列表组合数据
     * @param $list
     * @return mixed
     */
    public function combinationReleaseList($list)
    {
        $release_submit_ids = array_values(array_filter(array_column($list, 'release_submit_id')));

        $release_submit_list = $this->getStaffList($release_submit_ids);
        $release_submit_list = array_column($release_submit_list, null, 'staff_info_id');

        $store_ids = array_column($list, 'sys_store_id');

        $sys_store_list = (new SysStoreService())->getStoreList($store_ids);
        $sys_store_list = array_column($sys_store_list, null, 'id');

        $province_list  = SysProvinceModel::find()->toArray();
        $province_list  = array_column($province_list, null, 'code');
        $hire_type_list = array_column((new \App\Services\SysService())->getHireTypeList(), 'label', 'value');

        foreach ($list as &$value) {
            $value['id']              = explode(',', $value['id']);
            $value['hold_id']         = explode(',', $value['hold_id']);
            $value['hold_reason_arr'] = explode(',', $value['hold_reasons']);
            $value['version']         = explode(',', $value['version']);
            switch ($value['release_state']) {
                case 1:
                    $value['release_state_name'] = self::$t->_('franchisee_status_0');
                    break;
                case 2:
                    $value['release_state_name'] = self::$t->_('paid');
                    break;
                default:
                    $value['release_state_name'] = '';
            }
            if (!empty($value['release_submit_id'])) {
                if ($value['release_submit_id'] == 10000 || $value['release_submit_id'] == 10003) {
                    $value['staff_submit_name'] = 'system auto';
                } else {
                    $submit_name                = $release_submit_list[$value['release_submit_id']]['name'] ?? '';
                    $value['staff_submit_name'] = $submit_name . ' ' . $value['release_submit_id'];
                }
            } else {
                $value['staff_submit_name'] = "";
            }

            $value['area_name'] = '';
            if ($value['sys_store_id'] == '-1') {
                $value['store_name'] = GlobalEnums::HEAD_OFFICE;
            } else {
                $value['store_name'] = $this->showStoreName($value['sys_store_id']);
                $province            = $province_list[$sys_store_list[$value['sys_store_id']]['province_code']] ?? [];
                if (!empty($province)) {
                    $value['area_name'] = self::$manage_areas[$province['manage_geography_code']];
                }
            }

            $value['staff_info_name'] = $value['staff_info_name'] . ' ' . $value['staff_info_id'];
            $value['source']          = explode(',', $value['source']);

            $value['manage_region_name'] = $this->showRegionName($value['manage_region']);
            $value['manage_piece_name']  = $this->showPieceName($value['manage_piece']);
            $value['manage_piece_name']  = $this->showPieceName($value['manage_piece']);
            $value['hire_type_text']     = $hire_type_list[$value['hire_type']] ?? '';
        }
        return $list;
    }

    /**
     * release 下载
     * @param $params
     * @return array
     */
    public function exportReleaseList($params)
    {
        $builder = $this->getReleaseListBuilder($params);
        $builder->groupBy('hm.staff_info_id');
        $builder->orderBy('hm.handle_progress ASC');
        $list       = $builder->getQuery()->execute()->toArray();
        $items      = !empty($list) ? $this->combinationReleaseList($list) : [];
        $export_row = [];

        foreach ($items as $key => $value) {
            $staff_state_text  = ($value['staff_state'] == 1 && $value['wait_leave_state'] == 1) ? self::$t->_('wait_leave_state_' . $value['wait_leave_state']) : self::$t->_('staff_state_' . $value['staff_state']);
            $hold_reasons      = array_filter(explode(',', $value['hold_reasons']));
            $hold_reasons_text = '';
            foreach ($hold_reasons as $k => $v) {
                $hold_reasons_text .= ' ' . self::$t->_($v);
            }
            $export_row[] = [
                $value['staff_info_id'],        //员工
                $value['staff_info_name'],      //员工
                implode(',', $value['hold_id']),//HoldID
                $value['store_name'],           //网点
                $value['manage_region_name'],   //所属大区
                $value['manage_piece_name'],    //所属片区
                $value['area_name'],            //所属区域
                $value['hire_type_text'],       //雇佣类型
                $value['staff_submit_name'],    //操作人
                $value['release_state_name'],   //处理状态
                $hold_reasons_text,
                $staff_state_text,     // 在职状态
                $value['release_time'],//最后更新时间
            ];
        }

        $head = [
            'staff_info_id',                                   //'员工',
            self::$t->_('staff'),                              //'员工',
            self::$t->_('hold_id'),                            //'HoldID',
            self::$t->_('dot'),                                //'网点',
            self::$t->_('store_area'),                         //所属大区
            self::$t->_('store_district'),                     //所属片区
            self::$t->_('hr_probation_field_store_area_text'), //'所属区域',
            self::$t->_('hire_type'),                          //雇佣类型
            self::$t->_('submit_name'),                        //'操作人',
            self::$t->_('handle_progress'),                    //'处理状态',
            self::$t->_('hold_reason'),                        // hold原因
            self::$t->_('staff_state'),                        //'在职状态',
            self::$t->_('latest_update_time'),                 //'最后更新时间',
        ];

        $fileName   = gmdate('YmdHis', time() + ($this->timeOffset) * 3600) . '_release_list.xls';
        $excel_file = $this->exportExcel($head, $export_row, $fileName);

        $flashOss = new FlashOss();
        //每个国家有每个国家的名字
        $json_url_name = self::HOLD_MANAGER_OSS_PATH . '/' . $fileName;
        $flashOss->uploadFile($json_url_name, $excel_file['data']);
        $download_url = $flashOss->signUrl($json_url_name);

        return ['url' => $download_url];
    }

    /**
     * 更新释放
     * @param $params
     * @return bool
     */
    public function updateRelease($params) {
        $type = $params['type'];
        $staff_info_id = $params['staff_info_id'];
        $release_state = $params['release_state'];
        $release_submit_id = $params['release_submit_id'];

        $current_time = date('Y-m-d H:i:s');
        try {
            $db = $this->getDI()->get('db_backyard');
            $sql = "update hold_staff_manage set release_state = {$release_state}, release_time = '{$current_time}', update_at = '{$current_time}', release_submit_id = {$release_submit_id} where staff_info_id={$staff_info_id} and type = {$type}";
            $update_result = $db->execute($sql);

            if(!$update_result) {
                $this->logger->error(['function' => 'HoldManageService-uploadHold','params' => $params, 'result' => $update_result]);
                return  false;
            }

            return true;
        } catch (Exception $e) {
            $this->logger->error(['function' => 'HoldManageService-updateRelease','message' =>$e->getMessage(),'line' => $e->getLine(),'params' => $params]);
            return  false;
        }
    }

    /**
     * 员工hold原因
     * @param $staff_info_id
     * @return array
     */
    public function getStaffReasonList($staff_info_id) {
        try {
            $staff_hold_list = HoldStaffManageModel::find([
                                    'columns' => 'staff_info_id,hold_reason',
                                    'conditions' => 'staff_info_id = :staff_info_id: and is_delete = 0',
                                    'bind' => ['staff_info_id' => $staff_info_id],
                                ])->toArray();
            $reason_list = [];
            foreach ($staff_hold_list as $key => $value) {
                switch($value['hold_reason']){
                    case 'off_3_days':
                        $reason_list[] = 8;
                        break;
                    case 'incomplete_resignation_procedures':
                        $reason_list[] = 10;
                        break;
                    case 'fail_to_return_public_property':
                        $reason_list[] = 11;
                        break;
                    case 'corruption/theft_of_public_property':
                        $reason_list[] = 14;
                        break;
                    case 'suspicion_of_corruption/theft_of_public_property':
                        $reason_list[] = 5;
                        break;
                    case 'violation_of_law':
                        $reason_list[] = 6;
                        break;
                    case 'fail_to_submit_public_funds':
                        $reason_list[] = 9;
                        break;
                    case 'fights_with_employees':
                        $reason_list[] = 3;
                        break;
                    case 'bad_for_the_company':
                        $reason_list[] = 4;
                        break;
                    case 'work_for_lalamove':
                        $reason_list[] = 12;
                        break;
                    case 'report_false_mileage':
                        $reason_list[] = 13;
                        break;
                    case 'unable_to_confirm_employment':
                        $reason_list[] = 1;
                        break;
                    case 'off_8_days':
                        $reason_list[] = 17;
                        break;
                    case 'less_than_12_days':
                        $reason_list[] = 16;
                        break;
                    case 'incomplete_resignation_procedures':
                        $reason_list[] = 2;
                        break;
                    case 'fail_to_submit_public_funds_on_time':
                        $reason_list[] = 15;
                        break;
                    case 'investigate_the_impersonation':
                        $reason_list[] = 20;
                        break;
                    default:
                        $reason_list[] = 7;
                        break;
                }
            }
            return $reason_list;
        } catch (Exception $e) {
            $this->logger->error(['function' => 'HoldManageService-getStaffReasonList','message' =>$e->getMessage(),'line' => $e->getLine(),'params' => $staff_info_id]);
            return [];
        }
    }

    /**
     * hold 筛选条件
     * @return array
     */
    public function getScreeningList() {
        $hold_reason = $this->getHoldReason();
        $handle_people = $this->getHandlePeople();
        $handle_opinion = $this->getHandleOpinion();
        $tab_list = $this->getTabList();
        $remark_list = $this->getRemarkList();
        $remark_source = $this->getHoldSource();
        $handle_progress = $this->getHandle_progress();
        $hire_type_list    = (new SysService())->getHireTypeList();
        $manage_areas_desc = [];
        foreach(self::$manage_areas as $k=>$v){
            $manage_areas_desc[] = [
                'key' => $k,
                'value' => $v,
            ];
        }

        return [
            'handle_people'   => $handle_people,     //处理人列表
            'area_list'       => $manage_areas_desc, //区域列表
            'hold_reason'     => $hold_reason,       //hold原因
            'handle_opinion'  => $handle_opinion,
            'tab_list'        => $tab_list,
            'remark_list'     => $remark_list,
            'remark_source'   => $remark_source,
            'handle_progress' => $handle_progress,
            'hire_type_list'  => $hire_type_list,
        ];
    }

    /**
     * hold reason hold 原因 hold工资/提成和工资+提成原因展示不一样
     * @return array
     */
    public function getHoldReason() {
        $hold_reason = [
            //工资
            '1' => [
                [
                    'key' => 'off_3_days',
                    'value' => self::$t->_('stay_away_from_work'),//调整为: 连续旷工三天以上=>连续旷工
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '8',
                ],
                [
                    'key' => 'incomplete_resignation_procedures',
                    'value' => self::$t->_('incomplete_resignation_procedures'),//离职手续不全
                    'handle_people' => 'HRO',
                    'reason_id' => '10',
                ],
                [
                    'key' => 'fail_to_return_public_property',
                    'value' => self::$t->_('fail_to_return_public_property'),//公共财产未归还
                    'handle_people' => 'HRO_Purchase',
                    'reason_id' => '11',
                ],
                [
                    'key' => 'corruption/theft_of_public_property',
                    'value' => self::$t->_('corruption/theft_of_public_property'),//贪污腐败/偷窃公有财务
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '14',
                ],
                [
                    'key' => 'suspicion_of_corruption/theft_of_public_property',
                    'value' => self::$t->_('suspicion_of_corruption/theft_of_public_property'),//怀疑贪污腐败/偷窃公有财物
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '5',
                ],
                [
                    'key' => 'violation_of_law',
                    'value' => self::$t->_('violation_of_law'),//违法乱纪，违反法律法规
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '6',
                ],
                [
                    'key' => 'fail_to_submit_public_funds',
                    'value' => self::$t->_('fail_to_submit_public_funds'),//未缴纳公款
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '9',
                ],
                [
                    'key' => 'fights_with_employees',
                    'value' => self::$t->_('fights_with_employees'),//发生与员工/客户争执打架斗殴的行为
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '3',
                ],
                [
                    'key' => 'bad_for_the_company',
                    'value' => self::$t->_('bad_for_the_company'),//散布谣言或对公司造成不利影响
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '4',
                ],
                [
                    'key' => 'work_for_lalamove',
                    'value' => self::$t->_('work_for_lalamove'),//工作时间跑 lalamove及其他公司的兼职
                    'handle_people' => 'HRBP',
                    'reason_id' => '12',
                ],
                [
                    'key' => 'report_false_mileage',
                    'value' => self::$t->_('report_false_mileage'),//虚报里程
                    'handle_people' => 'HRBP',
                    'reason_id' => '13',
                ],
                [
                    'key' => 'unable_to_confirm_employment',
                    'value' => self::$t->_('unable_to_confirm_employment'),//无法确认是否在职，无工作记录
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '1',
                ],
                [
                    'key' => 'received_warning_letters',
                    'value' => self::$t->_('received_warning_letters'),//收到警告书
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '18',
                ],
                [
                    'key' => 'false_finished_del_v1',
                    'value' => self::$t->_('false_finished_del_v1'),//虚假妥投
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '19',
                ],
                [
                    'key' => 'provide_false_information',
                    'value' => self::$t->_('provide_false_information'),//向公司提供虚假信息（待确认）
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '20',
                ],
                [
                    'key' => 'investigate_the_impersonation',
                    'value' => self::$t->_('investigate_the_impersonation'),//调查冒名顶替
                    'handle_people' => 'HRO',
                    'reason_id' => '21',
                ],
            ],
            //提成
            '2' => [
                [
                    'key' => 'off_8_days',
                    'value' => self::$t->_('off_8_days'),//请假和旷工超过8天
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '17',
                ],
                [
                    'key' => 'incomplete_resignation_procedures',
                    'value' => self::$t->_('incomplete_resignation_procedures'),//离职手续不全
                    'handle_people' => 'HRO',
                    'reason_id' => '10',
                ],
                [
                    'key' => 'fail_to_return_public_property',
                    'value' => self::$t->_('fail_to_return_public_property'),//公共财产未归还
                    'handle_people' => 'HRO_Purchase',
                    'reason_id' => '11',
                ],
                [
                    'key' => 'corruption/theft_of_public_property',
                    'value' => self::$t->_('corruption/theft_of_public_property'),//贪污腐败/偷窃公有财务
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '14',
                ],
                [
                    'key' => 'suspicion_of_corruption/theft_of_public_property',
                    'value' => self::$t->_('suspicion_of_corruption/theft_of_public_property'),//怀疑贪污腐败/偷窃公有财物
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '5',
                ],
                [
                    'key' => 'violation_of_law',
                    'value' => self::$t->_('violation_of_law'),//违法乱纪，违反法律法规
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '6',
                ],
                [
                    'key' => 'fail_to_submit_public_funds',
                    'value' => self::$t->_('fail_to_submit_public_funds'),//未缴纳公款
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '9',
                ],
                [
                    'key' => 'fights_with_employees',
                    'value' => self::$t->_('fights_with_employees'),//发生与员工/客户争执打架斗殴的行为
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '3',
                ],
                [
                    'key' => 'bad_for_the_company',
                    'value' => self::$t->_('bad_for_the_company'),//散布谣言或对公司造成不利影响
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '4',
                ],
                [
                    'key' => 'work_for_lalamove',
                    'value' => self::$t->_('work_for_lalamove'),//工作时间跑 lalamove及其他公司的兼职
                    'handle_people' => 'HRBP',
                    'reason_id' => '12',
                ],
                [
                    'key' => 'report_false_mileage',
                    'value' => self::$t->_('report_false_mileage'),//虚报里程
                    'handle_people' => 'HRBP',
                    'reason_id' => '13',
                ],
                [
                    'key' => 'less_than_12_days',
                    'value' => self::$t->_('less_than_12_days'),//未通过试用期后离职且当月出勤少于12天
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '16',
                ],
                [
                    'key' => 'fail_to_submit_public_funds_on_time',
                    'value' => self::$t->_('fail_to_submit_public_funds_on_time'),//未按时缴纳公款
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '15',
                ],
                [
                    'key' => 'received_warning_letters',
                    'value' => self::$t->_('received_warning_letters'),//收到警告书
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '18',
                ],
                [
                    'key' => 'false_finished_del_v1',
                    'value' => self::$t->_('false_finished_del_v1'),//虚假妥投
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '19',
                ],
                [
                    'key' => 'provide_false_information',
                    'value' => self::$t->_('provide_false_information'),//向公司提供虚假信息（待确认）
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '20',
                ],
                [
                    'key' => 'investigate_the_impersonation',
                    'value' => self::$t->_('investigate_the_impersonation'),//调查冒名顶替
                    'handle_people' => 'HRO',
                    'reason_id' => '21',
                ],
            ],
            //工资+提成
            '3' => [
                [
                    'key' => 'incomplete_resignation_procedures',
                    'value' => self::$t->_('incomplete_resignation_procedures'),//离职手续不全
                    'handle_people' => 'HRO',
                    'reason_id' => '10',
                ],
                [
                    'key' => 'fail_to_return_public_property',
                    'value' => self::$t->_('fail_to_return_public_property'),//公共财产未归还
                    'handle_people' => 'HRO_Purchase',
                    'reason_id' => '11',
                ],
                [
                    'key' => 'corruption/theft_of_public_property',
                    'value' => self::$t->_('corruption/theft_of_public_property'),//贪污腐败/偷窃公有财务
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '14',
                ],
                [
                    'key' => 'suspicion_of_corruption/theft_of_public_property',
                    'value' => self::$t->_('suspicion_of_corruption/theft_of_public_property'),//怀疑贪污腐败/偷窃公有财物
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '5',
                ],
                [
                    'key' => 'violation_of_law',
                    'value' => self::$t->_('violation_of_law'),//违法乱纪，违反法律法规
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '6',
                ],
                [
                    'key' => 'fail_to_submit_public_funds',
                    'value' => self::$t->_('fail_to_submit_public_funds'),//未缴纳公款
                    'handle_people' => 'security_HRBP_ER',
                    'reason_id' => '9',
                ],
                [
                    'key' => 'fights_with_employees',
                    'value' => self::$t->_('fights_with_employees'),//发生与员工/客户争执打架斗殴的行为
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '3',
                ],
                [
                    'key' => 'bad_for_the_company',
                    'value' => self::$t->_('bad_for_the_company'),//散布谣言或对公司造成不利影响
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '4',
                ],
                [
                    'key' => 'work_for_lalamove',
                    'value' => self::$t->_('work_for_lalamove'),//工作时间跑 lalamove及其他公司的兼职
                    'handle_people' => 'HRBP',
                    'reason_id' => '12',
                ],
                [
                    'key' => 'report_false_mileage',
                    'value' => self::$t->_('report_false_mileage'),//虚报里程
                    'handle_people' => 'HRBP',
                    'reason_id' => '13',
                ],
                [
                    'key' => 'received_warning_letters',
                    'value' => self::$t->_('received_warning_letters'),//收到警告书
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '18',
                ],
                [
                    'key' => 'false_finished_del_v1',
                    'value' => self::$t->_('false_finished_del_v1'),//虚假妥投
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '19',
                ],
                [
                    'key' => 'provide_false_information',
                    'value' => self::$t->_('provide_false_information'),//向公司提供虚假信息（待确认）
                    'handle_people' => 'HRBP_ER',
                    'reason_id' => '20',
                ],
                [
                    'key' => 'investigate_the_impersonation',
                    'value' => self::$t->_('investigate_the_impersonation'),//调查冒名顶替
                    'handle_people' => 'HRO',
                    'reason_id' => '21',
                ],
            ],
        ];

        return $hold_reason;
    }

    /**
     * hold 处理人列表
     * @return array
     */
    public function getHandlePeople() {
        $people = [];
        foreach (self::$handle_people as $k => $v) {
            $people[] = [
                'key' => $v,
                'value' => self::$t->_($v),
            ];
        }
        return $people;
    }

    public function getHandle_progress() {
        $handle_progress = [];
        foreach(self::$handle_progress as $k => $v){
            $handle_progress[] = [
                'key' => $v,
                'value' => self::$t->_($v),
            ];
        }
        return $handle_progress;
    }

    /**
     * handle opinioin 处理意见
     * @return array
     */
    public function getHandleOpinion() {
        $progress = [];
        foreach(self::$handle_opinion as $k => $v){
            $progress[] = [
                'key' => $v,
                'value' => self::$t->_($v),
            ];
        }
        return $progress;
    }

    /**
     * hold source hold来源
     * @return array
     */
    public function getHoldSource() {
        $source = [];
        foreach(self::$hold_source as $k => $v){
            $source[] = [
                'key' => $k,
                'value' => self::$t->_($v),
            ];
        }
        return $source;
    }

    /**
     * tab list 前端展示
     * @return array
     */
    public function getTabList() {
        $tab_list = [
            [
                'key' => 'salary_hold_management',
                'value' => '工资hold处理',
            ],
            [
                'key' => 'incentive_hold_management',
                'value' => '提成hold处理',
            ],
            [
                'key' => 'pay_salary',
                'value' => '工资释放',
            ],
            [
                'key' => 'pay_incentive',
                'value' => '提成释放',
            ],
        ];
        return $tab_list;
    }

    /**
     * 工资发放备注
     * @return array
     */
    public function getRemarkList() {
        $remark_list = [
            [
                'key' => '101',
                'value' => self::$t->_('the_salary_not_be_paid'),
            ],
            [
                'key' => '102',
                'value' => self::$t->_('the_salary_current_period'),
            ],
        ];
        return $remark_list;
    }

    /**
     * 获取指定工号信息
     * @param array $staff_info_ids
     * @return array
     */
    public function getStaffList($staff_info_ids = []) {
        if(empty($staff_info_ids)) {
            return [];
        }

        try {
            $staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name,identity,mobile,job_title,sys_store_id,sys_department_id,formal,state,wait_leave_state,node_department_id',
                'conditions' => 'staff_info_id in ({staff_info_ids:array})',
                'bind' => ['staff_info_ids' => $staff_info_ids],
            ])->toArray();

            return $staff_list;
        } catch (Exception $e) {
            $this->logger->error(['function' => 'HoldManageService-getStaffList','message' =>$e->getMessage(),'line' => $e->getLine(),'params' => $staff_info_ids]);
            return [];
        }
    }

    /**
     * 获取员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfo($staff_info_id) {
        if(empty($staff_info_id)) {
            return [];
        }
        $staff_detail = [];
        try {
            $staff_info = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id,name,identity,mobile,job_title,sys_store_id,sys_department_id,formal,state,wait_leave_state,node_department_id',
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => ['staff_info_id' => $staff_info_id],
            ]);

            if(!empty($staff_info)) {
                $staff_info = $staff_info->toArray();
                $position_category_list = HrStaffInfoPositionModel::find([
                    'columns' => 'staff_info_id,position_category',
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $staff_info_id],
                ])->toArray();

                $position_category_text = '';
                if(!empty($position_category_list)) {
                    $position_category = [];
                    foreach ($position_category_list as $key => $value) {
                        $position_category[] = self::$t->_('role_'.$value['position_category']);
                    }
                    $position_category_text = implode(',', $position_category);
                }

                $job_title = (new HrJobTitleService())->getJobTitleDetail($staff_info['job_title']);
                $store_name = '';
                if($staff_info['sys_store_id'] == '-1'){
                    $staff_info['store_name'] = GlobalEnums::HEAD_OFFICE;
                } else {
                    $store = (new SysStoreService())->getStoreById($staff_info['sys_store_id']);
                    $store_name = $store['name'] ?? '';
                }
                $staff_detail = [
                    'staff_info_id' => $staff_info_id,
                    'name' => $staff_info['name'],
                    'mobile' => $staff_info['mobile'],
                    'job_title' => $staff_info['job_title'],
                    'job_title_name' => $job_title['job_name'],
                    'sys_store_id' => $staff_info['sys_store_id'],
                    'store_name' => $store_name,
                    'position_category_text' => $position_category_text,
                ];
            }
        } catch (Exception $e) {
            $this->logger->error(['function' => 'HoldManageService-getStaffInfo','message' =>$e->getMessage(),'line' => $e->getLine(),'params-staff_info_id' => $staff_info_id]);
        }
        return $staff_detail;
    }

    /**
     * 根据工号获取hold list
     * @param $params
     * @return array
     */
    public function getHoldByStaffInfoIds($params) {
        try {
            $staff_info_ids = $params['staff_info_ids'] ?? [];
            $type = $params['type'] ?? 1;
            $release_state = $params['release_state'] ?? [HoldStaffManageModel::HOLD_RELEASE_STATE_TODO];
            $handle_progress = $params['handle_progress'] ?? [HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED];
            if(empty($staff_info_ids)) {
                return [];
            }
            $conditions['conditions'] ='staff_info_id in ({staff_info_ids:array}) and is_delete = 0 and type = :type:';
            $conditions['bind']['staff_info_ids']    = $staff_info_ids;
            $conditions['bind']['type']    = $type;
            if (!empty($release_state) && is_array($release_state)) {
                $conditions['conditions']  .= '  and release_state in ({release_state:array})';
                $conditions['bind']['release_state'] = $release_state;
            }
            if (!empty($handle_progress) && is_array($handle_progress)) {
                $conditions['conditions']  .= '  and handle_progress in ({handle_progress:array})';
                $conditions['bind']['handle_progress'] = $handle_progress;
            }
            
            $hold_list = HoldStaffManageModel::find($conditions)->toArray();
            if(empty($hold_list)) {
                return [];
            }
            $hold_list = array_column($hold_list, null, 'staff_info_id');
            return $hold_list;
        } catch (Exception $e) {
            $this->logger->error(['function' => 'HoldManageService-getHoldByStaffInfoIds','message' =>$e->getMessage(),'line' => $e->getLine(),'params' => $params]);
            return [];
        }
    }

    /**
     * 添加到批量处理任务列表
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function addHoldManageBatchRelease($params) {

        $operator_id = $params['operator_id'];
        $release_type = $params['release_type'];
        $file_name = $params['file_name'];
        $excel_data = $params['excel_data'];
        $db = $this->getDI()->get('db_backyard');
        $count = HoldManageBatchReleaseModel::count([
            'conditions' => "operator_id = :operator_id: and release_type = :release_type: and is_del = 0 and status = 0",
            'bind' => [
                'operator_id' => $operator_id,
                'release_type' => $release_type,
            ],
        ]);
        if ($count > 0) {
            throw new BusinessException(self::$t->_('hold_manage_batch_release_error_1'));//有正在执行导入的任务，请等待导入完成后再进行上传
        }

        try {
            $excel_title = [
                self::$t->_('hold_batch_release_excel_staff_id'),
                self::$t->_('hold_batch_release_excel_release_type'),
            ];
            $excel_file = $this->exportExcel($excel_title, $excel_data, $file_name);
            $flashOss = new FlashOss();
            $json_url_name = self::HOLD_MANAGER_OSS_PATH . '/' . $file_name;
            $flashOss->uploadFile($json_url_name, $excel_file['data']);
            $file_path_oss = $flashOss->signUrl($json_url_name, 86400 * 30);
            $db->begin();
            $insert_arr = [
                'operator_id' => $operator_id,
                'release_type' => $release_type,
                'file_name' => $file_name,
                'file_path_oss' => $file_path_oss,
                'file_language' => self::$language,

            ];
            $result = $db->insertAsDict("hold_manage_batch_release", $insert_arr);
            if($result) {
                $id = $db->lastInsertId();
                $insert_detail_arr = [];
                foreach ($excel_data as $key => $value) {
                    $insert_detail_arr[] = [
                        'batch_release_id' => $id,
                        'row_content'      => json_encode($value),
                    ];
                }
                if (empty($insert_detail_arr)) {
                    throw new BusinessException(self::$t->_('no_data'));
                }

                $insert_detail_arr = array_chunk($insert_detail_arr, 1000);
                foreach ($insert_detail_arr as $v) {
                    (new HoldManageBatchReleaseDetailModel())->batch_insert($v);
                }

                $db->commit();
                return true;
            } else {
                $this->logger->error([
                    'function' => 'addHoldManageBatchRelease',
                    'message' => 'error',
                    'params' => $params,
                ]);
                $db->rollback();
                return false;
            }
        } catch (BusinessException $be) {
            $db->rollback();
            throw $be;
        }
        catch (Exception $e) {
            $db->rollback();
            $this->logger->error([
                'function' => 'addHoldManageBatchRelease',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return false;
        }
    }

    /**
     * 批量释放
     * @param $params
     * @return bool
     */
    public function batchRelease($params) {
        $release_id = $params['release_id'];
        $excel_data = $params['excel_data'] ?? [];
        $operator_id = $params['operator_id'] ?? -1;
        $type = $params['type'] ?? 1;
        $file_language = $params['file_language'];
        $release_type = 2;
        $release_state = 2;//1.待确认,2.已释放
        $staff_info_ids = [];
        $error_data = [];

        $data=[
            'error_num' => 0,
            'url' => '',
            'success_num' => 0,
        ];

        try {
            array_map(function ($value) use (&$staff_info_ids){
                $staff_info_ids[] = $value[0];
            }, $excel_data);

            $hold_list = $this->getHoldByStaffInfoIds(['staff_info_ids' => $staff_info_ids, 'type' => $type, 'release_state' => [HoldStaffManageModel::HOLD_RELEASE_STATE_TODO], 'handle_progress' => [HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED]]);

            $hold_list_processing = $this->getHoldByStaffInfoIds(['staff_info_ids' => $staff_info_ids, 'type' => $type, 'handle_progress' => [HoldStaffManageModel::HANDLE_PROGRESS_UNPROCESSED,HoldStaffManageModel::HANDLE_PROGRESS_PROCESSING]]);
            

            foreach ($excel_data as $key => $value) {
                $staff_info_id = $value[0];//工号
                $payment_markup = $value[1] == 1 ? '101' : '102';//状态
                
                if (isset($hold_list_processing[$staff_info_id])){
                    $value[] = BaseService::getTranslation($file_language)->t('hold_manage_batch_release_error_5'); //员工有未处理或处理中的hold
                    array_push($error_data, $value);
                    continue;
                }

                if(!isset($hold_list[$staff_info_id])) {
                    $value[] = BaseService::getTranslation($file_language)->t('hold_manage_batch_release_error_2'); //未找到需要释放的工号
                    array_push($error_data, $value);
                    continue;
                }

                $result = $this->updateRelease(['staff_info_id' => $staff_info_id, 'release_state' => $release_state, 'type' => $type, 'release_submit_id' => $operator_id]);
                if($result) {
                    //同步员工管理
                    $api_params = [
                        'type' => $release_type,
                        'staff_info_id' => $staff_info_id,
                        'payment_markup' => $payment_markup,
                        'operator_id' => $operator_id,
                        'stop_payment_type' => $type,
                    ];
                    $sync_hold_result = $this->syncHrisStaffHoldSvc($api_params);
                    if($sync_hold_result !== true) {
                        $value[] = BaseService::getTranslation($file_language)->t('hold_manage_batch_release_error_3'); //同步员工hold失败
                        array_push($error_data, $value);
                    }
                } else {
                    $value[] = BaseService::getTranslation($file_language)->t('hold_manage_batch_release_error_4'); //更新释放hold失败
                    array_push($error_data, $value);
                }
            }
            $data['success_num'] = count($excel_data);
            if(count($error_data) > 0) {
                $data['error_num'] = count($error_data);
                $data['success_num'] = $data['success_num'] - $data['error_num'];
                $fileName = 'hold_error_data_' . date('Ymdhis') . '_' . self::$language . date('Ymd-His') . '.xlsx';

                $excel_title = [
                    BaseService::getTranslation($file_language)->t('hold_batch_release_excel_staff_id'),
                    BaseService::getTranslation($file_language)->t('hold_batch_release_excel_release_type'),
                    BaseService::getTranslation($file_language)->t('hold_batch_release_excel_result'),
                ];
                $excel_file = $this->exportExcel($excel_title, $error_data, $fileName);
                $flashOss = new FlashOss();
                $json_url_name = self::HOLD_MANAGER_OSS_PATH . '/' . $fileName;
                $flashOss->uploadFile($json_url_name, $excel_file['data']);
                $data['url'] = $flashOss->signUrl($json_url_name, 86400 * 30);
            }

            //更新表
            $release = HoldManageBatchReleaseModel::findFirst([
                'conditions' => 'id = :id: and operator_id = :operator_id: and release_type = :release_type:',
                'bind' => [
                    'id' => $release_id,
                    'operator_id' => $operator_id,
                    'release_type' => $type,
                ],
            ]);

            $release->status = 1;
            $release->success_count = $data['success_num'];
            $release->error_count = $data['error_num'];
            $release->result_file_path_oss = $data['url'];
            $release->release_time = date("Y-m-d h:i:s");

            return $release->save();
        } catch (Exception $e) {
            $this->logger->error(['function' => 'HoldManageService-batchRelease','message' =>$e->getMessage(),'line' => $e->getLine()]);
            return false;
        }
    }

    /**
     * 批量上传释放历史列表
     * @param $params
     * @return array
     */
    public function getReleaseHistoryList($params) {
        $page_num = intval($params['page_num'] ?? 1);
        $page_size = intval($params['page_size'] ?? 20);
        $offset = $page_size * ($page_num - 1);
        try {
            $user = $params['user'];
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('
                            h.id,
                            h.operator_id,
                            h.release_type,
                            h.file_name,
                            h.file_path_oss,
                            h.status,
                            h.success_count,
                            h.error_count,
                            h.result_file_path_oss,
                            h.release_time,
                            h.created_at
                            ');
            $builder->from(['h' => HoldManageBatchReleaseModel::class]);
            $builder->where("h.is_del = :is_del:", ['is_del' => 0]);
            $builder->andWhere("h.operator_id = :operator_id:", ['operator_id' => $user['id']]);
            $builder->andWhere("h.release_type = :release_type:", ['release_type' => $params['release_type']]);
            $builder->andWhere("h.created_at >= :created_at:", ['created_at' => date("Y-m-d 00:00:00", strtotime("-30 day"))]);
            $builder_count = $builder;
            $builder->limit($page_size, $offset);
            $list = $builder->orderBy('h.id DESC')->getQuery()->execute()->toArray();

            foreach ($list as $key => $value) {
                $list[$key]['release_time'] = !empty($value['release_time']) ? date('Y-m-d H:i:s', strtotime($value['release_time']) + $this->timeOffset * 3600) : '';
                $list[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($value['created_at']) + $this->timeOffset * 3600);
                $list[$key]['status_text'] = self::$t->_('hold_manage_batch_release_status_' . $value['status']);
            }

            $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
            return [
                'items' => $list,
                'paginate' => [
                    'total_count' => !empty($totalCount) ? $totalCount->count : 0,
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                ],
            ];
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'getImportHistoryList',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return [
                'items' => [],
                'paginate' => [
                    'total_count' => 0,
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                ],
            ];
        }
    }

    /**
     * 删除历史批量释放数据
     * @param $params
     * @return bool
     */
    public function delReleaseHistory($params) {
        $operator_id = $params['user']['id'];
        $del_ids = $params['del_ids'] ?? '';
        $release_type = $params['release_type'];
        try {
            foreach ($del_ids as $key => $value) {
                $history = HoldManageBatchReleaseModel::findFirst([
                    'conditions' => 'id = :id: and operator_id = :operator_id: and release_type = :release_type:',
                    'bind' => [
                        'id' => $value,
                        'operator_id' => $operator_id,
                        'release_type' => $release_type,
                    ],
                ]);
                if (!empty($history) && $history->is_del == 0) {
                    $history->is_del = 1;
                    if ($history->save() === false) {
                        $error = '';
                        foreach ($history->getMessages() as $log) {
                            $error .= ', ' . $log;
                        }
                        $this->logger->error([
                            'function' => 'delReleaseHistory',
                            'message' => '删除历史上传数据',
                            'message_error' => $error,
                            'params' => $params,
                            'history' => $history->toArray(),
                        ]);
                    }
                } else {
                    $this->logger->info([
                        'function' => 'delReleaseHistory',
                        'message' => '删除历史上传数据-未找到数据',
                        'params' => $params,
                        'history_id' => $value,
                    ]);
                }
            }
            return true;
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'delReleaseHistory',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'params' => $params,
            ]);
            return false;
        }
    }

    //根据警告书id获取员工hold记录
    public function getHoldListByMessageWarningId($params = []) {
        $message_warning_ids = $params['message_warning_ids'] ?? [];
        $list = [];
        if(!empty($message_warning_ids)) {
            $list = HoldStaffManageModel::find([
                'conditions' => 'is_delete = 0 and message_warning_id in({message_warning_ids:array})',
                'bind' => ['message_warning_ids' => $message_warning_ids],
            ])->toArray();
        }

        return $list;
    }

    //根据工号获取员工的hold记录
    public function getHoldListByStaffInfoIds($params) {

        $staff_info_ids = $params['staff_info_ids'] ?? [];
        $staff_hold_list = [];
        if(!empty($staff_info_ids)) {
            $list = HoldStaffManageModel::find([
                'conditions' => 'staff_info_id in({staff_info_ids:array}) and release_state !=2 and is_delete = 0',
                'bind' => ['staff_info_ids' => $staff_info_ids],
            ])->toArray();

            foreach ($list as $key => $value) {
                $staff_hold_list[$value['staff_info_id']][$value['type']][] = $value;
            }
        }
        return $staff_hold_list;
    }

    /**
     * 删除警告书关联hold
     * @param $message_warning_ids
     * @return mixed
     */
    public function delHoldByMessageWarningId($message_warning_ids) {
        $db_backyard = $this->getDI()->get('db_backyard');
        $result = $db_backyard->updateAsDict("hold_staff_manage", ["is_delete" => 1], "message_warning_id in (" . $message_warning_ids . ")");
        return $result;
    }


    /**
     *
     * @param $staff_info_ids
     * @return true|void
     * @throws ValidationException
     */
    public function releaseWarningHold($staff_info_ids)
    {
        if (!isCountry('TH')) {
            throw new ValidationException('only TH');
        }

        $bind      = [
            'hold_reason'     => 'received_warning_letters',
            'release_state'   => HoldStaffManageModel::RELEASE_STATE_DONE,
            'type'            => HoldStaffManageModel::HOLD_TYPE_PERCENTAGE,//提成
        ];
        $condition = 'hold_reason = :hold_reason: and release_state != :release_state: and type = :type: and is_delete = 0';
        if (!empty($staff_info_ids)) {
            $bind['staff_info_id'] = $staff_info_ids;
            $condition             .= " and staff_info_id in ({staff_info_id:array})";
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hold.staff_info_id', 'GROUP_CONCAT(hold.id) as ids']);
        $builder->from(['hold' => HoldStaffManageModel::class]);
        $builder->innerJoin(HireTypeImportListModel::class, 'hold.staff_info_id = hire.old_staff_id', 'hire');
        $builder->andWhere($condition, $bind);
        $builder->andWhere("hire.entry_state = :entry_state:",
            ['entry_state' => HireTypeImportListModel::STATUS_EMPLOYED]);
        $warningHold = $builder->groupBy('hold.staff_info_id')->getQuery()->execute()->toArray();

        if (empty($warningHold)) {
            return true;
        }
        $handle_result    = '[{"key":"no_punishment","value":""}]';
        $hold_attachments = '[{"file_name":"system_auto.jpeg","object_url":"https:\/\/sai.flashexpress.com\/workOrder\/1589542185-232b8cc3a4ec473e81408f30a36dee50.jpeg","object_key":"workOrder\/1589542185-232b8cc3a4ec473e81408f30a36dee50.jpeg"}]';
        $hold_remark      = 'Transfer to IC to process hold automatically';


        $leaveManageService = new LeaveManagerService();
        foreach ($warningHold as $item) {
            $otherHold = HoldStaffManageModel::findFirst([
                'columns'    => ['id'],
                'conditions' => 'staff_info_id = :staff_info_id: and hold_reason != :hold_reason: and handle_progress != :handle_progress: and type = :type: and  is_delete = 0',
                'bind'       => [
                    'staff_info_id'   => $item['staff_info_id'],
                    'hold_reason'     => 'received_warning_letters',
                    'handle_progress' => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                    'type'            => HoldStaffManageModel::HOLD_TYPE_PERCENTAGE,
                ],
            ]);

            $where                          = 'staff_info_id = ' . $item['staff_info_id'] . ' and type = ' . HoldStaffManageModel::HOLD_TYPE_PERCENTAGE . ' and id in (' . $item['ids'] . ') ';
            $updateData                     = [
                'handle_progress'   => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                'release_submit_id' => 10003,
            ];
            $updateData['handle_result']    = $handle_result;
            $updateData['update_at']        = date('Y-m-d H:i:s');
            $updateData['hold_attachments'] = $hold_attachments;
            if (empty($otherHold)) {
                $where                       = 'staff_info_id = ' . $item['staff_info_id'] . ' and type = ' . HoldStaffManageModel::HOLD_TYPE_PERCENTAGE .' and release_state != '.HoldStaffManageModel::RELEASE_STATE_DONE;
                $updateData['release_state'] = HoldStaffManageModel::RELEASE_STATE_DONE;
                $updateData['release_time']  = date('Y-m-d H:i:s');

                $params = [
                    'operator_id'       => '-1',
                    'type'              => 2,                       //(1 hold ; 2 释放)
                    'staff_info_id'     => $item['staff_info_id'],  //员工id
                    'payment_markup'    => '102',
                    'stop_payment_type' => HoldStaffManageModel::HOLD_TYPE_PERCENTAGE,
                ];
                $res    = $leaveManageService->getApiDatass('hris', '', 'staff_hold', self::$language, $params);

                if ($res !== true) {
                    $this->logger->error(['params' => $item, 'releaseWarningHold' => 'error']);
                    continue;
                }
            }
            $db = HoldStaffManageModel::beginTransaction($this);
            foreach (explode(',', $item['ids']) as $hid) {
                (new HoldHistoryModel())->insert_record([
                    'hold_no'          => $hid,
                    'staff_submit_id'  => 10003,
                    'handle_progress'  => HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED,
                    'handle_result'    => $handle_result,
                    'hold_attachments' => $hold_attachments,
                    'hold_remark'      => $hold_remark,
                    'hold_time'        => date('Y-m-d H:i:s'),
                ]);
            }
            $this->getDI()->get('db_backyard')->updateAsDict("hold_staff_manage", $updateData, $where);
            $db->commit();
        }
        return true;
    }

    protected function getLessThanLeaveSource(): array
    {
        $leave_source_default = [
            LeaveManagerService::LEAVE_SOURCE_OFF3DAYS,
            LeaveManagerService::LEAVE_SOURCE_NOTPAY,
            LeaveManagerService::LEAVE_SOURCE_BATCH,
            LeaveManagerService::LEAVE_SOURCE_CRIMINAL,
            LeaveManagerService::LEAVE_SOURCE_HRIS_EDIT,
        ];

        $leave_source_special = [
            LeaveManagerService::LEAVE_SOURCE_BACKYARD,
            LeaveManagerService::LEAVE_SOURCE_OTHER,
        ];
        return [$leave_source_default, $leave_source_special];
    }


    public function buildResignLessThan($builder)
    {
        [$leave_source_default,$leave_source_special] = $this->getLessThanLeaveSource();
        $builder->andWhere("i.leave_date >= '2024-06-01' ");
        $builder->andWhere("i.leave_source in ({leave_source_default:array}) or (i.leave_source in ({leave_source_special:array})  and l.apply_resign_time is not null and datediff(i.leave_date,DATE_FORMAT(CONVERT_TZ(l.apply_resign_time, '+00:00', '{$this->timeZone}'),'%Y-%m-%d')) < 30 )",
            ['leave_source_default' => $leave_source_default,'leave_source_special'=>$leave_source_special]);
        return $builder;
    }

    public function getResignEarly($params): array
    {
        $size   = $params['size'] ?? 20;
        $page   = $params['page'] ?? 1;
        $offset = $size * ($page - 1);


        $result = ['list' => []];

        $join_condition = 'i.staff_info_id = l.staff_info_id ';
        $builder        = $this->modelsManager->createBuilder();

        $builder->from(['i' => HrStaffInfoModel::class]);
        $builder->leftJoin(LeaveManagerModel::class, $join_condition, 'l');
        $builder = $this->buildResignLessThan($builder);
        $builder->andWhere("l.is_rehire = 0 and i.formal = :formal: and i.is_sub_staff = 0  and i.state = :state: and i.hire_type in ({hire_type:array})",
            [
                'formal'    => HrStaffInfoModel::FORMAL_EDITING,
                'state'     => HrStaffInfoModel::STATE_RESIGN,//离职
                'hire_type' => [
                    HrStaffInfoModel::HIRE_TYPE_1,
                    HrStaffInfoModel::HIRE_TYPE_2,
                    HrStaffInfoModel::HIRE_TYPE_3,
                    HrStaffInfoModel::HIRE_TYPE_4,
                ],
            ]);

        if (!empty($params['staff_info_id'])) {
            $builder->andWhere("i.staff_info_id = :staff_info_id:", ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['job_title'])) {
            $builder->andWhere("i.job_title = :job_title:", ['job_title' => $params['job_title']]);
        }

        //所属部门
        if (!empty($params['department_id'])) {
            $deptIds = [];
            if (!empty($params['is_sub_department'])) {
                $deptIds = (new SysDepartmentService())->getChildrenListByDepartmentId($params['department_id'], true);
            }
            $deptIds[] = $params['department_id'];
            $builder->andWhere("i.node_department_id IN ({department_ids:array})", ['department_ids' => $deptIds]);
        }

        if (!empty($params['store_id'])) {
            $builder->andWhere("i.sys_store_id = :store_id:", ['store_id' => $params['store_id']]);
        }

        if (!empty($params['start_date'])) {
            $builder->andWhere("i.leave_date >= :start_date:", ['start_date' => $params['start_date']]);
        }

        if (!empty($params['end_date'])) {
            $builder->andWhere("i.leave_date <= :end_date:",
                ['end_date' => $params['end_date']]);
        }
        $count           = $builder->columns('count(1) as count')->getQuery()->getSingleResult()->toArray();
        $result['count'] = intval($count['count']);

        if (empty($result['count'])) {
            return $result;
        }

        //查询列表
        $builder->columns([
            'i.staff_info_id',
            'i.name as staff_name',
            'i.job_title',
            'i.node_department_id',
            'i.sys_store_id',
            'i.leave_source',
            'i.leave_reason',
            'i.leave_date',
            'i.working_country',
            'l.apply_resign_time',
            "datediff(i.leave_date,DATE_FORMAT(CONVERT_TZ(l.apply_resign_time, '+00:00', '{$this->timeZone}'),'%Y-%m-%d')) as df",
        ]);

        $builder->limit($size, $offset);

        $builder->orderBy('i.leave_date desc');

        $result['list'] = $builder->getQuery()->execute()->toArray();
        if (empty($result['list'])) {
            return $result;
        }
        //获取工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');
        $leaveManageService = new LeaveManagerService();
        $leaveManageListService = new LeaveManageListService();
        foreach ($result['list'] as &$item) {
            $item['hold_advance_resign_days'] = in_array($item['job_title'],
                $this->holdAdvanceResignPosition) ? $this->holdAdvanceResignPositionDays : 30;
            $item['apply_resign_time']        = $item['apply_resign_time'] ? show_time_zone($item['apply_resign_time']) : '';
            $item['leave_date']               = substr($item['leave_date'], 0, 10);
            $item['leave_source_text']        = self::$t->_($leaveManageService->getLeaveSource($item['leave_source']));
            $item['leave_reason_text']        = $item['leave_source'] == LeaveManagerService::LEAVE_SOURCE_OTHER ? '' : self::$t->_($leaveManageListService->getReasonKeyByCode($item['leave_reason']));
            $item['work_country_text']        = $workingCountryList[$item['working_country']] ?? '';
            $item['job_name']                 = $this->showJobTitleName($item['job_title']);
            $item['store_name']               = $this->showStoreName($item['sys_store_id']);
            $item['department_name']          = $this->showDepartmentName($item['node_department_id']);
        }
        return $result;
    }

    /**
     * 异步释放hold
     * @param $params
     * @return mixed
     */
    public function pushAsyncReleaseHold($params)
    {
        $data = json_encode($params);
        $this->getDI()->get('logger')->write_log('pushAsyncReleaseHold  ' . $data, 'info');
        return $this->getDI()->get('redis')->lpush(RedisListEnums::ASYNC_RELEASE_HOLD, $data);
    }

    /**
     * 获取员工 非已处理的 hold信息
     * @param $staffIdsArr
     * @param $columns
     * @return array
     */
    public function getStaffHoldInfoIncomplete($staffIdsArr, $columns = '*')
    {
        if(empty($staffIdsArr)) {
            return [];
        }
        $conditions              = 'staff_info_id in ({staff_ids:array}) and type = :type: and handle_progress != :handle_progress: and is_delete = :is_delete:';

        $bind['staff_ids']       = $staffIdsArr;
        $bind['type']            = HoldStaffManageModel::HOLD_TYPE_PERCENTAGE;
        $bind['handle_progress'] = HoldStaffManageModel::HOLD_HANDLE_PROGRESS_PROCESSED;
        $bind['is_delete']       = HoldStaffManageModel::IS_DELETE_NO;

        return HoldStaffManageRepository::getHoldInfo($columns, $conditions, $bind);
    }

    public function getHoldReasonList($local, $params)
    {
        $hold_reason = $this->getHoldReason();
        $hold_reason_to_id = [];
        //获取hold 原因 key 和 id 映射
        foreach ($hold_reason as $oneType) {
            foreach ($oneType as $onReason){
                $hold_reason_to_id[$onReason['key']] = $onReason['reason_id'];
            }
        }
        BaseService::setLanguage($local['locale']??getCountryDefaultLang());

        $holdReasonList = [];
        foreach ($hold_reason_to_id as $key => $reasonId) {
            $reason['key'] = $reasonId;
            $reason['label'] = self::$t->_($key);
            $holdReasonList[] = $reason;
        }

        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => $holdReasonList,
        ];
    }



}