<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\AttendanceDataLogModel;
use App\Models\backyard\HrStaffInfoExtendModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SickLeaveCertificateDetailModel;
use App\Models\backyard\SickLeaveCertificateModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffAuditModel;
use App\Library\Enums\GlobalEnums;
use App\Models\backyard\StaffWaitingLeaveSendModel;
use App\Models\backyard\SysStoreModel;
use App\Library\Enums\ApprovalEnums;
use App\Models\backyard\StaffLeaveOperateLogModel;
use App\Models\backyard\StaffLeaveOperateLogFileModel;
use App\Models\backyard\StaffLeaveRemainDaysModel;

class LeaveBllService extends BaseService
{

    public $failNum;
    public $successNum;

    //假期额度查询页面 获取额度详情列表
    public function annualDetail($param)
    {
        $params = [
            'staff_id' => $param['staff_info_id'],
        ];
        $return = [];
        //新增员工信息
        $staffServer            = new HrStaffService();
        $staffInfo              = $staffServer->getStaffInfosMap([$param['staff_info_id']]);
        $staffInfo              = empty($staffInfo[$param['staff_info_id']]) ? [] : $staffInfo[$param['staff_info_id']];
        $staffInfo['hire_date'] = empty($staffInfo['hire_date']) ? '' : date('Y-m-d', strtotime($staffInfo['hire_date']));
        //迁移账号 清空了计算年假日期
        if(!empty($staffInfo['annual_date']) && $staffInfo['annual_date'] == HrStaffInfoExtendModel::ANNUAL_STOP_DATE){
            $staffInfo['annual_date'] = '';
        }
        if(is_null($staffInfo['annual_date'])){
            $staffInfo['annual_date'] = $staffInfo['hire_date'];
        }
        $return['staff_info']   = $staffInfo;
        $return['detail_list']  = [];
        $return['is_permission'] = false;
        $apiClient              = new ApiClient('by', '', 'annual_detail', self::$language);
        $apiClient->setParamss($params);
        $result = $apiClient->execute();
        $this->getDI()->get('logger')->write_log(['annualDetail' => $result], 'info');
        if (isset($result['code']) && $result['code'] == 1) {
            $return['detail_list'] = $result['data']['detail_list'];
            $return['is_permission'] = $result['data']['is_permission'] ?? false;
        }
        //马来 特殊逻辑 待离职 预发
        if(!empty($result['data']['waiting_list'])){
            $return['waiting_list'] = $result['data']['waiting_list'];
        }
        return $return;
    }

    //计算日期修改 有可能为空
    public function annualDateEdit($param)
    {
        $exist = HrStaffInfoExtendModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $param['staff_info_id']],
        ]);
        //有问题
        if (empty($exist)) {
            throw new ValidationException('no extend info ');
        }
        //不能比入职日期小
        if(!empty($param['date_at'])){
            $staffInfo = HrStaffInfoModel::findFirst("staff_info_id = {$param['staff_info_id']}");
            if(empty($staffInfo) || empty($staffInfo->hire_date)){
                throw new ValidationException('staff info error');
            }
            $hireDate = strtotime($staffInfo->hire_date);
            $editDate = strtotime($param['date_at']);
            if($editDate < $hireDate){
                throw new ValidationException('can not before hire date');
            }
        }
        $before = $exist->annual_date;
        //如果 没有日期 说明是清空
        if (empty($param['date_at'])) {
            $exist->annual_date = '1970-01-01';
        } else {
            $exist->annual_date = date('Y-m-d', strtotime($param['date_at']));
        }

        //跟目前日期一样 不操作
        if ($before == $exist->annual_date) {
            throw new ValidationException('no change');
        }
        $exist->update();
        //记录日志
        $param['before'] = $before == '1970-01-01' ? '' : $before;
        $param['after']  = $exist->annual_date == '1970-01-01' ? '' : $exist->annual_date;
        return $this->saveLog($param, StaffLeaveOperateLogModel::EDIT_TYPE_DATE);
    }

    //年假额度修改
    public function annualDaysEdit($param)
    {
        //要修改的额度周期
        $remainInfo = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year:',
            'bind'       => ['staff_id' => $param['staff_info_id'], 'year' => $param['cycle']],
        ]);

        $days = floatval($param['days']);
        if($days > 100){
            throw new ValidationException('days can not over 100!');
        }
        if (empty($remainInfo)) {
            throw new ValidationException('annual cycle info error');
        }

        $param['before'] = $remainInfo->days;
        $param['after'] = $param['days'];
        if ((float)$param['days'] == $remainInfo->days) {
            throw new ValidationException('no change');
        }
        //马来 特殊 要处理 预发放
        $waitingUsed = 0;
        if(isCountry('MY')){
            $waitingUsed = StaffWaitingLeaveSendModel::sum([
                'column'     => 'days',
                'conditions' => 'staff_info_id = :staff_id: and year_at = :year_at: and is_used = 1',
                'bind' => ['staff_id' => $param['staff_info_id'], 'year_at' => $param['cycle']],
            ]);
        }


        $leaveDays               = $remainInfo->leave_days - $waitingUsed;//马来特殊 减去预发放
        $remainInfo->days        = (float)$param['days'];
        $remainInfo->freeze_days = bcadd($param['days'], $leaveDays, 5);
        $remainInfo->update();
        return $this->saveLog($param, StaffLeaveOperateLogModel::EDIT_TYPE_DAYS, ['cycle' => $param['cycle']]);
    }


    /**
     * 年假操作日志表
     * @param $param
     * @param $logType
     * @param array $extend 额外需要记录的数据
     * @return bool
     * @throws ValidationException
     */
    public function saveLog($param, $logType, $extend = [])
    {
        $staffService = new HrStaffService();
        $staffInfo    = $staffService->getStaffInfosMap([$param['staff_info_id']]);
        if (empty($staffInfo)) {
            throw new ValidationException('staff info error');
        }
        $staffInfo = $staffInfo[$param['staff_info_id']];

        $insert['staff_info_id'] = $staffInfo['staff_info_id'];
        $insert['staff_name']    = $staffInfo['name'];
        $insert['operate_id']    = $param['user_info']['id'];
        $insert['operate_name']  = $param['user_info']['name'];;
        $insert['edit_type'] = $logType;
        $insert['hire_date'] = date('Y-m-d', strtotime($staffInfo['hire_date']));
        $insert['remark'] = $param['remark'];
        $json = ['before' => $param['before'], 'after' => $param['after']];
        if (!empty($extend)) {
            $json = array_merge($json, $extend);
        }
        $insert['json_data'] = json_encode($json);
        $model = new StaffLeaveOperateLogModel();
        $model->create($insert);

        //图片
        if(!empty($param['image_path'])){
            $imgModel = new StaffLeaveOperateLogFileModel();
            $url = array_column($param['image_path'],'object_url');
            foreach ($url as $u){
                $img['log_id'] = $model->id;
                $img['file_path'] = $u;
                $clone = clone $imgModel;
                $clone->create($img);
            }
        }

        return true;
    }

    //操作记录
    public function annualLogList($param)
    {
        $page = $param['page'] ?? 1;
        $size = $param['size'] ?? 20;

        if (!empty($param['staff_info_id'])) {
            $conditions[]          = ' (staff_info_id = :staff_id: or staff_name like :like_staff_id:) ';
            $bind['staff_id']      = intval($param['staff_info_id']);
            $bind['like_staff_id'] = "%{$param['staff_info_id']}%";
        }

        if (!empty($param['operate_id'])) {
            $conditions[]            = ' (operate_id = :operate_id: or operate_name like :like_operate_id:) ';
            $bind['operate_id']      = intval($param['operate_id']);
            $bind['like_operate_id'] = "%{$param['operate_id']}%";
        }

        $conditions = empty($conditions) ? [] : implode(' and ', $conditions);
        $bind       = empty($bind) ? [] : $bind;
        //工号姓名 模糊查询
        $count = StaffLeaveOperateLogModel::findFirst([
            'columns'    => 'count(*) as count',
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        $count = $count->count;
        if (empty($count)) {
            return ['count' => 0, 'list' => []];
        }

        //工号姓名 模糊查询
        $data = StaffLeaveOperateLogModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id DESC',
            'limit'      => $size,
            'offset'     => ($page - 1) * $size,
        ])->toArray();

        //图片信息
        $ids     = array_column($data, 'id');
        $imgData = StaffLeaveOperateLogFileModel::find([
            'columns'    => "log_id, file_path",
            'conditions' => 'log_id in ({ids:array})',
            'bind'       => ['ids' => $ids],
        ])->toArray();
        $imgArr = [];
        if (!empty($imgData)) {
            foreach ($imgData as $v){
                $imgArr[$v['log_id']][] = $v['file_path'];
            }
        }

        //列表整理
        $addHour  = $this->config->application->add_hour;
        $typeText = StaffLeaveOperateLogModel::$typeText;
        $list     = [];
        $server = reBuildCountryInstance($this);
        foreach ($data as $datum) {
            $row['staff_name']   = "{$datum['staff_name']}({$datum['staff_info_id']})";
            $row['operate_name'] = "{$datum['operate_name']}({$datum['operate_id']})";
            $row['operate_time'] = date('Y-m-d H:i:s', strtotime($datum['created_at']) + ($addHour * 3600));;
            $row['operate_type']      = $datum['edit_type'];
            $row['operate_type_text'] = static::$t->_($typeText[$datum['edit_type']]);
            $row['cycle_info']   = '';
            $row['invalid_time'] = '';
            $json                = json_decode($datum['json_data'], true);
            //额度修改 有周期数据
            if ($datum['edit_type'] == StaffLeaveOperateLogModel::EDIT_TYPE_DAYS) {
                $hireDate = $datum['hire_date'];
                $cycle    = $json['cycle'];
                $info = $server->formatAnnualCycle($hireDate, $cycle);
                $row['cycle_info'] = $info['cycle_info'] ?? '';
                $row['invalid_time'] = $info['invalid_date'] ?? '';
            }
            $row['operate_text'] = "{$json['before']}→{$json['after']}";
            $row['remark']       = $datum['remark'];
            $row['image_path']   = $imgArr[$datum['id']] ?? [];
            $list[]              = $row;
        }
        return ['count' => $count, 'list' => $list];
    }


    //要 周期 开始- 结束 和失效日期
    public function formatAnnualCycle($hireDate, $cycle)
    {
        $add     = $cycle - 1;
        $start   = date('Y.m.d', strtotime("{$hireDate} +{$add} year"));
        $end     = date('Y.m.d', strtotime("{$hireDate} +{$cycle} year -1 day"));
        $realEnd = date('Y-m-d', strtotime("{$hireDate} +{$cycle} year -1 day"));
        $invalid = date('Y.m.d', strtotime("{$realEnd} +90 days"));
        return ['cycle_info' => "{$start}-{$end}", 'invalid_date' => $invalid];
    }


    //批量获取 时间区间内 员工请假数据 轮休页面展示 以及配置新增轮休用
    /**
     *
    select s.date_at,s.staff_info_id,sum(s.type) as type,group_concat(a.leave_type) as leave_type_str
    ,concat(s.date_at,'_',s.staff_info_id) as u_key

    from staff_audit_leave_split s
    join staff_audit a on a.audit_id = s.audit_id where

    s.staff_info_id in (22238) and s.date_at in ('2023-06-03')
    and a.audit_type = 2 and a.status in (1,2) group by s.staff_info_id ,s.date_at;
     * @param $staffIds
     * @param $dateList
     * @return mixed
     */
    public function getLeaveByDate($staffIds,$dateList){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("a.leave_start_time,s.date_at,s.staff_info_id,sum(s.type) as type,group_concat(a.leave_type) as leave_type_str
        ,concat(s.date_at,'_',s.staff_info_id) as u_key,a.audit_reason");
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->inWhere("s.staff_info_id", $staffIds);
        $builder->inWhere('s.date_at ', $dateList);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status', [Enums::audit_status_1,Enums::audit_status_2]);
        $builder->andWhere('a.leave_type != 38');
        $builder->groupBy("s.staff_info_id,s.date_at");
        $data = $builder->getQuery()->execute()->toArray();

        return $data;
    }


    //批量导入 请假 快捷工具
    public function batchImport($param, $userId){
        $fileName  = basename($param['import_path']);
        $fileInfo  = pathinfo($fileName);
        $localName = date('Y-m-d') . '_' . time() . $userId . '.' . $fileInfo['extension'];
        $filePath = sys_get_temp_dir() . '/' . $localName;

        //下载到本地
        $this->downloadFile($param['import_path'], $filePath);
        $config = ['path' => dirname($filePath)];
        $excel  = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($localName)->openSheet();
        //补卡时间
        $excel->setType([
            3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//开始日期
            5 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//结束日期

        ]);

        //跳过文件头
        $data = $excel->getSheetData();
        //前两行是标题
        unset($data[0]);
        $data = array_values($data);
        if (empty($data)) {
            echo 'there is nothing in the file';
            return true;
        }

        $staffs = array_column($data,'0');
        if(empty($staffs)){
            $this->logger->info('导入数据 没有工号');
            return true;
        }

        $staffs[] = $userId;
        $staffData = HrStaffInfoModel::find([
            'columns' => 'staff_info_id,name',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind' => [
                'ids' => $staffs,
            ],
        ])->toArray();

        $staffData = empty($staffData) ? [] : array_column($staffData,'name', 'staff_info_id');
        $server = reBuildCountryInstance($this);

        //组装数据 掉用rpc
        $attendanceServer = new AttendanceToolService();
        foreach ($data as $k => &$da) {
            //日期
            $da[3] = (empty($da[3]) || !is_integer($da[3])) ? $da[3] : date('Y-m-d', $da[3]);
            $da[5] = (empty($da[5]) || !is_integer($da[5])) ? $da[5] : date('Y-m-d', $da[5]);

            //工号和类型为空 也不处理
            if(empty($da[0]) && empty($da[1])){
                unset($data[$k]);
                continue;
            }

            if(empty($da[3]) || empty($da[5])){
                $da['remark'] = 'date format error';
                continue;
            }

            $da['remark'] = '';
            $staffId = $da[0];
            if(empty($staffId)){
                $da['remark'] = 'wrong staff id';
                continue;
            }
            //工号不正确
            if(empty($staffData[$staffId])){
                $da['remark'] = 'wrong staff id';
                continue;
            }

            //类型
            $sendParam = [];
            $sendParam['leave_type'] = $da['leave_type'] = $server->leaveTypeToCode($da[1]);
            if(empty($sendParam['leave_type'])){
                $da['remark'] = 'wrong leave type';
                continue;
            }
            //子类型 只有产假有
            if($sendParam['leave_type'] == StaffAuditModel::LEAVE_TYPE_4 && !empty($da[2]) && (isCountry('LA') || isCountry('VN') || isCountry('PH'))){
                $sendParam['sub_type'] = $server->leaveTypeToCode($da[2]);
            }
            //泰国新增家人去世假子类型
            if($sendParam['leave_type'] == StaffAuditModel::LEAVE_TYPE_7 && !empty($da[9]) && (isCountry('TH'))){
                $sendParam['sub_type'] = $da['sub_type_7'] = $server->leaveTypeToCode($da[9]);
            }
            if($sendParam['leave_type'] == StaffAuditModel::LEAVE_TYPE_4 && empty($sendParam['sub_type']) && (isCountry('LA') || isCountry('VN') || isCountry('PH'))){
                $da['remark'] = 'Special case must be filled';
                continue;
            }

            //开始日期 验证
            $year = date('Y', strtotime($da[3]));
            if(!checkYear($year)){
                $da['remark'] = 'wrong start date';
                continue;
            }
            $sendParam['leave_start_time'] = date('Y-m-d', strtotime($da[3]));
            //结束日期
            $year = date('Y', strtotime($da[5]));
            if(!checkYear($year)){
                $da['remark'] = 'wrong end date';
                continue;
            }
            $sendParam['leave_end_time'] = date('Y-m-d', strtotime($da[5]));

            //上下午 马来 国民假期不用必填
            $sendParam['leave_start_type'] = $server->dayTypeToCode($da[4]);
            if(empty($sendParam['leave_start_type']) && $sendParam['leave_type'] != StaffAuditModel::LEAVE_TYPE_42){
                $da['remark'] = 'wrong start type';
                continue;
            }
            $sendParam['leave_end_type'] = $server->dayTypeToCode($da[6]);
            if(empty($sendParam['leave_end_type']) && $sendParam['leave_type'] != StaffAuditModel::LEAVE_TYPE_42){
                $da['remark'] = 'wrong end type';
                continue;
            }

            $sendParam['staff_info_id'] = $sendParam['staff_id'] = $staffId;
            $sendParam['operator']      = $userId;
            $sendParam['operator_name'] = $staffData[$userId] ?? '';
            //其他国家 字段顺序
            $sendParam['reason'] = $da[7] ?? '';
            if (mb_strlen(trim($sendParam['reason'])) > 500) {
                $da['remark'] = static::$t->_('over_varchar_notice');
                continue;
            }
            //请假原因必填
            if(empty(trim($sendParam['reason']))){
                $da['remark'] = 'need leave reason';
                continue;
            }

            if ($sendParam['leave_type'] == StaffAuditModel::LEAVE_TYPE_38 && isCountry('TH')) {
                $sickCheck = $this->checkSickImg($da);
                if(!is_array($sickCheck)){
                    $da['remark'] = $sickCheck;
                    continue;
                }
                $sendParam = array_merge($sendParam, $sickCheck);
            }
            //非病假申请 按之前逻辑
            if($sendParam['leave_type'] != StaffAuditModel::LEAVE_TYPE_38){
                $sendParam['image_path']        = empty($da[8]) ? [] : explode(',', $da[8]);
                $checkUrl = $this->pregUrl($sendParam['image_path']);
                if(!$checkUrl){
                    $da['remark'] = 'wrong image url';
                    continue;
                }
                //非病假
                if(count($sendParam['image_path']) > 5){
                    $da['remark'] = 'at most 5 pictures';
                    continue;
                }
            }

            $sendParam['paid_leave_reason'] = 0;//请假说明 给默认
            $sendParam['edit_type'] = AttendanceDataLogModel::TYPE_FOR_LEAVE;

            try{
                $attendanceServer->askForLeave($sendParam);
            }catch (BusinessException $b){
                $da['remark'] = $b->getMessage();
                continue;
            }catch (\Exception $e){
                $da['remark'] = $e->getMessage();
                continue;
            }
        }

        //整理数据 发消息
        $path = $this->formatImport($data,$param['result_file_name']);
        if(empty($path)){
            $this->logger->error('补卡导入 生成结果文件失败' . json_encode($param));
            throw new \Exception('补卡导入 生成结果文件失败 ');
        }
        $info = AsyncImportTaskModel::findFirst($param['id']);
        if (empty($info)) {
            throw new \Exception('任务信息错误 没找到对应记录' . $param['id']);
        }
        $info->status           = AsyncImportTaskModel::STATE_EXECUTED;
        $info->result_file_name = $param['result_file_name'];
        $info->result_path      = $path . $param['result_file_name'];
        $info->fail_number      = $this->failNum ?? 0;
        $info->success_number   = $this->successNum ?? 0;
        $info->update();

        return true;
    }

    //新增 验证 病假图片的逻辑
    public function checkSickImg($da)
    {
        //泰国病假 需要至少填一项图片 整理图片结构
        $sendParam['image_path']['other'] = empty($da[8]) ? [] : explode(',', $da[8]);
        if (!empty($sendParam['image_path']['other']) && empty($da[9])) {
            return 'need attachment description';
        }
        if (!empty($da[9]) && mb_strlen(trim($da[9])) > 100) {
            return 'attachment description at most 100 char';
        }
        $sendParam['other_content']             = trim($da[9]);
        $sendParam['image_path']['certificate'] = empty($da[10]) ? [] : explode(',', $da[10]);
        $sendParam['image_path']['anticipate']  = empty($da[11]) ? [] : explode(',', $da[11]);
        $sendParam['image_path']['receipt']     = empty($da[12]) ? [] : explode(',', $da[12]);
        if (empty($sendParam['image_path']['other']) && empty($sendParam['image_path']['certificate']) && empty($sendParam['image_path']['anticipate']) && empty($sendParam['image_path']['receipt'])) {
            return static::$t->_('sick_at_least_one');
        }

        $str = '';
        foreach (SickLeaveCertificateDetailModel::$sickImgCategory as $category){
            //每个分类最多三张
            if(count($sendParam['image_path'][$category]) > 3){
                $str = 'at most 3 pictures';
                break;
            }
            //url 合法
            if (!empty($sendParam['image_path'][$category]) && !$this->pregUrl($sendParam['image_path'][$category])) {
                $str = 'wrong image url';
                break;
            }
        }
        if(!empty($str)){
            return $str;
        }
        return $sendParam;
    }
    //验证图片链接是否正确
    public function pregUrl($imgList){
        $res = true;
        foreach ($imgList as $url){
            $flag = validation_url($url);
            if($flag === false){
                $res = false;
                break;
            }
        }
        return $res;
    }



    //导入结果 保存 然后发消息
    public function formatImport($data,$fileName)
    {
        $header[] = '*ID';
        $header[] = '*Leave Type';
        $header[] = 'Special case';
        $header[] = '*Start Date（YYYY-MM-DD）';
        $header[] = '*Start Time Type';
        $header[] = '*End Date（YYYY-MM-DD）';
        $header[] = '*End Date Type';
        $header[] = '*Reason';
        $header[] = 'Attachment link';
        $header[] = 'Attachment Description';
        $header[] = 'Medical record（for SL）';
        $header[] = 'Reservation list（for SL）';
        $header[] = 'Invoice（for SL）';
        $header[] = 'Relationship with deceased family members';
        $header[] = 'Result';

        $res      = [];
        foreach ($data as $da) {
            $row[0] = $da[0];
            $row[1] = $da[1];
            $row[2] = $da[2];
            $row[3] = $da[3];
            $row[4] = $da[4];
            $row[5] = $da[5];
            $row[6] = $da[6];
            $row[7] = $da[7];
            $row[8] = $da[8];
            $row[9] = $da['leave_type'] == 38 ? $da[9] : '';
            $row[10] = $da['leave_type'] == 38 ? $da[10] : '';
            $row[11] = $da['leave_type'] == 38 ? $da[11] : '';
            $row[12] = $da['leave_type'] == 38 ? $da[12] : '';
            $row[13] = $da['sub_type_7'] ? $da[9] : '';
            $row[14] = $da['remark'];
            //导入结果
            if (empty($da['remark'])) {
                $this->successNum++;
                $row[14] = 'success;';
            } else {
                $this->failNum++;
            }
            $res[] = $row;
        }

        $date        = date('Y-m-d');
        $update_path = 'backyard/' . $date . '/';
        $result      = (new BllService())->exportExcels($header, $res, $update_path, $fileName);
        if ($result['code'] != ErrCode::SUCCESS) {
            return '';
        }
        return $update_path;
    }

    //excel 里面的枚举 转化成type
    public function dayTypeToCode($str){
        $list = [
            '1st Half Day' => 1,
            '2nd Half Day' => 2,
        ];

        return $list[$str] ?? '';
    }


    //病假证明列表
    public function sickCertificateList($param){
        $builder = $this->modelsManager->createBuilder();

        $builder->from(['c' => SickLeaveCertificateModel::class]);
        $builder->join(StaffAuditModel::class, 'a.audit_id = c.audit_id', 'a');
        $builder->join(HrStaffInfoModel::class, 'a.staff_info_id = s.staff_info_id', 's');
        $builder->andWhere('a.audit_type = 2 and a.leave_type = :leave_type:', ['leave_type' => StaffAuditModel::LEAVE_TYPE_38]);
        $builder = $this->buildCondition($builder, $param);

        //非导出 先取总数
        $builder->columns('count(1) as num');
        $count_info      = $builder->getQuery()->getSingleResult();
        $return['count'] = $count_info->num;
        if(empty($count_info->num)){
            return ['count' => 0,'list' => []];
        }
        //再分页
        $column = 'c.id,a.audit_id,s.staff_info_id,s.name,s.state,s.wait_leave_state,s.job_title,s.node_department_id,s.sys_store_id,s.hire_type
                    ,c.created_at,a.sub_status,c.approval_time,c.status,a.leave_day,a.leave_start_time,a.leave_start_type,a.leave_end_time,a.leave_end_type
                    ,a.audit_reason, c.created_at, c.approval_time, c.reject_reason, c.approve_id';
        $builder->columns($column);
        $size   = empty($param['size']) ? 20 : $param['size'];
        $offset = empty($param['page']) ? 1 : $param['size'] * ($param['page'] - 1);
        $builder->limit($size, $offset);

        $list = $builder->getQuery()->execute()->toArray();

        if(empty($list)){
            return [];
        }
        //审批状态文案
        $apply_key = Enums::$audit_status;
        //子类型 病假文案
        $sub_status = StaffAuditModel::$sub_status_map;


        //整理数据
        foreach ($list as &$li) {
            //整理 在职状态显示 如果待离职 显示 在职（待离职）
            $li['state_text'] = self::$t->_(Enums::$staff_state[$li['state']]);
            if (!empty($da['wait_leave_state'])) {
                $li['state_text'] = self::$t->_('hris_working_state_4');
            }
            $li['store_id']        = $li['sys_store_id'];
            $li['department_name'] = $this->showDepartmentName($li['node_department_id']);
            $li['job_name']        = $this->showJobTitleName($li['job_title']);
            $li['store_name']      = $this->showStoreName($li['sys_store_id']);
            $li['hire_type_text']  = self::$t->_('hire_type_' . $li['hire_type']);

            //提交状态
            $li['sub_status_text'] = empty($sub_status[$li['sub_status']]) ? '' : self::$t->_($sub_status[$li['sub_status']]);
            //审批状态
            $li['status_text'] = empty($apply_key[$li['status']]) ? '' : self::$t->_($apply_key[$li['status']]);

            //拼接 开始 结束时间 上下午
            $li['leave_start_time'] = date('Y-m-d', strtotime($li['leave_start_time']));
            $li['leave_start_time'] .= ($li['leave_start_type'] == 1) ? ' am' : ' pm';
            $li['leave_end_time']   = date('Y-m-d', strtotime($li['leave_end_time']));
            $li['leave_end_time']   .= ($li['leave_end_type'] == 1) ? ' am' : ' pm';
            $li['created_at']       = date('Y-m-d H:i:s', strtotime($li['created_at']) + ($this->timeOffset) * 3600);
        }

        return ['count' => $count_info->num, 'list' => $list];
    }

    //证明导出 下载中心
    public function exportList($param){
        BaseService::setLanguage($param['lang']);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => SickLeaveCertificateModel::class]);
        $builder->join(StaffAuditModel::class, 'a.audit_id = c.audit_id', 'a');
        $builder->join(HrStaffInfoModel::class, 'a.staff_info_id = s.staff_info_id', 's');
        $builder->leftJoin(HrStaffInfoModel::class, 'app.staff_info_id = c.approve_id', 'app');//有可能没有审批人
        $builder->leftJoin(SysStoreModel::class, 's.sys_store_id = store.id', 'store');

        $builder->andWhere('a.audit_type = 2 and a.leave_type = :leave_type:', ['leave_type' => StaffAuditModel::LEAVE_TYPE_38]);
        $builder = $this->buildCondition($builder, $param);

        $column = 'c.id,a.audit_id,s.staff_info_id,s.name,s.state,s.wait_leave_state,s.job_title,s.node_department_id,s.sys_store_id,s.hire_type
                    ,c.created_at,a.sub_status,c.approval_time,c.status, store.manage_region,store.manage_piece
                    ,a.leave_start_time,a.leave_start_type,a.leave_end_time,a.leave_end_type,a.leave_day,a.audit_reason,c.reject_reason
                    ,a.serial_no as leave_serial_no, c.other_content, c.approve_id,app.name as app_name, app.job_title as app_job_title';
        $builder->columns($column);
        //分页处理
        $pageNum  = 1;
        $pageSize = 10;
        $list     = [];
        while (true) {
            $offset = $pageSize * ($pageNum - 1);
            $builder->limit($pageSize, $offset);
            $item = $builder->getQuery()->execute()->toArray();
            if (empty($item)) {
                break;
            }
            $pageNum++;
            $list = array_merge($list, $item);
        }
        if(empty($list)){
            [$header, $data] = $this->formatExport($list);
            return [$header, $data];
        }

        $cids = array_column($list,'id');
        //获取所有材料申请的 图片
        $imgData = $this->formatSickImg($cids);

        //在职状态文案
        $text_key = Enums::$hris_working_state;
        //审批状态文案
        $apply_key = Enums::$audit_status;
        //子类型 病假文案
        $sub_status = StaffAuditModel::$sub_status_map;

        //整理数据
        foreach ($list as &$li) {
            //整理 在职状态显示 如果待离职 显示 在职（待离职）
            $li['state_text'] = empty($text_key[$li['state']]) ? '' : self::$t->_($text_key[$li['state']]);
            if (!empty($li['wait_leave_state'])) {
                $li['state_text'] = self::$t->_('hris_working_state_4');
            }

            $li['store_id']        = $li['sys_store_id'];
            $li['department_name'] = $this->showDepartmentName($li['node_department_id']);
            $li['job_name']        = $this->showJobTitleName($li['job_title']);
            $li['app_job_name']    = $this->showJobTitleName($li['app_job_title']);
            $li['store_name']      = $this->showStoreName($li['sys_store_id']);
            $li['hire_type_text']  = self::$t->_('hire_type_' . $li['hire_type']);
            $li['region_name']     = $li['piece_name'] = '';
            if ($li['sys_store_id'] != GlobalEnums::HEAD_OFFICE_ID) {
                $li['region_name'] = $this->showRegionName($li['manage_region']);
                $li['piece_name']  = $this->showPieceName($li['manage_piece']);
            }
            //提交状态
            $li['sub_status_text'] = empty($sub_status[$li['sub_status']]) ? '' : self::$t->_($sub_status[$li['sub_status']]);
            //审批状态
            $li['status_text'] = empty($apply_key[$li['status']]) ? '' : self::$t->_($apply_key[$li['status']]);
            //拼接 开始 结束时间 上下午
            $li['leave_start_time'] = date('Y-m-d', strtotime($li['leave_start_time']));
            $li['leave_start_time'] .= ($li['leave_start_type'] == 1) ? ' am' : ' pm';
            $li['leave_end_time']   = date('Y-m-d', strtotime($li['leave_end_time']));
            $li['leave_end_time']   .= ($li['leave_end_type'] == 1) ? ' am' : ' pm';

            $li['certificate'] = empty($imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_CERTIFICATE]) ? '' : implode(',',
                $imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_CERTIFICATE]);
            $li['anticipate']  = empty($imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_ANTICIPATE]) ? '' : implode(',',
                $imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_ANTICIPATE]);
            $li['receipt']     = empty($imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_RECEIPT]) ? '' : implode(',',
                $imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_RECEIPT]);
            $li['other']       = empty($imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_OTHER]) ? '' : implode(',',
                $imgData[$li['id']][SickLeaveCertificateDetailModel::SICK_OTHER]);

            //其他证明描述
            if (empty($li['other'])) {
                $li['other_content'] = '';
            }

            //申请时间
            $li['created_at'] = date('Y-m-d H:i:s', strtotime($li['created_at']) + ($this->timeOffset) * 3600);
            $li['reject_id']  = $li['reject_name'] = $li['reject_job'] = '';
            //驳回人
            if ($li['status'] == Enums::audit_status_3) {
                $li['reject_id']   = $li['approve_id'];
                $li['reject_name'] = $li['app_name'];
                $li['reject_job']  = $li['app_job_name'];
            }
            $li['timeout_id'] = $li['timeout_name'] = $li['timeout_job'] = '';
            //超时审批人
            if ($li['status'] == Enums::audit_status_5) {
                $li['timeout_id']   = $li['approve_id'];
                $li['timeout_name'] = $li['app_name'];
                $li['timeout_job']  = $li['app_job_name'];
            }
        }

        [$header, $data] = $this->formatExport($list);
        return [$header, $data];
    }

    public function buildCondition($builder, $param){
        //员工姓名 工号
        if(!empty($param['staff_info_id'])){
            $builder->andWhere('(s.staff_info_id = :staff_name: or s.name = :staff_name:)',['staff_name' => $param['staff_info_id']]);
        }
        //在职状态
        if(!empty($param['staff_state'])){
            $tmp_sql = [];
            if(in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION,$param['staff_state'])){
                $tmp_sql[] = '( s.state = 1 and s.wait_leave_state  = 1 )';
            }
            if(in_array(HrStaffInfoModel::STATE_ON_JOB,$param['staff_state'])){
                $tmp_sql[] = '( s.state = '.HrStaffInfoModel::STATE_ON_JOB .' and s.wait_leave_state  = 0) ';
            }
            if(in_array(HrStaffInfoModel::STATE_RESIGN,$param['staff_state'])){
                $tmp_sql[] = 's.state = '.HrStaffInfoModel::STATE_RESIGN;
            }
            if(in_array(HrStaffInfoModel::STATE_SUSPEND,$param['staff_state'])){
                $tmp_sql[] = 's.state = '.HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ',$tmp_sql);
            $builder->andWhere($_sql);
        }

        if (!empty($param['department_ids'])) {
            $builder->inWhere("s.node_department_id", $param['department_ids']);
        }

        //过滤职位
        if (!empty($param['job_title'])) {
            $builder->inWhere("s.job_title", $param['job_title']);
        }
        //雇佣类型
        if (!empty($param['hire_type'])) {
            $builder->inWhere("s.hire_type", $param['hire_type']);
        }

        //过滤网点
        if (!empty($param['store_id'])) {
            $builder->inWhere("s.sys_store_id", $param['store_id']);
        }

        //审批状态
        if (!empty($param['audit_status'])) {
            $builder->inWhere("c.status", $param['audit_status']);
        }

        //材料状态
        if(!empty($param['sub_status'])){
            $builder->inWhere("a.sub_status", $param['sub_status']);
        }

        //申请时间 前端传的是 时间戳
        $addHour = $this->config->application->add_hour;
        if(!empty($param['submit_start_time']) && !empty($param['submit_end_time'])) {
            $start = date('Y-m-d H:i:s', $param['submit_start_time'] - ($addHour * 3600));
            $end = date('Y-m-d H:i:s', $param['submit_end_time'] + 86399 - ($addHour * 3600));
            $builder->betweenWhere("c.created_at", $start, $end);
        }

        return $builder;
    }

    //整理图片
    public function formatSickImg($cids){
        $data = SickLeaveCertificateDetailModel::find([
            'conditions' => 'cid in ({ids:array})',
            'bind' => ['ids' => $cids],
        ])->toArray();

        if(empty($data)){
            return [];
        }
        $return = [];

        foreach ($data as $da){
            $return[$da['cid']][$da['category']][] = $da['image_path'];
        }

        return $return;
    }


    public function formatExport($list){
        $header['tool_employee']                        = self::$t->_('tool_employee');//工号
        $header['tool_name']                            = self::$t->_('tool_name');//姓名
        $header['staff_state']                          = self::$t->_('staff_state');//在职状态
        $header['job_name']                             = self::$t->_('job_name');//职位
        $header['department_name']                      = self::$t->_('department');//部门
        $header['dot']                                  = self::$t->_('dot');//网点
        $header['hire_type']                            = self::$t->_('hire_type');//雇佣类型
        $header['regional']                             = self::$t->_('regional');//大区
        $header['area']                                 = self::$t->_('area');//片区
        $header['sick_leave_serial_no']                 = self::$t->_('sick_leave_serial_no');//病假单号

        $header['start_time']                           = self::$t->_('start_time');//开始时间
        $header['end_time']                             = self::$t->_('end_time');//结束时间
        $header['by_days']                              = self::$t->_('by_days');//天数
        $header['by_leave_reason']                      = self::$t->_('by_leave_reason');//请假原因
        $header['certificate_status']                   = self::$t->_('certificate_status');//材料状态
        $header['sick_certificate']                     = self::$t->_('certificate');//病例证明
        $header['sick_anticipate']                      = self::$t->_('anticipate');//预约单
        $header['sick_receipt']                         = self::$t->_('receipt');//票据
        $header['sick_other']                           = self::$t->_('other');//其他说明
        $header['other_content']                        = self::$t->_('other_content');//其他图片

        $header['by_leave_status']                      = self::$t->_('by_leave_status');//审批状态
        $header['created_at']                           = self::$t->_('by_apply_time');//申请时间
        $header['approval_time']                        = self::$t->_('by_apply_over_time');//审批时间
        $header['hcm_translate_key_reject_reason']      = self::$t->_('hcm_translate_key_reject_reason');//驳回原因
        $header['hcm_translate_key_approver_id']        = self::$t->_('hcm_translate_key_approver_id');//驳回人id
        $header['hcm_translate_key_approver_name']      = self::$t->_('hcm_translate_key_approver_name');//驳回人
        $header['hcm_translate_key_approver_job_title'] = self::$t->_('hcm_translate_key_approver_job_title');//职位记录
        $header['audit_timeout_staff_ids']              = self::$t->_('audit_timeout_staff_ids');//超时审批人工号
        $header['audit_timeout_staff_names']            = self::$t->_('audit_timeout_staff_names');//超时审批人姓名
        $header['audit_timeout_staff_jobs']             = self::$t->_('audit_timeout_staff_jobs');//超时审批人职位

        //数据
        $data = [];
        foreach ($list as $li){
            $row['staff_info_id']    = $li['staff_info_id'];
            $row['name']             = $li['name'];
            $row['staff_state']      = $li['state_text'];
            $row['job_name']         = $li['job_name'];
            $row['department_name']  = $li['department_name'];
            $row['store_name']       = $li['store_name'];
            $row['hire_type_text']   = $li['hire_type_text'];
            $row['region_name']      = $li['region_name'];
            $row['piece_name']       = $li['piece_name'];
            $row['leave_serial_no']  = $li['leave_serial_no'];
            $row['leave_start_time'] = $li['leave_start_time'];
            $row['leave_end_time']   = $li['leave_end_time'];
            $row['leave_day']        = $li['leave_day'];
            $row['audit_reason']     = $li['audit_reason'];
            $row['sub_status_text']  = $li['sub_status_text'];
            $row['sick_certificate'] = $li['certificate'];
            $row['sick_anticipate']  = $li['anticipate'];
            $row['sick_receipt']     = $li['receipt'];
            $row['sick_other']       = $li['other'];
            $row['other_content']    = $li['other_content'];
            $row['status_text']      = $li['status_text'];
            $row['created_at']       = $li['created_at'];
            $row['approval_time']    = $li['approval_time'];
            $row['reject_reason']    = $li['reject_reason'];
            $row['reject_id']        = $li['reject_id'];
            $row['reject_name']      = $li['reject_name'];
            $row['reject_job']       = $li['reject_job'];
            $row['timeout_id']       = $li['timeout_id'];
            $row['timeout_name']     = $li['timeout_name'];
            $row['timeout_job']      = $li['timeout_job'];
            $data[] = $row;
        }

        return [$header, $data];

    }


    //病假证明详情
    public function sickCertificateDetail($param){
        $info = $this->getLeaveCertificateData((int)$param['id']);
        if (empty($info)) {
            throw new ValidationException('sick doc info error');
        }

        $info      = $info->toArray();
        $staffInfo = HrStaffInfoModel::findFirst("staff_info_id={$info['staff_info_id']}");
        if (empty($staffInfo)) {
            throw new ValidationException("staff info error");
        }
        $staffInfo = $staffInfo->toArray();

        $sub_status                = StaffAuditModel::$sub_status_map;
        $return['staff_info_id']   = $info['staff_info_id'];
        $return['name']            = $staffInfo['name'];
        $return['sub_status_text'] = empty($sub_status[$info['sub_status']]) ? '' : self::$t->_($sub_status[$info['sub_status']]);

        //图片
        $imgData      = $this->formatSickImg([$info['cid']]);
        $imgData      = $imgData[$info['cid']];
        $categoryEnum = SickLeaveCertificateDetailModel::$sickImgCategory;
        if (!empty($imgData)) {
            foreach ($imgData as $category => $img) {
                $return[$categoryEnum[$category]] = $img;
            }
        }

        //审批详情
        $audit_params['id_union']     = 'id_' . (int)$param['id'];
        $audit_params['type']         = ApprovalEnums::APPROVAL_TYPE_SICK_CERTIFICATE;//病假资料
        $audit_params['staff_id']     = $info['staff_info_id'];
        $audit_params['locale']       = self::$language;
        $audit_params['date_created'] = $info['created_at'] ?? '';
        //获取审批列表
        $audit_detail                = (new WorkflowService())->getAuditDetailV2($audit_params, $param['user_info']);
        $return['audit_list_detail'] = [];
        if ($audit_detail) {
            $return['audit_list_detail'] = $audit_detail['stream'];
        }

        return $return;
    }


    //根据材料id 获取 只有一条记录
    public function getLeaveCertificateData($cid){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.audit_id,a.serial_no as leave_serial_no, a.staff_info_id,a.leave_start_time,a.leave_start_type, a.leave_end_time,a.leave_end_type,a.leave_day,a.status,a.sub_status,
                         a.audit_reason, c.id as cid,c.status as c_status, c.is_new, c.created_at');
        $builder->from(['c' => SickLeaveCertificateModel::class]);
        $builder->join(StaffAuditModel::class, "a.audit_id = c.audit_id", 'a');
        $builder->andWhere('c.id = :cid:', ['cid' => $cid]);
        $data = $builder->getQuery()->execute()->getFirst();
        return $data;
    }

    //计算天数 调用by rpc
    public function countLeaveDays($params){
        $send_params = [
            'staff_id'          => $params['staff_info_id'],
            'staff_info_id'     => $params['staff_info_id'],
            'leave_start_time'  => $params['leave_start_time'],
            'leave_start_type'  => $params['leave_start_type'],
            'leave_end_time'    => $params['leave_end_time'],
            'leave_end_type'    => $params['leave_end_type'],
            'leave_type'        => $params['leave_type'],
        ];

        $res = (new AttendanceToolService())->getApiDatass('by', '', 'conversionTime', self::$language, $send_params);
        if (!isset($res['code']) || $res['code'] != 1) {
            $msg = $res['message'] ?? ($res['msg'] ?? '');
            throw  new BusinessException($msg);
        }
        return $res['data'];
    }

    public function familyDeathEnum()
    {
        $client = new ApiClient('by', '', 'getFamilyDeathEnum', self::$language);
        $result = $client->withParam([])->execute();
        if (isset($result['code']) && $result['code'] == 1) {
            return $result['data'];
        }
        return [];
    }

}