<?php


namespace App\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Validation\ValidationException;
use App\Models\backyard\RecruitmentChannelModel;
use App\Repository\HrRecruitChannelsRepository;

class H5appointService extends BaseService
{
    const LINK_TYPE_1 = 1;     // 通用
    const LINK_TYPE_2 = 2;     // 网络招聘
    const LINK_TYPE_AGENT = 3; // 个人代理
    const LINK_TYPE_LNT = 4; // 个人代理

    const STATUS_1 = 1; // 使用中
    const STATUS_2 = 2; //  停用


    public function getH5appointLinkType(): array
    {
        $list = [self::LINK_TYPE_1, self::LINK_TYPE_2, self::LINK_TYPE_AGENT];
        if (isCountry('MY')) {
            $list[] = self::LINK_TYPE_LNT;
        }
        return $list;
    }


    public function addChannel($params)
    {
        $this->getDI()->get('logger')->write_log('add_channel ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        $isExist = RecruitmentChannelModel::findFirst([
            'conditions' => 'channel_name = :name: ',
            'bind' => ['name' => $params['channel_name']],
        ]);
        if ($isExist)
            throw new ValidationException('this channel name is exist');

        $isExist = RecruitmentChannelModel::findFirst([
            'conditions' => 'channel_label = :label: ',
            'bind' => ['label' => $params['channel_label']],
        ]);
        if ($isExist)
            throw new ValidationException('this channel label is exist');


        $recruitmentChannelModel                  = new RecruitmentChannelModel();
        $recruitmentChannelModel->h5_link_type    = $params['h5_link_type'];
        $recruitmentChannelModel->channel_name    = $params['channel_name'];
        $recruitmentChannelModel->channel_label   = $params['channel_label'];
        $recruitmentChannelModel->recruit_channel = $params['recruit_channel'];
        $recruitmentChannelModel->status          = self::STATUS_1;
        $recruitmentChannelModel->operator_id     = $params['operator_id'];
        $result = $recruitmentChannelModel->save();
        if ($result) {
            return true;
        }

        throw new ValidationException('please retry');
    }


    public function editChannel($id, $status, $operatorId)
    {
        $model = RecruitmentChannelModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind' => ['id' => $id],
        ]);
        if (!$model)
            throw new ValidationException('this channel  is not exist');

        $model->status = $status;
        $model->operator_id = $operatorId;
        $result = $model->save();

        if ($result) {
            return true;
        }

        throw new ValidationException('please retry');

    }


    public function listChannel($page, $pageSize)
    {
        $list = RecruitmentChannelModel::find([
            'order' => 'created_at DESC',
            'limit' => $pageSize,
            'offset' => ($page - 1) * $pageSize,
        ])->toArray();
        $count = RecruitmentChannelModel::count();

        $recruitList   = (new HrRecruitChannelsRepository())->getListFromCache(['id', 'name']);
        $recruitListKy = array_column($recruitList, 'name', 'id');

        $result = [
            'list' => [],
            'count' => $count,
        ];
        foreach ($list as $item) {
            $result['list'][] = [
                'id'                   => $item['id'],
                'h5_link_type'         => $item['h5_link_type'],
                'h5_link_type_text'    => self::$t->_("h5_link_type_" . $item['h5_link_type']),
                'channel_name'         => $item['channel_name'],
                'channel_label'        => $item['channel_label'],
                'recruit_channel'      => $item['recruit_channel'],
                'recruit_channel_name' => $recruitListKy[$item['recruit_channel']] ?? '',
                'url'                  => $this->composeUrl($item),
                'status'               => $item['status'],
                'status_text'          => self::$t->_("channel_status_" . $item['status']),
                'created_at'           => date("Y-m-d H:i:s", strtotime($item['created_at']) + $this->config->application->add_hour * 3600),
            ];
        }
        return $result;
    }

    /**
     * @return array
     */
    public function getLinkTypes()
    {
        $data = [];

        $pathCollection = $this->getPathCollection();
        foreach ($pathCollection as $key=>$url) {
            $data[] = [
                'type'=> $key,
                'url' => $url,
            ];
        }
        return $data;
    }

    /**
     * @return string[]
     */
    private function getPathCollection()
    {
        $settingEnvService = new SettingEnvService();
        $flashDomain = $settingEnvService->getSetVal('h5_appoint_advertising');

        switch (get_country_code()) {
            case 'TH':
                $pathCollection = [
                    self::LINK_TYPE_1     => $flashDomain.'/#/resume',
                    self::LINK_TYPE_2     => $flashDomain.'/#/resume_hub',
                    self::LINK_TYPE_AGENT => $flashDomain.'/#/resume_proxy',
                ];
                break;
            case 'PH':
                $pathCollection = [
                    self::LINK_TYPE_1 => $flashDomain.'/#/resume',
                    self::LINK_TYPE_2 => $flashDomain.'/#/resume_network',
                ];
                break;
            case 'MY':
                $LntDomain = $settingEnvService->getSetVal('lnt_h5_appoint');
                $pathCollection = [
                    self::LINK_TYPE_1     => $flashDomain.'/#/resume',
                    self::LINK_TYPE_2     => $flashDomain.'/#/resume_channel',
                    self::LINK_TYPE_AGENT => $flashDomain.'/#/resume_proxy',
                    self::LINK_TYPE_LNT   => $LntDomain.'/#/resume_channel',
                ];
                break;
            default:
                $pathCollection = [
                    self::LINK_TYPE_1 => $flashDomain.'/#/resume',
                    self::LINK_TYPE_2 => $flashDomain.'/#/resume',
                ];
                break;
        }
        return $pathCollection;
    }

    /**
     * @param $domain
     * @param $item
     * @return mixed|string
     */
    private function composeUrl($item)
    {
        $pathCollection = $this->getPathCollection();
        $url = $pathCollection[$item['h5_link_type']];
        $query = http_build_query(['advertising_channel'=>$item['channel_name']]);
        $url = $url . '?' . $query;
        if(isCountry(['TH','PH',"MY"])){
            $url = $this->shortenUrlFromCache($url);
        }
        return $url;
    }


}