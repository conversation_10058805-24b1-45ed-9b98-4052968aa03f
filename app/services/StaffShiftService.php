<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums\ConditionsRulesEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\Enums\ShiftEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrShiftDepartmentConfigModel;
use App\Models\backyard\HrShiftRegionPieceConfigModel;
use App\Models\backyard\HrShiftStoreConfigModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftHistoryModel;
use App\Models\backyard\HrStaffShiftMiddleBackupModel;
use App\Models\backyard\HrStaffShiftMiddleDateModel;
use App\Models\backyard\HrStaffShiftModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffShiftPresetModel;
use App\Models\backyard\MessageModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffShiftChangeNoticeLogModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\TaskDataLogModel;
use App\Repository\HrShiftDepartmentConfigRepository;
use App\Repository\HrShiftRegionPieceConfigRepository;
use App\Repository\HrShiftRepository;
use App\Repository\HrShiftStoreConfigRepository;
use App\Repository\HrStaffShiftMiddleDateRepository;
use App\Repository\HrStaffShiftRepository;
use App\Repository\StaffShiftChangeNoticeLogRepository;
use App\Repository\SysStoreRepository;
use Exception;

class StaffShiftService extends BaseService
{

    public $staffInfo;//用staffinfoservice -> getStaffInfo_hr
    public $shiftInfo;//用户当前班次信息
    public $changeShiftInfo;//本次修改请求的班次信息
    public $shiftStaffData;//未来到下月底之间的员工班次信息
    public $unSuggestDates = [];//不符合排班建议的日期 保存时候剔除
    public $dateList = [];//未来多天修改班次用 最多到当前时间的下月最后一天
    public $isMaster;//是否是主管
    public $isShowMsg;//消息里面是否显示那句话

    public $today;//当天日期
    public $tomorrow;//明天日期
    public $jobTitleList;//职位数据
    public $shiftStringList = [];//班次 开始 + 结束时间 拼接

    public $settingEnvConfig = [];//env配置
    public $freeShiftJobTitle=[];//弹性班次职位


    //轮休页面修改班次 获取班次下拉方法 hr 迁移
    public function getStaffConfigShift($department_id, $job_title_id, $store_id)
    {
        $shift_list = [];
        if (empty($department_id) || empty($job_title_id) || empty($store_id)) {
            return $shift_list;
        }
        $shift_list = $this->getStaffConfigShiftList($department_id, $job_title_id, $store_id);

        //整理结构返回
        if (empty($shift_list)) {
            //可以随意设置
            $shift_list = HrShiftRepository::getShiftData();
            $shift_list = $this->formatShift($shift_list);
            return $shift_list;
        }

        $shift_ids  = array_column($shift_list, 'shift_id');
        $shift_list = HrShiftRepository::getShiftData($shift_ids);
        $shift_list = $this->formatShift($shift_list);
        return $shift_list;
    }

    //根据部门职位网点获取配置的班次信息
    public function getStaffConfigShiftList($department_id, $job_title_id, $store_id)
    {
        //总部员工班次
        if ($store_id == '-1') {
            $shift_list = HrShiftDepartmentConfigRepository::getDepartmentShiftList($department_id, $job_title_id);
            return $shift_list;
        }
        //按照网点设置
        $shift_list = HrShiftStoreConfigRepository::getStoreShiftList($store_id, $job_title_id);
        if (!empty($shift_list)) {
            return $shift_list;
        }

        //网点没配置 按照大区片区职位设置
        $storeInfo    = SysStoreRepository::getStoreById($store_id);
        $first_region = $storeInfo['manage_region'] ?? '';
        $first_piece  = $storeInfo['manage_piece'] ?? '';
        $shift_list   = HrShiftRegionPieceConfigRepository::getRegionPieceShiftList($first_region, $first_piece,$job_title_id);

        return $shift_list;
    }

    public function formatShift($data)
    {
        $result = [];
        foreach ($data as $da) {
            $row['start']          = $da['start'];
            $row['end']            = $da['end'];
            $row['id']             = $da['id'];
            $row['type']           = $da['type'];
            $t_text                = self::$t->_("shift_" . strtolower($da['type']));
            $row['markup']         = "({$t_text}) {$da['start']}-{$da['end']}";
            $result[$da['type']][] = $row;
        }
        ksort($result);
        return $result;
    }


    public function getStoreDepartmentShiftList($params)
    {
        $department_id = $params['department_id'] ?? 0;
        $job_title_id  = $params['job_title_id'] ?? 0;
        $category      = $params['category'] ?? 0;
        //查询出父部门, 因为设置的班次包含子部门
        //获取管辖部门
        $departments = SysDepartmentModel::findFirst($department_id);
        if (empty($departments)) {
            throw  new ValidationException('department is empty !');
        }
        $ancestry_v3 = array_reverse(explode("/", $departments->ancestry_v3));
        //查找这些部门设置的班次
        $shift_list   = HrShiftDepartmentConfigModel::find([
            'columns'    => ['id', 'department_id', 'job_title_id', 'shift_id', 'category', 'is_def_shift', 'type'],
            'conditions' => 'department_id in ({ids:array}) and type = :type:',
            'bind'       => [
                'ids'  => $ancestry_v3,
                'type' => HrShiftDepartmentConfigModel::TYPE_STORE,
            ],
        ])->toArray();
        $shift_list_k = [];
        foreach ($shift_list as $shift) {
            $key                  = $shift['department_id'] . '_' . $shift['job_title_id'] . '_' . $shift['category'];
            $shift_list_k[$key][] = $shift;
        }

        $shift_list_v = [];
        foreach ($ancestry_v3 as $dep_id) {
            //先获取部门-职位-网点类型
            $key = $dep_id . '_' . $job_title_id . '_' . $category;
            if (isset($shift_list_k[$key])) {
                $shift_list_v = $shift_list_k[$key];
                break;
            }
            //然后获取 部门-职位
            $key = $dep_id . '_' . $job_title_id . '_' . '0';
            if (isset($shift_list_k[$key])) {
                $shift_list_v = $shift_list_k[$key];
                break;
            }
            //部门-网点类型
            $key = $dep_id . '_' . '0' . '_' . $category;
            if (isset($shift_list_k[$key])) {
                $shift_list_v = $shift_list_k[$key];
                break;
            }
            //部门
            $key = $dep_id . '_' . '0' . '_' . '0';
            if (isset($shift_list_k[$key])) {
                $shift_list_v = $shift_list_k[$key];
                break;
            }
        }

        return $shift_list_v;
    }


    /**
     * @throws ValidationException
     */
    public function initShiftDateList($params): array
    {
        $startDate = $endDate = $params['date_at'];
        //改预设 结束日期 取下个月最后一天 按当前时间算
        if ($params['change_type'] == ShiftEnums::EFFECT_FUTURE) {
            $endDate = date('Y-m-t', strtotime(date('Y-m-01',strtotime($params['date_at'])) ." +1 month last day of"));//下月最后一天
            //前端没限制住 选了下个月底之后的日期
            if ($endDate < $params['date_at']) {
                throw new ValidationException("wrong date_at, can not over 2 month");
            }
        }

        //不能选当天和之前的日期
        if ($params['date_at'] < date('Y-m-d')) {
            throw new ValidationException("wrong date_at, can not be past date");
        }

        //真实的修改班次的时间区间
        $this->dateList = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        return [$startDate, $endDate];
    }


    /**
     * 修改班次 验证dc 是否符合建议 分单个日期 和多个日期
     * 返回结构 {'2023-01-01' => ['result' => true/false,'remark' => '备注日志用'],...}
     * @param $params
     * @return array|bool
     * @throws ValidationException
     */
    public function checkChangeShift($params)
    {
        $startDate = $params['startDate'];
        $endDate   = $params['endDate'];

        $dateList = $this->dateList;

        //建议班次和建议人数
        $suggestServer = new SchedulingSuggestionService();
        $suggest       = $suggestServer->getSchedulingSuggest($params['store_id'], $startDate, $endDate);
        //没有排班建议 不验证
        if (empty($suggest)) {
            return true;
        }

        //获取 员工对应日期的班次
        $shiftServer          = new HrShiftService();
        $shiftServer = reBuildCountryInstance($shiftServer);
        $this->shiftStaffData = $shiftServer->getShiftInfos([$params['staff_info_id']], $dateList);

        //获取对应网点的 当前实际出勤人数
        $workdayServer = new WorkdayService();
        $workSuggest   = $workdayServer->workdaySuggest($params['store_id'], $startDate, $endDate, $suggest);
        //轮休数据
        $restStaff = $workSuggest['restStaff'] ?? [];

        //本次修改的新班次 开始时间
        $newShift = $params['start'];

        //不合符条件的日期list
        $checkData['list']  = [];//['2023-01-01' => ['result' => true/false,'remark' => '备注日志用']]对应日期 是否符合条件
        $checkData['count'] = 0;//不符合条件的数量 发消息用
        foreach ($dateList as $date) {
            //没配置建议 可以保存
            if (empty($suggest[$date])) {
                $row['result']            = true;
                $row['remark']            = 'no suggest info';
                $checkData['list'][$date] = $row;
                continue;
            }
            //有配置但是没 dc的 也可以保存
            $types = array_column($suggest[$date], 'position_type');
            if (!in_array(SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER, $types)) {
                $row['result']            = true;
                $row['remark']            = 'no suggest dc info';
                $checkData['list'][$date] = $row;
                continue;
            }
            $shiftKey = "{$date}_{$params['staff_info_id']}";
            $oldShift = $this->shiftStaffData[$shiftKey]['start'];

            //其他dc 建议班次 满没满
            $otherFlag = true;//其他班次 是否符合条件(都排满了) 默认符合
            $thisFlag  = false;//本次修改班次符合条件 表示可以保存成功 默认不成功
            $containSuggest = false;//当前日期操作对应的建议班次 默认不符合
            $remarkStr = '';//记录备注 查问题用
            $rest = !empty($restStaff[$date]) ? explode(',', $restStaff[$date]) : [];
            foreach ($suggest[$date] as $s) {
                //快递员的类型跳过
                if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_COURIER) {
                    continue;
                }
                //仓管 但是 没有配置时间 也跳过只判断人数就行了
                if($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER && empty($s['shift_date'])){
                    $thisFlag = $containSuggest = true;
                    continue;
                }

                $suggestNum = $s['suggest_number'];
                $key        = "{$date}_{$s['shift_date']}";
                if ($shiftServer->betweenOneHour($s['shift_date'], $newShift)) {
                    $containSuggest = true;
                    //是本次操作班次建议 判断是否满了 如果没满 可以操作记录 true
                    $actNum = ($workSuggest['dc'][$key] ?? 0) + 1;//加上本次操作的人数
                    //如果当前操作员工就在实际出勤里 要减去本人
                    if($shiftServer->betweenOneHour($s['shift_date'], $oldShift)){
                        $actNum--;
                    }
                    if ($actNum <= $suggestNum) {
                        //符合条件
                        $row['result']            = true;
                        $row['remark']            = "suitable suggest {$key} {$actNum} {$suggestNum}";
                        $checkData['list'][$date] = $row;
                        $thisFlag                 = true;
                        break;
                    }
                    //本次不符合条件 需要看其他建议班次人数状况如何
                    $remarkStr .= "{$key} {$actNum} {$suggestNum}|";
                } else {
                    //非本次操作班次 判断是否满了 如果没满 记录otherFlag
                    $actNum = $workSuggest['dc'][$key] ?? 0;
                    //如果 员工旧班次符合条件
                    if (!empty($this->shiftStaffData[$shiftKey])
                        && $shiftServer->betweenOneHour($s['shift_date'], $this->shiftStaffData[$shiftKey]['start'])
                        && !in_array($params['staff_info_id'], $rest)
                    ) {
                        $actNum -= 1;
                    }
                    //如果符合条件 都满了 continue
                    if ($actNum >= $suggestNum) {
                        continue;
                    }
                    $remarkStr .= "{$key} {$actNum} {$suggestNum}|";
                    $otherFlag = false;
                }
            }
            //本次日期 已经整理完了 不需要整理 $row
            if ($thisFlag === true) {
                continue;
            }
            //当前日期操作对应的建议班次都不符合要求
            if($containSuggest === false){
                $this->unSuggestDates[] = $date;
                //都属于不符合条件范围
                $row['result']            = false;
                $row['remark']            = "no suitable ";
                $checkData['list'][$date] = $row;
                continue;
            }

            //有符合班次 但是已经满了 判断 其他班次 当前建议满了 但是其他班次也满了 可以保存成功
            if ($otherFlag) {
                //符合条件
                $row['result']            = true;
                $row['remark']            = "suitable other suggest {$date}";
                $checkData['list'][$date] = $row;
                continue;
            }
            //不符合建议
            $this->unSuggestDates[] = $date;
            //都属于不符合条件范围
            $row['result']            = false;
            $row['remark']            = "unSuggest {$remarkStr}";
            $checkData['list'][$date] = $row;
        }
        $this->logger->write_log("checkChangeShift " . json_encode($checkData), 'info');
        return $this->formatCheckData($params);
    }


    //整理验证后的结构 确定是否confirm 弹窗 等
    public function formatCheckData($param)
    {
        //判断 当前登录人是否是 主管
        $operator = $param['operator_id'];
        //Branch Supervisor or Assistant Branch Supervisor（主管强限制，其他角色弱限制）
        $operatorInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'columns'    => 'job_title',
            'bind'       => ['staff_id' => $operator],
        ]);

        if ($operator != 10000 && empty($operatorInfo)) {
            throw new ValidationException('wrong operator');
        }
        //是否是主管
        $jobTitle      = $operatorInfo->job_title;
        $supervisorJob = (new SettingEnvService)->getSetVal('shift_supervisor_job_title') ?? [];
        $supervisorJob = empty($supervisorJob) ? [] : explode(',', $supervisorJob);
        $this->isMaster      = in_array($jobTitle, $supervisorJob);

        //1 返回true 可以保存
        if (empty($this->unSuggestDates)) {
            return true;
        }

        //以下是存在不符合条件日期的返回结构
        //单天 修改
        if ($param['change_type'] == ShiftEnums::EFFECT_CURRENT) {
            //主管 单天修改 直接限制返回
            if ($this->isMaster) {
                throw new ValidationException(self::$t->_('workday_change_notice'));
            }

            //非主管 二次弹窗
            if(empty($param['is_submit'])){
                throw new ValidationException(self::$t->_('workday_change_confirm'),10086);
            }

            return true;
        }

        //多天修改
        $allDays = count($this->dateList);
        $unDays  = count($this->unSuggestDates);
        //是主管
        if ($this->isMaster) {
            //全部不符合 不能保存
            if ($allDays == $unDays) {
                throw new ValidationException(self::$t->_('workday_change_notice'));
            }
            //部分 不符合弹二次确认
            if(empty($param['is_submit'])){
                throw new ValidationException(self::$t->_('workday_master_change_confirm',['date_at' => $param['date_at'], 'days_num' => $unDays]),10086);
            }
            return true;
        }

        //非主管 都不符合条件 二次确认
        if ($allDays == $unDays) {
            if(empty($param['is_submit'])){
                throw new ValidationException(self::$t->_('workday_change_confirm'),10086);
            }
            return true;
        }
        //部分不符合 三个按钮
        if(empty($param['is_submit'])){
            throw new ValidationException(self::$t->_('workday_not_master_change_confirm',['date_at' => $param['date_at'], 'days_num' => $unDays]),10087);
        }
        return true;
    }

    //单天多天公共验证基础数据信息

    /**
     * @throws ValidationException
     */
    public function checkData($params)
    {
        $staff_info_id = $params['staff_info_id'];
        $shift_id      = $params['shift_id'];
        $shift_date    = $params['date_at'];

        $staffServer = new StaffInfoService();
        $staff_info  = $staffServer->getStaffInfo_hr($staff_info_id);

        if (empty($staff_info)) {
            throw new ValidationException(self::$t->_('create_staff_shift_middle_date_error_6'));
        }
        $this->staffInfo = $staff_info;

        $shift = HrShift::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $shift_id],
        ]);
        if (empty($shift)) {
            //未找到指定班次
            throw new ValidationException(self::$t->_('create_staff_shift_middle_date_error_2'));
        }

        //验证可选班次
        $selfShiftList = $this->getStaffConfigShiftList($staff_info['node_department_id'], $staff_info['job_title'], $staff_info['sys_store_id']);
        if(!empty($selfShiftList)){
            $selfIds = array_column($selfShiftList,'shift_id');
            if(!in_array($shift_id,$selfIds)){
                //可选班次维护 不为空 并且 不符合可选班次
                throw new ValidationException(self::$t->_('create_staff_shift_middle_date_error_2'));
            }
        }

        $this->changeShiftInfo = $shift;

        if ($shift_date < date('Y-m-d')) {
            //不能设置过去日期的班次
            throw new ValidationException(self::$t->_('create_staff_shift_middle_date_error_3'));
        }

        //如果到了今天23：30 不能修改班次 准备跑任务了
        $limitTime = date('Y-m-d 23:30:00');
        if(date('Y-m-d H:i:s') > $limitTime){
            throw new ValidationException(self::$t->_('staff_shift_tip'));
        }
        if (isset($params['modify_today_shift'])) {
            $shiftKey = "{$params['date_at']}_{$params['staff_info_id']}";
            if (!empty($this->shiftStaffData[$shiftKey]['start'])) {
                $newShiftStartTime = date('Y-m-d ') . $params['start'] . ':00';
                $currentTime = date('Y-m-d H:i:s');
                if($newShiftStartTime <= $currentTime){
                    throw new ValidationException(self::$t->_('edit_shift_notice'));
                }
            }
        }
    }


    /**
     * 轮休页面修改班次 当天及以后日期 保存预设表
     * @param $params
     * @return true
     * @throws ValidationException
     * @throws Exception
     */
    public function shiftSavePreset($params): bool
    {
        $this->checkData($params);
        //新增判断 如果都不符合条件 并且非 强制更新 提示
        $allDays = count($this->dateList);
        $unDays  = count($this->unSuggestDates);
        $this->getShiftInfo($params);

        $staff_info_id = $params['staff_info_id'];
        $shift_id      = $params['shift_id'];
        $shift_date    = $params['date_at'];
        $begin_day     = date("Y-m-01", strtotime($shift_date));
        $end_day       = date("Y-m-t", strtotime($shift_date));

        //验证一个月不能超过3个班次
        $p = ['staff_info_id'  => $staff_info_id,
              'begin_day'      => $begin_day,
              'end_day'        => $end_day,
              'after_shift_id' => $shift_id,
              'date_at'        => $params['date_at'],
              'is_preset'      => true,
        ];
        $this->verifyStaffShiftMonthCount($p);

        $allDate = $this->dateList;
        //如果 全部不符合条件 仍然点了 submit 表示全部更新
        //如果是 全部更新 不操作 reBackOldShift 方法
        if($this->isMaster && !empty($this->unSuggestDates)){//是主管只能部分更新
            $this->isShowMsg = true;
            $allDate = array_diff($allDate,$this->unSuggestDates);
        }
        if(!$this->isMaster && empty($params['update_all']) && !empty($this->unSuggestDates) && ($allDays != $unDays)){
            //非主管 可以部分更新 也可以 全部更新 只有部分更新的时候回填原班次
            $this->isShowMsg = true;
            $allDate = array_diff($allDate,$this->unSuggestDates);
        }
        if(empty($allDate)){
            return true;
        }
       
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try{

            $staffShift = HrStaffShiftModel::findFirst(
                [
                    'conditions' => 'staff_info_id = :staff_id:',
                    'bind'       => [
                        'staff_id' => $params['staff_info_id'],
                    ],
                ]
            );
            //理论上不存在
            if (empty($staffShift)) {
                $staffShift                = new HrStaffShiftModel();
                $staffShift->staff_info_id = $params['staff_info_id'];
            }
            $staffShift->shift_type    = $this->changeShiftInfo->type;
            $staffShift->start         = $this->changeShiftInfo->start;
            $staffShift->end           = $this->changeShiftInfo->end;
            $staffShift->shift_id      = $params['shift_id'];
            //预设的班次ID
            $staffShift->preset_shift_id = $params['shift_id'];
            $staffShift->save();


            $existShift = HrStaffShiftMiddleDateRepository::getStaffShiftMiddleDateList([
                'staff_info_ids' => [$params['staff_info_id']],
                'begin_day'      => $params['date_at'],
                'end_day'        => max($this->dateList),
            ]);

            $existShiftDate  = array_column($existShift, 'shift_date');
            $insertShiftDate = array_diff($allDate, $existShiftDate);
            if (!empty($insertShiftDate)) {
                $insertData = [];
                foreach ($insertShiftDate as $date) {
                    $row['shift_id']      = $params['shift_id'];
                    $row['staff_info_id'] = $params['staff_info_id'];;
                    $row['shift_date']  = $date;
                    $row['shift_type']  = $this->changeShiftInfo->type;
                    $row['shift_start'] = $this->changeShiftInfo->start;
                    $row['shift_end']   = $this->changeShiftInfo->end;
                    $insertData[]       = $row;
                }
                if ($insertData) {
                    (new HrStaffShiftMiddleDateModel())->batch_insert($insertData);
                }
            }

            $db_by = $this->getDI()->get('db_backyard');

            $updateInfo = ['shift_id' => $params['shift_id'],'shift_start'=>$this->changeShiftInfo->start,'shift_end'=>$this->changeShiftInfo->end,'shift_type'=>$this->changeShiftInfo->type,'operator_id'=>$params['operator_id']];
            $db_by->updateAsDict('hr_staff_shift_middle_date', $updateInfo,
                [
                    "conditions" => "shift_date in ('" . implode("','", $allDate) . "') and staff_info_id = {$params['staff_info_id']} and deleted = 0",
                ]);

            $db_by->updateAsDict('hr_staff_shift_middle_date', $updateInfo,
                [
                    "conditions" => "shift_date > ? and staff_info_id = ? and deleted = 0",
                    'bind'=>[max($this->dateList),$params['staff_info_id']],
                ]);

            //加操作日志
            $this->shiftChangeHrLog($params);

            $db->commit();

        }catch (\Exception $e){
            $db->rollback();
            $this->logger->write_log("shiftSaveMiddle save error " . json_encode($params).$e->getTraceAsString());
            throw $e;
        }

        //发消息 如果是导入规则 不发消息 那边发
        if(!empty($params['is_import'])){
            return true;
        }
        $flag = $this->shiftSaveSendMsg($params);
        if ($flag != ErrCode::SUCCESS) {
            $this->logger->write_log("shiftSaveMiddle send msg failed " . json_encode($params));
        }
        return true;
    }

    //如果存在部分不符合条件日期 要把这几天的班次 用旧班次加到middle表中
    public function reBackOldShift()
    {
        if(empty($this->unSuggestDates)){
            return ;
        }

        //清除 middle todo ?
        HrStaffShiftMiddleDateModel::find([
            'conditions' => 'staff_info_id = :staff_id: and shift_date in ({date_at:array})',
            'bind'       => [
                'staff_id' => $this->shiftInfo->staff_info_id,
                'date_at'  => $this->unSuggestDates,
            ],
        ])->update(['deleted' => 1]);

        $model = new HrStaffShiftMiddleDateModel();
        foreach ($this->unSuggestDates as $date) {
            $clone                = clone $model;
            $row['staff_info_id'] = $this->shiftInfo->staff_info_id;
            $row['shift_date']    = $date;
            $row['shift_id']      = $this->shiftInfo->shift_id;
            $row['shift_type']    = $this->shiftInfo->shift_type;
            $row['shift_start']   = $this->shiftInfo->start;
            $row['shift_end']     = $this->shiftInfo->end;
            $row['operator_id']   = 10000;//系统回补的 中间表
            $clone->create($row);
        }
    }


    //轮休页面修改班次 保存 中间表
    public function shiftSaveMiddle($params)
    {
        $this->getShiftInfo($params);
        $this->checkData($params);


        $staff_info_id = $params['staff_info_id'];
        $shift_id      = $params['shift_id'];
        $shift_date    = $params['date_at'];
        $begin_day     = date("Y-m-01", strtotime($shift_date));
        $end_day       = date("Y-m-t", strtotime($shift_date));


        //验证一个月不能超过3个班次
        $p = ['staff_info_id'  => $staff_info_id,
              'begin_day'      => $begin_day,
              'end_day'        => $end_day,
              'after_shift_id' => $shift_id,
              'date_at'        => $params['date_at'],
        ];
        $this->verifyStaffShiftMonthCount($p);


        //保存入库
        $params['start']      = $this->changeShiftInfo->start;
        $params['end']        = $this->changeShiftInfo->end;
        $params['shift_type'] = $this->changeShiftInfo->type;

        $flag = HrStaffShiftMiddleDateRepository::saveData($params);
        if (!$flag) {
            $this->logger->write_log("shiftSaveMiddle failed " . json_encode($params));
            throw new ValidationException('server error');
        }


        $this->shiftChangeHrLog($params);

        //发消息
        if(!empty($params['is_import'])){
            return true;
        }
        $flag = $this->shiftSaveSendMsg($params);
        if ($flag != ErrCode::SUCCESS) {
            $this->logger->write_log("shiftSaveMiddle send msg failed " . json_encode($params));
        }
        return true;
    }


    /**
     * 验证员工当月班次设置是否超过三个不同班次
     * @param $params
     * @return bool
     */
    public function verifyStaffShiftMonthCount($params)
    {
        //符合条件的员工 不验证 三个班次 只有菲律宾
        if(isCountry('PH')){
            $checkFlag = $this->noNeedCheckCount($this->staffInfo);//getStaffInfo_hr 方法的员工信息
            if($checkFlag){
                return true;
            }
        }elseif (in_array($this->staffInfo['sys_store_id'], explode(',', $this->settingEnvConfig['shift_no_limited_branch'])) ||
            in_array($this->staffInfo['category'], explode(',', $this->settingEnvConfig['shift_no_limited_type'])) ||
            in_array($this->staffInfo['node_department_id'],  $this->settingEnvConfig['shift_no_limited_department']))
        {
            return true;
        }

        $begin_day      = $params['begin_day'];
        $end_day        = $params['end_day'];
        $staff_info_id  = $params['staff_info_id'];
        $after_shift_id = $params['after_shift_id'];


        $shiftIds = [$params['date_at'] => $after_shift_id];
        $this->logger->write_log("afterShift {$params['staff_info_id']} " . json_encode(array_unique($shiftIds)), 'info');
        //中间表
        $mid_end_day = $end_day;
        $conditions = 'staff_info_id = :staff_id: and shift_date between :start_date: and :end_date: and deleted = 0';
        if(isset($params['is_preset'])){
            $mid_end_day = $params['date_at'];
            $conditions = 'staff_info_id = :staff_id: and shift_date >= :start_date: and shift_date < :end_date: and deleted = 0';
        }
        $tmpShiftData = HrStaffShiftMiddleDateModel::find([
            'columns'    => "shift_date,shift_id",
            'conditions' => $conditions,
            'bind'       => ['staff_id' => $staff_info_id, 'start_date' => $begin_day, 'end_date' => $mid_end_day],
        ])->toArray();

        if (!empty($tmpShiftData)) {
            $shiftIds = array_merge(array_column($tmpShiftData, 'shift_id', 'shift_date'), $shiftIds);
            $this->logger->write_log("tmpShift {$params['staff_info_id']} " . json_encode(array_unique($shiftIds)), 'info');
            $this->checkShiftNum($shiftIds);
        }

        return true;
    }

    protected function checkShiftNum($shiftIds)
    {
        $max_num = (new SettingEnvService())->getSetVal('month_edit_shift_num') ? : ShiftEnums::MAX_SHIFT_TYPE;

        $num = count(array_unique(array_values($shiftIds)));
        if ($num > $max_num) {
            throw new ValidationException(self::$t->_('create_staff_shift_middle_date_error_1',['num'=>$max_num]));
        }
        return;
    }

    //不需要验证班次次数的配置
    public function noNeedCheckCount($staffInfo){
        //是否转正字段
        $probationInfo = HrProbationModel::findFirst("staff_info_id = {$staffInfo['id']}");
        $probationInfo = empty($probationInfo) ? [] : $probationInfo->toArray();
        //角色
        $staffInfoPositions = (new HrStaffService())->getStaffInfoPositions([$staffInfo['id']]);

        $params = [
            'w_f_condition_staff_id'           => $staffInfo['id'],
            'w_f_condition_store_id'           => $staffInfo['sys_store_id'],
            'w_f_condition_job_title'          => $staffInfo['job_title'],
            'w_f_condition_sex'                => $staffInfo['sex'],
            'w_f_condition_state'              => $staffInfo['state'],
            'w_f_condition_job_title_grade_v2' => $staffInfo['job_title_grade_v2'],
            'w_f_condition_node_department_id' => $staffInfo['node_department_id'],
            'w_f_condition_hire_type'          => $staffInfo['hire_type'],
            'w_f_condition_category'           => $staffInfo['category'],
            'w_f_condition_probation'          => empty($probationInfo['formal_at']) ? GlobalEnums::OPTION_YES: GlobalEnums::OPTION_NO,
            'w_f_condition_position_category'  => $staffInfoPositions[$staffInfo['id']] ?? [],
            'w_f_condition_nationality'        => $staffInfo['nationality'],
        ];
        $conditionsResult = ConditionsRulesService::getInstance()
            ->setRuleKey('shift_no_limited')
            ->loadParameters($params)
            ->getConfig();

        $flag = false;
        if(!empty($conditionsResult['response_type']) && $conditionsResult['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            //处理数据
            $flag = $conditionsResult['response_data'];
        }
        return $flag;
    }


    //保存完班次以后 发消息
    public function shiftSaveSendMsg($param)
    {
        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return true;
        }

        $staffLang = (new StaffService())->getAcceptLanguage($param['staff_info_id']);
        $t = BaseService::getTranslation($staffLang);
        $shiftKey = "{$param['date_at']}_{$param['staff_info_id']}";
        $id = time() . $param['staff_info_id'] . rand(1000000, 9999999);
        //整理新旧班次信息内容
        $shiftType                 = $this->shiftStaffData[$shiftKey]['shift_type'] ?? '';
        $shiftType                 = strtolower($shiftType);
        $t_data['before_shift']    = empty($this->shiftStaffData[$shiftKey]) ? '' : $t->_($shiftType) . '，' . $this->shiftStaffData[$shiftKey]['start'] . '-' . $this->shiftStaffData[$shiftKey]['end'];
        $shiftTypeAfter            = $this->changeShiftInfo->type;
        $shiftTypeAfter            = strtolower($shiftTypeAfter);
        $t_data['after_shift']     = $t->_($shiftTypeAfter) . '，' . $this->changeShiftInfo->start . '-' . $this->changeShiftInfo->end;
        $t_data['staff_info_name'] = $this->staffInfo['name'];
        $t_data['staff_info_id']   = $param['staff_info_id'];
        $t_data['job_title']       = $this->staffInfo['job_title_name'];
        $t_data['effective_date']  = $param['date_at'];

        //修改当天 未来日期班次
        $title = $content = '';
        if ($param['change_type'] == ShiftEnums::EFFECT_FUTURE) {
            $title          = $t->_('update_staff_shift_send_message_title_change');
            $insertStr = '';
            if($this->isShowMsg){
                $p['days'] = count($this->unSuggestDates);
                $p['before_shift'] = $t_data['before_shift'];
                $insertStr = $t->_('skipped_days', $p);
            }
            $t_data['skipped_days'] = $insertStr;
            $content        = $t->_('update_staff_shift_send_message_change', $t_data);
        }

        //修改一天班次
        if ($param['change_type'] == ShiftEnums::EFFECT_CURRENT) {
            $title   = $t->_('update_staff_shift_send_message_title_change');
            $content = $t->_('update_staff_shift_send_message_change_v2', $t_data);
        }

        $kit_param['staff_info_ids_str'] = "{$param['staff_info_id']}";
        $kit_param['staff_users']        = [$param['staff_info_id']];
        $kit_param['message_title']      = $title;
        $kit_param['message_content']    = $content;
        $kit_param['id']                 = $id;
        $kit_param['category']           = MessageModel::MESSAGE_CATEGORY_SHIFT;
        $data                            = (new MessagesService())->add_kit_message($kit_param);
        $kitId = empty($data[2]) ? null :current($data[2]);
        if (isset($param['modify_today_shift']) && $kitId) {
            //发push
            $pushData   = [
                "staff_info_id"   => $param['staff_info_id'],  //推送人员工ID
                "src"             => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title"   => $title,  //标题
                "message_content" =>  preg_replace('/<.*?>/', '',$content),//内容
                'message_scheme'  => "flashbackyard://fe/page?path=message&messageid=" . $kitId,
            ];
            $client = new ApiClient('bi_rpc', '', 'push_to_staff');
            $client->withParam($pushData)->execute();
            $client = new ApiClient('bi_rpc', '', 'push_to_staff');
            $pushData['src'] = 'kit';
            $client->withParam($pushData)->execute();
        }
        return $data[1];//这是返回的code
    }

    //员工操作记录 日志
    public function shiftChangeHrLog($param)
    {
        $type = $logType = '';
        if ($param['change_type'] == ShiftEnums::EFFECT_FUTURE) {
            $type                    = 'preset-shift-hcm';
            $after['effective_date'] = $param['date_at'];
            $logType = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
        }
        if ($param['change_type'] == ShiftEnums::EFFECT_CURRENT) {
            $type                     = 'middle-shift-hcm';
            $after['attendance_date'] = $param['date_at'];
            $logType = HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT;
        }

        $shiftKey = "{$param['date_at']}_{$param['staff_info_id']}";

        $before['start'] = $this->shiftStaffData[$shiftKey]['start'];
        $before['end']   = $this->shiftStaffData[$shiftKey]['end'];
        $before['shift'] = $this->shiftStaffData[$shiftKey]['shift_id'];

        $after['start'] = $this->changeShiftInfo->start;
        $after['end']   = $this->changeShiftInfo->end;
        $after['shift'] = $this->changeShiftInfo->id;

        $data   = [
            [
                "operater"      => $param['operator_id'],
                "staff_info_id" => $param['staff_info_id'],
                "type"          => $type,
                "before"        => json_encode(['body' => $before], JSON_UNESCAPED_UNICODE),
                "after"         => json_encode(['body' => $after], JSON_UNESCAPED_UNICODE),
            ],
        ];
        $client = new ApiClient('hris', '', 'add_operate_logs');
        $client->setParams($data);
        $client->execute();
        //日志表
        //多天修改
        $allDays = count($this->dateList);
        $unDays  = count($this->unSuggestDates);
        if($allDays != $unDays){
            $allDate = array_diff($this->dateList,$this->unSuggestDates);
            $logServer                 = new WorkShiftService();
            $logParam['staff_info_id'] = $param['staff_info_id'];
            $logParam['date_at']       = min($allDate);
            $logParam['operate_id']    = $param['operator_id'];
            $extend['before']          = "{$before['start']}-{$before['end']}";
            if($param['change_type'] == ShiftEnums::EFFECT_FUTURE){
                //如果是修改未来多天 记录不上原来班次  不知道取哪天的
                $extend['before'] = '';
            }
            $extend['after']           = "{$after['start']}-{$after['end']}";
            $logServer->addShiftLog($logType, $logParam, $extend);
        }
        $this->logger->write_log("shiftChangeHrLog " . json_encode($param) . json_encode($data), 'info');
        return;
    }

    //获取对应日期的真实班次 发消息和写日志用
    protected function getShiftInfo($params){
        $shiftKey = "{$params['date_at']}_{$params['staff_info_id']}";
        if (empty($this->shiftStaffData[$shiftKey])){
            $dateList = [$params['date_at']];
            $tomorrow = date('Y-m-d',strtotime('+1 day'));
            if($params['date_at'] > $tomorrow && !isCountry('MY')){
                $dateList = DateHelper::DateRange(strtotime($tomorrow),strtotime($params['date_at']));
            }
            $shiftServer = new HrShiftService();
            $shiftServer = reBuildCountryInstance($shiftServer);
            $this->shiftStaffData = $shiftServer->getShiftInfos([$params['staff_info_id']], $dateList);
        }
    }


    /**
     * 给班次变动的员工发消息通知
     * @param $time
     * @param $staffInfoIds
     * @return bool
     * @throws Exception
     */
    public function shiftChangeNotice($time,$staffInfoIds): bool
    {
        $this->today        = date('Y-m-d');
        $this->tomorrow     = date('Y-m-d', strtotime($this->today . ' +1 day'));
        $hrShiftServer      = new HrShiftService();
        $shiftInfo          = $hrShiftServer->getList();
        $this->jobTitleList = array_column((new HrJobTitleService())->getJobTitleList(false), 'job_name', 'id');
        $this->freeShiftJobTitle = array_values(array_filter((new SettingEnvService)->getSetVal('free_shift_position',',')));

        foreach ($shiftInfo as &$item) {
            $item['end']                        = $item['end'] == '00:00' ? '24:00' : $item['end'];
            $item['is_cross_day']               = $item['end'] < $item['start'];//是否跨天
            $this->shiftStringList[$item['id']] = $item['start'] . '-' . $item['end'];
        }

        $shiftInfo = array_column($shiftInfo, null, 'end');
        ksort($shiftInfo);

        //本次跑脚本需要处理的班次
        $needCheckShift = [];
        foreach ($shiftInfo as $value) {
            if ($time < $value['end']) {
                $needCheckShift = $value;
                break;
            }
        }
        if(empty($needCheckShift)){
            throw new Exception('当前时间未发现待验证的班次');
        }

        if ($needCheckShift['is_cross_day']) {
            //跨天班次 需要查 昨天和今天的班次信息进行比对
            $needSendMessageStaff = $this->getCrossDayShiftNeedNoticeStaff($this->today, $needCheckShift['id'],$staffInfoIds);
        } else {
            //非跨天班次 查今天和明天的班次信息进行比对
            $needSendMessageStaff = $this->getUnCrossDayShiftNeedNoticeStaff($this->today, $needCheckShift['id'],$staffInfoIds);
        }

        if (empty($needSendMessageStaff)) {
            return true;
        }

        return $this->sendShiftNoticeMsg($needSendMessageStaff);

    }


    public function sendShiftNoticeMsg($needSendMessageStaff){
        $allStaffIds   = array_keys($needSendMessageStaff);
        //获取已发送记录
        $logData       = (new StaffShiftChangeNoticeLogRepository())->getList('staff_info_id in ({staff_info_id:array}) and date_at in ({date_at:array})',
            ['staff_info_id' => $allStaffIds, 'date_at' => [$this->today, $this->tomorrow]], "concat(staff_info_id,'-',date_at) as unique_key" );
        $logData       = array_column($logData, 'unique_key');
        $staffsLang    = (new StaffService())->getStaffEquipmentLanguage($allStaffIds);

        $insertData = [];
        foreach ($needSendMessageStaff as $staff) {
            //如果发送过消息 跳过不发
            if (in_array($staff['staff_info_id'] . '-' . $staff['date_at'],$logData)) {
                continue;
            }
            $staffLang                       = $staffsLang[$staff['staff_info_id']];
            $t                               = BaseService::getTranslation($staffLang);
            $id                              = time() . $staff['staff_info_id'] . rand(1000000, 9999999);
            $title                           = $t->_('update_staff_shift_send_message_title_change');
            $content                         = $t->_('shift_change_notice_beforepunch', [
                'staff_info_name' => $staff['name'],
                'staff_info_id'   => $staff['staff_info_id'],
                'job_title'       => $this->jobTitleList[$staff['job_title']],
                'effective_date'  => $staff['date_at'],
                'after_shift'     => $this->shiftStringList[$staff['after_shift_id']],
            ]);
            $kit_param['staff_info_ids_str'] = "{$staff['staff_info_id']}";
            $kit_param['staff_users']        = [$staff['staff_info_id']];
            $kit_param['message_title']      = $title;
            $kit_param['message_content']    = $content;
            $kit_param['id']                 = $id;
            $kit_param['category']           = MessageModel::MESSAGE_CATEGORY_SHIFT;
            $data                            = (new MessagesService())->add_kit_message($kit_param);
            if ($data[1] == 1) {
                $insertData[] = [
                    'staff_info_id'   => $staff['staff_info_id'],
                    'date_at'         => $staff['date_at'],
                    'content'         => $content,
                    'before_shift_id' => $staff['before_shift_id'],
                    'after_shift_id'  => $staff['after_shift_id'],
                ];
            }
        }
        $insertData && (new StaffShiftChangeNoticeLogModel())->batch_insert($insertData);
        return true;
    }

    /**
     * 非跨天班次 当前时间是1号，查2号的考勤日
     * 结束时间是24:00及之前的都是非跨天班次
     * @param $today
     * @param $shift_id
     * @param $staffInfoIds
     * @return array
     */
    public function getUnCrossDayShiftNeedNoticeStaff($today, $shift_id,$inputStaffInfoIds): array
    {
        $tomorrow      = date('Y-m-d', strtotime($today . ' +1 day'));
        //中间表设置的班次 也会是今天的
        $builder = $this->modelsManager->createBuilder();
        $column  = 'ssmd_today.shift_id as before_shift_id,ssmd_tomorrow.shift_id as after_shift_id,ssmd_today.staff_info_id';
        $builder->columns($column);
        $builder->from(['ssmd_today' => HrStaffShiftMiddleDateModel::class]);
        $builder->innerJoin(HrStaffShiftMiddleDateModel::class, 'ssmd_today.staff_info_id = ssmd_tomorrow.staff_info_id', 'ssmd_tomorrow');
        $builder->where('ssmd_today.shift_date = :today: and ssmd_today.shift_id = :shift_id: and ssmd_tomorrow.shift_date = :tomorrow: and ssmd_today.shift_id != ssmd_tomorrow.shift_id and ssmd_today.deleted = 0  and ssmd_tomorrow.deleted = 0 ',
            ['today' => $today, 'shift_id' => $shift_id,'tomorrow' => $tomorrow]);
        if(!empty($inputStaffInfoIds)){
            $builder->andWhere('ssmd_today.staff_info_id in ({inputStaffInfoIds:array})',['inputStaffInfoIds'=>$inputStaffInfoIds]);
        }
        $needSendStaff = $builder->getQuery()->execute()->toArray();

        if (empty($needSendStaff)) {
            return $needSendStaff;
        }

        return $this->getNeedSendStaff($needSendStaff,$tomorrow);
    }


    /**
     * 结束时间是8点30及之前的，都是跨天班次
     * 跨天班次  比如当前时间是2号，但是是1号的考勤日
     * 需要拿HrStaffShiftHistoryModel 昨天 和 HrStaffShiftModel 、HrStaffShiftMiddleDateModel 今天
     * @param $today
     * @param $shift_id
     * @param $inputStaffInfoIds
     * @return array
     */
    public function getCrossDayShiftNeedNoticeStaff($today, $shift_id, $inputStaffInfoIds): array
    {
        $yesterday = date('Y-m-d', strtotime($today . ' -1 day'));
        //昨天的班次与今天的班次不相同
        $builder = $this->modelsManager->createBuilder();
        $column  = 'ssh.shift_id as before_shift_id,ssh.staff_info_id,ssmd.shift_id as after_shift_id';
        $builder->columns($column);
        $builder->from(['ssmd' => HrStaffShiftMiddleDateModel::class]);
        $builder->innerJoin(HrStaffShiftHistoryModel::class,
            'ssh.staff_info_id = ssmd.staff_info_id and ssmd.deleted = 0 ', 'ssh');
        $builder->where('ssh.shift_id = :shift_id: and ssh.shift_day = :yesterday: and ssmd.shift_date = :today: and ssmd.shift_id != ssh.shift_id ',
            ['shift_id' => $shift_id, 'yesterday' => $yesterday, 'today' => $today]);
        if (!empty($inputStaffInfoIds)) {
            $builder->andWhere('ssh.staff_info_id in ({inputStaffInfoIds:array})',
                ['inputStaffInfoIds' => $inputStaffInfoIds]);
        }

        $needSendStaff = $builder->getQuery()->execute()->toArray();

        if (empty($needSendStaff)) {
            return $needSendStaff;
        }
        return $this->getNeedSendStaff($needSendStaff, $today);
    }

    protected function getNeedSendStaff($needSendStaff, $date_at)
    {
        if(empty($needSendStaff)){
            return [];
        }
        $staff_info_ids = array_column($needSendStaff, 'staff_info_id');

        //用来过滤一下员工
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('i.staff_info_id,i.job_title,i.name');
        $builder->from(['i' => HrStaffInfoModel::class]);
        $builder->where('i.formal in ({formal:array}) and i.state = :state: and i.is_sub_staff = 0 ',
            [
                'formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'state'  => HrStaffInfoModel::STATE_ON_JOB,
            ]);
        $builder->andWhere('i.staff_info_id in ({staff_info_id:array})', ['staff_info_id' => $staff_info_ids]);
        if (!empty($this->freeShiftJobTitle)) {
            $builder->andWhere('i.job_title not in ({not_job_title:array})',
                ['not_job_title' => $this->freeShiftJobTitle]);
        }
        $builder->andWhere('i.hire_type != :hire_type:', ['hire_type' => HrStaffInfoModel::HIRE_TYPE_UN_PAID]);
        $validateStaffs = $builder->getQuery()->execute()->toArray();
        $validateStaffs = array_column($validateStaffs, null, 'staff_info_id');

        $result = [];
        foreach ($needSendStaff as $info) {
            if (array_key_exists($info['staff_info_id'], $validateStaffs)) {
                $result[$info['staff_info_id']] = [
                    'staff_info_id'   => $info['staff_info_id'],
                    'job_title'       => $validateStaffs[$info['staff_info_id']]['job_title'],
                    'name'            => $validateStaffs[$info['staff_info_id']]['name'],
                    'date_at'         => $date_at,
                    'after_shift_id'  => $info['after_shift_id'],
                    'before_shift_id' => $info['before_shift_id'],
                ];
            }
        }
        return $result;
    }


    /**
     * 固化 班次数据
     * @param $date
     * @throws Exception
     */
    public function collectStaffShiftToday($shift_day)
    {
        $where['state']      = [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND];
        $where['begin_date'] = date('Y-m-d',strtotime(' -30 days'));

        $currentShift = (new \App\Repository\HrStaffShiftRepository())->getStaffShiftList($where, ['hss.*']);

        $sql = "delete from hr_staff_shift_history where shift_day = :shift_day";
        $query_param = [
            'shift_day' => $shift_day,
        ];
        $db  = $this->getDI()->get('db_backyard');
        $db->execute($sql, $query_param);

        $staff_shift_middle_list = HrStaffShiftMiddleDateRepository::getStaffShiftMiddleByShiftDate($shift_day);

        $insertData              = [];
        foreach ($currentShift as $value) {
            $staffId     = $value['staff_info_id'];
            $shift_start = $value['start'] ?? '';
            $shift_end   = $value['end'] ?? '';
            $shift_type  = $value['shift_type'] ?? '';
            $shift_id    = $value['shift_id'] ?? 0;
            if (isset($staff_shift_middle_list[$staffId])) {
                $shift_id    = $staff_shift_middle_list[$staffId]['shift_id'] ?? 0;
                $shift_start = $staff_shift_middle_list[$staffId]['shift_start'] ?? '';
                $shift_end   = $staff_shift_middle_list[$staffId]['shift_end'] ?? '';
                $shift_type  = $staff_shift_middle_list[$staffId]['shift_type'] ?? '';
            }
            $insertData[] = [
                'staff_info_id' => $staffId,
                'start'         => $shift_start,
                'end'           => $shift_end,
                'shift_type'    => $shift_type,
                'shift_id'      => $shift_id,
                'shift_day'     => $shift_day,
            ];
        }
        $model = new HrStaffShiftHistoryModel();
        $model->batch_insert($insertData);

        $this->logger->info('班次数据固化完成，总数量: '.count($currentShift). ' res:'.count($insertData));
    }


    /**
     * 创建未来的班次
     * @return true|void
     * @throws Exception
     */
    public function staffFutureShiftCreate($dateList)
    {
        $this->logger->info(['staffFutureShiftCreate'=>$dateList]);

        $hrStaffShiftService = new HrStaffShiftRepository();
        $allStaffs = $hrStaffShiftService->getStaffShiftList([
            'state'                => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
            'formal'               => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
            'begin_date'           => date('Y-m-d', strtotime(' -30 days')),
        ], ['hss.*']);
        $osStaffs = $hrStaffShiftService->getStaffShiftList([
            'state'                => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
            'formal'               => [HrStaffInfoModel::FORMAL_0],
            'staff_type'           => [HrStaffInfoModel::STAFF_TYPE_1],
            'begin_date'           => date('Y-m-d', strtotime(' -30 days')),
        ], ['hss.*']);
        $allStaffs = array_merge($allStaffs, $osStaffs);
        if (empty($allStaffs)) {
            return true;
        }

        $shiftInfo = HrShift::find()->toArray();
        $shiftInfo = array_column($shiftInfo, null, 'id');

        $allStaffs = array_chunk($allStaffs, 1000);
        foreach ($allStaffs as $allStaff) {
            $insertData = [];
            $staff_ids  = array_column($allStaff, 'staff_info_id');
            $middleData = HrStaffShiftMiddleDateModel::find([
                'columns'    => "staff_info_id,shift_date,concat(staff_info_id,'-',shift_date) as unique_key",
                "conditions" => 'shift_date in ({shift_date:array}) and deleted = 0 and staff_info_id in ({staff_info_id:array})',
                "bind"       => ['shift_date' => $dateList, 'staff_info_id' => $staff_ids],
            ])->toArray();
            $middleData = array_column($middleData, 'unique_key');
            foreach ($allStaff as $staff) {
                foreach ($dateList as $date) {
                    $unique_key = $staff['staff_info_id'] . '-' . $date;
                    //已经设置过了，就跳过，一天不能出现多条
                    if (in_array($unique_key, $middleData)) {
                        continue;
                    }
                    $shift_id             = $staff['preset_shift_id'] ?: $staff['shift_id'];
                    $row['shift_id']      = $shift_id;
                    $row['staff_info_id'] = $staff['staff_info_id'];
                    $row['shift_date']    = $date;
                    $row['shift_type']    = $shiftInfo[$shift_id]['type'];
                    $row['shift_start']   = $shiftInfo[$shift_id]['start'];
                    $row['shift_end']     = $shiftInfo[$shift_id]['end'];
                    $insertData[]         = $row;
                }
            }
            if ($insertData) {
                (new HrStaffShiftMiddleDateModel())->batch_insert($insertData);
            }
        }
    }


    //还原 支援主账号班次
    public function shiftReBack($staffData,$dateList){
        $masterId = $staffData['staff_info_id'];

        //恢复主账号班次信息
        $staffBackupShift = HrStaffShiftMiddleBackupModel::find([
            'conditions' => ' staff_info_id = :staff_info_id: and shift_date in ({dates:array}) and deleted = 0',
            'bind'       => [
                'staff_info_id' => $masterId,
                'dates'         => $dateList,
            ],
        ]);

        if(empty($staffBackupShift->toArray())){
            $this->logger->info("backup shift empty {$masterId} " . json_encode($dateList));
        }

        if (!empty($staffBackupShift->toArray())) {
            //删除多余支援日期的主账号班次
            HrStaffShiftMiddleDateModel::find([
                'conditions' => 'shift_date in ({dates:array}) and staff_info_id = :staff_id:',
                'bind'       => [
                    'staff_id' => $masterId,
                    'dates'    => $dateList,
                ],
            ])->update(['deleted' => 1]);

            $staffShift = [];
            foreach ($staffBackupShift as $shift) {
                $staffShift[] = [
                    'staff_info_id' => $shift->staff_info_id,
                    'shift_date'    => $shift->shift_date,
                    'shift_id'      => $shift->shift_id,
                    'shift_type'    => $shift->shift_type,
                    'shift_start'   => $shift->shift_start,
                    'shift_end'     => $shift->shift_end,
                    'operator_id'   => $shift->operator_id,
                ];
            }
            (new HrStaffShiftMiddleDateModel())->batch_insert($staffShift, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
            //删除备份数据
            $staffBackupShift->update(['deleted' => 1]);
        }

        $this->logger->write_log("shiftReBack {$masterId} " . json_encode($dateList), 'info');
        return true;
    }


    //备份新增 支援主账号班次
    public function shiftBackup($afterSupportData, $dateList)
    {
        $masterId = $afterSupportData['staff_info_id'];
        //备份主账号班次信息
        $staffShift = HrStaffShiftMiddleDateModel::find([
            'conditions' => ' staff_info_id = :staff_info_id: and shift_date in ({dates:array}) and deleted = 0',
            'bind'       => [
                'staff_info_id' => $masterId,
                'dates'         => $dateList,
            ],
        ]);
        //如果没班次数据 说明超过2月
        if(empty($staffShift->toArray())){
            $this->logger->write_log("shiftBackup no shift info {$masterId} " . json_encode($dateList),'info');
            return true;
        }


        if (!empty($staffShift->toArray())) {
            $staffShiftBackup = [];
            foreach ($staffShift as $shift) {
                $staffShiftBackup[] = [
                    'staff_info_id' => $shift->staff_info_id,
                    'shift_date'    => $shift->shift_date,
                    'shift_id'      => $shift->shift_id,
                    'shift_type'    => $shift->shift_type,
                    'shift_start'   => $shift->shift_start,
                    'shift_end'     => $shift->shift_end,
                    'operator_id'   => $shift->operator_id,
                    'remark'        => 'hcm edit',
                ];
            }
            (new HrStaffShiftMiddleBackupModel())->batch_insert($staffShiftBackup, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
            $staffShift->update(['deleted' => 1]);
        }

        $staffNewShift = $subStaffShit = [];
        foreach ($dateList as $day) {
            $staffNewShift[] = [
                'staff_info_id' => $afterSupportData['staff_info_id'],
                'shift_date'    => $day,
                'shift_id'      => $afterSupportData['shift_id'],
                'shift_type'    => $afterSupportData['shift_type'],
                'shift_start'   => $afterSupportData['shift_start'],
                'shift_end'     => $afterSupportData['shift_end'],
                'operator_id'   => $afterSupportData['operator_id'] ?? 10000,
            ];
            if (!empty($afterSupportData['sub_staff_info_id'])) {
                $subStaffShit[] = [
                    'staff_info_id' => $afterSupportData['sub_staff_info_id'],
                    'shift_date'    => $day,
                    'shift_id'      => $afterSupportData['shift_id'],
                    'shift_type'    => $afterSupportData['shift_type'],
                    'shift_start'   => $afterSupportData['shift_start'],
                    'shift_end'     => $afterSupportData['shift_end'],
                    'operator_id'   => $afterSupportData['operator_id'] ?? 10000,
                ];
            }
        }
        $model = new HrStaffShiftMiddleDateModel();
        $model->batch_insert(array_merge($staffNewShift, $subStaffShit), BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        $this->logger->write_log("shiftBackup {$masterId} " . json_encode($dateList),'info');
        return true;
    }


    public function getShiftInfoForSvc($local,$params)
    {
        BaseService::setLanguage($local['locale']);

        if (empty($params['staff_id'])) {
            throw new ValidationException('need staff_id');
        }
        if (empty($params['date'])) {
            throw new ValidationException('need date');
        }
        $shiftServer = new HrShiftService();
        $shiftServer = reBuildCountryInstance($shiftServer);
        $shiftInfo   = $shiftServer->getShiftInfos([$params['staff_id']], [$params['date']]);
        $shiftInfo   = current($shiftInfo);
        if (empty($shiftInfo['shift_info_v2'])) {
            $shiftInfo['shift_info_v2'] = $shiftInfo['shift_info'];
        }
        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $shiftInfo];
    }

}
