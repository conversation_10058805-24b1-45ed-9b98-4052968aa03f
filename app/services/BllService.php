<?php

namespace App\Services;

use App\Controllers\BaseController;
use App\Library\CacheKeyEnums;
use App\Library\Enums\ApprovalEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\HttpCurl;
use App\Library\OssHelper;
use App\Library\StaffHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\SickLeaveCertificateDetailModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffLeavePropertyModel;
use App\Models\fle\StaffInfoPositionModel;
use App\Services\StaffInfoService;
use App\Models\backyard\StaffAuditImageModel;
use App\Services\SysStoreService;
use App\Library\ApiClient;
use App\Library\BaseService;
use App\Models\backyard\BusinessTripModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrOvertimeModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\StaffDaysForLeaveModel;
use App\Models\backyard\StaffDaysFreezeModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\bi\HrStaffManageRegionsModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Library\Enums\StaffEnums;
use Exception;
use App\Library\Enums;

class BllService extends BaseService
{

    // 角色id
    const SUPER_ADMIN = 99; // 超级管理员
    const SYSTEM_ADMIN = 14; // 系统管理员

    // 角色ID
    const HRBP = 68; // hrbp
    const PAYROLL_MANAGER = 42; // payroll
    const HR_SERVICE = 77; // HR_SERVICE
    public static $sorting_no_map = [
        'B' => 'BKK' ,
        'C' => 'Central' ,
        'N' => 'North' ,
        'NE' => 'NorthEast' ,
        'S' => 'South' ,
    ];

    public static $admin_role = array(
        'SUPER_ADMIN' => 99,
        'PERSONNEL_MANAGER' => 17,
        'PERSONNEL_COMMISSIONER' => 16,
        'HR_OFFICER' => 43,
        'PAYROLL_MANAGER' => 42,
        'HRIS_MANAGER' => 41,
        'SYSTEM_ADMIN' => 14,
        'QAQC_SPECIALIST' => 52,
        'SECURITY_MANAGER' => 30,
    );

    public $export = false;//是否是导出

    public static $s_v_param = [

    ];

    //网点
    public function getSysStoreList ($areas = '')
    {
        $SysStoreModel = new  SysStoreService();
        $data = $SysStoreModel->getSysStoreList($areas);
        return $data;
    }

    //请假类型  by 接口 ot
    public function getTypeBookOtList ($type_param)
    {
        $key = CacheKeyEnums::LEAVE_OT_TYPE_DATA.$type_param.static::$language.'_';
        $info = $this->getCache($key);
        if($info){
            return json_decode($info,true);
        }

        $api = new ApiClient('by' , '' , $type_param , static::$language);
        $api->setParams([[]]);
        $result = $api->execute();
        if (isset($result['error'])) {
            throw new ValidationException($result['error']);
        }
        //获取请假类型
        $type_book = empty($result) ? [] : $result['data'];
        $this->setCache($key,json_encode($type_book),10 * 60);
        return $type_book;
    }

    /**获取by 员工请假列表
     * @param $param
     * @param bool $export
     * @return mixed
     * @throws BusinessException
     */
    public function getLeaveList($param , $export = false)
    {
        $timeZone = env('timeZone', '+07:00');
        $staff_id = $param['staff_info_id'];
        $this->export = $export;

        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->from(['s' => StaffAuditModel::class]);
        $audit_object->innerJoin(HrStaffInfoReadModel::class, 's.staff_info_id = i.staff_info_id', 'i');
        $audit_object->leftJoin(SysStoreModel::class, 'i.sys_store_id = store.id', 'store');

//        //通用数据权限限定条件
        $condition = (new AttendanceStatisticsService())->buildStaffPurviewData($param['current_uid'],'i');
        if($condition['condition'] && $condition['bind']){
            $audit_object->andWhere($condition['condition'] , $condition['bind'] );
        }

        //申请类型
        $audit_object->andWhere("s.audit_type = :audit_type:", ['audit_type' => 2]);

//        //在职状态
//        if(!empty($param['staff_state'])){
//            $audit_object->inWhere("i.state", $param['staff_state']);
//        }


        //在职状态
        if(!empty($param['staff_state'])){
            $tmp_sql = [];
            if(in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION,$param['staff_state'])){
                $tmp_sql[] = '( i.state = 1 and i.wait_leave_state  = 1 )';
            }
            if(in_array(HrStaffInfoModel::STATE_ON_JOB,$param['staff_state'])){
                $tmp_sql[] = '( i.state = '.HrStaffInfoModel::STATE_ON_JOB .' and i.wait_leave_state  = 0) ';
            }
            if(in_array(HrStaffInfoModel::STATE_RESIGN,$param['staff_state'])){
                $tmp_sql[] = 'i.state = '.HrStaffInfoModel::STATE_RESIGN;
            }
            if(in_array(HrStaffInfoModel::STATE_SUSPEND,$param['staff_state'])){
                $tmp_sql[] = 'i.state = '.HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ',$tmp_sql);
            $audit_object->andWhere($_sql);
        }


        //审批状态
        if (!empty($param['audit_status'])) {
            $audit_object->inWhere("s.status", $param['audit_status']);
        }

        //过滤部门
//        if (!empty($param['department'])) {
//            $audit_object->andWhere("i.node_department_id = :department:", ['department'=>$param['department']]);
//        }
        if (!empty($param['department_ids'])) {
            $audit_object->inWhere("i.node_department_id", $param['department_ids']);
        }

        //过滤职位
        if (!empty($param['job_title'])) {
            $audit_object->inWhere("i.job_title", $param['job_title']);
        }

        if (!empty($param['hire_type'])) {
            $audit_object->inWhere("i.hire_type", $param['hire_type']);
        }


        //实际时间过滤
        $apply_time_sql = '';
        $apply_time_bind_1 = $apply_time_bind_2 = [];
        if(!empty($param['apply_start_time'])){
            $apply_time_sql = ' s.leave_end_time >= :apply_start_time: ';
            $apply_time_bind_1 = ['apply_start_time'=>gmdate('Y-m-d H:i:s',$param['apply_start_time'])];
        }
        if(!empty($param['apply_end_time'])){
            $tmp_sql = $apply_time_sql ? ' and ':'';
            $apply_time_sql .= $tmp_sql. ' s.leave_start_time <= :apply_end_time: ';
            $apply_time_bind_2 = ['apply_end_time'=>gmdate('Y-m-d H:i:s',$param['apply_end_time']+86399)];
        }

        if(!empty($apply_time_sql)){
            $audit_object->andWhere($apply_time_sql,array_merge($apply_time_bind_1,$apply_time_bind_2));
        }
        //申请时间
        if(!empty($param['submit_start_time']) && !empty($param['submit_end_time'])) {
            $_submit_start_time = gmdate('Y-m-d H:i:s', $param['submit_start_time'] - $this->config->application->add_hour*3600);
            $_submit_end_time = gmdate('Y-m-d H:i:s', $param['submit_end_time'] + 86399 - $this->config->application->add_hour*3600);
            $audit_object->betweenWhere("s.created_at", $_submit_start_time, $_submit_end_time);
        }

        //查找员工
        if (!empty($staff_id)) {
            $s_id = intval($staff_id);
            if ($s_id > 0) {
                $audit_object->andWhere("i.staff_info_id = :staff_info_id:", ['staff_info_id' => $s_id]);
            } else {
                $audit_object->andWhere("i.name like :name:", ['name' => "%{$staff_id}%"]);
            }
        }

        //过滤网点
        if (!empty($param['store_id'])) {
            $audit_object->inWhere("i.sys_store_id", $param['store_id']);
        }

        //请假类型
        if (!empty($param['leave_type'])) {
            //搜索新病假 要把原来的 带薪病假 搜出来
            if(in_array(StaffAuditModel::LEAVE_TYPE_38,$param['leave_type'])){
                $param['leave_type'][] = StaffAuditModel::LEAVE_TYPE_3;
            }
            $audit_object->inWhere("s.leave_type", $param['leave_type']);
        }

        //新增病假类型 合并了 带薪和不带薪 搜索列表 只展示非子类型的
        $audit_object->andWhere("s.parent_id = :parent_id:", ['parent_id' => 0]);

        //自增ID
        if (!empty($param['max_id'])) {
            $audit_object->andWhere("s.audit_id < :max_id:",  ['max_id' => $param['max_id']]);
        }


        if (!$export) {
            $audit_object->columns("count(1) as total");
            //查总数
            $totalInfo = $audit_object->getQuery()->getSingleResult();
            $count     = intval($totalInfo->total);
        }

        //排序
        $audit_object->orderBy('s.audit_id desc');

        $audit_object->columns(
            "i.staff_info_id,
            i.name as staff_name,
            i.state,
            i.job_title,
            i.sys_store_id,
            i.hire_date,
            i.job_title_level,
            i.hire_type,
            i.job_title_level,
            i.node_department_id,
            i.wait_leave_state,
            s.leave_type,
            s.approver_id,
            s.approver_name,
            s.leave_start_time,
            s.leave_end_time,
            s.leave_end_type,
            s.leave_day,
            s.status,
            s.audit_reason,
            s.leave_start_type,
            s.template_comment,
            store.manage_region as manage_region,
            store.manage_piece as manage_piece,
            DATE_FORMAT( CONVERT_TZ( s.created_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at,
            DATE_FORMAT( CONVERT_TZ( s.updated_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as updated_at,
            s.approver_id,
            s.audit_id as id,
            s.reject_reason,
            s.approver_name,
            s.paid_leave_reason
            ");
        //分页
        $offset = ($param['page'] -1) * $param['size'];
        $limit = $param['size'];
        $audit_object->offset($offset);
        $audit_object->limit($limit);

        $sql = $audit_object->getQuery()->getsql();//var_dump($sql);exit;

        $this->logger->info('getLeaveList:员工请假列表-sql' .json_encode($sql));

        $audit_result = $audit_object->getQuery()->execute();

        $list = $audit_result->toArray();//var_dump($list);exit;

        if (!empty($list)) {
            //获取网点信息 拼接 网点名称
            //年假新增 周期字段 等所有国家都改版 就去掉 还差马来没有
            if(!isCountry('my')){
                $detailData = $this->formatAnnual($list);
            }

            //获取请假说明
            if (isCountry("TH")) {
                $service = new AttendanceToolService();
                $paidLeaveReasonList = $service->getPaidLeaveReason();
                $paidLeaveReasonList = array_column($paidLeaveReasonList, 'label', 'value');
            } else {
                $paidLeaveReasonList = [];
            }

            //拼接员工姓名 和在职状态
            foreach ($list as &$li) {
                //计算类型假期 总额度

                $li['store_id']        = $li['sys_store_id'];
                $li['department_name'] = $this->showDepartmentName($li['node_department_id']);
                $li['job_name'] = $this->showJobTitleName($li['job_title']);
                $li['store_name'] = $this->showStoreName($li['sys_store_id']);
                $li['regional'] = $this->showRegionNameByStoreId($li['sys_store_id']); //大区
                $li['area'] = $this->showPieceNameByStoreId($li['sys_store_id']);      //片区
                //拼接 开始 结束时间 上下午
                $li['leave_start_time'] = date('Y-m-d', strtotime($li['leave_start_time']));
                $li['leave_start_time'] .= ($li['leave_start_type'] == 1) ? ' am' : ' pm';
                $li['leave_end_time']   = date('Y-m-d', strtotime($li['leave_end_time']));
                $li['leave_end_time']   .= ($li['leave_end_type'] == 1) ? ' am' : ' pm';
                //员工状态
                $li['state_text'] = $li['state'];
                //审核状态
                $li['status_text'] = $li['status'];
                //实际发生时间
                $li['apply_time'] = $li['leave_start_time'] . ' ~ ' . $li['leave_end_time'];

                //新增 周期字段 非马来 都有
                if (!isCountry('my')) {
                    $li['cycle_text'] = $detailData[$li['id']] ?? '';
                }
                $li['hire_type_text'] = self::$t->_('hire_type_' . $li['hire_type']);

                //请假说明
                $li['paid_leave_reason_txt'] = $paidLeaveReasonList[$li['paid_leave_reason']] ?? "";
            }
        }
        $data['list'] = $list;
        $data['count'] = $count??0;
        $list = null;
        return $data;

    }

    //请假列表 整理 周期字段  2022.01.01-2022.01.02(1) 日期区间 和天数
    protected function formatAnnual($data){
        $annualIds = [];
        foreach ($data as $da){
            if($da['leave_type'] == StaffAuditModel::LEAVE_TYPE_1){
                $annualIds[] = $da['id'];
            }
        }

        if(empty($annualIds)){
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sp.audit_id,sum(if(sp.type=0,1,0.5)) as num,sp.year_at,s.hire_date');
        $builder->from(['sp' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'sp.staff_info_id = s.staff_info_id', 's');
        $builder->inWhere('sp.audit_id', $annualIds);
        $builder->groupBy('sp.audit_id,sp.year_at,s.hire_date');
        $builder->orderBy('sp.id');
        $splitData = $builder->getQuery()->execute()->toArray();

        if(empty($splitData)){
            return [];
        }

        $res = [];
        $server = reBuildCountryInstance($this);
        foreach ($splitData as $datum){
            //第一条记录
            if(empty($res[$datum['audit_id']])){
                $res[$datum['audit_id']] = $server->formatBetween($datum['hire_date'],$datum['year_at'])."({$datum['num']})";
            }else{//不是 第一条 需要换行
                $partition = '<br />';
                if($this->export === true){
                    $partition = '|';//不知道咋换行 先凑合看
                }
                $res[$datum['audit_id']] .= $partition.$server->formatBetween($datum['hire_date'],$datum['year_at'])."({$datum['num']})";
            }
        }
        return $res;
    }

    //列表页 新增周期字段
    public function formatBetween($hire_date,$cycle){
        //旧规则 是按自然年 固定的
        if($cycle > 2000){
            $start = "{$cycle}.01.01";
            $end = "{$cycle}.12.31";
            return "{$start}-{$end}";
        }

        //其他 按周期算 开始结束区间
        $startCycle = $cycle - 1;
        if($startCycle > 0){
            $start = date('Y.m.d',strtotime("{$hire_date} +{$startCycle} year"));
        }else{
            $start = date('Y.m.d',strtotime("{$hire_date}"));
        }

        $end = date("Y.m.d",strtotime("{$hire_date} +{$cycle} year -1 day"));

        return "{$start}-{$end}";

    }


    //获取by 员工请假详情
    public function getLeaveDetail ($params)
    {

        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->columns(
            "i.staff_info_id,
            i.name as staff_name,
            i.state,
            i.job_title,
            i.sys_store_id,
            i.hire_date,
            i.hire_date,
            s.leave_type,
            s.approver_id,
            s.approver_name,
            s.leave_start_time,
            s.leave_end_time,
            s.leave_end_type,
            s.leave_day,
            s.status,
            s.audit_reason,
            s.created_at,
            s.leave_start_type,
            s.approver_id,
            dep.name as department_name,
            job.job_name,
            s.paid_leave_reason
            ");
        $audit_object->from(['s' => StaffAuditModel::class]);
        $audit_object->leftJoin(HrStaffInfoReadModel::class, 's.staff_info_id = i.staff_info_id', 'i');
        $audit_object->leftJoin(\App\Models\backyard\SysDepartmentModel::class, 'i.node_department_id = dep.id', 'dep');
        $audit_object->leftJoin(\App\Models\backyard\HrJobTitleModel::class, 'i.job_title = job.id', 'job');
        $audit_object->leftJoin(\App\Models\backyard\SysStoreModel::class, 'i.sys_store_id = store.id', 'store');
        //指定记录
        $audit_object->andWhere("s.audit_id = :id:", ['id' => $params['id']]);
        //申请类型
        $audit_object->andWhere("s.audit_type = :audit_type:", ['audit_type' => 2]);

        //排序
        $audit_object->groupBy('s.audit_id');
        $audit_object->orderBy('s.created_at');

        $sql = $audit_object->getQuery()->getsql();
        $this->logger->info('getLeaveDetail:员工请假详情-sql' .json_encode($sql));

        $audit_result = $audit_object->getQuery()->execute();
        $result = $audit_result->toArray();
        //获取 每年 各种类型 年假 应该有多少天
        $leave_days = StaffDaysForLeaveModel::find()->toArray();
        $should_days = array();
        foreach ($leave_days as $days) {
            if ($days['leave_type'] == 1)
                continue;
            $should_days[$days['leave_type']] = $days['days'];
        }
        //新逻辑 由job title 更改为 职位等级 判断该员工应该由几天年假
        $year_days = StaffHelper::getDaysByGrade();
        $max_days = StaffHelper::getMaxDaysByGrade();

        $result = (!empty($result) && isset($result[0])) ? $result[0] : [];
        if(!$result){
            throw new Exception(self::$t->_('1001'),-1);
        }
        $result['image_path'] = [];
        if (!empty($result)) {
            $staff_id = $result['staff_info_id'];
            //获取网点信息 拼接 网点名称
            $store_bll = new SysStoreService();
            $store_list = $store_bll->getSysStoreList();
            $store_list = array_column($store_list , 'name' , 'id');
            $store_list['-1'] = GlobalEnums::HEAD_OFFICE;
;
            //获取当天员工固化的职等额度
            $freeze_days = StaffDaysFreezeModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = 1" ,
                'bind' => ['staff_info_id' => $staff_id] ,
                'columns' => 'staff_info_id, days',
            ]);
            if($freeze_days){
                $freeze_days = $freeze_days->toArray();
                $freeze_days =  $freeze_days ?: array_column($freeze_days , 'days' , 'staff_info_id');
            }

            //获取请假说明
            if (isCountry("TH")) {
                $service = new AttendanceToolService();
                $paidLeaveReasonList = $service->getPaidLeaveReason();
                $paidLeaveReasonList = array_column($paidLeaveReasonList, 'label', 'value');
            } else {
                $paidLeaveReasonList = [];
            }
            //请假说明
            $result['paid_leave_reason'] = $result['paid_leave_reason'] ?? 0;
            $result['paid_leave_reason_txt'] = $paidLeaveReasonList[$result['paid_leave_reason']] ?? "";

            //图片附件
            $attachment = StaffAuditImageModel::find([
                'conditions' => "audit_id = :audit_id:",
                'bind' => [
                    'audit_id'  => $params['id'],
                ],
            ])->toArray();
            $result['image_path'] = [];
            if(!empty($attachment)){
                //病假图片结构
                if($result['leave_type'] == StaffAuditModel::LEAVE_TYPE_38){
                    $categoryEnum = SickLeaveCertificateDetailModel::$sickImgCategory;
                    foreach ($attachment as $img){
                        $result[$categoryEnum[$img['business_category']]][] = $img['image_path'];
                    }
                }else{
                    //其他假期图片结构不变
                    $result['image_path'] = array_column($attachment,'image_path');
                }
            }

            //拼接员工姓名 和在职状态
            //计算类型假期 总额度

            if ($result['leave_type'] == 1) {
                $job_level = empty($result['job_title_level']) ? 0 : $result['job_title_level'];
                $level_days = empty($year_days[$job_level]) ? 7 : $year_days[$job_level];
                $freeze = empty($freeze_days[$result['staff_info_id']]) ? 0 : $freeze_days[$result['staff_info_id']];
//                        if(date('Y') == $year)//如果查询日期 是当年 用每天固化的数据 额度 去计算
                $have_days = StaffHelper::getHaveDays($level_days , $freeze);
                //如果 满365天入职 则第366天那一年 +1天 而后每自然年加一天
                $entry = strtotime($result['hire_date']);
                $work_days = floor((time() - $entry) / (24 * 3600));//工作天数 没满365 时候用
                if (time() > ($entry + 365 * 24 * 3600)) {
                    $first_year = date('Y' , $entry + 365 * 24 * 3600);//转正当年 +1额度
                    $add_days = intval(date('Y' , time())) - intval($first_year);//而后每自然年 +1天
                    $have_days = $have_days + 1 + $add_days;
                    $max_day = $max_days[$job_level] ?? '';
                    if (empty($max_day))
                        $max_day = 15;
                    if ($have_days > $max_day)
                        $have_days = $max_day;
                } else {//未满365天
                    //未过试用期 额度是0
                    if (time() <= ($entry + 119 * 24 * 3600)) {
                        $have_days = 0;
                    } else {
                        //判断是否大于n.5
                        $have_days = round($have_days / 365 * $work_days , 2);//2.6
                        $int_day = floor($have_days);//2
                        $have_days = $have_days > ($int_day + 0.5) ? ($int_day + 0.5) : $int_day;//2.5
                        //$have_days 为365天总数 如果跨年 当前应有年假 = 公式结果 - 去年应有
                        $last_in_all = empty($last_all_left[$result['staff_info_id']]) ? 0 : $last_all_left[$result['staff_info_id']];
                        $have_days = $have_days - $last_in_all;
                    }
                }
                if ($have_days < 0)
                    $have_days = 0;
                $result['should_day'] = $have_days;
                //已经申请的假期
                $result['applied'] = empty($sum[$result['staff_info_id']][$result['leave_type']]) ? 0 : $sum[$result['staff_info_id']][$result['leave_type']];
                //计算 剩余天数
                $result['left_day'] = $result['should_day'] - $result['applied'];

                //如果当前时间在有效期内  计算去年剩余额度 加到一起
                $result['last_year_left'] = empty($last_left[$result['staff_info_id']]) ? 0 : $last_left[$result['staff_info_id']];
                $result['last_year_got'] = empty($last_all_left[$result['staff_info_id']]) ? 0 : $last_all_left[$result['staff_info_id']];
                $result['last_year_should'] = empty($last_should_left[$result['staff_info_id']]) ? 0 : $last_should_left[$result['staff_info_id']];
            } else {
                $result['should_day'] = empty($should_days[$result['leave_type']]) ? '-' : $should_days[$result['leave_type']];
                //已经申请的假期
                $result['applied'] = empty($sum[$result['staff_info_id']][$result['leave_type']]) ? 0 : $sum[$result['staff_info_id']][$result['leave_type']];
                $result['left_day'] = '-';
                if ($result['should_day'] != '-')
                    $result['left_day'] = $result['should_day'] - $result['applied'];
            }

            if (is_numeric($result['left_day']) && $result['left_day'] < 0)
                $result['left_day'] = 0;
            //拼接 网点名称
            $result['store_name'] = '';
            if (!empty($result['sys_store_id']))
                $result['store_name'] = $store_list[$result['sys_store_id']];
            //创建时间 零时区转泰国时间
            $result['created_at'] = date('Y-m-d H:i' , strtotime($result['created_at']) + ($this->timeOffset) * 3600);
            //拼接 开始 结束时间 上下午
            $result['leave_start_time'] = date('Y-m-d' , strtotime($result['leave_start_time']));
            $result['leave_start_time'] .= ($result['leave_start_type'] == 1) ? ' am' : ' pm';
            $result['leave_end_time'] = date('Y-m-d' , strtotime($result['leave_end_time']));
            $result['leave_end_time'] .= ($result['leave_end_type'] == 1) ? ' am' : ' pm';
            //员工状态
            $result['state_text'] = $result['state'];
            //审核状态
            $result['status_text'] = $result['status'];
        }
        return $result;
    }

    //获取by 员工ot列表
    public function getOtList ($param , $export = false)
    {
        $timeZone     = env('timeZone', '+07:00');
        $staff_id     = empty($param['staff_info_id']) ? '' : $param['staff_info_id'];
        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->columns("count(1) as total");
        $audit_object->from(['ho' => HrOvertimeModel::class]);
        $audit_object->innerJoin(HrStaffInfoReadModel::class, 'ho.staff_id = i.staff_info_id', 'i');
        $audit_object->leftJoin(SysStoreModel::class, 'i.sys_store_id = store.id', 'store');
        //通用数据权限限定条件

        $condition = (new AttendanceStatisticsService())->buildStaffPurviewData($param['current_uid'], 'i');
        if ($condition['condition'] && $condition['bind']) {
            $audit_object->andWhere($condition['condition'], $condition['bind']);
        }

        if (!empty($param['department_ids'])) {
            $audit_object->inWhere("i.node_department_id ", $param['department_ids']);
        }
        //过滤网点
        if (!empty($param['store_id'])) {
            $audit_object->inWhere("i.sys_store_id ", $param['store_id']);
        }

        //过滤职位
        if (!empty($param['job_title'])) {
            $audit_object->inWhere("i.job_title", $param['job_title']);
        }
        //过滤用户
        if (!empty($staff_id)) {
            if (is_numeric($staff_id)) {
                $audit_object->andWhere("i.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_id]);
            } else {
                $audit_object->andWhere("i.name like :name:", ['name' => "%{$staff_id}%"]);
            }
        }

        //OT类型
        if (!empty($param['ot_type'])) {
            $audit_object->inWhere("ho.type", $param['ot_type']);
        }

        //审批状态
        if (!empty($param['audit_status'])) {
            $audit_object->inWhere("ho.state", $param['audit_status']);
        }

        if (!empty($param['hire_type'])) {
            $audit_object->inWhere("i.hire_type", $param['hire_type']);
        }

        //申请日期

        if (!empty($param['submit_start_time']) && !empty($param['submit_end_time'])) {
            $_submit_start_time = gmdate('Y-m-d H:i:s',
                $param['submit_start_time'] - $this->config->application->add_hour * 3600);
            $_submit_end_time   = gmdate('Y-m-d H:i:s',
                $param['submit_end_time'] + 86399 - $this->config->application->add_hour * 3600);
            $audit_object->betweenWhere("ho.created_at", $_submit_start_time, $_submit_end_time);
        }


        //实际日期
        if (!empty($param['apply_start_time'])) {
            $audit_object->andWhere("ho.date_at >= :apply_start_time: ",
                ['apply_start_time' => gmdate('Y-m-d', $param['apply_start_time'])]);
        }
        if (!empty($param['apply_end_time'])) {
            $audit_object->andWhere("ho.date_at <= :apply_end_time:",
                ['apply_end_time' => gmdate('Y-m-d', $param['apply_end_time'] + 86399)]);
        }

        //自增ID
        if (!empty($param['max_id'])) {
            $audit_object->andWhere("ho.overtime_id < :max_id:", ['max_id' => $param['max_id']]);
        }

        //在职状态
        if(!empty($param['staff_state'])){
            $tmp_sql = [];
            if(in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION,$param['staff_state'])){
                $tmp_sql[] = '( i.state = 1 and i.wait_leave_state  = 1 )';
            }
            if(in_array(HrStaffInfoModel::STATE_ON_JOB,$param['staff_state'])){
                $tmp_sql[] = '( i.state = '.HrStaffInfoModel::STATE_ON_JOB .' and i.wait_leave_state  = 0) ';
            }
            if(in_array(HrStaffInfoModel::STATE_RESIGN,$param['staff_state'])){
                $tmp_sql[] = 'i.state = '.HrStaffInfoModel::STATE_RESIGN;
            }
            if(in_array(HrStaffInfoModel::STATE_SUSPEND,$param['staff_state'])){
                $tmp_sql[] = 'i.state = '.HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ',$tmp_sql);
            $audit_object->andWhere($_sql);
        }

        //查总数
        $totalInfo = $audit_object->getQuery()->getSingleResult();

        $count = intval($totalInfo->total);

        if ($export) {
            if ($count > 300000) {
                throw new BusinessException(self::$t->_('file_download_limit', ['num' => 300000]));
            }
            if (isset($param['from_api'])) {
                return true;
            }
        }

        $audit_object->columns(
            "
                i.name as staff_name,
                i.state as staff_state,
                i.job_title,
                i.sys_store_id,
                i.node_department_id,
                i.staff_info_id,
                i.hire_type,
                ho.overtime_id,
                ho.overtime_id as id,
                ho.staff_id,
                ho.type,
                ho.start_time,
                ho.end_time,
                ho.state,
                ho.salary_state,
                ho.duration,
                ho.date_at,
                ho.reason,
                store.manage_region as manage_region,
                store.manage_piece as manage_piece,
                DATE_FORMAT( CONVERT_TZ( ho.created_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at,
                DATE_FORMAT( CONVERT_TZ( ho.updated_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as updated_at,
                ho.approver_id,
                ho.approver_name,
                ho.reject_reason"
        );

        if ($export) {//导出 不分页
            $data = $audit_object->getQuery()->execute();
            $list = $data->toArray();
        } else {
            $audit_object->orderBy('ho.overtime_id DESC');
            $offset = ($param['page'] - 1) * $param['size'];
            $limit  = $param['size'];
            $audit_object->offset($offset);
            $audit_object->limit($limit);
            $data = $audit_object->getQuery()->execute();
            $list = $data->toArray();
        }

        //拼接返回数据
        if (!empty($list)) {
            $otServer = new OvertimeBllService();
            //拼接员工姓名 和在职状态
            foreach ($list as &$li) {
                //ot是有有效
                [$li['salary_state_text'], $li['salary_state_reason']] = $otServer->formatSalaryState($li);

                $li['start_time']      = date('Y-m-d H:i:s', strtotime($li['start_time']));
                $li['end_time']        = date('Y-m-d H:i:s', strtotime($li['end_time']));
                $li['store_name']      = $this->showStoreName($li['sys_store_id']);
                $li['job_name']        = $this->showJobTitleName($li['job_title']);
                $li['department_name'] = $this->showDepartmentName($li['node_department_id']);
                $li['hire_type_text']  = self::$t->_('hire_type_' . $li['hire_type']);
            }
        }
        $total['list']  = $list ?: '';
        $total['count'] = $count;
        return $total;


    }

    //获取by 员工请假列表 ot
    public function getOtDetail ($params)
    {
        try {

            $audit_object = $this->modelsManager->createBuilder();
            $audit_object->columns(
                "
            i.name as staff_name,
            i.state as staff_state,
            i.job_title,
            i.sys_store_id,
            i.sys_department_id,
            i.staff_info_id,
            job.job_name,
            dep.name as department_name,
            store.name as store_name,
            job.job_name,
            dep.name department_name,
            ho.overtime_id,
            ho.staff_id,
            ho.type,
            ho.start_time,
            ho.end_time,
            ho.state,
            ho.duration,
            ho.date_at,
            ho.reason,
            ho.created_at
            "
            );
            $audit_object->from(['ho' => HrOvertimeModel::class]);
            $audit_object->leftJoin(HrStaffInfoReadModel::class, 'ho.staff_id = i.staff_info_id', 'i');
            $audit_object->leftJoin(\App\Models\backyard\SysDepartmentModel::class, 'i.node_department_id = dep.id', 'dep');
            $audit_object->leftJoin(\App\Models\backyard\HrJobTitleModel::class, 'i.job_title = job.id', 'job');
            $audit_object->leftJoin(\App\Models\backyard\SysStoreModel::class, 'i.sys_store_id = store.id', 'store');
            $audit_object->andWhere("ho.overtime_id = :id:" , ['id' => $params['id']]);
            $audit_object->groupBy('ho.overtime_id');
            $data = $audit_object->getQuery()->execute();
            $result = $data->toArray();
            $result = (!empty($result) && isset($result[0])) ? $result[0] : [];


            //拼接返回数据
            if (!empty($result)) {
                //获取网点信息 拼接 网点名称
                $store_bll = new SysStoreService();
                $store_list = $store_bll->getSysStoreList();
                $store_list = array_column($store_list , 'name' , 'id');
                $store_list['-1'] = GlobalEnums::HEAD_OFFICE;
                //拼接员工姓名 和在职状态
                $result['start_time'] = date('Y-m-d H:i:s' , strtotime($result['start_time']));
                $result['end_time'] = date('Y-m-d H:i:s' , strtotime($result['end_time']));
                $result['store_name'] = empty($result['sys_store_id']) ? '' : $store_list[$result['sys_store_id']];
                $result['apply_time'] = $result['start_time'].'~'.$result['end_time'];

                //图片附件
//                $attachment = StaffAuditImageModel::find([
//                    'conditions' => "audit_id = :audit_id:",
//                    'bind' => [
//                        'audit_id'  => $params['id'],
//                    ]
//                ])->toArray();
                $result['image_path'] = [];
//                if($attachment){
//                    $result['image_path'] = array_column($attachment,'image_path');
//                }
            }

            return $result;
        } catch (Exception $e) {
            $this->getDi()->get('logger')->info(['type' => 'error' , 'msg' => $e->getMessage()]);
            throw new BusinessException($e->getMessage());
        }

    }

    /**
     * 出差列表数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getTripListData($params): array
    {
        $search_result['list']  = [];
        $search_result['count'] = $this->getTripCount($params);
        if (empty($search_result['count'])) {
            return $search_result;
        }
        $list                  = $this->dealShowItems($this->getTripList($params));
        $search_result['list'] = $list;
        return $search_result;
    }


    /**
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getTripExportData($params): array
    {
        $t = BaseService::getTranslation($params['lang']);
        //是否是 外出 外出的导出 字段 跟出差不一样
        $is_go_out = false;
        $auditType = ApprovalEnums::APPROVAL_TYPE_BT;
        if ($params['type'] == HcmExcelTackModel::TYPE_GOOUT_EXPORT) {
            $is_go_out = true;
            $auditType = ApprovalEnums::APPROVAL_TYPE_GO;
        }

        //工作所在国家
        $addressCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('address_country_region');
        $addressCountryList = array_column($addressCountryList, 'label', 'value');
        //获取出差 工具类型
        $type_book          = $this->getTypeBookOtList('trip_traffic_type');
        $approveService     = new ApproveService();
        $jobTitleService    = new HrJobTitleService();
        $staff_list_service = new StaffPayrollYearListService();

        $export         = [];
        $params['page'] = 1;
        $params['size'] = 500;
        while (true) {
            $return = $this->getTripList($params);
            if (empty($return)) {
                break;
            }
            $params['page']++;

            //获取附件
            $business_trip_ids      = array_column($return, 'id');
            $business_trip_img_arr  = (new StaffInfoAuditService())->businessTripImg($business_trip_ids);
            $business_trip_img_list = array_column($business_trip_img_arr, null, 'business_trip_id');

            //驳回人信息
            $approver_id_arr = $audit_id_arr = [];
            foreach ($return as $value) {
                if ($value['status'] == StaffEnums::STAFF_AUDIT_REJECT_STATUS) {
                    $approver_id_arr[] = $value['approve_user'];
                }
                //审批超时的 业务 id
                if ($value['status'] == ApprovalEnums::APPROVAL_STATUS_TIMEOUT) {
                    $audit_id_arr[] = $value['id'];
                }
            }

            //获取审批人超时节点信息
            $auditTimeOutNode = $approveService->getAuditNodeInfo($audit_id_arr, $auditType,
                ApprovalEnums::APPROVAL_STATUS_TIMEOUT);

            $approver_job_title = [];
            if (!empty($approver_id_arr)) {
                $approver_job_title = $jobTitleService->getJobTitleListByStaffInfo($approver_id_arr);
            }

            $staff_list     = $staff_list_service->getStaffList($approver_id_arr, 'name,staff_info_id');
            $staff_list_arr = !empty($staff_list) ? array_column($staff_list, null, 'staff_info_id') : [];

            //拼接文案
            foreach ($return as &$li) {
                //交通工具 文案
                $li['tools_text']  = $type_book[$li['traffic_tools']] ?? '';
                $li['status_text'] = $t['by_state_' . $li['status']];
                //单程往返 文案 单程1往返2
                $li['return_text'] = $li['oneway_or_roundtrip'] == 1 ? $t['trip_single'] : $t['trip_backturn'];
                $row['apply_user']      = $li['staff_info_id'];
                $row['staff_name']      = $li['staff_name'];
                $row['serial_no']      = $li['serial_no'];
                $row['state_text']      = $t['hris_working_state_' . $li['state']];
                $row['job_name']        = $this->showJobTitleName($li['job_title']);
                $row['department_name'] = $this->showDepartmentName($li['node_department_id']);
                $row['store_name']      = $this->showStoreName($li['sys_store_id']);
                $row['hire_type']       = $t['hire_type_' . $li['hire_type']];
                $row['regional']        = $this->showRegionNameByStoreId($li['sys_store_id']); //大区
                $row['area']            = $this->showPieceNameByStoreId($li['sys_store_id']);      //片区
                if ($is_go_out === false) {
                    $row['business_trip_type'] = $t['business_trip_type_' . $li['business_trip_type']];//出差类型
                }
                $row['reason_application'] = $li['reason_application'];//出差原因 或者 外出事由

                if ($is_go_out === false) {
                    $row['return_text']              = $li['return_text'];
                    $row['departure_city']           = $li['departure_city'];
                    $row['destination_country']      = $addressCountryList[$li['destination_country']] ?? '';//目的地国家
                    $row['destination_country_name'] = $li['destination_country_name'];                      //目的地国家名称
                }
                $row['destination_city'] = $li['destination_city'];
                $row['start_time']       = $li['start_time'];
                $row['end_time']         = $li['end_time'];
                $row['days_num']         = $li['days_num'];
                if ($is_go_out === false) {
                    $row['remarks'] = $li['remark'];
                }
                $row['image_path']         = isset($business_trip_img_list[$li['id']]) ? $business_trip_img_list[$li['id']]['image_path'] : '';//图片附件
                $row['status_text']        = $li['status_text'];                                                                               //审批状态
                $row['by_apply_time']      = $li['created_at'];                                                                                //申请时间
                $row['by_apply_over_time'] = $li['status'] == 1 ? '' : $li['updated_at'];                                                      //审批完成时间

                $row['reject_reason']      = '';
                $row['approver_id']        = '';
                $row['approver_name']      = '';
                $row['approver_job_title'] = '';
                if ($li['status'] == StaffEnums::STAFF_AUDIT_REJECT_STATUS) {
                    $row['reject_reason']      = $li['reason'] ?? '';
                    $row['approver_id']        = $li['approve_user'] ?? '';
                    $row['approver_name']      = $staff_list_arr[$li['approve_user']]['name'] ?? '';
                    $row['approver_job_title'] = $approver_job_title[$li['approve_user']]['job_name'] ?? '';
                }

                //超时节点信息
                $auditInfo = $auditTimeOutNode[$li['id']] ?? [];

                $row['audit_timeout_staff_ids']   = $auditInfo['staff_info_ids'] ?? '';//超时审批人姓名
                $row['audit_timeout_staff_names'] = $auditInfo['staff_name'] ?? '';    //超时审批人姓名
                $row['audit_timeout_staff_jobs']  = $auditInfo['job_title'] ?? '';     //超时审批人职位
                $export[]                         = $row;
            }
        }


        $header['tool_employee']   = $t['tool_employee'];//工号
        $header['tool_name']       = $t['tool_name'];    //姓名
        $header['order_number']    = $t['order_number']; //审批单号
        $header['staff_state']     = $t['staff_state'];  //在职状态
        $header['job_name']        = $t['job_name'];     //职位
        $header['department_name'] = $t['department'];   //部门
        $header['dot']             = $t['dot'];          //网点
        $header['hire_type']       = $t['hire_type'];    //雇佣类型
        $header['regional']        = $t['regional'];     //$t['staff_state']  ;//大区
        $header['area']            = $t['area'];         //片区
        if ($is_go_out === false) {
            $header['business_trip_type'] = $t['business_trip_type'];//出差类型
        }
        $header['reason_application'] = $is_go_out ? $t['go_out_reason_application'] : $t['trip_reason'];

        if ($is_go_out === false) {
            $header['tools_text']               = $t['trip_return'];
            $header['departure_city']           = $t['trip_leave_address'];
            $header['destination_country']      = $t['destination_country'];     //目的地国家
            $header['destination_country_name'] = $t['destination_country_name'];//目的地国家名称
        }

        $header['destination_city'] = $is_go_out ? $t['go_out_location'] : $t['trip_arrive_address'];
        $header['start_time']       = $t['start_time'];
        $header['end_time']         = $t['end_time'];
        $header['days_num']         = $t['by_days'];

        if ($is_go_out === false) {
            $header['remarks'] = $t['remarks'];
        }
        $header['image']                                = $t['attachment_img'];//图片附件
        $header['status_text']                          = $t['by_leave_status'];
        $header['by_apply_time']                        = $t['by_apply_time'];                       //申请时间
        $header['by_apply_over_time']                   = $t['by_apply_over_time'];                  //审批完成时间
        $header['hcm_translate_key_reject_reason']      = $t['hcm_translate_key_reject_reason'];     //驳回原因
        $header['hcm_translate_key_approver_id']        = $t['hcm_translate_key_approver_id'];       //驳回人id
        $header['hcm_translate_key_approver_name']      = $t['hcm_translate_key_approver_name'];     //驳回人
        $header['hcm_translate_key_approver_job_title'] = $t['hcm_translate_key_approver_job_title'];//职位记录
        $header['audit_timeout_staff_ids']              = $t['audit_timeout_staff_ids'];             //超时审批人工号
        $header['audit_timeout_staff_names']            = $t['audit_timeout_staff_names'];           //超时审批人姓名
        $header['audit_timeout_staff_jobs']             = $t['audit_timeout_staff_jobs'];            //超时审批人职位

        return [$header, $export];
    }


    /**
     * 外出列表数据
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getOutOfficeData($params): array
    {
        $params['is_go_out']    = 1;
        $search_result['list']  = [];
        $search_result['count'] = $this->getTripCount($params);
        if (empty($search_result['count'])) {
            return $search_result;
        }
        $search_result['list']  = $this->dealShowItems($this->getTripList($params));
        return $search_result;
    }

    protected function getTripCount($params): int
    {
        $builder = $this->getTripBuilder($params);
        $builder->columns(
            "count(1) as total"
        );
        //查总数
        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);
    }

    protected function getTripList($params)
    {
        $builder = $this->getTripBuilder($params);
        $timeZone = env('timeZone','+07:00');
        $builder->columns(
            "
            i.name as staff_name,
            i.state,
            i.job_title,
            i.sys_store_id,
            i.node_department_id,
            i.staff_info_id,
            i.hire_type,
            s.reason_application,
            oneway_or_roundtrip,
            s.departure_city,
            s.destination_city,
            s.days_num,
            s.start_time,
            s.end_time,
            s.remark,
            s.status,
            DATE_FORMAT( CONVERT_TZ( s.created_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at,
            DATE_FORMAT( CONVERT_TZ( s.updated_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as updated_at,
            s.id,
            s.traffic_tools,
            s.reason,
            s.approve_user,
            s.business_trip_type,
            s.destination_country,
            s.destination_country_name,
            s.serial_no
            "
        );
        $builder->orderBy('s.status ASC,s.created_at DESC');
        $builder->offset(($params['page'] - 1) * $params['size']);
        $builder->limit($params['size']);
        return $builder->getQuery()->execute()->toArray();
    }

    protected function getTripBuilder($params)
    {
        $store_id = empty($params['store_id']) ? '' : $params['store_id'];
        $staff_id = $params['staff_info_id'];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => BusinessTripModel::class]);
        $builder->leftJoin(HrStaffInfoReadModel::class, 's.apply_user = i.staff_info_id', 'i');
        //通用数据权限限定条件
        $condition = (new AttendanceStatisticsService())->buildStaffPurviewData($params['current_uid'],'i');
        if($condition['condition'] && $condition['bind']){
            $builder->andWhere($condition['condition'] , $condition['bind'] );
        }
        // 是否外出 外出不给钱 其他的出差类型都给钱 入口是2个 用这个字段区分
        if(!empty($params['is_go_out']) && $params['is_go_out'] == 1){
            $builder->andWhere("s.business_trip_type = :type:", ['type' => BusinessTripModel::BTY_GO_OUT]);
        }else{
            $builder->andWhere("s.business_trip_type != :type:", ['type' => BusinessTripModel::BTY_GO_OUT]);
        }

        //自增ID
        if (!empty($params['max_id'])) {
            $builder->andWhere("s.id < :max_id:",  ['max_id' => $params['max_id']]);
        }
        //过滤用户
        if (!empty($staff_id)) {
            if (is_numeric($staff_id)) {
                $builder->andWhere("i.staff_info_id = :s_id:" , ['s_id' => $staff_id]);
            } else {
                $builder->andWhere("i.name like :staff_id:" , ['staff_id' => "%{$staff_id}%"]);
            }
        }
        if (!empty($params['serial_no'])) {
            $builder->andWhere("s.serial_no like :serial_no:", ['serial_no' => "%{$params['serial_no']}%"]);
        }

        if (!empty($params['department_ids'])) {
            $builder->inWhere("i.node_department_id" , $params['department_ids']);
        }

        //过滤网点
        if (!empty($store_id)) {
            $builder->inWhere("i.sys_store_id" , $store_id);
        }

        //过滤职位
        if (!empty($params['job_title'])) {
            $builder->inWhere("i.job_title",$params['job_title']);
        }

        //过滤在职状态
        if (!empty($params['staff_state'])) {
            $builder->inWhere("i.state" , $params['staff_state']);
        }
        //过滤审批
        if (!empty($params['audit_status'])) {
            $builder->inWhere("s.status" , $params['audit_status']);
        }

        if (!empty($params['hire_type'])) {
            $builder->inWhere("i.hire_type", $params['hire_type']);
        }

        //申请时间
        if(!empty($params['submit_start_time']) && !empty($params['submit_end_time'])) {
            $_submit_start_time = gmdate('Y-m-d H:i:s', $params['submit_start_time'] - $this->config->application->add_hour*3600);
            $_submit_end_time = gmdate('Y-m-d H:i:s', $params['submit_end_time'] + 86399 - $this->config->application->add_hour*3600);
            $builder->betweenWhere("s.created_at", $_submit_start_time, $_submit_end_time);
        }
        //实际时间过滤
        if(!empty($params['apply_start_time']) && !empty($params['apply_end_time'])) {
            $builder->andWhere(" !(s.start_time > :start: or end_time < :end: )", ['end' => gmdate('Y-m-d H:i:s',$params['apply_start_time']), 'start' => gmdate('Y-m-d H:i:s',$params['apply_end_time']+86399)]);
        }
        return $builder;
    }




    /**
     * 出差-详情
     * @param $param
     * @param false $export
     * @return array
     */
    public function getTripLDetail($params)
    {
        $timeZone = env('timeZone','+07:00');
        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->columns(
            "
            i.name as staff_name,
            i.state,
            i.job_title,
            i.sys_store_id,
            i.sys_department_id,
            dep.name as department_name,
            i.staff_info_id,
            s.reason_application,
            oneway_or_roundtrip,
            s.departure_city,
            s.destination_city,
            s.days_num,
            s.start_time,
            s.end_time,
            s.remark,
            s.traffic_tools,
            s.other_traffic_name,
            s.id,
            s.status,
            s.business_trip_type,
            s.created_at,
            s.destination_country,
            s.destination_country_name,
            s.reason_application_type,
            s.serial_no
            "
        );
        $audit_object->from(['s' => BusinessTripModel::class]);
        $audit_object->leftJoin(HrStaffInfoReadModel::class, 's.apply_user = i.staff_info_id', 'i');
        $audit_object->leftJoin(\App\Models\backyard\SysDepartmentModel::class, 'i.node_department_id = dep.id', 'dep');
        $audit_object->andWhere("s.id = :id:" , ['id' => $params['id']]);

        $data_t = $audit_object->getQuery()->execute();
        $result = $data_t->toArray();

        $result = (!empty($result) && isset($result[0])) ? $result[0] : [];


        if (empty($result)) {
            return $result;
        }
        $hrJobTitle = HrJobTitleModel::findFirst([
            'conditions' => ' id =  :id: ',
            'bind'       => [
                'id' => $result['job_title'] ?? '',
            ],
            'columns'    => 'job_name',

        ]);
        if ($hrJobTitle) {
            $hrJobTitle         = $hrJobTitle->toArray();
            $result['job_name'] = $hrJobTitle['job_name'];
        }
        $storeObj = SysStoreModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $result['sys_store_id'] ?? ''],
            'columns'    => 'name',
        ]);
        if ($storeObj) {
            $storeObj             = $storeObj->toArray();
            $result['store_name'] = $storeObj['name'];
        }


        return $result;

    }



    //上面的方法 精简版
    public function get_new_list($params){
        $current_time = date('Y-m-d H:i:s');
        // 超级管理员、人事管理员、人事专员、人力资源、薪酬管理员、HRIS管理员、系统管理员、QAQC专员、安全监督员（所有员工）
        $admin_role = HrStaffInfoPositionModel::find([
            'conditions' => ' staff_info_id = :staff_id: and  position_category in ({positions:array}) ',
            'bind'       => [
                'staff_id'  => $params['userinfo_id'],
                'positions' => array_values(self::$admin_role),
            ],
        ])->toArray();
        if (empty($admin_role)) {//fbi 没有 再查 ms
            $admin_role = StaffInfoPositionModel::find([
                'conditions' => ' staff_info_id = :staff_id: and  position_category in ({positions:array}) ',
                'bind'       => [
                    'staff_id'  => $params['userinfo_id'],
                    'positions' => array_values(self::$admin_role),
                ],
            ])->toArray();
        }

        //列表查询 主表
        $builder = $this->modelsManager->createBuilder();

        $builder->from(['s' => HrStaffInfoModel::class]);


        //动态返回 该国家 的字段头
        $solid_column = $this->get_leave_column();
        $trends_column = $this->get_trends_columnFromCache(self::$language);

        //新增 假期类型的icon
        $server = reBuildCountryInstance($this);
        $iconData = $server->formatIconText();
        $iconData = empty($iconData) ? [] : array_column($iconData,'msg','code');

        $column_data   = $solid_column + $trends_column;//不能用merge 索引会转整形
        $format_column = [];
        foreach ($column_data as $k => $datum) {
            $row = [];
            $row['prop']     = $k;
            $row['label']    = $datum;
            $row['icon_text'] = $iconData[$k] ?: '';
            $format_column[] = $row;
        }
        $column_data = $format_column;

        $flag = false;
        //管理员等角色 可管理列表取 所有人 不需要调接口
        if (empty($admin_role)) {
            //登陆人 所管理员工列表 如果不是管理员等角色 调接口查询 可视范围
            $purview = (new StaffInfoService())->StaffDataPurviewV2($params['userinfo_id']);

            if (empty($purview)) {//不是 管理员角色 返回了空 可能是 hr 那边 超管 有变动 需要同步这边
                $flag       = true;
            }

            if (!empty($purview['stores'])) {
                $flag       = true;
                $areaStores = array_unique(array_filter($purview['stores']));
                if (in_array('-2', $areaStores)) {
                    $builder->orWhere("s.sys_store_id != '-1' ");
                } else {
                    $areaStores = "'".implode("','", $areaStores)."'";
                    $builder->orWhere("s.sys_store_id in ({$areaStores})");
                }
            }
            if (!empty($purview['departments'])) {
                $flag          = true;
                $departmentIds = array_unique(array_filter($purview['departments']));
                $dep_str       = "'".implode("','", $departmentIds)."'";
                $builder->orWhere("s.node_department_id in ({$dep_str})");
            }

            if (!empty($purview['flash_home_departments'])) {
                $flag          = true;
                $departmentIds = array_unique(array_filter($purview['flash_home_departments']));
                $home_dep      = "'".implode("','", $departmentIds)."'";
                $builder->orWhere("s.sys_store_id = -1 and  s.node_department_id in ({$home_dep})");
            }

            if (!empty($purview['staff_ids'])) {
                $flag      = true;
                $staffIds  = array_unique(array_filter($purview['staff_ids']));
                $staff_str = "'".implode("','", $staffIds)."'";
                $builder->orWhere("s.staff_info_id in ({$staff_str})");
            }

            if ($flag === false) {//既不是管理员 也没有 下属管理数据 返回空
                return [
                    'count'       => 0,
                    'rows'        => [],
                    'column_data' => $column_data,
                ];
            }
        }

        //工号搜索 单个
        if ($params['staff']) {
            $builder->andWhere("(s.staff_info_id = :staff: or s.name = :staff:)", ['staff' => $params['staff']]);
        }
        //在职状态
        if(!empty($params['staff_state'])){
            $tmp_sql = [];
            if($params['staff_state'] == HrStaffInfoModel::STATE_PENDING_RESIGNATION){
                $tmp_sql[] = '( s.state = 1 and s.wait_leave_state  = 1 )';
            }
            if($params['staff_state'] == HrStaffInfoModel::STATE_ON_JOB){
                $tmp_sql[] = '( s.state = '.HrStaffInfoModel::STATE_ON_JOB .' and s.wait_leave_state  = 0) ';
            }
            if($params['staff_state'] == HrStaffInfoModel::STATE_RESIGN){
                $tmp_sql[] = 's.state = '.HrStaffInfoModel::STATE_RESIGN;
            }
            if($params['staff_state'] == HrStaffInfoModel::STATE_SUSPEND){
                $tmp_sql[] = 's.state = '.HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ',$tmp_sql);
            $builder->andWhere($_sql);
        }
        //部门搜索
        if ($params['dep_id']) {
            $builder->andWhere(" (s.sys_department_id = :dep_id: or s.node_department_id = :dep_id:) ",
                ['dep_id' => $params['dep_id']]);
        }
        //职位
        if ($params['job_id']) {
            $builder->andWhere(" s.job_title = :job_title: ", ['job_title' => $params['job_id']]);
        }
        //网点
        if ($params['store_id']) {
            $builder->andWhere(" s.sys_store_id = :store_id: ", ['store_id' => $params['store_id']]);
        }
        //雇佣类型
        if ($params['hire_type']) {
            $builder->andWhere(" s.hire_type = :hire_type: ", ['hire_type' => $params['hire_type']]);
        }
        //大区
        $concat_column = '';
        if (!empty($params['region_id'])) {
            $concat_column .= ',store.name store_name,region.name region_name,store.manage_region,store.manage_piece';
            $builder->join(SysStoreModel::class, 's.sys_store_id = store.id', 'store');
            $builder->join(SysManageRegionModel::class, 'store.manage_region = region.id', 'region');

            $builder->andWhere('region.id = :region_id:', ['region_id' => $params['region_id']]);
        }
        //片区
        if (!empty($params['piece_id'])) {
            $concat_column .= ',piece.name piece_name';
            //上面查过了 不用再联一次
            if (empty($params['region_id'])) {
                $concat_column .= ",store.name store_name,store.manage_region,store.manage_piece";
                $builder->join(SysStoreModel::class, 's.sys_store_id = store.id', 'store');
            }

            $builder->join(SysManagePieceModel::class, 'store.manage_piece = piece.id', 'piece');
            $builder->andWhere('piece.id = :piece_id:', ['piece_id' => $params['piece_id']]);
        }

        $builder->andWhere('s.formal in (1,4) and is_sub_staff = 0');


        //先取总数
        $builder->columns('count(1) as num');
        $count_info      = $builder->getQuery()->getSingleResult();
        $return['count'] = $count_info->num;

        //再分页
        $column = 's.staff_info_id,s.name,s.job_title,s.sys_store_id,s.sys_department_id,s.node_department_id,s.state,s.wait_leave_state,s.hire_date,
                    s.hire_type';
        $column .= $concat_column;
        $builder->columns($column);

        $size   = empty($params['page_size']) ? 20 : $params['page_size'];
        $offset = empty($params['page']) ? 1 : $params['page_size'] * ($params['page'] - 1);
        $builder->limit($size, $offset);

        $data = $builder->getQuery()->execute()->toArray();

        if (empty($data)) {
            return [
                'count'       => 0,
                'rows'        => [],
                'column_data' => $column_data,
            ];
        }


        //拼数据 部门
        $dep_ids  = array_column($data, 'node_department_id');
        $dep_data = SysDepartmentModel::find([
            'columns'    => 'id,name',
            "conditions" => 'id in ({dep_ids:array})',
            "bind"       => ['dep_ids' => $dep_ids],
        ])->toArray();
        if (!empty($dep_data)) {
            $dep_data = array_column($dep_data, 'name', 'id');
        }


        //职位
        $job_ids  = array_column($data, 'job_title');
        $job_data = HrJobTitleModel::find([
            'columns'    => 'id,job_name',
            "conditions" => 'id in ({job_ids:array})',
            "bind"       => ['job_ids' => $job_ids],
        ])->toArray();
        if (!empty($job_data)) {
            $job_data = array_column($job_data, 'job_name', 'id');
        }

        //网点 大区 片区
        $store_ids = array_column($data, 'sys_store_id');
        $store_ids = array_unique($store_ids);
        $store_ids = array_diff($store_ids, ['-1']);

        $store_data = $region_data = $piece_data = [];
        if (empty($params['region_id']) || empty($params['piece_id'])) {//啥都没有 需要都拼进去
            if (!empty($store_ids)) {
                $store_builder = $this->modelsManager->createBuilder();
                $store_builder->columns('store.id store_id,store.name store_name,piece.id piece_id,piece.name piece_name,region.id region_id,region.name region_name');
                $store_builder->from(['store' => SysStoreModel::class]);
                $store_builder->leftjoin(SysManagePieceModel::class, 'store.manage_piece = piece.id', 'piece');
                $store_builder->leftjoin(SysManageRegionModel::class, 'store.manage_region = region.id', 'region');
                $store_builder->inWhere('store.id', $store_ids);
                $store_data  = $store_builder->getQuery()->execute()->toArray();
                $region_data = array_column($store_data, 'region_name', 'store_id');
                $piece_data  = array_column($store_data, 'piece_name', 'store_id');
                $store_data  = array_column($store_data, 'store_name', 'store_id');
            }
        }

        $staffIds = array_column($data, 'staff_info_id');
        //获取 额度
        $api = new ApiClient('by', '', 'holiday_nums', static::$language);
        $api->setParamss(['staff_ids' => $staffIds]);
        $holidayNums = $api->execute();
        if (isset($holidayNums['error'])) {
            throw new Exception($holidayNums['error']);
        }

        //接口没额度数据 直接返回
        if(empty($holidayNums) || !isset($holidayNums['data'])){
            return [
                'count'       => 0,
                'rows'        => [],
                'column_data' => $column_data,
            ];
        }

        $trends_keys = array_keys($trends_column);
        $holidayNums = $holidayNums['data'];

        //处理 拼接到 data里面取
        foreach ($data as &$da) {
            //拼名字   store.name store_name,j.job_name job_title_name,d.name department_name,region.name region_name,piece.name piece_name
            //s.staff_info_id,s.name,s.job_title,s.sys_store_id,s.sys_department_id,s.node_department_id,s.state,s.wait_leave_state,s.hire_date
            $da['department_name'] = trim($dep_data[$da['node_department_id']]) ?? '';
            $da['job_title_name']  = trim($job_data[$da['job_title']]) ?? '';
            if (!isset($da['store_name'])) {
                $da['store_name'] = trim($store_data[$da['sys_store_id']]) ?? '';
            }

            if (!isset($da['region_name'])) {
                $da['region_name'] = trim($region_data[$da['sys_store_id']]) ?? '';
            }

            if (!isset($da['piece_name'])) {
                $da['piece_name'] = trim($piece_data[$da['sys_store_id']]) ?? '';
            }

            $da['hire_type'] = self::$t->_("hire_type_{$da['hire_type']}");

            //整理 在职状态显示 如果待离职 显示 在职（待离职）
            $da['state_name'] = self::$t->_(\App\Library\Enums::$staff_state[$da['state']]);
            if (!empty($da['wait_leave_state'])) {
                $wait_text        = self::$t->_('hris_working_state_4');
                $da['state_name'] .= "({$wait_text})";
            }

            $da['refresh_date'] = $current_time;
            $da['hire_date']    = date('Y-m-d', strtotime($da['hire_date']));
            if ($da['sys_store_id'] == '-1') {
                $da['store_name'] = GlobalEnums::HEAD_OFFICE;
            }

            //拼额度
            $row = [];
            //单个人 假期额度没数据 不知道啥情况 都显示为 无数据
            if (empty($holidayNums[$da['staff_info_id']])) {
                foreach ($trends_keys as $type_key_name) {
                    $row[$type_key_name] = self::$t->_('no_have_permission');
                }
                $da = $da + $row;
                continue;
            }
            //把数据 塞进 da
            foreach ($trends_keys as $type_key) {
                //判断是没权限 还是 没额度
                if (empty($holidayNums[$da['staff_info_id']][$type_key])) {
                    //没返回这个类型 就是没权限
                    $row[$type_key] = self::$t->_('no_have_permission');
                    continue;
                }

                if (!empty($holidayNums[$da['staff_info_id']][$type_key]) && !empty($holidayNums[$da['staff_info_id']][$type_key]['text'])) {
                    //有返回类型 并且 有text  就是没额度限制
                    $row[$type_key] = self::$t->_($holidayNums[$da['staff_info_id']][$type_key]['text']);
                } else {//有day_limit（分母） 和 day_sub（分子）
                    $row[$type_key] = "{$holidayNums[$da['staff_info_id']][$type_key]['day_sub']}/{$holidayNums[$da['staff_info_id']][$type_key]['day_limit']}";
                    //超额病假 定制的 只显示一个数字
                    if($type_key == StaffAuditModel::LEAVE_TYPE_18){
                        $row[$type_key] = "{$holidayNums[$da['staff_info_id']][$type_key]['day_sub']}";
                    }
                }
            }
            $da = $da + $row;
        }

        $return['rows']        = $data;
        $return['column_data'] = $column_data;

        return $return;
    }

    /**
     * @param $list
     * @return array
     * @throws ValidationException
     */
    public function dealShowItems($list): array
    {
        $type_book = $this->getTypeBookOtList('trip_traffic_type');
        //拼接文案
        foreach ($list as &$li) {
            $li['store_name']      = $this->showStoreName($li['sys_store_id']);
            $li['department_name'] = $this->showDepartmentName($li['node_department_id']);
            $li['job_name']        = $this->showJobTitleName($li['job_title']);
            $li['state_text']      = self::$t->_('hris_working_state_' . $li['state']);
            //获取出差 工具类型
            $li['tools_text']  = $type_book[$li['traffic_tools']];
            $li['status_text'] = self::$t->_('by_state_' . $li['status']);
            //单程往返 文案 单程1往返2
            $li['return_text'] = $li['oneway_or_roundtrip'] == 1 ? self::$t->_('trip_single') : self::$t->_('trip_backturn');
            if ($li['business_trip_type'] == 2) {
                $li['reason_application'] = self::$t->_('reason_application_type_' . $li['reason_application_type']);
            }
            $li['apply_time']     = $li['start_time'] . '~' . $li['end_time'];
            $li['hire_type_text'] = self::$t->_('hire_type_' . $li['hire_type']);
            //待审批不展示
            if ($li['status'] == 1) {
                $li['updated_at'] = '';
            }
        }
        return $list;
    }


    /**
     * 根据部门链获取部门及其子部门
     *
     * @param $chain
     *
     * @return array
     */
    private function getDepartmentIdsList ($chain) : array
    {
        $departmentIdsArr = SysDepartmentModel::find([
            'conditions' => 'ancestry_v3 like :chain: or ancestry_v3 = :chain_id:' ,
            'bind' => [
                'chain' => $chain . '/%' ,
                'chain_id' => $chain,
            ] ,
            'columns' => 'id',
        ])->toArray();
        return array_column($departmentIdsArr , 'id') ?? [];
    }

    /**
     * 查找对应网点关联的大区的大区经理下的网点
     *
     * @param $staffId
     *
     * @return array
     */
    public function findStoreIds ($staffId)
    {
        $regions = HrStaffManageRegionsModel::find([
            'conditions' => ' staff_info_id = :staff_id: and region_id != 0 and piece_id = 0 ' ,
            'bind' => [
                'staff_id' => $staffId,
            ],
        ])->toArray();
        $regionIds = array_column($regions , 'region_id');

        $storeIds = [];
        if ($regionIds) {
            $stores = SysStoreModel::find([
                'conditions' => ' manage_region in ({region_ids:array}) or manager_id = :manager_id: ' ,
                'bind' => [
                    'region_ids' => $regionIds ,
                    'manager_id' => $staffId,
                ],
            ])->toArray();
            $storeIds = array_column($stores , 'id');
        }

        $pieces = HrStaffManageRegionsModel::find([
            'conditions' => ' staff_info_id = :staff_id: and region_id != 0 and piece_id != 0 ' ,
            'bind' => [
                'staff_id' => $staffId,
            ],
        ])->toArray();
        $pieceIds = array_column($pieces , 'piece_id');

        if ($pieceIds) {
            $stores = SysStoreModel::find([
                'conditions' => ' manage_piece in ({piece_ids:array}) or manager_id = :manager_id: ' ,
                'bind' => [
                    'piece_ids' => $pieceIds ,
                    'manager_id' => $staffId,
                ],
            ])->toArray();
            $storeIds = array_merge($storeIds , array_column($stores , 'id'));
        }


//        }

        $positions = HrStaffInfoPositionModel::find([
            'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ' ,
            'bind' => [
                'staff_id' => $staffId ,
                'positions' => [
                    self::HRBP ,
                    self::HR_SERVICE ,
                    self::PAYROLL_MANAGER,
                ],
            ],
        ])->toArray();
        if ($positions) {
            //获取管辖范围
            $relations = (new  StaffService())->getStaffJurisdiction($staffId);
            $regionIds = $relations['regions'] ?? [];
            $pieceIds = $relations['pieces'] ?? [];
            $conditions = [];
            if ($regionIds) {
                $conditions[] = ' manage_region in ({region_ids:array}) ';
                $bind['region_ids'] = $regionIds;
            }
            if ($pieceIds) {
                $conditions[] = ' manage_piece in ({piece_ids:array}) ';
                $bind['piece_ids'] = $pieceIds;
            }
            if ($conditions) {
                $stores = SysStoreModel::find([
                    'conditions' => implode(' or ' , $conditions) ,
                    'bind' => $bind,
                ])->toArray();
                $storeIds = array_merge($storeIds , array_column($stores , 'id'));
            }
        }

        return $storeIds;
    }

    /**
     *
     *
     * @param $staffIds
     *
     */
    public function auditLeaveDays ($staffIds)
    {
        $year = date('Y-01-01');
        $result = [];
        if ($staffIds) {
            $leaveDays = StaffAuditModel::find([
                'columns' => 'staff_info_id, leave_type, sum(leave_day) as days ' ,
                'conditions' => ' staff_info_id in ({staff_ids:array}) and audit_type = :audit_type: and status in ({status:array}) and leave_type not in ({leave_types:array}) and created_at >= :year: ' ,
                'bind' => [
                    'staff_ids' => $staffIds ,
                    'audit_type' => 2 ,
                    'status' => [1 , 2] ,
                    'leave_types' => [1 , 19] ,
                    'year' => $year,
                ] ,
                'group' => 'staff_info_id, leave_type',
            ])->toArray();

            foreach ($leaveDays as $leaveDay) {
                $result[$leaveDay['staff_info_id']][$leaveDay['leave_type']] = $leaveDay;
            }
        }

        return $result;
    }

    /**
     * @param $departmentIds
     *
     * @return array
     *
     */
    private function departments ($departmentIds)
    {
        $departments = SysDepartmentModel::find([
            'conditions' => ' id in ({ids:array}) ' ,
            'bind' => [
                'ids' => $departmentIds,
            ],
        ])->toArray();

        return array_column($departments , null , 'id');
    }

    /**
     * @param $jobIds
     *
     * @return array
     */
    private function Jobs ($jobIds)
    {
        $jobs = HrJobTitleModel::find([
            'conditions' => 'id in ({ids:array})' ,
            'bind' => [
                'ids' => $jobIds,
            ],
        ])->toArray();

        return array_column($jobs , null , 'id');
    }

    /**
     *
     * @param $storeIds
     *
     * @return array
     */
    private function storeList ($storeIds)
    {
        $stores = SysStoreModel::find([
            'conditions' => ' id in ({ids:array}) ' ,
            'bind' => [
                'ids' => $storeIds,
            ],
        ])->toArray();

        return array_column($stores , null , 'id');
    }

    /**
     * 获取员工在职状态
     *
     * @param $state
     * @param $waitLeaveState
     *
     * @return string
     */
    public function getStateName ($state , $waitLeaveState)
    {
        if ($state == 1 && $waitLeaveState == 1) {
            $stateName = self::$t->_('wait_leave_state_' . $waitLeaveState);
        } else {
            $stateName = self::$t->_('staff_state_' . $state);
        }

        return $stateName;
    }

    /**
     * @param        $staffId
     * @param        $fileName
     * @param string $exportAction
     * @param array  $args
     * @param array  $header
     * @param array  $body
     * @param int $type
     * @return array
     */
    public function exportToDownloadCenter ($staffId , $fileName , $exportAction = '' , $args = [] , $type = HcmExcelTackModel::TYPE_LEAVE)
    {
        try {
            $fileName = $fileName . '_' . $this->request->getBestLanguage() . date('YmdHis') .rand(10000,99999) . '.xlsx';
            $excelTask = HcmExcelTackModel::findFirst(['conditions' => 'staff_info_id = :staff_id: and  type = :type: and status = 0  and action_name = :action_name: and is_delete = 0',
                'bind' => [
                    'action_name'=> $exportAction,
                    'staff_id' => $staffId,
                    'type' => $type,
                ]]);
            if($excelTask){
                return [self::$t->_('downloading') , 1];
            }

            if (!empty($exportAction)) {
                $headerData       = $this->request->getHeaders();
                $from = isset($headerData['From']) && $headerData['From'] === 'fbi' ? $headerData['From'] : '';

                $eb = new ExcelService();
                $args['file_name'] = $fileName;
                $args['From'] = $from;
                $args = base64_encode(json_encode($args));
                $result = $eb->insertExcelTask($staffId ,$fileName, $type, $exportAction , $args);
                if($result['code'] == 10000 && $from == 'fbi') {
                    $this->addFbiExportMessage($staffId,  'hcm_excel_export_down', $fileName, $result['excel_task_id']);
                }
                //导出失败 情况
                if($result['code'] != 10000){
                    return [$result['msg'] , ErrCode::VALIDATE_ERROR];
                }
            }
            return [self::$t->_('file_download_tip') , 1];
        } catch (Exception $e) {
            return [];
        }

    }

    /**
     * 仅需要 在 FBI 右上角【下载列表】中下载使用
     * @param $staffId
     * @param $exportAction
     * @param $fileName
     * @param $excelTaskId
     * @param $from
     */
    public function addFbiExportMessage($staffId, $exportAction, $fileName, $excelTaskId)
    {
        $ex  = [
            'staff_id'       => $staffId,
            'module_code'    => $exportAction . '_' . $excelTaskId,
            'file_name'      => $fileName,
        ];
        $emb = new DownLoadTaskService();
        $emb->insertExportmanage($ex);
    }

    //导出excel
    public function export($params)
    {
        $page_size = 100;
        $page = 1;

        $rows = [];
        $info_column = $this->get_leave_column();
        $info_column = array_keys($info_column);
        $trends_column = $this->get_trends_column();

        $trends_column = array_keys($trends_column);
        $column = array_merge($info_column,$trends_column);

        do {
            $result = $this->get_new_list(array_merge($params , [
                'page' => $page ,
                'page_size' => $page_size ,
            ]));

            foreach ($result['rows'] as $row) {
                $item = array();
                foreach ($column as $v){
                    $item[] = $row[$v];
                }
                $rows[] = $item;
            }

            $page++;
        } while ($result['count'] > $page_size * ($page - 1));


        return $rows;


    }

    /**
     * @param $header
     * @param $rows
     * @param $update_path
     * @param $fileName
     * @param $type 2 多个 sheet  $data[] = [
                'header'=> [],
                'sheet_title' => '旷工超过五天',
                'row' => [],
            ];
     * @param $data
     * @return array
     * @throws Exception
     */
    public function exportExcels($header=[] , $rows=[] , $update_path='' ,  $fileName = 'excel.xlsx',$type='1',$data=[])
    {
        ini_set("memory_limit" , -1);
        //生成excel
        $url = $this->creatExcel($header , $rows , $fileName,$type,$data);
        $this->getDi()->get('logger')->info(['type' => '这里是 exportExcels 返回了生成的 url' , 'data' => $url]);
        //上传oss服务
        $flashOss = new FlashOss();
        $flashOss->uploadFile($update_path .$fileName ,  $url);
        $requestUri = $flashOss->signUrl($update_path .$fileName);
        return ['code' => 1 , 'url' => $update_path .$fileName,'request_uri' => $requestUri];
    }


    public function creatExcel(array $header=[] , array $data =[], string $fileName = 'excel.xlsx',string $type='1',array $data_list=[])
    {
        //统一处理数组
        $header_ = $data_ = [];
        foreach ($header as $value) {
            $header_[] = $value;
        }
        foreach ($data as $value) {
            $data_[] = array_values($value);
        }
        unset($data,$header);
        gc_collect_cycles();
        $res = $this->exportExcel($header_, $data_, $fileName, $type, $data_list);
        return $res['data'];
    }

    public function sysInfo()
    {
        $sysServer    = new SysService();
        $hireTypeList = $sysServer->getHireTypeList();

        return [
            'group_department_list' => $this->getGroupDeptList(),
            'job_title'             => $this->getJobTitle(),
            'store_list'            => $this->sysStore(),
            'hire_type_list'        => $hireTypeList,
        ];
    }

    public function sysStore()
    {
        $SysStoreModel = new  SysStoreService();
        $storeList = $SysStoreModel->getSysStoreList([]);
        $storeList[] = [
            'id' => '-1',
            'name' => GlobalEnums::HEAD_OFFICE,
            'store_id' => "-1",
            'store_name' => GlobalEnums::HEAD_OFFICE,
            'store_phone' =>  "",
            'store_manager_name' => '',
            'store_manager_phone' => '',
            'detail_address' =>  "",
            'category' => "",
            'lat' => "",
            'lng' => "",
            'state' => "",
            'manage_areas' => "",
        ];

        return $storeList;
    }

    public function getJobTitle()
    {
        $redis = $this->getDI()->get('redis');
        $list = $redis->get('job_title_list_v2');
        if (!$list) {
            $list = HrJobTitleModel::find([
                'columns' => ' job_name, id ',
                'conditions' => 'status = :status:',
                'bind' => [
                    'status' => 1,
                ],
                'order' => 'job_name asc',
            ])->toArray();

            $redis->set('job_title_list_v2', json_encode($list, JSON_UNESCAPED_UNICODE), 86400 * 2);
        } else {
            $list = json_decode($list, true);
        }

        return $list;
    }

    public function getGroupDeptList()
    {
        $redis = $this->getDI()->get('redis');

        $list = $redis->get('group_department_list_v2');
        if (!$list) {
            $list = SysDepartmentModel::find([
                'columns' => 'id, name, ancestry, ancestry_v2, ancestry_v3, type, level, group_boss_id',
                'conditions' => ' id not in ({ids:array}) and deleted = :deleted: ',
                'bind' => [
                    'ids' => [2, 6, 14],
                    'deleted' => 0,
                ],
                'order' => 'name asc',
            ])->toArray();

            $list = self::_GroupDeptTreeNodeV2($list);
            $redis->set('group_department_list_v2', json_encode($list, JSON_UNESCAPED_UNICODE), 86400 * 2);
        } else {
            $list = json_decode($list, true);
        }

        return $list;
    }

    public static function _GroupDeptTreeNodeV2($data, $pk = 'ancestry', $parentId = '')
    {
        $node = [];
        foreach ($data as $key => $value) {
            if ($parentId == $value[$pk]) {
                $node[] = [
                    'value' => $value['id'],
                    'label' => $value['name'],
                    'level' => $value['level'] ?? 0,
                    'children' => self::_GroupDeptTreeNodeV2($data, $pk, $value['id']),
                ];
            }
        }
        return $node;
    }


    //请假查询页面 字段头 固定字段
    public function get_leave_column(){
        //固定字段名
        $solid = [
            'staff_info_id'   => static::$t['staff_id'],
            'name'            => static::$t['tool_name'],
            'job_title_name'  => static::$t['job_name'],
            'department_name' => static::$t['node_department_name'],
            'store_name'      => static::$t['store_name'],
            'hire_date'       => static::$t['hr_probation_field_hire_date'],
            'region_name'     => static::$t['affiliated_area'],
            'piece_name'      => static::$t['area'],
            'hire_type'       => static::$t['hire_type'],
            'state_name'      => static::$t['hr_probation_field_state'],
            'refresh_date'    => static::$t['refresh_time'],
        ];

        return $solid;
    }

    //请假查询 动态假期类型字段
    public function get_trends_column(){
        $trends_column = [];

        $api = new ApiClient('by', '', 'leaveTypeBook', static::$language);
        $api->setParamss([]);
        $type_result = $api->execute();
        if (isset($type_result['error'])) {
            throw new Exception($type_result['error']);
        }
        $countryCode = $this->config->application->country_code;
        if ($type_result && isset($type_result['data'])) {
            foreach ($type_result['data'] as $k => $v) {
                //特殊判断 新冠相关假期不显示 休息日类型不显示
                if (in_array($v['code'],
                    [StaffAuditModel::LEAVE_TYPE_15, StaffAuditModel::LEAVE_TYPE_25, StaffAuditModel::LEAVE_TYPE_26])) {
                    continue;
                }
                $trends_column[$v['code']] = $v['msg'];

                //在病假后面追加 超额病假
                if (strtolower($countryCode) == 'th' && $v['code'] == StaffAuditModel::LEAVE_TYPE_38) {
                    $trends_column[StaffAuditModel::LEAVE_TYPE_18] = self::$t->_('sick_over');
                }
            }
        }
        return $trends_column;
    }
    //假期额度查询 表头新增icon  只有泰国
    public function formatIconText(){
        $type = StaffLeavePropertyModel::find([
            'columns' => 'leave_type',
            'conditions' => 'is_delete = 0 and is_hide_type = 0',
        ])->toArray();
        if(empty($type)){
            return [];
        }
        $data = [];
        foreach ($type as $v){
            $row['code'] = (string)$v['leave_type'];
            $row['msg'] = self::$t->_('leave_icon_normal');
            //马来国民假期没有 icon
            if($v['leave_type'] == StaffAuditModel::LEAVE_TYPE_42){
                $row['msg'] = '';
            }
            $data[] = $row;
        }

        return $data;
    }

}


