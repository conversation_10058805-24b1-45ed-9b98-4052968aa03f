<?php
/**
 * Author: Bruce
 * Date  : 2021-12-06 14:45
 * Description:
 */

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\EnumSingleton;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MessageEnums;
use App\Library\Enums\SalaryEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HcmPermissionModel;
use App\Models\backyard\HcmRolePermissionInfoModel;
use App\Models\backyard\HcmStaffPermissionInfoModel;
use App\Models\backyard\HrJobDepartmentRelationModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrAgreementSignRecordModel;
use App\Models\backyard\MessageIdentityDetailModel;
use App\Models\backyard\MessageOssFileModel;
use App\Models\backyard\MessagePdf;
use App\Models\backyard\OsStaffContractSignRecordModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use app\models\backyard\ToolStaffInfoModel;
use App\Models\bi\MenusModel;
use App\Models\backyard\MessageModel;
use App\Models\backyard\QuestionnaireAnswerModel;
use App\Models\backyard\QuestionnaireLibModel;
use App\Models\bi\RoleMenusModel;
use App\Models\bi\RoleStaffMenusModel;
use App\Models\coupon\MessageContentModel;
use App\Models\coupon\MessageCourierModel;
use App\Repository\HrStaffInfoRepository;
use App\Repository\MessageRepository;
use App\Repository\StaffPayrollCompanyInfoRepository;
use App\Repository\SysStoreTypeRepository;
use App\Services\message\BuildMessageService;
use App\Services\MessageIdentityDetailService;
use Exception;

class MessagesService extends BaseService
{
    public $hire_type_list = [];
    public $job_title_list = [];
    public $store_category_list = [];
    public $hire_date_list = [];
    public $contract_company_ids = [];
    public $position_types = [];

    public $makeSignPdfPermission = true;//是否需要走权限，默认需要走（仅发送人、审批人可见）

    protected $create_params = NULL;
    public $msg_permission_path = [
        // 普通消息
        'common_msg'        => [
            'access_authority' => '/message/common',
            'add_authority'    => '/message/common_msg_add_edit',
            'edit_authority'   => '/message/common_msg_add_edit',
        ],

        // 问卷消息
        'questionnaire_msg' => [
            'access_authority' => '/message/questionnaire',
            'add_authority'    => '/message/questionnaire_add_edit',
            'edit_authority'   => '/message/questionnaire_add_edit',
        ],

        // 签字消息
        'sign_msg'          => [
            'access_authority' => '/message/signature',
            'add_authority'    => '/message/signature_add_edit',
            'edit_authority'   => '/message/signature_add_edit',
        ],

        // 答题消息
        'answer_msg'        => [
            'access_authority' => '/message/answer',
            'add_authority'    => '/message/answer_msg_add_edit',
            'edit_authority'   => '/message/answer_msg_add_edit',
        ],
        // 证书消息
        'certificate_msg'        => [
            'access_authority' => '/message/certificate',
            'add_authority'    => '/message/certificate_msg_add_edit',
            'edit_authority'   => '/message/certificate_msg_add_edit',
        ],

    ];

    public static $msg_entrance_config = [
        // 普通消息
        'common_msg'        => [
            // 该模块访问权限
            'access_authority' => false,

            // 添加按钮权限
            'add_authority'    => false,

            // 编辑按钮权限
            'edit_authority'   => false,
        ],

        // 问卷消息
        'questionnaire_msg' => [
            'access_authority' => false,
            'add_authority'    => false,
            'edit_authority'   => false,
        ],

        // 签字消息
        'sign_msg'          => [
            'access_authority' => false,
            'add_authority'    => false,
            'edit_authority'   => false,
            'download_pdf'     => false,
        ],

        // 答题消息
        'answer_msg'        => [
            'access_authority' => false,
            'add_authority'    => false,
            'edit_authority'   => false,
        ],
        //证书消息
        'certificate_msg' => [
            'access_authority' => false,
            'add_authority'    => false,
            'edit_authority'   => false,
        ],
    ];

    /**
     * 筛选项
     * @return mixed
     */
    public function getSysInfo()
    {
        $publish_status = [];
        foreach (MessageEnums::$publish_status as $key => $onesStatus) {
            $publish_status[] = ['value' => $key, 'label' => self::$t->_($onesStatus)];
        }

        $audit_status = [];
        foreach (MessageEnums::$audit_status as $key => $onesStatus) {
            $audit_status[] = ['value' => $key, 'label' => self::$t->_($onesStatus)];
        }

        $data['publish_status'] = $publish_status;
        $data['audit_status'] = $audit_status;
        return $data;
    }

    /**
     * 网点类型
     * @return array
     */
    public function getStoreCategory(): array
    {
        $headOffice = ['value' => GlobalEnums::HEAD_OFFICE_ID, 'label' => GlobalEnums::HEAD_OFFICE];
        $list       = SysStoreTypeRepository::getList();
        return array_merge([$headOffice], $list);
    }



    /**
     * 迁移自FBI同名方法
     * @param $param
     * @return array
     */
    public function add_kit_message($param): array
    {
        $this->logger->info($param);
        //必填项
        $message_title = $param['message_title'];
        $message_title = stripslashes($message_title);  //反转义title

        $message_content = $param['message_content'];
        $staff_users     = $param['staff_users'];//接收消息 员工 一个工号时候格式是 [12345], 多个工号格式是 array( 0 => array('id' => 12345))

        //问卷类型消息新增字段默认值设置
        $questionnaire_lib_id = $param['questionnaire_lib_id'] ?? 0;
        $feedback_setting     = $param['feedback_setting'] ?? 0;
        $end_time             = $param['end_time'] ?? null;
        $audit_status         = $param['audit_status'] ?? 0;

        //非必填
        $staff_info_ids_str = empty($param['staff_info_ids_str']) ? '' : $param['staff_info_ids_str'];//bi页面 指定员工发送
        $add_userid         = empty($param['add_userid']) ? 0 : $param['add_userid'];                 //操作人 id
        $top                = empty($param['top']) ? 3 : $param['top'];                               //置顶状态 bi库（1.永久置顶；2:按时间区间置顶；3:未置顶） coupon库 状态为 0和1
        //bi工具 发送消息接受人组别 根据页面 四个按钮对应  其他渠道分类 不用传
        $group      = empty($param['to_group']) ? '' : trim($param['to_group']);
        $send_type  = empty($param['send_type']) ? 0 : intval($param['send_type']);
        $related_id = $param['related_id'] ?? '';

        //判断 category 只有为0的 是页面录入 其他 没有分类 都写成-1 未知类型
        if (isset($param['category']) && $param['category'] === 0) {
            $category = 0;
        } else {
            $category = empty($param['category'])? -1: intval($param['category']);
        }
        $category_code = empty($param['category_code']) ? 0 : intval($param['category_code']);
        $by_show_type  = $param['by_show_type'] ?? 0;


        //新增bi页面 定时发送功能
        $set_time       = empty($param['set_time']) ? null : $param['set_time'];
        $publish_status = empty($set_time) ? 6 : 9;


        //下面都是我粘贴过来的 不是我拼的
        $by_db = $this->getDI()->get('db_backyard');
        // message_content & message_courier 表 迁移到 db_message
        $message_db = $this->getDI()->get('db_message');
        $pushNum    = count($staff_users);

        try {
            //开启事务
            $by_db->begin();
            $message_db->begin();
            //message_content 主键 和 message_courier message_content_id字段 也是 bi  message表 remote_message_id字段
            $remote_message_id = empty($param['id']) ? time().mt_rand(100000000, 999999999):$param['id'];
            $id                = 0;

            // 如果是问卷消息，则message中的content和message_content 中的内容不同
            // message中的content为FBI后台录入的原生html，message_content为可跳转到问卷消息页的html
            // 其他类型的消息，该字段维持原逻辑
            if (!empty($questionnaire_lib_id)) {
                $remote_message_content = $this->make_qn_msg_remote_tpl($remote_message_id);
            } else {
                $remote_message_content = $message_content;
            }

            //如果是 定时发送 不插入coupon库
            if (empty($set_time)) {
                $model = new MessageContentModel();
                //消息内容表
                $sql_param                  = [
                    'id'         => $remote_message_id,
                    'message'    => $remote_message_content,
                    'related_id' => $related_id,
                ];

                $message_db_message_content = $model->batch_insert([$sql_param],'db_message');

                if (!$message_db_message_content) {
                    $by_db->rollback();
                    $message_db->rollback();
                    throw new Exception('error,please,try again1');
                }

                //消息员工表
                if ($pushNum == 1 && !empty($param['id'])) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             //添加单条记录
                    $id                           = $insert['id'] = $insert['mns_message_id'] = $param['id'];                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             //不知道 mns_message_id 干什么用的
                    $insert['staff_info_id']      = $staff_users[0];
                    $insert['title']              = $message_title;
                    $insert['category']           = $category;
                    $insert['category_code']      = $category_code;
                    $insert['top_state']          = empty($param['top_state']) ? 0 : $param['top_state'];
                    $insert['read_state']         = empty($param['read_state']) ? 0 : $param['read_state'];
                    $insert['push_state']         = empty($param['push_state']) ? 0 : $param['push_state'];
                    $insert['push_time']          = empty($param['push_time']) ? null : $param['push_time'];
                    $insert['source_type']        = empty($param['source_type']) ? 0 : $param['source_type'];
                    $insert['message_content_id'] = $remote_message_id;
                    $insert['by_show_type']       = $by_show_type;
                    $insert['is_del']             = 0;
                    $insert['update_state']       = 0;
                    $model = new MessageCourierModel();
                    
                    $flag  = $model->batch_insert([$insert],'db_message');

                    if (!$flag) {
                        $by_db->rollback();
                        $message_db->rollback();
                        throw new Exception('error,please,try again202');
                    }

                } else {//批量多条 并且没有id 入参
                    // 如果是答题消息，且发送人数超过500，则临时设置执行时间

                    $messages_title       = addslashes($message_title);    //转义title
                    $message_courier_data = '';
                    $id                   = 0;
                    foreach ($staff_users as $staff_info) {
                        $id                   = time().$staff_info['id'].rand(1000000, 9999999);
                        $message_courier_data .= '(';
                        $message_courier_data .= $id;
                        $message_courier_data .= ','.$staff_info['id'];
                        $message_courier_data .= ','."'{$messages_title}'";
                        $message_courier_data .= ','.$category;
                        $message_courier_data .= ','.$category_code;
                        if ($top == 1) {
                            $message_courier_data .= ','. 1;
                        } else {
                            $message_courier_data .= ','. 0;
                        }
                        $message_courier_data .= ','. 1;

                        $message_courier_data .= ','. 1;
                        $message_courier_data .= ','.$id;
                        $message_courier_data .= ','.$remote_message_id;
                        $message_courier_data .= ','.$by_show_type;
                        $message_courier_data .= '),';
                    }
                    $message_courier_data = rtrim($message_courier_data, ',');

                    $message_db_message_courier_sql    = "INSERT INTO message_courier (id,staff_info_id,title,category,category_code,top_state,push_state,source_type,mns_message_id,message_content_id,by_show_type) VALUES {$message_courier_data}";
                    $message_db_message_courier_result = $message_db->execute($message_db_message_courier_sql);
                    if (!$message_db_message_courier_result) {
                        $by_db->rollback();
                        $message_db->rollback();
                        throw new Exception('error,please,try again2 [sql]'.$message_db_message_courier_sql);
                    }
                }
            }


            //如果是定时发送消息 并且 没有指定工号发送  需要吧 要接受的员工工号 存入staff_info_ids 便于定时任务 发送消息
            if (!empty($set_time) && $send_type != 4) {
                // 针对外部调用，不同的数据结构兼容取值
                if (isset($staff_users[0]['id'])) {
                    $staff_info_ids_str = implode(',', array_column($staff_users, 'id'));
                } else {
                    $staff_info_ids_str = implode(',', $staff_users);
                }
            }

            //上面insert语句，替换参数绑定方式
            $message_insert_data = [
                'title'             => $message_title,
                'content'           => $message_content,
                'staff_info_ids'    => $staff_info_ids_str,
                'staff_num'         => $pushNum,
                'add_userid'        => $add_userid,
                'last_edit_userid'  => $add_userid,
                'publish_status'    => $publish_status,
                'top_status'        => $top,
                'send_type'         => $send_type,
                'to_group'          => $group,
                'remote_message_id' => $remote_message_id,
                'category'          => $category,
                'by_show_type'      => $by_show_type,
                'audit_status'      => $audit_status,
                'again_time'        => gmdate('Y-m-d H:i:s'),
                'real_send_time'    => gmdate('Y-m-d H:i:s'),
            ];
            if (!empty($set_time)) {
                $message_insert_data['set_time'] = $set_time;
                $message_insert_data['again_time']     = gmdate('Y-m-d H:i:s', strtotime($set_time));
                $message_insert_data['real_send_time'] = gmdate('Y-m-d H:i:s', strtotime($set_time));//发布时间

            } else {
                // 为空，则增加默认值，配合索引
                $message_insert_data['set_time'] = '1970-01-01 00:00:00';
            }
            //questionnaire_lib_id,feedback_setting,end_time
            if (!empty($questionnaire_lib_id)) {
                $message_insert_data['questionnaire_lib_id'] = $questionnaire_lib_id;
            }
            if (in_array($feedback_setting, [0, 1])) {
                $message_insert_data['feedback_setting'] = $feedback_setting;
            }
            if (!empty($end_time)) {
                $message_insert_data['end_time'] = $end_time;
            }
            if (!empty($category_code)) {
                $message_insert_data['category_code'] = $category_code;
            }
            if (in_array($category, [7, 33,])) {
                $message_insert_data['recipient_staff_info_id'] = intval(current($staff_users));
                $by_db_message                                  = $by_db->insertAsDict('message', $message_insert_data);
                if (!$by_db_message) {
                    $by_db->rollback();
                    $message_db->rollback();
                    throw new Exception('error,please,try again3');
                }
            }

            $by_db->commit();
            $message_db->commit();
            return ['ok', 1, [$id]];
        } catch (Exception $e) {
            $by_db->rollback();
            $message_db->rollback();
            $result = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
                'trace'   => $e->getTraceAsString(),
                'params'  => $param,
            ];
            $this->logger->error($result);
            return [$e->getMessage(), -1, []];
        }
    }



    /**
     * 设置雇佣类型
     * @param $send_type
     * @param $hire_type_list
     * @return void
     */
    public function setHireTypeList($hire_type_list)
    {
        $this->hire_type_list = $hire_type_list ?: [];
    }

    public function setJobTitleList($job_title_list)
    {
        $this->job_title_list = $job_title_list ?: [];
    }
    public function setStoreCategoryList($store_category_list)
    {
        $this->store_category_list = $store_category_list ?: [];
    }
    public function setHireDateList($hire_date_list)
    {
        $this->hire_date_list = $hire_date_list ?: [];
    }

    //设置 合同公司
    public function setContractCompanyIds($contract_company_ids)
    {
        $this->contract_company_ids = $contract_company_ids ?: [];
    }
    //设置 职位性质
    public function setPositionTypes($position_types)
    {
        $this->position_types = $position_types ?: [];
    }

    /**
     * 消息证书模版
     * @return array
     */
    public function certificateTplUrls(): array
    {
        $tplUrls = (new SettingEnvService())->getSetVal('certificate_tpl_url');
        return (array)json_decode($tplUrls, true);
    }
    /**
     * 消息添加
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function messageAdd($param)
    {
        $this->setHireTypeList($param['hire_type_list']);
        $this->setJobTitleList($param['job_title_list']);
        $this->setStoreCategoryList($param['store_category_list']);
        $this->setHireDateList($param['hire_date_list']);

        $this->setContractCompanyIds($param['contract_company_ids']);
        $this->setPositionTypes($param['position_types']);

        $param['content'] = $this->addContentStyle($param);
        // 替换 指定工号 发送 分隔符
        // 非数字非逗号的字符，替换为英文逗号, 处理录入时不符遵守规则的数字 [20201004]
        if (!empty($param['staff_info_ids'])) {
            $staff_info_ids_str = preg_replace('/[^\d]/', ',', $param['staff_info_ids']);
            $staff_info_ids_str = implode(',', array_unique(array_filter(explode(',', $staff_info_ids_str))));
            $param['staff_info_ids'] = $staff_info_ids_str;
        }

        if ($param['category'] == 6 && $param['category_code'] == 3) {
            // 原逻辑：问卷消息说明字符数校验 200汉字 400字符
            // 现逻辑：问卷消息说明字符数校验 5000字符
            $strip_tags_content = strip_tags($param['content'], '<img><a>');
            $content_char_count = mb_strlen($strip_tags_content);
            if (!preg_match("/(<img.*\/>)|(<a.*\/a>)/", $strip_tags_content) && ($content_char_count > 5000 || $content_char_count < 1)) {
                throw new ValidationException('Please enter the specified number of message description characters');
            }

            $all_questionnaire_permission_staff_ids = (new SettingEnvService())->getSetVal('all_questionnaire_permission_staff_id',',');

            // 验证该问卷库是否是当前用户创建的，避免越权
            $questionnaire_info = (new QuestionnaireLibService())->getTemplateInfo($param['questionnaire_lib_id']);
            $first_department_id = (new DepartmentService())->getFirstDepartmentById($param['user_info']['department_id']);

            if (empty($questionnaire_info) || $questionnaire_info['is_del'] || $questionnaire_info['qn_lib_type'] != 1 || (!in_array($param['user_info']['id'],$all_questionnaire_permission_staff_ids) &&  $questionnaire_info['first_department_id'] != $first_department_id )) {
                throw new ValidationException(self::$t->_('msg_qn_057'));
            }
            //将问卷模板中的数据，复制到 业务题库表
            $libId = (new QuestionnaireLibService())->copyTemplateToLib($questionnaire_info);
            //将新的业务表id 与message 表关联。
            if(!$libId) {
                throw new ValidationException(self::$t->_('msg_qn_057'));
            }
            $param['questionnaire_lib_id'] = $libId;
            if (!empty($param['end_time'])) {
                $param['end_time'] = date('Y-m-d H:i:59', strtotime($param['end_time']));
            }

            if ($param['end_time'] == '1970-01-01 00:00:00') {
                throw new ValidationException('end time error');
            }
        }
        // 问卷/答题消息: 问卷库必关联
        if (in_array($param['category'], [6, 34]) && empty($param['questionnaire_lib_id'])) {
            throw new ValidationException(self::$t->_('msg_qn_057'));
        }

        // 答题消息, 验证题库类型 和 数据
        if ($param['category'] == 34) {
            $lib_info = (new QuestionnaireLibService())->read_lib(['lib_id' => $param['questionnaire_lib_id']]);
            if ($lib_info['qn_lib_type'] != 3 || $lib_info['is_del']) {
                throw new ValidationException(self::$t->_('msg_qn_057'));
            }
            (new QuestionnaireLibService())->validateQesUUid($lib_info);
        }

        if (!empty($param['set_time'])) {
            if (strtotime($param['set_time']) <= strtotime(date('Y-m-d H:00'))) {
                throw new ValidationException(self::$t->_('limit_set_time'));
            }
            $param['set_time'] = date('Y-m-d H:00:00',strtotime($param['set_time']));
        }

        if ($param['set_time'] == '1970-01-01 00:00:00') {
            throw new ValidationException('set time error');
        }

        if ($param['set_time'] >= date('Y-m-d 23:01:01',strtotime('+2 month'))) {
            throw new ValidationException('CAN NOT OVER 2 MONTH');
        }

        // 截止时间不可以早于发送时间
        if (!empty($param['end_time']) && !empty($param['set_time']) && $param['set_time'] > $param['end_time']) {
            throw new ValidationException(self::$t->_('msg_qn_056'));
        }

        // 发送对象权限验证: 消息页 btn 权限
        $msgType = [];
        if($param['category'] == MessageEnums::CATEGORY_GENERAL && $param['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE) {
            $msgType['msg_type'] = 'common';
        } elseif($param['category'] == MessageEnums::CATEGORY_SIGN && $param['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE) {
            $msgType['msg_type'] = 'sign';
        }elseif($param['category'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE && $param['category_code'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE_CODE_TEMPLATE) {
            $msgType['msg_type'] = 'certificate';
        }
        $btn_permission = $this->getMessagePageBtnPermission($msgType,$param['user_info']);
        if ($btn_permission[$param['send_type']] == false) {
            throw new ValidationException(self::$t->_('send_message_no_permissions'));
        }

        $param['user_department_id'] = $param['user_info']['department_id'];
        $param['user_position_category'] = explode(',', $param['user_info']['position_category']);
        // 消息添加
        $flag = $this->addMsg($param);
        if (isset($flag['error_msg'])) {
            throw new ValidationException($flag['error_msg']);
        }

        if ($flag === true) {
            return [];
        }
        throw new ValidationException('fail');
    }

    /**
     * 泰国不加样式，普通消息
     * @param $param
     * @return mixed
     */
    public function addContentStyle($param)
    {
        return $param['content'];
    }


    protected function getStaffUserForType4($param)
    {
        $staff_users = [];
        $param['staff_info_ids'] = explode(',', $param['staff_info_ids']);
        $param['staff_info_ids'] = array_values(array_unique($param['staff_info_ids']));
        $staffIdGroup = array_chunk($param['staff_info_ids'], 5000);
        $this->logger->info('send staff 【1】');
        foreach ($staffIdGroup as $oneBatchStaffIds) {
            $oneBatchStaffIdsString = implode(',', $oneBatchStaffIds);
            $this->logger->info('send staff 【1】 json_data :' . $oneBatchStaffIdsString);
            $staff_users_batch             = $this->get_receive_staff($param['send_type'], $oneBatchStaffIdsString, $param['user_info']);
            $staff_users = array_merge($staff_users, $staff_users_batch);
        }
        return $staff_users;
    }

    protected function getStaffUserForType13($param)
    {
        $staff_users = [];
        $param['staff_info_ids'] = explode(',', $param['staff_info_ids']);
        $param['staff_info_ids'] = array_values(array_unique($param['staff_info_ids']));
        $staffIdGroup = array_chunk($param['staff_info_ids'], 5000);
        $this->logger->info('send tool staff 【1】');
        foreach ($staffIdGroup as $oneBatchStaffIds) {
            $oneBatchStaffIdsString = implode(',', $oneBatchStaffIds);
            $this->logger->info('send tool staff 【1】 json_data :' . $oneBatchStaffIdsString);
            $staff_users_batch             = $this->get_receive_tool_staff($oneBatchStaffIdsString);
            $staff_users = array_merge($staff_users, $staff_users_batch);
        }

        return $staff_users;
    }

    /**
     * 消息添加
     * @param $param
     * @return array|bool
     * @throws \Exception
     */
    public function addMsg($param)
    {
        $staff_users = [];

        // 发送给指定工号
        if ($param['send_type'] == 4) {
            $staff_users = $this->getStaffUserForType4($param);
            $param['staff_info_ids'] = implode(',', array_column($staff_users, 'id'));

            $staff_permission_type = $this->getStaffPermissionType($param['user_info']);
            if ($staff_permission_type == 'limit') {
                // 获取当前用户的直线部门
                $user_direct_department_list = (new DepartmentService())->getVerticalDepartmentListByDepartmentId($param['user_info']['department_id']);
                $user_direct_department_list = array_column($user_direct_department_list, 'id');

                // 不属同一直线部门的工号, 进行筛选和报错提示
                $error_staff_ids = '';
                foreach ($staff_users as $staff_value) {
                    if (!in_array($staff_value['department_id'], $user_direct_department_list)) {
                        $error_staff_ids .= "{{$staff_value['id']}}";
                    }
                }

                if (!empty($error_staff_ids)) {
                    // 与您不是同一个部门, 因此不能发送给这些工号
                    return [
                        'error_msg' => str_replace('STAFF_INFO_IDS', $error_staff_ids,
                            self::$t->_('msg_not_same_department')),
                    ];
                }
            }
        }
        // 发送给指定职位  废弃
        if ($param['send_type'] == 7) {
            //职位部门关联关系个数不能超过150个
            $to_group = explode(',', $param['to_group']);
            if (count($to_group) > 150) {
                return [
                    'error_msg' => 'job title number limit 150',
                ];
            }
            // 职位下的员工 并 去重
            $staff_users = $this->get_receive_staff($param['send_type'], $param['to_group']);
            $staff_users = array_values(array_column($staff_users, null, 'id'));

            $job_ids             = explode(',', $param['to_group']);
            $this->create_params = json_encode($job_ids, JSON_UNESCAPED_UNICODE);//保存-按职位部门关联关系id.

            $job_names           = $this->getJobTitleName($job_ids);
            $param['to_group']   = implode(',', $job_names);
            //职位部门关联 反写字符串，不能大于8100
            if (mb_strlen($param['to_group']) > 8100) {
                return [
                    'error_msg' => 'job title string limit 8100',
                ];
            }
        }

        // 按照模板发送
        if ($param['send_type'] == 11) {
            // 读取附件信息表中 接受消息的员工id
            $staff_users = (new MessageIdentityDetailService)->getIdentityDetailByParams([
                "oss_file_id"   => $param['oss_id'],
                "send_staff_id" => $param['user_info']['id'],
                "is_del"        => 0,
            ], ["staff_info_id AS id"]);
            if (!empty($staff_users)) {
                // 此处可不用做去重处理， 在落地时已经去重了
                $param['staff_info_ids'] = implode(',', array_column($staff_users, 'id'));
            }
        }

        // 按工具号发送消息
        if ($param['send_type'] == 13) {
            $message_send_to_tool_staff = (new SettingEnvService())->getSetVal('message_send_to_tool_staff', ',');
            if(!in_array($param['user_info']['id'], $message_send_to_tool_staff)) {
                return [
                    'error_msg' => self::$t->_('no_permission_send_tool_staff'),
                ];
            }

            $staff_users_list = $this->getStaffUserForType13($param);
            $staff_users = array_column($staff_users_list, 'id');
            $staff_list = array_column($staff_users_list, 'state', 'id');
            //找出 不是 工具号的数据。
            $notStaffId = [];
            $staff_info_ids = explode(',', $param['staff_info_ids']);
            foreach ($staff_info_ids as $oneStaffId) {
                if(!in_array($oneStaffId, $staff_users)) {
                    $notStaffId[] = $oneStaffId;
                }
            }

            if(!empty($notStaffId)) {
                return [
                    'error_msg' => self::$t->_('not_tool_staff_id', ['staff_ids' => implode(',', $notStaffId)]),
                ];
            }
            $staff_users = [];
            foreach ($staff_list as $staff_id => $state) {
                if($state == HrStaffInfoModel::STATE_RESIGN) {
                    continue;
                }
                $staff_users[] = $staff_id;
            }

            $param['staff_info_ids'] = implode(',', $staff_users);
        }

        // 消息 必须 有对应接收人
        if (empty($staff_users)) {
            return ['error_msg' => self::$t->_('no_recipient')];
        }

        $msg_param['send_type']            = $param['send_type'];
        $msg_param['to_group']             = $param['to_group'];
        $msg_param['staff_users']          = $staff_users;
        $msg_param['message_title']        = $param['title'];
        $msg_param['message_content']      = $param['content'];
        $msg_param['staff_info_ids_str']   = $param['staff_info_ids'];
        $msg_param['add_userid']           = $param['user_info']['id'];
        $msg_param['top']                  = $param['top'];
        $msg_param['category']             = empty($param['category']) ? 0 : intval($param['category']);
        $msg_param['category_code']        = empty($param['category_code']) ? 0 : intval($param['category_code']);
        $msg_param['set_time']             = $param['set_time'] ?? null;
        $msg_param['questionnaire_lib_id'] = $param['questionnaire_lib_id'] ?? 0;
        $msg_param['feedback_setting']     = in_array($param['feedback_setting'],
            [0, 1]) ? $param['feedback_setting'] : 0;
        $msg_param['by_show_type']         = in_array($param['by_show_type'], [0, 1]) ? $param['by_show_type'] : 0;
        $msg_param['end_time']             = $param['end_time'] ?? null;
        $msg_param['visible']              = $param['visible'] ?? 0;    // 进发送人 审批人可见
        $msg_param['oss_id']               = $param['oss_id'] ?? 0;     // 按模板发送 附件表主键id
        $msg_param['approver_id']          = "";                        // 审批人
        $msg_param['create_params']        = $this->create_params;      // 按 组织，按职位 创建条件
        $msg_param['extend']               =  null;//扩展字段
        if ($msg_param['category'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE) {
            foreach (MessageModel::$extendColumns as $extendColumn) {
                $msg_param['extend'][$extendColumn] = $param[$extendColumn] ?? '';
            }
            $tplUrls                                = $this->certificateTplUrls();
            $msg_param['extend']['certificate_id']  = $param['certificate_id'] ?? 0;
            $msg_param['extend']['certificate_url'] = $tplUrls[$msg_param['extend']['certificate_id']] ?? '';
        }
        //是否同步子账号:1否，2是
        $msg_param['is_sync_sub']         = empty($param['is_sync_sub']) || (!empty($param['is_sync_sub']) && $param['is_sync_sub'] == MessageModel::IS_SYNC_SUB_NO) ? MessageModel::IS_SYNC_SUB_NO : MessageModel::IS_SYNC_SUB_YES;
        $msg_param['read_duration']         = empty($param['read_duration']) ? 0 : intval($param['read_duration']);//阅读时长

        $setting = (new SettingEnvService())->getSetVal('msg_not_audit_staff_ids',',');
        if(in_array($param['user_info']['id'],$setting)){
            $param['btn_approve'] = 0;
        }

        // 验证审批类型 和 提取节点审批人
        //TODO 跳过审批 发送营业时间
        if ((isset($param['btn_approve']) && !$param['btn_approve']) || $param['send_type'] == 13) {
            $audit_info = true;//直接审批通过
        } else {
            // 获取审批人
            BuildMessageService::init()->setStaff(array_column($staff_users, 'id'));
            $audit_info = BuildMessageService::init()->getExamineAndApproveData();
            if (!empty($audit_info['node_auditor'])) {
                $msg_param['approver_id'] = implode(',', $audit_info['node_auditor']);
            }
        }

        // 创建消息
        $add_result = $this->createMessage($msg_param);
        if ($add_result === false) {
            return ['error_msg' => 'message creation failed'];
        }
        //审批人可空 直接发送
        if ($audit_info === true) {
            //调用 bi  rpc 发送
            $rpcParam['message_id']   = $add_result;
            $rpcParam['audit_status'] = 2;
            if ($this->syncAuditStatus($rpcParam)) {
                $this->logger->write_log("调用bi_rpc: sync_message_audit_status 发送消息-成功 :".json_encode($rpcParam,
                        JSON_UNESCAPED_UNICODE), 'info');
                return true;
            } else {
                return ['message created successfully send fail message_id: '.$add_result];
            }
        }
        // 创建审批流
        $create_audit_flow_res = $this->syncCreateMsgAuditFlow($add_result, $audit_info);
        if ($create_audit_flow_res['code'] == 1) {
            return true;
        } else {
            return ['error_msg' => 'message created successfully, '.$create_audit_flow_res['msg']];
        }
    }

    /**
     * 创建消息审批流
     * @param int $message_id
     * @param array $audit_info
     * @param int $exec_count
     * @return mixed
     */
    public function syncCreateMsgAuditFlow(int $message_id, array $audit_info, int $exec_count = 1)
    {
        $return = [
            'code' => -1,
            'msg' => '',
        ];

        try {
            // 注入backyard 审批流
            $post_data = [
                'message_id' => $message_id,
                'extend' => $audit_info,
            ];

            $max_retry_total = 3;// 最多重试次数
            while($exec_count <= $max_retry_total) {
                $this->logger->write_log("by_rpc: create_message_audit_flow[exec_count = {$exec_count}] - start: post_data: " . json_encode($post_data, JSON_UNESCAPED_UNICODE), 'info');

                $client = new ApiClient('by', '', 'create_message_audit');
                $client->setParams([$post_data]);
                $rpc_res = $client->execute();

                $this->logger->write_log("by: create_message_audit_flow[exec_count = {$exec_count}] - rpc_res: " . json_encode($rpc_res, JSON_UNESCAPED_UNICODE), 'info');

                if (isset($rpc_res['code']) && $rpc_res['code'] == 1) {
                    $this->logger->write_log("by: create_message_audit_flow[exec_count = {$exec_count}] - end [success]: message_id - " . $message_id, 'info');

                    $return['code'] = 1;
                    $return['msg'] = 'success';
                    break;
                } else {
                    // 调整日志级别：最后一次重试失败 - error，其他的 notice
                    $log_level = $exec_count == $max_retry_total ? 'error' : 'notice';
                    $this->logger->write_log("by: create_message_audit_flow[exec_count = {$exec_count}] - end [fail]: message_id - " . $message_id, $log_level);

                    $return['code'] = 0;
                    $return['msg'] = "message audit flow creation failed[{$exec_count}]";

                    // 2s后, 重试一次
                    if ($exec_count <= $max_retry_total) {
                        sleep(2);

                        $exec_count++;
                    }
                }
            }

        } catch (\Exception $e) {
            $this->logger->write_log('syncCreateMsgAuditFlow fail: '.$e->getMessage());
        }

        return $return;
    }

    /**
     * 消息创建
     * @param array $message_data
     * @return mixed
     */
    public function createMessage(array $message_data)
    {
        try {
            // 若未定时发送 且 非发送指定人，则提取发送对象对应的收信人工号列表
            // 增加组织发送类型
            if (!in_array($message_data['send_type'], [4, 9, 13])) {
                $staff_info_ids_str = implode(',', array_column($message_data['staff_users'], 'id'));
            } else {
                // 发送指定人工号
                $staff_info_ids_str = empty($message_data['staff_info_ids_str']) ? '' : $message_data['staff_info_ids_str'];
            }

            $remote_message_id = time().rand(100000000, 999999999);
            $created_at = gmdate('Y-m-d H:i:s');
            // 消息写入
            $message_param = [
                'title'                => $message_data['message_title'],
                'content'              => stripslashes($message_data['message_content']),
                'staff_info_ids'       => $staff_info_ids_str,
                'staff_num'            => count($message_data['staff_users']),
                'add_userid'           => $message_data['add_userid'],
                'last_edit_userid'     => $message_data['add_userid'],
                'publish_status'       => 9,
                'top_status'           => empty($message_data['top']) ? 3 : $message_data['top'],
                'send_type'            => $message_data['send_type'],
                'to_group'             => empty($message_data['to_group']) ? '' : trim($message_data['to_group']),
                'remote_message_id'    => $remote_message_id,
                'category'             => $message_data['category'],
                'category_code'        => $message_data['category_code'] ? $message_data['category_code'] : 0,
                'by_show_type'         => $message_data['by_show_type'] ? $message_data['by_show_type'] : 0,
                'set_time'             => $message_data['set_time'] ? $message_data['set_time'] : '1970-01-01 00:00:00',
                'questionnaire_lib_id' => $message_data['questionnaire_lib_id'] ? $message_data['questionnaire_lib_id'] : 0,
                'feedback_setting'     => $message_data['feedback_setting'] ? $message_data['feedback_setting'] : 0,
                'end_time'             => $message_data['end_time'] ? $message_data['end_time'] : null,
                'audit_status'         => 1,
                'visible'              => $message_data['visible'] ?? 0,      // 仅发送人审批人可见
                'approver_id'          => $message_data['approver_id'] ?? "", // 审批人id
                'create_params'        => $message_data['create_params'],     // 按 组织，按职位 创建条件
                //如果有定时发送，取定时发送时间，否则按当前创建时间，作为发布时间
                'again_time'           =>  $created_at,//最近一次发布时间
                'real_send_time'       => (empty($message_data['set_time']) || $message_data['set_time'] == '1970-01-01 00:00:00') ? $created_at : gmdate('Y-m-d H:i:s', strtotime($message_data['set_time'])),//发布时间

                'created_at'           => $created_at,//发布时间
                'extend'               => empty($message_data['extend']) ? null : json_encode($message_data['extend'],JSON_UNESCAPED_UNICODE),
                'is_sync_sub'          => empty($message_data['is_sync_sub'])  || (!empty($message_data['is_sync_sub']) && $message_data['is_sync_sub'] == MessageModel::IS_SYNC_SUB_NO) ? MessageModel::IS_SYNC_SUB_NO : MessageModel::IS_SYNC_SUB_YES,
                'read_duration'        => empty($message_data['read_duration']) ? 0 : intval($message_data['read_duration']),
            ];
            if(!empty($this->hire_type_list)){
                $message_param['hire_type_list'] = json_encode($this->hire_type_list);
            }
            //发送职位
            if(!empty($this->job_title_list)){
                $message_param['job_title_list'] = json_encode($this->job_title_list);
            }
            //发送网点类型
            if(!empty($this->store_category_list)){
                $message_param['store_category_list'] = json_encode($this->store_category_list);
            }
            //雇佣日期
            if(!empty($this->hire_date_list)){
                $message_param['hire_date_list'] = json_encode($this->hire_date_list);
            }

            //合同公司
            if(!empty($this->contract_company_ids)){
                $message_param['contract_company_ids'] = json_encode($this->contract_company_ids);
            }

            //职位性质
            if(!empty($this->position_types)){
                $message_param['position_types'] = json_encode($this->position_types);
            }

            $this->logger->write_log('createMessage, 消息内容 - '.json_encode($message_param, JSON_UNESCAPED_UNICODE),
                'info');

            // 开启事务
            $db = $this->getDI()->get('db_backyard');
            $db->begin();

            // 向message表插入数据
            $message_model = new MessageModel();
            if ($message_model->create($message_param) === false) {
                $create_log = '';
                foreach ($message_model->getMessages() as $log) {
                    $create_log .= ', '.$log;
                }
                throw new \Exception($create_log);
            }

            //[1] 拿到最新插入的mesageId
            $last_message_id = $message_model->id;

            if (!empty($message_data['oss_id']) && (($message_data['category'] == MessageEnums::CATEGORY_GENERAL && $message_data['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE)
                    || ($message_data['category'] == MessageEnums::CATEGORY_SIGN && $message_data['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE)
                    || ($message_data['category'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE && $message_data['category_code'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE_CODE_TEMPLATE)
                )
            ) {
                //[2] 更新 message_oss_file 表
                $updateOssFileRet = (new MessageOssFileService())->updateMessageOssFileById([
                    'remote_message_id' => $remote_message_id,
                    'message_id'        => $last_message_id,
                ], $message_data['oss_id'], (int)$message_data['add_userid']);
                if (!$updateOssFileRet) {
                    throw new \Exception("update message_oss_file error");
                }

                //[3] 更新 message_identity_detail 表
                $identityDetailUpdate           = ["remote_message_id" => $remote_message_id];
                $updateMessageIdentityDetailRet = (new MessageIdentityDetailService())->updateMessageIdentityDetailByOssFileId(
                    $identityDetailUpdate,
                    $message_data['oss_id'],
                    (int)$message_data['add_userid']
                );
                if (!$updateMessageIdentityDetailRet) {
                    throw new \Exception("update message_identity_detail error");
                }
            }
            $db->commit();
            $this->logger->write_log('createMessage, 写入成功', 'info');
            return $last_message_id;
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log("createMessage, 消息创建失败, 原因可能是: ".$e->getMessage());
        }
        return false;
    }

    protected function getReceiveStaffBuilder(){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.staff_info_id as id',
            's.node_department_id as department_id',
        ]);
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->where('s.formal in ({formal:array}) AND s.state in ({state:array}) AND s.is_sub_staff = 0', ['formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN], 'state' => [Enums::HRIS_WORKING_STATE_1, Enums::HRIS_WORKING_STATE_3]]);
        //雇佣类型
        if (!empty($this->hire_type_list)) {
            $builder->inWhere('s.hire_type', array_values($this->hire_type_list));
        }
        if (!empty($this->job_title_list)) {
            $builder->inWhere('s.job_title', array_values($this->job_title_list));
        }
        if (!empty($this->hire_date_list)) {
            $builder->andWhere('s.hire_date between :start_date: and :end_date:',
                ['start_date' => $this->hire_date_list[0], 'end_date' => $this->hire_date_list[1]]);
        }
        if (!empty($this->store_category_list)) {
            $store_category = array_values(array_diff($this->store_category_list, [GlobalEnums::HEAD_OFFICE_ID]));
            $bind           = [];
            $sql            = [];
            if (!empty($store_category)) {
                $builder->leftJoin(SysStoreModel::class, 's.sys_store_id = store.id', 'store');
                $bind['category'] = $store_category;
                $sql[]            = 'store.category in ({category:array})';
            } elseif (in_array(GlobalEnums::HEAD_OFFICE_ID, $this->store_category_list)) {
                $sql[]                  = "s.sys_store_id = :head_office_id:";
                $bind['head_office_id'] = strval(GlobalEnums::HEAD_OFFICE_ID);
            }
            if (!empty($bind)) {
                $builder->andWhere(implode(' OR ', $sql), $bind);
            }
        }
        return $builder;
    }


    public function get_receive_staff(int $send_type, string $to_group = '', array $staff_info = [])
    {
        if (empty($send_type)) {
            return [];
        }

        $builder = $this->getReceiveStaffBuilder();

        // 发送类型 非 所有人, 该参数不可为空
        $to_group = explode(',', $to_group);
        if ($send_type != 1 && empty($to_group)) {
            return false;
        }
        $deptIds = $this->getNotReceiveDepartmentId();
        // 发送指定员工
        if ($send_type == 4) {
            $setting = (new SettingEnvService())->getSetVal('msg_not_audit_staff_ids', ',');
            if (empty($staff_info) || !in_array($staff_info['id'], $setting)) {
                $builder->notInWhere('s.node_department_id', $deptIds);
            }
            $builder->inWhere('s.staff_info_id', $to_group);
        }

        // 发送指定职位
        if ($send_type == 7) {
            $builder->notInWhere('s.node_department_id', $deptIds);
//            $builder->inWhere('s.job_title', $to_group);
            $relationInfo = (new DepartmentService())->getJobIdsDepartmentRelationById($to_group);
            $whereString = [];
            foreach ($relationInfo as $oneRelation) {
                $whereString[] = '( s.job_title = ' . $oneRelation['job_id'] . ' and s.node_department_id = ' . $oneRelation['department_id'] . ')';
            }
            if(empty($whereString)) {
                return false;
            }
            $where = implode(' OR ', $whereString);
            $this->logger->write_log('job_title_where:' . $where, 'info');
            $builder->andWhere($where);
        }

        return $builder->getQuery()->execute()->toArray();
    }

    public function get_receive_tool_staff(string $to_group = '')
    {
        // 发送类型 非 所有人, 该参数不可为空
        $to_group = explode(',', $to_group);
        if (empty($to_group)) {
            return false;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.staff_info_id as id,state',
        ]);
        $builder->from(['s' => ToolStaffInfoModel::class]);
        $builder->inWhere('s.staff_info_id', $to_group);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取不接受消息的部门id
     * @return array
     */
    public function getNotReceiveDepartmentId()
    {
        if(MessageEnums::MESSAGE_NOT_DISPLAY_COMPANY != 0) {
            $parentIds = explode(',', MessageEnums::MESSAGE_NOT_DISPLAY_COMPANY);
            $deptIds = [];
            foreach ($parentIds as $id) {
                $childDeptIds = (new SysDepartmentService())->getChildrenListByDepartmentId($id, true);
                $childDeptIds[] = $id;
                $deptIds = array_merge($deptIds, $childDeptIds);
            }
        }
        return !empty($deptIds) ? $deptIds : [0];
    }

    /**
     * 验证当前用户权限类型
     * 说明：受限是指发送消息/消息接收人的权限不同
     * @param array $staff_info
     * @return mixed
     */
    public function getStaffPermissionType(array $staff_info)
    {
        $permission_type = '';
        if (empty($staff_info)) {
            return $permission_type;
        }

        $staff_position = explode(',', $staff_info['position_category']);

        if(in_array($staff_info['id'],explode(',',env('sa_id')))){
            return 'un_limit';
        }

        // 超管
        if (in_array(MessageEnums::SUPER_MASTER_POSITION_ID, $staff_position) || empty($staff_info['department_id'])) {
            return 'un_limit';
        }

        // [1] 获取员工的部门链
        $staff_info['department_id'] = $staff_info['department_id'] ? $staff_info['department_id'] : 0;
        $staff_department_chain = (new DepartmentService())->getAncestryDepartmentByDepId($staff_info['department_id']);

        // [2] 获取不受限制的部门id
        $un_limit_first_level_department_ids = MessageEnums::UNLIMITED_FIRST_LEVEL_DEPARTMENT_IDS;

        // [3] 获取受限制的部门id
        $limit_first_level_department_ids = MessageEnums::LIMITED_FIRST_LEVEL_DEPARTMENT_IDS;

        foreach ($staff_department_chain as $dep_id) {
            if (in_array($dep_id, $un_limit_first_level_department_ids)) {
                $permission_type = 'un_limit';
                break;
            }

            if (in_array($dep_id, $limit_first_level_department_ids)) {
                $permission_type = 'limit';
                break;
            }
        }

        return $permission_type;
    }

    public function getMessagePageBtnPermissionOther($param = [], $staff_info = [])
    {
        $btn_permission = [
            // 发送给指定工号
            4  => true,

            // 知悉及同意按钮
            5  => true,

            // 发送指定职位
            7  => false,

            // 知悉按钮
            8  => true,

            //发送给指定组织或个人
            9  => true,

            // 按照模板发送,默认没有权限
            11 => false,

            // 问卷消息 是否展示 仅自己可见本条消息 按钮
            12 => false,
            //发送给工具号
            13 => false,
            //是否可以展示 阅读时长
            14 => false,
        ];

        // 读取setting_env表中配置 拥有按模板发送权限的员工id
        // 获取拥有 按模板发送 权限的人员id
        $permissionUserIds = (new SettingEnvService())->getHrisTemplateSendMessagePermission();
        // 有权限 展示 按模板发送 按钮
        // msgBtnPermission 接口传递额 param参数
        if ((!empty($param['msg_type']) && in_array($param['msg_type'],
                    ['common', 'sign','certificate'])) && is_array($permissionUserIds) && $staff_info && in_array($staff_info['id'],
                $permissionUserIds)) {
            $btn_permission[11] = true;
        }

        // msgAdd 方法 没有传给 $param 参数
        if (is_array($param) && empty($param) && is_array($permissionUserIds) && $staff_info && in_array($staff_info['id'],
                $permissionUserIds)) {
            $btn_permission[11] = true;
        }


        //问卷消息-是否展示-仅自己可见本条消息
        if(!empty($param['msg_type']) && $param['msg_type'] == 'questionnaire') {
            $staffsPersonPermission = $this->getSettingData(MessageEnums::MESSAGE_IS_PERSON_VISIBLE, 'array');
            if($staff_info && in_array($staff_info['id'], $staffsPersonPermission)) {
                $btn_permission[12] = true;
            }
        }
        $message_send_to_tool_staff = (new SettingEnvService())->getSetVal('message_send_to_tool_staff', ',');
        if(in_array($staff_info['id'], $message_send_to_tool_staff)) {
            $btn_permission[13] = true;
        }

        $readMinTimeStaff = (new SettingEnvService())->getSetVal('ReadminTime', ',');
        if(in_array($staff_info['id'], $readMinTimeStaff)) {
            $btn_permission[14] = true;
        }

        return $btn_permission;
    }

    /**
     * 获取消息页面 btn 权限
     * @param array $param 请求参数
     * @param array $staff_info
     * @param $check_type 0:展示使用 1:权限校验
     * @return mixed
     */
    public function getMessagePageBtnPermission($param = [], array $staff_info = [], $check_type =1)
    {
        $btn_permission = [
            // 发送给所有人
//            1 => false,

            // 发送给指定部门
//            2 => false,

            // 发送给指定大区
//            3 => false,

            // 发送给指定工号
            4  => false,

            // 知悉及同意按钮
            5  => false,

            // 发送指定角色
//            6 => false,

            // 发送指定职位
            7  => false,

            // 知悉按钮
            8  => false,

            //发送给指定组织或个人
            9  => false,

            //是否需要审批
            10 => false,

            // 按照模板发送
            11 => false,

            // 问卷消息 是否展示 仅自己可见本条消息 按钮
            12 => false,
            //发送给工具号
            13 => false,
            //是否可以展示 阅读时长
            14 => false,
        ];

        if (empty($staff_info)) {
            return $btn_permission;
        }

        $staff_position = explode(',', $staff_info['position_category']);

        // [1] 获取员工的部门链
        $staff_info['department_id'] = $staff_info['department_id'] ?: 0;
        $staff_department_chain = (new DepartmentService())->getAncestryDepartmentByDepId($staff_info['department_id']);

        // [2] 获取不受限制的部门id
        $un_limit_first_level_department_ids = MessageEnums::UNLIMITED_FIRST_LEVEL_DEPARTMENT_IDS;

        // [3] 获取受限制的部门id
        $limit_first_level_department_ids = MessageEnums::LIMITED_FIRST_LEVEL_DEPARTMENT_IDS;

        $permission_type = '';

        //按钮白名单员工
        $staffs_white_list = $this->getSettingData(MessageEnums::MESSAGE_SEND_BTN_WHITE_LIST, 'array');

        // 超管或白名单员工
        if (in_array(MessageEnums::SUPER_MASTER_POSITION_ID, $staff_position) || in_array( $staff_info['id'],$staffs_white_list)) {
            $permission_type = 'un_limit';
        } else {
            foreach ($staff_department_chain as $dep_id) {
                if (in_array($dep_id, $un_limit_first_level_department_ids)) {
                    $permission_type = 'un_limit';
                    break;
                }

                if (in_array($dep_id, $limit_first_level_department_ids)) {
                    $permission_type = 'limit';
                    break;
                }
            }
        }
        //给页面接口返回可见

        if (!empty($permission_type) || $check_type == 0) {
            foreach ($btn_permission as $k => $v) {
                $btn_permission[$k] = true;
            }

            // 角色btn现均已关闭
//            $btn_permission[6] = false;
//            $btn_permission[1] = false;
//            $btn_permission[2] = false;
//            $btn_permission[3] = false;

            if ($permission_type == 'limit') {
                // 发送所有人btn关闭
//                $btn_permission[1] = false;

                // 知悉及同意btn关闭
                $btn_permission[5] = false;
            }
            $btn_permission[12] = false;//仅自己可见本条消息。默认不展示
            $btn_permission[11] = false;//按模板发送。默认不展示
            $btn_permission[13] = false;//按工具号发送。默认不展示
            $btn_permission[14] = false;//阅读时长
        }

        //setting 获取按钮权限的人单独设置 发送走审批的
        $by_set = SettingEnvModel::findFirst(" code = 'send_message_no_audit_staff' ");
        if(!empty($by_set) && !empty($staff_info['id'])){
            $settings = $by_set->toArray();
            if( in_array($staff_info['id'],explode(',',$settings['set_val']))){
                $btn_permission[10] = true;
            }
        }

        // 读取setting_env表中配置 拥有按模板发送权限的员工id
        // 获取拥有 按模板发送 权限的人员id
        $permissionUserIds = (new SettingEnvService())->getHrisTemplateSendMessagePermission();
        if ((!empty($param['msg_type']) && !in_array($param['msg_type'], [
                    'common',
                    'sign',
                    'certificate',
                ])) || (is_bool($permissionUserIds) && !$permissionUserIds) || !in_array($staff_info['id'],
                $permissionUserIds)) {
            // 无权展示 按 模板 发送按钮
            $btn_permission[11] = false;
        }

        // 有权限 展示 按模板发送 按钮
        if ((!empty($param['msg_type']) && in_array($param['msg_type'],
                ['common', 'sign','certificate'])) && is_array($permissionUserIds) && in_array($staff_info['id'],
                $permissionUserIds)) {
            $btn_permission[11] = true;
        }

        //问卷消息-是否展示-仅自己可见本条消息
        if(!empty($param['msg_type']) && $param['msg_type'] == 'questionnaire') {
            $staffsPersonPermission = $this->getSettingData(MessageEnums::MESSAGE_IS_PERSON_VISIBLE, 'array');
            if(in_array($staff_info['id'], $staffsPersonPermission)) {
                $btn_permission[12] = true;
            }
        }
        $btn_permission[7] = false;

        $message_send_to_tool_staff = (new SettingEnvService())->getSetVal('message_send_to_tool_staff', ',');
        if(in_array($staff_info['id'], $message_send_to_tool_staff)) {
            $btn_permission[13] = true;
        }

        $readMinTimeStaff = (new SettingEnvService())->getSetVal('ReadminTime', ',');
        if(in_array($staff_info['id'], $readMinTimeStaff)) {
            $btn_permission[14] = true;
        }

        return $btn_permission;
    }

    /**
     * 获取配置信息
     * @param $setting_code
     * @param string $return_format
     * @return array|bool|string
     */
    public function getSettingData($setting_code, $return_format = 'string')
    {
        if (empty($setting_code)) {
            return false;
        }

        $setting_val = SettingEnvModel::get_val($setting_code);

        if ($return_format == 'string') {
            return $setting_val;
        }

        if ($return_format == 'array') {
            return explode(',', $setting_val);
        }

        return false;
    }

    /**
     * 当前工号是否有删除消息权限 - 普通消息/签字消息
     * @param int $staff_id
     * @return bool $result
     */
    public function checkRemoveMsgPermission(int $staff_id)
    {
        if (empty($staff_id)) {
            return false;
        }

        $permission_staff_ids = SettingEnvModel::get_val('delete_message_permission_ids');
        if (in_array($staff_id, explode(',', $permission_staff_ids))) {
            return true;
        }

        return false;
    }

    /**
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function cancel($params)
    {
        // [1] 获取消息
        $message_model = MessageModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
        ]);
        if (empty($message_model)) {
            throw new ValidationException('message not exist');
        }
        //非待审核的不能撤销了不存在
        if ($message_model->audit_status != 1) {
            throw new ValidationException('message not wait audit');
        }

        $loginUserInfo = $params['user_info'];
        $info          = $message_model->toArray();
        // 1 仅发送人和审批人可见 0 全部可见
        if (MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL == $info['visible'] && !empty($loginUserInfo['id'])) {
            // 只有普通消息、签字消息 当中 按模板发送类型 才会走该验证
//            if (($info['category'] == MessageEnums::CATEGORY_GENERAL && $info['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE) ||
//                (($info['category'] == MessageEnums::CATEGORY_SIGN && $info['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE))
//            ) {
            // 发送人
            $visible = [$info['add_userid']];
            // 审批人
            if (!empty($info['approver_id'])) {
                $visible = array_merge($visible, explode(',', $info['approver_id']));
            }

            if (!in_array($loginUserInfo['id'], $visible)) {
                // 仅发送人、审批人可见
                throw new ValidationException(self::$t->_('no_have_permission'));
            }
            //}
        }

        //rpc调用撤销审批 backyard
        $post_data = [
            'message_id'    => $params['id'],
            'cancel_reason' => $params['cancel_reason'],
            'add_userid'    => $params['user_info']['id'],
        ];

        $client = new ApiClient('by', '', 'message_audit_cancel');
        $client->setParams([$post_data]);
        $rpc_res = $client->execute();

        $this->logger->write_log("by_rpc: message_audit_cancel  request: ".json_encode($post_data,
                JSON_UNESCAPED_UNICODE)." rpc_res: ".json_encode($rpc_res, JSON_UNESCAPED_UNICODE), 'info');
        if (isset($rpc_res['code']) && $rpc_res['code'] == 1) {
            $this->logger->write_log("by_rpc: message_audit_cancel  [success]: message_id - ".$params['id'], 'info');
            $message_model->audit_status = 4;
            return $message_model->save();
        }
        throw new ValidationException('fail');
    }

    /**
     * 获取职位列表
     * @param array $user_info
     * @param string $job_name
     * @return array $list
     */
    public function getJobList(array $user_info, string $job_name = '')
    {
        $sender_permission_type = $this->getStaffPermissionType($user_info);

        $staff_bll = new DepartmentService();
        $job_ids = [];
        if(!empty($job_name)) {
            $jobInfo = $staff_bll->getJobList($job_name);
            if(!$jobInfo) {
                return [];
            }
            $job_ids = array_column($jobInfo, 'id');

        } else {
            $jobInfo = $staff_bll->getJobList();
        }
        $department_ids = [];
        if ($sender_permission_type == '') {
            return [];
        } else if ($sender_permission_type == 'limit') {
            // 获取直线垂直部门关联的职位id
            // [1] 提取员工的直线部门id
            $department_list = $staff_bll->getVerticalDepartmentListByDepartmentId($user_info['department_id']);
            $department_ids = array_column($department_list, 'id');
            $department_list = array_column($department_list, 'name', 'id');
        } else {
            $sysService = new SysService();
            $department_list = array_column($sysService->getDepartmentListFromCache(), 'name', 'id');

        }

        $this->logger->write_log("getJobList select where:" . json_encode($department_ids) . 'job_ids:' . json_encode($job_ids), 'info');
        // [2] 提取员工直线部门关联的职位id
        $relateInfo = $staff_bll->getJobIdsByDepartmentIds($department_ids, $job_ids);

        if (empty($relateInfo)) {
            return [];
        }
        $jobInfoId = array_column($jobInfo, 'name', 'id');

        foreach ($relateInfo as $key => $oneData) {
            $jobTitle = $jobInfoId[$oneData['job_id']] ?? '';
            $departmentName = $department_list[$oneData['department_id']] ?? '';
            $relateInfo[$key]['name'] = $jobTitle . '(' . $departmentName . ')' ;
        }

        return $relateInfo;
    }

    /**
     * 获取职位部门关联名称
     * @param $ids
     * @return array
     */
    public function getJobTitleName($ids)
    {
        $department_bll = new DepartmentService();
        $relateInfo = $department_bll->getJobIdsDepartmentRelationById($ids);

        if (empty($relateInfo)) {
            return [];
        }
        $department_list = array_column((new SysService())->getDepartmentListFromCache(), 'name', 'id');
        $jobInfo = $department_bll->getJobList();
        $jobInfoId = array_column($jobInfo, 'name', 'id');
        $job_name = [];
        foreach ($relateInfo as $key => $oneData) {
            $jobTitle = $jobInfoId[$oneData['job_id']] ?? '';
            $departmentName = $department_list[$oneData['department_id']] ?? '';
            $job_name[] = $jobTitle . '(' . $departmentName . ')' ;
        }

        return $job_name;
    }
    /**
     * 获取职位列表
     * @param array $user_info
     * @param string $job_name
     * @return array $list
     */
    public function getJobListOld(array $user_info, string $job_name = '')
    {
        $sender_permission_type = $this->getStaffPermissionType($user_info);

        $staff_bll = new DepartmentService();

        $job_ids = [];
        if ($sender_permission_type == '') {
            return [];
        } else if ($sender_permission_type == 'limit') {
            // 获取直线垂直部门关联的职位id
            // [1] 提取员工的直线部门id
            $department_list = $staff_bll->getVerticalDepartmentListByDepartmentId($user_info['department_id']);
            $department_ids = array_column($department_list, 'id');

            // [2] 提取员工直线部门关联的职位id
            $job_ids = $staff_bll->getJobIdsByDepartmentIds($department_ids);

            if (empty($job_ids)) {
                return [];
            }
        }

        return $staff_bll->getJobList($job_name, $job_ids);
    }

    /**
     * 操作 置顶 或 不置顶
     * @param $param
     * @return bool
     */
    public function top_msg($param)
    {
        $id                = $param['id'];
        $user_info         = $param['user_info'];
        $message_info      = $this->get_msg_info($id, $user_info);
        $top               = $message_info['top_status'];
        $remote_message_id = $message_info['remote_message_id'];

        // 已删除消息，不可在操作
        if (empty($message_info) || $message_info['isdel'] == 6) {
            return false;
        }

        // 校验权限: 待审批 和 已驳回的不可操作
        if (in_array($message_info['audit_status'], [1, 3])) {
            $this->logger->write_log("top_msg: id: {$message_info['id']}, audit_status: {$message_info['audit_status']}, 不可置顶",
                'info');

            return false;
        }

        $bi_db      = $this->getDI()->get('db_backyard');//bi数据库
        $message_db = $this->getDI()->get('db_message'); //coupon数据库

        // 开启事务
        $bi_db->begin();
        $message_db->begin();

        try {
            // 取消置顶操作的默认值
            $up_top        = 3;
            $up_coupon_top = 0;

            // 置顶操作
            if ($top != 1) {
                $up_top = $up_coupon_top = 1;//置顶默认值

                // 原逻辑：取出来 置顶最新的3条,   这3条 以外的 全部 改为不置顶 [SQL: top_state = 0 where not in {3}]
                // 新逻辑：取出置顶消息最新 前10 条：前3条 -> 置顶状态不变；剩余的7条 -> 更新为未置顶状态；[SQL: top_state = 0 where in {7}]
                // 说明：实际置顶数据有 4 条，此处 控制取10条，是担心数据出坏，造成全表扫描
                $top_sql = "SELECT id,remote_message_id,top_status FROM message WHERE top_status = 1 ORDER BY updated_at DESC LIMIT 10;";
                $top_id  = $this->getDI()->get('db_rby')->query($top_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (count($top_id) > 3) {
                    $top_id = array_slice($top_id, 3);

                    $bi_top     = array_column($top_id, 'id');
                    $coupon_top = array_column($top_id, 'remote_message_id');

                    $bi_top_ids     = implode(',', $bi_top);
                    $coupon_top_ids = "'".implode("','", $coupon_top)."'";

                    $bi_top_sql = "UPDATE message SET top_status = 3, updated_at = updated_at  WHERE id  IN ({$bi_top_ids})";
                    $bi_db->execute($bi_top_sql);

                    $coupon_top_sql = "UPDATE message_courier SET top_state = 0, updated_at = updated_at  WHERE message_content_id IN ({$coupon_top_ids})";
                    $message_db->execute($coupon_top_sql);
                }
            }

            // 更新两个数据库 置顶状态
            $message_db_message_courier_sql = "update message_courier set top_state = {$up_coupon_top}
                                          where message_content_id = '{$remote_message_id}'";
            $message_db->execute($message_db_message_courier_sql);

            $bi_sql = "update  message set top_status = {$up_top} ,last_edit_userid = {$user_info['id']}
                        where id = {$id}";
            $bi_db->execute($bi_sql);

            $bi_db->commit();
            $message_db->commit();
            $this->logger->write_log('置顶消息：'.$id.'_'.$top, 'info');
            return true;
        } catch (\Exception $e) {
            $bi_db->rollback();
            $message_db->rollback();
            $this->logger->write_log('置顶消息:'.$e->getMessage());
            return false;
        }
    }

    /**
     * bi message 详情
     * @param int $id
     * @return mixed
     */
    public function get_msg_info(int $id, $loginUserInfo = [])
    {
        if (empty($id)) {
            return [];
        }

        $info = MessageModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
            'columns'    => [
                'id',
                'title',
                'content',
                'send_type',
                'staff_info_ids',
                'to_group',
                'top_status',
                'category',
                'category_code',
                'remote_message_id',
                'questionnaire_lib_id',
                'feedback_setting',
                'audit_status',
                'set_time',
                'end_time',
                'isdel',
                'by_show_type',
                'publish_status',
                'visible',
                'approver_id',
                'add_userid',
                'hire_type_list',
                'job_title_list',
                'store_category_list',
                'hire_date_list',
                'extend',
                'contract_company_ids',
                'position_types',
                'is_sync_sub',
                'read_duration',
            ],
        ]);

        $info = $info ? $info->toArray() : [];

        // 1 仅发送人和审批人可见 0 全部可见
        if (!empty($info) && MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL == $info['visible'] && !empty($loginUserInfo['id'])) {
            // 只有普通消息、签字消息 当中 按模板发送类型 才会走该验证
//            if (($info['category'] == MessageEnums::CATEGORY_GENERAL && $info['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE) ||
//                (($info['category'] == MessageEnums::CATEGORY_SIGN && $info['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE))
//            ) {
            // 发送人
            $visible = [$info['add_userid']];
            // 审批人
            if (!empty($info['approver_id'])) {
                $visible = array_merge($visible, explode(',', $info['approver_id']));
            }

            if (!in_array($loginUserInfo['id'], $visible)) {
                // 仅发送人、审批人可见
                throw new ValidationException(self::$t->_('no_have_permission'));
            }
            //}
        }

        //问卷消息类型，读取问卷库属性
        if (!empty($info['questionnaire_lib_id'])) {
            $qn_lib_info = QuestionnaireLibModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $info['questionnaire_lib_id']],
                'columns'    => ['id', 'qn_lib_title'],
            ]);

            $info['qn_lib_title'] = $qn_lib_info ? $qn_lib_info->qn_lib_title : '';
            if (!empty($info['end_time'])) {
                $info['end_time'] = date('Y-m-d H:i', strtotime($info['end_time']));
            }
        }

        // 获取消息审批日志 FROM backyard（老数据：无需获取审批流）
        $info['audit_stream'] = [];
        if (in_array($info['audit_status'], [1, 2, 3, 4, 5])) {
            $post_data = [
                'message_id' => $info['id'],
            ];

            $this->logger->write_log("by_rpc: get_message_audit_stream - start: post_data: ".json_encode($post_data,
                    JSON_UNESCAPED_UNICODE), 'info');

            $client = new ApiClient('by', '', 'get_message_audit_stream', self::$language);
            $client->setParams([$post_data]);
            $rpc_res = $client->execute();

            $this->logger->write_log("by_rpc: get_message_audit_stream - end: rpc_res: ".json_encode($rpc_res,
                    JSON_UNESCAPED_UNICODE), 'info');

            $info['audit_stream'] = $rpc_res['data']['stream'] ?? [];
        }
        // 去除反转义字符
        $info['title']   = stripcslashes($info['title']);
        $info['content'] = stripcslashes($info['content']);

        // 普通消息 签字消息 按摩板发送
        if (($info['category'] == MessageEnums::CATEGORY_GENERAL && $info['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE)
            || ($info['category'] == MessageEnums::CATEGORY_SIGN && $info['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE)
            || ($info['category'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE && $info['category_code'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE_CODE_TEMPLATE)
        ) {
            // 读取message_oss_file 表数据
            $ossFileRet     = (new MessageOssFileService())->getOssFileInfoByParams([
                "message_id" => (int)$info['id'],
            ], ["id"]);
            $info['oss_id'] = !empty($ossFileRet[0]['id']) ? (int)$ossFileRet[0]['id'] : 0;
        }

        $info['visible'] = intval($info['visible']);
        $info['is_create_self'] = $info['add_userid'] == $loginUserInfo['id'] ? 1 : 0;//是否是当前登录人创建的消息。

        if ($this->isArgeementSign($info)) {
            //获取pdf_url
            $agreementInfo = HrAgreementSignRecordModel::getInfoByStaffInfoId($info['staff_info_ids']);

            $info['pdf_url'] = $agreementInfo['pdf_url'] ?? '';
        }
        if (isCountry('MY') && $info['category'] == MessageEnums::CATEGORY_SIGN && $info['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_CONTRACT_EXPIRE){
            // 获取 pdf url
            $message_pdf_data = MessagePdf::getInfo($info['staff_info_ids'],MessagePdf::MODULE_CATEGORY_1);
            $info['pdf_url'] = $message_pdf_data['pdf_url'] ?? '';
        } elseif (isCountry() && $info['category'] == MessageEnums::CATEGORY_SIGN && $info['category_code'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT')) {
            //21322【TH|BY|消息】 外协仓管自动发送合同
            $sign_info = $this->getOsStaffContract($info['remote_message_id']);
            $info['pdf_url'] = $sign_info['pdf_url'] ?? '';
        }

        if (
            isCountry('MY')
            && $info['category'] == MessageEnums::CATEGORY_SIGN
            && in_array($info['category_code'],
                [
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_INDEPENDENT,
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT,
                    EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_PDPA'), //PDPA消息详情
                    EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_BEBAS'),
                ]
            )
        ) {
            // 获取 pdf url
            //获取消息id
            $messageCourierInfo = MessageCourierModel::findFirst(
                [
                    'columns'    => 'id',
                    'conditions' => 'message_content_id = :message_content_id:',
                    'bind'       => ['message_content_id' => $info['remote_message_id']],
                ]
            );
            if(empty($messageCourierInfo)) {
                throw new ValidationException('getMessageCourierInfo '. self::$t->_('data_error'));

            }
            //获取签名的pdf
            $message_pdf_data = MessagePdf::getInfoByMsgId($messageCourierInfo->id);
            $info['pdf_url'] = $message_pdf_data['pdf_url'] ?? '';
        }

        if(isCountry('MY') && $info['category'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT')) {
            $info['pdf_url'] = $info['content'] ?? '';
        }
        if (isCountry('PH') && $info['category'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT')){
            $messageCourierInfo = MessageCourierModel::findFirst(
                [
                    'columns'    => 'id',
                    'conditions' => 'message_content_id = :message_content_id:',
                    'bind'       => ['message_content_id' => $info['remote_message_id']],
                ]
            );
            if(empty($messageCourierInfo)) {
                throw new ValidationException('getMessageCourierInfo '. self::$t->_('data_error'));

            }
            $message_pdf_data = MessagePdf::getInfoByMsgId($messageCourierInfo->id);
            $info['pdf_url'] = !empty($message_pdf_data['pdf_url']) ? $message_pdf_data['pdf_url'] : '';
        }

        // 将匹配到的内容替换为其中的子内容
        $info['content'] = preg_replace('/^<div>(.*)<\\/div>$/s', '$1', $info['content']);
        //雇佣类型
        $info['hire_type_list_text']      = $this->getHireTypeListText($info['hire_type_list']);
        //发送职位
        $info['job_title_list_text']      = $this->getJobTitleListText($info['job_title_list']);
        //网点类型
        $info['store_category_list_text'] = $this->getStoreCategoryListText($info['store_category_list']);
        //入职日期
        $info['hire_date_list_text']      = $this->getHireDateListText($info['hire_date_list']);
        //合同公司
        $info['contract_company_ids_text']= $this->getContractCompanyIdsText($info['contract_company_ids']);
        //职位性质
        $info['position_types_text']      = $this->getPositionTypesText($info['position_types']);
        //扩展字段信息
        $extend = (array) json_decode($info['extend'],true);
        foreach ($extend as $column => $coLVal) {
            $info[$column] = $coLVal;
        }
        unset($info['extend']);

        return $info;
    }

    /**
     * 获取合同公司名称
     * @param $contract_company_ids
     * @return string
     */
    protected function getContractCompanyIdsText($contract_company_ids)
    {
        $contract_company_ids = isJson($contract_company_ids) ? json_decode($contract_company_ids, true) : $contract_company_ids;
        if (empty($contract_company_ids)) {
            return '';
        }

        $companyInfo = StaffPayrollCompanyInfoRepository::allData([], 'company_id,company_short_name');
        $companyInfoToId = array_column($companyInfo, 'company_short_name', 'company_id');
        $companyNames = [];
        foreach ($contract_company_ids as $oneId) {
            if(!isset($companyInfoToId[$oneId])) {
                continue;
            }
            $companyNames[] = $companyInfoToId[$oneId] ?? '';
        }
        return implode(',', $companyNames);
    }

    /**
     * 获取职位性质
     * @param $position_types
     * @return string
     */
    protected function getPositionTypesText($position_types)
    {
        $position_types = isJson($position_types) ? json_decode($position_types, true) : $position_types;
        if (empty($position_types)) {
            return '';
        }

        $position_types_text = [];
        foreach ($position_types as $value) {
            if(!isset(HrJobDepartmentRelationModel::$position_type_list[$value])) {
                continue;
            }
            $position_types_text[] = static::$t->_(HrJobDepartmentRelationModel::$position_type_list[$value]);
        }
        return implode(',', $position_types_text);
    }



    //发送职位
    protected function getJobTitleListText($job_title_list): string
    {
        $job_title_list = isJson($job_title_list) ? json_decode($job_title_list, true) : $job_title_list;
        if (empty($job_title_list)) {
            return '';
        }
        $jobTitle = (new HrJobTitleService())->getJobTitleByIds(array_values($job_title_list));
        return implode(',', array_column($jobTitle, 'job_name'));
    }
    //网点类型
    protected function getStoreCategoryListText($store_category_list): string
    {
        $store_category_list = isJson($store_category_list) ? json_decode($store_category_list,
            true) : $store_category_list;
        if (empty($store_category_list)) {
            return '';
        }
        $list = [];
        foreach ($this->getStoreCategory() as $store_category) {
            if (in_array($store_category['value'], $store_category_list)) {
                $list[] = $store_category['label'];
            }
        }
        return implode(',', $list);
    }
    //入职日期
    protected function getHireDateListText($hire_date_list): string
    {
        $hire_date_list = isJson($hire_date_list) ? json_decode($hire_date_list,
            true) : $hire_date_list;
        if (empty($hire_date_list)) {
            return '';
        }
        return $hire_date_list[0] . ' ~ ' . $hire_date_list[1];
    }


    protected function getHireTypeListText($hire_type_list)
    {
        $text = [];
        if (empty($hire_type_list)) {
            return '';
        }
        $hire_type_list = json_decode($hire_type_list, true);
        $hireTypeEnum   = (new SysService())->getHireTypeList();
        foreach ($hireTypeEnum as $item) {
            if (in_array($item['value'], $hire_type_list)) {
                $text[] = $item['label'];
            }
        }
        return implode(',', $text);
    }


    /**
     * 消息删除: 普通/签字 = 需验证权限; 问卷/答题
     * @param array $param
     *
     * @return bool $result
     */
    public function del_msg($param){
        $id = $param['id'];
        $user_info = $param['user_info'];
        $message_info = $this->get_msg_info($id);

        if (empty($message_info) || $message_info['isdel'] == 6) {
            return false;
        }

        // 校验权限
        // [1] 待审批 和 已驳回的不可删除
        if (in_array($message_info['audit_status'], [1, 3])) {
            $this->logger->write_log("del_msg: id: {$message_info['id']}, audit_status: {$message_info['audit_status']}, 不可删除", 'info');

            return false;
        }

        // [2] 审批通过, 不在指定人工号的, 不可删除
        if ($message_info['audit_status'] == 2 && $this->isEditPermission($user_info['id']) == false) {
            $this->logger->write_log("del_msg: id: {$message_info['id']}, audit_status: {$message_info['audit_status']}, user_id: {$user_info['id']}, 无删除权限", 'info');

            return false;
        }

        // 普通/签字删除, 需校验权限
        if (in_array($message_info['category'], [0, 33]) && !$this->checkRemoveMsgPermission($user_info['id'])) {
            return false;
        }

        $remote_message_id = $message_info['remote_message_id'];

        $bi_db = $this->getDI()->get('db_backyard');//bi数据库
        $message_db = $this->getDI()->get('db_message');//coupon数据库

        // 开启事务
        $bi_db->begin();
        $message_db->begin();
        try{
            // bi库的message表删除状态更新 消息主表
            $bi_sql = "UPDATE  message SET isdel = ?, last_edit_userid = ? WHERE id = ? LIMIT 1";
            $bi_db->execute($bi_sql, [6, $user_info['id'], $id]);

            // coupon库的message_courier表删除状态更新 消息投递表
            $query_msg_courier_sql = "SELECT id FROM message_courier WHERE message_content_id = :mcid AND is_del = :is_del LIMIT 1";
            $query_msg_courier_param = [
                'mcid' => $remote_message_id,
                'is_del' => 0,
            ];
            $exist_msg_courier = $message_db->query($query_msg_courier_sql, $query_msg_courier_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($exist_msg_courier)) {
                $message_db_message_courier_sql = "UPDATE message_courier SET is_del = ? WHERE message_content_id = ?";
                $message_db->execute($message_db_message_courier_sql, [1, $remote_message_id]);
            }

            $bi_db->commit();
            $message_db->commit();
            $this->logger->write_log("del_msg success：msg_id: $id, user_id: {$user_info['id']}", 'info');
            return true;
        } catch (\Exception $e) {
            $bi_db->rollback();
            $message_db->rollback();
            $this->logger->write_log('del_msg fail:'.$e->getMessage());
            return false;
        }
    }

    /**
     * 用户是否有编辑权限
     * @param int $user_id
     * @return bool
     */
    public function isEditPermission(int $user_id, $categoryInfo = [])
    {
        if (empty($user_id)) {
            return false;
        }

        $edit_staff_ids = $this->getSettingData(MessageEnums::EDIT_PERMISSION_SETTING_KEY, 'array');
        if (in_array($user_id, $edit_staff_ids)) {
            return true;
        }

        return false;
    }

    /**
     * 消息 api 列表
     * @param $param
     * @return array
     * @throws \Exception
     */
    public function get_msg_list($param)
    {
        //限制三个月数据 要去掉
//        $date_limit = date('Y-m-d H:i:s',strtotime('-3 month'));

        try {
            $page                = empty($param['page']) ? 1 : $param['page'];
            $page_size           = empty($param['length']) ? 100 : intval($param['length']);
            $param['search_msg'] = trim($param['search_msg']);

            $countBuilder = $this->getMsgListQuery('COUNT(*) AS count', $param);
            $count        = $countBuilder->getQuery()->getSingleResult()->toArray();
            $count        = !empty($count['count']) ? $count['count'] : 0;
            if ($count == 0) {
                return ['count' => 0, 'data' => []];
            }

            $columns     = "m.id
                    ,m.title
                    ,DATE_FORMAT(CONVERT_TZ(m.updated_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as updated_at
                    ,DATE_FORMAT(CONVERT_TZ(m.created_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as created_at
                    ,m.top_status
                    ,m.publish_status
                    ,m.audit_status
                    ,m.last_edit_userid
                    ,m.remote_message_id
                    ,m.set_time
                    ,m.end_time
                    ,m.isdel
                    ,m.staff_num
                    ,m.read_num as read_count
                    ,m.add_userid
                    ,m.category
                    ,m.category_code
                    ,m.visible
                    ,m.recipient_staff_info_id
                    ,m.approver_id
                    ,m.create_params                   
                    ,DATE_FORMAT(CONVERT_TZ(m.real_send_time, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as real_send_time";
            $listBuilder = $this->getMsgListQuery($columns, $param);
            $listBuilder->orderBy('m.top_status ASC , m.real_send_time DESC');
            $listBuilder->limit($page_size, $page_size * ($page - 1));
            $list = $listBuilder->getQuery()->execute()->toArray();

            // 是否有编辑权限
            $is_edit_permission = $this->isEditPermission($param['user_id']);

            $startStatisticsTime = '';
            //是否是问卷消息
            $statistics = false;
            if ($param['msg_type'] == 'qn') {
                $statistics = true;
                //开始统计问卷消息时间
                $startStatisticsTime = SettingEnvModel::get_val('start_statistics_time');
            }

            //数据组合
            foreach ($list as $key => $value) {
                if ($statistics) {
                    //是否展示统计按钮
                    $list[$key]['is_statistics'] = $value['audit_status'] == 2 && $value['publish_status'] == 6 ? true : false;
                    //是否是开始统计问卷消息时间后的消息，是：新接口，否老接口
                    $list[$key]['is_new'] = empty($startStatisticsTime) ? false : $value['created_at'] > $startStatisticsTime;
                }

                $list[$key]['unread_count'] = $value['staff_num'] < $value['read_count'] ? 0 : $value['staff_num'] -
                    $value['read_count'];
                if (!empty($value['set_time'])) {
                    $list[$key]['created_at'] = $value['set_time'];
                }

                //发布时间
                $list[$key]['updated_at'] = $this->getPublishTime($value);

                //问卷消息类型新增的截止时间
                if (!empty($value['end_time'])) {
                    $value['end_time']      = date('Y-m-d H:i', strtotime($value['end_time']));
                    $list[$key]['end_time'] = $value['end_time'];
                }

                //发布状态 publish_status = -1 已结束
                if (!empty($value['end_time']) && $value['end_time'] <= gmdate("Y-m-d H:i:s",
                        time() + ($this->timeOffset) * 3600)) {
                    $list[$key]['publish_status'] = -1;
                }

                //删除状态与发布状态合并 publish_status = -1 已删除
                if ($value['isdel'] == 6) {
                    $list[$key]['publish_status'] = -3;
                }

                // 审批通过，是否展示编辑按钮
                $is_edit = false;
                if ($value['audit_status'] == 2 && $is_edit_permission && $value['isdel'] != 6) {
                    $is_edit = true;
                }

                $list[$key]['is_edit']       = $is_edit;
                //新员工再次发送
                $list[$key]['is_once_again'] = $this->isOnceAgainSend($value);

                unset($list[$key]['isdel']);
                //撤销按钮
                $list[$key]['is_show_cancel_btn'] = $value['audit_status'] == 1 && $value['publish_status'] == 9;
            }
            $return['count'] = $count;
            $return['data']  = $list;
            return $return;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 是否可以再次发送
     * create_params 不为空
     * 消息类型：普通消息，签字消息
     * 发布状态：已发布
     * 审批状态：同意
     * @param $data
     * @return bool
     */
    public function isOnceAgainSend($data)
    {
        $is_once_again = false;
        if (!empty($data['create_params'])
            && in_array($data['category'], [MessageEnums::CATEGORY_GENERAL, MessageEnums::CATEGORY_SIGN,])
            && $data['publish_status'] == MessageEnums::PUBLISH_YES
            && $data['audit_status']   == MessageEnums::APPROVAL_STATUS_APPROVAL) {
            $is_once_again = true;
        }

        return $is_once_again;
    }

    /**
     * 获取发布时间
     * @param $value
     * @return mixed
     */
    public function getPublishTime($value)
    {
        return $value['real_send_time'];
    }

    public function loginUserCanView($messageInfo, $loginUserId): bool
    {
//        if (($messageInfo['category'] == MessageEnums::CATEGORY_GENERAL && $messageInfo['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE) ||
//            ($messageInfo['category'] == MessageEnums::CATEGORY_SIGN && $messageInfo['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE)
//        ) {
        if ($messageInfo['visible'] == MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL) {
            $viewStaffInfoIds = [$messageInfo['add_userid']];
            $approvers        = $messageInfo['approver_id'] ? explode(',',
                $messageInfo['approver_id']) : [];
            $viewStaffInfoIds = !empty($approvers) ? array_merge($viewStaffInfoIds, $approvers) : $viewStaffInfoIds;
            if (!in_array($loginUserId, $viewStaffInfoIds)) {
                return false;
            }
        }
        //}
        return true;
    }

    /**
     * @param $param array 参数列表
     * @return bool
     */
    public function msgCanView($param)
    {
        $messageInfo = $this->getMessageByParam([
            'id' => $param['id'],
        ], ['id', 'add_userid', 'category_code', 'category', 'visible', 'approver_id']);
        if (!empty($messageInfo)) {
            return $this->loginUserCanView($messageInfo[0], $param['user_info']['id']);
        }
        return true;
    }

    /**
     * @param $params array 查询参数
     * @param $colum array 查询字段
     * @return false 返回值
     */
    public function getMessageByParam($params = [], $colum = [])
    {
        if (!is_array($params) || empty($params)) {
            return false;
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['m' => MessageModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 消息列表query
     * @param $columns
     * @param $params
     * @return mixed
     */
    public function getMsgListQuery($columns ,$params)
    {

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['m' => MessageModel::class]);
        $builder = $this->getMsgListBuilderWhere($builder, $params);
        return $builder;
    }

    /**
     * 消息列表WHERE
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getMsgListBuilderWhere($builder, $params)
    {
        //消息类型条件
        $msg_type = isset($params['msg_type']) ? $params['msg_type'] : '';
        switch ($msg_type) {
            // 问卷消息 qn = questionnaire
            case 'qn':
                $builder->andWhere('m.category = 6 and ((m.add_userid = :user_id: and m.visible = :visible_true: ) or (m.visible = :visible_false:))',
                    [
                        'user_id'       => $params['user_id'],
                        'visible_true'  => MessageModel::MESSAGE_VISIBLE_SEND_APPROVAL,
                        'visible_false' => MessageModel::MESSAGE_VISIBLE_SEND_NO_APPROVAL,
                    ]);
                break;
            // 签字消息
            case 'sign':
                $builder->andWhere("m.category = 33 and ((category_code = 0 and m.title not LIKE :not_show:) OR category_code  in ({category_code:array}))  ",
                    ['not_show' => 'ยินดีด้วยคุณคือผู้โชคดี กิจกรรม%', 'category_code' => [4,MessageEnums::CATEGORY_SIGN_CODE_CONTRACT_EXPIRE]]);
                break;
            // 答题消息
            case 'answer':
                $builder->andWhere('m.category = 34');
                break;
            // 证书消息
            case 'certificate':
                $builder->andWhere('m.category = :category:',['category' => MessageEnums::MESSAGE_CATEGORY_CERTIFICATE]);
                break;
            //系统消息
            case 'sys':
                $builder->andWhere('m.category =' . MessageEnums::CATEGORY_SIGN . ' and category_code =' . EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT'));
                break;
            // 普通消息
            default:
                $builder->andWhere('m.category = 0 and category_code IN (0,6)');
                break;
        }


        if(!empty($params['send_start'])){
            $start_time = gmdate('Y-m-d H:i:s',strtotime($params['send_start']));
            $builder->andWhere('m.again_time >= :send_start:', ['send_start' => $start_time]);

        }
        if(!empty($params['send_end'])){
            $end_time = gmdate('Y-m-d H:i:s',strtotime($params['send_end'])+86400);
            $builder->andWhere('m.again_time < :send_end:', ['send_end' => $end_time]);
        }

        if(!empty($params['search_msg'])){
            $builder->andWhere('m.title LIKE :title:', ['title' => "%{$params['search_msg']}%"]);
        }

        //接收人
        if(!empty($params['recipient_staff_info_id'])){
            $builder->andWhere('m.recipient_staff_info_id = :recipient_staff_info_id:', ['recipient_staff_info_id' => $params['recipient_staff_info_id']]);
        }

        $builder = $this->createPublicWhere($builder, $params);

        return $builder;
    }

    /**
     * 构建公共查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function createPublicWhere($builder, $params)
    {
        $over_time = date('Y-m-d H:i:s', time());
        //发布状态
        if (!empty($params['publish_status'])) {
            if ($params['publish_status'] == MessageEnums::PUBLISH_NO && $params['msg_type'] == 'qn') {
                $builder->andWhere('m.publish_status = :publish_status: and isdel = :isdel: and (m.end_time is NULL or m.end_time > :over_time:)',
                    [
                        'publish_status' => $params['publish_status'],
                        'isdel'          => MessageEnums::MESSAGE_NOT_DELETED,
                        'over_time'      => $over_time,
                    ]);
            } elseif ($params['publish_status'] == MessageEnums::PUBLISH_NO && $params['msg_type'] != 'qn') {
                $builder->andWhere('m.publish_status = :publish_status: and isdel = :isdel:',
                    ['publish_status' => $params['publish_status'], 'isdel' => MessageEnums::MESSAGE_NOT_DELETED]);
            } elseif ($params['publish_status'] == MessageEnums::PUBLISH_YES && $params['msg_type'] == 'qn') {
                $builder->andWhere('m.publish_status = :publish_status: and isdel = :isdel: and (m.end_time is NULL or m.end_time > :over_time:)',
                    [
                        'publish_status' => $params['publish_status'],
                        'isdel'          => MessageEnums::MESSAGE_NOT_DELETED,
                        'over_time'      => $over_time,
                    ]);
            } elseif ($params['publish_status'] == MessageEnums::PUBLISH_YES && $params['msg_type'] != 'qn') {
                $builder->andWhere('m.publish_status = :publish_status: and isdel = :isdel:',
                    ['publish_status' => $params['publish_status'], 'isdel' => MessageEnums::MESSAGE_NOT_DELETED]);
            } elseif ($params['publish_status'] == MessageEnums::PUBLISH_DELETE) {
                $builder->andWhere('m.isdel = :isdel:', ['isdel' => MessageEnums::MESSAGE_DELETED]);
            } elseif ($params['publish_status'] == MessageEnums::PUBLISH_END) {
                $builder->andWhere('m.end_time is not NULL AND m.end_time < :over_time: and isdel = :isdel:',
                    ['over_time' => date('Y-m-d H:i:s', time()), 'isdel' => MessageEnums::MESSAGE_NOT_DELETED]);
            }
        }

        //审批状态
        if(!empty($params['audit_status'])) {
            $builder->andWhere('m.audit_status in ({audit_status:array})', ['audit_status' => $params['audit_status']]);
        }

        return $builder;
    }

    /**
     * 编辑消息 只能修改 标题和内容 和是否置顶
     * @param $param
     * @return bool
     */
    public function edit_msg($param){

        $bi_db = $this->getDI()->get('db_backyard');//bi数据库
        $message_db = $this->getDI()->get('db_message');//coupon数据库
        $id = intval($param['id']);
        $title = $param['title'];
        $content = $param['content'];
        $top = $param['top'];
        $visible = $param['visible'] ?? 0;

//            $message_content = preg_replace('/<div style=\'font-size: 40px\'>(.*)<\/div>/','$1',$content);
//            $message_content = addslashes("<div style='font-size: 40px'>".$message_content."</div>");
        $message_content = $content;
        $remote_message_content = $content;
        $user_info = $param['user_info'];
        $remote_topstate = ($top == 3) ? 0 : 1; //coupon 置顶 只有 0和1
        $update_time = gmdate("y-m-d H:i:s",time());
        $message_info = $this->get_msg_info($id);
        $remote_message_id = $message_info['remote_message_id'];

        // 数据为空 或 已删除的不可编辑
        if (empty($message_info) || $message_info['isdel'] == 6) {
            return false;
        }

        //问卷消息-仅自己创建的消息，才可以变更 “仅自己可见本条消息” 的勾选状态
        if($message_info['category'] == MessageEnums::CATEGORY_QUES && $message_info['category_code'] == MessageEnums::CATEGORY_QUES_CODE) {
            if($visible != $message_info['visible'] && $message_info['add_userid'] != $user_info['id']) {
                throw new ValidationException(self::$t->_('no_self_visible_edit'));
            }
        } else {
            $visible = $message_info['visible'];
        }


        try{
            // 校验权限
            // [1] 待审批 和 已驳回的不可编辑
            if (in_array($message_info['audit_status'], [1, 3])) {
                $this->logger->write_log("edit_msg: id: {$message_info['id']}, audit_status: {$message_info['audit_status']}, 不可编辑", 'info');

                return false;
            }

            //检验是否有编辑权限
            if(!$this->validationEditPermission($message_info, $user_info)) return false;

            // 问卷类型消息，处理逻辑
            if (!empty($message_info['questionnaire_lib_id'])) {
                // 远端消息内容为自定义模板
                $remote_message_content = $this->make_qn_msg_remote_tpl($remote_message_id);

                // message 内容为提交内容
                $message_content = $content;
            }

            // 签字消息：内容不允许变更，仍为表中原数据
            if ($message_info['category'] == 33) {
                $message_content = $remote_message_content = $message_info['content'];
            }

            // 置顶规则  最多4条
            $bi_top_ids = $coupon_top_ids = '';
            if ($top == 1) {
                // 原逻辑: 取出来 最新的3条, 3 条外的 改为不置顶, LIMIT 3  [SQL: top_state = 0 where not in {3}]
                // 新逻辑：取出置顶消息最新 前10 条：前3条 -> 置顶状态不变；剩余的7条 -> 更新为未置顶状态；[SQL: top_state = 0 where in {7}]
                // 说明：实际置顶数据有 4 条，此处 控制取10条，是担心数据出坏，造成全表扫描
                $top_sql = "SELECT id,remote_message_id,top_status FROM message WHERE top_status = 1 ORDER BY updated_at DESC LIMIT 10";
                $top_id = $this->getDI()->get('db_rby')->query($top_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (count($top_id) > 3) {
                    $top_id = array_slice($top_id, 3);

                    $bi_top = array_column($top_id,'id');
                    $coupon_top = array_column($top_id,'remote_message_id');

                    $bi_top_ids = implode(',', $bi_top);
                    $coupon_top_ids = "'" . implode("','", $coupon_top) . "'";
                }
            }

            // 编辑前数据
            $this->logger->write_log("edit_msg: id: {$message_info['id']}, user_id: {$user_info['id']}, 编辑前数据: " . json_encode($message_info, JSON_UNESCAPED_UNICODE), 'info');


            // 开启事务 更新 coupon库 message相关两张表 和 bi message
            $bi_db->begin();
            $message_db->begin();

            $message_db_message_content_sql = "
                -- 更新远端消息内容
                UPDATE 
                    message_content 
                SET 
                    message = ? 
                WHERE 
                    id = ?
            ";
            $message_db_message_content_param = [
                $remote_message_content,
                $remote_message_id,
            ];
            $message_db->execute($message_db_message_content_sql, $message_db_message_content_param);

            $message_db_message_courier_sql = "
                -- 更新远端消息基本信息
                UPDATE 
                    message_courier 
                SET 
                    title = ?,
                    top_state = ?, 
                    updated_at = ?, 
                    update_state = ? 
                WHERE 
                    message_content_id = ?
            ";
            $message_db_message_courier_param = [
                $title,
                $remote_topstate,
                $update_time,
                1,
                $remote_message_id,
            ];
            $message_db->execute($message_db_message_courier_sql, $message_db_message_courier_param);

            $bi_sql = "
                -- BI message 信息更新
                UPDATE  
                    message 
                SET 
                    title = ?, 
                    content = ?,
                    last_edit_userid = ?,
                    top_status = ?,
                    visible = ?
                WHERE 
                    id = ?
            ";
            $bi_param = [
                $title,
                $message_content,
                $user_info['id'],
                $top,
                $visible,
                $id,
            ];
            $bi_db->execute($bi_sql, $bi_param);


            //如果置顶 最多四条消息
            if($top == 1 && !empty($bi_top_ids) && !empty($coupon_top_ids)){
                $bi_top_sql = "UPDATE message SET top_status = 3, updated_at = updated_at  WHERE id IN ({$bi_top_ids})";
                $bi_db->execute($bi_top_sql);

                $coupon_top_sql = "UPDATE message_courier SET top_state = 0, updated_at = updated_at  WHERE message_content_id IN ({$coupon_top_ids})";
                $message_db->execute($coupon_top_sql);
            }

            $bi_db->commit();
            $message_db->commit();

            // 编辑后数据
            $this->getDI()->get('logger')->write_log("edit_msg: id: {$message_info['id']}, user_id: {$user_info['id']}, 编辑后数据: " . json_encode($bi_param, JSON_UNESCAPED_UNICODE), 'info');

            return true;

        } catch (\Exception $e) {
            $bi_db->rollback();
            $message_db->rollback();
            $this->getDI()->get('logger')->write_log('edit_msg:'.$e->getMessage());
            return false;
        }
    }

    /**
     * 检验是否有编辑权限
     * @param $message_info
     * @param $user_info
     * @return bool
     */
    public function validationEditPermission($message_info, $user_info)
    {
        $result = true;
        // [2] 审批通过, 不在指定人工号的, 不可编辑
        if ($message_info['audit_status'] == 2 && $this->isEditPermission($user_info['id']) == false) {
            $this->logger->write_log("edit_msg: id: {$message_info['id']}, audit_status: {$message_info['audit_status']}, user_id: {$user_info['id']}, 无编辑权限", 'info');
            $result = false;
        }
        return $result;
    }
    /**
     * 生成问卷消息在backyard的详情模板
     * @param $remote_msg_id  int 远端消息ID
     * @return $tpl string 模板内容
     *
     */
    public function make_qn_msg_remote_tpl($remote_msg_id = 0){
        // 模板跳转内容, 改为 backyard端输出时, 自行处理 2020.01
        return '';

        $questionnaire_msg_url = "BACKYARD_FRONT_HOST?msg_id=$remote_msg_id";
        $tpl = <<<EOF
        <meta name=viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" /><div style="postion:fixed;left:0;top:0;width:100%;height:100%"><iframe src='{$questionnaire_msg_url}' width='100%' height='100%' scrolling='no' frameborder="0"></iframe></div>
EOF;

        return trim($tpl);
    }

    public function makeSignMsgPdf($param, $genPdfChannel = 'mpdf')
    {
        $log = '签字消息: 生成员工签字pdf. ';
        $log .= "[remote_message_id = {$param['remote_message_id']}, staff_info_id = {$param['staff_info_id']}, gen_pdf_channel = $genPdfChannel], ";

        try {
            // 验证工号格式
            if (!is_staff_info_id($param['staff_info_id'])) {
                throw new ValidationException(self::$t->_('sign_msg_021'));
            }

            $message = $this->get_msg_by_remote_id($param['remote_message_id']);

            if(isCountry('MY') && $message['category'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT')) {
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $param['staff_info_id'].'.pdf';
                $result['data']['file_size'] = get_headers($message['content'], true)['Content-Length'];
                $result['data']['file_url']  = $message['content'];
                return $result;
            } elseif (isCountry() && $message['category'] == MessageEnums::CATEGORY_SIGN && $message['category_code'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT')) {
                //21322【TH|BY|消息】 外协仓管自动发送合同，签字后会更新签字后pdf，未更新意味着没签字
                $sign_info = $this->getOsStaffContract($message['remote_message_id']);
                if (!$sign_info || $sign_info['staff_info_id'] != $param['staff_info_id']) {
                    throw new ValidationException(self::$t->_('sign_msg_020'));
                }
                if ($sign_info['state'] == OsStaffContractSignRecordModel::STATE_UNSIGNED) {
                    throw new ValidationException(self::$t->_('sign_msg_037'));
                }
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $param['staff_info_id'] . '.pdf';
                $result['data']['file_size'] = get_headers($sign_info['sign_pdf_url'], true)['Content-Length'];
                $result['data']['file_url']  = $sign_info['sign_pdf_url'];
                return $result;
            }elseif (isCountry('PH') && $message['category'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT')){
                $staff_msg = $this->get_staff_msg_info($param['staff_info_id'], $param['remote_message_id']);
                if (empty($staff_msg)) {
                    throw new ValidationException(self::$t->_('sign_msg_020'));
                }
                $sign_info = MessagePdf::findFirst([
                    'conditions' => 'msg_id = :msg_id:',
                    'bind' => ['msg_id' => $staff_msg['id']],
                ]);
                $sign_info = $sign_info ? $sign_info->toArray() : [];
                if (!$sign_info || $sign_info['staff_info_id'] != $param['staff_info_id']) {
                    throw new ValidationException(self::$t->_('sign_msg_020'));
                }
                if (empty($sign_info['sign_url'])) {
                    throw new ValidationException(self::$t->_('sign_msg_037'));
                }
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $param['staff_info_id'] . '.pdf';
                $result['data']['file_size'] = get_headers($sign_info['sign_url'], true)['Content-Length'];
                $result['data']['file_url']  = $sign_info['sign_url'];
                return $result;
            }

            $db = $this->getDI()->get('db_rby');

            // 获取员工签字结果
            $sign_result_sql   = "SELECT id, sign_url, sign_pdf_url, created_at FROM sign_msg_result 
                            WHERE staff_info_id = :staff_info_id AND remote_message_id = :remote_message_id";
            $sign_result_param = [
                'staff_info_id'     => $param['staff_info_id'],
                'remote_message_id' => $param['remote_message_id'],
            ];
            $sign_result       = $db->query($sign_result_sql, $sign_result_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
            if(empty($sign_result)) {
                // 验证工号是否收到消息
                $staff_msg = $this->get_staff_msg_info($param['staff_info_id'], $param['remote_message_id']);
                if (empty($staff_msg) || !$staff_msg['push_state']) {
                    throw new ValidationException(self::$t->_('sign_msg_020'));
                }
                if (!$staff_msg['read_state']) {
                    throw new ValidationException(self::$t->_('sign_msg_037'));
                }
            }

            // 获取消息内容
            // 如果存在消息 并且是签字消息， 按模板发送消息 ，且设置了仅发送人和审批人可见权限时，需要进行验证
//            if (!empty($message) &&
//                $message['category'] == MessageEnums::CATEGORY_SIGN &&
//                $message['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE &&
//                $message['visible'] == MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL
//            ) {
            if ($message['visible'] == MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL) {
                // 发送人
                $visible = [$message['add_userid']];
                // 审批人
                if (!empty($message['approver_id'])) {
                    $visible = array_merge($visible, explode(',', $message['approver_id']));
                }

                if ($this->makeSignPdfPermission && !in_array($param['user_info']['id'], $visible)) {
                    // 仅发送人、审批人可见
                    throw new ValidationException(self::$t->_('no_have_permission'));
                }
            }

            // 签字消息 按照模板发送 变量替换
            if ($message['category'] == MessageEnums::CATEGORY_SIGN && $message['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE) {
                // 查询附件明细表数据
                $identityData = (new MessageIdentityDetailService())->getIdentityDetailByParams([
                    "staff_info_id"     => $param['staff_info_id'],
                    "remote_message_id" => $param['remote_message_id'],
                    "is_del"            => 0,
                ], ["staff_info_attribute", "id"]);
                if (!empty($identityData)) {
                    $staffInfoAttributes = json_decode($identityData[0]['staff_info_attribute'], true);
                    // 拿到原始文办中的替换标记
                    preg_match_all("/\{\{\s*[A-Z]\s*\}\}/", $message['content'], $matches);
                    $pattern = $matches[0];
                    array_walk($pattern, function (&$item, $key) {
                        $item = '/'.trim($item).'/';
                    });

                    // 获取到模板的 变量 并 去掉 /{{}}/
                    $replaceData = [];
                    foreach ($pattern as $key => $val) {
                        $seat = trim($val, '/{{}}/');
                        // 按照模板变量的顺序 进行重序
                        $replaceData[] = $staffInfoAttributes[$seat];
                    }
                    // 替换后重新赋值
                    $message['content'] = preg_replace($pattern, $replaceData, $message['content']);
                }
            }

            if (empty($sign_result['sign_pdf_url']) && empty($sign_result['sign_url'])) {
                $log .= '员工签字图片及签字pdf文件均为空[sign_pdf_url | sign_url].';

                $this->logger->write_log($log, 'info');
                throw new ValidationException(self::$t->_('sign_msg_037'));
            }

            if ($this->isArgeementSign($message) && empty($sign_result['sign_pdf_url'])) {
                throw new ValidationException(self::$t->_('message.pdf_url_wai_generation'));
            }

            //PDPA消息限制导出
            if (
                isCountry('MY')
                && $message['category'] == MessageEnums::CATEGORY_SIGN
                && in_array($message['category_code'],
                    [
                        EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_PDPA'),
                        EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_BEBAS'),
                    ])
                && empty($sign_result['sign_pdf_url'])) {
                throw new ValidationException(self::$t->_('message.pdf_url_wai_generation'));
            }

            if (!empty($sign_result['sign_pdf_url'])) {
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $param['staff_info_id'].'.pdf';
                $result['data']['file_size'] = get_headers($sign_result['sign_pdf_url'], true)['Content-Length'];
                $result['data']['file_url']  = $sign_result['sign_pdf_url'];

                $log .= "签字pdf文件已存在, [file_url = {$sign_result['sign_pdf_url']}].";
                $this->logger->write_log($log, 'info');
                return $result;
            }

            // 获取消息内容
            //$message = $this->get_msg_by_remote_id($param['remote_message_id']);

            if($message['send_type'] == 13) {//按工具号发送
                $staffInfo = ToolStaffInfoModel::findFirst([
                    'columns' => 'staff_info_id,name,job_title',
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $param['staff_info_id']],
                ]);
            } else {
                // 获取员工信息
                $staffInfo = HrStaffInfoModel::findFirst([
                    'columns' => 'staff_info_id,name,job_title,hire_type',
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $param['staff_info_id']],
                ]);
            }

            $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() : [];
            $jobTitle = [];
            if(!empty($staffInfo)) {
                $jobTitle = HrJobTitleModel::findFirst([
                    'columns' => 'id,job_name',
                    'conditions' => 'id = :job_title:',
                    'bind' => ['job_title' => $staffInfo['job_title']],
                ]);
                $jobTitle = !empty($jobTitle) ? $jobTitle->toArray() : [];
            }

            // 公历 转 泰国佛历年 格式: วันที่ 20 ตุลาคม พ.ศ.2563
            $sign_date = $this->getSignDate($sign_result);
            [$pdf_template_file, $pdf_model_url] = $this->getPdfModel($genPdfChannel);
            $fileKey = $this->getStaffKeyName($staffInfo['hire_type']);
            // pdf模板数据
            $view_data = [
                'title'        => $message['title'],
                'content'      => $message['content'],
                'staff_id'     => $param['staff_info_id'],
                'staff_name'   => $staffInfo['name'] ?? '',
                'job_title'    => $jobTitle['job_name'] ?? '',
                'sign_url'     => $sign_result['sign_url'],
                'sign_date'    => $sign_date,
                'staff_id_key' => $fileKey['staff_id_key'],
                'position_key' => $fileKey['position_key'],
                'sign_key'     => $fileKey['sign_key'],
            ];

            // 构建pdf模板
            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH.$pdf_model_url);
            $view->setVars($view_data);
            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );
            $view->render("sign_message", $pdf_template_file);
            $view->finish();
            $pdf_content = $view->getContent();

            // 获取系统临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $path        = $file_dir = $sys_tmp_dir.'/';
            $file_name   = 'sign_'.md5($param['remote_message_id'].mt_rand(1, 10)).'_'.$param['staff_info_id'].".pdf";
            $file_path   = $path.$file_name;

            $log .= "[pdf_template = $pdf_template_file, file_path = $file_path], ";

            $make_pdf_result = false;
            if ($genPdfChannel == 'mpdf') {
                $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
                $fontDirs      = $defaultConfig['fontDir'];

                $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
                $fontData          = $defaultFontConfig['fontdata'];

                $mpdf = new \Mpdf\Mpdf([
                    'format' => 'A4',
                    'mode'   => 'zh-CN', // th zh

                    'fontDir'      => array_merge($fontDirs, [
                        BASE_PATH.'/public/fonts',
                    ]),
                    'fontdata'     => $fontData + [
                            'm_font' => [
                                'B'          => 'THSarabun Bold.ttf',
                                'useOTL'     => 0xFF, /* Uses OTL for all scripts */
                                'useKashida' => 75,
                            ],
                        ],
                    'default_font' => 'm_font',
                ]);

                $mpdf->useAdobeCJK = true;

                // 处理中文
                $mpdf->autoScriptToLang = true;
                $mpdf->autoLangToFont   = true;
                $mpdf->SetDisplayMode('fullpage');
                $mpdf->SetHTMLHeader("");
                $mpdf->SetHTMLFooter("");
                $mpdf->WriteHTML($pdf_content);
                $mpdf->Output($file_path, "f");

                $make_pdf_result = true;
            }
            if ($make_pdf_result == false) {
                $log .= "pdf文件生成失败.";

                $this->logger->write_log($log, 'info');
                throw new ValidationException('pdf create fail');
            } else {
                $log .= "pdf文件生成成功, ";
            }

            // pdf文件上传OSS
            $upload_result = OssHelper::uploadFile($file_path,
                OssService::OSS_DIR_MAPS[OssService::HRIS_STAFF_PROFILE]);
            if (empty($upload_result['object_url'])) {
                $log .= "OSS上传失败[file_url = {$upload_result['object_url']}], ";
                $this->logger->write_log($log, 'info');
                throw new \Exception('upload pdf to oss fail');
            }
            // 更新pdf_url 到 签字结果表中
            $update_sql   = "UPDATE sign_msg_result SET sign_pdf_url = ? WHERE id = ? LIMIT 1";
            $update_param = [$upload_result['object_url'], $sign_result['id']];
            $this->getDI()->get('db_backyard')->execute($update_sql, $update_param);

            $result['code']              = ErrCode::SUCCESS;
            $result['message']           = 'success';
            $result['data']['file_name'] = $param['staff_info_id'].'.pdf';
            $result['data']['file_size'] = get_headers($upload_result['object_url'], true)['Content-Length'];
            $result['data']['file_url']  = $upload_result['object_url'];

            $log .= "OSS上传成功[file_url = {$upload_result['object_url']}], ";

            // 删除临时文件
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
            $this->logger->write_log($log, 'info');
            return $result;
        } catch (ValidationException $e) {
            $code    = ErrCode::VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $this->logger->write_log($log.'出现异常, 原因可能是'.$e->getMessage(), 'error');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 获取pdf模板信息
     * @param $genPdfChannel
     * @return array
     */
    public function getPdfModel($genPdfChannel)
    {
        if ($genPdfChannel == 'go_pdf') {
            $pdf_template_file = 'staff_sign_pdf_go';
        } else {
            $pdf_template_file = 'staff_sign_pdf';
        }

        $pdf_model_url = '/views';
        return [$pdf_template_file, $pdf_model_url];
    }

    /**
     * 获取pdf模板信息 个人代理 改 翻译
     * @param $hireType
     * @return array
     */
    public function getStaffKeyName($hireType)
    {
        $result['staff_id_key'] = 'รหัสพนักงาน';
        $result['position_key'] = 'ตำแหน่ง';
        $result['sign_key'] = 'พนักงานลงลายมือชื่อ';
        //个人代理展示文案
        if ($hireType == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $result['staff_id_key'] = 'รหัสผู้รับจ้างส่งพัสดุรายชิ้น';
            $result['position_key'] = 'ประเภท';
            $result['sign_key'] = 'ผู้รับจ้างส่งพัสดุรายชิ้นลงลายมือชื่อ';
        }

        return $result;
    }

    /**
     * 公历 转 泰国佛历年 格式: วันที่ 20 ตุลาคม พ.ศ.2563
     * @param $sign_result
     * @return string
     */
    public function getSignDate($sign_result)
    {
        $sign_date = '';
        if (!empty($sign_result['created_at'])) {
            $th_sign_timestamp = strtotime($sign_result['created_at']) + ($this->timeOffset)*3600;
            $th_year = date('Y', $th_sign_timestamp) + 543;
            $th_month = SalaryEnums::$month_list[date('n', $th_sign_timestamp)];
            $sign_date = 'วันที่ ' . date('j', $th_sign_timestamp) . ' ' . $th_month . ' พ.ศ.' . $th_year;
        }
        return $sign_date;
    }

    /**
     * 消息模块权限验证: 普通/问卷/签字模块访问权限 + 各模块的编辑/添加操作权限
     * @param $staff_info array
     * @return $result array
     */
    public function msgAuthorityCheck($staff_info = [])
    {
        if (empty($staff_info)) {
            return [];
        }
        $this->logger->write_log( 'msgAuthorityCheck--LoginUserInfo:' . json_encode($staff_info, JSON_UNESCAPED_UNICODE), 'info');
        // 功能 -> key
        $data = $this->msg_permission_path;

        $download_pdf_permission = (new SettingEnvService())->getSetVal('message_sign_download_pdf', ',');
        $download_pdf = false;
        if(in_array($staff_info['id'], $download_pdf_permission)) {
            $download_pdf = true;
        }
        // 超管默认拥有所有默认权限
        $staff_position = explode(',', $staff_info['position_category']);
        if (in_array(99, $staff_position)  || in_array($staff_info['id'], explode(',', env('sa_id')))) {
            foreach ($data as $key => $value) {
                foreach ($value as  $setting_k => $route_action) {
                    $value[$setting_k] = true;
                }

                $data[$key] = $value;
            }
            $data['sign_msg']['download_pdf'] = $download_pdf;
            return $data;
        }

        $headerData       = $this->request->getHeaders();
        //区分不同请求源权限控制。
        if($headerData['From'] === 'fbi'){
            //验证工号权限fbi
            $staff_authority = $this->getFbiRoleStaffMenus($staff_info['id']);
        } else {
            //验证工号权限hcm
            $staff_authority = $this->getPermissionByStaffId($staff_info['id'], $staff_position);
        }

        if (!empty($staff_authority)) {
            foreach ($data as $key => $value) {
                foreach ($value as  $setting_k => $route_action) {
                    if (in_array($route_action, $staff_authority)) {
                        $value[$setting_k] = true;
                    } else {
                        $value[$setting_k] = false;
                    }
                }

                $data[$key] = $value;
            }

            $data['sign_msg']['download_pdf'] = $download_pdf;

            return $data;
        }

        //区分不同请求源权限控制。
        if($headerData['From'] === 'fbi'){
            //验证角色权限fbi
            $role_authority = $this->getFbiRoleMenus($staff_position);

        }

        if (!empty($role_authority)) {
            foreach ($data as $key => $value) {
                foreach ($value as  $setting_k => $route_action) {
                    if (in_array($route_action, $role_authority)) {
                        $value[$setting_k] = true;
                    } else {
                        $value[$setting_k] = false;
                    }
                }

                $data[$key] = $value;
            }

            $data['sign_msg']['download_pdf'] = $download_pdf;

            return $data;
        }

        return [];
    }

    /**
     * 按工号查询是否有相关权限 + 角色权限
     * @param $staff_info_id
     * @param $position
     * @return array
     */
    public function getPermissionByStaffId($staff_info_id, $position)
    {
        //配置了个人的维度，就是用个人表的权限
        $permission_ids = [];
        $staffPermission = HcmStaffPermissionInfoModel::find(
            [
                'conditions' => 'is_deleted = 0 and staff_info_id = ?1',
                'bind' => [
                    1 => $staff_info_id,
                ],
            ]
        )->toArray();
        if(!empty($staffPermission)) {
            $permission_ids = array_column($staffPermission, 'permission_id');
        }

        if(!empty($position)) {
            //配置了角色的维度，就是用角色表的权限
            $rolePermission = HcmRolePermissionInfoModel::find(
                [
                    'columns'=>'permission_id',
                    'conditions' => 'role_id IN ({ids:array}) and is_deleted = 0',
                    'bind' => [
                        'ids' => $position,
                    ],
                ]
            )->toArray();
            if(!empty($rolePermission)) {
                $permission_ids = array_merge($permission_ids, array_column($rolePermission, 'permission_id'));
            }
        }


        $permission_ids = array_values(array_unique($permission_ids));
        if(empty($permission_ids)) {
            return [];
        }
        return $this->getPermissionInfo($permission_ids);
    }

    /**
     * 获取员工fbi 按工号配置的菜单权限
     * @param $staff_info_id
     * @return mixed
     */
    public function getFbiRoleStaffMenus($staff_info_id)
    {
        $menusInfo = RoleStaffMenusModel::find([
            'conditions' => 'staff_info_id = ?1',
            'bind' => [
                1 => $staff_info_id,
            ],
        ]);
        $menusInfoToIds = [];
        if($menusInfo) {
            $menusInfoToIds = array_column($menusInfo->toArray(), 'menu_id');
        }

        return $this->getFbiMenusInfo($menusInfoToIds);
    }


    /**
     * 获取员工fbi 按角色配置的菜单权限
     * @param $position
     * @return mixed
     */
    public function getFbiRoleMenus($position)
    {
        if(empty($position)) {
            return  [];
        }
        //配置了角色的维度，就是用角色表的权限
        $rolePermission = RoleMenusModel::find(
            [
                'columns'=>'menu_id',
                'conditions' => 'role_id IN ({ids:array})',
                'bind' => [
                    'ids' => $position,
                ],
            ]
        )->toArray();
        if(empty($rolePermission)) {
            return [];
        }

        $permissionIds = array_column($rolePermission, 'menu_id');
        return $this->getFbiMenusInfo($permissionIds);
    }

    /**
     * 根据角色获取菜单权限
     * @param $position
     * @return array|mixed
     */
    public function getPermissionByRole($position)
    {
        if(empty($position)) {
            return  [];
        }
        //配置了角色的维度，就是用角色表的权限
        $rolePermission = HcmRolePermissionInfoModel::find(
            [
                'columns'=>'permission_id',
                'conditions' => 'role_id IN ({ids:array}) and is_deleted = 0',
                'bind' => [
                    'ids' => $position,
                ],
            ]
        )->toArray();
        if(empty($rolePermission)) {
            return [];
        }
        $permissionIds = array_column($rolePermission, 'permission_id');

        $permissionIds = array_values(array_unique($permissionIds));
        return $this->getPermissionInfo($permissionIds);
    }

    /**
     * 获取菜单信息
     * @param $permission_ids
     * @return mixed
     */
    public function getPermissionInfo($permission_ids)
    {
        if(!$permission_ids){
            return [];
        }
        $rolePermission = HcmPermissionModel::find(
            [
                'columns'=>'url',
                'conditions' => 'id IN ({permission_ids:array}) and is_deleted = 0',
                'bind' => [
                    'permission_ids' => $permission_ids,
                ],
            ]
        );
        $rolePermissionToUrl = [];
        if($rolePermission) {
            $rolePermissionToUrl = array_column($rolePermission->toArray(), 'url');
        }

        return $rolePermissionToUrl;
    }

    /**
     * 获取菜单信息
     * @param $menuIds
     * @return mixed
     */
    public function getFbiMenusInfo($menuIds)
    {
        if(!$menuIds){
            return [];
        }
        $menus = MenusModel::find(
            [
                'columns'=>'route_action',
                'conditions' => 'id IN ({menu_ids:array}) and status = 1',
                'bind' => [
                    'menu_ids' => $menuIds,
                ],
            ]
        );
        $menusRouteAction = [];
        if($menus) {
            $menusRouteAction = array_column($menus->toArray(), 'route_action');
        }

        return $menusRouteAction;
    }

    /**
     * 获取某员工接收的消息内容
     * @param $staff_info_id int 员工id
     * @param $remote_message_id int 推送端消息id
     * @return $result array | boolean
     */
    public function get_staff_msg_info($staff_info_id, $remote_message_id){
        if (empty($staff_info_id || empty($remote_message_id))) {
            return [];
        }

        $sql = "SELECT read_state, push_state ,id 
                FROM message_courier 
                WHERE staff_info_id = :staff_info_id AND message_content_id = :remote_message_id ";
        $param = [
            'staff_info_id' => $staff_info_id,
            'remote_message_id' => $remote_message_id,
        ];

        return $this->getDI()->get('db_message_read')->query($sql, $param)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 创建pdf
     * @param $remote_message_id
     * @return array
     */
    public function genMsgContentPdf($param)
    {
        try {
            $remote_message_id = $param['remote_message_id'];
            // 获取消息内容
            $message = $this->get_msg_by_remote_id($remote_message_id);
            $attachmentCategory = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT');
            if (empty($message) || !in_array($message['category'], [0, 33, $attachmentCategory,EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT')])) {
                $this->logger->write_log('genMsgContentPdf:出现异常, 原因是：未查到消息信息', 'info');
                throw new ValidationException('message param error');
            }

            if(isCountry('MY') && $attachmentCategory) {
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $message['title'].'_'.gmdate('Y.m.d',
                        time() + ($this->timeOffset) * 3600).'.pdf';
                $result['data']['file_size'] = filesize($message['content']);
                $result['data']['file_url']  = $message['content'];

                return $result;
            } elseif (isCountry() && $message['category_code'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT')) {
                //21322【TH|BY|消息】 外协仓管自动发送合同
                $sign_info = $this->getOsStaffContract($message['remote_message_id']);
                if (empty($sign_info)) {
                    throw new ValidationException(self::$t->_('21322_by_error_message_005'));
                }
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $message['title'] . '_' . gmdate('Y.m.d', time() + ($this->timeOffset) * 3600) . '.pdf';
                $result['data']['file_size'] = filesize($sign_info['pdf_url']);
                $result['data']['file_url']  = $sign_info['pdf_url'];

                return $result;
            }elseif(isCountry('PH') && $message['category'] == EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT')){
                $messageCourierInfo = MessageCourierModel::findFirst(
                    [
                        'columns' => 'id',
                        'conditions' => 'message_content_id = :message_content_id:',
                        'bind' => ['message_content_id' => $remote_message_id],
                    ]
                );
                if (empty($messageCourierInfo)) {
                    throw new ValidationException('getMessageCourierInfo ' . self::$t->_('data_error'));
                }
                $pdf_data = MessagePdf::getInfoByMsgId($messageCourierInfo->id);
                $pdf_url       = $pdf_data['pdf_url'];
                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $message['title'] . '_' . gmdate('Y.m.d', time() + ($this->timeOffset) * 3600) . '.pdf';
                $result['data']['file_size'] = filesize($pdf_url);
                $result['data']['file_url']  = $pdf_url;
                return $result;
            }

            //消息内容导出
            if ($this->isArgeementSign($message)) {
                $pdf_url = '';
                if($message['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_AGREEMENT) {
                    $agreementInfo = HrAgreementSignRecordModel::getInfoByStaffInfoId($message['staff_info_ids']);
                    if (empty($agreementInfo['pdf_url'])) {
                        throw new ValidationException(self::$t->_('message.pdf_url_wai_generation'));
                    }
                    $pdf_url = $agreementInfo['pdf_url'];
                }

                if (in_array($message['category_code'], [
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_INDEPENDENT,
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT,
                    EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_PDPA'), //PDPA-消息导出
                    EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_BEBAS'),
                    EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT')
                ])) {
                    //获取消息id
                    $messageCourierInfo = MessageCourierModel::findFirst(
                        [
                            'columns' => 'id',
                            'conditions' => 'message_content_id = :message_content_id:',
                            'bind' => ['message_content_id' => $remote_message_id],
                        ]
                    );
                    if (empty($messageCourierInfo)) {
                        throw new ValidationException('getMessageCourierInfo ' . self::$t->_('data_error'));
                    }
                    //获取签名的pdf
                    $agreementInfo = MessagePdf::getInfoByMsgId($messageCourierInfo->id);
                    $pdf_url       = $agreementInfo['pdf_url'];
                }

                $result['code']              = ErrCode::SUCCESS;
                $result['message']           = 'success';
                $result['data']['file_name'] = $message['title'].'_'.gmdate('Y.m.d',
                        time() + ($this->timeOffset) * 3600).'.pdf';
                $result['data']['file_size'] = filesize($pdf_url);
                $result['data']['file_url']  = $pdf_url;

                return $result;
            }

            // 校验权限: 待审批 和 已驳回的不可操作
            if (in_array($message['audit_status'], [1, 3])) {
                $this->logger->write_log("genMsgContentPdf: id: {$message['id']}, audit_status: {$message['audit_status']}, 不可导出",
                    'info');

                throw new ValidationException('no root export');
            }

            // 如果存在消息 并且是签字消息， 按模板发送消息 ，且设置了仅发送人和审批人可见权限时，需要进行验证
//            if (($message['category'] == MessageEnums::CATEGORY_SIGN &&
//                    $message['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE) ||
//                ($message['category'] == MessageEnums::CATEGORY_GENERAL &&
//                    $message['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE) &&
//                $message['visible'] == MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL
//            ) {
            if ($message['visible'] == MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL) {
                // 发送人
                $visible = [$message['add_userid']];
                // 审批人
                if (!empty($message['approver_id'])) {
                    $visible = array_merge($visible, explode(',', $message['approver_id']));
                }

                if (!in_array($param['user_info']['id'], $visible)) {
                    // 仅发送人、审批人可见
                    throw new ValidationException(self::$t->_('no_have_permission'));
                }
            }

            // 替换非可见字符为空格, 避免文件名有误
            $message['title'] = preg_replace('/\s/', ' ', $message['title']);

            // pdf 模板内容
            $view_data = [
                'title'   => $message['title'],
                'content' => stripcslashes($message['content']),
            ];

            // 获取系统临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $path        = $sys_tmp_dir.'/';
            $file_name   = 'message_c_'.md5($remote_message_id).".pdf";
            $file_path   = $path.$file_name;

            // 构建pdf模板
            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH.'/views');
            $view->setVars($view_data);
            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );
            $view->render("sign_message", "message_content_pdf");
            $view->finish();
            $pdf_content = $view->getContent();

            $mpdf = new \Mpdf\Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN', // th
            ]);

            $mpdf->useAdobeCJK      = true;
            $mpdf->autoScriptToLang = true;
            $mpdf->autoLangToFont   = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($pdf_content);
            $mpdf->Output($file_path, "f");

            // 上传OSS
            $upload_result = OssHelper::uploadFile($file_path,
                OssService::OSS_DIR_MAPS[OssService::HRIS_STAFF_PROFILE]);
            $this->logger->write_log("genMsgContentPdf: id: {$message['id']}, content: {$message['content']} ,oss return:".json_encode($upload_result,
                    JSON_UNESCAPED_UNICODE), 'info');
            if (empty($upload_result['object_url'])) {
                $this->logger->write_log("OSS上传失败[file_url = {$upload_result['object_url']}],", 'info');
                throw new \Exception('upload pdf to oss fail');
            }
            $result['code']              = ErrCode::SUCCESS;
            $result['message']           = 'success';
            $result['data']['file_name'] = $message['title'].'_'.gmdate('Y.m.d',
                    time() + ($this->timeOffset) * 3600).'.pdf';
            $result['data']['file_size'] = filesize($file_path);
            $result['data']['file_url']  = $upload_result['object_url'];

            // 删除临时文件
            if (file_exists($file_path)) {
                @unlink($file_path);
            }

            return $result;
        } catch (ValidationException $e) {
            $code    = ErrCode::VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $this->logger->write_log('genMsgContentPdf:出现异常, 原因是'.$e->getMessage(), 'error');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * bi message 详情
     * @param $remote_message_id
     * @return bool
     */
    public function get_msg_by_remote_id($remote_message_id)
    {
        if (empty($remote_message_id)) {
            return false;
        }

        $sql = "select id, title, content, send_type,
                staff_info_ids,to_group,top_status,publish_status
                ,remote_message_id,questionnaire_lib_id, category,category_code,audit_status,add_userid,visible,approver_id 
                from message where remote_message_id = :remote_message_id
                ";

        $message_param = ['remote_message_id' => $remote_message_id];

        return $this->getDI()->get('db_rby')->query($sql, $message_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    public function qnmsgDownDealExport($params, $file_name)
    {
        $staff_info_id = $params['staff_info_id'];
        $remote_message_id = $params['remote_message_id'];
        // 读取问卷消息[含题目]
        $msg_info = $this->get_msg_by_remote_id($remote_message_id);

        // 问卷数据获取
        $data_param = [
            'remote_message_id' => $remote_message_id,
            'staff_info_id' => $staff_info_id,
            'lib_id' => $msg_info['questionnaire_lib_id'],
            'send_type' => $msg_info['send_type'],
        ];

        $data_param['page_num'] = 1;
        $data_param['page_size'] = 2000;
        $data = [];
        while (true) {
            $data_tmp = $this->get_qnmsg_result($data_param);

            if (empty($data_tmp)) {
                break;
            }
            $data = array_merge($data, $data_tmp);
            $data_param['page_num']++;
        }

        // 准备Excel表头配置
        // 获取问卷消息题目
        $questionnaireLibService = new QuestionnaireLibService();
        $question_item = $questionnaireLibService->cal_question_item($msg_info['questionnaire_lib_id']);

        // 生成表头
        $header = [
            self::$t->_('msg_qn_050'), // 序号
            self::$t->_('msg_title_01'), // 消息标题
            self::$t->_('msg_outlets'), // 网点
            self::$t->_('msg_staff_id'), // 员工ID
            self::$t->_('msg_staff_name'), // 员工姓名
            self::$t->_('msg_qn_051'), // 提交问卷时间
        ];
        $header = array_merge($header, $question_item['question_serial']);


        $result = [];
        if(!empty($data)){
            foreach ($data as $v){
                $row = [];

                $row[0] = $v['serial'];
                $row[1] = $v['title'];
                $row[2] = $v['store_name'];
                $row[3] = $v['staff_info_id'];
                $row[4] = $v['staff_info_name'];
                $row[5] = $v['submit_time'];

                // 生成答案与题干映射关系
                $answer = $questionnaireLibService->make_question_answer_map($question_item['question'], $v['staff_answer']);
                if (!empty($answer)) {
                    $row = array_merge($row, $answer);
                }

                $result[] = $row;
            }
        }

        return $this->exportExcel($header, $result, $file_name);
    }

    /**
     * 问卷下载
     * @param $params
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws ValidationException
     */
    public function qnmsgDownDeal($params)
    {
        $staff_info_id = $params['staff_info_id'];
        $remote_message_id = $params['remote_message_id'];
        // 下载某员工的问卷结果
        if (!empty($staff_info_id)) {
            // 验证工号格式
            if (!preg_match('/^\d{5,6}$/', $staff_info_id)) {
                throw new ValidationException(self::$t->_('msg_qn_053'));
            }

            // 验证工号是否存在
            $staff_info = $this->get_staff_info($staff_info_id);
            if (empty($staff_info)) {
                throw new ValidationException(self::$t->_('msg_qn_054'));
            }

            // 验证工号是否参与答题
            $staff_msg = $this->getStaffSubmitQuestionnaire($staff_info_id, $remote_message_id);
            if (empty($staff_msg)) {
                throw new ValidationException(self::$t->_('msg_qn_055'));

            }
        }

        // 读取问卷消息[含题目]
        $msg_info = $this->get_msg_by_remote_id($remote_message_id);
        if ($msg_info['publish_status'] != 6) {
            throw new ValidationException('Msg No Publish');
        }

        // 校验权限: 待审批 和 已驳回的不可操作
        if (in_array($msg_info['audit_status'], [1, 3])) {
            $this->logger->write_log("qnmsg_downdeal: id: {$msg_info['id']}, audit_status: {$msg_info['audit_status']}, 不可下载", 'info');
            throw new ValidationException('This status is not downloadable');
        }

        if (empty($msg_info['questionnaire_lib_id'])) {
            throw new ValidationException('Msg Category Error');
        }

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "message" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "qnmsgDownDeal";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['user_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $headerData = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && in_array($headerData['From'], ['fbi'])? $headerData['From'] : '';

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['user_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        if (!empty($hcmExcelTaskId) && in_array($params['From'], ['fbi'])) {
            (new BllService())->addFbiExportMessage(
                $params['user_id'],
                'hcm_message_qnmsgDownDeal',
                $params['file_name'],
                $hcmExcelTaskId
            );
        }

        return $hcmExcelTaskId;
    }

    /**
     * 获取员工信息
     * @param $staff_info_id int
     * @return $result array
     *
     */
    public function get_staff_info($staff_info_id){
        $param['id'] = $staff_info_id;
        $sql = "SELECT * FROM hr_staff_info WHERE staff_info_id = :id LIMIT 1";
        return $this->getDI()->get('db_rby')->query($sql, $param)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 获取员工已提交的问卷数据
     * @param int $staff_id
     * @param string $remote_message_id
     * @return mixed
     */
    public function getStaffSubmitQuestionnaire(int $staff_id, string $remote_message_id)
    {
        try {
            if (empty($staff_id) || empty($remote_message_id)) {
                return [];
            }

            $data = QuestionnaireAnswerModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND remote_message_id = :remote_message_id:',
                'bind' => ['staff_info_id' => $staff_id, 'remote_message_id' => $remote_message_id],
            ]);

            return $data ? $data->toArray() : [];
        } catch (\Exception $e) {
            $this->logger->write_log('获取员工已提交的问卷数据, 异常的原因是: '.$e->getMessage());
        }

        return [];
    }

    /**
     * 获取问卷结果数据
     * @param $param array
     * @return $result array
     */
    public function get_qnmsg_result($param){
        $remote_message_id = $param['remote_message_id'];
        if (empty($param['remote_message_id'])) {
            return [];
        }

        $messageRepository = new MessageRepository();

        $send_result = $messageRepository->getMessageCourierList($param, 'message_content_id AS remote_message_id, read_state, staff_info_id, title');

        if (empty($send_result)) {
            return [];
        }

        // 提取所有员工ID
        $where['staff_ids'] = array_column($send_result,'staff_info_id');
        if($param['send_type'] == 13) {//给工具号发送的消息
            $staff_info_result = $this->getStaffToolRelationInfo($where);
        } else {
            $staff_info_result = $this->getStaffRelationInfo($where);
        }
        if(!$staff_info_result) {
            return [];
        }
        $staff_info_result = array_column($staff_info_result, null ,'id');

        // 查询问卷结果表该问卷的所有员工提交结果
        $remote_message_params['remote_message_id'] = $remote_message_id;
        $remote_message_params['staff_info_ids'] = $where['staff_ids'];

        $staff_answer = $messageRepository->getQuestionnaireAnswer($remote_message_params, 'answer_content, staff_info_id, submit_time');
        $staff_answer = array_column($staff_answer, null, 'staff_info_id');

        //拼接数据
        $data = array();
        foreach ($send_result as $key => $value) {
            $data[$key]['serial'] = ($param['page_size'] * ($param['page_num'] - 1)) + $key + 1;
            $data[$key]['title'] = $value['title'];

            $curr_staff_info = isset($staff_info_result[$value['staff_info_id']]) ? $staff_info_result[$value['staff_info_id']] : [];
            if (!empty($curr_staff_info)) {
                $data[$key]['store_name'] = '(' .$curr_staff_info['store_id'].') '.$curr_staff_info['store_name'];
                $data[$key]['staff_info_name'] = $curr_staff_info['name'];
                $data[$key]['manage_region_name'] = $curr_staff_info['manage_region_name'];
                $data[$key]['store_name'] = $curr_staff_info['store_name'];
                $data[$key]['job_title_name'] = $curr_staff_info['job_title_name'];

            } else {
                $data[$key]['store_name'] = '';
                $data[$key]['staff_info_name'] = '';
            }

            $data[$key]['staff_info_id'] = $value['staff_info_id'];

            // read_state: 0 未提交；1-已提交(查询问卷结果表的答案) $value['read_state']
            $curr_staff_answer = isset($staff_answer[$value['staff_info_id']]) ? $staff_answer[$value['staff_info_id']] : [];
            if (!empty($curr_staff_answer) && $value['read_state']) {
                $data[$key]['submit_time'] = date('Y/n/j H:i', strtotime($curr_staff_answer['submit_time']));

                // 拼接员工提交的答案
                $data[$key]['staff_answer'] = json_decode($curr_staff_answer['answer_content'], true);
            } else {
                $data[$key]['submit_time'] = '';
                $data[$key]['staff_answer'] = [];
            }
        }

        return $data;
    }

    /**
     * 获取员工相关信息
     * @param $where
     * @return mixed
     */
    public function getStaffRelationInfo($where)
    {
        $staffInfo = HrStaffInfoModel::find([
            'conditions'=>' staff_info_id in ({staff_info_id:array})',
            'bind'=>['staff_info_id' => $where['staff_ids']],
            'columns'=>'staff_info_id as id,name,sys_store_id,job_title',
        ])->toArray();

        $storeIds = array_column($staffInfo, 'sys_store_id');
        $store_list = (new SysStoreService())->getStoreListByIdsFromCache($storeIds);
        $store_list = array_column($store_list, NULL, 'id');
        $store_list[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $job_title = array_column((new HrJobTitleService())->getJobTitleListFromCache(false),'job_name','id');

        $result = [];
        foreach ($staffInfo as $oneStaffInfo) {
            $oneData['id'] = $oneStaffInfo['id'];
            $oneData['name'] = $oneStaffInfo['name'];
            $oneData['store_id'] = $oneStaffInfo['sys_store_id'];
            $oneData['store_name'] = isset($store_list[$oneStaffInfo['sys_store_id']]) ? $store_list[$oneStaffInfo['sys_store_id']]['store_name'] : '';
            $oneData['manage_region_name'] = isset($store_list[$oneStaffInfo['sys_store_id']]) ? $store_list[$oneStaffInfo['sys_store_id']]['region_name'] : '';
            $oneData['job_title_name'] = $job_title[$oneStaffInfo['job_title']] ?? '';
            $result[] = $oneData;
        }

        return $result;
    }

    /**
     * 获取工具号员工相关信息
     * @param $where
     * @return mixed
     */
    public function getStaffToolRelationInfo($where)
    {
        $staffInfo = ToolStaffInfoModel::find([
            'conditions'=>' staff_info_id in ({staff_info_id:array})',
            'bind'=>['staff_info_id' => $where['staff_ids']],
            'columns'=>'staff_info_id as id,name,sys_store_id,job_title',
        ])->toArray();

        $storeIds = array_column($staffInfo, 'sys_store_id');
        $store_list = (new SysStoreService())->getStoreListByIdsFromCache($storeIds);
        $store_list = array_column($store_list, NULL, 'id');
        $store_list[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $job_title = array_column((new HrJobTitleService())->getJobTitleListFromCache(false),'job_name','id');

        $result = [];
        foreach ($staffInfo as $oneStaffInfo) {
            $oneData['id'] = $oneStaffInfo['id'];
            $oneData['name'] = $oneStaffInfo['name'];
            $oneData['store_id'] = $oneStaffInfo['sys_store_id'];
            $oneData['store_name'] = isset($store_list[$oneStaffInfo['sys_store_id']]) ? $store_list[$oneStaffInfo['sys_store_id']]['store_name'] : '';
            $oneData['manage_region_name'] = isset($store_list[$oneStaffInfo['sys_store_id']]) ? $store_list[$oneStaffInfo['sys_store_id']]['region_name'] : '';
            $oneData['job_title_name'] = $job_title[$oneStaffInfo['job_title']] ?? '';
            $result[] = $oneData;
        }

        return $result;
    }

    /**
     * 导出阅读
     * @param $remote_message_id
     * @return array
     */
    public function exportReadStatus($remote_message_id, $loginUserInfo)
    {
        $msg_info = $this->get_msg_by_remote_id($remote_message_id);
        if ($msg_info['publish_status'] == 6)//非发布状态 不用取数据
        {
            $data = $this->getReaderStaff($remote_message_id, $msg_info['send_type']);
        }

        // 1 仅发送人和审批人可见 0 全部可见
        if (MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL == $msg_info['visible'] && !empty($loginUserInfo['id'])) {
            // 发送人
            $visible = [$msg_info['add_userid']];
            // 审批人
            if (!empty($msg_info['approver_id'])) {
                $visible = array_merge($visible, explode(',', $msg_info['approver_id']));
            }

            if (!in_array($loginUserInfo['id'], $visible)) {
                // 仅发送人、审批人可见
                throw new ValidationException(self::$t->_('no_have_permission'));
            }
        }

        try {
            $result = [];
            if (!empty($data)) {
                foreach ($data as $v) {
                    $row[0] = $v['title'];
                    $row[1] = $v['store_id'];
                    $row[2] = $v['staff_info_id'];
                    $row[3] = $v['staff_info_name'];
                    $row[4] = $v['read_state'];

                    $result[] = $row;
                }
            }

            $header = [
                self::$t->_('message_title'),    //'文章标题',
                self::$t->_('store_name'),       //'网店',
                self::$t->_('bag_courierid'),    //'员工编号',
                self::$t->_('bag_tcouriername'), //'员工姓名',
                self::$t->_('isread'),           //'是否阅读',
            ];

            $file_name = "read_status";
            $res       = $this->exportExcelReturn($header, $result ?? [], $file_name);
            if ($res['code'] == 1) {
                $result['code']             = ErrCode::SUCCESS;
                $result['message']          = 'success';
                $result['data']['file_url'] = $res['data'];
                return $result;
            }
            throw new \Exception($res['data']);
        } catch (\Exception $e) {
            $this->logger->write_log('exportReadStatus:出现异常, 原因是'.$e->getMessage(), 'error');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     *
     * @param $remote_message_id
     * @return array
     */
    public function getReaderStaff($remote_message_id, $send_type = 0)
    {
        //根据消息id  获取 已读未读员工 阅读量导出 逻辑
        if(empty($remote_message_id))
            return [];

        $param = ['remote_message_id' => $remote_message_id];
        $read_sql = "select mc.message_content_id as remote_message_id, mc.read_state,mc.staff_info_id, mc.title, mc.read_state
                      from message_courier mc
                      where mc.message_content_id = :remote_message_id";

        $read_result = $this->getDI()->get('db_message_read')->query($read_sql,$param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if(empty($read_result))
            return [];

        // 提取所有员工ID
        $where['staff_ids'] = array_column($read_result,'staff_info_id');

        if($send_type == 13) {//给工具号发送的消息
            $staff_info_result = $this->getStaffToolRelationInfo($where);
        } else {
            $staff_info_result = $this->getReaderStaffRelationInfo($where);
        }

        if(!$staff_info_result) {
            return [];
        }
        $staff_info_result = array_column($staff_info_result, null ,'id');

        //拼接数据
        $data = array();
        foreach ($read_result as $key => $value) {
            $data[$key]['title'] = $value['title'];
            $data[$key]['store_id'] = '(' .$staff_info_result[$value['staff_info_id']]['store_id'].') '.$staff_info_result[$value['staff_info_id']]['store_name'];
            $data[$key]['staff_info_id'] = $value['staff_info_id'];
            $data[$key]['staff_info_name'] = $staff_info_result[$value['staff_info_id']]['name'];
            $data[$key]['read_state'] = $value['read_state'] == 0 ? 'N' : 'Y';
        }
        return $data;
    }

    /**
     * 获取阅读相关信息
     * @param $where
     * @return mixed
     */
    public function getReaderStaffRelationInfo($where)
    {
        $staffInfo = HrStaffInfoModel::find([
            'conditions'=>' staff_info_id in ({staff_info_id:array})',
            'bind'=>['staff_info_id' => $where['staff_ids']],
            'columns'=>'staff_info_id as id,name,sys_store_id',
        ])->toArray();

        $stores          = array_column((new SysStoreService())->getStoreListFromCache(), 'name', 'id');
        $stores    += ['-1' => GlobalEnums::HEAD_OFFICE];

        $result = [];
        foreach ($staffInfo as $oneStaffInfo) {
            $oneData['id'] = $oneStaffInfo['id'];
            $oneData['name'] = $oneStaffInfo['name'];
            $oneData['store_id'] = $oneStaffInfo['sys_store_id'];
            $oneData['store_name'] = $stores[$oneStaffInfo['sys_store_id']] ?? '';
            $result[] = $oneData;
        }

        return $result;
    }

    /**
     * 签名阅读
     * @param $remote_message_id
     * @return array
     */
    public function signMsgExcel($remote_message_id)
    {
        try {

            // 准备Excel表头配置
            $header = [
                self::$t->_('sign_msg_003'), // 消息标题
                self::$t->_('sign_msg_034'), // 网点
                self::$t->_('sign_msg_035'), // 员工ID
                self::$t->_('sign_msg_030'), // 员工姓名
                self::$t->_('sign_msg_036'), // 是否完成
            ];

            $data = $this->get_sign_msg_result($remote_message_id);
            $result = [];
            if(!empty($data)){
                foreach ($data as $v){
                    $row = [];

                    $row[0] = $v['title'];
                    $row[1] = $v['store_name'];
                    $row[2] = $v['staff_id'];
                    $row[3] = $v['staff_name'];
                    $row[4] = $v['sign_status'];

                    $result[] = $row;
                }
            }

            $file_name = "signMsgStatus";
            $res = $this->exportExcelReturn($header, $result ?? [], $file_name);
            if ($res['code'] == 1) {
                $result['code'] = ErrCode::SUCCESS;
                $result['message'] = 'success';
                $result['data']['file_url'] = $res['data'];
                return $result;
            }
            throw new \Exception($res['data']);
        } catch (\Exception $e) {
            $this->logger->write_log('signMsgExcel:出现异常, 原因是' . $e->getMessage(), 'error');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 获取签字消息完成情况
     * @param $remote_message_id string  远端消息ID
     * @return $result array             结果列表
     */
    public function get_sign_msg_result($remote_message_id){
        if (empty($remote_message_id)) {
            return [];
        }

        $msg_sql = "SELECT message_content_id AS remote_message_id, read_state, staff_info_id, title
                     FROM message_courier
                     WHERE message_content_id = :remote_message_id";
        $msg_param = ['remote_message_id' => $remote_message_id];

        $msg_list = $this->getDI()->get('db_message_read')->query($msg_sql, $msg_param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($msg_list)) {
            return [];
        }

        // 提取所有员工ID
        $where['staff_ids'] = array_column($msg_list, 'staff_info_id');
        $staff_list = $this->getReaderStaffRelationInfo($where);
        if(!$staff_list) {
            return [];
        }
        $staff_list = array_column($staff_list, null ,'id');

        //拼接数据
        $data = [];
        foreach ($msg_list as $key => $value) {
            $data[$key]['title'] = $value['title'];

            $curr_staff_info = isset($staff_list[$value['staff_info_id']]) ? $staff_list[$value['staff_info_id']] : [];
            if (!empty($curr_staff_info)) {
                $data[$key]['store_name'] = '(' .$curr_staff_info['store_id'].') '.$curr_staff_info['store_name'];
                $data[$key]['staff_name'] = $curr_staff_info['name'];
            } else {
                $data[$key]['store_name'] = '';
                $data[$key]['staff_name'] = '';
            }

            // 员工ID
            $data[$key]['staff_id'] = $value['staff_info_id'];
            $data[$key]['sign_status'] = $value['read_state'] ? 'Y' : 'N';
        }

        return $data;
    }
    /*
     * 答题消息题目顺序处理
     * @param $question_content
     *
     * @return $result
     */
    public function shuffleQuestionLibOption($question_content = '')
    {
        return $question_content;
        if (empty($question_content)) {
            return '';
        }

        $question_lib = json_decode($question_content, true);

        // 对question顺序打散
        shuffle($question_lib['question']);
//        shuffle($question_lib['question']);

        return json_encode($question_lib, JSON_UNESCAPED_UNICODE);
    }

    public function checkIsTop($category, $category_code): bool
    {
        return false;
        if (in_array($category, [6, 34])) {
            return true;
        }
        if ($category == 33 && in_array($category_code, [4, 10, 13])) {
            return true;
        }
        if ($category == 0 && in_array($category_code, [0, 6])) {
            return true;
        }
        return false;
    }

    //最多指定 4条消息 $db_connection 包括 bi库链接 和 coupon库链接 array('db' => $bi_db, 'db_coupon' => $message_db);
    public function topMessage4($db_connection)
    {
        $sql = "SELECT id,remote_message_id,top_status FROM message WHERE top_status = 1 and category in (0,6,33,34) ORDER BY updated_at DESC LIMIT 100 OFFSET 4";
        while (($tops = $db_connection['db']->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC)) && !empty($tops)) {
            foreach ($tops as $msg) {
                if ($db_connection['db']->execute("UPDATE message SET top_status = 3  WHERE id = {$msg['id']}") === false) {
                    return false;
                }
                if ($db_connection['db_message']->execute("UPDATE message_courier SET top_state = 0  WHERE message_content_id = '{$msg['remote_message_id']}'") === false) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 根据message.id批量统计阅读量，发送人数
     * @param array $ids
     * @return array
     */
    public function countReadByIds(array $ids): array
    {

        if (empty($ids)) {
            return [];
        }
        $ids = array_unique($ids);
        sort($ids);
        $in = str_repeat('?,', count($ids, 1) - 1) . '?';
        $sql = '--
            SELECT `message_content_id`,sum(read_state) AS `read_num`, count(*) as `staff_num` 
            FROM `message_courier` 
            WHERE 
                `message_content_id` IN(' . $in . ') 
            GROUP BY `message_content_id`
            ';
        $db = $this->getDI()->get('db_message_read');
        $count = $db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, $ids);
        return empty($count) ? [] : array_column($count, null, 'message_content_id');
    }

    /**
     * 通过读取文件的头部来确认文件的格式，防止人为强制修改后缀
     * @param $filename
     * @return false|string
     */
    public function checkFileHeader($filename)
    {
        $filepath = realpath($filename);
        $filetype = [
            '8075' => 'xlsx',
            //'208207'  => 'xls'
        ];
        if (!($fp = @fopen($filepath, 'rb'))) {
            return false;
        }
        $bin = fread($fp, 2);
        fclose($fp);
        $str_info = @unpack('C2chars', $bin);
        $str_code = intval($str_info['chars1'].$str_info['chars2']);
        return $filetype[$str_code] ?? false;
    }

    /**
     * 解析excel
     * @param $file
     * @return void
     * @throws ValidationException
     */
    public function analysisStaffInfoExcel($params)
    {
        $file = $params['file'];
        // 获取拥有 按模板发送 权限的人员id
        $permissionUserIds = (new SettingEnvService())->getHrisTemplateSendMessagePermission();
        if ((is_bool($permissionUserIds) && !$permissionUserIds) || !in_array($params['user_info']['id'],
                $permissionUserIds)) {
            // 无权操作
            throw new ValidationException(self::$t->_("no_permission"));
        }

        // 字符串类型解析
        $excel           = new \Vtiful\Kernel\Excel(['path' => '']);
        $read_excel_data = $excel->openFile($file->getTempName())->openSheet()->setType([
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
            \Vtiful\Kernel\Excel::TYPE_STRING,
        ])->getSheetData();
        if (empty($read_excel_data)) {
            // 文件不能为空
            throw new ValidationException(self::$t->_("file_content_is_null"));
        }

        // 头部信息 ,暂时最大支持 $maxColumns 列
        $header     = array_shift($read_excel_data);
        $maxColumns = MessageEnums::MAX_COLUMNS;
        if (count($header) > $maxColumns) {
            //该文件最多支持 $maxColumns 列
            throw new ValidationException(self::$t->_("max_column",
                ['num' => $maxColumns]));
        }

        // 只是写了头部信息，没有内容
        if (empty($read_excel_data)) {
            // 文件不能为空
            throw new ValidationException(self::$t->_("file_content_is_null"));
        }

        $headCode = ord('A');
        foreach ($header as $key => $val) {
            //转换字符第一个字节为int类型
            $code = ord(strtoupper(trim($val)));
            if ($headCode != $code) {
                throw new ValidationException(self::$t->_("file_header_fail"));
            }
            // 位数 + 1 获取下一个字母
            $headCode++;
        }

        // 开始-数据处理
        // [1] 去重
        $unique_staff_ids = [];
        $staff_info       = [];
        $maxColumnWidth   = MessageEnums::MAX_COLUMN_WIDTH;
        foreach ($read_excel_data as $key => $val) {
            // 工号列去除下空格
            $val[0] = trim($val[0]);
            // 检查临时变量，如果存在重复则剔除，保证重复数据第一条有效
            if (in_array($val[0], $unique_staff_ids)) {
                unset($read_excel_data[$key]);
            } elseif (preg_match("/^\d*$/", $val[0]) && $val[0] > 0) {
                // 对一行数据的每列做长度验证，如果超过$maxColumnWidth，产品要求截取处理，
                foreach ($val as $k => $v) {
                    if ($maxColumnWidth < mb_strlen($v, 'UTF-8')) {
                        $val[$k] = mb_substr($v, 0, $maxColumnWidth, 'UTF-8');
                    }
                }
                // staff_id列 如果不是int类型,则不放入查询db的数组中
                // 不存在重复的staff_id放入临时变量
                $unique_staff_ids[]  = $val[0];
                $staff_info[$val[0]] = $val;
            }
        }

        if (empty($staff_info)) {
            // 空数据 直接返回
            return [
                'data' => [
                    'oss_id' => 0,
                    'nums'   => 0,
                ],
            ];
        }

        // 去重之后的数据进行分块处理
        $groupList = array_chunk($staff_info, 50, true);
        // [2] 验证staff_ids 是否有效
        $staffInfoServiceObj = new StaffInfoService();
        $write_excel_data    = [];
        $insert_detail_data  = [];
        foreach ($groupList as $items) {
            $staff_ids = array_keys($items);
            // 读取员工信息表，在职、待离职
            $staff_info_form_db = $staffInfoServiceObj->getStaffInfoJobAndLeave($staff_ids);
            $staff_info_list    = array_column($staff_info_form_db, null, 'staff_info_id');
            foreach ($items as $val) {
                if (!empty($staff_info_list[$val[0]])) {
                    $write_excel_data[]   = $val;
                    $insert_detail_data[] = [
                        'staff_info_id'        => $val[0],
                        'staff_info_attribute' => json_encode(array_combine($header, $val), JSON_UNESCAPED_UNICODE),
                        'send_staff_id'        => $params['user_info']['id'],
                    ];
                }
            }
            // 释放
            unset($staff_ids, $staff_info_form_db, $staff_info_list);
        }

        if (empty($write_excel_data) || empty($insert_detail_data)) {
            // 空数据 直接返回
            return [
                'data' => [
                    'oss_id' => 0,
                    'nums'   => 0,
                ],
            ];
        }

        // 拿到原始文件的名字
        $upload_filename = substr($file->getName(), 0, strrpos($file->getName(), '.'));
        // 拿到原始文件的扩展名
        $extend = $file->getExtension();
        // 重命名
        $file_name = $upload_filename.'-'.date("YmdHis").'-'.random_int(10000, 99999).'.'.$extend;
        // 上传到 /var/tmp/xxx
        $excelWrite = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
        $filePath   = $excelWrite->fileName($file_name)->header($header)->data($write_excel_data)->output();

        //上传oss
        $flashOss  = new FlashOss();
        $ossObject = 'send-message-staff-info/'.date('Y-m-d').'/'.$file_name;
        $flashOss->uploadFile($ossObject, $filePath);
        //var_dump($flashOss->signUrl($ossObject));

        //开启事务
        $db = BackyardBaseModel::beginTransaction($this);

        // 插入 message_oss_file 表
        $insert_oss_file_data = [
            'oss_path'      => $ossObject,
            'send_staff_id' => $params['user_info']['id'],
        ];
        $insert_oss_file_ret  = $db->insertAsDict("message_oss_file", $insert_oss_file_data);
        if (!$insert_oss_file_ret) {
            $this->logger->error("Insert message_oss_file fail Result:".$insert_oss_file_ret." Params:".json_encode($insert_oss_file_data,
                    JSON_UNESCAPED_UNICODE));
            $db->rollBack();
        }
        // 获取lastId
        $oss_file_id = $db->lastInsertId();

        // 插入message_identity_detail表
        // oss_file_id id 插入到数组中
        array_walk($insert_detail_data, function (&$val, $key, $param) {
            $val[$param['key']] = $param['val'];
        }, ['key' => 'oss_file_id', 'val' => $oss_file_id]);

        $obj               = new MessageIdentityDetailModel();
        $insert_detail_ret = $obj->batch_insert($insert_detail_data, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        if (!$insert_detail_ret) {
            $this->logger->error("Insert message_identity_detail fail Result:".$insert_detail_ret." Params:".json_encode($insert_detail_data,
                    JSON_UNESCAPED_UNICODE));
            $db->rollBack();
        }
        $db->commit();
        // 返回
        $res = [
            'data' => [
                'oss_id' => (int)$oss_file_id,
                'nums'   => count($insert_detail_data),
            ],
        ];
        return $res;
    }

    /**
     * oss 文件下载
     * @param $params
     * @param $timeOut
     * @return false|\OSS\Http\ResponseCore|string
     * @throws ValidationException
     */
    public function downloadStaffInfoExcel($params, $time_out = 10)
    {
        if (!is_array($params) || empty($params['oss_id'])) {
            return false;
        }

        // 获取拥有 按模板发送 权限的人员id
//        $permissionUserIds = (new SettingEnvService())->getHrisTemplateSendMessagePermission();
//        if ((is_bool($permissionUserIds) && !$permissionUserIds) || !in_array($params['user_info']['id'],
//                $permissionUserIds)) {
//            // 无权操作
//            throw new ValidationException(self::$t->_("no_permission"));
//        }

        $oss_file = (new MessageOssFileService())->getOssFileInfoByParams([
            'id'     => $params['oss_id'],
            'is_del' => 0,
        ], ['id', 'oss_path', 'message_id', 'remote_message_id']);
        if (empty($oss_file)) {
            throw new ValidationException(self::$t->_("file_download_fail"));
        }

        // 验证是否 勾选了仅发送人、审批人可见的权限
        $params['id'] = $oss_file[0]['message_id'];
        $bool         = $this->msgCanView($params);
        if (is_bool($bool) && !$bool) {
            throw new ValidationException(self::$t->_("no_permission"));
        }

        $flashOss = new FlashOss();
        return $flashOss->signUrl($oss_file[0]['oss_path'], $time_out);
    }

    /**
     * oss 文件删除
     * @param $params
     * @return false|void
     */
    public function delStaffInfoExcel($params)
    {
        if (!is_array($params) || empty($params['oss_id'])) {
            return false;
        }

        // 获取拥有 按模板发送 权限的人员id
        $permissionUserIds = (new SettingEnvService())->getHrisTemplateSendMessagePermission();
        if ((is_bool($permissionUserIds) && !$permissionUserIds) || !in_array($params['user_info']['id'],
                $permissionUserIds)) {
            // 无权操作
            throw new ValidationException(self::$t->_("no_permission"));
        }

        // 查询验证 验证
        $oss_file = (new MessageOssFileService())->getOssFileInfoByParams([
            'id'     => $params['oss_id'],
            'is_del' => 0,
        ], ['id', 'oss_path']);
        if (empty($oss_file)) {
            // 没有数据 直接返回
            throw new ValidationException(self::$t->_("unknown_data"));
        }

        //开启事务
        $db          = BackyardBaseModel::beginTransaction($this);
        $delIdentRet = (new MessageIdentityDetailService())->delMessageIdentityDetailByOssFileId($params['oss_id']);
        if (!$delIdentRet) {
            $this->logger->write_log(__CLASS__." delStaffInfoExcel error ".json_encode(['delIdentRet' => $delIdentRet],
                    JSON_UNESCAPED_UNICODE), 'info');
            // 回滚
            $db->rollBack();
        }

        $delOssFileRet = (new MessageOssFileService)->delMessageOssFileById($params['oss_id']);
        if (!$delOssFileRet) {
            $this->logger->write_log(__CLASS__." delStaffInfoExcel error".json_encode(['delOssFileRet' => $delOssFileRet],
                    JSON_UNESCAPED_UNICODE), 'info');
            // 回滚
            $db->rollBack();
        }
        // 提交
        $db->commit();
        return true;
    }

    public function getSendMessageTemplate()
    {
        return 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1673492125-14e80e97f4324716820382b2e54f9d1c.xlsx';
    }

    /**
     * 是否是签字协议
     * @param $messageInfo
     * @return bool
     */
    public function isArgeementSign($messageInfo)
    {
        return (isCountry('MY') && $messageInfo['category'] == MessageEnums::CATEGORY_SIGN && in_array($messageInfo['category_code'],
                [
                    MessageEnums::CATEGORY_SIGN_CODE_AGREEMENT,
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_INDEPENDENT,
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT,
                ]));
    }

    /**
     * 再次发送
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function onceAgain($params)
    {
        $messageInfo = $this->getMessageInfo($params);
        if (empty($messageInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        $this->validationPermission($messageInfo,$params['staff_id']);

        $messageInfo['current_time'] = gmdate('Y-m-d H:i:s');
        $staffInfoIds = $this->getOnceSendStaff($messageInfo);

        $flag = $this->sendMessage($messageInfo, $staffInfoIds);

        $update_data['again_time']       = $messageInfo['current_time'];
        $update_data['real_send_time']   = $messageInfo['current_time'];
        $update_data['staff_info_ids']   = $messageInfo['staff_info_ids'] . ',' . implode(',', $staffInfoIds);
        $update_data['staff_num']        = $messageInfo['staff_num'] + count($staffInfoIds);
        $update_data['last_edit_userid'] = $params['staff_id'];

        $db = $this->getDI()->get('db_backyard');
        if ($flag) {
            $this->logger->info("msg_once_send 发送消息 staff_ids：" . json_encode($staffInfoIds, JSON_UNESCAPED_UNICODE));
            $db->updateAsDict('message', $update_data, ["conditions" => "id = ?", 'bind' => $messageInfo['id']]);
            return true;
        }

        throw new ValidationException(self::$t->_('data_error'));
    }

    /**
     * @param $messageInfo
     * @param $staff_info_id
     * @return true
     * @throws ValidationException
     */
    protected function validationPermission($messageInfo,$staff_info_id): bool
    {
        // 1 仅发送人和审批人可见 0 全部可见
        if (MessageEnums::MESSAGE_VISIBLE_SEND_APPROVAL == $messageInfo['visible'] && !empty($staff_info_id)) {
            // 发送人
            $visible = [$messageInfo['add_userid']];
            // 审批人
            if (!empty($messageInfo['approver_id'])) {
                $visible = array_merge($visible, explode(',', $messageInfo['approver_id']));
            }
            if (!in_array($staff_info_id, $visible)) {
                // 仅发送人、审批人可见
                throw new ValidationException(self::$t->_('no_have_permission'));
            }
        }
        return true;
    }



    /**
     * 检查是否可以再次发送
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function onceAgainCheck($params)
    {
        $messageInfo = $this->getMessageInfo($params);
        if (empty($messageInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        $this->validationPermission($messageInfo,$params['staff_id']);

        $messageInfo['current_time'] = gmdate('Y-m-d H:i:s');
        $staffInfoIds = $this->getOnceSendStaff($messageInfo);
        $staffNum = count($staffInfoIds);

        $org_info = $messageInfo['to_group'];
        $send_type_text = self::$t->_('msg.send_position');
        if($messageInfo['send_type'] == MessageEnums::SEND_TYPE_ORG) {
            $org_info_arr = explode(';', $org_info);
            $org_info = $org_info_arr[0];
            $send_type_text = self::$t->_('msg.send_organizational');

        }

        $data['staff_num'] = intval($staffNum);
        $data['is_send']   = $data['staff_num'] > 0 ? true : false;
        $data['org_info']  = $org_info;
        $data['send_type'] = $send_type_text;
        $data['time']      = date('Y-m-d H:i:s', strtotime($messageInfo['real_send_time'])  + $this->config->application->add_hour * 3600);

        return $data;
    }

    /**
     * 获取消息信息
     * @param $params
     * @return array
     */
    public function getMessageInfo($params)
    {
        $messageInfo = MessageModel::findFirst([
            'conditions' => 'id=:id:',
            'bind'       => ['id' => $params['msg_id']],
        ]);

        return empty($messageInfo) ? [] : $messageInfo->toArray();
    }

    /**
     * 获取发送员工信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getOnceSendStaff($params)
    {
        if (empty($params['create_params'])) {
            throw new ValidationException('create_params '.self::$t->_('data_error'));
        }

        if (!in_array($params['send_type'], [MessageEnums::SEND_TYPE_JOB, MessageEnums::SEND_TYPE_ORG])) {
            throw new ValidationException('send_type '.self::$t->_('data_error'));
        }

        if (!in_array($params['category'], [MessageEnums::CATEGORY_GENERAL, MessageEnums::CATEGORY_SIGN])) {
            throw new ValidationException('send_type '.self::$t->_('data_error'));
        }

        $params['create_params'] = json_decode($params['create_params'], true);

        switch ($params['send_type']) {
            case MessageEnums::SEND_TYPE_JOB;//按职位
                $staffInfoIds = $this->getStaffIdsByJobTitle($params);
                break;
            case MessageEnums::SEND_TYPE_ORG;//按组织
                $staffInfoIds = $this->getStaffIdsByOrg($params,BuildMessageService::init());
                break;

            default:
                $staffInfoIds = [];
        }

        $allStaffIds = explode(',', $params['staff_info_ids']);
        $sendStaffIds = array_diff(array_values(array_unique($staffInfoIds)), $allStaffIds);

        return array_values(array_unique($sendStaffIds));
    }

    /**
     * 根据职位 获取 新入职员工id
     * @param $params
     * @return array
     */
    public function getStaffIdsByJobTitle($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.staff_info_id',
        ]);

        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->where('s.formal in ({formal:array}) AND s.state in ({state:array}) AND s.is_sub_staff = 0', [
            'formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
            'state'  => [Enums::HRIS_WORKING_STATE_1, Enums::HRIS_WORKING_STATE_3],
        ]);
        $builder->andWhere('s.created_at > :start_again_time: and s.created_at <= :end_again_time:',
            ['start_again_time' => $params['again_time'], 'end_again_time' => $params['current_time']]);
        
        if (!empty($params['hire_type_list'])) {
            $builder->inWhere('s.hire_type', json_decode($params['hire_type_list'], true));
        }
        $relationInfo = (new DepartmentService())->getJobIdsDepartmentRelationById($params['create_params']);
        $whereString  = [];
        foreach ($relationInfo as $oneRelation) {
            $whereString[] = '( s.job_title = '.$oneRelation['job_id'].' and s.node_department_id = '.$oneRelation['department_id'].')';
        }
        if (empty($whereString)) {
            return [];
        }
        $where = implode(' OR ', $whereString);
        $this->logger->write_log('job_title_where:'.$where, 'info');
        $builder->andWhere($where);
        $staffInfo = $builder->getQuery()->execute()->toArray();

        if (empty($staffInfo)) {
            return [];
        }
        return array_column($staffInfo, 'staff_info_id');
    }

    /**
     * 根据组织信息获取员工id
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffIdsByOrg($params,BuildMessageService $buildMessageService)
    {
        $params['hire_type_list'] && $buildMessageService->setHireTypeList(json_decode($params['hire_type_list'],
            true));
        $params['job_title_list'] && $buildMessageService->setJobTitleList(json_decode($params['job_title_list'],
            true));
        $params['store_category_list'] && $buildMessageService->setStoreCategoryList(json_decode($params['store_category_list'],
            true));
        $params['hire_date_list'] && $buildMessageService->setHireDateList(json_decode($params['hire_date_list'],
            true));

        $params['contract_company_ids'] && $buildMessageService->setContractCompanyIds(json_decode($params['contract_company_ids'],
            true));

        $params['position_types'] && $buildMessageService->setContractCompanyIds(json_decode($params['position_types'],
            true));
        $sendData = !empty($params['create_params']['select_organizations']) ? $params['create_params']['select_organizations'] : $params['create_params']['select_send_list'];

        $buildMessageService->again_time   = $params['again_time'];
        $buildMessageService->current_time = $params['current_time'];
        foreach ($sendData as $item) {
            switch ($item['current_type']) {
                case MessageEnums::ORGANIZATION_TYPE_DEPARTMENT://给下面的全部子节点发
                    $buildMessageService->dealDepartmentToStaff($item);
                    break;
                case MessageEnums::ORGANIZATION_TYPE_REGION://给大区下的网点的员工
                    $buildMessageService->dealRegionToStaff($item);//
                    break;
                case MessageEnums::ORGANIZATION_TYPE_PIECE://当前片区下网点的员工
                    $buildMessageService->dealPieceToStaff($item);
                    break;
                case MessageEnums::ORGANIZATION_TYPE_STORE:
                    $buildMessageService->dealStoreToStaff($item);//当前网点的员工
                    break;
                case MessageEnums::ORGANIZATION_TYPE_STAFF:
                    //$buildMessageService->dealFireToStaff($item);//指定工号
                    break;
                default:
                    throw new ValidationException("params current_type invalid");
            }
        }
        return $buildMessageService->getStaff();
    }

    /**
     * 发送消息
     * @param $msg_info
     * @param $staff_info_ids
     * @return bool
     */
    public function sendMessage($msg_info, $staff_info_ids)
    {
        if (empty($staff_info_ids)) {
            $this->logger->notice("msg_once_send 发送消息 msg_id：{$msg_info['id']}, 发送人数为空");
            return false;
        }
        $message_db = $this->getDI()->get('db_message');
        try {
            $message_db->begin();

            $top               = ($msg_info['top_status'] == 1) ? $msg_info['top_status'] : 0;
            $staff_users       = $staff_info_ids;
            $remote_message_id = $msg_info['remote_message_id'];

            $default_category      = $msg_info['category'];
            $default_category_code = 0;

            $title = addslashes($msg_info['title']);

            $staffs = array_chunk($staff_users, 2000);

            $msgContent = MessageContentModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $remote_message_id],
            ]);
            if(empty($msgContent)) {
                $model = new MessageContentModel();
                //消息内容表
                $sql_param                  = [
                    'id'         => $remote_message_id,
                    'message'    => addslashes($msg_info['content']),
                ];

                $model->insert_record($sql_param);
            }

            //分批插入
            foreach ($staffs as $batchStaffs) {
                $message_courier_data = '';
                foreach ($batchStaffs as $staff_id) {
                    $id                   = time().$staff_id.rand(1000000, 9999999);
                    $message_courier_data .= '(';
                    $message_courier_data .= $id;                       //id
                    $message_courier_data .= ','.$staff_id;             //工号
                    $message_courier_data .= ','."'{$title}'";          //title
                    $message_courier_data .= ','.$default_category;     //category
                    $message_courier_data .= ','.$default_category_code;//category_code
                    $message_courier_data .= ','.$top;                  //top_state
                    //$message_courier_data .= ','. 1;                    //push_state
                    //is_sync_sub 是否同步子账号：1否，2是
                    if ($msg_info['is_sync_sub'] == 2) {
                        $message_courier_data .= ',' . 2;
                    } else {
                        $message_courier_data .= ',' . 1;
                    }

                    $message_courier_data .= ','. 1;                //source_type
                    $message_courier_data .= ','.$id;               //mns_message_id
                    $message_courier_data .= ','.$remote_message_id;//message_content_id
                    $message_courier_data .= '),';
                }

                $message_courier_data           = rtrim($message_courier_data, ',');
                $message_db_message_courier_sql = "INSERT INTO message_courier (id,staff_info_id,title,category,category_code,top_state,push_state,source_type,mns_message_id,message_content_id) 
                                                          VALUES {$message_courier_data}";
                $message_db->execute($message_db_message_courier_sql);
            }

            $message_db->commit();
            $this->logger->info("msg_once_send 发送消息：{$remote_message_id} 成功");
            return true;
        } catch (\Exception $e) {
            $message_db->rollback();
            $this->logger->error("msg_once_send 发送消息 失败 msg_id：" . $msg_info['id'] . ',message:' .$e->getMessage().' '.$e->getTraceAsString().' '.$e->getLine());
            return false;
        }
    }

    /**
     * 部门职位关联-名称
     * @return array
     */
    public function getDepartmentJobRelateAll()
    {
        $relationInfo = HrJobDepartmentRelationModel::find([
            'columns'    => ['id', 'job_id', 'department_id'],
        ])->toArray();

        $jobTitle = HrJobTitleModel::find([
            'columns'    => ['id', 'job_name'],
        ])->toArray();

        $departmentInfo = SysDepartmentModel::find([
            'columns'    => ['id', 'name'],
        ])->toArray();

        $relationInfoToId = array_column($relationInfo, NULL, 'id');
        $jobTitleToId = array_column($jobTitle, 'job_name', 'id');
        $departmentInfoToId = array_column($departmentInfo, 'name', 'id');

        $relationInfoToIdToName = [];
        foreach ($relationInfoToId as $oneRelation) {
            if(!isset($jobTitleToId[$oneRelation['job_id']]) || !isset($departmentInfoToId[$oneRelation['department_id']])) {
                continue;
            }
            $relationInfoToIdToName[$jobTitleToId[$oneRelation['job_id']] . '(' . $departmentInfoToId[$oneRelation['department_id']] . ')'] = $oneRelation['id'];
        }

        return $relationInfoToIdToName;
    }

    public function getMessage($id)
    {
        if(empty($id)) {
            return [];
        }

        $data = MessageModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);

        return !empty($data) ? $data->toArray() : [];
    }




    /**
     * 消息审批通过
     * @param array $param
     * @return true
     * @throws BusinessException
     */
    public function syncAuditStatus(array $param)
    {
        $message_id   = $param['message_id'] ?? 0;
        $audit_status = $param['audit_status'] == 2 ? $param['audit_status'] : 0;

        if (empty($message_id) || empty($audit_status)) {
            throw new \Exception('参数错误' . json_encode($param, JSON_UNESCAPED_UNICODE));
        }

        $by_db     = $this->getDI()->get('db_backyard');
        $message_db = $this->getDI()->get('db_message');

        // 开启事务
        $by_db->begin();
        $message_db->begin();

        try {

            // [1] 获取消息
            $message_model = MessageModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $message_id],
            ]);

            if (empty($message_model)) {
                throw new \Exception('消息不存在' . $message_model);
            }

            // 已有终态, 不可覆盖
            if (in_array($message_model->audit_status, [2, 3, 4, 5])) {
                return true;
            }

            // 如果是问卷消息，message_content 为空, 跳转路由在backyard端输出时处理
            if (!empty($message_model->questionnaire_lib_id)) {
                $remote_message_content = '';
            } else {
                $remote_message_content = addslashes($message_model->content);
            }

            // 提取答题消息题库
            $answer_lib_content = '';
            if ($message_model->category == 34) {
                $lib_info = (new QuestionnaireLibService())->read_lib(['lib_id' => $message_model->questionnaire_lib_id]);
                if ($lib_info['qn_lib_type'] == 3 && !$lib_info['is_del']) {
                    $answer_lib_content = $lib_info['question'];
                }
            }

            // 是否需要立即发送
            $is_send   = false;
            $curr_time = gmdate('Y-m-d H:i:s', time() + ($this->timeOffset) * 3600);
            if (empty($message_model->set_time) || $message_model->set_time <= $curr_time) {
                $is_send = true;
            }


            // 1 同步审批终态
            $message_model->audit_status = $audit_status;

            $this->getDI()->get('logger')->write_log("syncAuditStatus audit_status:{$audit_status}  is_send:{$is_send} id:{$message_model->id}",
                'info');

            if ($is_send) {
                $message_model->publish_status = 6;
            }

            if ($message_model->save() === false) {
                throw new \Exception('消息终态更新失败, id = ' . $message_model->id . ', audit_status = ' . $audit_status);
            }

            // 2 消息发送
            if ($is_send) {
                $this->getDI()->get('logger')->write_log("syncAuditStatus  send  id:{$message_model->id}", 'info');

                //消息内容表
                $message_db_message_content_sql   = '
                    -- 消息分发:insert message_content
                    INSERT INTO
                        message_content (id, message) 
                    VALUES 
                        (?, ?);';
                $message_db_message_content_param = [
                    $message_model->remote_message_id,
                    $remote_message_content,
                ];
                $message_db_message_content       = $message_db->execute($message_db_message_content_sql,
                    $message_db_message_content_param);
                $this->getDI()->get('logger')->write_log("syncAuditStatus  insert coupon_db_message_content  result:" . print_r($message_db_message_content,
                        true), 'info');

                if (!$message_db_message_content) {
                    throw new \Exception('消息分发, message_content 写入失败, insert sql = ' . $message_db_message_content_sql . ', insert data = ' . json_encode($message_db_message_content_param,
                            JSON_UNESCAPED_UNICODE));
                }

                // 消息员工表
                // 如果是答题消息，且发送人数超过500，则临时设置执行时间
                if ($message_model->category == 34 && $message_model->staff_num > 500) {
                    set_time_limit(0);
                    ini_set('memory_limit', '8192M');
                }

                $title = addslashes($message_model->title);

                $staff_users = explode(',', $message_model->staff_info_ids);
                $staffs      = array_chunk($staff_users, 2000);
                //分批插入
                foreach ($staffs as $batchStaffs) {
                    $message_courier_data = '';
                    $questionAnswerData   = '';//问卷消息答题记录
                    foreach ($batchStaffs as $staff_id) {
                        $id                   = time() . $staff_id . rand(1000000, 9999999);
                        $message_courier_data .= '(';
                        $message_courier_data .= $id;
                        $message_courier_data .= ',' . $staff_id;
                        $message_courier_data .= ',' . "'$title'";
                        $message_courier_data .= ',' . $message_model->category;
                        $message_courier_data .= ',' . $message_model->category_code;

                        if ($message_model->top_status == 1) {
                            $message_courier_data .= ',' . 1;
                        } else {
                            $message_courier_data .= ',' . 0;
                        }
                        //is_sync_sub 是否同步子账号：1否，2是
                        if ($message_model->is_sync_sub == 2) {
                            $message_courier_data .= ',' . 2;
                        } else {
                            $message_courier_data .= ',' . 1;
                        }
                        $message_courier_data .= ',' . 1;
                        $message_courier_data .= ',' . $id;
                        $message_courier_data .= ',' . $message_model->remote_message_id;
                        $message_courier_data .= ',' . $message_model->by_show_type;
                        $message_courier_data .= '),';

                        // 拼接答题消息员工端题库内容
                        if (!empty($answer_lib_content)) {
                            $answer_lib_content         = $this->shuffleQuestionLibOption($answer_lib_content);
                            $insert_staff_questions_sql = '
                            -- 消息分发: 员工答题数据入库
                            INSERT INTO 
                                questionnaire_answer (remote_message_id, question_content, staff_info_id, create_time) 
                             VALUES 
                                (?, ?, ?, ?);';

                            $insert_staff_questions = [
                                $message_model->remote_message_id,
                                $answer_lib_content,
                                $staff_id,
                                gmdate('Y-m-d H:i:s', time() + ($this->timeOffset) * 3600),
                            ];

                            if (!$by_db->execute($insert_staff_questions_sql, $insert_staff_questions)) {
                                throw new \Exception('消息分发, questionnaire_answer 写入失败, insert sql = ' . $insert_staff_questions_sql . ', insert data = ' . json_encode($insert_staff_questions,
                                        JSON_UNESCAPED_UNICODE));
                            }
                        }
                        if ($message_model->category == 6 && $message_model->category_code == 3) {//问卷消息
                            $questionAnswerData .= '(';
                            $questionAnswerData .= $message_model->remote_message_id;
                            $questionAnswerData .= ',' . $staff_id;
                            $questionAnswerData .= '),';
                        }
                    }

                    if (!empty($questionAnswerData)) {//问卷消息，发布时创建回答记录
                        $questionAnswerData             = rtrim($questionAnswerData, ',');
                        $bi_db_questionnaire_answer_sql = "
                        -- 消息分发: insert questionnaire_answer
                        INSERT INTO 
                            questionnaire_answer 
                            (remote_message_id, staff_info_id) 
                        VALUES 
                            $questionAnswerData ;";
                        $result                         = $by_db->execute($bi_db_questionnaire_answer_sql);
                        if (!$result) {
                            throw new \Exception('消息分发,问卷消息, questionnaire_answer 写入失败, insert sql = ' . $bi_db_questionnaire_answer_sql);
                        }
                    }

                    $message_courier_data = rtrim($message_courier_data, ',');

                    $message_db_message_courier_sql    = "
                    -- 消息分发: insert message_courier
                    INSERT INTO 
                        message_courier 
                        (id, staff_info_id, title, category, category_code, top_state, push_state, source_type, mns_message_id, message_content_id, by_show_type) 
                    VALUES 
                        $message_courier_data ;";
                    $message_db_message_courier_result = $message_db->execute($message_db_message_courier_sql);
                    if (!$message_db_message_courier_result) {
                        throw new \Exception('消息分发, message_courier 写入失败, insert sql = ' . $message_db_message_courier_sql);
                    }
                }
            }

            $by_db->commit();
            $message_db->commit();
            return true;
        } catch (\Exception $e) {
            $by_db->rollback();
            $message_db->rollback();
            $this->getDI()->get('logger')->write_log('syncAuditStatus - end, 同步消息审批状态失败, 原因可能是: ' . $e->getMessage());
            throw new BusinessException(self::$t->_('server_error'));
        }
    }

    /**
     * by 用户签字完成，立即生成pdf
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function makeMessageSignPdfPushRedis($locale, $params)
    {
        $this->getDi()->get('logger')->write_log(['makeMessageSignPdfPushRedis' => $params], "info");

        self::setLanguage($locale['locale']);

        if(empty($params)) {
            throw new ValidationException('params error');
        }
        $params['num'] = 0;//重试次数
        $content = json_encode($params, JSON_UNESCAPED_UNICODE);

        $redis = $this->getDI()->get("redis");
        $res = $redis->lpush(MessageEnums::REDIS_MESSAGE_SIGN_PDF_LIST, $content);

        if(!$res) {
            $this->getDi()->get('logger')->write_log(['makeMessageSignPdfPushRedis' => $params], "notice");
            throw new ValidationException(self::$t->_('server_error') . ": makeMessageSignPdfPushRedis redis lpush 失败");
        }

        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => []];
    }

    /**
     * by 签字消息完成，立即生成pdf
     * @return bool
     */
    public function consumerMakeSignPdf()
    {
        $redis    = $this->getDI()->get("redis");
        $i = 0;
        while ($dataJson = $redis->rpop(MessageEnums::REDIS_MESSAGE_SIGN_PDF_LIST)){
            $i++;
            if ($i > 500) {
                $redis->rpush(MessageEnums::REDIS_MESSAGE_SIGN_PDF_LIST, $dataJson);
                break;
            }
            echo "数据: " . $dataJson . PHP_EOL;
            $data = json_decode($dataJson, true);

            if($data['num'] > 3) {
                break;
            }

            $params['staff_info_id'] = $data['staff_info_id'];
            $params['remote_message_id'] = $data['remote_message_id'];
            $this->makeSignPdfPermission = false;//不是redis生成pdf : false
            $res = $this->makeSignMsgPdf($params);
            if($res['code'] == ErrCode::SUCCESS) {
                continue;
            }

            $this->getDi()->get('logger')->write_log(['consumerMakeSignPdf-fail' => ['params' => $dataJson, 'res' => $res]], "notice");
            $data['num']++;//重试次数
            $dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
            $redis->lpush(MessageEnums::REDIS_MESSAGE_SIGN_PDF_LIST, $dataJson);
            gc_collect_cycles();
        }

    }


    /**
     * 发送站内信
     * @param $local
     * @param $params
     * @return array
     */
    public function sendStaffMessage($local,$params): array
    {
        $result = [
            'code'    => 1,
            'data'    => [],
            'message' => 'success',
        ];
        // 开启事务
        $message_db = $this->getDI()->get('db_message');
        $message_db->begin();

        try {
            if (empty($params['staff_info_id'])) {
                throw new ValidationException('need [staff_info_id]');
            }
            $staff_info_id = $params['staff_info_id'];

            if (empty($params['title'])) {
                throw new ValidationException('need 【title]');
            }
            $title = stripslashes($params['title']);

            if (empty($params['category'])) {
                throw new ValidationException('need  [category]');
            }
            $category = $params['category'];

            if (empty($params['content'])) {
                throw new ValidationException('need [content]');
            }
            if ($category == -1) {
                $content = "<div style='font-size: 40px'>" . $params['content'] . "</div>";
            } else {
                $content = $params['content'];
            }

            $sub_category = 0;
            if (!empty($params['sub_category'])) {
                $sub_category = $params['sub_category'];
            }

            $is_top = 0;
            if (!empty($params['is_top'])) {
                $is_top = 1;
            }

            $related_id = '';
            if (!empty($params['related_id'])) {
                $related_id = $params['related_id'];
            }

            $id = empty($params['id']) ? time() . $staff_info_id . mt_rand(1000000, 9999999) : $params['id'];


            $courierModel                     = new MessageCourierModel();
            $courierModel->id                 = $id;
            $courierModel->title              = $title;
            $courierModel->staff_info_id      = $staff_info_id;
            $courierModel->category           = $category;
            $courierModel->category_code      = $sub_category;
            $courierModel->top_state          = $is_top;
            $courierModel->message_content_id = $id;
            $courierModel->created_at         = gmdate('Y-m-d H:i:s');
            $courierModel->save();
            $contentModel                     = new MessageContentModel();
            $contentModel->id                 = $id;
            $contentModel->message            = $content;
            $contentModel->message_courier_id = $id;
            $contentModel->related_id         = $related_id;
            $contentModel->created_at         = gmdate('Y-m-d H:i:s');
            $contentModel->save();

            $message_db->commit();
            return $result;
        } catch (ValidationException $ve) {
            $message_db->rollback();
            $result['code']    = 0;
            $result['message'] = $ve->getMessage();
            return $result;
        } catch (Exception $exception) {
            $message_db->rollback();
            $this->logger->error([
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ]);
            $result['code']    = 0;
            $result['message'] = self::$t->_('server_error');
            return $result;
        }
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同
     * 根据消息id获取外协仓管自动发送合同签署消息
     * @param string $message_id 消息id
     * @return string
     */
    public function getOsStaffContract($message_id)
    {
        $info = OsStaffContractSignRecordModel::findFirst([
            'conditions' => 'message_id = :message_id:',
            'bind' => ['message_id' => $message_id],
        ]);
        return $info ? $info->toArray() : [];
    }

    /**
     * 异步生成pdf,合成签字
     * @param $local
     * @param $params
     * @return array
     */
    public function getMessageHotNum($local, $params)
    {
        if (empty($params['staff_info_id'])) {
            $this->logger->error(['function' => 'getMessageHotNum', 'params' => $params]);
            return ['code' => ErrCode::VALIDATE_ERROR, 'msg' => 'params error', 'data' => []];
        }

        $data['num'] = 0;
        $data['staff_info_id'] = $params['staff_info_id'];

        $where['staff_info_id'] = $params['staff_info_id'];
        $where['read_state'] = MessageCourierModel::READ_STATE_NO;
        $currentMessageData = MessageRepository::getCourierOne($where, ['count(1) as num']);

        $data['num'] = !empty($currentMessageData['num']) ? $data['num'] + intval($currentMessageData['num']) : $data['num'];
        $supportStaffInfo = (new StaffSupportStoreServer())->getSupportInfoBySubStaff($params['staff_info_id']);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $where['staff_info_id'] = $supportStaffInfo['staff_info_id'];
            $where['push_state'] = MessageCourierModel::PUSH_STATE_TO_SUB_YES;
            $masterMessageData = MessageRepository::getCourierOne($where, ['count(1) as num']);
            $data['num'] = !empty($masterMessageData['num']) ? $data['num'] + intval($masterMessageData['num']) : $data['num'];
        }

        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => $data,
        ];
    }

    /**
     * 签字消息-异步下载
     * @param $params
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws ValidationException
     */
    public function staff_sign_pdf($params)
    {
        $download_pdf_permission = (new SettingEnvService())->getSetVal('message_sign_download_pdf', ',');
        if(!in_array($params['user_id'], $download_pdf_permission)) {
            throw new ValidationException(self::$t->_('no_have_permission'));
        }

        $staff_info_ids = empty($params['staff_info_ids']) ? [] : explode(',', $params['staff_info_ids']);
        if (empty($staff_info_ids)) {
            throw new ValidationException(self::$t->_('msg_qn_053'));
        }

        if(count($staff_info_ids) > 10) {
            throw new ValidationException(self::$t->_('staff_num_limit'));
        }

        if(strtotime($params['start_send_date']) > strtotime($params['end_send_date'])) {
            throw new ValidationException(self::$t->_('send_date_limit_error'));
        }
        $staffInfoList    = HrStaffInfoRepository::getHrStaffByIds($staff_info_ids);
        //未找到工号
        if(empty($staffInfoList)) {
            throw new ValidationException(self::$t->_('not_isset_staff_ids',
                ['staff_ids' => implode(',', $staff_info_ids)]));
        }
        $staffInfoListIds = array_column($staffInfoList, 'staff_info_id');
        $notStaffId       = [];
        foreach ($staff_info_ids as $staffInfoId) {
            if (!in_array($staffInfoId, $staffInfoListIds)) {
                $notStaffId[] = $staffInfoId;
            }
        }
        if (!empty($notStaffId)) {
            throw new ValidationException(self::$t->_('not_isset_staff_ids',
                ['staff_ids' => implode(',', $notStaffId)]));
        }
        $params['staff_info_ids'] = implode(',', array_values(array_unique($staff_info_ids)));
        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "message" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "staff_sign_pdf";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['user_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $headerData     = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && in_array($headerData['From'],
            ['fbi']) ? $headerData['From'] : '';

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['user_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        if (!empty($hcmExcelTaskId) && in_array($params['From'], ['fbi'])) {
            (new BllService())->addFbiExportMessage(
                $params['user_id'],
                'hcm_message_staff_sign_pdf',
                $params['file_name'],
                $hcmExcelTaskId
            );
        }

        return $hcmExcelTaskId;
    }

    public function staffSignPdfExport($params, $file_name)
    {
        $staff_info_ids   = empty($params['staff_info_ids']) ? [] : explode(',', $params['staff_info_ids']);
        $staffInfoListAll = [];
        $staffInfoList    = HrStaffInfoRepository::getHrStaffByIds($staff_info_ids, ['staff_info_id', 'name', 'hire_date', 'leave_date', 'state']);
        if (!empty($staffInfoList)) {
            $staffInfoListAll = array_merge($staffInfoListAll, $staffInfoList);
        }
        $staffInfoListTool = HrStaffInfoRepository::getToolStaffInfoList(['staff_info_ids' => $staff_info_ids], ['staff_info_id', 'name', 'hire_date', 'leave_date', 'state']);
        if (!empty($staffInfoListTool)) {
            $staffInfoListAll = array_merge($staffInfoListAll, $staffInfoListTool);
        }
        $staffInfoListAllToId = array_column($staffInfoListAll, 'name', 'staff_info_id');
        $messageRepository = new MessageRepository();

        $where['category']        = MessageEnums::CATEGORY_SIGN;
        $category_codes = [MessageEnums::CATEGORY_SIGN_CODE, MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE];
        if(isCountry(['MY'])) {
            $category_codes = array_merge($category_codes, [MessageEnums::CATEGORY_SIGN_CODE_CONTRACT_EXPIRE]);
        }
        $where['category_codes']  = $category_codes;

        $backup_db    = $this->getDI()->get('db_backup');

        $allData = [];
        $this->makeSignPdfPermission = false;
        foreach ($staffInfoListAll as $staffInfo) {
            $where['start_send_date'] = strtotime($params['start_send_date']) > strtotime($staffInfo['hire_date']) ? $params['start_send_date'] : date('Y-m-d', strtotime($staffInfo['hire_date']));
            $where['end_send_date']   = $staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN && !empty($staffInfo['leave_date']) && (strtotime($params['end_send_date']) > strtotime($staffInfo['leave_date'])) ? date('Y-m-d', strtotime($staffInfo['leave_date'])) : $params['end_send_date'];
            $where['staff_info_id'] = $staffInfo['staff_info_id'];
            $signList            = $messageRepository->getMessageCourierList($where,
                'message_content_id AS remote_message_id, read_state, staff_info_id, title, category, category_code, updated_at');
            $this->logger->info(['sign_message_download_getYearsBetweenDates' => $staffInfo['staff_info_id']]);
            $years = $this->getYearsBetweenDates($where['start_send_date'], $where['end_send_date']);
            //检验表是否存在 创建
            $start_send_date = gmdate('Y-m-d H:i:s', strtotime($params['start_send_date'].' 00:00:00'));
            $end_send_date = gmdate('Y-m-d H:i:s', strtotime($params['end_send_date'].' 23:59:59'));

            foreach ($years as $year) {
                $m_table = 'message_courier_'.$year;
                $exist = "show tables like '{$m_table}'";
                $info  = $backup_db->query($exist)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (empty($info)) {
                    continue;
                }
                $category_codes = implode(',', $where['category_codes']);
                $sql  = "select message_content_id AS remote_message_id, read_state, staff_info_id, title, category, category_code, updated_at from {$m_table} where staff_info_id = :staff_info_id and category = :category and category_code in ({$category_codes}) and created_at >= :start_send_date and created_at <= :end_send_date";
                $bind['staff_info_id'] = $staffInfo['staff_info_id'];
                $bind['category'] = $where['category'];
                $bind['start_send_date'] = $start_send_date;
                $bind['end_send_date'] = $end_send_date;
                $signListBack = $backup_db->query($sql, $bind)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if(!empty($signListBack)) {
                    $signList = array_merge($signList, $signListBack);
                }
            }
            $server = reBuildCountryInstance($this);
            foreach ($signList as $sign) {
                $makeParams['staff_info_id'] = $sign['staff_info_id'];
                $makeParams['remote_message_id'] = $sign['remote_message_id'];
                $pdfInfo = $server->makeSignMsgPdf($makeParams);
                $ondData                    = [];
                $ondData[]                  = $sign['staff_info_id'];
                $ondData[]                  = $staffInfoListAllToId[$sign['staff_info_id']] ?? "";
                $ondData[]                  = $sign['title'];
                if (isset($pdfInfo['code']) && $pdfInfo['code'] == ErrCode::SUCCESS) {
                    $ondData[] = self::$t->_('signed');
                    $ondData[]                  = show_time_zone($sign['updated_at']);
                    $ondData[] = $pdfInfo['data']['file_url'];

                } else {
                    $ondData[] = self::$t->_('unsigned');
                    $ondData[] = '';
                    $ondData[] = '';
                }
                $allData[] = $ondData;
            }
        }
        // 生成表头
        $header = [
            self::$t->_('staff_info_id'), //工号
            self::$t->_('name'), // 姓名
            self::$t->_('msg_title_01'), // 消息标题
            self::$t->_('sign_state'), // 签字状态
            self::$t->_('signature_time'), // 签字时间
            self::$t->_('pdf_url'), // 签字时间
        ];
        return $this->exportExcel($header, $allData, $file_name);
    }

    /**
     * 列出开始日期到结束日期所在的年份
     * @param string $startDate 开始日期 (格式: Y-m-d)
     * @param string $endDate 结束日期 (格式: Y-m-d)
     * @param bool $excludeCurrentYear 是否排除当前年份，默认false
     * @return array 年份数组
     * @throws ValidationException
     */
    public function getYearsBetweenDates($startDate, $endDate, $excludeCurrentYear = false)
    {
        // 验证日期格式
        if (!$this->isValidDate($startDate) || !$this->isValidDate($endDate)) {
            $this->logger->info(['sign_message_download_getYearsBetweenDates' => '日期格式不正确, 请使用 Y-m-d 格式', 'params' => [$startDate, $endDate]]);
            return [];
        }

        // 验证开始日期不能大于结束日期
        if (strtotime($startDate) > strtotime($endDate)) {
            $this->logger->info(['sign_message_download_getYearsBetweenDates' => '开始日期不能大于结束日期', 'params' => [$startDate, $endDate]]);
            return [];
        }

        $startYear = (int)date('Y', strtotime($startDate));
        if($startYear < 2019) {
            $startYear = 2019;
        }
        $endYear = (int)date('Y', strtotime($endDate));
        $currentYear = (int)date('Y');

        $years = [];
        for ($year = $startYear; $year <= $endYear; $year++) {
            // 如果需要排除当前年份，则跳过当前年
            if ($excludeCurrentYear && $year === $currentYear) {
                continue;
            }
            $years[] = $year;
        }

        return $years;
    }

    /**
     * 验证日期格式是否为 Y-m-d
     * @param string $date 日期字符串
     * @return bool
     */
    private function isValidDate($date)
    {
        $dateTime = \DateTime::createFromFormat('Y-m-d', $date);
        return $dateTime && $dateTime->format('Y-m-d') === $date;
    }
}