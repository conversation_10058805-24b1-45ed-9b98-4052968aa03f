<?php
/**
 * Author: Bruce
 * Date  : 2023-08-12 23:17
 * Description:
 */

namespace App\Services;


use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrShiftStoreConfigModel;
use App\Repository\HrShiftDepartmentConfigRepository;
use App\Repository\HrShiftRegionPieceConfigRepository;
use App\Repository\HrShiftRepository;
use App\Repository\HrShiftStoreConfigRepository;
use App\Repository\SysStoreRepository;

class ShiftConfigService extends BaseService
{
    /**
     *
     * @param $params
     * @return array|mixed
     * @throws ValidationException
     */
    public function getShiftConfigList($params)
    {
        $store_ids      = array_values(array_unique(array_column($params, 'store')));
        $job_title_ids  = array_values(array_unique(array_column($params, 'job_title')));
        $department_ids = array_values(array_unique(array_column($params, 'department_id')));
        $formal_ids = array_values(array_unique(array_column($params, 'formal')));
        if (count($job_title_ids) > 1) {
            //多个职位
            throw new ValidationException(self::$t->_('shift_error'));
        }
        $os_half_shift_list = [];
        if (isCountry('PH')) {
            if (count($formal_ids) > 1) {
                //多个员工类型
                throw new ValidationException(self::$t->_('shift_error_1'));
            }
            if (isset($formal_ids[0]) && $formal_ids[0] == '0' && isset($job_title_ids[0])) {
                $os_half_shift_position = (new SettingEnvService())->getSetVal('os_half_shift_position', ',');
                if (!empty($os_half_shift_position) && in_array($job_title_ids[0], $os_half_shift_position)) {
                    $os_half_shift_list = $this->get_shift_group_half_day_shift();
                }
            }
        }
        $first_job_title = $job_title_ids[0];

        if (in_array(GlobalEnums::HEAD_OFFICE_ID, $store_ids)) {
            $shift_list = $this->getHeadOfficeShiftList($store_ids, $department_ids, $first_job_title);
            $shift_list = $this->get_shift_and_half_day_shift($os_half_shift_list,$shift_list);
            return $shift_list;
        }

        //指定网点设置班次，是否都是同一网点
        $is_store = count($store_ids) == 1 ? true : false;
        //是同一个网点
        if ($is_store) {
            $shift_list = $this->getStoreShiftList($store_ids, $first_job_title);
            if ($shift_list) {
                $shift_list = $this->get_shift_and_half_day_shift($os_half_shift_list,$shift_list);
                return $shift_list;
            }
        }

        //判断是否同一个大区和片区
        $sys_store_list = SysStoreRepository::getStoreByIds($store_ids, false,
            ['id', 'manage_piece', 'manage_region', 'category']);
        $manage_piece   = array_values(array_unique(array_column($sys_store_list, 'manage_piece')));
        $manage_region  = array_values(array_unique(array_column($sys_store_list, 'manage_region')));
        $category       = array_values(array_unique(array_column($sys_store_list, 'category')));

        $is_piece = count($manage_piece) == 1 && count($manage_region) == 1 ? true : false;
        if ($is_piece) {
            $shift_list = $this->getPieceShiftList($manage_region, $manage_piece, $first_job_title);
            if ($shift_list) {
                $shift_list = $this->get_shift_and_half_day_shift($os_half_shift_list,$shift_list);
                return $shift_list;
            }
        }

        //判断是否同一部门职位网点类型
        $shift_list_dep = $this->getStoreDepartmentShift([
            'department_ids' => $department_ids,
            'job_title_ids'  => $job_title_ids,
            'categorys'      => $category,
        ]);
        $is_dep     = $shift_list_dep['is_dep'] ?? false;
        $shift_list = $shift_list_dep['list'] ?? [];
        if (!empty($shift_list)) {
            $shift_ids  = array_column($shift_list, 'shift_id');
            $shift_list = $this->shiftsToTypeByShiftIds($shift_ids);
            $shift_list = $this->get_shift_and_half_day_shift($os_half_shift_list,$shift_list);
            return $shift_list;
        }
        //代表走进了网点或者片区 但是没有找到 返回所有的
        if ($is_piece || $is_store || $is_dep) {
            $shift_list = (new SysService())->shiftsToType();//获取所有班次
            $shift_list = $this->get_shift_and_half_day_shift($os_half_shift_list,$shift_list);
            return $shift_list;
        }
        throw new ValidationException(self::$t->_('shift_config_error_1'));
    }
    
    public function get_shift_and_half_day_shift($os_half_shift_list,$shift_list)
    {
        if (isCountry('PH')){
            if (isset($os_half_shift_list['EARLY']) || isset($shift_list['EARLY'])){
                $shift_list['EARLY'] = empty($os_half_shift_list['EARLY']) ?  $shift_list['EARLY'] ?? [] : array_merge($os_half_shift_list['EARLY'],$shift_list['EARLY'] ?? []);
            }
            if (isset($os_half_shift_list['MIDDLE']) || isset($shift_list['MIDDLE'])){
                $shift_list['MIDDLE'] = empty($os_half_shift_list['MIDDLE']) ?  $shift_list['MIDDLE'] ?? [] : array_merge($os_half_shift_list['MIDDLE'],$shift_list['MIDDLE'] ?? []);
            }
            if (isset($os_half_shift_list['NIGHT']) || isset($shift_list['NIGHT'])){
                $shift_list['NIGHT'] = empty($os_half_shift_list['NIGHT']) ?  $shift_list['NIGHT'] ?? [] : array_merge($os_half_shift_list['NIGHT'],$shift_list['NIGHT'] ?? []);
            }
        }
        return $shift_list;
    }

    /**
     * 获取外指定职位的外协半天班次
     * @return array
     */
    public function get_shift_group_half_day_shift(): array
    {
        $params['shift_group_half_day_shift'] = true;
        $params['shift_attendance_type']      = HrShift::SHIFT_ATTENDANCE_TYPE_FIXED;
        return (new SysService())->shiftsToType($params);
    }

    public function getStoreDepartmentShift($params)
    {
        return [];
    }

    /**
     * 获取总部 班次
     * @param $store_ids
     * @param $department_ids
     * @param $first_job_title
     * @return array|mixed
     * @throws ValidationException
     */
    public function getHeadOfficeShiftList($store_ids, $department_ids, $first_job_title)
    {
        //既有总部的 也有网点的是不允许的
        if (count($store_ids) > 1) {
            throw new ValidationException(self::$t->_('shift_config_error_3'));
        }
        //按照总部规则查询 是否同一部门员工，否则不让设置
        if (count($department_ids) != 1) {
            throw new ValidationException(self::$t->_('shift_config_error_4'));
        }
        $first_department_id = $department_ids[0];
        $shift_list          = HrShiftDepartmentConfigRepository::getDepartmentShiftList($first_department_id,
            $first_job_title);
        if (empty($shift_list)) {
            $shift_list = (new SysService())->shiftsToType();//获取所有班次
        } else {
            $shift_ids  = array_column($shift_list, 'shift_id');
            $shift_list = $this->shiftsToTypeByShiftIds($shift_ids);
        }
        return $shift_list;
    }

    /**
     * 获取 网点 班次
     * @param $store_ids
     * @param $first_job_title
     * @return array
     */
    public function getStoreShiftList($store_ids, $first_job_title)
    {
        $shift_list = HrShiftStoreConfigRepository::getStoreShiftList($store_ids[0], $first_job_title);
        if (empty($shift_list)) {
            return [];
        }
        $shift_ids  = array_column($shift_list, 'shift_id');
        $shift_list = $this->shiftsToTypeByShiftIds($shift_ids);

        return $shift_list;
    }

    /**
     * 获取片区 班次
     * @param $manage_region
     * @param $manage_piece
     * @param $first_job_title
     * @return array
     */
    public function getPieceShiftList($manage_region, $manage_piece, $first_job_title)
    {
        $shift_list = HrShiftRegionPieceConfigRepository::getRegionPieceShiftList($manage_region[0], $manage_piece[0],
            $first_job_title);
        if (empty($shift_list)) {
            return [];
        }
        $shift_ids  = array_column($shift_list, 'shift_id');
        $shift_list = $this->shiftsToTypeByShiftIds($shift_ids);
        return $shift_list;
    }

    /**
     * 班次信息 type 分组 通过 班次id 查询
     * @param $shift_ids
     * @return array
     */
    public function shiftsToTypeByShiftIds($shift_ids)
    {
        $list   = HrShiftRepository::getShiftData($shift_ids);
        $result = [];
        foreach ($list as $oneData) {
            $result[$oneData['type']][] = [
                'start'  => $oneData['start'],
                'end'    => $oneData['end'],
                'id'     => $oneData['id'],
                'type'   => $oneData['type'],
                'markup' => '(' . self::$t->_('shift_' . strtolower($oneData['type'])) . ') ' . $oneData['start'] . '-' . $oneData['end'],
            ];
        }
        return $result;
    }

    public function getStoreDepartmentShiftList($params)
    {
        return [];
    }

    //根据部门职位网点获取配置的班次信息
    public function getStaffConfigShiftList($department_id, $job_title_id, $store_id)
    {
        //总部员工班次
        if ($store_id == '-1') {
            $shift_list = HrShiftDepartmentConfigRepository::getDepartmentShiftList($department_id, $job_title_id);
            return $shift_list;
        }
        //按照网点设置
        $shift_list = HrShiftStoreConfigModel::getStoreShiftList($store_id, $job_title_id);
        if (!empty($shift_list)) {
            return $shift_list;
        }

        //网点没配置 按照大区片区职位设置
        $storeInfo    = SysStoreRepository::getStoreById($store_id);
        $first_region = $storeInfo['manage_region'] ?? '';
        $first_piece  = $storeInfo['manage_piece'] ?? '';
        $shift_list   = HrShiftRegionPieceConfigRepository::getRegionPieceShiftList($first_region, $first_piece,$job_title_id);

        if(!empty($shift_list)) {
            return $shift_list;
        }

        $shift_list_data = $this->getStoreDepartmentShiftList(
            ['department_id' => $department_id,
             'job_title_id'  => $job_title_id,
             'category'      => $storeInfo['category']]
        );
        $shift_list = $shift_list_data['list'] ?? [];

        return $shift_list;
    }
}