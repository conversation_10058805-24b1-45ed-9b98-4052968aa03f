<?php
/**
 * Author: Bruce
 * Date  : 2023-08-08 17:23
 * Description:
 */

namespace App\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftMiddleDateModel;
use App\Models\backyard\HrStaffShiftModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffShiftPresetModel;
use App\Models\backyard\OutsourcingOvertimeModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Repository\HrShiftRepository;
use App\Repository\HrStaffInfoPositionRepository;
use App\Repository\HrStaffInfoRepository;
use App\Repository\HrStaffShiftHistoryRepository;
use App\Repository\HrStaffShiftMiddleDateRepository;
use App\Repository\HrStaffShiftPresetRepository;
use App\Repository\HrStaffShiftRepository;
use App\Repository\SysProvinceRepository;
use App\Repository\SysStoreRepository;
use App\Traits\FreeShiftTrait;
use Exception;
use App\Library\Enums\ConditionsRulesEnums;

use function Hprose\Promise\co;

class StaffShiftSettingService extends BaseService
{
    use FreeShiftTrait;

    const LIMIT_DOWNLOAD_NUM = 300000;


    public function createShiftAfterEntry($staff_info_id)
    {
        $staffShift = HrStaffShiftModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staff_info_id],
        ]);
        if (empty($staffShift)) {
            $this->logger->error('staff not find shit from hr_staff_shift  staff_info_id is ' . $staff_info_id);
            return false;
        }
        $start_date = date('Y-m-d');
        $startDay   = strtotime($start_date);
        $end_date   = date("Y-m-t", strtotime(date('Y-m-01', $startDay) . " + 2 months"));
        $endDay     = strtotime($end_date);
        $dateList   = DateHelper::DateRange($startDay, $endDay);
        $insertData = [];
        foreach ($dateList as $date) {
            $row['shift_id']      = $staffShift->shift_id;
            $row['staff_info_id'] = $staff_info_id;
            $row['shift_date']    = $date;
            $row['shift_type']    = $staffShift->shift_type;
            $row['shift_start']   = $staffShift->start;
            $row['shift_end']     = $staffShift->end;
            $insertData[]         = $row;
        }
        if ($insertData) {
            $db_by       = $this->getDI()->get('db_backyard');
            $bind = [
                'staff_id'   => $staff_info_id,
                'start_date' => $start_date,
                'end_date'   => $end_date,
            ];
            $db_by->execute("update hr_staff_shift_middle_date set  deleted = 1 where staff_info_id =:staff_id and shift_date >=:start_date and shift_date <=:end_date ",$bind);
            (new HrStaffShiftMiddleDateModel())->batch_insert($insertData);
        }
        return true;
    }

    /**
     * 给前端 是否是主管
     * @param $params
     * @return mixed
     */
    public function isManager($params)
    {
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($params['staff_info_id'], ['job_title']);
        if (empty($staffInfo)) {
            $data['is_master'] = false;
            return $data;
        }

        $data['is_master'] = !$this->checkSettingPermission($staffInfo);
        return $data;
    }

    /**
     *
     * @param $params
     * @return bool
     */
    public function checkSettingPermission($params)
    {
        return true;
    }

    /**
     * 获取列表数据
     * @param $params
     * @return array
     */
    public function getStaffList($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];

        $isCount = empty($params['is_download']);

        if ($isCount) {
            $total         = $this->getShiftListQuery($params, true);
            $data['total'] = !empty($total) ? intval($total['count']) : 0;

            if (empty($data['total'])) {
                return ['total' => 0, 'list' => []];
            }
        }

        if (isset($params['is_check_data_num']) && $params['is_check_data_num'] == true) {
            return $data;
        }
        $list         = $this->getShiftListQuery($params);
        $data['list'] = $this->formatList($list, $params['month']);

        return $data;
    }


    /**
     * 格式化列表数据
     * @param $list
     * @param $month
     * @return mixed
     */
    public function formatList($list, $month)
    {
        if(empty($list)) {
            return [];
        }
        $staffIds = array_column($list, 'staff_info_id');

        $hrStaffShift          = HrStaffShiftRepository::getShift('staff_info_id,[start] as shift_start,[end] as shift_end, shift_type , shift_id, working_day',
            'staff_info_id in ({ids:array})', ['ids' => $staffIds]);
        $hrStaffShiftToStaffId = array_column($hrStaffShift, null, 'staff_info_id');

        $staffIdRoles       = HrStaffInfoPositionRepository::getStaffsBatch($staffIds);
        $staffRolesIdToName = (new StaffInfoService())->getStaffRolesName($staffIdRoles);

        $staffsShifts = $this->getstaffsShift($staffIds, $month);

        $sysService         = new SysService();
        $departmentInfoToId = array_column($sysService->getDepartmentListFromCache(), 'name', 'id');

        $jobTitleId       = array_values(array_unique(array_column($list, 'job_title')));
        $jobTitleInfo     = (new DepartmentService())->getJobList('', $jobTitleId, true);
        $jobTitleInfoToId = !empty($jobTitleInfo) ? array_column($jobTitleInfo, 'name', 'id') : [];


        $sysStoreId  = array_values(array_unique(array_column($list, 'sys_store_id')));
        $storeList   = (new SysStoreService())->getStoreListByIds($sysStoreId);
        $storeList[] = [
            'id'            => -1,
            'store_name'    => GlobalEnums::HEAD_OFFICE,
            'region_name'   => '',
            'piece_name'    => '',
            'province_code' => '',
        ];

        $storeListToId = $storeList ? array_column($storeList, null, 'id') : [];

        $provinceInfo       = SysProvinceRepository::getSysProvinceAll(['code', 'manage_geography_code']);
        $provinceInfoToCode = array_column($provinceInfo, 'manage_geography_code', 'code');

        //弹性班次
        $free_shift_config     = SettingEnvModel::getMultiEnvByCode([
            'free_shift_position',
            'day_shift_duration',
            'night_shift_duration',
        ]);
        $this->setFreeShiftInfo($free_shift_config);
        $emptyShiftList = $this->getEmptyShiftList($month);
        foreach ($list as &$one) {
            $state = $one['state'] ?? 0;
            if ($one['state'] == HrStaffInfoModel::STATE_ON_JOB && $one['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
            }
            $one['state_text']             = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';
            $one['position_category']      = isset($staffIdRoles[$one['staff_info_id']]) ? explode(',',
                $staffIdRoles[$one['staff_info_id']]) : [];
            $one['position_category_text'] = $staffRolesIdToName[$one['staff_info_id']]??'';
            $one['region_name']            = isset($storeListToId[$one['sys_store_id']]) ? $storeListToId[$one['sys_store_id']]['region_name'] : '';
            $one['piece_name']             = isset($storeListToId[$one['sys_store_id']]) ? $storeListToId[$one['sys_store_id']]['piece_name'] : '';
            $one['sys_store_name']         = isset($storeListToId[$one['sys_store_id']]) ? $storeListToId[$one['sys_store_id']]['store_name'] : '';
            $one['department_name']        = $one['node_department_id'] ? ($departmentInfoToId[$one['node_department_id']] ?? "") : ($departmentInfoToId[$one['sys_department_id']] ?? "");
            $one['sys_department_name']    = $departmentInfoToId[$one['sys_department_id']] ?? '';
            $one['node_department_name']   = $departmentInfoToId[$one['node_department_id']] ?? '';
            $one['store_area']             = isset($storeListToId[$one['sys_store_id']]) ? $this->getStoreArea($storeListToId[$one['sys_store_id']],
                $provinceInfoToCode) : '';
            $one['job_title_name']         = $jobTitleInfoToId[$one['job_title']] ?? '';
            $one['shift_type']             = $hrStaffShiftToStaffId[$one['staff_info_id']]['shift_type'] ?? '';
            $one['start']                  = $hrStaffShiftToStaffId[$one['staff_info_id']]['shift_start'] ?? '';
            $one['end']                    = $hrStaffShiftToStaffId[$one['staff_info_id']]['shift_end'] ?? '';
            $one['shift_id']               = $hrStaffShiftToStaffId[$one['staff_info_id']]['shift_id'] ?? '';
            $one['working_day']            = $hrStaffShiftToStaffId[$one['staff_info_id']]['working_day'] ?? '';
            $one['shifts']                 = $this->isFreeShiftJobTitle($one['job_title']) ? $emptyShiftList : $staffsShifts[$one['staff_info_id']];
            $one['is_edit_disable']        = $this->isFreeShiftJobTitle($one['job_title']);
            $one['hire_type_text']         = self::$t->_('hire_type_'.$one['hire_type']);
            $one['employee_type']          = '';
            if (in_array($one['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
                $one['employee_type'] = $this->getStaffType(Enums::HRIS_STAFF_TYPE_1);
            } else {
                if (in_array($one['staff_type'], [
                    HrStaffInfoModel::STAFF_TYPE_1,
                    HrStaffInfoModel::STAFF_TYPE_2,
                    HrStaffInfoModel::STAFF_TYPE_3,
                ])) {
                    $one['employee_type'] = $this->getStaffType(Enums::HRIS_STAFF_TYPE_2);
                }
            }
        }

        return $list;
    }

    private function getStaffType($staff_type)
    {
        if (isCountry('MY')) {
            $staffTypeList = Enums::$hris_staff_type_my;
        } else {
            $staffTypeList = Enums::$hris_staff_type_common;
        }
        return isset($staffTypeList[$staff_type])
            ? self::$t->_($staffTypeList[$staff_type])
            : '';
    }

    /**
     * 获取所属区域
     * @param $storeInfo
     * @param $data
     * @return string
     */
    public function getStoreArea($storeInfo, $data = [])
    {
        return empty($data[$storeInfo['province_code']]) ? '' : (GlobalEnums::$areas[$data[$storeInfo['province_code']]] ?? '');
    }

    /**
     * 构建 列表查询
     * @param $params
     * @param bool $isCount
     * @return array
     */
    public function getShiftListQuery($params, $isCount = false)
    {
        $columns = [
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.node_department_id',
            'hsi.sys_department_id',
            'hsi.job_title',
            'hsi.sys_store_id',
            'hsi.state',
            'hsi.formal',
            'hsi.staff_type',
            'hsi.hire_type',
            'hsi.wait_leave_state',
        ];
        if ($isCount) {
            $columns = 'count(*) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->where('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO]);

        $builder = $this->getBuilderWhere($builder, $params);
        if ($builder == false) {
            return [];
        }

        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));

        $builder->orderBy('hsi.hire_date desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 构建查询条件
     * @param $builder
     * @param $params
     * @return bool|mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        //员工id or name
        $builder = $this->getWhereByName($builder, $params);
        if (!$builder) {
            return false;
        }
        //员工类型
        $builder = $this->getWhereByFormalType($builder, $params);

        //在职状态
        $builder = $this->getWhereByState($builder, $params);

        //大区、片区
        $builder = $this->getWhereByRegionPiece($builder, $params);

        //所属区域
        $builder = $this->getWhereByAreaBuild($builder, $params);

        //网点
        if (!empty($params['store'])) {
            $builder->andWhere('hsi.sys_store_id = :store_id:', ['store_id' => $params['store']]);
        }

        //部门
        if (!empty($params['node_department_id'])) {
            $builder->andWhere('hsi.node_department_id = :department_id:',
                ['department_id' => $params['node_department_id']]);
        }

        //职位
        if (!empty($params['job_title'])) {
            $builder->andWhere('hsi.job_title = :job_title:', ['job_title' => $params['job_title']]);
        }

        //职位
        if (!empty($params['hire_type'])) {
            $builder->inWhere('hsi.hire_type', $params['hire_type']);
        }
        //班次
        if (!empty($params['shift_id']) && $params['shift_id'] != '-1') {
            $builder->leftJoin(HrStaffShiftModel::class, 'hss.staff_info_id = hsi.staff_info_id', 'hss');
            $builder->andWhere('hss.shift_id = :shift_id:',['shift_id' => $params['shift_id']]);
        }

        //获取登录人可查看的员工的条件：网点、部门 条件员工权限
        //数据权限
        $permissionSql = (new StaffPermissionService())->getStaffDataPermissionSql($params['staff_info_id']);
        if (!empty($permissionSql['builder_sql']) && !empty($permissionSql['builder_bind'])) {
            $builder->andWhere(implode(' or ', $permissionSql['builder_sql']), $permissionSql['builder_bind']);
        }

        return $builder;
    }

    /**
     * 工号姓名
     * @param $builder
     * @param $params
     * @return bool
     */
    public function getWhereByName($builder, $params)
    {
        if (empty($params['name'])) {
            return $builder;
        }
        $name = trim($params['name']);
        if (strpos($name, ',') == false) {
            return $builder->andWhere('(hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword: OR hsi.emp_id LIKE :staff_keyword: OR hsi.name_en LIKE :staff_keyword:)',
                ['staff_keyword' => '%' . $name . '%']);
        }

        $name     = rtrim($name, ',');//去除末尾逗号
        $name_arr = explode(',', $name);
        $staffIds = [];
        foreach ($name_arr as $key => $value) {
            if (empty(trim($value))) {
                continue;
            }
            $staffIds[] = trim($value);
        }
        //为空，则返回查不到信息
        if (empty($staffIds)) {
            return false;
        }
        return $builder->andWhere('hsi.staff_info_id in ({staffIds:array})', ['staffIds' => $staffIds]);
    }

    /**
     * 员工类型
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByFormalType($builder, $params)
    {
        //获取能够进入到
        $svr = new SettingEnvService();
        $job_title_config = $svr->getSetVal('outsource_do_setting_shift_job_title_config', ',');
        if (!empty($params['formal_type'])) {
            if ($params['formal_type'] == Enums::HRIS_STAFF_TYPE_1) {
                //正式员工
                $formal     = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
                $builder->andWhere("hsi.formal in ({formal:array})", ['formal' => $formal]);
            } else if (empty($job_title_config)) { //不存在配置职位，则不展示数据
                $builder->andWhere("1 = 0");
            } else {
                //外协员工
                $formal     = [HrStaffInfoModel::FORMAL_0];
                $staff_type = [
                    HrStaffInfoModel::STAFF_TYPE_1,
                    HrStaffInfoModel::STAFF_TYPE_2,
                    HrStaffInfoModel::STAFF_TYPE_3,
                ];
                $builder->andWhere("hsi.formal in ({formal:array}) and hsi.staff_type in({staff_type:array}) and hsi.job_title IN({job_titles:array})", [
                    'formal'     => $formal,
                    'staff_type' => $staff_type,
                    'job_titles' => $job_title_config,
                ]);
            }
        } else {

            if (empty($job_title_config)) { //没有外协职位，则仅展示正式员工
                $formal     = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
                $builder->andWhere("hsi.formal in ({formal:array})", ['formal' => $formal]);
            } else {
                $formal     = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
                $staff_type = [
                    HrStaffInfoModel::STAFF_TYPE_1,
                    HrStaffInfoModel::STAFF_TYPE_2,
                    HrStaffInfoModel::STAFF_TYPE_3,
                ];
                $builder->andWhere("hsi.formal in ({formal:array}) or hsi.formal = 0 and hsi.staff_type in ({staff_type:array}) 
                    and hsi.job_title IN({job_titles:array})", [
                    'formal'     => $formal,
                    'staff_type' => $staff_type,
                    'job_titles' => $job_title_config,
                ]);
            }
        }

        return $builder;
    }

    /**
     * 在职状态
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByState($builder, $params)
    {

        if (empty($params['state']) || !is_array($params['state'])) {
            return $builder;
        }
        if (in_array(Enums::HRIS_WORKING_STATE_4, $params['state'])) {
            $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state=:state: AND hsi.wait_leave_state=:wait_leave_state:)",
                [
                    'states'           => $params['state'],
                    'state'            => Enums::HRIS_WORKING_STATE_1,
                    'wait_leave_state' => Enums::WAIT_LEAVE_STATE,
                ]);
        } elseif (!in_array(Enums::HRIS_WORKING_STATE_4, $params['state']) && in_array(Enums::HRIS_WORKING_STATE_1,
                $params['state'])) {
            $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state != :wait_leave_state:",
                ['states' => $params['state'], 'wait_leave_state' => Enums::WAIT_LEAVE_STATE]);
        } else {
            $builder->andWhere("hsi.state IN ({states:array})", ['states' => $params['state']]);
        }

        return $builder;
    }

    /**
     * 大区、片区
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByRegionPiece($builder, $params)
    {
        //片区
        $store_ids = [];
        if (!empty($params['piece_id'])) {
            $piece_store_list    = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => ' state = :state: AND manage_piece=:manage_piece:',
                'bind'       => ['state' => SysStoreModel::STATE_1, 'manage_piece' => $params['piece_id']],
            ])->toArray();
            $store_ids           = array_column($piece_store_list, 'id');
            $params['region_id'] = '';// 如果选了片区，则不按大区筛选
        }
        //大区
        if (!empty($params['region_id'])) {
            $region_store_list = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => ' state = :state: AND manage_region=:manage_region:',
                'bind'       => ['state' => SysStoreModel::STATE_1, 'manage_region' => $params['region_id']],
            ])->toArray();
            $store_ids         = array_column($region_store_list, 'id');
        }

        if (!empty($store_ids)) {
            $builder->andWhere('hsi.sys_store_id IN ({store_ids:array})', ['store_ids' => $store_ids]);
        }

        return $builder;
    }

    /**
     * 所属区域
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByAreaBuild($builder, $params)
    {
        if (empty($params['store_area_id'])) {
            return $builder;
        }

        $builder->leftJoin(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss');
        $builder->leftJoin(SysProvinceModel::class, 'ss.province_code = sp.code', 'sp');

        $builder = $this->getWhereByArea($builder, $params);

        return $builder;
    }

    /**
     * 所属区区域查询
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByArea($builder, $params)
    {
        if (empty($params['store_area_id'])) {
            return $builder;
        }
        return $builder->andWhere("sp.manage_geography_code = :code:", ['code' => $params['store_area_id']]);
    }

    protected function getEmptyShiftList($month){
        // 开始结束时间
        $startDay = date("Y-m-01", strtotime($month));
        $endDay   = date("Y-m-" . date("t", strtotime($month)), strtotime($month));
        $dateList = DateHelper::DateRange(strtotime($startDay),strtotime($endDay));
        $result = [];
        foreach ($dateList as $item) {
            $result[$item] = [];
        }
        return $result;
    }

    /**
     * 获取员工的某月所有班次
     * @param $staffIds
     * @param $month
     * @return array
     */
    public function getStaffsShift($staffIds, $month): array
    {
        // 开始结束时间
        $startDay = date("Y-m-01", strtotime($month));
        $endDay   = date("Y-m-" . date("t", strtotime($month)), strtotime($month));

        $dateList = DateHelper::DateRange(strtotime($startDay),strtotime($endDay));
        $historyShift = $this->getHistoryShift($staffIds, $startDay, date('Y-m-d',strtotime('-1 days')));
        $currentShift = $this->getCurrShift($staffIds, $dateList);
        $middleShift  = $this->getMiddleShift($staffIds, $dateList);

        $result = [];
        foreach ($staffIds as $staffId) {
            foreach ($dateList as $day) {
                if ($day < date("Y-m-d")) {
                    $result[$staffId][$day] = $historyShift[$staffId][$day] ?? [];
                    continue;
                }
                //如果有指定天班次的话显示指定天班次
                if (isset($middleShift[$staffId][$day])) {
                    $result[$staffId][$day] = $middleShift[$staffId][$day];
                    continue;
                }
                $result[$staffId][$day] = $currentShift[$staffId][$day]??[];
           }
        }

        return $result;
    }

    /**
     * 尝试获取在开始结束时间段中 hr_staff_shift_history 表的班次
     * @param $staffIds
     * @param $startDay
     * @param $endDay
     * @return array
     */
    public function getHistoryShift($staffIds, $startDay, $endDay)
    {
        $staffIds = array_chunk($staffIds, 500);
        $list     = [];
        foreach ($staffIds as $staffId) {
            $res  = HrStaffShiftHistoryRepository::getStaffShiftHistoryList($staffId, $startDay, $endDay);
            $list = array_merge($list, $res);
        }

        $result = [];
        foreach ($list as $item) {
            $result[$item['staff_info_id']][$item['shift_day']] = [
                "start"      => $item['start'],
                "end"        => $item['end'],
                "shift_type" => $item['shift_type'],
            ];
        }
        return $result;
    }




    /**
     * 尝试获取在开始结束时间段内 hr_staff_shift 表的班次
     * @param $staffIds
     * @param $dataList
     * @return array
     */
    public function getCurrShift($staffIds, $dataList): array
    {
        $list = HrStaffShiftRepository::getShift('staff_info_id,  shift_id, preset_shift_id',
            'staff_info_id in ({ids:array})', ['ids' => $staffIds]);

        $result  = [];
        $hrShift = array_column(HrShiftRepository::getShiftData([],[HrShift::SHIFT_GROUP_FULL_DAY_SHIFT,HrShift::SHIFT_GROUP_HALF_DAY_SHIFT]), null, 'id');
        foreach ($list as $item) {
            foreach ($dataList as $day) {
                $shift_id                             =  $item['shift_id'];
                $result[$item['staff_info_id']][$day] = [
                    "start"      => $hrShift[$shift_id]['start'],
                    "end"        => $hrShift[$shift_id]['end'],
                    "shift_type" => $hrShift[$shift_id]['type'],
                ];
            }
        }
        return $result;
    }


    /**
     * 尝试获取在开始结束时间段内 hr_staff_shift_middle_date 表的班次
     * @param $staffIds
     * @param $dataList
     * @return array
     */
    public function getMiddleShift($staffIds, $dataList): array
    {
        $startDay = $dataList[0];
        $endDay   = $dataList[count($dataList) - 1];

        if(empty($startDay) || empty($endDay)){
            return  [];
        }
        $list     = HrStaffShiftMiddleDateRepository::getStaffShiftMiddleDateList([
            'staff_info_ids' => $staffIds,
            'begin_day'      => $startDay,
            'end_day'        => $endDay,
        ]);

        $result   = [];
        foreach ($list as $item) {
            $result[$item['staff_info_id']][$item['shift_date']] = [
                "start"          => $item['shift_start'],
                "end"            => $item['shift_end'],
                "shift_type"     => $item['shift_type'],
            ];
        }
        return $result;
    }



    /**
     * 获取指定天班次列表,格式化 数据组
     * @param $params
     * @return array
     */
    public function getStaffShiftMiddleDateFormat($params): array
    {
        $data             = [];
        $staff_shift_list = HrStaffShiftMiddleDateRepository::getStaffShiftMiddleDateList($params);
        foreach ($staff_shift_list as $key => $value) {
            $data[$value['staff_info_id']][$value['shift_date']] = [
                'start'      => $value['shift_start'],
                'end'        => $value['shift_end'],
                'shift_type' => $value['shift_type'],
            ];
        }
        return $data;
    }


    /**
     * 生成班次前预览班次
     * @param $params
     * @return array
     */
    public function staffShiftPreview($params): array
    {
        $staffIds      = explode(",", $params['staff_ids']);
        $dateList      = $this->getPresetDateList($params['effective_date']);
        $middleShifts  = $this->getMiddleShift($staffIds, $dateList);
        $currentShifts = $this->getCurrShift($staffIds, $dateList);
        $result        = [];
        foreach ($staffIds as $staffId) {
            foreach ($dateList as $day) {
                //先用每日的
                if (isset($middleShifts[$staffId][$day])) {
                    $result[$staffId][$day] = $middleShifts[$staffId][$day];
                    continue;
                }
                if (isset($currentShifts[$staffId][$day])) {
                    $result[$staffId][$day] = $currentShifts[$staffId][$day];
                    continue;
                }
                $result[$staffId][$day] = [];
            }
        }
        return $result;
    }

    protected function getPresetDateList($effective_date): array
    {

        $startDay = strtotime($effective_date);
        $endDay   = strtotime(date("Y-m-t", strtotime(date('Y-m-01',$startDay) . " + 1 months")));
        return DateHelper::DateRange($startDay, $endDay);
    }


    /**
     * 异常班次的处理
     * @param $staff_info_id
     * @param $dateList
     * @param $shift_id
     * @param $shift_type
     * @param $shift_start
     * @param $shift_end
     * @return void
     * @throws Exception
     */
    protected function fixShiftData($staff_info_id, $dateList, $shift_id, $shift_type, $shift_start, $shift_end)
    {
        $existShift = HrStaffShiftMiddleDateRepository::getStaffShiftMiddleDateList([
            'staff_info_ids' => [$staff_info_id],
            'begin_day'      => min($dateList),
            'end_day'        => max($dateList),
        ]);

        $existShiftDate  = array_column($existShift, 'shift_date');
        $insertShiftDate = array_diff($dateList, $existShiftDate);
        if (!empty($insertShiftDate)) {
            $insertData = [];
            foreach ($insertShiftDate as $date) {
                $row['shift_id']      = $shift_id;
                $row['staff_info_id'] = $staff_info_id;
                $row['shift_date']    = $date;
                $row['shift_type']    = $shift_type;
                $row['shift_start']   = $shift_start;
                $row['shift_end']     = $shift_end;
                $insertData[]         = $row;
            }
            if ($insertData) {
                (new HrStaffShiftMiddleDateModel())->batch_insert($insertData);
            }
        }
    }


    /**
     * 编辑
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function creates($params)
    {
        $end_time     = strtotime(date('Y-m-d 23:30:00'));
        $current_time = strtotime(date('Y-m-d H:i:s'));
        if ($current_time > $end_time) {
            throw new ValidationException(self::$t->_("staff_shift_tip"));
        }

        $body   = $params['data'];
        $result = [];

        $min_date = strtotime('+1 day midnight');
        $max_date = strtotime("+1 month last day of");//下月最后一天

        $index           = 0;
        $staff_info_ids  = array_column($body, 'staff_info_id');
        $staff_info_list = HrStaffInfoRepository::getHrStaffInfoByIds($staff_info_ids);
        $outsourceStaffInfoList = HrStaffInfoRepository::getOutsourceHrStaffInfoByIds($staff_info_ids);

        $staffService = new StaffService();

        $jobTitleList     = array_merge(array_column($staff_info_list, 'job_title'), array_column($outsourceStaffInfoList, 'job_title'));
        $jobTitleId       = array_values(array_unique($jobTitleList));
        $jobTitleInfo     = (new DepartmentService())->getJobList('', $jobTitleId, true);
        $jobTitleInfoToId = !empty($jobTitleInfo) ? array_column($jobTitleInfo, 'name', 'id') : [];

        $date = current(array_column($body,'effective_date'));
        $dateList = $this->getPresetDateList($date);

        //改用新规则
        if($params['staff_info_id'] != 10000 && in_array($params['staff_info_id'], $staff_info_ids)){
            //新增规则配置校验 能否修改自己的休息日
            $ruleParam['staff_info_id'] = $params['staff_info_id'];
            $ruleParam['rule_key']      = 'Allow_set_selfShift';
            $rules                      = $this->getConditionRule(self::$language, $ruleParam);
            //- N or 空：不允许修改自己，给出如下提示
            if (empty($rules['data']) || $rules['data']['response_type'] != ConditionsRulesEnums::RESPONSE_TYPE_VALUE || !$rules['data']['response_data']) {
                throw new ValidationException(self::$t->_('can_not_edit_self_shift'));
            }
        }

        //判断是否有支援
        $supportEnd    = date('Y-m-t', strtotime('+1 month'));
        $supportServer = new StaffSupportStoreServer();
        $count         = $supportServer->validateConflictBatch($date, $supportEnd, $staff_info_ids);
        if (!empty($count)) {
            throw new ValidationException(implode(',', array_keys($count)) . self::$t->_('support_info_exist'));
        }

        //弹性班次
        $free_shift_config     = SettingEnvModel::getMultiEnvByCode([
            'free_shift_position',
            'day_shift_duration',
            'night_shift_duration',
        ]);
        $this->setFreeShiftInfo($free_shift_config);

        foreach ($body as $row) {
            $index++;
            $staff_info_id = $row['staff_info_id'];

            if (!isset($staff_info_list[$staff_info_id]) && !isset($outsourceStaffInfoList[$staff_info_id])) {
                throw new ValidationException($index . ' type Staff Not Found');
            }

            if (isset($staff_info_list[$staff_info_id])) {
                $tmpStaffInfoList = $staff_info_list;
            } else {
                $tmpStaffInfoList = $outsourceStaffInfoList;
            }

            //增加数据权限验证
            if (isset($staff_info_list[$staff_info_id]) && !$this->isHasPermissions($tmpStaffInfoList[$staff_info_id],
                    $params['staff_info_id'])
            ) {
                throw new ValidationException($index . ' type ' . self::$t->_("permission_denied"));
            }

            //弹性班次的员工，不能修改班次
            if ($this->isFreeShiftJobTitle($tmpStaffInfoList[$staff_info_id]['job_title'])) {
                throw new ValidationException(self::$t->_('staff_disable_edit_shift',['staff_info_id'=>$staff_info_id]));
            }

            if (empty($row['shift_id'])) {
                throw new ValidationException($index . ' type Shift Id Not Found');
            }

            $shift = HrShiftRepository::getShiftOne($row['shift_id']);
            if (empty($shift)) {
                throw new ValidationException($index . ' type Shift Id Not Found');
            }

            if (empty($row['effective_date'])) {
                throw new ValidationException($index . ' type Effective Date Not Found');
            }

            if (strtotime($row['effective_date']) === false) {
                throw new ValidationException($index . ' type Effective Date error 1');
            }

            //验证生效日期范围
            if (strtotime($row['effective_date']) < $min_date || strtotime($row['effective_date']) > $max_date) {
                throw new ValidationException($index . ' type Effective Date error 2');
            }

            //主要针对 菲律宾 替换是否弹性打卡班次 id
            $shift['id'] = $this->getShiftIdIsFlexible([
                'node_department_id' => $tmpStaffInfoList[$staff_info_id]['node_department_id'] ?? 0,
                'job_title_id'       => $tmpStaffInfoList[$staff_info_id]['job_title'] ?? 0,
                'sys_store_id'       => $tmpStaffInfoList[$staff_info_id]['sys_store_id'] ?? 0,
                'shift_id'           => $shift['id'],
            ]);
            $db_by       = $this->getDI()->get('db_backyard');
            $db_by->begin();

            $effective_date = date('Y-m-d', strtotime($row['effective_date']));


            $lang = $staffService->getAcceptLanguage($staff_info_id);
            $t    = BaseService::getTranslation($lang);

            $staff_info_name        = $tmpStaffInfoList[$staff_info_id]['name'] ?? '';
            $staff_job_title        = $tmpStaffInfoList[$staff_info_id]['job_title'] ?? '';
            $job_name               = $jobTitleInfoToId[$staff_job_title] ?? '';
            $message_effective_date = self::effective_date_format($effective_date, $lang);

            //没有班次的，兼容处理一下
            $this->fixShiftData($staff_info_id,$dateList,$shift['id'],$shift['type'],$shift['start'],$shift['end']);

            $presetData['staff_info_id']  = $staff_info_id;
            $presetData['start']          = $shift['start'];
            $presetData['end']            = $shift['end'];
            $presetData['shift_id']       = $shift['id'];
            $presetData['shift_type']     = $shift['type'];
            $presetData['effective_date'] = $effective_date;

            $bind = ['staff_id'    => $staff_info_id,
                     'shift_date'  => $effective_date,
                     'shift_id'    => $shift['id'],
                     'shift_type'  => $shift['type'],
                     'shift_start' => $shift['start'],
                     'shift_end'   => $shift['end'],
                     'operator_id' => $params['staff_info_id'],
            ];
            $db_by->execute("update hr_staff_shift_middle_date SET shift_id=:shift_id,shift_type=:shift_type,shift_start=:shift_start,shift_end=:shift_end,operator_id=:operator_id where staff_info_id =:staff_id and shift_date >= :shift_date and deleted = 0",$bind);

            $hrStaffShiftModel = HrStaffShiftModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'      => ['staff_info_id' => $staff_info_id],
            ]);
            if (empty($hrStaffShiftModel)) {
                $hrStaffShiftModel                = new HrStaffShiftModel();
                $hrStaffShiftModel->staff_info_id = $staff_info_id;
            }
            $hrStaffShiftModel->start         = $shift['start'];
            $hrStaffShiftModel->end           = $shift['end'];
            $hrStaffShiftModel->shift_type    = $shift['type'];
            $hrStaffShiftModel->shift_id      = $shift['id'];
            $hrStaffShiftModel->preset_shift_id = $shift['id'];
            $hrStaffShiftModel->save();

            //操作日志表
            $logServer                 = new WorkShiftService();
            $logParam['staff_info_id'] = $staff_info_id;
            $logParam['date_at']       = $effective_date;
            $logParam['operate_id']   = $params['staff_info_id'];
            $extend['before']          = "";
            $extend['after']           = "{$shift['start']}-{$shift['end']}";
            $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
            $logServer->addShiftLog($logType, $logParam, $extend);

            $t_set_data['set_shift']     = $t->_('shift_' . strtolower($shift['type'])) . ',' . $shift['start'] . '-' . $shift['end'];
            $t_set_data['staff_info_name'] = $staff_info_name;
            $t_set_data['staff_info_id']   = $staff_info_id;
            $t_set_data['job_title']       = $job_name;
            $t_set_data['effective_date']  = $message_effective_date;

            $by_message_title   = $t->_('update_staff_shift_send_message_title_set');
            $by_message_content = $t->_('update_staff_shift_send_message_set', $t_set_data);


            //发送签字消息
            $add_message_param = [
                'staff_info_ids_str' => $staff_info_id,
                'staff_users'        => [['id' => $staff_info_id]],
                'message_title'      => $by_message_title,
                'message_content'    => $by_message_content,
                'category'           => Enums\MessageEnums::MESSAGE_CATEGORY_SHIFT_CHANGE,
            ];
            if(!in_array($staff_info_list[$staff_info_id]['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
                $re =  (new MessagesService())->add_kit_message($add_message_param);
                $this->logger->info([
                    'res'               => $re,
                    'func'              => 'staff_shift_change_send_message',
                    'add_message_param' => $add_message_param,
                ]);
            }

            $data = [
                "operater"      => $params['staff_info_id'],
                "staff_info_id" => $staff_info_id,
                "type"          => 'preset-shift',
                "before"        => json_encode(['body' => []], JSON_UNESCAPED_UNICODE),
                "after"         => json_encode(['body' => $presetData], JSON_UNESCAPED_UNICODE),
            ];

            $db_by->insertAsDict('hr_operate_logs', $data);

            $result[] = [
                'staff_info_id' => $row['staff_info_id'],
                'status'        => 0,
                'msg'           => 'ok',
            ];

            $db_by->commit();

        }

        return $result;
    }

    /**
     * 编辑某一天的班次
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function editShift($params)
    {
        $end_time     = strtotime(date('Y-m-d 23:30:00'));
        $current_time = strtotime(date('Y-m-d H:i:s'));
        if ($current_time > $end_time) {
            throw new ValidationException(self::$t->_("staff_shift_tip"));
        }

        $operatorStaffInfoId = $params['staff_info_id'];
        $staffInfoId         = $params['staff_id'];
        $effectiveDate       = $params['effective_date'];
        $shiftId             = $params['shift_id'];
        $result              = [];

        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($staffInfoId);
        if (empty($staffInfo)) {
            throw new ValidationException('Staff Not Found');
        }
        if (!(
            (in_array($staffInfo['formal'], [
                    HrStaffInfoModel::FORMAL_1,
                    HrStaffInfoModel::FORMAL_INTERN,
                ]) || $staffInfo['formal'] == HrStaffInfoModel::FORMAL_0) &&
            $staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB &&
            $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_NO
        )) {
            throw new ValidationException('Staff Not Found');
        }

        if ($staffInfo['formal'] == HrStaffInfoModel::FORMAL_0) {
            $min_date = strtotime(date("Y-m-d"));
        } else {
            $min_date = strtotime('+1 day midnight');
        }
        $max_date = strtotime("+1 month last day of");//下月最后一天

        $jobTitleInfo     = (new DepartmentService())->getJobList('', [$staffInfo['job_title']], true);
        $jobTitleInfoToId = !empty($jobTitleInfo) ? array_column($jobTitleInfo, 'name', 'id') : [];
        $dateList         = $this->getPresetDateList($effectiveDate);

        //是否可以给自己修改班次
        if ($operatorStaffInfoId != GlobalEnums::SYSTEM_STAFF_ID && $operatorStaffInfoId == $staffInfoId) {
            //新增规则配置校验 能否修改自己的休息日
            $ruleParam['staff_info_id'] = $operatorStaffInfoId;
            $ruleParam['rule_key']      = 'Allow_set_selfShift';
            $rules                      = $this->getConditionRule(self::$language, $ruleParam);
            //- N or 空：不允许修改自己，给出如下提示
            if (empty($rules['data']) || $rules['data']['response_type'] != ConditionsRulesEnums::RESPONSE_TYPE_VALUE || !$rules['data']['response_data']) {
                throw new ValidationException(self::$t->_('can_not_edit_self_shift'));
            }
        }

        //判断是否有支援
        $supportEnd    = date('Y-m-t', strtotime('+1 month'));
        $supportServer = new StaffSupportStoreServer();
        $count         = $supportServer->validateConflictBatch($effectiveDate, $supportEnd, [$staffInfoId]);
        if (!empty($count)) {
            throw new ValidationException(implode(',', array_keys($count)) . self::$t->_('support_info_exist'));
        }

        //弹性班次
        $free_shift_config = SettingEnvModel::getMultiEnvByCode([
            'free_shift_position',
            'day_shift_duration',
            'night_shift_duration',
        ]);
        $this->setFreeShiftInfo($free_shift_config);

        //增加数据权限验证
        if (!$this->isHasPermissions($staffInfo, $operatorStaffInfoId)) {
            throw new ValidationException(' type ' . self::$t->_("permission_denied"));
        }
        //弹性班次的员工，不能修改班次
        if ($this->isFreeShiftJobTitle($staffInfo['job_title'])) {
            throw new ValidationException(self::$t->_('staff_disable_edit_shift', ['staff_info_id' => $staffInfoId]));
        }
        if (empty($shiftId)) {
            throw new ValidationException(' type Shift Id Not Found');
        }
        $shift = HrShiftRepository::getShiftOne($shiftId);
        if (empty($shift)) {
            throw new ValidationException(' type Shift Id Not Found');
        }
        if (empty($effectiveDate)) {
            throw new ValidationException(' type Effective Date Not Found');
        }
        if (strtotime($effectiveDate) === false) {
            throw new ValidationException(' type Effective Date error 1');
        }
        //验证生效日期范围
        if (strtotime($effectiveDate) < $min_date || strtotime($effectiveDate) > $max_date) {
            throw new ValidationException(' type Effective Date error 2');
        }
        $effective_date = date('Y-m-d', strtotime($effectiveDate));

        $shiftStartTime = strtotime(sprintf('%s %s', $effectiveDate, $shift['start']));
        if ($shiftStartTime < time()) {
            throw new ValidationException(static::$t->_('cannot_apply_before_now'));
        }

        //主要针对 菲律宾 替换是否弹性打卡班次 id
        $shift['id'] = $this->getShiftIdIsFlexible([
            'node_department_id' => $staffInfo['node_department_id'] ?? 0,
            'job_title_id'       => $staffInfo['job_title'] ?? 0,
            'sys_store_id'       => $staffInfo['sys_store_id'] ?? 0,
            'shift_id'           => $shift['id'],
        ]);
        $db_by       = $this->getDI()->get('db_backyard');
        $db_by->begin();

        $staffService = new StaffService();
        $lang = $staffService->getAcceptLanguage($staffInfoId);
        $t    = BaseService::getTranslation($lang);

        $staff_info_name        = $staffInfo['name'] ?? '';
        $job_name               = $jobTitleInfoToId[$staffInfo['job_title']] ?? '';
        $message_effective_date = self::effective_date_format($effective_date, $lang);

        //获取指定日期的班次
        $specDayShift = HrStaffShiftMiddleDateModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and shift_date = :date_at: and deleted = 0',
            'bind' => [
                'staff_id' => $staffInfoId,
                'date_at'  => $effective_date,
            ],
        ]);
        if (empty($specDayShift)) {
            $specDayShift = new HrStaffShiftMiddleDateModel();
            $specDayShift->staff_info_id = $staffInfoId;
            $specDayShift->shift_date = $effective_date;
            $specDayShift->shift_id = $shift['id'];
            $specDayShift->shift_type = $shift['type'];
            $specDayShift->shift_start = $shift['start'];
            $specDayShift->shift_end = $shift['end'];
            $specDayShift->operator_id = $operatorStaffInfoId;
            $specDayShift->save();
        } else {
            $bind = [
                'staff_id'    => $staffInfoId,
                'shift_date'  => $effective_date,
                'shift_id'    => $shift['id'],
                'shift_type'  => $shift['type'],
                'shift_start' => $shift['start'],
                'shift_end'   => $shift['end'],
                'operator_id' => $operatorStaffInfoId,
            ];
            $db_by->execute("update hr_staff_shift_middle_date SET shift_id=:shift_id,shift_type=:shift_type,shift_start=:shift_start,shift_end=:shift_end,operator_id=:operator_id where staff_info_id =:staff_id and shift_date = :shift_date and deleted = 0",
                $bind);
        }
        $specDayShift = $specDayShift->toArray();

        //如果是外协员工并且修改的是当天班次，则要同步更新打卡表的班次
        if ($effective_date == date('Y-m-d') && $staffInfo['formal'] == HrStaffInfoModel::FORMAL_0 && in_array($staffInfo['staff_type'],
                [HrStaffInfoModel::STAFF_TYPE_1, HrStaffInfoModel::STAFF_TYPE_2, HrStaffInfoModel::STAFF_TYPE_3])) {
            $attendanceData = StaffWorkAttendanceModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and attendance_date = :attendance_date:',
                'bind' => [
                    'staff_info_id' => $staffInfoId,
                    'attendance_date' => $effective_date,
                ],
            ]);
            if (!empty($attendanceData)) {
                $attendanceData->shift_start = $shift['start'];
                $attendanceData->shift_end   = $shift['end'];
                $attendanceData->shift_id    = $shift['id'];
                $attendanceData->save();

                $this->logger->write_log(sprintf('StaffWorkAttendanceModel updated, staff %d,attendance_date %s, shift_info %s', $staffInfoId, $effective_date, json_encode($shift)), 'info');
            }
            $otInfo = OutsourcingOvertimeModel::findFirst([
                'conditions' => 'os_staff_id = :staff_info_id: and date_at = :date_at: and state in({audit_state:array})',
                'bind' => [
                    'staff_info_id' => $staffInfoId,
                    'date_at' => $effective_date,
                    'audit_state' => [Enums\ApprovalEnums::APPROVAL_STATUS_PENDING, Enums\ApprovalEnums::APPROVAL_STATUS_APPROVAL],
                ],
            ]);
            if ($otInfo) {
                //撤销
                $rpcParam['audit_id'] = $otInfo->id;
                $rpcParam['audit_type']   = Enums\ApprovalEnums::APPROVAL_TYPE_OVERTIME_OS;
                $t_en = BaseService::getTranslation('en');
                $rpcParam['reject_reason'] = $t_en->_('shift_change_reject_reason', ['shift_period' => sprintf('%s - %s', $shift['start'], $shift['end'])]);

                $ret = new ApiClient('by', '', 'systemReject', 'en');
                $ret->setParams([$rpcParam]);
                $res = $ret->execute();
                if (isset($res['code']) && $res['code'] == 1) {
                    $lang = $staffService->getAcceptLanguage($otInfo->staff_info_id);
                    $t_submitter = BaseService::getTranslation($lang);

                    $storeInfo = SysStoreRepository::getStoreById($staffInfo['sys_store_id']);

                    //    1. 标题：%工号%外协延长工时申请作废通知
                    //    2. 内容：%工号%-%姓名%-%网点名称%， 审批编号:%OSOT2024121422240000757%.
                    //      %延长日期% 的班次时间已修改为%班次开始-结束时间07:00-16:00%，当前申请延长班次开始时间%开始时间2024-10-15 15:00:00%不满足条件，系统自动驳回，如需延长班次请重新申请
                    $reject_message_title   = $t_submitter->_('os_overtime_system_force_reject_title', ['staff_info_id' => $staffInfoId]);
                    $reject_message_content = $t_submitter->_('os_overtime_system_force_reject_content', [
                        'staff_info_id'    => $staffInfoId,
                        'staff_info_name'  => $staff_info_name,
                        'store_name'       => $storeInfo ? $storeInfo['name'] : '',
                        'serial_no'        => $otInfo->serial_no,
                        'effective_date'   => $effective_date,
                        'after_edit_shift' => sprintf('%s - %s', $shift['start'], $shift['end']),
                        'origin_shift'     => $otInfo->start_time,
                    ]);

                    //发送签字消息
                    $reject_message_param = [
                        'staff_info_ids_str' => $otInfo->staff_info_id,
                        'staff_users'        => [['id' => $otInfo->staff_info_id]],
                        'message_title'      => $reject_message_title,
                        'message_content'    => $reject_message_content,
                        'category'           => Enums\MessageEnums::CATEGORY_GENERAL,
                    ];
                    $re = (new MessagesService())->add_kit_message($reject_message_param);
                    $this->logger->info([
                        'res'               => $re,
                        'func'              => 'editShift',
                        'add_message_param' => $reject_message_param,
                    ]);
                }
            }
        }

        //操作日志表
        $logServer                 = new WorkShiftService();
        $logParam['staff_info_id'] = $staffInfoId;
        $logParam['date_at']       = $effective_date;
        $logParam['operate_id']    = $operatorStaffInfoId;
        $extend['before']          = "";
        $extend['after']           = "{$shift['start']}-{$shift['end']}";
        $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT;
        $logServer->addShiftLog($logType, $logParam, $extend);

        //发送消息
        $beforeShiftTypeKey            = strtolower($specDayShift['shift_type']);
        $t_set_data['before_shift']    = sprintf('%s,%s-%s', $t->_($beforeShiftTypeKey),
            $specDayShift['shift_start'], $specDayShift['shift_end']);
        $afterShiftTypeKey             = strtolower($shift['type']);
        $t_set_data['after_shift']     = sprintf('%s,%s-%s', $t->_($afterShiftTypeKey), $shift['start'],
            $shift['end']);
        $t_set_data['staff_info_name'] = $staff_info_name;
        $t_set_data['staff_info_id']   = $staffInfoId;
        $t_set_data['job_title']       = $job_name;
        $t_set_data['effective_date']  = $message_effective_date;

        $by_message_title   = $t->_('update_staff_shift_send_message_title_change');
        $by_message_content = $t->_('update_staff_shift_send_message_change_v2', $t_set_data);

        //发送签字消息
        $add_message_param = [
            'staff_info_ids_str' => $staffInfoId,
            'staff_users'        => [['id' => $staffInfoId]],
            'message_title'      => $by_message_title,
            'message_content'    => $by_message_content,
            'category'           => Enums\MessageEnums::MESSAGE_CATEGORY_SHIFT_CHANGE,
        ];
        if (!in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $re = (new MessagesService())->add_kit_message($add_message_param);
            $this->logger->info([
                'res'               => $re,
                'func'              => 'staff_shift_change_send_message',
                'add_message_param' => $add_message_param,
            ]);
        }

        $presetData['start']           = $shift['start'];
        $presetData['end']             = $shift['end'];
        $presetData['shift']           = $shift['id'];
        $presetData['attendance_date'] = $effective_date;
        $before['start']               = $specDayShift['shift_start'];
        $before['end']                 = $specDayShift['shift_end'];
        $before['shift']               = $specDayShift['shift_id'];

        $data = [
            "operater"      => $operatorStaffInfoId,
            "staff_info_id" => $staffInfoId,
            "type"          => 'middle-shift-hcm',
            "before"        => json_encode(['body' => $before], JSON_UNESCAPED_UNICODE),
            "after"         => json_encode(['body' => $presetData], JSON_UNESCAPED_UNICODE),
        ];

        $db_by->insertAsDict('hr_operate_logs', $data);

        $result[] = [
            'staff_info_id' => $staffInfoId,
            'status'        => 0,
            'msg'           => 'ok',
        ];

        $db_by->commit();

        return $result;
    }

    /**
     * @description: 这个方法会在 ph 重写,主要用于获取 ph 弹性 id
     * @param array $params ['shift_id'] 当前班次 id
     * @return int|mixed   shift_id
     * @author: L.J
     * @time: 2022/11/11 14:42
     */
    public function getShiftIdIsFlexible(array $params = [])
    {
        return $params['shift_id'] ?? HrShift::EARLY_7;
    }

    public function isHasPermissions($staff, $staff_info_id)
    {
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($staff_info_id, ['job_title']);
        if (empty($staffInfo)) {
            return false;
        }
        $param['job_title']     = $staffInfo['job_title'];
        $param['staff_info_id'] = $staff_info_id;
        $isEnable               = $this->checkSettingPermission($param);
        if (!$isEnable) {
            return false;
        }

        $purview = (new StaffPermissionService)->getStaffDataPermission($staff_info_id);;
        if ($purview === true) {
            return true;
        }
        if (
            $purview && (
                (isset($purview['stores']) && (in_array($staff['sys_store_id'], $purview['stores']) || ($staff['sys_store_id'] != GlobalEnums::HEAD_OFFICE_ID && in_array('-2', $purview['stores']))))
                || (isset($purview['departments']) && in_array($staff['node_department_id'], $purview['departments']))
                || (isset($purview['staff_ids']) && in_array($staff['staff_info_id'], $purview['staff_ids']))
                || (isset($purview['flash_home_departments']) && ($staff['sys_store_id'] == GlobalEnums::HEAD_OFFICE_ID && in_array($staff['node_department_id'],
                        $purview['flash_home_departments'])))
            )
        ) {
            return true;
        }

        return false;
    }

    //生效日期格式化
    public static function effective_date_format($effective_date, $language)
    {
        $message_effective_date = $effective_date;
        if (strpos($language, 'en') !== false) {
            $message_effective_date = date('d/m/Y', strtotime($effective_date));
        }
        if (strpos($language, 'th') !== false) {
            $message_effective_date = date('d/m/Y', strtotime("$effective_date +543 year"));
        }
        if (strpos($language, 'vn') !== false) {
            $message_effective_date = date('d/m/Y', strtotime($effective_date));
        }
        return $message_effective_date;
    }

    /**
     *
     * @param $params
     * @return array
     */
    public function getDetail($params)
    {

        $staffShift       = HrStaffShiftRepository::getShiftOne($params['staff_info_id']);
        if (empty($staffShift)) {
            return [];
        }
        $staffShift = HrShiftRepository::getShiftOne($staffShift['preset_shift_id'] ?: $staffShift['shift_id']);

        $staffShiftPreset['shift_id']      = $staffShift['id'];
        $staffShiftPreset['shift_type']    = $staffShift['type'];
        $staffShiftPreset['start']         = $staffShift['start'];
        $staffShiftPreset['end']           = $staffShift['end'];
        $staffShiftPreset['staff_info_id'] = $params['staff_info_id'];
        $staffShiftPreset['effective_date'] = date('Y-m-d',strtotime('+1 day'));

        return $staffShiftPreset;
    }

    /**
     * 获取指定日期的班次详情
     * @param $params
     * @return array
     */
    public function getSingelDayShiftDetail($params)
    {
        $staffMiddleShift = HrStaffShiftMiddleDateRepository::getStaffShift($params['staff_info_id'], $params['effective_date']);
        if (!empty($staffMiddleShift)) {
            $staffShiftInfo['shift_id']       = $staffMiddleShift['shift_id'];
            $staffShiftInfo['shift_type']     = $staffMiddleShift['shift_type'];
            $staffShiftInfo['start']          = $staffMiddleShift['shift_start'];
            $staffShiftInfo['end']            = $staffMiddleShift['shift_end'];
            $staffShiftInfo['staff_info_id']  = $params['staff_info_id'];
            $staffShiftInfo['effective_date'] = $params['effective_date'];

            return $staffShiftInfo;
        }

        $staffShift       = HrStaffShiftRepository::getShiftOne($params['staff_info_id']);
        if (empty($staffShift)) {
            return [];
        }

        $staffShiftPreset['shift_id']      = $staffShift['shift_id'];
        $staffShiftPreset['shift_type']    = $staffShift['shift_type'];
        $staffShiftPreset['start']         = $staffShift['start'];
        $staffShiftPreset['end']           = $staffShift['end'];
        $staffShiftPreset['staff_info_id'] = $params['staff_info_id'];
        $staffShiftPreset['effective_date'] = date('Y-m-d',strtotime('+1 day'));

        return $staffShiftPreset;
    }

    /**
     * 导入，加事务
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function importShift($params)
    {
        $fileName  = basename($params['file']);
        $fileInfo  = pathinfo($fileName);
        $localName = uniqid(date('Y-m-d') . '_' . time()) . '.' . $fileInfo['extension'];
        if ($fileInfo['extension'] !== "xlsx") {
            throw  new ValidationException(self::$t->_('only_xls_xlsx'));
        }

        $end_time     = strtotime(date('Y-m-d 23:30:00'));
        $current_time = strtotime(date('Y-m-d H:i:s'));
        if ($current_time > $end_time) {
            throw new ValidationException(self::$t->_("staff_shift_tip"));
        }

        $filePath = sys_get_temp_dir() . '/' . $localName;

        //下载到本地
        $this->downloadFile($params['file'], $filePath);

        $excelReader = \PHPExcel_IOFactory::createReader('Excel2007');
        $excelReader->setReadDataOnly(true);
        //载入文件并获取第一个sheet
        $sheet     = $excelReader->load($filePath)->getSheet(0);
        $totalLine = $sheet->getHighestRow();

        if ($totalLine > 501 || $totalLine < 2) {
            throw new ValidationException(self::$t->_('file_empty_error'));
        }
        $result       = [];
        $cols         = range("A", "E");
        for ($i = 2; $i < $totalLine + 1; $i++) {
            if (trim($sheet->getCell("A" . $i)->getValue())) {
                if (isset($result[trim($sheet->getCell("A" . $i)->getValue())])) {
                    $result[trim($sheet->getCell("A" . $i)->getValue())] = [];
                }
                foreach ($cols as $col) {
                    $data = trim($sheet->getCell($col . $i)->getValue());
                    $this->logger->info('Staff Shift ' . $col . $i . " " . $data);
                    if ($col == "B") {
                        $data = gmdate("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($data));
                    } else {
                        if ($col == "D" || $col == "E") {
                            $data = $data ? gmdate("H:i", \PHPExcel_Shared_Date::ExcelToPHP($data)) : '';
                        }
                    }
                    if (trim($sheet->getCell("A" . $i)->getValue())) {
                        $result[trim($sheet->getCell("A" . $i)->getValue())][] = $data;
                    }
                }
            }
        }

        $staffIds = array_values(array_filter(array_unique(array_keys($result))));
        $canEditSelf = false;
        //改用新规则
        if(in_array($params['user_id'], $staffIds)){
            //新增规则配置校验 能否修改自己的休息日
            $ruleParam['staff_info_id'] = $params['user_id'];
            $ruleParam['rule_key']      = 'Allow_set_selfShift';
            $rules                      = $this->getConditionRule(self::$language, $ruleParam);
            //- N or 空：不允许修改自己，给出如下提示
            if (!empty($rules['data']) && $rules['data']['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE && $rules['data']['response_data']) {
                $canEditSelf = true;
            }
        }

        $staffs = HrStaffInfoRepository::getHrStaffByIds($staffIds);

        $jobTitleId       = array_values(array_unique(array_column($staffs, 'job_title')));
        $jobTitleInfo     = (new DepartmentService())->getJobList('', $jobTitleId, true);
        $jobTitleInfoToId = !empty($jobTitleInfo) ? array_column($jobTitleInfo, 'name', 'id') : [];

        $shiftTypes = [
            "EARLY"  => 'EARLY',
            "MIDDLE" => 'MIDDLE',
            "NIGHT"  => 'NIGHT',
        ];
        $db_by      = $this->getDI()->get('db_backyard');

        $errorList    = [];
        $shiftServer = new HrShiftService();
        $successCount = $errorCount = 0;

        //弹性班次
        $free_shift_config     = SettingEnvModel::getMultiEnvByCode([
            'free_shift_position',
            'day_shift_duration',
            'night_shift_duration',
        ]);
        $this->setFreeShiftInfo($free_shift_config);

        $staffService = new StaffService();
        foreach ($result as $staffId => $item) {
            if (!isset($staffs[$staffId]) || $staffs[$staffId]['state'] != 1) {
                // 不是系统在职工号
                $errorCount++;
                $errorList[] = array_merge($item, [self::$t->_("not_job_in_system")]);
                continue;
            }

            //不能修改自己的班次
            if($params['user_id'] == $staffId && !$canEditSelf){
                $errorCount++;
                $errorList[] = array_merge($item, [self::$t->_("no_modify_own_shift")]);
                continue;
            }


            if (!$this->isHasPermissions($staffs[$staffId], $params['user_id'])) {
                // 无权限配置此工号
                $errorCount++;
                $errorList[] = array_merge($item, [self::$t->_("permission_denied")]);
                continue;
            }
            //弹性班次的员工，不能修改班次
            if ($this->isFreeShiftJobTitle($staffs[$staffId]['job_title'])) {
                $errorCount++;
                $errorList[] = array_merge($item,
                    [self::$t->_("staff_disable_edit_shift", ['staff_info_id' => $staffId])]);
                continue;
            }

            if (!$this->isDate($item[1])) {//生效日期 Effective Date
                // 不是可选日期
                $errorCount++;
                $errorList[] = array_merge($item, [self::$t->_("not_date_format")]);
                continue;
            }

            if (strtotime($item[1]) <= strtotime(date("Y-m-d")) || strtotime($item[1]) >= strtotime(date("Y-m-d",
                    strtotime("+1 months")))) {
                // 不是可选日期
                $errorCount++;
                $errorList[] = array_merge($item, [self::$t->_("not_selectable_date")]);
                continue;
            }

            //存在支援信息不能修改
            $supportParam['start'] = $item[1];
            $supportParam['end'] = date('Y-m-t', strtotime('+1 month'));
            $supportParam['staff_info_id'] = $staffId;
            $count = $shiftServer->checkSupport($supportParam);
            if($count > 0){
                $errorCount++;
                $errorList[] = array_merge($item, [self::$t->_("support_info_exist")]);
                continue;
            }

            $shift = $this->isHasShiftV2($staffs[$staffId], $shiftTypes[$item[2]], $item[3], $item[4]);
            if (is_array($shift)) {
                $lang = $staffService->getAcceptLanguage($staffId);
                $t    = BaseService::getTranslation($lang);


                $effective_date  = date('Y-m-d', strtotime($item[1]));
                $staff_info_name = $staffs[$staffId]['name'] ?? '';
                $staff_job_title = $staffs[$staffId]['job_title'] ?? '';
                $dateList = $this->getPresetDateList($effective_date);
                //主要针对 菲律宾 替换是否弹性打卡班次 id
                $shift['id'] = $this->getShiftIdIsFlexible([
                    'node_department_id' => $staffs[$staffId]['node_department_id'] ?? 0,
                    'job_title_id'       => $staffs[$staffId]['job_title'] ?? 0,
                    'sys_store_id'       => $staffs[$staffId]['sys_store_id'] ?? 0,
                    'shift_id'           => $shift['id'],
                ]);

                $job_name               = $jobTitleInfoToId[$staff_job_title] ?? '';
                $message_effective_date = self::effective_date_format($effective_date, $lang);

                $presetData['staff_info_id']  = $staffId;
                $presetData['start']          = date("H:i", strtotime($item[3]));//开始时间
                $presetData['end']            = date("H:i", strtotime($item[4]));//结束时间
                $presetData['shift_id']       = $shift['id'];//班次id
                $presetData['shift_type']     = $shift['type'];//班次类型
                $presetData['effective_date'] = $effective_date;

                //没有班次的，兼容处理一下
                $this->fixShiftData($staffId,$dateList,$shift['id'],$shift['type'],$shift['start'],$shift['end']);

                $bind = ['staff_id'    => $staffId,
                         'shift_date'  => $effective_date,
                         'shift_id'    => $shift['id'],
                         'shift_type'  => $shift['type'],
                         'shift_start' => $shift['start'],
                         'shift_end'   => $shift['end'],
                         'operator_id' => $params['user_id'],
                ];
                $db_by->execute("update hr_staff_shift_middle_date SET shift_id=:shift_id,shift_type=:shift_type,shift_start=:shift_start,shift_end=:shift_end,operator_id=:operator_id where staff_info_id =:staff_id and shift_date >= :shift_date and deleted = 0",$bind);
                $hrStaffShiftModel = HrStaffShiftModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind'      => ['staff_info_id' => $staffId],
                ]);
                if (empty($hrStaffShiftModel)) {
                    $hrStaffShiftModel                = new HrStaffShiftModel();
                    $hrStaffShiftModel->staff_info_id = $staffId;
                    $hrStaffShiftModel->start         = '09:00';
                    $hrStaffShiftModel->end           = '18:00';
                    $hrStaffShiftModel->shift_type    = 'EARLY';
                    $hrStaffShiftModel->shift_id      = HrShift::EARLY_7;
                }
                $hrStaffShiftModel->preset_shift_id = $shift['id'];
                $hrStaffShiftModel->save();


                //操作日志表
                $logServer                 = new WorkShiftService();
                $logParam['staff_info_id'] = $staffId;
                $logParam['date_at']       = $effective_date;
                $logParam['operate_id']   = $params['user_id'];
                $extend['before']          = "";
                $extend['after']           = "{$shift['start']}-{$shift['end']}";
                $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
                $logServer->addShiftLog($logType, $logParam, $extend);

                $t_set_data['set_shift']       = $t->_('shift_' . strtolower($shift['type'])) . ',' . $shift['start'] . '-' . $shift['end'];
                $t_set_data['staff_info_name'] = $staff_info_name;
                $t_set_data['staff_info_id']   = $staffId;
                $t_set_data['job_title']       = $job_name;
                $t_set_data['effective_date']  = $message_effective_date;

                $by_message_title   = $t->_('update_staff_shift_send_message_title_set');
                $by_message_content = $t->_('update_staff_shift_send_message_set', $t_set_data);


                //发送签字消息
                $add_message_param = [
                    'staff_info_ids_str' => $staffId,
                    'staff_users'        => [['id' => $staffId]],
                    'message_title'      => $by_message_title,
                    'message_content'    => $by_message_content,
                    'category'           => Enums\MessageEnums::MESSAGE_CATEGORY_SHIFT_CHANGE,
                ];
                if($staffs[$staffId]['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                    $re = (new MessagesService())->add_kit_message($add_message_param);
                    $this->logger->info([
                        'res'               => $re,
                        'func'              => 'staff_shift_change_send_message',
                        'add_message_param' => $add_message_param,
                    ]);
                }
                
                $data = [
                    "operater"      => $params['user_id'],
                    "staff_info_id" => $staffId,
                    "type"          => 'preset-shift-import',
                    "before"        => json_encode(['body' =>  []], JSON_UNESCAPED_UNICODE),
                    "after"         => json_encode(['body' => $presetData ?? []], JSON_UNESCAPED_UNICODE),
                ];

                $db_by->insertAsDict('hr_operate_logs', $data);

                $successCount++;
            } else {
                if ($shift == 1) {
                    // 不是已有班次类型、班次时间
                    $errorCount++;
                    $errorList[] = array_merge($item, [self::$t->_("not_exist_shift_type")]);
                } else {
                    if ($shift == 2) {
                        // 班次类型与工作时间无法对应
                        $errorCount++;
                        $errorList[] = array_merge($item, [self::$t->_("not_exist_shift_time")]);
                    } else {
                        // 不是可选班次
                        $errorCount++;
                        $errorList[] = array_merge($item, [self::$t->_("not_exist_shift")]);
                    }
                }
            }
        }

        //若有 失败数据，则导出
        $errorExcelFile = "shift_setting_fail_" . date("Ymd_H:i:s") . ".xlsx";
        $header         = [
            "Employee No.",
            "Effective Date",
            "Shift type",
            "Work time(start)",
            "Work time(end)",
            "Reason",
        ];

        $url = $this->uploadFailData($header, $errorList, $errorExcelFile);

        return [
            "error_count"   => $errorCount,
            "success_count" => $successCount,
            "path"          => $url,
        ];
    }

    /**
     * 导出，并上传失败数据到oss.导出，并上传失败数据到oss.
     * @param $header
     * @param $failData
     * @param $fileName
     * @return bool|\OSS\Http\ResponseCore|string
     * @throws Exception
     */
    public function uploadFailData($header, $failData, $fileName)
    {
        //上传失败文件
        $res = $this->exportExcel($header, $failData ?? [], $fileName);
        if ($res['code'] != 1) {
            $this->logger->notice('shift_setting_fail : ' . json_encode($res, JSON_UNESCAPED_UNICODE));
            return false;
        }
        $flashOss  = new FlashOss();
        $ossObject = 'shift_setting_fail/' . $fileName;
        $uploadRes = $flashOss->uploadFile($ossObject, $res['data']);
        if ($uploadRes) {
            return $flashOss->signUrl($ossObject, 60 * 60);
        }
        return '';
    }

    /**
     * 校验 日期
     * @param $date
     * @return bool
     */
    private function isDate($date)
    {
        return strtotime(date('Y-m-d', strtotime($date))) === strtotime($date);
    }

    /**
     * @param $staff
     * @param $shirtType
     * @param $start
     * @param $end
     * @return int
     * @throws \Exception
     */
    private function isHasShiftV2($staff, $shirtType, $start, $end)
    {
        $start = date("H:i", strtotime($start));
        $end   = date("H:i", strtotime($end));

        $shift_list = reBuildCountryInstance(new StaffShiftService())->getStaffConfigShiftList($staff['node_department_id'],
            $staff['job_title'], $staff['sys_store_id']);

        if (empty($shift_list)) {
            //可以随意设置
            $shift_list = (new SysService())->shiftsToType();//获取所有班次
        } else {
            $shift_ids  = array_column($shift_list, 'shift_id');
            $shift_list = (new ShiftConfigService())->shiftsToTypeByShiftIds($shift_ids);//获取所有班次
        }

        foreach ($shift_list as $type => $shift) {
            if ($type == $shirtType) {
                foreach ($shift as $item) {
                    if ($item['start'] == $start && $item['end'] == $end) {
                        return $item;
                    }
                }
                return 2; //班次类型与工作时间无法对应
            }
        }

        return 1; // 不是已有班次类型、班次时间
    }

    public function exportShift($params)
    {
        $params['is_check_data_num'] = true;
        $result                      = $this->getStaffList($params);
        if ($result['total'] > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit', ['num' => self::LIMIT_DOWNLOAD_NUM]));
        }
        unset($params['is_check_data_num']);

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "staff_shift_export" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "main";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_info_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        //判断是否是fbi 操作的 导出
        $headerData = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && in_array($headerData['From'], ['fbi', '_ms'])? $headerData['From'] : '';

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_info_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = date('Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        if (!empty($hcmExcelTaskId) && in_array($params['From'], ['fbi', '_ms'])) {
            (new BllService())->addFbiExportMessage(
                $params['staff_info_id'],
                'hcm_staff_shift_export_main',
                $params['file_name'],
                $hcmExcelTaskId
            );
        }

        return $hcmExcelTaskId;
    }
}