<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HcHireTypeConfigModel;
use App\Models\backyard\HcHireTypePositionModel;
use App\Models\backyard\HcHireTypeBranchModel;
use App\Models\backyard\HcHireTypeOperationLogModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysStoreModel;
use Exception;
use OSS\Core\OssException;
use OSS\OssClient;
use Phalcon\Mvc\Model\Query\Builder;

class HcHireTypeConfigService extends BaseService
{
    protected $daily_contract_mail_title   = '【TH】待确认招聘雇佣类型的网点名单';
    protected $daily_contract_mail_content = "Dear all，<br/>
    附件是需要确认招聘雇佣类型的新开网点，请尽快确认。。<br/>
    Attached is the list of new branches for which the hire types need to be confirmed. Please confirm as soon as possible.";

    protected $checkJobTitle = [];
    protected $checkStoreId  = [];
    protected $checkHireType = [];

    public function statusList()
    {
        return [
            ['label' => static::$t->_('effective'), 'value' => (string)HcHireTypeConfigModel::STATUS_ACTIVE],
            ['label' => static::$t->_('expired'), 'value' => (string)HcHireTypeConfigModel::STATUS_INACTIVE],
        ];
    }

    /**
     * 作废
     * @throws ValidationException
     */
    public function obsolete($params, $operatorId)
    {
        //1、开启事务
        $db = HcHireTypeConfigModel::beginTransaction($this);
        try {
            //2 校验配置是否存在
            $config = HcHireTypeConfigModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if (!$config) {
                throw new ValidationException(static::$t->_('22369_config_error_2'));
            }
            //3 校验配置是否已失效
            if ($config->status == HcHireTypeConfigModel::STATUS_INACTIVE) {
                throw new ValidationException(static::$t->_('22369_config_error_3'));
            }
            //5 失效配置
            $config->status = HcHireTypeConfigModel::STATUS_INACTIVE;
            $config->expire_time = date('Y-m-d H:i:s');
            $config->save();

            if ($config->config_type == HcHireTypeConfigModel::CONFIG_TYPE_BRANCH) {
                HcHireTypeBranchModel::find([
                    'conditions' => 'config_id = :config_id: and status = :status:',
                    'bind'       => ['config_id' => $config->id, 'status' => HcHireTypeBranchModel::STATUS_ACTIVE],
                ])->update([
                    'status'      => HcHireTypeBranchModel::STATUS_INACTIVE,
                    'expire_time' => date('Y-m-d H:i:s'),
                ]);
            } else {
                //子表失效
                HcHireTypePositionModel::find([
                    'conditions' => 'config_id = :config_id: and status = :status:',
                    'bind'       => ['config_id' => $config->id, 'status' => HcHireTypeBranchModel::STATUS_ACTIVE],
                ])->update([
                    'status'      => HcHireTypeBranchModel::STATUS_INACTIVE,
                    'expire_time' => date('Y-m-d H:i:s'),
                ]);
            }

            //7 操作日志
            $this->createOperationLog($config->id, HcHireTypeOperationLogModel::OPERATION_TYPE_EXPIRE,
                $operatorId, $params['remark'] ?? '');

            //6 提交事务
            $db->commit();
        } catch (Exception $e) {
            //7 回滚事务
            $db->rollback();
            //8 抛出异常
            throw $e;
        }
        return true;
    }

    /**
     * 导出生效中的数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getEffectiveConfigListData($params)
    {
        $list       = $this->getConfigList($params);
        $updatePath = 'hc_hire_type_config/' . date('Y-m-d') . '/';
        $file_name  = uniqid('Void hc hire type branch_' . date('YmdHis')) . '.xlsx';
        $t          = self::$t;
        $title      = [
            $t['id'],
            $t['store_class_store_id'],
            $t['store_name'],
            $t['applicant_position'],
            $t['hire_type'],
            $t['description'],
        ];
        $data       = [];
        foreach ($list as $item) {
            $data[] = [
                intval($item['id']),
                $item['store_id'],
                $item['store_name'],
                $item['job_title_name'],
                $item['hire_type_text'],
                null,
            ];
        }
        return (new BllService())->exportExcels($title, $data, $updatePath, $file_name);
    }


    public function list($params)
    {
        // 获取数据列表
        $list = $this->getConfigList($params);

        // 获取总数
        $total = $this->getConfigCount($params);

        return [
            'list'  => $list,
            'total' => $total,
        ];
    }

    /**
     * 获取雇佣类型配置数据列表
     * @description: 获取雇佣类型配置数据列表，支持分页和无分页模式
     * @param array $params 查询参数
     * @return array 配置数据列表
     * @author: AI
     * @date: 2024-12-19 15:30:00
     */
    public function getConfigList($params = [])
    {
        // 构建主查询
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'config.id',
            'config.config_type',
            'config.store_id',
            'config.parent_store_id',
            'config.hire_types',
            'config.job_titles',
            'config.status',
            'config.effective_time',
            'config.expire_time',
        ]);
        $builder->from(['config' => HcHireTypeConfigModel::class]);

        // 应用通用查询条件
        $this->applyQueryConditions($builder, $params);

        // 分组和排序
        $builder->groupBy('config.id')->orderBy('config.id DESC');

        // 如果有分页参数则应用分页
        if (isset($params['page']) && isset($params['size'])) {
            // 分页处理
            $page     = $params['page'];
            $pageSize = $params['size'];
            $offset   = ($page - 1) * $pageSize;
            $builder->limit($pageSize, $offset);
        }

        // 执行查询
        $result = $builder->getQuery()->execute();

        $list = [];
        if ($result) {
            $list = $result->toArray();
            // 处理JSON字段
            foreach ($list as &$item) {
                $item['hire_type_text'] = implode(',', array_map(function ($hire_type) {
                    return self::$t->_('hire_type_' . $hire_type);
                }, json_decode($item['hire_types'], true)));
                $item['job_title_name'] = implode(',', array_map(function ($job_title) {
                    return $this->showJobTitleName($job_title);
                }, json_decode($item['job_titles'], true)));
                $item['store_name']     = '';
                if (!empty($item['store_id'])) {
                    $item['store_name'] = $this->showStoreName($item['store_id']);
                }
                $item['status_text'] = $item['status'] == HcHireTypeConfigModel::STATUS_INACTIVE ? self::$t->_('expired') : self::$t->_('effective');
            }
        }

        return $list;
    }

    /**
     * 获取雇佣类型配置总数
     * @description: 获取符合条件的雇佣类型配置记录总数
     * @param array $params 查询参数
     * @return int 配置记录总数
     * @author: AI
     * @date: 2024-12-19 15:30:00
     */
    public function getConfigCount($params = [])
    {
        // 构建计数查询
        $countBuilder = $this->modelsManager->createBuilder();
        $countBuilder->columns('COUNT(DISTINCT config.id) as total')
            ->from(['config' => HcHireTypeConfigModel::class]);

        // 应用相同的查询条件
        $this->applyQueryConditions($countBuilder, $params);

        $countResult = $countBuilder->getQuery()->execute();
        return $countResult ? $countResult->getFirst()->total : 0;
    }

    /**
     * 应用查询条件
     * @description: 为查询构建器应用通用的连接和筛选条件
     * @param array $params
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function applyQueryConditions(&$builder, $params)
    {
        // 根据config_type动态连接子表
        if ($params['config_type'] == HcHireTypeConfigModel::CONFIG_TYPE_POSITION) {
            $this->applyPositionJoinAndConditions($builder, $params);
        } elseif ($params['config_type'] == HcHireTypeConfigModel::CONFIG_TYPE_BRANCH) {
            $this->applyBranchJoinAndConditions($builder, $params);
        }
        // 应用主表筛选条件
        $this->applyMainTableConditions($builder, $params);
    }

    /**
     * 应用按职位配置的连接和条件
     * @description: 为按职位配置应用连接和筛选条件
     * @param array $params
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function applyPositionJoinAndConditions(&$builder, $params)
    {
        // 连接hc_hire_type_position表
        $builder->leftJoin(HcHireTypePositionModel::class, 'position.config_id = config.id', 'position');

        // 职位筛选条件
        if (!empty($params['job_title'])) {
            if (is_array($params['job_title'])) {
                $builder->inWhere('position.job_title', $params['job_title']);
            } else {
                $builder->andWhere('position.job_title = :job_title:', ['job_title' => $params['job_title']]);
            }
        }

        // 雇佣类型筛选条件
        if (!empty($params['hire_type'])) {
            if (is_array($params['hire_type'])) {
                $builder->inWhere('position.hire_type', $params['hire_type']);
            } else {
                $builder->andWhere('position.hire_type = :hire_type:', ['hire_type' => $params['hire_type']]);
            }
        }
    }

    /**
     * 应用按网点配置的连接和条件
     * @description: 为按网点配置应用连接和筛选条件
     * @param array $params
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function applyBranchJoinAndConditions(&$builder, $params)
    {
        // 连接hc_hire_type_branch表
        $builder->leftJoin(HcHireTypeBranchModel::class, 'branch.config_id = config.id', 'branch');

        // 网点筛选条件
        if (!empty($params['store_id'])) {
            if (is_array($params['store_id'])) {
                $builder->inWhere('branch.store_id', $params['store_id']);
            } else {
                $builder->andWhere('branch.store_id = :store_id:', ['store_id' => $params['store_id']]);
            }
        }

        // 母网点筛选条件
        if (!empty($params['parent_store_id'])) {
            if (is_array($params['parent_store_id'])) {
                $builder->inWhere('branch.parent_store_id', $params['parent_store_id']);
            } else {
                $builder->andWhere('branch.parent_store_id = :parent_store_id:',
                    ['parent_store_id' => $params['parent_store_id']]);
            }
        }

        // 职位筛选条件
        if (!empty($params['job_title'])) {
            if (is_array($params['job_title'])) {
                $builder->inWhere('branch.job_title', $params['job_title']);
            } else {
                $builder->andWhere('branch.job_title = :job_title:', ['job_title' => $params['job_title']]);
            }
        }

        // 雇佣类型筛选条件
        if (!empty($params['hire_type'])) {
            if (is_array($params['hire_type'])) {
                $builder->inWhere('branch.hire_type', $params['hire_type']);
            } else {
                $builder->andWhere('branch.hire_type = :hire_type:', ['hire_type' => $params['hire_type']]);
            }
        }
    }

    /**
     * 应用主表筛选条件
     * @description: 为主表应用通用的筛选条件
     * @param Builder $builder
     * @param array $params
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function applyMainTableConditions(&$builder, $params)
    {
        // 配置类型筛选
        $builder->andWhere('config.config_type = :config_type:', [
            'config_type' => $params['config_type'] ?? HcHireTypeConfigModel::CONFIG_TYPE_POSITION,
        ]);

        // 状态筛选
        if (!empty($params['status'])) {
            $builder->inWhere('config.status', $params['status']);
        }
    }

    /**
     * 初始化配置信息
     * @param $job_title_str_arr
     * @param $store_id_str_arr
     * @return void
     */
    protected function init($job_title_str_arr, $store_id_str_arr = [])
    {
        if (!empty($job_title_str_arr)) {
            $job_title_ids = [];
            foreach ($job_title_str_arr as $item) {
                $job_title_ids = array_merge($job_title_ids, explode('/', $item));
            }
            $job_title_ids       = array_values(array_filter(array_unique($job_title_ids)));
            $jobTitleList        = (new HrJobTitleService())->getJobTitleByIds($job_title_ids,'id','status = 1');
            $this->checkJobTitle = array_column($jobTitleList, 'id');
        }
        if (!empty($store_id_str_arr)) {
            $store_id = [];
            foreach ($store_id_str_arr as $item) {
                $store_id = array_merge($store_id, explode('/', $item));
            }
            $store_id           = array_values(array_filter(array_unique($store_id)));
            $storeList          = (new SysStoreService())->getByStoreListByIds($store_id);
            $this->checkStoreId = array_column($storeList, 'id');
        }
        $this->checkHireType = (new SettingEnvService())->getSetVal('hire_type_enum', ',');
    }

    /**
     * 按职位配置导入处理
     * @description: 处理按职位配置的Excel导入数据
     * @param string $filePath 文件路径
     * @param int $operatorId 操作员ID
     * @param string $fileName 文件名
     * @param string $taskCreateTime 任务创建时间
     * @return array 返回处理结果
     * @throws BusinessException
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function dealImportPositionData($filePath, $operatorId, $fileName): array
    {
        // 获取Excel数据
        $excelData = $this->getExcelData($filePath);
        // 初始化数据和文件数据
        $data = $fileData = [];

        foreach ($excelData as $key => $datum) {
            // 表头处理
            if ($key == 0) {
                $fileData[] = $datum;
                continue;
            }

            // 去除多余空行
            if (empty($datum[0]) && empty($datum[1])) {
                continue;
            }

            // 导出使用 - 添加结果列头
            if ($key == 1) {
                $datum[]    = static::$t->_('store_material_008'); // 状态
                $datum[]    = static::$t->_('reason_error');       // 错误原因
                $fileData[] = $datum;
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            //初始化员工相关数据
            $job_title_str_arr = array_column($data, 1);
            $this->init($job_title_str_arr);
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError] = $this->processPositionRowData($item, $operatorId);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        $url = $this->makeReturnReport($fileData, $fileName, 'A1:E1', 80, 'A:E');
        return ['url' => $url, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    /**
     * 按网点配置导入处理
     * @description: 处理按网点配置的Excel导入数据
     * @param string $filePath 文件路径
     * @param int $operatorId 操作员ID
     * @param string $fileName 文件名
     * @param string $taskCreateTime 任务创建时间
     * @return array 返回处理结果
     * @throws BusinessException
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function dealImportBranchData($filePath, $operatorId, $fileName): array
    {  // 获取Excel数据
        $excelData = $this->getExcelData($filePath);

        // 初始化数据和文件数据
        $data = $fileData = [];

        foreach ($excelData as $key => $datum) {
            // 表头处理
            if ($key == 0) {
                $fileData[] = $datum;
                continue;
            }

            // 去除多余空行
            if (empty($datum[0]) && empty($datum[1]) && empty($datum[2]) && empty($datum[3])) {
                continue;
            }

            // 导出使用 - 添加结果列头
            if ($key == 1) {
                $datum[]    = static::$t->_('store_material_008'); // 状态
                $datum[]    = static::$t->_('reason_error');       // 错误原因
                $fileData[] = $datum;
                continue;
            }
            $data[] = $datum;
        }

        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            //初始化员工相关数据
            $store_str_arr     = array_column($data, 0);
            $job_title_str_arr = array_column($data, 1);
            $this->init($job_title_str_arr, $store_str_arr);
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError] = $this->processBranchRowData($item, $operatorId);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        $url = $this->makeReturnReport($fileData, $fileName, 'A1:F1', 80, 'A:F');
        return ['url' => $url, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    /**
     * 按网点配置导入处理
     * @description: 处理按网点配置的Excel导入数据
     * @param string $filePath 文件路径
     * @param int $operatorId 操作员ID
     * @param string $fileName 文件名
     * @return array 返回处理结果
     * @throws BusinessException
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function dealImportObsoleteData($filePath, $operatorId, $fileName): array
    {  // 获取Excel数据
        $excelData = $this->getExcelData($filePath);

        // 初始化数据和文件数据
        $data = $header = $fileData = [];

        foreach ($excelData as $key => $datum) {
            // 表头处理
            if ($key == 0) {
                $datum[2] = static::$t->_('store_material_008'); // 状态
                $datum[3] = static::$t->_('reason_error');       // 错误原因
                $header[] = $datum;
                continue;
            }

            // 去除多余空行
            if (empty($datum[0]) && empty($datum[1])) {
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            //初始化员工相关数据
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError] = $this->processObsoleteRowData($item, $operatorId);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        $url = $this->makeReturnReport(array_merge($header, $fileData), $fileName, null);
        return ['url' => $url, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    /**
     * 获取Excel数据
     * @description: 下载并解析Excel文件
     * @param string $importPath OSS文件路径
     * @return array
     * @throws BusinessException
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function getExcelData($importPath, $setColumnType = [])
    {
        $fileName  = basename($importPath);
        $fileInfo  = pathinfo($fileName);
        $localName = 'hc_hire_type_import_' . date('YmdHis') . '.' . $fileInfo['extension'];
        $filePath  = sys_get_temp_dir() . '/' . $localName;

        // 下载文件到本地
        if (!file_put_contents($filePath, file_get_contents($importPath))) {
            throw new BusinessException('文件下载失败');
        }

        try {
            $excelService = new ExcelService();
            $data         = $excelService->getExcelData($filePath);

            if (empty($data)) {
                throw new BusinessException('Excel文件为空');
            }

            return $data;
        } finally {
            // 清理临时文件
            @unlink($filePath);
        }
    }

    /**
     * 处理职位数据行
     * @description: 处理单行职位配置数据，采用事务处理和操作记录
     * @param array $datum 行数据
     * @param int $operatorId 操作员ID
     * @return array 处理结果
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function processPositionRowData($datum, $operatorId): array
    {
        $errorMsg = '';
        $status   = static::$t->_('success');
        // 开启事务
        $db = BackyardBaseModel::beginTransaction($this);

        try {
            // 验证数据格式
            $validationResult = $this->validatePositionRowData($datum);
            if (!$validationResult['valid']) {
                throw new ValidationException($validationResult['error']);
            }
            // 构建数据
            $rowData = [
                'job_titles'  => $validationResult['job_title_arr'],
                'hire_types'  => $validationResult['hire_type_arr'],
                'operator_id' => $operatorId,
                'remark'      => $datum[2],
            ];

            // 保存配置数据
            $saveResult = $this->savePositionConfig($rowData);
            if (!$saveResult) {
                throw new \Exception('保存职位配置失败');
            }

            // 提交事务
            $db->commit();
        } catch (ValidationException $e) {
            // 回滚事务
            $db->rollback();
            $status   = static::$t->_('fail');
            $errorMsg = $e->getMessage();
            $datum[]  = $status;
            $datum[]  = $errorMsg;
            return [$datum, true];
        } catch (Exception $e) {
            throw $e;
            // 回滚事务
            $db->rollback();
            $status   = static::$t->_('fail');
            $errorMsg = self::$t->_('retry_later');
            $datum[]  = $status;
            $datum[]  = $errorMsg;
            return [$datum, true];
        }

        // 添加状态和错误信息到文件行
        $datum[] = $status;
        $datum[] = $errorMsg;
        return [$datum, false];
    }

    /**
     * 处理网点数据行
     * @description: 处理单行网点配置数据
     * @param array $datum 行数据
     * @param int $operatorId 操作员ID
     * @return array 处理结果
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function processBranchRowData($datum, $operatorId, $parent_store_id = ''): array
    {
        $errorMsg = '';
        $status   = static::$t->_('success');
        // 开启事务
        $db = BackyardBaseModel::beginTransaction($this);

        try {
            // 验证数据格式
            $validationResult = $this->validateBranchRowData($datum, $parent_store_id);
            if (!$validationResult['valid']) {
                throw new ValidationException($validationResult['error']);
            }
            // 构建数据
            $rowData = [
                'store_id'        => $validationResult['store_id'],
                'job_titles'      => $validationResult['job_title_arr'],
                'hire_types'      => $validationResult['hire_type_arr'],
                'operator_id'     => $operatorId,
                'parent_store_id' => $parent_store_id,
                'remark'          => $datum[3] ?? '',
            ];

            // 保存配置数据
            $saveResult = $this->saveBranchConfig($rowData);
            if (!$saveResult) {
                throw new \Exception('保存职位配置失败');
            }

            // 提交事务
            $db->commit();
        } catch (ValidationException $e) {
            // 回滚事务
            $db->rollback();
            $status   = static::$t->_('fail');
            $errorMsg = $e->getMessage();
            $datum[]  = $status;
            $datum[]  = $errorMsg;
            return [$datum, true];
        } catch (Exception $e) {
            throw $e;
            // 回滚事务
            $db->rollback();
            $status   = static::$t->_('fail');
            $errorMsg = self::$t->_('retry_later');
            $datum[]  = $status;
            $datum[]  = $errorMsg;
            return [$datum, true];
        }

        // 添加状态和错误信息到文件行
        $datum[] = $status;
        $datum[] = $errorMsg;
        return [$datum, false];
    }

    /**
     * 处理作废
     * @param $datum
     * @param $operatorId
     * @return array
     */
    private function processObsoleteRowData($datum, $operatorId): array
    {
        $errorMsg = '';
        $status   = static::$t->_('success');
        // 开启事务

        try {
            $params['id']     = $datum[0];
            $params['remark'] = $datum[1];
            if (empty($params['remark'])) {
                throw new ValidationException(self::$t->_('22369_remark_error'));
            }
            $this->obsolete($params, $operatorId);
        } catch (ValidationException $e) {
            $status   = static::$t->_('fail');
            $errorMsg = $e->getMessage();
            $datum[]  = $status;
            $datum[]  = $errorMsg;
            return [$datum, true];
        } catch (Exception $e) {
            $status   = static::$t->_('fail');
            $errorMsg = self::$t->_('retry_later');
            $datum[]  = $status;
            $datum[]  = $errorMsg;
            return [$datum, true];
        }

        // 添加状态和错误信息到文件行
        $datum[] = $status;
        $datum[] = $errorMsg;
        return [$datum, false];
    }


    /**
     * 生成返回报告
     * @description: 生成导入结果报告并上传到OSS
     * @param array $fileData 文件数据
     * @param string $fileName 原文件名
     * @param string $rowRange
     * @param int $cellHeight
     * @param string $columnRange
     * @param int $width
     * @return string OSS文件路径
     * @throws OssException
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function makeReturnReport(
        $fileData,
        $fileName,
        $rowRange = 'A1:G1',
        $cellHeight = 170,
        $columnRange = 'A:G',
        $width = 16
    ) {
        $excel     = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
        $excel     = $excel->fileName($fileName);
        $format    = new \Vtiful\Kernel\Format($excel->getHandle());
        $wrapStyle = $format->wrap()->toResource();
        foreach ($fileData as $row => $rowItem) {
            foreach ($rowItem as $column => $colItem) {
                if ($row == 0 && $rowRange) {
                    if ($column == 0) {
                        $excel->mergeCells($rowRange, $colItem);
                        $excel->setRow($rowRange, $cellHeight, $wrapStyle);
                        $excel->setColumn($columnRange, $width);
                    } else {
                        break;
                    }
                } else {
                    $excel->insertText($row, $column, $colItem);
                }
            }
        }
        $filePath = $excel->output();

        $flashOss = new FlashOss();
        $object   = 'hc_hire_type_config/' . date('Ymd') . '/' . $fileName;
        $flashOss->uploadFile($object, $filePath, [
            OssClient::OSS_CONTENT_DISPOSTION => 'attachment',
            OssClient::OSS_CONTENT_TYPE       => OssClient::DEFAULT_CONTENT_TYPE,
        ]);
        if (RUNTIME == 'dev') {
            $ulr = $flashOss->signUrl($object);
            var_dump($ulr);
        }
        return $object;
    }

    /**
     * 创建操作日志
     * @description: 创建操作日志记录
     * @param int $configId 配置ID
     * @param int $operationType 操作类型
     * @param int $operatorId 操作人ID
     * @param string $remark 备注
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    protected function createOperationLog($configId, $operationType, $operatorId, $remark = '')
    {
        static $userInfo = [];
        if (empty($userInfo)) {
            $userInfo = (new StaffService())->getStaffInfoSimplify(['staff_info_id' => $operatorId]);
        }
        $log                 = new HcHireTypeOperationLogModel();
        $log->config_id      = $configId;
        $log->operation_type = $operationType;
        if ($operationType == HcHireTypeOperationLogModel::OPERATION_TYPE_EXPIRE) {
            $log->expire_time = date('Y-m-d H:i:s');
        } else {
            $log->effective_time = date('Y-m-d H:i:s');
        }
        $log->operator_id   = $operatorId;
        $log->operator_name = $userInfo['name'] ?? '';
        $log->remark        = $remark;
        $log->save();
    }

    /**
     * 验证职位行数据
     * @description: 验证职位配置行数据的有效性
     * @param array $datum 行数据
     * @return array 验证结果
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function validatePositionRowData($datum): array
    {
        if (empty($datum[0])  || strlen($datum[0]) > 200) {
            return ['valid' => false, 'error' => self::$t->_('22369_hire_type_error')];
        }
        if (empty($datum[1])  || strlen($datum[1]) > 200) {
            return ['valid' => false, 'error' => self::$t->_('22369_job_title_error')];
        }
        if (empty($datum[2])) {
            return ['valid' => false, 'error' => self::$t->_('22369_remark_error')];
        }
        $jobTitle = array_values(array_unique(array_map(function ($item) {
            return trim($item);
        }, explode('/', $datum[1]))));

        foreach ($jobTitle as $jt) {
            if (!in_array($jt, $this->checkJobTitle)) {
                return ['valid' => false, 'error' => self::$t->_('22369_job_title_error')];
            }
        }

        $hireType = array_values(array_unique(array_map(function ($item) {
            return trim($item);
        }, explode('/', $datum[0]))));

        foreach ($hireType as $ht) {
            if (!in_array($ht, $this->checkHireType)) {
                return ['valid' => false, 'error' => self::$t->_('22369_hire_type_error')];
            }
        }

        $model = HcHireTypePositionModel::findFirst(
            [
                'conditions' => 'hire_type in ({hire_type:array}) AND job_title in ({job_title:array}) and status = :status:',
                'bind'       => [
                    'hire_type' => $hireType,
                    'job_title' => $jobTitle,
                    'status'    => HcHireTypePositionModel::STATUS_ACTIVE,
                ],
            ]
        );
        if (!empty($model)) {
            return ['valid' => false, 'error' => self::$t->_('22369_config_error_1')];
        }


        return ['valid' => true, 'error' => '', 'job_title_arr' => $jobTitle, 'hire_type_arr' => $hireType];
    }

    /**
     * 验证网点行数据
     * @description: 验证网点配置行数据的有效性
     * @param array $datum 行数据
     * @param string $parent_store_id
     * @return array 验证结果
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    private function validateBranchRowData($datum, string $parent_store_id = ''): array
    {
        if (empty($datum[0])) {
            return ['valid' => false, 'error' => self::$t->_('22369_branch_id_error')];
        }
        $storeId = trim($datum[0]);

        if (!$parent_store_id && !in_array($storeId, $this->checkStoreId)) {
            return ['valid' => false, 'error' => self::$t->_('22369_branch_id_error')];
        }

        if (empty($datum[1]) || strlen($datum[1]) > 200) {
            return ['valid' => false, 'error' => self::$t->_('22369_job_title_error')];
        }
        if (empty($datum[2]) || strlen($datum[2]) > 200) {
            return ['valid' => false, 'error' => self::$t->_('22369_hire_type_error')];
        }
        if (empty($datum[3])) {
            return ['valid' => false, 'error' => self::$t->_('22369_remark_error')];
        }
        $jobTitle = array_values(array_unique(array_map(function ($item) {
            return trim($item);
        }, explode('/', $datum[1]))));

        foreach ($jobTitle as $jt) {
            if (!$parent_store_id && !in_array($jt, $this->checkJobTitle)) {
                return ['valid' => false, 'error' => self::$t->_('22369_job_title_error')];
            }
        }

        $hireType = array_values(array_unique(array_map(function ($item) {
            return trim($item);
        }, explode('/', $datum[2]))));

        foreach ($hireType as $ht) {
            if (!$parent_store_id && !in_array($ht, $this->checkHireType)) {
                return ['valid' => false, 'error' => self::$t->_('22369_hire_type_error')];
            }
        }

        $model = HcHireTypeBranchModel::findFirst(
            [
                'conditions' => 'store_id = :store_id: and  hire_type in ({hire_type:array}) AND job_title in ({job_title:array}) and status = :status:',
                'bind'       => [
                    'store_id'  => $storeId,
                    'hire_type' => $hireType,
                    'job_title' => $jobTitle,
                    'status'    => HcHireTypeBranchModel::STATUS_ACTIVE,
                ],
            ]
        );
        if (!empty($model)) {
            return ['valid' => false, 'error' => self::$t->_('22369_config_error_1')];
        }

        //互斥的雇佣类型
        $mutual_exclusion_hire_type = [
            HrStaffInfoModel::HIRE_TYPE_1,
            HrStaffInfoModel::HIRE_TYPE_2,
            HrStaffInfoModel::HIRE_TYPE_3,
            HrStaffInfoModel::HIRE_TYPE_4,
        ];
        // - 网点、职位与列表重复（网点重复，且职位至少1个重复），且列表中状态为生效中，但雇佣类型不同，失败原因：同网点同职位的雇佣类型不同

        // 检查是否存在互斥的雇佣类型配置

        $currentMutualTypes = array_intersect($hireType, $mutual_exclusion_hire_type);
        if (count(array_unique($currentMutualTypes)) > 1) {
            return ['valid' => false, 'error' => self::$t->_('22369_hire_type_conflict_1')];
        }

        if (!empty($currentMutualTypes)) {
            $conflictModel = HcHireTypeBranchModel::findFirst(
                [
                    'conditions' => 'store_id = :store_id: AND job_title in ({job_title:array}) and hire_type in ({hire_type:array}) and status = :status:',
                    'bind'       => [
                        'store_id'  => $storeId,
                        'job_title' => $jobTitle,
                        'status'    => HcHireTypeBranchModel::STATUS_ACTIVE,
                        'hire_type' => array_diff($mutual_exclusion_hire_type, $hireType),
                    ],
                ]
            );
            if ($conflictModel) {
                return ['valid' => false, 'error' => self::$t->_('22369_hire_type_conflict_2')];
            }
        }

        return [
            'valid'         => true,
            'error'         => '',
            'job_title_arr' => $jobTitle,
            'hire_type_arr' => $hireType,
            'store_id'      => $storeId,
        ];
    }

    /**
     * 保存职位配置
     * @description: 保存职位配置到数据库
     * @param array $data 配置数据
     * @return bool
     * @throws Exception
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function savePositionConfig($data): bool
    {
        // 保存主配置记录
        $config                 = new HcHireTypeConfigModel();
        $config->config_type    = HcHireTypeConfigModel::CONFIG_TYPE_POSITION;
        $config->job_titles     = json_encode($data['job_titles'], JSON_UNESCAPED_UNICODE);
        $config->hire_types     = json_encode($data['hire_types'], JSON_UNESCAPED_UNICODE);
        $config->operator_id    = $data['operator_id'];
        $config->effective_time = date('Y-m-d H:i:s');
        $config->status         = HcHireTypeConfigModel::STATUS_ACTIVE;
        $config->save();
        $configId   = $config->id;
        $insertData = [];
        foreach ($data['job_titles'] as $job_title) {
            foreach ($data['hire_types'] as $hire_type) {
                $insertData[] = [
                    'config_id'      => $configId,
                    'job_title'      => $job_title,
                    'hire_type'      => $hire_type,
                    'status'         => $config->status,
                    'operator_id'    => $data['operator_id'],
                    'effective_time' => $config->effective_time,
                ];
            }
        }
        // 将hire_type按/分割并保存到子表
        if (!empty($insertData)) {
            (new HcHireTypePositionModel())->batch_insert($insertData);
        }
        // 记录操作日志
        $this->createOperationLog(
            $configId,
            HcHireTypeOperationLogModel::OPERATION_TYPE_IMPORT,
            $data['operator_id'],
            mb_substr($data['remark'], 0, 200)
        );
        return true;
    }

    /**
     * 保存网点配置
     * @description: 保存网点配置到数据库
     * @param array $data 配置数据
     * @return bool
     * @author: AI
     * @date: 2024-12-19 10:30:00
     */
    public function saveBranchConfig($data)
    {
        // 保存主配置记录
        $config                  = new HcHireTypeConfigModel();
        $config->config_type     = HcHireTypeConfigModel::CONFIG_TYPE_BRANCH;
        $config->store_id        = $data['store_id'];
        $config->job_titles      = json_encode($data['job_titles'], JSON_UNESCAPED_UNICODE);
        $config->hire_types      = json_encode($data['hire_types'], JSON_UNESCAPED_UNICODE);
        $config->operator_id     = $data['operator_id'];
        $config->parent_store_id = $data['parent_store_id'];
        $config->effective_time  = $data['effective_time'] ?? date('Y-m-d H:i:s');
        $config->expire_time     = $data['expire_time'] ?? null;
        $config->status          = empty($config->expire_time) ? HcHireTypeConfigModel::STATUS_ACTIVE : HcHireTypeConfigModel::STATUS_INACTIVE;

        $config->save();
        $configId   = $config->id;
        $insertData = [];
        foreach ($data['job_titles'] as $job_title) {
            foreach ($data['hire_types'] as $hire_type) {
                $insertData[] = [
                    'store_id'        => $data['store_id'],
                    'parent_store_id' => $data['parent_store_id'],
                    'config_id'       => $configId,
                    'job_title'       => $job_title,
                    'hire_type'       => $hire_type,
                    'status'          => $config->status,
                    'operator_id'     => $data['operator_id'],
                    'effective_time'  => $config->effective_time,
                    'expire_time'     => $config->expire_time,
                ];
            }
        }
        // 将hire_type按/分割并保存到子表
        if (!empty($insertData)) {
            (new HcHireTypeBranchModel())->batch_insert($insertData);
        }
        // 记录操作日志
        $this->createOperationLog(
            $configId,
            HcHireTypeOperationLogModel::OPERATION_TYPE_IMPORT,
            $data['operator_id'],
            mb_substr($data['remark'], 0, 200)
        );
        if ($config->status == HcHireTypeConfigModel::STATUS_INACTIVE) {
            $this->createOperationLog(
                $configId,
                HcHireTypeOperationLogModel::OPERATION_TYPE_EXPIRE,
                $data['operator_id'],
                mb_substr($data['remark'], 0, 200)
            );
        }
        return true;
    }

    /**
     * 获取操作记录
     * @param $config_id
     * @return array
     */
    public function operatorLogList($config_id): array
    {
        $data = HcHireTypeOperationLogModel::find([
            'conditions' => 'config_id = :config_id: ',
            'bind'       => [
                'config_id' => $config_id,

            ],
            'columns'    => 'operator_id,operator_name,effective_time,expire_time,remark',
        ])->toArray();
        foreach ($data as &$datum) {
            $datum['operator_name'] = $datum['operator_name'] . '(' . $datum['operator_id'] . ')';
        }
        return ['list' => $data];
    }


    /**
     * 同步拆分网点
     * @param $start_time
     * @param $end_time
     * @param $store_id
     * @return bool
     * @throws ValidationException
     */
    public function syncSubStore($start_time, $end_time, $store_id): bool
    {
        [$condition, $bind] = (new SysStoreService())->notVirtualStoreCondition();
        $condition .= " and created_at >= :start: and created_at <= :end: and  removed_store_id  is not null and removed_store_id <> '' ";
        $bind      = array_merge($bind, ['start' => $start_time, 'end' => $end_time]);
        if (!empty($store_id)) {
            $bind['store_id'] = $store_id;
            $condition        .= " and id = :store_id:";
        }
        $findData = SysStoreModel::find([
            'columns'    => 'id,removed_store_id,name',
            'conditions' => $condition,
            'bind'       => $bind,
        ])->toArray();
        if(empty($findData)){
            $this->logger->info(['sync_sub_store' => 'empty']);
            return true;
        }

        $email_store_list = [];
        foreach ($findData as $datum) {
            if (empty($datum['removed_store_id'])) {
                continue;
            }

            //已存在 先跳过
            $check_exist = HcHireTypeConfigModel::findFirst([
                'conditions' => 'store_id = :store_id: and config_type = :config_type:',
                'bind'       => [
                    'store_id'    => $datum['id'],
                    'config_type' => HcHireTypeConfigModel::CONFIG_TYPE_BRANCH
                ],
            ]);
            if ($check_exist) {
                continue;
            }

            $removed_store_ids  = array_values(array_unique(explode(',', $datum['removed_store_id'])));
            $removed_store_num = count($removed_store_ids);
            $this->logger->info(['sync_sub_store' => $datum,'removed_store_num'=>$removed_store_num]);
            if ($removed_store_num == 1) {
                $parent_config = HcHireTypeConfigModel::find([
                    'conditions' => 'store_id in ({store_ids:array}) and config_type = :config_type: and status = :status:',
                    'bind'       => [
                        'store_ids'    => $removed_store_ids,
                        'config_type' => HcHireTypeConfigModel::CONFIG_TYPE_BRANCH,
                        'status'      => HcHireTypeConfigModel::STATUS_ACTIVE,
                    ],
                ])->toArray();
                if (!empty($parent_config)) {
                    //调用批量导入的方法
                    foreach ($parent_config as $item) {
                        $insertData[0] = $datum['id'];
                        $insertData[1] = implode('/', json_decode($item['job_titles'], true));
                        $insertData[2] = implode('/', json_decode($item['hire_types'], true));
                        $insertData[3] = 'Branch split';
                        [$res, $haveError] = $this->processBranchRowData($insertData, 10000, $item['store_id']);
                        if ($haveError) {
                            var_dump(['res' => $res, 'insertData' => $insertData, 'sync_sub_store_error' => 1]);
                            $this->logger->error(['sync_sub_store_error' => $insertData, 'res' => $res]);
                        }
                    }
                }
            } elseif ($removed_store_num > 1) {
                $email_store_list[] = ['store_id' => $datum['id'], 'store_name' => $datum['name']];
            }
        }
        $this->logger->info(['email_store_list' => $email_store_list]);
        $config_notice = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => 'hc_hire_type_error_store_notice'],
        ]);
        if (empty($config_notice)) {
            $config_notice         = new SettingEnvModel();
            $config_notice->code   = 'hc_hire_type_error_store_notice';
            $config_notice->remark = 'HC雇佣类型拆分同步异常网点';
        }
        if ($email_store_list) {
            $before_val             = $config_notice->set_val ? json_decode($config_notice->set_val, true) : [];
            $all_store_list         = array_values(array_column(array_merge($email_store_list, $before_val), null,
                'store_id'));
            $config_notice->set_val = json_encode($all_store_list, JSON_UNESCAPED_UNICODE);
            $config_notice->save();
        }
        return true;
    }

    /**
     * 网点拆分继承新增的异常网点 邮件提醒
     * @return bool
     * @throws \PHPMailer\PHPMailer\Exception
     */
    public function extendErrorStoreEmail(): bool
    {
        $config_notice = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => 'hc_hire_type_error_store_notice'],
        ]);

        if (empty($config_notice) || empty($config_notice->set_val)) {
            $this->logger->info(['email_store_list' => 'empty']);
            return true;
        }
        $email_store_list = json_decode($config_notice->set_val, true);

        $this->logger->info(['email_store_list' => $email_store_list]);
        $daily_employees_branch_emails = (new SettingEnvService())->getSetVal('daily_employees_branch_emails', ',');

        $email_store_list = array_map(function ($item) {
            return [$item['store_id'], $item['store_name']];
        }, $email_store_list);
        $file_name                       = 'List of New Branches ' . date('Y-m-d') . ".xlsx";
        $daily_employees_branch_emails[] = '<EMAIL>';
        $res                             = $this->exportExcel(['网点ID', '网点名称'], $email_store_list, $file_name);
        $config_notice->set_val          = '';
        $config_notice->save();
        $filePath = $res['data'];
        return $daily_employees_branch_emails && (new \App\Library\Mailer((new BiMail)->initConfig()))->send($daily_employees_branch_emails,
                $this->daily_contract_mail_title,
                $this->daily_contract_mail_content, [$filePath]);
    }
}