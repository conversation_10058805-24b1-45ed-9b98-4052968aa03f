<?php


namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\MsAccessPermissionEnums;
use App\Library\Enums\RedisEnums;
use App\Library\Enums\SalaryEnums;
use App\Library\Exception\BusinessException;
use App\Library\PasswordHash;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmPermissionModel;
use App\Models\backyard\HcmStaffPermissionInfoModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\fle\StaffInfoModel;
use App\Models\fle\StaffInfoPositionModel;
use app\traits\FlashTokenTrait;
use Stringy\StaticStringy;
use Exception;

/**
 * Class UserService
 * @package App\Services
 */
class UserService extends BaseService
{
    use FlashTokenTrait;
    //staff session_id key
    const STAFF_SESSION_ID_KEY = 'staff_session_id:%s';
    //staff session_id 过期时长 三天
    const STAFF_SESSION_ID_EXPIRE_TIME = 86400*3;
    const PLATFORM_MS = '_ms';
    const PLATFORM_FBI = 'fbi';
    const PLATFORM = array(self::PLATFORM_MS,self::PLATFORM_FBI);

    //设置 staff session_id
    public function setStaffSessionId($uid, $session_id)
    {
        return $this->cache->save(sprintf(self::STAFF_SESSION_ID_KEY, $uid), $session_id, self::STAFF_SESSION_ID_EXPIRE_TIME);
    }
    //获取 staff session_id
    public function getStaffSessionId($uid)
    {
        return  $this->cache->get(sprintf(self::STAFF_SESSION_ID_KEY, $uid));
    }
    //删除 staff session_id
    public function delStaffSessionId($uid)
    {
        return  $this->cache->delete(sprintf(self::STAFF_SESSION_ID_KEY, $uid));

    }


    /**
     * 获取二维码状态
     * @param $params
     * @return array|bool|mixed
     */
    public function polling($params)
    {
        $hr_rpc = (new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffAuthSvc', 'qrLogin', self::$language));
        $hr_rpc->withParam($params);
        $result = $hr_rpc->execute();
        if (isset($result['error'])) {
            if ($result['code'] > 5) {
                $result['code'] = 100508;
            }
            $result['qrstate'] = $result['code'];
            return $result;
        }
        if ($result['qrstate'] == 2) {
            $result            = $this->getToken($result['id']);
            $result['qrstate'] = 2;
        }
        return $result;
    }


    /**
     * 获取二维码
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function getQRCode($params)
    {
        $params['equipment_type'] = 16;
        $params['ip']             = getRealIp();
        $hr_rpc                   = (new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffAuthSvc', 'getQrcode', self::$language));
        $hr_rpc->withParam($params);
        $result = $hr_rpc->execute();
        if (isset($result['error'])) {
            throw new BusinessException($result['message']);
        }
        return $result;
    }



    /**
     * staff 登录
     * @param $params
     * @return array
     * @throws Exception
     */
    public function userLogin($params)
    {
        if (RUNTIME == 'pro' && !isCountry(['vn', 'id'])) {
            throw new BusinessException('Please Use Scan Code Login System');
        }

        $user = $this->getUserById($params['login']);

        if (!$user) {
            throw new BusinessException('Incorrect Account or Password');
        }
        if ($user->state != 1) {
            throw new BusinessException('Account not allowed to Access');
        }

        $hash = new PasswordHash(10, false);
        if (!$hash->CheckPassword($params['password'], $user->encrypted_password)) {
            throw new BusinessException('Incorrect Account or Password');
        }
        return $this->getToken($user->id);
    }

    /**
     * 颁发临时票据
     * @param $locale
     * @param array $params
     * @return string
     * @throws BusinessException|ValidationException
     */
    public function ticketLoginUrl($locale, array $params): string
    {
        self::setLanguage($locale['locale']);
        $validation = [
            'staff_info_id' => 'Required|Int|>>>:' . self::$t->_('input_staffinfoid_error'),
        ];
        Validation::validate($params, $validation);
        $staff_info_id = $params['staff_info_id'];
        $ticket        = uniqid('T' . $staff_info_id . 'T');
        $staff_session = $this->setCache(sprintf(RedisEnums::HCM_LOGIN_TMP_TICKET,
            $ticket), $staff_info_id, 600);
        if (!$staff_session) {
            throw new BusinessException(self::$t->_('server_error'));
        }

        $hcm_url = (new SettingEnvService())->getSetVal('hcm_ui_url');
        if (empty($hcm_url)) {
            throw new Exception('hcm_ui_url setting  is not set');
        }

        $param['auth_code'] = $ticket;
        $param['l']         = $locale['locale'];
        return ($hcm_url . 'home?' . http_build_query($param));
    }


    /**
     * 根据ticket票证登录
     * @param $ticket
     * @return array
     * @throws BusinessException
     */
    public function loginByTicket($ticket): array
    {
        $cache_key = sprintf(RedisEnums::HCM_LOGIN_TMP_TICKET,
            $ticket);
        $staff_info_id = $this->getCache($cache_key);
        if (empty($staff_info_id)) {
            throw new BusinessException(self::$t->_('ticket_expired'));
        }
        RUNTIME != 'dev' && $this->delCache($cache_key);
        return $this->getToken($staff_info_id);
    }


    
    protected function getToken($staff_info_id)
    {
        $token      = $this->generateSessionId($staff_info_id);
        $hrisToken  = $this->hrisToken($staff_info_id);
        $winhrToken = $this->winhrToken($staff_info_id);
        return ['token' => $token, 'hris_token' => $hrisToken, 'winhr_token' => $winhrToken];
    }



    /**
     * @throws BusinessException
     */
    private function hrisToken($uid)
    {
        $time = time();
        $pwd = md5('fbi' . $uid . $time) . $time;
        $tokenPostData = [
            'username' => $uid,
            'password' => $pwd,
            'expireTime' => env('TOKEN_EXPIRATION', 86400),
        ];
        $hr_rpc = (new ApiClient('hris', '', 'login', self::$language));
        $hr_rpc->withParam($tokenPostData);
        $result = $hr_rpc->execute();
        $this->logger->info("re_login hrisToken {$uid} " . json_encode($result));
        if (isset($result['token'])) {
            return $result['token'];
        }
        throw new BusinessException(self::$t->_('server_error'));
    }

    /**
     * winhr Token获取
     * @param $staffId
     * @return mixed|string
     * @throws BusinessException
     */
    private function winhrToken($staffId)
    {
        $ret = new ApiClient('winhr_rpc', '', 'login');
        $ret->setParamss(['staff_id' => $staffId]);
        $res = $ret->execute();
        if($res && isset($res['data']) && isset($res['data']['token'])){
            return  $res['data']['token'];
        }
        throw new BusinessException(self::$t->_('server_error'));
    }

    /**
     * 获取staff 菜单和权限
     * @param $uid
     * @return mixed
     * @throws Exception
     */
    public function getStaffMenuAndPermission($uid)
    {
        $menus = $this->getStaffMenus($uid);
        $menus = list_to_tree($menus, 'menu_id', 'ancestry', 'list');
        $data['menuList'] = array_values($menus);
        [$permissions] = $this->getUserPermissions($uid, false);

        $permissions = $permissions? array_column($permissions, 'key'):[];
        $headerData       = $this->request->getHeaders();
        if ((isset($headerData['From']) && strtolower($headerData['From'])==='fbi')) {
            $permissions = array_merge($permissions, MsAccessPermissionEnums::NEED_MERGE_PERMISSION_ROUTER);
        }
        $data['permissions'] = $permissions;
        $this->logger->info(['data'=>$data,'debug'=>'getStaffMenuAndPermission','headerData'=>$headerData,'uid'=>$uid]);
        return $data;
    }

    /**
     * nick_name toArray没有。
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getUserById($id)
    {
        return StaffInfoModel::findFirst(
            [
                'conditions' => 'id = ?1',
                'bind' => [
                    1 => $id,
                ],
            ]
        );
    }

    /**
     *
     * @param $uid
     * @param $pids
     * @param $userId-当前登录人id
     * @return bool
     * @throws Exception
     */
    public function setUserPermissions($uid, $pids, $userId)
    {
        try {
            $user = $this->getUserById($uid);
            if (!$user){
                throw new ValidationException('Staff not exists');
            }

            $permissionServer = new PermissionService();
            //过滤payroll 相关权限id
            $pids = $permissionServer->filterPayrollIds($pids);

            //查出该员工 角色权限
            $role_ids = $permissionServer->getStaffRolePermission($uid);

            //去除，角色中已有的权限。
            $pids = array_values(array_diff($pids, $role_ids));

            $permissionTree = $permissionServer->getPermissionTree($type = 0, $userId, $is_get_payroll = false);

            //去除，角色中已有的权限, 拿剩余权限，找出父级链。
            $endPids = [];
            foreach ($pids as $oneId) {
                $endPids = array_merge($endPids, $permissionServer->getFatherNode($permissionTree, $oneId));
            }
            $endPids = array_values(array_unique($endPids));

            $payrollIds = $permissionServer->getPayrollPermission();
            $sp = HcmStaffPermissionInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and permission_id not in ({permission_id:array})',
                'bind' => ['staff_id' => $uid, 'permission_id' => $payrollIds],
            ]);

            if (!empty($sp)) {
                $permissionServer->cleanStaffPermission($uid, implode(',', $payrollIds));//硬删除
            }
            //设置新权限
            if (!empty($endPids)) {
                $this->setOneStaffPermission($endPids, $uid, $userId);
            }

            return true;

        }catch (Exception $e){
            throw $e;
        }

    }

    /**
     *
     * @param $uid
     * @param $pids
     * @param $userId-当前登录人id
     * @return bool
     * @throws Exception
     */
    public function setUserPayrollPermissions($uid, $pids, $userId)
    {
        try {

            $permissionServer = new PermissionService();
            $payrollIds = $permissionServer->getPayrollPermission();

            //将提交的 菜单id 与payroll全部id 做交集。目的：为了过滤除payroll之外的id
            $pids = array_intersect($payrollIds, $pids);

            $user = $this->getUserById($uid);
            if (!$user){
                throw new ValidationException('Staff not exists');
            }

            $sp = HcmStaffPermissionInfoModel::find([
                'conditions' => 'staff_info_id = :staff_id: and permission_id in ({permission_id:array})',
                'bind' => ['staff_id' => $uid, 'permission_id' => $payrollIds],
                'columns' => ['permission_id'],
            ])->toArray();

            if ($sp) {
                $sp = array_column($sp, 'permission_id');

                $permissionServer->cleanStaffPayrollPermission($uid, implode(',', $payrollIds));//硬删除
            }
            $logPid = $pids;
            //设置新权限
            if(!empty($pids)) {
                $pids[] = HcmPermissionModel::PAYROLL_PERMISSION_ID;
                $this->setOneStaffPermission($pids, $uid, $userId);
            }

            $this->addPermissionLog($logPid, $sp, $uid, $userId);

            return true;
        }catch (Exception $e){
            throw $e;
        }
    }

    public function addPermissionLog($pids, $sp, $uid, $userId)
    {
        $permissionServer = new PermissionService();

        $permissions = $permissionServer->getPermissionTree(0, $userId,true);
        $translation = BaseService::getTranslation('zh');

        $menu = [];
        foreach ($permissions as $one) {
            $menu[$one['id']] = $one['t_key'] ? $translation->t($one['t_key']) : $one['name'];
            if(!empty($one['children'])) {
                foreach ($one['children'] as $oneChild) {
                    $oneChildName = $oneChild['t_key'] ? $translation->t($oneChild['t_key']) : $oneChild['name'];
                    $menu[$oneChild['id']] = $menu[$one['id']] . '-' . $oneChildName;
                }
            }
        }
        $before = $after = $beforePermission = $afterPermission =[];
        foreach ($sp as $oneSp) {
            if(isset($menu[$oneSp])) {
                $beforePermission['menu_' . $oneSp] = $menu[$oneSp] ?? '';
            }
        }

        foreach ($pids as $onePid) {
            if(isset($menu[$onePid])) {
                $afterPermission['menu_' . $onePid] = $menu[$onePid] ?? '';
            }
        }

        $before['body']                     = $beforePermission;
        $after['body']                      = $afterPermission;

        $data =[
            [
                "operater"      => $userId,
                "staff_info_id" => $uid,
                "type"          => 'payroll_permission',
                "before"        => json_encode($before, JSON_UNESCAPED_UNICODE),
                "after"         => json_encode($after, JSON_UNESCAPED_UNICODE),
            ],
        ];
        $client =  new ApiClient('hris','','add_operate_logs');
        $client->setParams($data);
        return  $client->execute();
    }


    /**
     * 设置新权限
     * @param $pids
     * @param $staffId
     * @param $userId
     * @return bool
     * @throws \Exception
     */
    public function setOneStaffPermission($pids, $staffId, $userId)
    {
        $allStaffPermission = [];
        foreach ($pids as $id) {
            $staffPermission['staff_info_id'] = $staffId;
            $staffPermission['permission_id'] = $id;
            $staffPermission['operator_id'] = $userId;
            $allStaffPermission[] = $staffPermission;
        }
        return (new BaseModel())->table_batch_insert($allStaffPermission, 'db_backyard', 'hcm_staff_permission_info');
    }

    /**
     * 查询工号，是否在 setting_env:staff_salary_permission_info 中有配置
     * 目的：兼容对工号保存权限信息时,如果工号有配置薪资相关权限，则补充。
     * 废弃
     * @param $uid
     * @return array|mixed
     */
    public function getSalaryPermission($uid)
    {
        if(empty($uid)) {
            return [];
        }
        //查询父级权限信息
        $permission_config      = SettingEnvModel::get_val('staff_salary_permission_info');
        if(empty($permission_config)) {
            return [];
        }

        $needAddStaffInfo = json_decode($permission_config, true);

        $permissionService = new PermissionService();
        $permissionInfo = $permissionService->getPermissionInfo(['t_keys' => SalaryEnums::T_KEYS]);
        if(empty($permissionInfo)) {
            return [];
        }

        //查询子级权限信息
        $permissionIds = array_column($permissionInfo, 'id');
        $childPermissionInfo = $permissionService->getPermissionInfo(['ids' => $permissionIds]);

        $permissionInfoToKey = array_column($permissionInfo, 'id','t_key');
        $allPermissionInfo = [];
        foreach ($permissionInfoToKey as $key => $id) {
            $allPermissionInfo[$key][] = $id;
            foreach ($childPermissionInfo as $oneChildInfo) {
                if($oneChildInfo['ancestry'] == $id) {
                    $allPermissionInfo[$key][] = $oneChildInfo['id'];
                }
            }
        }
        //整理出，工号，需要添加的权限id
        $needAddStaffInfoPermission = [];
        foreach ($needAddStaffInfo as $oneStaff) {
            if(isset($allPermissionInfo[$oneStaff['t_key']])) {
                $needAddStaffInfoPermission[$oneStaff['staff_id']] = array_merge($needAddStaffInfoPermission[$oneStaff['staff_id']] ?? [4], $allPermissionInfo[$oneStaff['t_key']]);

            }
        }
        return $needAddStaffInfoPermission[$uid] ?? [];
    }
    /**
     * 获取staff菜单
     * @param [type] $staff_id
     * @return array|\Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getStaffMenus($staff_id)
    {
        $menus = [];
        $menuService = new MenuService();
        $headerData       = $this->request->getHeaders();

        $is_delete_payroll_manager = false;//是否删除 payroll权限管理菜单
        if(!$this->checkPayrollWhiteList($staff_id)) {
            $is_delete_payroll_manager = true;
        }


        //ms过来的，由于前端根据该接口来展示页面，所以菜单都返回，让他们看到，但是数据接口只有指定范围的。
        //兼容fbi 与ms 同理。
        if (in_array($staff_id, explode(',', env('sa_id')))) {
            $menus = $menuService->getMenuPermissions();
        } elseif ((isset($headerData['From']) && (strtolower($headerData['From']) === '_ms') || strtolower($headerData['From']) === 'fbi')) {
            $menus      = $menuService->getMenuPermissions();
            $hiddenPayrollIds = (new PermissionService())->getPayrollPermission();
        } else {
            $permissionService = new PermissionService();
            [$pids] =  $permissionService->getUserPermissionIds($staff_id);
            if($pids){
                if(!$is_delete_payroll_manager) {//不删除，则添加。
                    $pids = array_merge($pids, [HcmPermissionModel::PAYROLL_MANAGER_MENU_ID, HcmPermissionModel::PERMISSION_MENU_ID]);
                }
                $menus = $menuService->getMenuPermissions($pids);
            }
        }

        if ($menus) {
            $menus = $menus->toArray();
            foreach ($menus as $key => &$item) {
                //下掉，payroll权限管理菜单
                if($is_delete_payroll_manager && $item['menu_id'] == HcmPermissionModel::PAYROLL_MANAGER_MENU_ID) {
                    unset($menus[$key]);
                    continue;
                }
                if (isset($hiddenPayrollIds) && in_array($item['menu_id'], $hiddenPayrollIds)) {
                    unset($menus[$key]);
                    continue;
                }

                $item['name'] =  static::$t->_($item['t_key'])?: $item['name'] ;
                if ($item['is_iframe'] && $item['ancestry'] > 0) {
                    $pre_url = env('hris_ui_url', 'https://dev-01-th-hris.fex.pub');
                    if(strtolower(env('country_code')) == 'vn' && $this->request->get('x-demonstration-app')){
                        $pre_url = 'https://hris-tra.xbackyard.com';
                    }
                    $item['url'] = $pre_url .$item['url'];
                }
            }
        }
        return array_values($menus);
    }

    /**
     * 获取staff权限
     * @param $uid
     * @param $filter--过滤payrollId
     * @return array
     * @throws Exception
     */
    public function getUserPermissions($uid, $filter = true)
    {
        try {
            $permissions = [];
            $user = $this->getUserById($uid);
            if(empty($user)){
                throw new ValidationException('The staff not exist');
            }
            $permissionService = new PermissionService();

            [$pids, $role_ids] =  $permissionService->getUserPermissionIds($uid);
            if($filter) {
                $pids = $permissionService->filterPayrollIds($pids);
                $role_ids = $permissionService->filterPayrollIds($role_ids);
            }
            if(!empty($pids)) {
                $permissions = $permissionService->getPermissionsByIds($pids);
                $permissions = $permissions ? $permissions->toArray() : [];
            }

            if(!empty($role_ids)) {
                $role_ids = $permissionService->getPermissionsByIds($role_ids);
                $role_ids = $role_ids ? $role_ids->toArray() : [];
            }

            return [$permissions, $role_ids];
        }catch (Exception $e){
            throw  $e;
        }
    }

    /**
     * 获取按工号查询的，菜单信息
     * @param $uid
     * @return mixed
     * @throws Exception
     */
    public function getUserPermissionsInfo($uid)
    {
        [$permissions, $rolePermissions] = $this->getUserPermissions($uid);

        if ($permissions) {
            $permissions = array_column($permissions, 'id');
        }

        if ($rolePermissions) {
            $rolePermissions = array_column($rolePermissions, 'id');
        }

        $staffInfo = $this->getOneStaffInfoById($uid);

        $data['permissions'] = $permissions;
        $data['role_permissions'] = $rolePermissions;
        $data['staff_info'] = $staffInfo;
        return $data;
    }

    /**
     * 按工号获取 员工 payroll 权限
     * @param $staff_id-查询指定工号的权限
     * @param $uid-当前登录人
     * @return mixed
     * @throws Exception
     */
    public function getUserPayRolePermissionsInfo($staff_id, $uid)
    {
        try {
            if(!$this->checkPayrollWhiteList($uid)) {
                throw new ValidationException(self::$t->_('permission_denied'));
            }
            $user = $this->getUserById($staff_id);
            if(!$user){
                throw new ValidationException('The staff not exist');
            }
            $permissionService = new PermissionService();

            $permissions =  $permissionService->getPayrollStaffPermission($staff_id);
            $staffInfo = $this->getOneStaffInfoById($staff_id);

            $data['permissions'] = $permissions;
            $data['staff_info'] = $staffInfo;
            return $data;
        }catch (Exception $e){
            throw  $e;
        }
    }

    /**
     * 身份校验
     * @param $params
     * @param $uid
     * @return array
     * @throws ValidationException
     */
    public function identityCheckInfo($params, $uid)
    {
        if($params['login'] != $uid || !$this->checkPayrollWhiteList($uid)) {
            throw new ValidationException(self::$t->_('identity_check_fail'));
        }

        $user = $this->getUserById($params['login']);

        $hash = new PasswordHash(10, false);
        if (!$hash->CheckPassword($params['password'], $user->encrypted_password)) {
            throw new BusinessException('Incorrect Account or Password');
        }
        return ['result' => true];
    }

    /**
     * 获取fle数据源员工基本信息
     * @param $staff_info_id
     * @return array
     */
    public function getFleStaffInfo($staff_info_id){
        // 查询fle 员工表信息
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['si' => StaffInfoModel::class]);
        $builder->leftjoin(StaffInfoPositionModel::class, "si.id=sip.staff_info_id",
            "sip");
        $builder->columns([
            'si.id',
            'si.name',
            'si.organization_id',
            'si.organization_type',
            'si.mobile',
            'GROUP_CONCAT(sip.position_category) position_category',
            'si.state',
            'si.department_id',
            'si.job_title',
        ]);
        $builder->andWhere('si.id = :staff_info_id:', ["staff_info_id" => $staff_info_id]);
        $staff_info_data = $builder->getQuery()->getSingleResult();
        $staff_info_data = !empty($staff_info_data) ? $staff_info_data->toArray() : [];
        return $staff_info_data;
    }

    /**
     * 获取用户信息
     * @param $staffId
     * @return mixed
     */
    public function getOneStaffInfoById($staffId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id, name, job_title, node_department_id, state, wait_leave_state',
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id' => $staffId,
                ],
            ]);

        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];

        $sysService = new SysService();
        $department_list = array_column($sysService->getDepartmentListFromCache(), 'name', 'id');

        $job_title_service = new HrJobTitleService();
        $job_title = $job_title_service->getJobTitleListFromCache();
        $job_title_list = array_column($job_title , 'job_name' , 'id');

        $positions = HrStaffInfoPositionModel::find([
            'columns' => 'position_category',
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ])->toArray();

        $roleName = '';
        if($positions) {
           $roleInfo = array_column((new RolesService())->getListFromCache(''), 'name', 'id');

           $role = [];
           foreach ($positions as $one) {
               if(isset($roleInfo[$one['position_category']])) {
                   $role[] = $roleInfo[$one['position_category']];
               }
           }

           if($role) {
               $roleName = implode(',', $role);
           }
        }
        if($staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 1) {//待离职
            $staffInfo['state'] = 999;
        }
        $state = !empty(Enums::$hris_working_state[$staffInfo['state']]) ? self::$t->_(Enums::$hris_working_state[$staffInfo['state']]) : '';

        $result['staff_info_id'] = $staffInfo['staff_info_id'];
        $result['name'] = $staffInfo['name'];
        $result['department_name'] = $department_list[$staffInfo['node_department_id']] ?? '';
        $result['job_title'] = $job_title_list[$staffInfo['job_title']] ?? '';
        $result['role_name'] = $roleName;
        $result['state'] = $state;

        return $result;
    }



    /**
     * @param $uid
     * @return array
     * @throws Exception
     */
    public function getUserActionKeys($uid)
    {

        $keys = [];
        $permissionService = new PermissionService();

        [$pids] =  $permissionService->getUserPermissionIds($uid);
        if($pids){
            $permissions = $permissionService->getPermissionsByIds($pids);
            foreach ($permissions->toArray() as $k => $v) {
                if (StaticStringy::startsWith($v['key'], 'action')) {
                    $keys[] = StaticStringy::substr($v['key'], 7);
                }
            }
        }

        return $keys;
    }

    /**
     * 查询员工信息
     * @param $staffIds
     * @return mixed
     */
    public function getStaffInfo($staffIds)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hire_type','staff_info_id', 'name', 'state', 'formal', 'is_sub_staff', 'sys_store_id', 'node_department_id']);
        $builder->from(HrStaffInfoReadModel::class);
        $builder->inWhere('staff_info_id', $staffIds);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询员工信息
     * @param $staffId
     * @param $state
     * @return mixed
     */
    public function getStaffInfoById($staffId, $state = true)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['staff_info_id', 'name', 'state', 'sys_store_id', 'node_department_id', 'job_title','hire_type']);
        $builder->from(HrStaffInfoReadModel::class);
        $builder->where('state != 2 and formal = 1 and is_sub_staff = 0');
        if (!isCountry('MY')){
            $builder->andWhere('hire_type not in ({hire_type:array})', ['hire_type' => HrStaffInfoModel::$agentTypeTogether]);
        }
        $builder->andWhere('staff_info_id LIKE :staff_id:', ['staff_id' => '%' . $staffId . '%']);
        $builder->limit(10, 0);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param $platform_token  对应平台的token
     * @param $platform 对应平台的 code
     * @throws Exception
     * @return mixed
     */
    public function checkOtherPlatform($platform_token, $platform)
    {
        if (empty($platform)) {
            throw new Exception('platform code error');
        }

        $code_arr = self::PLATFORM;
        if (!in_array($platform, $code_arr)) {
            throw new ValidationException('platform code error');
        }

        //验证 ms
        if ($platform == self::PLATFORM_MS) {
            $client = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffAuthSvc', 'getStaffInfoByToken',
                static::$language);
            $param  = [$platform_token];
            $client->setParams($param);
            $result = $client->execute();
            if (isset($result['error'])) {
                throw new ValidationException("validate from ms failed");
            }

            if (!$this->setStaffSessionId($result['id'], $platform_token)) {
                throw new Exception('Network error ms setStaffSessionId');
            }

            return $result['id'];
        }

        //验证 fbi
        if ($platform == self::PLATFORM_FBI) {
            $tokenArray = explode('_', $platform_token);
            $time       = $tokenArray[0];
            $auth       = $tokenArray[1];
            $staff_id   = $tokenArray[2];

            $checkAuth = md5('fbi'.$staff_id.$time);
            if ($checkAuth != $auth) {
                throw new ValidationException("validate from fbi failed");
            }

            if (!$this->setStaffSessionId($staff_id, $checkAuth)) {
                throw new Exception('Network error fbi setStaffSessionId');
            }
            return $staff_id;
        }

        return false;
    }


    //其他平台 免登 接口
    /**
     * @param $staff_info_id
     * @return array
     * @throws BusinessException
     */
    public function re_login($staff_info_id): array
    {
        $user = $this->getUserById($staff_info_id);
        if (empty($user)) {
            throw new BusinessException('staff id not exist');
        }
        $token = $this->generateSessionId($user->id);
        $hrisToken = $this->hrisToken($user->id);
        $this->logger->info("re_login success {$staff_info_id} {$token} {$hrisToken}");
        return ['token' => $token, 'hris_token' => $hrisToken];
    }

    /**
     * 校验payroll权限管理白名单
     * @param $staffId
     * @return bool
     */
    public function checkPayrollWhiteList($staffId)
    {
        $staffsList = (new SettingEnvService())->getSetVal('PAYROLL_PERMISSION_WHITE_LIST');

        if(!empty($staffsList)) {
            $staffList = explode(',', $staffsList);
            if(in_array($staffId, $staffList)) {
                return true;
            }
        }
        return false;
    }
}
