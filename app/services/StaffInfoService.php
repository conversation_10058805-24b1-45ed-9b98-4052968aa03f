<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\CacheKeyEnums;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffEnums;
use app\library\Enums\SuspensionEnums;
use App\Library\ErrCode;
use App\Library\HttpCurl;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\AuditLogModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffContractModel;
use App\Models\backyard\HrStaffInfoExtendModel;
use App\Models\backyard\LeaveManagerModel;
use App\Models\backyard\StaffAuditImageModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\StaffLeaveReasonModel;
use App\Models\backyard\StaffResignModel;
use App\Models\backyard\SuspensionFileModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\ThailandHolidayModel;
use App\Models\fle\SysDepartmentModel as FleSysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\fle\SysManageRegionModel;
use App\Models\fle\SysStoreModel as FleSysStoreModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\fle\StaffInfoModel;
use App\Models\fle\StaffInfoPositionModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffManageRegionModel;
use App\Models\backyard\HrStaffManagePieceModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\HrStaffManageStoreCategoryModel;

use App\Repository\AutoCreateEmailRepository;
use App\Repository\BankListRepository;
use App\Repository\CompanyTerminationContractRepository;
use App\Repository\HoldStaffManageRepository;
use App\Repository\HrStaffAnnexInfoRepository;
use App\Repository\HrStaffContractRepository;
use App\Repository\HrStaffInfoPositionRepository;
use App\Repository\HrStaffInfoRepository;
use App\Repository\HrStaffItemsRepository;
use App\Repository\StaffInfoRepository;
use App\Repository\StaffItemsRepository;
use App\Repository\StaffResignRepository;
use App\Repository\SysCityRepository;
use App\Repository\SysDistrictRepository;
use App\Repository\SysProvinceRepository;
use DateTime;
use Matrix\Exception;

class StaffInfoService extends BaseService
{

    public $staffItemsData = [];

    public function initStaffInfoData()
    {

    }

    public function initStaffItemData($staff_info_id)
    {
        $this->staffItemsData = StaffItemsRepository::getItemsByStaffInfoId($staff_info_id);
        return $this;
    }

    /**
     * 获取员工居住地址
     * @return string
     */
    public function getStaffResidenceAddressDetail(): string
    {
        $address = '';
        if (empty($this->staffItemsData)) {
            return $address;
        }

        $addressBox = [];

        //居住地址所在门牌号
        if(!empty($this->staffItemsData['RESIDENCE_HOUSE_NUM'])){
            $addressBox[] = $this->staffItemsData['RESIDENCE_HOUSE_NUM'];
        }

        //居住地址所在村号
        if(!empty($this->staffItemsData['RESIDENCE_VILLAGE_NUM'])){
            $addressBox[] = $this->staffItemsData['RESIDENCE_VILLAGE_NUM'];
        }
        //居住地址所在村
        if(!empty($this->staffItemsData['RESIDENCE_VILLAGE'])){
            $addressBox[] = $this->staffItemsData['RESIDENCE_VILLAGE'];
        }

        //居住地址所在巷
        if(!empty($this->staffItemsData['RESIDENCE_ALLEY'])){
            $addressBox[] = $this->staffItemsData['RESIDENCE_ALLEY'];
        }

        //居住地址所在街道
        if(!empty($this->staffItemsData['RESIDENCE_STREET'])){
            $addressBox[] = $this->staffItemsData['RESIDENCE_STREET'];
        }

        //居住地址所在省
        if (!empty($this->staffItemsData['RESIDENCE_PROVINCE'])) {
            $addressBox[] = SysProvinceRepository::findFirstByCode($this->staffItemsData['RESIDENCE_PROVINCE'])['name'] ?? $this->staffItemsData['RESIDENCE_PROVINCE'];
        }
        //居住地址所在市
        if (!empty($this->staffItemsData['RESIDENCE_CITY'])) {
            $addressBox[] = SysCityRepository::findFirstByCode($this->staffItemsData['RESIDENCE_CITY'])['name'] ?? $this->staffItemsData['RESIDENCE_CITY'];
        }
        //居住地址所在乡
        if (!empty($this->staffItemsData['RESIDENCE_DISTRICT'])) {
            $addressBox[] = SysDistrictRepository::findFirstByCode($this->staffItemsData['RESIDENCE_DISTRICT'])['name'] ?? $this->staffItemsData['RESIDENCE_DISTRICT'];
        }

        //居住地址所在邮编
        if(!empty($this->staffItemsData['RESIDENCE_POSTCODES'])){
            $addressBox[] = $this->staffItemsData['RESIDENCE_POSTCODES'];
        }

        return implode(' ', $addressBox);
    }

    /**
     * 获取员工户口地址
     * @param $data
     * @return string
     */
    public function getStaffRegisterAddress($data): string
    {
        $address = '';
        if (empty($data)) {
            return $address;
        }

        $addressBox = [];

        //户口地址所在门牌号
        if(!empty($data['REGISTER_HOUSE_NUM'])){
            $addressBox[] = $data['REGISTER_HOUSE_NUM'];
        }

        //户口地址所在村号
        if(!empty($data['REGISTER_VILLAGE_NUM'])){
            $addressBox[] = $data['REGISTER_VILLAGE_NUM'];
        }
        //户口地址所在村
        if(!empty($data['REGISTER_VILLAGE'])){
            $addressBox[] = $data['REGISTER_VILLAGE'];
        }

        //户口地址所在巷
        if(!empty($data['REGISTER_ALLEY'])){
            $addressBox[] = $data['REGISTER_ALLEY'];
        }

        //户口地址所在街道
        if(!empty($data['REGISTER_STREET'])){
            $addressBox[] = $data['REGISTER_STREET'];
        }

        //户口地址所在乡
        if (!empty($data['REGISTER_DISTRICT'])) {
            $addressBox[] = SysDistrictRepository::findFirstByCode($data['REGISTER_DISTRICT'])['name'] ?? $data['REGISTER_DISTRICT'];
        }

        //户口地址所在市
        if (!empty($data['REGISTER_CITY'])) {
            $addressBox[] = SysCityRepository::findFirstByCode($data['REGISTER_CITY'])['name'] ?? $data['REGISTER_CITY'];
        }

        //户口地址所在省
        if (!empty($data['REGISTER_PROVINCE'])) {
            $addressBox[] = SysProvinceRepository::findFirstByCode($data['REGISTER_PROVINCE'])['name'] ?? $data['REGISTER_PROVINCE'];
        }

        //户口地址所在邮编
        if(!empty($data['REGISTER_POSTCODES'])){
            $addressBox[] = $data['REGISTER_POSTCODES'];
        }

        return implode(' ', $addressBox);
    }



    /**
     * 员工数据权限
     * @param $staffId
     * @return array[]
     */
    public function StaffDataPurviewV2($staffId)
    {
        $key = CacheKeyEnums::TABLE_STAFF_DATA_PURVIEW_RANGE.'_v_'.$staffId;
        $info = $this->getCache($key);
        if($info){
            return json_decode($info,true);
        }

        $expireTime = 600;
        if(in_array(RUNTIME, ['dev','tra'])){
            $expireTime = 10;
        }

        $hr_rpc = (new ApiClient('hris', '', 'shift-purview-v2', 'zh-CN'));
        $hr_rpc->setParamss(['staff_info_id' => $staffId]);
        $result = $hr_rpc->execute();
        $this->logger->info('staffId:'.$staffId.' shift-purview-v2:'.json_encode($result,true));

        if ($result && $result['code'] == 0) {
            $this->setCache($key,json_encode($result['body']),$expireTime);
            return $result['body'];
        }
        $this->logger->error('staffId:'.$staffId.' shift-purview-v2:'.json_encode($result,true));

        return ['stores'=>[-100],'departments'=>[-100],'flash_home_departments'=>[-100],'staff_ids'=>[-100]];
    }

    /**
     * 获取hr用户信息
     *
     * @param $user_id
     * @return array
     */
    public function getStaffInfo_hr($user_id)
    {
        if (empty($user_id)) {
            return [];
        }
        $sql = " 
            --
            SELECT
                si.staff_info_id as id,
                si.staff_info_id,
                si.name,
                si.name_en,
                si.sys_store_id,
                si.mobile,
                si.manger,
                si.hire_type,
                si.hire_date,
                si.leave_date,
                si.identity,
                sip.position_category,
				if(sys_store_id='-1','head office',ss.name) AS store_name,
                si.state,
                si.email,
				si.job_title,
				sijt.job_name as job_title_name,
				si.sys_department_id as sys_department_id
				,si.node_department_id
				,si.hire_type
				,dep.name department_name
				,node.name node_name
				,si.personal_email
				,si.bank_no
				,si.health_status
				,ss.category
				,si.formal
				,si.sex
				,si.job_title_grade_v2
				,si.nationality
				,si.is_sub_staff
            FROM
                hr_staff_info AS si
                LEFT JOIN hr_staff_info_position AS sip ON si.id = sip.staff_info_id
                LEFT JOIN sys_store AS ss ON si.sys_store_id = ss.id 
				LEFT JOIN hr_job_title AS sijt ON sijt.id = si.job_title
				left join sys_department dep on si.sys_department_id = dep.id
				left join sys_department node on si.node_department_id = node.id
            WHERE
                si.staff_info_id = :staff_info_id
        ";
        $data = $this->getDI()->get('db_rby')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC,['staff_info_id'=>$user_id]);
        if ($data) {
            return $data;
        } else {
            return [];
        }
    }

	/**
	 * 获取员工大区，在职信息
	 */
	public function getStaffInfoByTask(array $options)
	{

		$store_sql = $staff_id_sql = $state_sql = $pcode_sql = $limit_sql = $piece_sql = $region_sql = $store_kind_sql = $andDepartmentWhere = "";
		if (isset($options['store_id']) && !empty($options['store_id'])) {
			$store_sql = " and a.sys_store_id in ('{$options['store_id']}') ";
		}
		if (isset($options['courier']) && !empty($options['courier'])) {
			$staff_id_sql = " and a.staff_info_id = {$options['courier']} ";
		}
		if (isset($options['state']) && !empty($options['state'])) {
			$state_sql = " and a.state = {$options['state']} ";
		}
		if (isset($options['pcode']) && !empty($options['pcode'])) {
			$pcode_sql = " and p.manage_geography_code = '{$options['pcode']}' ";
		}

		if (isset($options['piece']) && !empty($options['piece'])) {
			$piece_sql = " and t.manage_piece = {$options['piece']} ";
		}
		if (isset($options['region']) && !empty($options['region'])) {
			$region_sql = " and t.manage_region = {$options['region']} ";
		}

		if (!empty($options['limit'])) {
			$limit_sql = " limit {$options['offset']},{$options['limit']}";
		}
		if (!empty($options['kind'])) {
			$store_kind_sql = "and t.category in({$options['kind']})";
		}

		$str_where = '';//权限附加where

		//获取在职员工总数
		$sql = "--
            select
                count(1) as  count
            from
                hr_staff_info a
            -- left join
                -- staff_info_job_title b on a.job_title = b.id
            left join
                sys_store as t on a.sys_store_id = t.id
            left join
                sys_province as p on t.province_code = p.code
            where
                1=1
                {$store_sql}
                {$staff_id_sql}
                {$state_sql}
                {$piece_sql}
                {$region_sql}
                {$store_kind_sql}
                {$str_where}
                {$andDepartmentWhere}
                and a.sys_store_id != '-1'
                and a.formal = 1
                and a.is_sub_staff = 0
                {$pcode_sql}";
		$count = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

		//获取所有员工
		$sql = "--
            select
                a.staff_info_id as id,
                a.name,
                b.job_name,
                -- a.organization_id,
                a.sys_store_id as organization_id,
                a.hire_date,
                a.leave_date,
                a.state,
                a.stop_duties_date,
                p.name as province_name,
                p.code as province_code,
                p.manage_geography_code,
                t.manage_region as manage_region,
                t.manage_piece as manage_piece,
                t.name as store_name,
                a.week_working_day
            from
                hr_staff_info a
            left join
                hr_job_title b on a.job_title = b.id
            left join
                sys_store as t on a.sys_store_id = t.id
            left join
                sys_province as p on t.province_code = p.code
            where
                1=1
                {$store_sql}
                {$staff_id_sql}
                {$state_sql}
                {$piece_sql}
                {$region_sql}
                {$store_kind_sql}
                {$str_where}
                {$andDepartmentWhere}
                and a.sys_store_id != '-1'
                and a.formal = 1
                and a.is_sub_staff = 0
                {$pcode_sql}
                {$limit_sql}";
		$this->getDI()->get('logger')->write_log('getStaffInfoByTask ' .  ' ' . $sql, 'info');
		$dot_list = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
		return ['count' => isset($count[0]) ? $count[0]['count'] : 0, 'dot_list' => $dot_list];
	}

	/**
	 * 获取员工出差信息
	 */
	public function getStaffInfoByBusiness(array $staff_ids, $date_range)
	{
		if (empty($staff_ids)) {
			return [];
		}
		$staff = implode(',', array_keys($staff_ids));
		$start = current($date_range);
		$end = end($date_range);
		$where_params['apply_user'] = explode(',',$staff);
		$where_params['end_time'] = $start;
		$where_params['start_time'] = $end;
		$business_trip = \App\Models\backyard\BusinessTripModel::class;
		$sql = "
            select
                start_time,end_time,apply_user
            from
                {$business_trip}
            where
                apply_user in ({apply_user:array})
            and
                status = 2
            and
                 !(end_time  < :end_time: or start_time  > :start_time:)
        ";

//		$trip_info = $this->getDI()->get('db_back')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

		$trip_info = $this->modelsManager->executeQuery($sql,$where_params)->toArray();


		if (empty($trip_info)) {
			return [];
		}

		$return = [];
		foreach ($trip_info as $key => $value) {
			$date = $value['start_time'];
			while (true) {
				$return[$value['apply_user']][$date] = 1;
				$date = date('Y-m-d', strtotime($date) + 86400);
				if ($date > $value['end_time']) {
					break;
				}
			}
		}
		return $return;
	}


	/**
	 * 获取员工请假信息
	 */
	public function getStaffAuditInfo(array $options)
	{
		$logger = self::getDI()->get('logger');
		try {
			if (!isset($options['staff_ids']) || empty($options['staff_ids'])) {
				return [];
			}

			$staff_ids = array_chunk($options['staff_ids'], 1000);
			$apply_list = [];
			foreach ($staff_ids as $key => $value) {
				//获取请假信息
				$sql = "--
                select
                    s.staff_info_id,left(s.leave_start_time,10) as start_time,left(s.leave_end_time,10) as end_time,s.audit_reason,group_concat(i.image_path) as image_path
                from
                    staff_audit as s left join staff_audit_images as i on s.audit_id=i.audit_id
                where
                    s.leave_start_time >= '{$options['begin']}'
                    and s.audit_type = 2
                    and s.staff_info_id in (" . implode(",", $value) . ")
                    group by s.audit_id";
				$result = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
				$apply_list = array_merge($apply_list, $result);
			}
			return $apply_list;
		} catch (\Exception $exception) {
			//记录下日志
			$logger->write_log("调用的方法: " . __METHOD__ . " 错误信息: " . $exception->getMessage());
			return [];
		}

	}

    /**
     * 获取员工补卡信息
     * @param $param
     * @return mixed
     */
    public function getStaffAddAttendanceList($params, $export = false){

        $timeZone = env('timeZone','+07:00');
        $attendance_object = $this->modelsManager->createBuilder();
        $attendance_object->columns("count(1) as total");
        $attendance_object->from(['s' => StaffAuditModel::class]);
        $attendance_object->leftJoin(HrStaffInfoReadModel::class, 's.staff_info_id = i.staff_info_id', 'i');

        //通用数据权限限定条件
        $condition = (new AttendanceStatisticsService())->buildStaffPurviewData($params['current_uid'],'i');
        if($condition['condition'] && $condition['bind']){
            $attendance_object->andWhere($condition['condition'] , $condition['bind'] );
        }

        //查找员工
        $staff_id = $params['staff_info_id'];
        if (!empty($staff_id)) {
            $s_id = intval($staff_id);
            if ($s_id > 0) {
                $attendance_object->andWhere("i.staff_info_id = :staff_info_id:", ['staff_info_id' => $s_id]);
            } else {
                $attendance_object->andWhere("i.name like :name:", ['name' => "%{$staff_id}%"]);
            }
        }

        //过滤网点
        if (!empty($params['store_id'])) {
            $attendance_object->inWhere("i.sys_store_id", $params['store_id']);
        }

        //过滤部门
//        if (!empty($params['department'])) {
//            $attendance_object->andWhere("i.node_department_id = :department:", ['department' => $params['department']]);
//        }
        if (!empty($params['department_ids'])) {
            $attendance_object->inWhere("i.node_department_id", $params['department_ids']);
        }

        //过滤职位
        if (!empty($params['job_title'])) {
            $attendance_object->inWhere("i.job_title", $params['job_title']);
        }
        //在职状态
        if (!empty($params['staff_state'])) {
            $attendance_object->inWhere('i.state', $params['staff_state']);
        }
        //雇佣类型
        if (!empty($params['hire_type'])) {
            $attendance_object->inWhere('i.hire_type', $params['hire_type']);
        }
        //审批状态
        if(!empty($params['audit_status'])){
            $attendance_object->inWhere("s.status", $params['audit_status']);
        }
        //申请时间
        if(!empty($params['submit_start_time']) && !empty($params['submit_end_time'])) {
            $_submit_start_time = gmdate('Y-m-d H:i:s', $params['submit_start_time'] - $this->config->application->add_hour*3600);
            $_submit_end_time = gmdate('Y-m-d H:i:s', $params['submit_end_time'] + 86399 - $this->config->application->add_hour*3600);
            $attendance_object->betweenWhere("s.created_at", $_submit_start_time, $_submit_end_time);
        }

        //实际发生时间
        if(!empty($params['apply_start_time']) && !empty($params['apply_end_time'])) {
            $attendance_object->betweenWhere("s.reissue_card_date", gmdate('Y-m-d H:i:s',$params['apply_start_time']), gmdate('Y-m-d H:i:s',$params['apply_end_time']+86399));
        }
        //自增ID
        if (!empty($params['max_id'])) {
            $attendance_object->andWhere("s.audit_id < :max_id:",  ['max_id' => $params['max_id']]);
        }

        //申请类型-补卡
        $attendance_object->andWhere('audit_type=1');
        //查总数
        $totalInfo = $attendance_object->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);

        if(!$export ){
            //分页
            $offset = empty($params['page']) ? 0 : ($params['page'] - 1) * $params['size'];
            $limit = empty($params['size']) ? 100 : intval($params['size']);

            $attendance_object->offset($offset);
            $attendance_object->limit($limit);
        }

        $attendance_object->columns(
            "i.name as staff_name,
            i.staff_info_id,
            i.state,
            i.job_title,
            i.node_department_id,
            i.hire_type,
            DATE_FORMAT( CONVERT_TZ( s.created_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at,
            DATE_FORMAT( CONVERT_TZ( s.updated_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as updated_at,
            s.reissue_card_date,
            s.audit_id,
            s.attendance_date,
            s.attendance_type,
            s.audit_reason,
            s.audit_id as id,
            i.sys_store_id,
            s.status as audit_state,
            s.reject_reason,
            s.approver_id,
            s.approver_name"
        );

        $attendance_result = $attendance_object->getQuery()->execute();
        $list = $attendance_result->toArray();

        if(!empty($list)){
            //获取网点信息 拼接 网点名称
            $sysService = new SysService();
            $store_list = $sysService->getStoreListFromCache();
            $store_list = array_column($store_list , 'name' , 'id');
            $store_list['-1'] = GlobalEnums::HEAD_OFFICE;
            //职位
            $job_title_service = new HrJobTitleService();
            $job_title = $job_title_service->getJobTitleListFromCache(false);
            $job_title_list = array_column($job_title , 'job_name' , 'id');

            //部门
            $department_list = $sysService->getDepartmentListFromCache();
            $department_list = array_column($department_list , 'name' , 'id');
            foreach ($list as &$item) {
                $item['department_name'] = $department_list[$item['node_department_id']]??'';
                $item['job_name'] = $job_title_list[$item['job_title']]??'';
                $item['store_name'] = $store_list[$item['sys_store_id']]??'';
                $item['hire_type_text'] = self::$t->_('hire_type_' . $item['hire_type']);
            }
        }
        //查询补卡记录
        $data['DataList'] = $list;
        $data['page'] = isset($params['page']) ? intval($params['page']) : 1;
        $data['size'] = isset($params['size']) ? intval($params['size']) : 100;
        $data['count'] = $count;
        return $data;
    }

    /**
     * 获取员工补卡信息-详情
     * @param $param
     * @return mixed
     */
    public function getStaffAddAttendanceDetail($params){

        $timeZone = env('timeZone','+07:00');
        $attendance_object = $this->modelsManager->createBuilder();
        $attendance_object->columns(
            "i.name as staff_name,
            i.staff_info_id,
            s.reissue_card_date,
            s.audit_id,
            s.attendance_date,
            s.attendance_type,
            s.audit_reason,
            s.source_type,
            dep.id as department_name,
            job.job_name,
            i.sys_store_id,
            s.created_at
            "
        );
        $attendance_object->from(['s' => StaffAuditModel::class]);
        $attendance_object->leftJoin(HrStaffInfoReadModel::class, 's.staff_info_id = i.staff_info_id', 'i');
        $attendance_object->leftJoin(SysDepartmentModel::class, 'i.node_department_id = dep.id', 'dep');
        $attendance_object->leftJoin(HrJobTitleModel::class, 'i.job_title = job.id', 'job');
        $attendance_object->leftJoin(SysStoreModel::class, 'i.sys_store_id = store.id', 'store');

        //条件
        $attendance_object->andWhere('s.audit_id=:audit_id:',['audit_id'=>$params['audit_id']]);

        $sql = $attendance_object->getQuery()->getsql();//var_dump($sql);exit;
        $attendance_result = $attendance_object->getQuery()->execute();
        $data = [];
        //查询补卡记录
        $this->logger->info('getStaffAddAttendanceDetail:补卡列表-详情-sql' .json_encode($sql));
        //附件 审批流程
        if($attendance_result){
            $attendance_result = $attendance_result->toArray();
            $data = $attendance_result[0];
            $attachment = StaffAuditImageModel::find([
                'conditions' => "audit_id = :audit_id:",
                'bind' => [
                    'audit_id'  => $params['audit_id'],
                ],
            ])->toArray();
            if($attachment){
                $data['image_path'] = array_column($attachment,'image_path');
            }
            //审批流程
            $audit = $this->modelsManager->createBuilder();
            $audit->columns(
            "a.submitter_id,
            i.name as submitter_name,
            l.staff_name,
            l.staff_id,
            l.staff_department,
            l.staff_job_title_name,
            a.state,
            DATE_FORMAT( CONVERT_TZ( s.created_at, '+00:00', '{$timeZone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at
            ");
            $audit->from(['s' => StaffAuditModel::class]);
            $audit->leftJoin(AuditApplyModel::class, 's.audit_id = a.biz_value', 'a');
            $audit->leftJoin(AuditLogModel::class, 'a.flow_id = l.flow_id', 'l');
            $audit->andWhere('a.biz_type=:biz_type:',['biz_type'=>$params['biz_type']]);
            $audit->andWhere('s.audit_id=:audit_id:',['audit_id'=>$params['audit_id']]);
            $audit->andWhere('s.staff_info_id=:staff_info_id:',['staff_info_id'=>$params['staff_info_id']]);
            $audit->leftJoin(HrStaffInfoReadModel::class, 's.staff_info_id = i.staff_info_id', 'i');

            $audit->orderBy('l.id');
            $audit_list = $audit->getQuery()->execute();
            $audit_list_detail = $audit_list->toArray();
            if($audit_list_detail){
                $audit_status = (new SysService())->AuditStatus();
                $audit_status = array_column($audit_status, 'label', 'value');
                foreach($audit_list_detail as $k=>$v){
                    $audit_list_detail[$k]['state'] = $audit_status[$audit_list_detail[$k]['state']];
                }
                $data['audit_list_detail'] = $audit_list_detail;
            }else{
                $data['audit_list_detail'] = [];
            }
        }
        return $data;
    }

    /**
     * 获取员工信息
     *
     * @param $staff_info_id
     * @param string $filed
     */
    public function getStaffInfoByIdv4($staff_info_id, $filed = '*')
    {

        $staff = HrStaffInfoModel::find(
            [
                "conditions" => "staff_info_id =:staff_info_id:",
                "bind" => [
                    "staff_info_id" => $staff_info_id,
                ],
                "columns" => $filed,
            ]
        )->toArray();
        return $staff ? $staff[0] : [];
    }

    /**
     * 获取员工是否为底层员工
     * 底层员工定义
     *  （37）DC Officer、（807）Hub Operator、（812）Onsite Officer、（300）Warehouse staff、（13）Bike Courier、
     *  （110）Van Courier、（272）HUB Supervisor、 （16）Branch Supervisor、(745) Hub Forklift Driver
     *
     * 非底层员工定义
     *   非上面的职位
     *
     * @param string $staff_info_id
     * @return bool
     */
    public function getStaffInfoType(string $staff_info_id): bool
    {
        if (empty($staff_info_id)) {
            return false;
        }

        //获取员工的职位
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind' => [
                'staff_info_id' => $staff_info_id,
            ],
            'columns' => 'job_title',
        ]);
        if (empty($staffInfo)) { //fbi不存在员工工号
            return false;
        }

        //获取底层员工职位
        $settingEnv = new SettingEnvService();
        $underlyingStaffJobTitle = $settingEnv->getSetVal('underlying_staff_job_title');
        if (empty($underlyingStaffJobTitle)) {
            return false;
        }
        if (isset($staffInfo->job_title) && in_array($staffInfo->job_title, explode(',', $underlyingStaffJobTitle))) { //职位为基层员工
            return true;
        }
        return false;
    }

    /**
     * 获取用户信息
     *
     * @param $user_id
     * @return array
     */
    public function getStaffInfo($user_id)
    {
        if (empty($user_id)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("si.id,
                si.name,
                si.organization_id,
                si.organization_type,
                si.mobile,
                si.formal,
                si.encrypted_password,
                si.job_title,
                group_concat(sip.position_category) position_category,
                ss.category sys_category,
                ss.name AS store_name,
                si.state,
                si.email,
                si.virtual_number_enabled,
				si.organization_type as type,
				si.department_id,
                sd.name as department_name");
        $builder->from(['si' => StaffInfoModel::class]);
        $builder->leftJoin(StaffInfoPositionModel::class, 'si.id = sip.staff_info_id', 'sip');
        $builder->leftJoin(FleSysStoreModel::class, 'si.organization_id = ss.id', 'ss');
        $builder->leftJoin(FleSysDepartmentModel::class, 'si.department_id = sd.id', 'sd');
        $builder->Where('si.id=:id: AND si.state=:state:', ['id'=>$user_id, 'state'=>Enums::HRIS_WORKING_STATE_1]);
        $data = $builder->getQuery()->getSingleResult();

        return $data ?? [];
    }

    public function getStaffInfoListByStaffIds($staffIds)
    {
        if (empty($staffIds)) {
            return $staffIds;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.sys_department_id',
            'hsi.node_department_id',
            'hsi.hire_type',
            'hsi.mobile',
        ]);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->Where('staff_info_id IN ({staff_info_ids:array})', ['staff_info_ids' => $staffIds]);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 获取员工列表
     * @param $staffIds
     * @return mixed
     */
    function getStaffList($staffIds){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(["hsi.staff_info_id,
                    hsi.name,
                    hsi.state,
                    hsi.name_en,
                    hsi.sys_department_id,
                    hsi.node_department_id,
                    hsi.hire_date,
                    hsi.leave_date,
                    hjt.job_name as job_name,
                    sd.name as department_name,
                    ss.name as store_name,
                    hsi.sys_store_id"]);
        $builder->from(['hsi' => \App\Models\backyard\HrStaffInfoModel::class]);
        $builder->leftJoin(HrJobTitleModel::class, 'hsi.job_title = hjt.id', 'hjt');
        $builder->leftJoin(SysDepartmentModel::class, 'hsi.node_department_id = sd.id', 'sd');
        $builder->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss');
        $builder->Where('staff_info_id IN ({staff_info_ids:array})', ['staff_info_ids'=>$staffIds]);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 获取指定部门的hrbp  注意这里和审批流找 hrbp 不一样
     * @param $department_id
     * @param $extend
     */
    public function findHRBP($department_id, $extend)
    {
        $storeId           = $extend['store_id'] ?? '';
        $storeInfo         = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $storeId],
        ]);
        $region            = isset($storeInfo->manage_region) && !empty($storeInfo->manage_region) ? $storeInfo->manage_region : 0;
        $piece             = isset($storeInfo->manage_piece) && !empty($storeInfo->manage_piece) ? $storeInfo->manage_piece : 0;
        $category          = isset($storeInfo->category) && !empty($storeInfo->category) ? $storeInfo->category : 0;
        $all_id            = '-2';// 代表勾选了全部
        $position_category = 68;  //hrbp 角色 id
        $hrbp              = [];
        do {
            //网点不能管辖总部网点
            if (!empty($storeId) && $storeId != '-1') {
                //[1]先获取网点对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageStoreModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = 1 and staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: ',
                    ['position_category' => $position_category, 'type' => HrStaffManageStoreModel::$type_1]
                );
                $builder->inWhere('region.store_id', [$all_id, $storeId]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }
            if (!empty($piece)) {
                //[2]获取片区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManagePieceModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = 1 and staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: ',
                    ['position_category' => $position_category, 'type' => HrStaffManagePieceModel::$type_1]
                );
                $builder->inWhere('region.piece_id', [$all_id, $piece]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }
            if (!empty($region)) {
                //[3]获取大区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageRegionModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = 1 and staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: ',
                    ['position_category' => $position_category, 'type' => HrStaffManageRegionModel::$type_1]
                );
                $builder->inWhere('region.region_id', [$all_id, $region]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }

            if (!empty($category)) {
                //[4]获取网点类型对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageStoreCategoryModel::class,
                    'position.staff_info_id = region.staff_info_id', 'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = 1 and staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: and region.store_category = :store_category: ',
                    [
                        'position_category' => $position_category,
                        'type'              => HrStaffManageStoreCategoryModel::$type_1,
                        'store_category'    => $category,
                    ]
                );
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }

            if (!empty($department_id)) {
                //[5]获取部门对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageDepartmentModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = 1 and staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: and region.department_id = :department_id:',
                    [
                        'position_category' => $position_category,
                        'department_id'     => $department_id,
                        'type'              => HrStaffManageDepartmentModel::$type_1,
                    ]
                );
                $hrbp = $builder->getQuery()->execute()->toArray();

                //获取部门的部门链
                $department  = SysDepartmentModel::findFirst($department_id);
                $ancestry_v3 = isset($department->ancestry_v3) ? explode('/', $department->ancestry_v3) : [];
                //获取是否为包含子部门的管辖范围 hrbp
                if (empty($ancestry_v3)) {
                    break;
                }
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageDepartmentModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的  并且包含子部门的
                $builder->Where(
                    'staff.state = 1 and staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.is_include_sub = :is_include_sub: and region.type = :type: ',
                    [
                        'position_category' => $position_category,
                        'type'              => HrStaffManageDepartmentModel::$type_1,
                        'is_include_sub'    => HrStaffManageDepartmentModel::$is_include_sub_1,
                    ]
                );
                $builder->inWhere('region.department_id', $ancestry_v3);
                $hrbp_include_sub = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp_include_sub)) {
                    $hrbp = array_merge($hrbp, $hrbp_include_sub);
                }
            }
        } while (0);

        $staffInfoIds = array_unique(array_filter(array_column($hrbp, 'staff_info_id')));
        $staffInfoIds = $staffInfoIds ? implode(',', $staffInfoIds) : "";
        return $staffInfoIds;
    }

    /**
     * 查找hrbp 按照审批流查找
     * @param $params
     * @return array|mixed
     */
    public function findHRBPToBackyard($params)
    {
        $department_id = $params['department_id'] ?? '';
        $store_id      = $params['store_id'] ?? '';
        if (empty($department_id) || empty($store_id)) {
            return [];
        }
        //获取管辖范围
        $rpc        = new ApiClient('by', '', 'svc_find_hrbp', static::$language);
        $api_params = [
            [
                'department_id' => $department_id,   //部门 id
                'store_id'      => $store_id,        //网点 id
            ],
        ];
        $rpc->setParams($api_params);
        return $rpc->execute();
    }

    /**
     * 查询员工信息
     * @param $params
     * @param bool $is_all
     * @return array
     */
    public function getStaffInfoList($params, $is_all = true)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.hire_date',
        ]);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        //员工id or name
        if (!empty($params['staff_keyword'])) {
            $builder->andWhere('(hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword:)', ['staff_keyword' => '%'.$params['staff_keyword'].'%']);
        }
        //小于100000的工号
        if (!$is_all) {
            $builder->andWhere('hsi.staff_info_id < :limit_size: ', ['limit_size' => 100000]);
        } else {
            $builder->andWhere('state != :state:', ['state' => Enums::HRIS_WORKING_STATE_2]);
        }
        if(!empty($params['staff_info_id'])) {
            $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
        }
        $builder->andWhere('formal = :formal: and is_sub_staff = 0', ['formal' => HrStaffInfoModel::FORMAL_1]);

        $builder->limit(20);

        $staffInfoList = $builder->getQuery()->execute()->toArray();

        $staffList = [];
        foreach ($staffInfoList as $oneStaff) {
            $oneStaffInfo['staff_info_id'] = $oneStaff['staff_info_id'];
            $oneStaffInfo['hire_date']     = $oneStaff['hire_date'] ? date('Y-m-d',
                strtotime($oneStaff['hire_date'])) : null;
            $oneStaffInfo['name']          = $oneStaff['staff_info_id'].' '.$oneStaff['name'] ?? '';
            $staffList[]                   = $oneStaffInfo;
        }

        return $staffList;
    }

    /**
     * 查询员工信息
     * @param $staffIds
     * * @param array $state
     * @return mixed
     */
    function getLeaveStaffInfo($staffIds, array $state = [])
    {
        if (!is_array($staffIds) || empty($staffIds)) {
            return [];
        }

        if (empty($state)) {
            $state = [2];
        }

        $timeZone = $this->timeZone;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            "hsi.staff_info_id,
            hsi.name,
            hsi.leave_date,
            hsi.leave_type,
            hsi.leave_reason,
            CONVERT_TZ(hsi.updated_at,'+00:00','{$timeZone}') AS updated_at,
            hsi.state,
            hsi.name_en,
            hsi.sys_department_id,
            hsi.node_department_id,
            hsi.sys_store_id,
            hsi.job_title,
            hjt.job_name as job_name,
            sd.name as department_name",
        ]);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrJobTitleModel::class, 'hsi.job_title = hjt.id', 'hjt');
        $builder->leftJoin(SysDepartmentModel::class, 'hsi.node_department_id = sd.id', 'sd');
        $builder->where('hsi.staff_info_id IN ({staff_info_ids:array})', ['staff_info_ids' => $staffIds]);
        //$builder->andWhere('hsi.state = :state:', ['state' => (int) $state]);
        $builder->andWhere('hsi.state IN ({state:array})', ['state' => $state]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取员工基本信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfoOne($staff_info_id)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns'    => '
            staff_info_id,
            name,
            name_en,
            nick_name,
            state,
            wait_leave_state,
            job_title,
            identity,
            sys_store_id,
            node_department_id,
            manger,
            working_country,
            personal_email,
            hire_date,
            hire_type,
            job_title_grade_v2,
            leave_date,
            formal,
            identity',
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind'       => [
                'staff_id' => $staff_info_id,
            ],
        ]);

        if (!$staffInfo) {
            return [];
        }

        $staffInfo = $staffInfo->toArray();

        $departmentInfo = SysDepartmentModel::findFirst([
            'columns'    => 'name',
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $staffInfo['node_department_id']],
        ]);

        $jobInfo = HrJobTitleModel::findFirst([
            'columns'    => 'job_name',
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $staffInfo['job_title']],
        ]);

        if ($staffInfo['sys_store_id'] == -1) {
            $storeName = GlobalEnums::HEAD_OFFICE;
        } else {
            $storeInfo = SysStoreModel::findFirst([
                'columns'    => 'name',
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $staffInfo['sys_store_id']],
            ]);

            $storeName = $storeInfo->name ?? '';
        }

        return [
            'staff_info_id'      => $staffInfo['staff_info_id'],
            'identity'           => $staffInfo['identity'],
            'name'               => $staffInfo['name'],
            'name_en'            => $staffInfo['name_en'],
            'nick_name'          => $staffInfo['nick_name'],
            'formal'             => $staffInfo['formal'],
            'state'              => $staffInfo['state'],
            'wait_leave_state'   => $staffInfo['wait_leave_state'],
            'department_name'    => $departmentInfo->name ?? '',
            'node_department_id' => $staffInfo['node_department_id'],
            'working_country'    => $staffInfo['working_country'],
            'manger'             => $staffInfo['manger'],
            'job_title_name'     => $jobInfo->job_name ?? '',
            'store_name'         => $storeName,
            'hire_date'          => formatHrDate($staffInfo['hire_date']),
            'sys_store_id'       => $staffInfo['sys_store_id'],
            'personal_email'     => $staffInfo['personal_email'] ?? '',
            'job_title_grade_v2' => $staffInfo['job_title_grade_v2'] ?? '',
            'hire_type'          => $staffInfo['hire_type'] ?? '',
            'job_title'          => $staffInfo['job_title'] ?? 0,
            'identity'           => $staffInfo['identity'] ?? '',
            'leave_date'         => formatHrDate($staffInfo['leave_date']),
        ];
    }


    /**
     * 查询员工信息-在职、待离职
     * @param $staffId
     * @return mixed
     */
    public function getStaffInfoJobAndLeave($staffIds = [], $state = [])
    {
        if (!is_array($staffIds) || empty($staffIds)) {
            return [];
        }

        if (!is_array($state) || empty($state)) {
            $state = [1];   // 在职
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['id', 'staff_info_id', 'name', 'state', 'sys_store_id', 'node_department_id', 'job_title']);
        $builder->from(HrStaffInfoReadModel::class);
        $builder->where('state IN ({state:array})', ['state' => $state]);
        $builder->andWhere('staff_info_id IN ({staff_info_ids:array})', ["staff_info_ids" => $staffIds]);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 员工数据查询权限
     * @param $staffId
     * @return array[]
     */
    public function staffDataListInfo($staffId)
    {
        $key  = CacheKeyEnums::TABLE_STAFF_STAFF_WORK_ATTENDANCE_ATTACHMENT_RANGE . '_v2_' . $staffId;
        $info = $this->getCache($key);
        if ($info) {
            return json_decode($info, true);
        }

        $hr_rpc = (new ApiClient('hris', '', 'face-data-select-permission', 'zh-CN'));
        $hr_rpc->setParamss(['staff_info_id' => $staffId]);
        $result = $hr_rpc->execute();
        $this->logger->info('staffId:' . $staffId . ' face_data_select_permission:' . json_encode($result, true));

        if ($result && $result['code'] == 0) {
            $this->setCache($key, json_encode($result['body']), 100);
            return $result['body'];
        }
        $this->logger->error('staffId:' . $staffId . ' face_data_select_permission:' . json_encode($result, true));

        return ['stores' => [-100], 'departments' => [-100], 'flash_home_departments' => [-100], 'staff_ids' => [-100]];
    }


    public function getStaffInfoForComplaint($staff_info_id): array
    {
        $returnData = [];
        if (empty($staff_info_id)) {
            return $returnData;
        }
        $staffInfo = HrStaffInfoModel::findFirstByStaffInfoId($staff_info_id);
        if (empty($staffInfo)) {
            return $returnData;
        }
        $department = SysDepartmentModel::findFirst($staffInfo->node_department_id);
        $returnData['staff_info_id']              = $staffInfo->staff_info_id;
        $returnData['staff_name']                 = $staffInfo->name;
        $returnData['personal_email']             = $staffInfo->personal_email;
        $returnData['staff_mobile']               = $staffInfo->mobile;
        $returnData['staff_node_department_id']   = $staffInfo->node_department_id;
        $returnData['staff_node_department_name'] = $department?$department->name:'';
        $returnData['store_id']                = '';
        $returnData['store_manager_staff_id']  = '';
        $returnData['store_manager_name']      = '';
        $returnData['region_manager_name']     = '';
        $returnData['region_manager_staff_id'] = '';

        $server = reBuildCountryInstance(new StaffInfoService());
        $returnData['staff_address']  = $server->initStaffItemData($staff_info_id)->getStaffResidenceAddressDetail();
        $contract                          = HrStaffContractModel::FindFirst([
            'conditions' => 'staff_id = :staff_id: and contract_status in({contract_status:array}) and contract_is_deleted = 0 and contract_type = 1',
            'bind'       => [
                'staff_id'        => $staff_info_id,
                'contract_status' => [StaffEnums::CONTRACT_STATUS_ARCHIVED,StaffEnums::CONTRACT_STATUS_TAKE],
            ],
            'order'      => 'id desc',
        ]);
        $returnData['staff_contract_info'] = [
            'name' => $contract ? self::$t->_('contract_type_1') : '',
            'path' => $contract ? env('img_prefix') . $contract->contract_path : '',
        ];
        if ($staffInfo->sys_store_id != '-1') {
            $returnData['store_id'] = $staffInfo->sys_store_id;
            $storeInfo              = SysStoreModel::findFirstById($staffInfo->sys_store_id);
            if ($storeInfo) {
                $returnData['store_name']              = $storeInfo->name;
                $returnData['store_manager_staff_id']  = $storeInfo->manager_id;
                $returnData['store_manager_name']      = $storeInfo->manager_name;
                $regionInfo                            = SysManageRegionModel::findFirstById($storeInfo->manage_region);
                $returnData['region_manager_staff_id'] = $regionInfo ? $regionInfo->manager_id : '';
                $returnData['region_manager_name']     = $regionInfo ? $regionInfo->manager_name : '';
            }
        }
        return $returnData;
    }
    //员工入职日期
    public function getStaffHireDate(array $staff_info_ids): array
    {
        if(empty($staff_info_ids)){
            return [];
        }
        $hr_staff_info_model = HrStaffInfoModel::class;
        $sql                 = "select staff_info_id,DATE(hire_date) as hire_date from {$hr_staff_info_model}  where staff_info_id in ({staff_info_id:array})";
        $bind                = ['staff_info_id' => $staff_info_ids];
        $user_data           = $this->modelsManager->executeQuery($sql, $bind)->toArray();
        return array_column($user_data, 'hire_date', 'staff_info_id');
    }


    /**
     * 查询员工信息
     * @param array $staffIds
     * @param array $where
     * @return array
     */
    function getStaffListById(array $staffIds, array $where=[]): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $columns = [
            'staff_info_id',
            'name as staff_info_name',
            'state',
            'created_at',
            'hire_date',
            'leave_date',
            'stop_duties_date',
            'sys_department_id',
            'node_department_id',
            'sys_store_id',
            'job_title',
            'wait_leave_state',
        ];
        $list = $this->getStaffInfoById($staffIds, $columns, $where);

        $jobTitleMap      = array_column((new HrJobTitleService())->getJobTitleListFromCache(false), 'job_name', 'id');
        $storeMap         = array_column((new SysStoreService())->getStoreAllListFromCache(false), null, 'id');
        $sysDepartmentMap = array_column((new SysDepartmentService())->getDepartmentAllListFromCache(true), 'name',
            'id');

        foreach ($list as &$item) {
            $item['hire_date']            = substr($item['hire_date'], 0, 10);
            $item['leave_date']           = $item['leave_date'] ? substr($item['leave_date'], 0, 10) : null;
            $item['stop_duties_date']     = $item['stop_duties_date'] ? substr($item['stop_duties_date'], 0, 10) : null;
            $item['created_at']           = date('Y-m-d',
                strtotime($item['created_at'] . ' + ' . $this->timeOffset . ' hours'));
            $item['job_name']             = $jobTitleMap[$item['job_title']] ?? '';
            $item['store_name']           = $storeMap[$item['sys_store_id']]['name'] ?? '';
            $item['store_category']       = $storeMap[$item['sys_store_id']]['store_category'] ?? 0;
            $item['sys_department_name']  = $sysDepartmentMap[$item['sys_department_id']] ?? '';
            $item['node_department_name'] = $sysDepartmentMap[$item['node_department_id']] ?? '';
        }
        return $list;
    }

    /**
     * 获取员工信息
     * @param $staff_info_ids
     * @param array $columns
     * @param array $where
     * @return array
     */
    public function getStaffInfoById($staff_info_ids, array $columns = [], array $where = []): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(HrStaffInfoModel::class);
        $builder->inWhere('staff_info_id', $staff_info_ids);
        if (!empty($where)) {
            $builder->andWhere($where['condition'], $where['bind']);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取角色名称
     * @param $rolesInfo
     * @return array
     */
    public function getStaffRolesName($rolesInfo)
    {
        $staffRolesInfo = [];
        foreach ($rolesInfo as $staffId => $roleIds) {
            $position_category = [];
            $roleIdsArr        = explode(',', $roleIds);
            foreach ($roleIdsArr as $oneRoleId) {
                if (!is_numeric($oneRoleId)) {
                    continue;
                }
                $position_category[] = self::$t->_('role_' . $oneRoleId);
            }
            $staffRolesInfo[$staffId] = empty($position_category) ? '' : implode(',', $position_category);
        }

        return $staffRolesInfo;
    }

    /**
     * 获取税务信息
     * @param $staffIdsString
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function getStaffTaxInfo($staffIdsString)
    {
        $item_arr = [
            'REGISTER_COUNTRY',    //户口所在国家
            'REGISTER_PROVINCE',   //户口所在省
            'REGISTER_CITY',       //户口所在市
            'REGISTER_DISTRICT',   //户口所在乡
            'REGISTER_POSTCODES',  //户口所在邮编
            'REGISTER_HOUSE_NUM',  //户口所在门牌号
            'REGISTER_VILLAGE_NUM',//户口所在村号
            'REGISTER_VILLAGE',    //户口所在村,
            'REGISTER_ALLEY',      //户口所在巷
            'REGISTER_STREET',     //户口所在街道
            'BANK_NO_NAME',        //持卡人姓名
        ];
        if (empty($staffIdsString)) {
            return [];
        }
        $staffIdsArr = explode(',', $staffIdsString);
        if(count($staffIdsArr) > 500) {
            throw new ValidationException("staff number limit 500!");
        }

        $staffIdsItems = HrStaffItemsRepository::getStaffItemByIds($staffIdsArr, $item_arr);

        $staffInfos = HrStaffInfoRepository::getHrStaffByIds($staffIdsArr,
            ['staff_info_id', 'mobile', 'personal_email', 'bank_no', 'bank_type', 'leave_source', 'state']);

        $staffInfosToId = array_column($staffInfos, null, 'staff_info_id');

        $bankIds = array_values(array_unique(array_column($staffInfos, 'bank_type')));

        $bankList     = BankListRepository::getBankList($bankIds, ['bank_id', 'bank_name']);
        $bankListToId = array_column($bankList, 'bank_name', 'bank_id');

        $holdInfo = (new HoldManageService())->getStaffHoldInfoIncomplete($staffIdsArr, 'staff_info_id');
        $holdInfoToStaffId = empty($holdInfo) ? [] : array_column($holdInfo, 'staff_info_id');

        //生效中的劳动合同
        $where['staff_info_id'] = $staffIdsArr;
        $where['contract_status'] = [HrStaffContractModel::CONTRACT_STATUS_ARCHIVED, HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED, HrStaffContractModel::CONTRACT_STATUS_RDNEWED];
        $where['contract_type'] = HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT;
        $where['contract_date'] = date('Y-m-d');
        $contractList = HrStaffContractRepository::getListGroupStaffId($where, ['max(id) as id']);

        $contractListInfo = [];
        if(!empty($contractList)) {
            $contractWhere['ids'] = array_column($contractList, 'id');
            $contractListInfo = HrStaffContractRepository::getList($contractWhere, ['id', 'staff_id', 'contract_type', 'contract_child_type', 'version']);
        }

        $contractListInfoToStaffId = array_column($contractListInfo, NULL, 'staff_id');

        //旧合同到期，新合同还 未生效， 取已过期的合同
        $expiredWhere['staff_info_id'] = $staffIdsArr;
        $expiredWhere['contract_status'] = [HrStaffContractModel::CONTRACT_STATUS_EXPIRED];
        $expiredWhere['contract_type'] = HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT;
        $contractExpiredList = HrStaffContractRepository::getListGroupStaffId($expiredWhere, ['max(id) as id']);

        $contractExpiredListInfo = [];
        if(!empty($contractExpiredList)) {
            $contractExpiredWhere['ids'] = array_column($contractExpiredList, 'id');
            $contractExpiredListInfo = HrStaffContractRepository::getList($contractExpiredWhere, ['id', 'staff_id', 'contract_type', 'contract_child_type', 'version']);
        }

        $contractExpiredListInfoToStaffId = array_column($contractExpiredListInfo, NULL, 'staff_id');

        //查询最新的一次 已续约合同
        $renewWhere['staff_info_id'] = $staffIdsArr;
        $renewWhere['contract_status'] = [HrStaffContractModel::CONTRACT_STATUS_RDNEWED];
        $renewWhere['contract_type'] = HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT;
        $contractRenewList = HrStaffContractRepository::getListGroupStaffId($renewWhere, ['max(id) as id']);

        $contractRenewListInfo = [];
        if(!empty($contractRenewList)) {
            $contractRenewWhere['ids'] = array_column($contractRenewList, 'id');
            $contractRenewListInfo = HrStaffContractRepository::getList($contractRenewWhere, ['id', 'staff_id', 'contract_type', 'contract_child_type', 'version']);
        }

        $contractRenewListInfoToStaffId = array_column($contractRenewListInfo, NULL, 'staff_id');

        //by离职申请信息
        $resignWhere['resign_status'] = [Enums\ApprovalEnums::APPROVAL_STATUS_APPROVAL, Enums\ApprovalEnums::APPROVAL_STATUS_TIMEOUT];
        $resignWhere['staff_ids']     = $staffIdsArr;
        $resignWhere['resign_source'] = StaffResignModel::SOURCE_BY;
        $resignWhere['leave_source']  = LeaveManagerService::LEAVE_SOURCE_BACKYARD;
        $resignWhere['staff_state']   = HrStaffInfoModel::STATE_RESIGN;

        $staffResignList = (new StaffResignRepository())->getStaffResignList($resignWhere, ['sr.created_at', 'sr.leave_date', 'sr.submitter_id']);

        $staffResignListToId = array_column($staffResignList, NULL, 'submitter_id');

        //公司解约
        $terWhere['ter_status']    = [Enums\ApprovalEnums::APPROVAL_STATUS_APPROVAL];
        $terWhere['staff_ids']     = $staffIdsArr;
        $terWhere['leave_source']  = LeaveManagerService::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT;
        $terWhere['staff_state']   = HrStaffInfoModel::STATE_RESIGN;

        $staffCompanyTer = (new CompanyTerminationContractRepository())->getStaffList($terWhere, ['hsi.staff_info_id', 'ctc.leave_date']);

        $staffCompanyTerToId = array_column($staffCompanyTer, NULL,'staff_info_id');


        $list = [];
        foreach ($staffIdsArr as $oneId) {
            $data['staff_info_id']   = $oneId;//工号
            $data['tax_address']     = isset($staffIdsItems[$oneId]) ? $this->getStaffRegisterAddress($staffIdsItems[$oneId]) : '';//税务地址:身份证地址，户口所在地址
            $data['tax_postal_code'] = isset($staffIdsItems[$oneId]) ? ($staffIdsItems[$oneId]['REGISTER_POSTCODES'] ?? '') : '';//税务地址邮编 身份证地址所在地 邮编
            $data['mobile']          = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['mobile'] ?? '') : '';//联系电话
            $data['personal_email']  = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['personal_email'] ?? '') : '';//联系人邮箱
            $data['bank_no']         = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['bank_no'] ?? '') : '';//银行卡号
            $data['bank_no_name']    = isset($staffIdsItems[$oneId]) ? ($staffIdsItems[$oneId]['BANK_NO_NAME'] ?? '') : '';//持卡人姓名

            $bank_type               = isset($staffInfosToId[$oneId]) ? $staffInfosToId[$oneId]['bank_type'] : '';
            $data['bank_name']       = $bankListToId[$bank_type] ?? '';//银行名称

            $data['incentive_is_hold'] = in_array($oneId, $holdInfoToStaffId);//员工是否有 提成hold

            //是否解约
            $is_cancel_contract = GlobalEnums::NO;
            if(!empty($staffInfosToId[$oneId]) && $staffInfosToId[$oneId]['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $is_cancel_contract = GlobalEnums::YES;
            }

            //是否公司主动解约
            $is_company_termination = !empty($staffCompanyTerToId[$oneId]) ? GlobalEnums::YES : GlobalEnums::NO;

            //提前解约天数
            $early_termination_days = 0;
            if(empty($is_company_termination) && !empty($is_cancel_contract) && isset($staffResignListToId[$oneId])) {
                $startDay = date('Y-m-d', strtotime($staffResignListToId[$oneId]['created_at']) + ($this->timeOffset) * 3600);
                $endDay = $staffResignListToId[$oneId]['leave_date'];
                $current_date_obj    = new DateTime($endDay);
                $before_date_max_obj = new DateTime($startDay);
                $days                = $current_date_obj->diff($before_date_max_obj)->days;

                $early_termination_days = $days >= 0 ? $days : 0;
            }

            $is_individual_contract = GlobalEnums::NO;
            $staffContract = [];
            if(isset($contractExpiredListInfoToStaffId[$oneId])) {
                $staffContract = $contractExpiredListInfoToStaffId[$oneId];
            }
            //如果存在 有效合同，取有效合同（以有效合同为准）
            if(isset($contractListInfoToStaffId[$oneId])) {
                $staffContract = $contractListInfoToStaffId[$oneId];
            }

            //如果存在 已续约合同，取已续约合同
            if(empty($staffContract) && isset($contractRenewListInfoToStaffId[$oneId])) {
                $staffContract = $contractRenewListInfoToStaffId[$oneId];
            }

            if(!empty($staffContract) && $staffContract['contract_child_type'] == HrStaffContractModel::CONTRACT_CHILD_LDHT_INDE_TH && $staffContract['version'] == HrStaffContractModel::VERSION_LDHT_INDE_2) {
                $is_individual_contract = GlobalEnums::YES;
            }

            $data['is_cancel_contract'] = $is_cancel_contract;//是否解约
            $data['is_company_termination'] = $is_company_termination;//是否公司主动解约
            $data['early_termination_days'] = $early_termination_days;//提前解约天数
            $data['is_individual_contract'] = $is_individual_contract;//是否是个人代理合同2
            $this->logger->info(['getStaffTaxInfo-svc' => $data]);

            $list[]                  = $data;
        }

        return $list;
    }

    /**
     * 获取银行审核状态
     * @param $staffIds
     * @return array
     */
    public function getStaffBankAuditStatus($staffIds)
    {
        //获取银行审核状态
        $conditionsAnnex             = 'staff_info_id in ({staff_info_ids:array}) and type = :type:';
        $bindAnnex['staff_info_ids'] = $staffIds;
        $bindAnnex['type']           = HrStaffAnnexInfoModel::TYPE_BANK_CARD;
        $staffBankList  = HrStaffAnnexInfoRepository::getStaffAnnexInfoList('*', $conditionsAnnex, $bindAnnex);
        return array_column($staffBankList, 'audit_state', 'staff_info_id');
    }

    /**
     * 离职资产处理状态
     * @param $staffIds
     * @param $columns
     * @return array
     */
    public function getStaffLeaveAssetStatus($staffIds, $columns = ['*'])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(LeaveManagerModel::class, 'hsi.staff_info_id = lm.staff_info_id', 'lm');

        //正式员工和实习生 非子账号 离职日期>2020-04-17 00:00:00 待离职或离职状态
        $builder->where("hsi.formal in (1, 4) and hsi.is_sub_staff = 0 and hsi.leave_date > '2020-04-17 00:00:00' and ((hsi.state = 1 and hsi.wait_leave_state = 1) or hsi.state = 2)");
        $builder->andWhere("hsi.staff_info_id in ({staff_info_ids:array})", ['staff_info_ids' => $staffIds]);

        $data = $builder->getQuery()->execute()->toArray();
        return array_column($data, 'assets_remand_state', 'staff_info_id');
    }

    /**
     * 获取y员工信息 给invoice
     * @param $staffIdsString
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function getStaffInvoiceInfo($staffIdsString)
    {
        if (empty($staffIdsString)) {
            return [];
        }
        $staffIdsArr = explode(',', $staffIdsString);
        if(count($staffIdsArr) > 500) {
            throw new ValidationException("staff number limit 500!");
        }

        $item_arr = [
            'RESIDENCE_DETAIL_ADDRESS',    //详细地址
            'RESIDENCE_POSTCODES',   //邮编
            'RESIDENCE_CITY',       //居住市
            'RESIDENCE_PROVINCE',   //居住地省
            'BANK_NO_NAME',   //持卡人姓名
        ];

        $staffIdsItems = HrStaffItemsRepository::getStaffItemByIds($staffIdsArr, $item_arr);

        $staffInfos = HrStaffInfoRepository::getHrStaffByIds($staffIdsArr,
            ['staff_info_id', 'mobile', 'personal_email', 'bank_no', 'bank_type', 'identity', 'state']);

        $staffInfosToId = array_column($staffInfos, null, 'staff_info_id');

        $bankIds = array_values(array_unique(array_column($staffInfos, 'bank_type')));

        $bankList     = BankListRepository::getBankList($bankIds, ['bank_id', 'bank_name']);
        $bankListToId = array_column($bankList, 'bank_name', 'bank_id');

        //获取银行审核状态
        $staffBankListToStaffId = $this->getStaffBankAuditStatus($staffIdsArr);
        //离职资产处理状态
        $staffLeaveAssetStatusToStaffId = $this->getStaffLeaveAssetStatus($staffIdsArr, ['lm.staff_info_id', 'lm.assets_remand_state']);

        $list = [];
        foreach ($staffIdsArr as $oneId) {
            $data['staff_info_id']     = $oneId;//工号
            $address = '';
            if(isset($staffIdsItems[$oneId])) {
                $this->staffItemsData = $staffIdsItems[$oneId];
                $address = $this->getStaffResidenceAddressDetail();
            }
            $data['address']     = $address;//居住地址
            $data['identity']         = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['identity'] ?? '') : '';//身份证号
            $data['mobile']         = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['mobile'] ?? '') : '';//个人电话

            $data['bank_no']         = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['bank_no'] ?? '') : '';//银行卡号
            $data['bank_no_name']    = isset($staffIdsItems[$oneId]) ? ($staffIdsItems[$oneId]['BANK_NO_NAME'] ?? '') : '';//持卡人姓名
            $bank_type               = isset($staffInfosToId[$oneId]) ? $staffInfosToId[$oneId]['bank_type'] : '';
            $data['bank_name']       = $bankListToId[$bank_type] ?? '';//银行名称
            $data['bank_type']       = $bank_type ?? '';//银行类型

            $data['bank_audit_status'] = isset($staffBankListToStaffId[$oneId]) ? $staffBankListToStaffId[$oneId] : HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;//银行卡审核状态：0待审核，1审核通过，2审核未通过，999未上传
            $data['asset_fix_status']  = isset($staffLeaveAssetStatusToStaffId[$oneId]) ? $staffLeaveAssetStatusToStaffId[$oneId] : LeaveManagerService::ASSETS_STATE_UNPROCESS;//离职资产处理状态：1未处理，2处理中，3已处理。0不存在数据

            $list[] = $data;
        }

        return $list;
    }

    /**
     * 是否是一线职位
     * @param $departmentId
     * @param $JobId
     * @return bool
     */
    public function isFirstLineJob($departmentId, $JobId): bool
    {
        $jobs = (new SettingEnvService())->getSetVal('first_line_jobs',',');
        foreach ($jobs as $item) {
            $i = explode("|", $item);
            if (isset($i[0]) && isset($i[1])) {
                if ($i[0] == $departmentId && $i[1] == $JobId) {
                    return true;
                }
            }
        }
        return false;
    }

    public function getICSettlementStaffInfo($local, $params)
    {
        if (empty($params['staff_info_ids'])) {
            $this->logger->error(['function' => 'getICSettlementStaffInfo', 'params' => $params]);
            return ['code' => ErrCode::VALIDATE_ERROR, 'msg' => 'params error', 'data' => []];
        }

        $data = $this->getSettlementStaffInfo($params['staff_info_ids']);

        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => $data,
        ];
    }

    public function getSettlementStaffInfo($staffIdsString)
    {
        if (!isCountry(['PH'])) {
            throw new ValidationException("Not currently supported");
        }
        if (empty($staffIdsString)) {
            return [];
        }
        $staffIdsArr = explode(',', $staffIdsString);
        if(count($staffIdsArr) > 500) {
            throw new ValidationException("staff number limit 500!");
        }

        $item_arr = [
            'BANK_NO_NAME',        //持卡人姓名
        ];
        $staffIdsItems = HrStaffItemsRepository::getStaffItemByIds($staffIdsArr, $item_arr);

        $staffInfos = HrStaffInfoRepository::getHrStaffByIds($staffIdsArr,
            ['staff_info_id', 'mobile', 'personal_email', 'bank_no', 'bank_type', 'leave_source', 'state']);

        $staffInfosToId = array_column($staffInfos, null, 'staff_info_id');

        $bankIds = array_values(array_unique(array_column($staffInfos, 'bank_type')));

        $bankList     = BankListRepository::getBankList($bankIds, ['bank_id', 'bank_name']);
        $bankListToId = array_column($bankList, 'bank_name', 'bank_id');
        $holdManageService = new HoldManageService();
        $holdInfo = $holdManageService->getStaffHoldInfoIncomplete($staffIdsArr, 'staff_info_id,hold_reason');
        $holdInfoToStaffId = empty($holdInfo) ? [] : array_column($holdInfo, 'staff_info_id');

        $staff_hold_reason = [];
        foreach ($holdInfo as $oneHold) {
            $staff_hold_reason[$oneHold['staff_info_id']][] = $oneHold['hold_reason'];
        }

        $hold_reason = $holdManageService->getHoldReason();
        $hold_reason_to_id = [];
        //获取hold 原因 key 和 id 映射
        foreach ($hold_reason as $oneType) {
            foreach ($oneType as $onReason){
                $hold_reason_to_id[$onReason['key']] = $onReason['reason_id'];
            }
        }

        //获取员工 hold 原因
        $reason_ids = [];
        foreach ($staff_hold_reason as $staffId => $oneReason) {
            foreach ($oneReason as $oneKey) {
                $reason_ids[$staffId][] = $hold_reason_to_id[$oneKey];
            }
        }

        //获取银行审核状态
        $staffBankListToStaffId = $this->getStaffBankAuditStatus($staffIdsArr);

        $list = [];
        foreach ($staffIdsArr as $oneId) {
            $data['staff_info_id']     = $oneId;//工号
            $data['bank_audit_status'] = isset($staffBankListToStaffId[$oneId]) ? $staffBankListToStaffId[$oneId] : HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;//银行卡审核状态：0待审核，1审核通过，2审核未通过，999未上传
            $data['bank_no']         = isset($staffInfosToId[$oneId]) ? ($staffInfosToId[$oneId]['bank_no'] ?? '') : '';//银行卡号
            $data['bank_no_name']    = isset($staffIdsItems[$oneId]) ? ($staffIdsItems[$oneId]['BANK_NO_NAME'] ?? '') : '';//持卡人姓名

            $bank_type               = isset($staffInfosToId[$oneId]) ? $staffInfosToId[$oneId]['bank_type'] : '';
            $data['bank_name']       = $bankListToId[$bank_type] ?? '';//银行名称
            $data['incentive_is_hold'] = in_array($oneId, $holdInfoToStaffId);//员工是否有 提成hold
            $data['hold_reason_id'] = empty($reason_ids[$oneId]) ? '' : implode(',', array_values(array_unique($reason_ids[$oneId])));//hold 原因

            $list[] = $data;
        }

        return $list;
    }

    /**
     * 获取工具号信息
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getToolStaffInfo($local, $params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }
        $staffIdsArr = explode(',', $params['staff_info_ids']);
        if(count($staffIdsArr) > 500) {
            throw new ValidationException("staff number limit 500!");
        }

        $data = HrStaffInfoRepository::getToolStaffInfoList(['staff_info_ids' => $staffIdsArr], ['staff_info_id', 'name', 'bank_id', 'bank_no', 'mobile', 'identity', 'address', 'state', 'node_department_id']);

        $position = HrStaffInfoPositionRepository::getToolStaffPositionInfo($staffIdsArr);

        $bankIds = array_values(array_unique(array_column($data, 'bank_id')));
        $bankList     = BankListRepository::getBankList($bankIds, ['bank_id', 'bank_name']);
        $bankListToId = array_column($bankList, 'bank_name', 'bank_id');

        $list = [];
        foreach ($data as $oneData) {
            $oneData['bank_no'] = empty($oneData['bank_no']) ? '' : $oneData['bank_no'];
            $oneData['bank_name'] = $bankListToId[$oneData['bank_id']] ?? '';
            $oneData['mobile'] = empty($oneData['mobile']) ? '' : $oneData['mobile'];
            $oneData['identity'] = empty($oneData['identity']) ? '' : $oneData['identity'];
            $oneData['address'] = empty($oneData['address']) ? '' : $oneData['address'];
            $oneData['node_department_id'] = empty($oneData['node_department_id']) ? '' : $oneData['node_department_id'];
            $oneData['roles'] = empty($position[$oneData['staff_info_id']]) ? '' : $position[$oneData['staff_info_id']];
            $list[] = $oneData;
        }

        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => $list,
        ];
    }

    public function autoCreateEmailSend($date)
    {

        if(empty($date) || !preg_match('/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/', $date)) {
            echo "请输入有效日期";
            return false;
        }
        $log = '开始时间: ' . date('Y-m-d H:i:i:s') . PHP_EOL;
        $data = AutoCreateEmailRepository::getList(['date' =>$date]);

        if(empty($data)) {
            echo "暂无自动生成的邮箱数据";
            $log .= "暂无自动生成的邮箱数据" . PHP_EOL;
            $this->getDI()->get('logger')->info("autoCreateEmailAction： " . $log);
            return false;
        }

        $dataToStaffId = array_column($data, 'email', 'staff_info_id');

        $staffInfoList = StaffInfoRepository::getStaffIdForColumns(array_keys($dataToStaffId), ['staff_info_id, name, job_title, sys_store_id, node_department_id']);
        if(empty($staffInfoList)) {
            echo "获取员工数据失败";
            $log .= "获取员工数据失败" . PHP_EOL;
            $this->getDI()->get('logger')->info("autoCreateEmailAction： " . $log);
            return false;
        }


        $storeListToId = $jobTitleInfoToId = $operatorInfoToId = $list = [];
        $jobTitleId    = array_values(array_unique(array_column($staffInfoList, 'job_title')));
        $sysStoreId    = array_values(array_unique(array_column($staffInfoList, 'sys_store_id')));
        $departmentIds = array_values(array_unique(array_column($staffInfoList, 'node_department_id')));

        $storeList = (new SysStoreService())->getStoreListByIds($sysStoreId);

        $storeList[] = [
            'id'    => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'category'    => 0,
            'region_name' => '',
            'piece_name'  => '',
        ];

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $jobTitleInfo       = (new DepartmentService())->getJobList('', $jobTitleId, true);
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        //部门名称
        $departmentList = (new DepartmentService())->getDepartmentInfo($departmentIds);
        $departmentList = empty($departmentList) ? [] : array_column($departmentList, 'name', 'id');

        $allData = [];
        foreach ($staffInfoList as $staff_info_id => $oneStaff) {
            $oneData[] = $staff_info_id;
            $oneData[] = $oneStaff['name'];
            $oneData[] = $jobTitleInfoToId[$oneStaff['job_title']];
            $oneData[] = $departmentList[$oneStaff['node_department_id']];
            $oneData[] = !empty($storeListToId[$oneStaff['sys_store_id']]) ? $storeListToId[$oneStaff['sys_store_id']]['store_name'] : '';
            $oneData[] = !empty($storeListToId[$oneStaff['sys_store_id']]) ? $storeListToId[$oneStaff['sys_store_id']]['piece_name'] : '';
            $oneData[] = !empty($storeListToId[$oneStaff['sys_store_id']]) ? $storeListToId[$oneStaff['sys_store_id']]['region_name'] : '';
            $oneData[] = $dataToStaffId[$staff_info_id] ?? '';

            $allData[] = $oneData;
        }
        if(empty($allData)) {
            echo "导出数据为空";
            $log .= "导出数据为空" . PHP_EOL;
            $this->getDI()->get('logger')->info("autoCreateEmailAction： " . $log);
            return false;
        }

        $title = [
            'Staff id',
            'Name',
            'Position',
            'Department',
            'Branch',
            'District',
            'Area',
            'Company Email',
        ];

        $fileName = 'Staff_auto_create_email_' . date('YmdHis') . '.xlsx';
        $fileUrl[] = (new \App\Services\BllService())->creatExcel($title , $allData , $fileName);


        //获取邮件接收人邮箱
        $staffsEmail = (new SettingEnvService())->getSetVal('auto_create_email_send_email_config');
        //发送邮件
        $content = "The system automatically sets the email addresses of the following employees, please check.";
        if(!empty($staffsEmail)){
            $toUsers = explode(',', $staffsEmail);
            $sendResult = \App\Library\BiMail::send($toUsers,get_country_code() . '-Automatically set email address notification ' . date('Y-m-d') , $content, $fileUrl);
            if($sendResult) {
                $log .= "邮件发送成功: " . $staffsEmail . PHP_EOL;
            } else {
                $log .= "邮件发送失败: " . $staffsEmail . PHP_EOL;
            }
        } else {
            $log .= "邮件发送失败: 接收人不能为空" . PHP_EOL;
        }

        foreach ($fileUrl as $oneFile) {
            if(file_exists($oneFile)) {
                unlink($oneFile);
            }
        }
        $log .= "结束时间: " . date('Y-m-d H:i:i:s') . PHP_EOL;

        $this->getDI()->get('logger')->info("autoCreateEmailAction： " . $log);
        return true;
    }

    /**
     * 计算 员工在职天数
     * @param int $isAll
     * @throws \Exception
     */
    public function onJobCount($isAll = 0)
    {
        $page = 1;
        $page_size = 3000;
        $batch_count = 1;//批次
        $is_not_leave = empty($isAll) ? 1 : 0;

        $staffInfoRepository = new \App\Repository\StaffInfoRepository();
        $log =  PHP_EOL . "处理批次[batch=$batch_count]: start = $batch_count, per_length = $page_size-$page " . PHP_EOL;
        echo $log;
        $this->logger->info($log);
        $db            = $this->getDI()->get("db_backyard");
        while(true){
            $staffInfoList = $staffInfoRepository->getStaffInfoList($page, $page_size, ['formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN], 'is_not_leave' => $is_not_leave], ['staff_info_id', 'state', 'hire_date', 'leave_date']);
            if(empty($staffInfoList)) {
                break;
            }

            $this->execute($staffInfoList, $db);

            // 取下一批次同步员工
            $batch_count++;
            $page++;
            $log = PHP_EOL . "处理批次[batch=$batch_count]: start = $batch_count, per_length = $page_size-$page " . PHP_EOL;
            echo $log;
            $this->logger->info($log);
        }

        if(!empty($is_not_leave)) {
            //离职日期 30天内的重新计算。可能会是 停职 直接 变 离职的情况。那么离职日期会变为 停职日期。在职天数会减少。
            $where['formal']           = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
            $where['state']            = HrStaffInfoModel::STATE_RESIGN;
            $where['leave_date_limit'] = date('Y-m-d 00:00:00', strtotime('-30 day'));
            $where['all']              = 1;

            $staffInfoList             = $staffInfoRepository->getStaffInfoList(0, 0, $where,
                ['staff_info_id', 'state', 'hire_date', 'leave_date']);
            $this->execute($staffInfoList, $db);
        }

    }

    /**
     * 执行计算
     * @param $staffInfoList
     * @param $db
     */
    public function execute($staffInfoList, $db)
    {
        $today = date('Y-m-d');

        foreach ($staffInfoList as $one) {
            $hire_date         = date('Y-m-d', strtotime($one['hire_date']));
            switch ($one['state']) {
                case 1: //在职
                case 3: //停职
                    $on_job_days = abs((strtotime($today) - strtotime($hire_date)) / (60 * 60 * 24));
                    break;
                case 2: //离职
                    $leave_today        = date('Y-m-d', strtotime($one['leave_date']));
                    if(empty($one['leave_date'])) {//正常走不到这个逻辑
                        $leave_today = $today;
                    }
                    $on_job_days = abs((strtotime($leave_today) - strtotime($hire_date)) / (60 * 60 * 24));
                    break;
                default:
                    $on_job_days = 0;
            }
            $db->updateAsDict(
                'hr_staff_info_extend',
                ['on_job_days' => $on_job_days],
                [
                    "conditions" => 'staff_info_id = ?',
                    'bind'       => [$one['staff_info_id']],
                ]
            );
        }
    }


    /**
     * 户口地址
     * @return string
     */
    public function getStaffRegisterAddressDetailForLegalProsecutionMaterials()
    {
        return '';
    }

    /**
     * 获取员工合同签字图片
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffContractSignImage($local, $params): array
    {
        if (empty($params['staff_info_id'])) {
            throw new ValidationException('need staff_info_id');
        }
        $staff_info_id                  = $params['staff_info_id'];
        $contract                       = $this->getStaffSignContract($staff_info_id);
        $returnData['staff_sign_image'] = $contract ? $contract->contract_signature_img : '';
        return $this->checkReturn(['data' => $returnData]);
    }




    public function getStaffSignContract($staff_info_id)
    {
        $contract      = HrStaffContractModel::FindFirst([
            'conditions' => 'staff_id = :staff_id: and contract_status in({contract_status:array}) and contract_is_deleted = 0 and contract_type = 1',
            'bind'       => [
                'staff_id'        => $staff_info_id,
                'contract_status' => [
                    HrStaffContractModel::CONTRACT_STATUS_ARCHIVED,
                    HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED,
                    HrStaffContractModel::CONTRACT_STATUS_EXPIRED,
                ],
            ],
            'order'      => 'id desc',
        ]);
        if (empty($contract)) {
            $contract = HrStaffContractModel::FindFirst([
                'conditions' => 'staff_id = :staff_id: and contract_status in({contract_status:array}) and contract_is_deleted = 0 and contract_type = 1',
                'bind'       => [
                    'staff_id'        => $staff_info_id,
                    'contract_status' => [
                        HrStaffContractModel::CONTRACT_STATUS_ARCHIVED,
                        HrStaffContractModel::CONTRACT_STATUS_RESCIND,
                    ],
                ],
                'order'      => 'updated_at desc',
            ]);
        }
        return $contract;
    }



    /**
     * PH
     * 法律材料
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function legalProsecutionMaterials($local,$params)
    {
        if (empty($params['staff_info_id'])) {
            throw new ValidationException('need staff_info_id');
        }
        $returnData['demand_letter_1_info']    = [];
        $returnData['demand_letter_2_info']    = [];
        $returnData['contract_info']           = [];
        $returnData['staff_sing_image']        = '';
        //户口地址
        $returnData['staff_register_address'] = reBuildCountryInstance(new StaffInfoService())->initStaffItemData($params['staff_info_id'])->getStaffRegisterAddressDetailForLegalProsecutionMaterials();


        $staff_info_id = $params['staff_info_id'];

        $contract = $this->getStaffSignContract($staff_info_id);

        if ($contract) {
            $returnData['contract_info'] = [
                'name' => 'Annex C (Employment Agreement)',
                'file_url' => env('img_prefix') . $contract->contract_path,
            ];
            $returnData['staff_sing_image'] = $contract->contract_signature_img;
        }

        $dl01 = SuspensionFileModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :file_type:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'file_type'     => SuspensionEnums::FILE_TYPE_DL01,
            ],
            'order'      => 'id desc',
        ]);
        $dl02 = SuspensionFileModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :file_type:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'file_type'     => SuspensionEnums::FILE_TYPE_DL02,
            ],
            'order'      => 'id desc',
        ]);


        if ($dl01) {
            $returnData['demand_letter_1_info'] = [
                'name' => 'Demand Letter 1',
                'file_url' => $dl01->file_url,
            ];
        }

        if ($dl02) {
            $returnData['demand_letter_2_info'] = [
                'name' => 'Demand Letter 2',
                'file_url' => $dl01->file_url,
            ];
        }
        return $this->checkReturn(['data'=>$returnData]);
    }


}
