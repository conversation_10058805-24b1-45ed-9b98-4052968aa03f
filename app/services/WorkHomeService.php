<?php


namespace App\Services;


use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\WorkHomeModel;
use App\Models\backyard\WorkHomeSettingModel;
use App\Models\backyard\WorkHomeStaffModel;
use App\Models\backyard\WorkHomeStaffSettingModel;
use App\Modules\My\library\Enums\enums;

class WorkHomeService extends BaseService
{

    public function list($param)
    {
        $page       = $param['page'];
        $size       = $param['size'] ?: 50;
        $conditions = ' is_delete = :is_delete: ';
        $bind       = ['is_delete' => 0];

        if (!empty($param['type'])) {
            $conditions   .= ' and  type = :type: ';
            $bind['type'] = (int)$param['type'];
        }

        if (!empty($param['department_id'])) {
            $conditions     .= ' and  FIND_IN_SET(:dep_id:, department_id) ';
            $bind['dep_id'] = (int)$param['department_id'];
        }

        if (!empty($param['job_title'])) {
            foreach ($param['job_title'] as $k => $job) {
                $key        = 'job_' . $k;
                $jobArr[]   = "FIND_IN_SET(:{$key}:, job_title)";
                $conditions .= 'and (' . implode(' or ', $jobArr) . ')';
                $bind[$key] = $job;
            }
        }
        if (!empty($param['hire_type'])) {
            foreach ($param['hire_type'] as $k => $hire) {
                $key        = 'hire_' . $k;
                $hireArr[]  = "FIND_IN_SET(:{$key}:, hire_type)";
                $conditions .= 'and (' . implode(' or ', $hireArr) . ')';
                $bind[$key] = $hire;
            }
        }

        $count = WorkHomeModel::findFirst([
            'columns'    => 'count(1) as count',
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if (empty($count)) {
            return ['list' => [], 'count' => 0];
        }

        $data = WorkHomeModel::find([
            'offset'     => ($page - 1) * $size,
            'limit'      => $size,
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id desc',
        ])->toArray();

        $data = $this->format($data);
        return ['list' => $data, 'count' => $count->count];
    }


    //整理数据
    public function format($data)
    {
        if (empty($data)) {
            return [];
        }
        //部门
        $departmentIds    = array_column($data, 'department_id');
        $departmentIds    = implode(',', $departmentIds);
        $departmentIds    = array_unique(explode(',', $departmentIds));
        $departmentIds    = array_values(array_diff($departmentIds, ['', null]));
        $departmentServer = new SysDepartmentService();
        $depInfo          = $departmentServer->getListByIds($departmentIds);

        //所在州
        $province_list = SysProvinceModel::find()->toArray();
        $province_list = array_column($province_list, 'name', 'code');
        //职位
        $jobStrList = array_column($data, 'job_title');
        $jobStr     = implode(',', $jobStrList);
        $jobList    = array_unique(explode(',', $jobStr));
        $jobList    = array_values(array_diff($jobList, ['', null]));
        $jobServer  = new HrJobTitleService();
        $jobInfo    = $jobServer->getJobTitleMapByIds($jobList);


        foreach ($data as &$da) {
            //配置类型
            $da['type'] = (int)$da['type'];
            if ($da['type'] == WorkHomeModel:: WORK_HOME_DATE) {
                $da['type_text'] = self::$t->_('for_date');
            } else {
                $da['type_text'] = self::$t->_('for_week');
                $weeks           = explode(',', $da['weekday']);
                $da['weekday']   = array_map('intval', $weeks);
                $weekStr         = '';
                foreach ($weeks as $w) {
                    $weekStr .= self::$t->_('default_rest_day_' . $w) . ',';
                }
                $da['date_list'] = rtrim($weekStr, ',');
            }

            //部门
            $da['department_id'] = explode(',', $da['department_id']);
            foreach ($da['department_id'] as $depId) {
                $da['department_name'][] = $depInfo[$depId]['name'];
            }
            $da['department_name'] = implode(',', $da['department_name']);

            //所在州
            $da['province_code'] = explode(',', $da['province_code']);
            foreach ($da['province_code'] as $code) {
                $code                  = strtoupper($code);
                $da['province_name'][] = $province_list[$code];
            }
            $da['province_name'] = implode(',', $da['province_name']);

            //职位
            if (!empty($da['job_title'])) {
                $da['job_title'] = explode(',', $da['job_title']);
                foreach ($da['job_title'] as $job) {
                    $da['job_title_name'][] = $jobInfo[$job];
                }
                $da['job_title_name'] = implode(',', $da['job_title_name']);
            }

            //雇佣类型 $key = 'hire_type_' . $staff_info['hire_type'];
            $da['hire_type'] = explode(',', $da['hire_type']);
            foreach ($da['hire_type'] as $hire) {
                $da['hire_type_name'][] = self::$t->_("hire_type_{$hire}");
            }
            $da['hire_type_name'] = implode(',', $da['hire_type_name']);

            $da['created_at'] = show_time_zone($da['created_at']);
            $da['updated_at'] = show_time_zone($da['updated_at']);
        }
        return $data;
    }

    /**
     * @throws ValidationException
     * @throws \Exception
     */
    public function add($param)
    {
        sort($param['department_id']);
        sort($param['hire_type']);
        sort($param['province_code']);

        if (!empty($param['job_title'])) {
            sort($param['job_title']);
        }

        //验证逻辑
        if ($param['type'] == WorkHomeModel::WORK_HOME_DATE) {
            if (empty($param['date_list'])) {
                throw new ValidationException('need date param');
            }
            sort($param['date_list']);
            //不能选择过去日期
            $today = date('Y-m-d');
            foreach ($param['date_list'] as $date) {
                if ($date <= $today) {
                    throw new ValidationException('wrong date ' . $date);
                }
            }
        } else {
            if (empty($param['weekday'])) {
                throw new ValidationException('need weekday param');
            }
            sort($param['weekday']);
        }

        //主表
        $insert['department_id'] = empty($param['department_id']) ? '' : implode(',', $param['department_id']);
        $insert['job_title']     = empty($param['job_title']) ? '' : implode(',', $param['job_title']);
        $insert['hire_type']     = empty($param['hire_type']) ? '' : implode(',', $param['hire_type']);
        $insert['province_code'] = empty($param['province_code']) ? '' : implode(',', $param['province_code']);
        $insert['type']          = (int)$param['type'];
        if ($param['type'] == WorkHomeModel::WORK_HOME_DATE) {
            $insert['date_list'] = empty($param['date_list']) ? '' : implode(',', $param['date_list']);
        } else {
            $insert['weekday'] = empty($param['weekday']) ? '' : implode(',', $param['weekday']);
        }
        $insert['remark'] = $param['remark'];

        $this->validateCheck($insert);

        //获取所有子部门
        $departmentServer = new SysDepartmentService();
        $departmentData   = $departmentServer->batchGetSubList($param['department_id']);
        if (empty($departmentData)) {
            throw new ValidationException('department info lost');
        }
        $departmentIds = array_column($departmentData, 'id');
        $model         = new WorkHomeModel();
        $model->create($insert);

        //拆分表
        $data         = [];
        $settingModel = new WorkHomeSettingModel();
        foreach ($departmentIds as $dep) {
            foreach ($param['hire_type'] as $hire) {
                foreach ($param['province_code'] as $pCode) {
                    if ($param['type'] == WorkHomeModel::WORK_HOME_DATE) {
                        foreach ($param['date_list'] as $date) {
                            $row['origin_id']          = $model->id;
                            $row['node_department_id'] = $dep;
                            $row['hire_type']          = $hire;
                            $row['province_code']      = $pCode;
                            $row['date_at']            = $date;
                            $row['type']               = $param['type'];
                            if (!empty($param['job_title'])) {//非必填
                                foreach ($param['job_title'] as $job) {
                                    $row['job_title'] = $job;
                                }
                            }
                            $data[] = $row;
                        }
                    } else {
                        foreach ($param['weekday'] as $week) {
                            $row['origin_id']          = $model->id;
                            $row['node_department_id'] = $dep;
                            $row['hire_type']          = $hire;
                            $row['province_code']      = $pCode;
                            $row['weekday']            = $week;
                            $row['type']               = $param['type'];
                            if (!empty($param['job_title'])) {//非必填
                                foreach ($param['job_title'] as $job) {
                                    $row['job_title'] = $job;
                                }
                            }
                            $data[] = $row;
                        }
                    }
                }
            }
        }
        $settingModel->batch_insert($data, $settingModel::WRITE_DB_PHALCON_DI_NAME);
        return true;
    }

    /**
     * @throws ValidationException
     */
    public function validateCheck($insert)
    {
        //重复规则
        if ($insert['type'] == WorkHomeModel::WORK_HOME_DATE) {
            $conditions = 'department_id = :department_id: and hire_type = :hire_type: and province_code = :province_code: and date_list = :date_list: and is_delete = 0';
            $bind       = [
                'department_id' => $insert['department_id'],
                'hire_type'     => $insert['hire_type'],
                'province_code' => $insert['province_code'],
                'date_list'     => $insert['date_list'],
            ];
        } else {
            $conditions = 'department_id = :department_id: and hire_type = :hire_type: and province_code = :province_code: and weekday = :weekday: and is_delete = 0';
            $bind       = [
                'department_id' => $insert['department_id'],
                'hire_type'     => $insert['hire_type'],
                'province_code' => $insert['province_code'],
                'weekday'       => $insert['weekday'],
            ];
        }

        if (!empty($insert['job_title'])) {
            $conditions        .= " and job_title = :job_title:";
            $bind['job_title'] = $insert['job_title'];
        }else{
            $conditions .= " and job_title = '' ";
        }
        $exist = WorkHomeModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        //如果 数据库中有部门 本次添加 没部门 不算重复
        if (!empty($exist->job_title) && empty($insert['job_title'])) {
            return true;
        }

        if (!empty($exist)) {
            throw new ValidationException(self::$t->_('ticket_repeat_msg'));
        }

        return true;
    }

    /**
     * @throws ValidationException
     */
    public function delete($param)
    {
        if (empty($param['id'])) {
            throw new ValidationException('id error');
        }
        $info = WorkHomeModel::findFirst($param['id']);
        $this->db_backyard->execute("update work_home_setting set is_delete = 1 where origin_id = :id",
            ['id' => $param['id']]);
        $info->is_delete = 1;
        $info->update();
        return true;
    }


    /**
     * 轮休页面 获取对应日期是否居家办公
     * @param $dateList
     * @param $staffInfos
     * @return array
     */
    public function getWhDays($dateList, $staffInfos)
    {
        if (empty($dateList)) {
            return [];
        }

        //工作所在州
        $staffIds              = array_column($staffInfos, 'staff_info_id');
        $staffProvinceCodeList = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array}) and item = :item:',
            'bind'       => ['staff_info_id' => $staffIds, 'item' => 'STAFF_PROVINCE_CODE'],
        ])->toArray();
        $staffProvinceCodeList = array_column($staffProvinceCodeList, 'value', 'staff_info_id');
        //整理 成星期对应日期
        $weekDateList = formatDateWeek($dateList);
        $allWeeks     = array_keys($weekDateList);
        //先获取员工的 居家办公日期
        $staffWH     = WorkHomeStaffSettingModel::find([
            'columns'    => 'staff_info_id, date_at, weekday',
            'conditions' => 'staff_info_id in ({ids:array}) and (date_at in ({dates:array}) or weekday in ({weeks:array})) and is_delete = 0',
            'bind'       => ['ids' => $staffIds, 'dates' => $dateList, 'weeks' => $allWeeks],
        ])->toArray();
        $staffWHData = [];
        foreach ($staffWH as $item) {
            $staffWHData[$item['staff_info_id']] = empty($staffWHData[$item['staff_info_id']]) ? [] : $staffWHData[$item['staff_info_id']];
            if (!empty($item['weekday'])) {
                $staffDates                          = $weekDateList[$item['weekday']];
                $staffWHData[$item['staff_info_id']] = array_merge($staffWHData[$item['staff_info_id']], $staffDates);
            } else {
                $staffWHData[$item['staff_info_id']][] = $item['date_at'];
            }
        }

        foreach ($staffInfos as $staff) {
            $staffWHData[$staff['staff_info_id']] = empty($staffWHData[$staff['staff_info_id']]) ? [] : $staffWHData[$staff['staff_info_id']];
            //只有总部员工有配置
            if ($staff['sys_store_id'] != GlobalEnums::HEAD_OFFICE_ID) {
                $staffWHData[$staff['staff_info_id']] = [];
                continue;
            }
            //居家办公日期
            $homeData = WorkHomeSettingModel::find([
                'conditions' => 'node_department_id = :node_department_id: and (date_at in ({dates:array}) or weekday in ({weeks:array}))
                and hire_type = :hire_type: and province_code = :province_code: and  is_delete = 0
                and (job_title is null or job_title = :job_title:)',
                'bind'       => [
                    'node_department_id' => $staff['node_department_id'],
                    'hire_type'          => $staff['hire_type'],
                    'province_code'      => $staffProvinceCodeList[$staff['staff_info_id']] ?? enums::HEAD_OFFICE_PROVINCE_CODE,
                    'dates'              => $dateList,
                    'job_title'          => $staff['job_title'],
                    'weeks'              => $allWeeks,
                ],
            ])->toArray();
            $dates    = empty($homeData) ? [] : array_column($homeData, 'date_at');
            $dates    = array_diff($dates, ['0000-00-00']);
            $weeks    = empty($homeData) ? [] : array_column($homeData, 'weekday');
            $weeks    = array_diff($weeks, [0]);

            if (!empty($dates)) {//按日期配置
                $staffWHData[$staff['staff_info_id']] = array_merge($staffWHData[$staff['staff_info_id']], $dates);
            }
            if (!empty($weeks)) {//按星期配置
                foreach ($weeks as $w) {
                    $staffWHData[$staff['staff_info_id']] = array_merge($staffWHData[$staff['staff_info_id']],
                        $weekDateList[$w] ?? []);
                }
            }
            $staffWHData[$staff['staff_info_id']] = array_values(array_unique($staffWHData[$staff['staff_info_id']]));
        }
        return $staffWHData;
    }

    /**
     * @param $param
     * @return array
     */
    public function staffList($param)
    {
        $page       = $param['page'];
        $size       = $param['size'] ?: 50;
        $conditions = ' is_delete = :is_delete: ';
        $bind       = ['is_delete' => 0];

        if (!empty($param['staff_info_id'])) {
            $conditions            .= ' and  FIND_IN_SET(:staff_info_id:, staff_info_id) ';
            $bind['staff_info_id'] = $param['staff_info_id'];
        }
        if (!empty($param['type'])) {
            $conditions   .= ' and  type = :type: ';
            $bind['type'] = (int)$param['type'];
        }

        $count = WorkHomeStaffModel::findFirst([
            'columns'    => 'count(1) as count',
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if (empty($count)) {
            return ['list' => [], 'count' => 0];
        }

        $data = WorkHomeStaffModel::find([
            'offset'     => ($page - 1) * $size,
            'limit'      => $size,
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id desc',
        ])->toArray();

        foreach ($data as &$da) {
            //配置类型
            $da['type'] = (int)$da['type'];
            if ($da['type'] == WorkHomeModel::WORK_HOME_DATE) {
                $da['type_text'] = self::$t->_('for_date');
            } else {
                $da['type_text'] = self::$t->_('for_week');
                $weeks           = explode(',', $da['weekday']);
                $da['weekday']   = array_map('intval', $weeks);
                $weekStr         = '';
                foreach ($weeks as $w) {
                    $weekStr .= self::$t->_('default_rest_day_' . $w) . ',';
                }
                $da['date_list'] = rtrim($weekStr, ',');
            }
            $da['created_at'] = show_time_zone($da['created_at']);
            $da['updated_at'] = show_time_zone($da['updated_at']);
        }

        return ['list' => $data, 'count' => $count->count];
    }


    /**
     * @throws ValidationException
     * @throws \Exception
     */
    public function addStaffSetting($param)
    {
        //整理 参数
        $staffStr = str_replace('，', ',', $param['staff_info_id']);
        $staffStr = str_replace(' ', '', $staffStr);
        $staffIds = explode(',', $staffStr);
        $staffIds = array_values(array_diff($staffIds, ['', null]));
        sort($staffIds);

        //验证 是否是未来日期
        if ($param['type'] == WorkHomeModel::WORK_HOME_DATE) {
            sort($param['date_list']);
            if (empty($param['date_list'])) {
                throw new ValidationException('date_list error');
            }
            $today = date('Y-m-d');
            foreach ($param['date_list'] as $date) {
                if ($date <= $today) {
                    throw new ValidationException('wrong date ' . $date);
                }
            }
        } else {
            if (empty($param['weekday'])) {
                throw new ValidationException('weekday error');
            }
            sort($param['weekday']);
        }

        //验证工号
        $staffInfo = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        if (empty($staffInfo)) {
            throw new ValidationException('staff id error');
        }

        $staffInfo    = array_column($staffInfo, 'staff_info_id');
        $noExistStaff = array_diff($staffIds, $staffInfo);
        if (!empty($noExistStaff)) {
            throw new ValidationException('staff id error ' . json_encode($noExistStaff));
        }
        $insert['type'] = (int)$param['type'];
        if ($param['type'] == WorkHomeModel::WORK_HOME_DATE) {
            $insert['date_list'] = empty($param['date_list']) ? '' : implode(',', $param['date_list']);
        } else {
            $insert['weekday'] = empty($param['weekday']) ? '' : implode(',', $param['weekday']);
        }
        $insert['staff_info_id'] = implode(',', $staffIds);
        $insert['remark']        = $param['remark'];
        //验证重复
        $this->validateStaffCheck($insert);

        $model = new WorkHomeStaffModel();
        $model->create($insert);

        //拆分表
        $data         = [];
        $settingModel = new WorkHomeStaffSettingModel();
        foreach ($staffIds as $id) {
            if ($param['type'] == WorkHomeModel::WORK_HOME_DATE) {
                foreach ($param['date_list'] as $date) {
                    $row['origin_id']     = $model->id;
                    $row['staff_info_id'] = $id;
                    $row['date_at']       = $date;
                    $data[]               = $row;
                }
            } else {
                foreach ($param['weekday'] as $w) {
                    $row['origin_id']     = $model->id;
                    $row['staff_info_id'] = $id;
                    $row['weekday']       = $w;
                    $data[]               = $row;
                }
            }
        }
        $settingModel->batch_insert($data, $settingModel::WRITE_DB_PHALCON_DI_NAME);
        return true;
    }

    public function validateStaffCheck($insert)
    {
        //重复规则
        if ($insert['type'] == WorkHomeModel::WORK_HOME_DATE) {
            $conditions = 'staff_info_id = :staff_info_id: and date_list = :date_list: and type = :type: and is_delete = 0';
            $bind       = [
                'staff_info_id' => $insert['staff_info_id'],
                'date_list'     => $insert['date_list'],
                'type'          => $insert['type'],
            ];
        } else {
            $conditions = 'staff_info_id = :staff_info_id: and weekday = :weekday: and type = :type: and is_delete = 0';
            $bind       = [
                'staff_info_id' => $insert['staff_info_id'],
                'weekday'       => $insert['weekday'],
                'type'          => $insert['type'],
            ];
        }

        $exist = WorkHomeStaffModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if (!empty($exist)) {
            throw new ValidationException(self::$t->_('ticket_repeat_msg'));
        }

        return true;
    }

    /**
     * @throws ValidationException
     */
    public function deleteStaff($param)
    {
        if (empty($param['id'])) {
            throw new ValidationException('id error');
        }
        $info = WorkHomeStaffModel::findFirst($param['id']);
        $this->db_backyard->execute("update work_home_staff_setting set is_delete = 1 where origin_id = :id",
            ['id' => $param['id']]);
        $info->is_delete = 1;
        $info->update();
        return true;
    }
}