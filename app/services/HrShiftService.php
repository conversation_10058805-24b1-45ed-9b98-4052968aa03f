<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrShiftV2ExtendModel;
use App\Models\backyard\HrShiftV2Model;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftHistoryModel;
use App\Models\backyard\HrStaffShiftMiddleDateModel;
use App\Models\backyard\HrStaffShiftModel;
use App\Models\backyard\HrStaffShiftPresetModel;
use App\Library\Enums\ShiftEnums;
use App\Models\backyard\StaffAuditReissueForBusinessModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use Exception;
use App\Library\Enums\ConditionsRulesEnums;

/**
 * @method getListFromCache
 * @method getFlexibleShiftDataFromCache
 */
class HrShiftService extends BaseService
{
    /**
     * 六天班轮休 仅当天生效 是否可以修改当天班次
     * @var bool
     */
    public $modifyTodayShift = false;
    /**
     * 启用的班次ids
     * @var array
     */
    public $enable_shift_ids = [];

    public function setEnableShiftIds(array $enable_shift_ids): HrShiftService
    {
        $this->enable_shift_ids = $enable_shift_ids;
        return $this;
    }


    //获取所有班次
    public function getList($params = [])
    {
        $list_h = $this->modelsManager->createBuilder();
        $list_h->from(['shift' => HrShift::class]);
        //默认固定考勤
        $shift_attendance_type = !isset($params['shift_attendance_type']) ? HrShift::SHIFT_ATTENDANCE_TYPE_FIXED : $params['shift_attendance_type'];
        if (!empty($params['shift_group_half_day_shift'])) {
            $list_h->where('shift_group in ({shift_group:array})', ['shift_group' => [HrShift::SHIFT_GROUP_HALF_DAY_SHIFT]]);
        } else {
            //默认 全天班次
            if (empty($params['is_all'])) {
                $list_h->where('shift_group = :shift_group:', ['shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT]);
            }
        }
        if (!empty($shift_attendance_type)) {
            $list_h->andWhere('shift_attendance_type = :shift_attendance_type:',
                ['shift_attendance_type' => $shift_attendance_type]);
        }
        if (!empty($params['condition']) && !empty($params['bind'])) {
            $list_h->andWhere($params['condition'], $params['bind']);
        }
        $order = $params['order'] ?? 'type asc,start asc';
        $list_h->orderBy($order);
        $data = $list_h->getQuery()->execute();
        return $data->toArray();
    }

    /**
     * 网点支援可选班次list
     * @return array
     */
    public function getSupportStoreShiftList()
    {
        $shift_list = [];
        try {
            $list = HrShift::find([
                'conditions' => 'shift_attendance_type = :shift_attendance_type: and id not in(12,13,14,15,16,17,18,19,20,21,22,23,26,28,29,30,40,41,42,43,44,45,46,47,48,49,35) and shift_group = :shift_group:',
                'bind'       => ['shift_attendance_type' => HrShift::SHIFT_ATTENDANCE_TYPE_FIXED, 'shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT],
                'order'      => 'type asc,start asc',
            ])->toArray();

            foreach ($list as $k => $item) {
                switch ($item['type']) {
                    case 'EARLY':
                        $type_text = static::$t->_('shift_early');
                        break;
                    case 'MIDDLE':
                        $type_text = static::$t->_('shift_middle');
                        break;
                    case 'NIGHT':
                        $type_text = static::$t->_('shift_night');
                        break;
                    default:
                        $type_text = '';
                        break;
                }

                $shift_list[] = [
                    'id'         => $item['id'],
                    'type'       => $item['type'],
                    'start'      => $item['start'],
                    'end'        => $item['end'],
                    'type_text'  => $type_text,
                    'shift_text' => $type_text . ' ' . $item['start'] . '-' . $item['end'],
                ];
            }
            return $shift_list;
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'getSupportStoreShiftList',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
            ]);
            return [];
        }
    }

    /**
     * 根据班次id获取班次信息
     * @param $shift_id
     * @return array
     */
    public function getShiftById($shift_id)
    {
        $shift_detail = [];
        try {
            $info = HrShift::findFirst([
                'conditions' => 'id = :shift_id:',
                'bind'       => [
                    'shift_id' => $shift_id,
                ],
            ]);

            if (!empty($info)) {
                $info = $info->toArray();
                switch ($info['type']) {
                    case 'EARLY':
                        $type_text = static::$t->_('shift_early');
                        break;
                    case 'MIDDLE':
                        $type_text = static::$t->_('shift_middle');
                        break;
                    case 'NIGHT':
                        $type_text = static::$t->_('shift_night');
                        break;
                    default:
                        $type_text = '';
                        break;
                }

                $shift_detail['id']         = $info['id'];
                $shift_detail['type']       = $info['type'];
                $shift_detail['start']      = $info['start'];
                $shift_detail['end']        = $info['end'];
                $shift_detail['type_text']  = $type_text;
                $shift_detail['shift_text'] = $type_text . ' ' . $info['start'] . '-' . $info['end'];
            }
        } catch (Exception $e) {
            $this->logger->error([
                'function' => 'getSupportStoreShiftList',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'params'   => $shift_id,
            ]);
        }
        return $shift_detail;
    }

    /**
     * @description:获取弹性打卡班次信息
     * @param int $shift_id
     * @return array : 秒
     * @author: L.J
     * @time: 2022/11/14 20:46
     */
    public function getFlexibleShiftData($shift_id = 0)
    {
        $shift_data = [];
        if (empty($shift_id)) {
            return $shift_data;
        }
        //获取所有班次
        $flexible_shift_list = $this->getListFromCache(['shift_attendance_type' => 0]);
        $flexible_shift_list = array_column($flexible_shift_list, null, 'id');
        $shift_data          = $flexible_shift_list[$shift_id] ?? [];
        if (empty($shift_data)) {
            return $shift_data;
        }
        //弹性时间- 秒   存在弹性时间 并且 弹性是弹性打卡班次
        $shift_data['current_flexible_time'] = isset($shift_data['shift_attendance_type']) && $shift_data['shift_attendance_type'] == HrShift::SHIFT_ATTENDANCE_TYPE_FLEXIBLE ? ($shift_data['flexible_minute'] * 60) : 0;
        return $shift_data;
    }

    /**
     * @description:根据上班卡和班次id 获取实际打卡偏移量 返回是秒数,按照分钟计算的
     * @return float|int|mixed :
     * @author: L.J
     * @time: 2022/11/14 21:15
     */
    public function getCurrentFlexibleShiftTime(array $params = [])
    {
        $current_flexible_time = 0;
        //上班打卡时间
        $started_at_time = $params['started_at_time'] ?? ''; // 时间戳
        //班次 id
        $shift_id = $params['shift_id'] ?? 0;
        //考勤日期
        $date = $params['date'] ?? '';
        if (empty($started_at_time) || empty($shift_id) || empty($date)) {
            return $current_flexible_time;
        }
        //获取班次弹性时间
        $shift_data = $this->getFlexibleShiftDataFromCache($shift_id);
        if (empty($shift_data)) {
            return $current_flexible_time;
        }
        $shift_current_flexible_time = $shift_data['current_flexible_time'] ?? 0;

        $attendance_start_at          = strtotime($date . ' ' . $shift_data['start']);      //上班班次时间
        $attendance_start_at_flexible = $attendance_start_at + $shift_current_flexible_time; //弹性打卡最晚时间
        //判断是否上班卡迟到 并且在弹性打卡时间内
        if ($started_at_time >= $attendance_start_at + 60 && $started_at_time < $attendance_start_at_flexible + 60) {
            //实际弹性时间 - 秒   = 上班卡时间- 班次时间   ,计算的时候只要分钟数,不要秒
            $current_flexible_time = (floor(($started_at_time - $attendance_start_at) / 60) * 60);
        }
        return (int)$current_flexible_time;
    }


    /**
     * v2版本班次信息
     * @param $shift_id
     * @param $shift_extend_id
     * @return array
     */
    public function getV2ShiftInfo($shift_id, $shift_extend_id = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.id',
            's.shift_name',
            'se.shift_type',
            'se.first_start',
            'se.first_end',
            'se.second_start',
            'se.second_end',
            'se.id as shift_extend_id',
        ]);
        $builder->from(['s' => HrShiftV2Model::class]);
        $builder->leftJoin(HrShiftV2ExtendModel::class, 's.id = se.shift_id', 'se');
        $builder->where('s.id = :id: and  s.is_deleted = :is_deleted:',
            ['id' => $shift_id, 'is_deleted' => 0]);

        if (empty($shift_extend_id)) {
            $builder->andWhere('se.state = :state: ', ['state' => HrShiftV2ExtendModel::STATE_USING]);
        } else {
            $builder->andWhere('se.id = :shift_extend_id: ', ['shift_extend_id' => $shift_extend_id]);
        }
        $result = $builder->getQuery()->execute()->getFirst();

        if (empty($result)) {
            return [];
        }
        $result                    = $result->toArray();
        $result['shift_work_time'] = $result['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE ? $result['first_start'] . '-' . $result['first_end'] . ' ' . $result['second_start'] . '-' . $result['second_end'] : $result['first_start'] . '-' . $result['first_end'];

        return $result;
    }

    /**
     * v2版本班次列表
     * @return mixed
     */
    public function getV2ShiftList()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.id',
            's.shift_name',
            'se.shift_type',
            'se.first_start',
            'se.first_end',
            'se.second_start',
            'se.second_end',
            'se.id as shift_extend_id',
            'se.work_time as shift_duration'

        ]);
        $builder->from(['s' => HrShiftV2Model::class]);
        $builder->leftJoin(HrShiftV2ExtendModel::class, 's.id = se.shift_id', 'se');
        $builder->where('s.is_deleted = :is_deleted: AND se.state = :state: ',
            ['is_deleted' => 0, 'state' => HrShiftV2ExtendModel::STATE_USING]);
        $builder->orderBy('se.first_start asc');
        if (!empty($this->enable_shift_ids)) {
            $builder->andWhere('s.id in ({enable_shift_ids:array})', ['enable_shift_ids' => $this->enable_shift_ids]);
        }

        $result = $builder->getQuery()->execute()->toArray();
        foreach ($result as &$item) {
            $item['shift_work_time'] = $item['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE ? $item['first_start'] . '-' . $item['first_end'] . ' ' . $item['second_start'] . '-' . $item['second_end'] : $item['first_start'] . '-' . $item['first_end'];
        }
        return $result;
    }

    public function shiftListV2($columns = ['id', 'shift_name'])
    {
        return HrShiftV2Model::find(['columns' => $columns])->toArray();
    }


    /**
     * //根据日期list 获取对应的班次信息 打卡 和考勤日历用 如果对应日期 没有数据 有可能是支援 或者 新入职的
     * @param $staffIds
     * @param array $dateList
     * 返回结构字段 date_at start end shift_type shift_id
     * @return array
     */
    public function getShiftInfos(array $staffIds, array $dateList = [])
    {
        if (empty($staffIds)) {
            return [];
        }
        if (empty($dateList)) {
            $dateList[] = date('Y-m-d');
        }
        $today = date('Y-m-d');

        sort($dateList);
        $historyDate = $futureDate = $tmpDate = [];
        foreach ($dateList as $date) {
            //小于今天的日期 history 表
            if ($date < $today) {
                $historyDate[] = $date;
            }

            //大于等于今天 都去middle表
            if ($date >= $today) {
                $tmpDate[] = $date;
            }
        }

        if(!empty($tmpDate)){
            //优先查 新增中间表 当天 和未来的
            $tmpShiftData = HrStaffShiftMiddleDateModel::find([
                'columns'    => "shift_date as date_at,shift_start as start,shift_end as [end],shift_type,shift_id,concat(shift_date,'_',staff_info_id) as u_key",
                'conditions' => 'staff_info_id in ({staff_ids:array}) and shift_date in ({dates:array}) and deleted = 0',
                'bind'       => ['staff_ids' => $staffIds, 'dates' => $tmpDate],
            ])->toArray();
        }
        $tmpShiftData = empty($tmpShiftData) ? [] : array_column($tmpShiftData, null, 'u_key');

        //只用于当天
        $shiftInfoData = HrStaffShiftModel::find([
            'columns'    => 'staff_info_id,start,[end],shift_type,shift_id',
            'conditions' => "staff_info_id in ({staff_ids:array})",
            'bind'       => ['staff_ids' => $staffIds],
        ])->toArray();

        $shiftInfoData = empty($shiftInfoData) ? [] : array_column($shiftInfoData, null, 'staff_info_id');

        //历史日期班次
        if (!empty($historyDate)) {
            $historyShiftData = HrStaffShiftHistoryModel::find([
                'columns'    => "shift_day as date_at,start,[end],shift_type,shift_id,concat(shift_day,'_',staff_info_id) as u_key",
                'conditions' => 'staff_info_id in ({staff_ids:array}) and shift_day in ({dates:array})',
                'bind'       => ['staff_ids' => $staffIds, 'dates' => $historyDate],
            ])->toArray();
            $historyShiftData = empty($historyShiftData) ? [] : array_column($historyShiftData, null, 'u_key');
        }

        //整理班次 加上日期
        $return   = [];
        foreach ($staffIds as $staff) {
            foreach ($dateList as $date) {
                $key                        = "{$date}_{$staff}";
                $return[$key]['shift_info'] = '';
                //昨天 查历史数据 没有就没有了
                if (in_array($date, $historyDate)) {
                    if (empty($historyShiftData[$key])) {
                        continue;
                    }

                    $return[$key] = $this->formatShiftData($historyShiftData[$key]);
                }
                //今天 先中间表 后当前shift 表
                if ($date >= $today) {
                    if (!empty($tmpShiftData[$key])) {
                        $return[$key] = $this->formatShiftData($tmpShiftData[$key]);
                        continue;
                    }
                    //没有临时中间表数据 取当前班次信息
                    if (!empty($shiftInfoData[$staff])) {
                        $shiftInfoData[$staff]['date_at'] = $today;
                        $return[$key]       = $this->formatShiftData($shiftInfoData[$staff]);
                    }
                }

            }
        }
        return $return;
    }

    //整理班次
    protected function formatShiftData($da)
    {
        if (empty($da)) {
            return [];
        }
        $r['start']      = $da['start'];
        $r['end']        = $da['end'];
        $r['shift_id']   = $da['shift_id'];
        $r['shift_type'] = $da['shift_type'];
        $r['shift_info'] = "{$da['start']}-{$da['end']}";
        return $r;
    }


    //如果是仓管 有排班建议字段展示 快递员没有

    /**
     * @throws ValidationException
     */
    public function changeInfo($param, $operator)
    {
        $return['shift_list']   = [];//班次下拉选项
        $return['suggest_info'] = '-';//排班建议数据
        $return['shift_id']     = 0;
        //hrs 班次下拉接口数据
        $p['job_title_id']  = $param['job_title'] ?? 0;
        $p['store_id']      = $param['store_id'] ?? '';
        $p['department_id'] = $param['node_department_id'] ?? 0;

        //主播职位 返回空
        $liveJobId = (new SettingEnvService())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        if (in_array($param['job_title'], $liveJobId)) {
            return $return;
        }

        $staffShiftServer = new StaffShiftService();
        //马来不一样
        $staffShiftServer = reBuildCountryInstance($staffShiftServer);
        $shiftList = $staffShiftServer->getStaffConfigShift($param['node_department_id'], $param['job_title'], $param['store_id']);
        if (empty($shiftList)) {
            return $return;
        }
        foreach ($shiftList as $shiftType => $shift) {
            foreach ($shift as $s) {
                $row['shift_id']        = (int)$s['id'];
                $row['shift_info']      = $s['markup'];
                $row['start']           = $s['start'];
                $row['end']             = $s['end'];
                $return['shift_list'][] = $row;
            }
        }
        //当前员工 当前班次id 回显用
        $dateList = [$param['date_at']];
        if($param['date_at'] > date('Y-m-d')){
            $dateList = DateHelper::DateRange(strtotime(date('Y-m-d')),strtotime($param['date_at']));
        }
        $shiftServer        = reBuildCountryInstance($this);
        $shiftInfo          = $shiftServer->getShiftInfos([$param['staff_info_id']], $dateList);
        $key                = "{$param['date_at']}_{$param['staff_info_id']}";
        $return['shift_id'] = (int)$shiftInfo[$key]['shift_id'] ?? 0;


        //快递员或者其他非仓管职位 直接返回所有班次下拉
        $dcJob = (new SettingEnvService)->getSetVal('shift_dc_job_title');
        $dcJob = empty($dcJob) ? [] : explode(',', $dcJob);
        if (empty($dcJob) || !in_array($param['job_title'], $dcJob)) {
            return $return;
        }
        $staff = HrStaffInfoModel::findFirst([
            'columns'    => 'week_working_day',
            'conditions' => 'staff_info_id = :id:',
            'bind'       => ['id' => $param['staff_info_id']],
        ]);
        if ($staff && $staff->week_working_day != HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
            $this->handleSchedulingSuggestion($param, $operator, $return);
        }
        return $return;
    }

    /**
     * @param $param
     * @param $operator
     * @param $return
     * @return mixed
     * @throws ValidationException
     */
    public function handleSchedulingSuggestion($param, $operator, &$return)
    {
        //又改需求 如果登录人 不是主管 也展示所有 Branch Supervisor or Assistant Branch Supervisor
        $operatorInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'columns'    => 'job_title',
            'bind'       => ['staff_id' => $operator],
        ]);

        if ($operator != 10000 && empty($operatorInfo)) {
            throw new ValidationException('wrong operator');
        }
        $jobTitle  = $operatorInfo->job_title;
        $superFlag = true;
        //如果操作人 不是主管 显示所有班次
        $supervisorJob = (new SettingEnvService)->getSetVal('shift_supervisor_job_title') ?? [];
        $supervisorJob = empty($supervisorJob) ? [] : explode(',', $supervisorJob);
        if (!in_array($jobTitle, $supervisorJob)) {
            $superFlag = false;
        }

        //查询对应网点的 对应职位的 配置信息
        $suggestServer = new SchedulingSuggestionService();
        $suggest       = $suggestServer->getSchedulingSuggest($param['store_id'], $param['date_at'], $param['date_at']);
        $suggest       = $suggest[$param['date_at']] ?? [];
        $this->logger->info(['suggest' => $suggest,'operator' => $operator]);
        //没有配置建议班次
        if (empty($suggest)) {
            return $return;
        }

        //整理 去掉 不符合条件的班次 只留前后一小时的
        $suggestStr  = '';
        $formatShift = [];
        $existDcData = false;
        $emptyDate   = false;//是否有排班建议日期
        foreach ($suggest as $s) {
            if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_COURIER) {
                continue;
            }
            if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER) {
                $existDcData = true;
            }
            if(empty($s['shift_date'])){
                $emptyDate = true;
            }
            foreach ($return['shift_list'] as $k => $r) {
                $flag = $this->betweenOneHour($s['shift_date'], $r['start']);
                //留下符合条件的班次 剔除不符合条件的
                if ($flag) {
                    $formatShift[] = $r;
                }
            }
            //整理排班建议
            if (!empty($s['shift_date'])) {
                $suggestStr .= self::$t->_('more_less_1_hour', ['time' => $s['shift_date']]) . '<br/>';
            }
        }
        //如果是主管 替换成 经过筛选之后的符合建议的班次
        if ($superFlag && $existDcData && !$emptyDate) {
            $return['shift_list'] = $formatShift;
        }

        //排版建议 只有主管有信息 快递员返回空
        $return['suggest_info'] = empty($suggestStr) ? '-' : $suggestStr;
    }
    /**
     * 获取不限制的部门
     * @return array
     */
    public function getNoLimitedDepartmentIds(): array
    {
        $result                      = [];
        $shift_no_limited_department = (new SettingEnvService())->getSetVal('shift_no_limited_department', ',');
        if (!empty($shift_no_limited_department)) {
            $departmentService = new SysDepartmentService();
            foreach ($shift_no_limited_department as $item) {
                $dep_ids = $departmentService->getDepartmentAndSubDepartmentIds($item);
                $result  = array_merge($result, $dep_ids);
            }
        }
        return $result;
    }

    /**
     * 修改某一天的班次 调hris 接口 只能修改 未来日期
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function changeShift($params)
    {
        //新增逻辑验证 https://flashexpress.feishu.cn/docx/C6SidNLIIoA3OAxXsiKc2fEsnQf 只有菲律宾
        $workdayServer = new WorkdayService();
        $workdayServer = reBuildCountryInstance($workdayServer);

        $staff = HrStaffInfoModel::findFirst([
            'columns'    => 'staff_info_id, week_working_day, rest_type, formal',
            'conditions' => 'staff_info_id = :id:',
            'bind'       => ['id' => $params['staff_info_id']],
        ]);
        if(empty($staff)){
            throw new ValidationException('wrong staff');
        }
        //非外协验证 trunk
        if($staff->formal != HrStaffInfoModel::FORMAL_0){
            $flag = $workdayServer->checkTruckUpdatePermission([$params['staff_info_id']], $params['operator_id']);
            if(!$flag){
                $key = empty($params['is_import']) ? 'shift_truck_permission' : 'work_shift_truck_permission';
                throw new ValidationException(self::$t->_($key));
            }
        }
        if($params['operator_id'] != 10000 && $params['operator_id'] == $params['staff_info_id']){
            //新增规则配置校验 能否修改自己的休息日
            $ruleParam['staff_info_id'] = $params['operator_id'];
            $ruleParam['rule_key']      = 'Allow_set_selfShift';
            $rules                      = $this->getConditionRule(self::$language, $ruleParam);
            //- N or 空：不允许修改自己，给出如下提示
            if (empty($rules['data']) || $rules['data']['response_type'] != ConditionsRulesEnums::RESPONSE_TYPE_VALUE || !$rules['data']['response_data']) {
                throw new ValidationException(self::$t->_('can_not_edit_self_shift'));
            }
        }

        //如果是仓管 要验证修改的班次 是不是从建议班次中 减少人员 提示不让修改
        $settingEnvService = new SettingEnvService();
        $configList = $settingEnvService->listByCode(['shift_dc_job_title','modify_today_shift','shift_no_limited_branch','shift_no_limited_type','hub_store_work_partition_store_ids']);
        $configList = array_column($configList,'set_val','code');
        $configList['shift_no_limited_department'] = $this->getNoLimitedDepartmentIds();

        $dcJob                    = $configList['shift_dc_job_title'];
        $modifyTodayShift         = $configList['modify_today_shift'];
        $dcJob = empty($dcJob) ? [] : explode(',', $dcJob);
        if ($params['date_at'] == date('Y-m-d') && $params['change_type'] != ShiftEnums::EFFECT_CURRENT) {
            throw new ValidationException(static::$t->_('modify_today_shift_no_1'));
        }
        $this->modifyTodayShift = false;
        if (empty($modifyTodayShift) && $params['date_at'] == date('Y-m-d')) {
            throw new ValidationException(static::$t->_('modify_today_shift_no'));//!!超出权限范围
        }
        //验证是否存在支援
        $supportParam['start'] = $supportParam['end'] = $params['date_at'];
        $supportParam['staff_info_id'] = $params['staff_info_id'];
        if($params['change_type'] == ShiftEnums::EFFECT_FUTURE){
            $supportParam['end'] = date('Y-m-t',strtotime('+1 month'));
        }
        $count = $this->checkSupport($supportParam);
        if($count > 0){
            throw new ValidationException(static::$t->_('support_info_exist'));
        }

        if ($modifyTodayShift) {
            $modifyTodayShiftStaffIds = explode(',', $modifyTodayShift);
            if (!in_array($params['operator_id'], $modifyTodayShiftStaffIds) && $params['date_at'] == date('Y-m-d')) {
                throw new ValidationException(static::$t->_('modify_today_shift_no'));
            }
            //指定操作人修改当天班次
            if (in_array($params['operator_id'],
                    $modifyTodayShiftStaffIds) && $params['date_at'] == date('Y-m-d') && $params['change_type'] == ShiftEnums::EFFECT_CURRENT) {
                $firstWork = StaffWorkAttendanceModel::findFirst([
                    'conditions' => 'attendance_date=:attendance_date: and staff_info_id=:staff_info_id:',
                    'bind'       => [
                        'attendance_date' => $params['date_at'],
                        'staff_info_id'   => $params['staff_info_id'],
                    ],
                ]);
                $businessWork = StaffAuditReissueForBusinessModel::findFirst([
                    'conditions' => 'attendance_date=:attendance_date: and staff_info_id=:staff_info_id:',
                    'bind'       => [
                        'attendance_date' => $params['date_at'],
                        'staff_info_id'   => $params['staff_info_id'],
                    ],
                ]);
                if ($firstWork || $businessWork) {
                    throw new ValidationException(static::$t->_('modify_today_shift_error_3'));
                }
               $this->modifyTodayShift = $params['modify_today_shift'] = true;
            }
        }
        $staffShiftServer = new StaffShiftService();
        $staffShiftServer = reBuildCountryInstance($staffShiftServer);

        $staffShiftServer->settingEnvConfig = $configList;

        //初始化班次日期
        [$params['startDate'],$params['endDate']] = $staffShiftServer->initShiftDateList($params);
        //自由轮休和外协 不验证
        if (!empty($dcJob) && in_array($params['job_title'], $dcJob)
            && $staff->week_working_day != HrStaffInfoModel::WEEK_WORKING_DAY_FREE
            && $staff->formal != HrStaffInfoModel::FORMAL_0
        ) {
            /**
             * @see StaffShiftService::checkChangeShift()
             */
            $staffShiftServer->checkChangeShift($params);
        }

        /**
         * @see StaffShiftService::shiftSavePreset()
         */
        //改预设
        if($params['change_type'] == ShiftEnums::EFFECT_FUTURE){
            $staffShiftServer->shiftSavePreset($params);
        }
        /**
         * @see StaffShiftService::shiftSaveMiddle()
         */
        //改中间表
        if($params['change_type'] == ShiftEnums::EFFECT_CURRENT){
            $staffShiftServer->shiftSaveMiddle($params);
        }

        return true;
    }


    //判断 班次选项是否在建议班次的 前后一小时
    public function betweenOneHour($suggestStart, $shiftStart, $hour = 1)
    {
        if(empty($suggestStart)){
            return false;
        }
        $date    = date('Y-m-d');
        $suggest = strtotime("{$date} {$suggestStart}");

        $start = $suggest - ($hour * 3600);
        $end   = $suggest + ($hour * 3600);

        $tmp = strtotime("{$date} {$shiftStart}");

        if ($tmp >= $start && $tmp <= $end) {
            return true;
        }
        //加减一天
        $start = $suggest - ($hour * 3600) + (24 * 3600);
        $end = $suggest + ($hour * 3600) + (24 * 3600);
        if($tmp >= $start && $tmp <= $end){
            return true;
        }

        $start = $suggest - ($hour * 3600) - (24 * 3600);
        $end = $suggest + ($hour * 3600) - (24 * 3600);
        if($tmp >= $start && $tmp <= $end){
            return true;
        }

        return false;
    }

    //校验是否存在支援
    public function checkSupport($param){
        $start = $param['start'];
        $end = $param['end'];
        //有支援不能修改
        $supportServer = new StaffSupportStoreServer();

        $count = $supportServer->validateConflict($start, $end, $param['staff_info_id']);

        return $count;
    }


}