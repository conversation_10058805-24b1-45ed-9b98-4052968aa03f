<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MessageEnums;
use App\Library\Enums\OrganizationEnums;
use App\Library\Enums\StaffEnums;
use App\Library\ErrCode;
use App\Models\backyard\BankListModel;
use App\Models\backyard\CeoMailFollowModel;
use App\Models\backyard\DictionaryTypeModel;
use app\models\backyard\FrontlineSpecialNonCarriageModel;
use App\Models\backyard\HireTypeChangeInfoModel;
use App\Models\backyard\HireTypeImportListModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrDriverCrowdsourcingModel;
use App\Models\backyard\HrJobDepartmentRelationModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrOrganizationDepartmentPieceRelationModel;
use App\Models\backyard\HrProbationTargetModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffInfoExtendModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStoreSubsidiesCategoryModel;
use App\Models\backyard\LeaveQuitclaimInfoModel;
use App\Models\backyard\MessageWarningModel;
use App\Models\backyard\OvertimeHourSettingModel;
use app\models\backyard\RoleEnumsModel;
use App\Models\backyard\OsStaffInfoExtendModel;
use App\Models\backyard\SalaryBaseSettingModel;
use App\Models\backyard\SalaryItemsAdjustmentRecordModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\StaffInfoIntlModel;
use App\Models\backyard\StaffOtherStaffRelationModel;
use App\Models\backyard\StaffPayrollCompanyInfoModel;
use App\Models\backyard\StaffPayrollModel;
use App\Models\backyard\StaffSalaryItemsModel;
use App\Models\backyard\SysAttachmentModel;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysDistrictModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\VanContainerModel;
use App\Models\backyard\HrJobTitleRoleModel;
use App\Library\Validation\ValidationException;

use App\Models\backyard\WorkdaySettingDailyModel;
use App\Repository\StaffPayrollCompanyInfoRepository;
use App\Repository\SysProvinceRepository;

/**
 * @method getJobTitleListFromCache()
 */
class SysService extends BaseService
{

    /**
     * 验证国家是否有子账号
     * @return bool
     */
    public function checkCountryIsHasSubStaff(): bool
    {
        if (isCountry(['MY', 'TH'])) {
            return true;
        }
        return false;
    }

    /**
     * 获取公司配置
     * @return array
     */
    public function contractCompanyList(): array
    {
        $list = StaffPayrollCompanyInfoModel::find([
            'columns' => 'company_id,company_short_name',
        ])->toArray();
        $data = [];
        foreach ($list as $value) {
            $data[] = ['value' => strval($value['company_id']), 'label' => $value['company_short_name']];
        }
        return $data;

    }

    public function getHireTypeList($dataType = Enums::DATA_TYPE_STRING): array
    {
        $service     = new SettingEnvService();
        $config_list = $service->getSetVal('hire_type_enum', ',') ?: ['1', '2', '3', '4', '5'];
        $list        = [];
        foreach ($config_list as $item) {
            $list[] = [
                'value' => $dataType == Enums::DATA_TYPE_STRING ? strval($item) : intval($item),
                'label' => self::$t->_('hire_type_' . $item),
            ];
        }
        return $list;
    }

    public function getOsHireTypeList($dataType = Enums::DATA_TYPE_STRING): array
    {
        $config_list = ['11','12','15'];
        $list        = [];
        foreach ($config_list as $item) {
            $list[] = [
                'value' => $dataType == Enums::DATA_TYPE_STRING ? strval($item) : intval($item),
                'label' => self::$t->_('hire_type_' . $item),
            ];
        }
        return $list;
    }


    public function setCurrentStaffId($staff_info_id): SysService
    {
        $this->current_staff_id = $staff_info_id;
        return $this;
    }

    public $current_staff_id = 0;

    public function sysList($list_code)
    {
        $data = [];
        foreach ($list_code as $key => $value) {
            $state = method_exists($this, $value);
            if ($state) {
                $data[$value] = $this->$value();
            }
        }
        return $data;
    }

    // 税卡号
    public function taxAuditState(): array
    {
        return $this->bankCardAuditState();
    }

    // 医疗保险卡号审核状态
    public function medicalInsuranceAuditState(): array
    {
        return $this->bankCardAuditState();
    }

    // 公积金审核状态
    public function fundAuditState(): array
    {
        return $this->bankCardAuditState();
    }

    // 社保卡审核状态
    public function socialSecurityAuditState(): array
    {
        return $this->bankCardAuditState();
    }

    /**
     * 判断当前工号是否可以无限制的编辑员工轮休表
     * @return bool
     */
    public function isWorkDayRootId()
    {
        $service = new WorkdayService();
        return $service->isProductAdmin($this->current_staff_id);
    }

    /**
     * 判断当前工号是否可以修改六天班轮休员工当天休息日
     * @return bool
     */
    public function canModifyTodayRest(): bool
    {
        $set_val = (new SettingEnvService())->getSetVal('modify_today_rest');
        if (empty($set_val)) {
            return false;
        }
        return in_array($this->current_staff_id, explode(',', $set_val));
    }

    /**
     * 判断当前工号是否可以修改六天班轮休员工当天班次
     * @return bool
     */
    public function canModifyTodayShift(): bool
    {
        $set_val = (new SettingEnvService())->getSetVal('modify_today_shift');
        if (empty($set_val)) {
            return false;
        }
        return in_array($this->current_staff_id, explode(',', $set_val));
    }

    // ph 备用银行卡号审核状态
    public function backupbankCardAuditState(): array
    {
        return $this->bankCardAuditState();
    }

    // th 银行卡审核状态
    public function bankCardAuditState(): array
    {
        $data = [];
        // Enums::$hris_bank_state
        foreach (HrStaffAnnexInfoModel::$audit_state_key as $key => $val) {
            $data[] = [
                'value' => $key,
                'label' => self::$t->_($val),
            ];
        }
        return $data;
    }

    /**
     * 获取入职类型
     * @return array
     */
    public function entryState(): array
    {
        $data = [];
        foreach (HireTypeImportListModel::$status_list as $key => $val) {
            $data[] = [
                'value' => $key,
                'label' => self::$t->_($val),
            ];
        }
        return $data;
    }

    public function storeCategory()
    {
        $data = [];
        foreach (Enums::$store_category as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    //工作所在国家list
    public function WorkingCountryList()
    {
        return (new DictionaryService())->getDictionItemsListByCode('working_country',
            DictionaryTypeModel::DATA_TYPE_INT);
    }

    //国籍list
    public function NationalityList()
    {
        return (new DictionaryService())->getDictionItemsListByCode('nationality_region',
            DictionaryTypeModel::DATA_TYPE_INT);
    }

    public function addressCountryList()
    {
        return (new DictionaryService())->getDictionItemsListByCode('address_country_region',
            DictionaryTypeModel::DATA_TYPE_INT);
    }

    //外协公司
    public function outsource_company()
    {
        $osCompanyList  = (new DictionaryService())->getDictionItemsListByCode('outsource_company',
            DictionaryTypeModel::DATA_TYPE_INT);
        $hubCompanyInfo = (new HubOutSourcingStaffService())->companyInfo();
        $hubCompanyList = !empty($hubCompanyInfo) ? $hubCompanyInfo['outsourcing_company'] : [];
        return array_merge($osCompanyList, $hubCompanyList);
    }

    //获取外协公司rpc
    public function outsource_company_list($locale, $params)
    {
        self::setLanguage($locale['locale']);

        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $this->outsource_company()];
    }

    //外协员工 是否完善 户口地址信息
    public function is_complete_address()
    {
        $result = [];
        foreach (OsStaffInfoExtendModel::$isCompleteAddress as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }


    /**
     * 员工雇佣类型
     * @return array
     */
    public function HrisHireTypeList()
    {
        return $this->getHireTypeList(Enums::DATA_TYPE_INT);
    }

    /**
     * 员工雇佣类型
     * @return array
     */
    public function os_outsourcing_hire_type_list()
    {
        $config_list = ['11', '12', '15'];
        $list        = [];
        foreach ($config_list as $item) {
            $list[] = [
                'value' => strval($item),
                'label' => self::$t->_('hire_type_' . $item),
            ];
        }
        return $list;
    }

    /**
     * 编制类型
     * @return array
     */
    public function allHireTypeList()
    {
        $result          = [];
        $allHireTypeList = HrStaffInfoModel::$allHireTypeList;
        foreach ($allHireTypeList as $item) {
            $oneData['value'] = strval($item);
            $oneData['label'] = static::$t->_('hire_type_' . $item);
            $result[]         = $oneData;
        }
        return $result;
    }


    /**
     * 编制类型
     * @return array
     */
    public function staffFormalList()
    {
        $result          = [];
        $staffFormalList = HrStaffInfoModel::$staffFormalList;
        foreach ($staffFormalList as $item) {
            $oneData['value'] = strval($item);
            $oneData['label'] = static::$t->_('formal_' . $item);
            $result[]         = $oneData;
        }
        return $result;
    }

    /**
     * 员工班次设置员工类型
     * @return array
     */
    public function staffType()
    {
        $data = [];

        $staffTypeList = isCountry('MY') ? Enums::$hris_staff_type_my : Enums::$hris_staff_type_common;
        foreach ($staffTypeList as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 员工在职状态
     * @return array
     */
    public function HrisWorkingState()
    {
        $data = [];
        foreach (Enums::$hris_working_state as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 员工全部的在职状态
     * @return array
     */
    public function HrisAllWorkingState()
    {
        $data = [];
        foreach (Enums::$hris_all_working_state as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }
    /**
     * 只有外协展示的在职状态
     * @return array
     */
    public function osWorkingState()
    {
        $data = [];
        foreach (Enums::$hris_all_working_state as $key => $value) {
            if(!in_array($key, [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_RESIGN])){
                continue;
            }
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }


    /**
     * 员工身份证审核状态
     * @return array
     */
    public function HrisIdentityAuditState()
    {
        $data = [];
        foreach (Enums::$hris_identity_state as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 员工户口簿审核状态
     * @return array
     */
    public function HrisResidenceBookletAuditState()
    {
        $data = [];
        foreach (Enums::$hris_identity_state as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 职级
     * @return array
     */
    public function JobTitleGrade()
    {
        $data = [];
        foreach (Enums::$job_level as $key => $value) {
            $data[] = ['value' => $value['id'], 'label' => $value['name']];
        }
        return $data;
    }

    /**
     * 职级调整原因
     * @return array
     */
    public function JobTitleGradeReason(): array
    {
        return HrJobTitleGradeService::getInstance()->getChangeGradeReason();
    }

    //OT类型
    public function getOtType()
    {
        $data = [];
        $lang = self::$language;
        $res  = (new AttendanceToolService())->getApiDatass('by', '', 'getApprovalOtType', $lang, []);
        if ($res) {
            foreach ($res['data'] as $k => $v) {
                $data[] = ['value' => intval($v['code']), 'label' => $v['msg']];
            }
        }
        return $data ?? [];
    }
    public function effectiveEnum(){
        $data = [];
        foreach (OvertimeHourSettingModel::$effective_type as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    //外协OT类型
    public function getOsOtType()
    {
        $data = [];
        $lang = self::$language;
        $res  = (new AttendanceToolService())->getApiDatass('by', '', 'getOsOtType', $lang, []);
        if ($res) {
            foreach ($res['data'] as $k => $v) {
                $data[] = ['value' => intval($v['code']), 'label' => $v['msg']];
            }
        }
        return $data ?? [];
    }

    //时长
    public function selectTime()
    {
        $data = [];
        foreach (Enums::$select_time as $key => $value) {
            $data[] = ['value' => $key, 'label' => $value];
        }
        return $data;
    }

    //打卡类型
    public function clockType()
    {
        $data = [];
        foreach (Enums::$clock_type as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    //Quitclaim处理进度
    public function quitclaimFixType()
    {
        $data = [];
        foreach (LeaveQuitclaimInfoModel::STATUS_LIST as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    //请假类型 实时类型 工具给员工申请用
    public function leaveType()
    {
        $data = [];
        $lang = self::$language;
        $res  = (new AttendanceToolService())->getApiDatass('by', '', 'leaveTypeBook', $lang, []);
        if ($res) {
            foreach ($res['data'] as $key => $value) {
                $row = ['value' => $value['code'], 'label' => $value['msg']];
                if (!empty($value['template_type'])) {
                    $row['template_type'] = $value['template_type'];
                }
                if (!empty($value['need_img'])) {
                    $row['need_img'] = $value['need_img'];
                }
                $data[] = $row;
            }
            $sort_key = array_column($data, 'value');//var_dump($data);exit;
            array_multisort($sort_key, SORT_ASC, $data);
        }
        return $data;
    }

    //历史所有类型菜单 列表页搜索用
    public function leaveTypeAll()
    {
        $data = [];
        $lang = self::$language;
        $res  = (new AttendanceToolService())->getApiDatass('by', '', 'leaveTypeBookAll', $lang, []);
        if ($res) {
            foreach ($res['data'] as $key => $value) {
                $row = ['value' => $value['code'], 'label' => $value['msg']];
                if (!empty($value['template_type'])) {
                    $row['template_type'] = $value['template_type'];
                }
                if (!empty($value['need_img'])) {
                    $row['need_img'] = $value['need_img'];
                }
                $data[] = $row;
            }
            $sort_key = array_column($data, 'value');//var_dump($data);exit;
            array_multisort($sort_key, SORT_ASC, $data);
        }
        return $data;
    }

    public function sickCertificateStatus()
    {
        $data = [
            ['value' => StaffAuditModel::SICK_SUB_STATUS_NO, 'label' => self::$t->_('not_commit')],
            ['value' => StaffAuditModel::SICK_SUB_STATUS_YES, 'label' => self::$t->_('commit')],
        ];

        return $data;
    }


    //时间区间
    public function timeInterval()
    {
        $data = [];
        foreach (Enums::$time_interval as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 操作类型
     * @return array
     */
    public function OperationType()
    {
        $data = [];
        foreach (Enums::$operation_type as $key => $value) {
            //只有菲律宾有全勤奖
            if ($key == Enums::operation_type_full_press && !isCountry('PH')) {
                continue;
            }
            //目前只有泰国有 出差
            if($key == Enums::operation_type_bt && !isCountry('TH')){
                continue;
            }
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }


    /**
     * 大区列表
     */
    public function ManageRegionList()
    {
        $data = SysManageRegionModel::find([
            'columns'    => 'id,name',
            'conditions' => 'deleted = 0',
        ])->toArray();
        return $data;
    }

    /**
     * 片区列表
     */
    function ManagePieceList()
    {
        $data = SysManagePieceModel::find([
            'columns'    => 'id,name,manage_region_id',
            'conditions' => 'deleted = 0',
        ])->toArray();
        return $data;
    }


    /**
     * 大区
     * @return mixed
     */
    public function getRegionList()
    {
        return SysManageRegionModel::find([
            'columns' => 'id,name',
            'order'   => 'substring(name,1,2), substring(name,5)*1,substring(name,6,1),substring(name,9)*1',
            'conditions' => 'deleted=0',
        ])->toArray();
    }

    /**
     * 片区
     * @return mixed
     */
    public function getPieceList()
    {
        return SysManagePieceModel::find([
            'columns' => 'id,name',
            'order'   => 'substring(name,1,2), substring(name,4)*1,substring(name,3)*1,substring(name,2)*1',
            'conditions' => 'deleted=0',
        ])->toArray();
    }

    /**
     * 网点
     * @param string $columns
     * @param bool $haveHead
     * @return \string[][]
     */
    public function getStoreList($columns = 'id,name,manage_region,manage_piece,category', $haveHead = true)
    {
        $store = SysStoreModel::find(['columns' => $columns])->toArray();
        if ($haveHead) {
            return array_merge([
                [
                    'id'            => '-1',
                    'name'          => GlobalEnums::HEAD_OFFICE,
                    'province_code' => '',
                    'manage_region' => '',
                    'manage_piece'  => '',
                    'category'      => 0,
                ],
            ], $store);
        }
        return $store;
    }

    /**
     * 部门
     * @return mixed
     */
    public function getDepartmentList()
    {
        return SysDepartmentModel::find(['columns'=>'id,name,deleted'])->toArray();
    }

    /**
     * 职位列表-all
     * @Token
     * @return array
     */
    public function getJobTitleList()
    {
        return (new HrJobTitleService())->getJobTitleList();
    }

    /**
     * 银行类型列表-all
     * @Token
     * @return array
     */
    public function getBankTypeList()
    {
        return BankListModel::find([
            'columns'    => "bank_id,bank_name",
        ])->toArray();
    }


    //区域
    public function getProvinceList($columns = 'id,code,name')
    {
        return SysProvinceModel::find(['columns' => $columns])->toArray();
    }

    /**
     * 省 详情
     * @param $province_code
     * @return array
     */
    public function getProvinceDetail($province_code): array
    {
        $province = SysProvinceModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $province_code],
        ]);

        return !empty($province) ? $province->toArray() : [];
    }

    //地址 市
    public function getCityList($columns = 'id,name')
    {
        return SysCityModel::find(['columns' => $columns])->toArray();
    }

    /**
     * 市 详情
     * @param $city_code
     * @return array
     */
    public function getCityDetail($city_code): array
    {
        $city = SysCityModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $city_code],
        ]);
        return !empty($city) ? $city->toArray() : [];
    }

    //地址 区
    public function getDistrictList($columns = 'id,name')
    {
        return SysDistrictModel::find(['columns' => $columns])->toArray();
    }

    /**
     * 区 详情
     * @param $district_code
     * @return array
     */
    public function getDistrictDetail($district_code): array
    {
        $district = SysDistrictModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $district_code],
        ]);
        return !empty($district) ? $district->toArray() : [];
    }

    //省市 联动 key  value  children 用缓存调用!
    public function getProvinceCitySelect()
    {
        $provinceData = SysProvinceModel::find([
            'columns'    => 'code,name,en_name',
            'conditions' => 'deleted = 0',
        ])->toArray();

        if (empty($provinceData)) {
            return [];
        }
        $changeLang = false;//是否切英文 但是 英文是空 还切name
        if(self::$language != 'th'){
            $changeLang = true;
        }

        $res = [];
        foreach ($provinceData as $p) {
            $row            = [];
            $row['value']     = $p['code'];
            $row['label']   = $changeLang ? (empty($p['en_name']) ? $p['name'] : $p['en_name']) : $p['name'];
            $cityData       = SysCityModel::find([
                'columns'    => 'code,name,en_name',
                'conditions' => 'deleted = 0 and province_code = :province_code:',
                'bind'       => ['province_code' => $p['code']],
            ])->toArray();
            foreach ($cityData as $c) {
                $item['value']       = $c['code'];
                $item['label']     = $changeLang ? (empty($c['en_name']) ? $c['name'] : $c['en_name']) : $c['name'];
                $row['children'][] = $item;
            }
            $res[] = $row;
        }
        return $res;
    }

    //根据 city code 获取 信息和 省信息
    public function getProvinceCityInfo($cityCodes){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('c.code c_code,c.name c_name, c.en_name c_en_name,p.code p_code, p.name p_name, p.en_name p_en_name');
        $builder->from(['c' => SysCityModel::class]);
        $builder->leftjoin(SysProvinceModel::class, 'c.province_code = p.code', 'p');
        $builder->inWhere('c.code', $cityCodes);
        return $builder->getQuery()->execute()->toArray();
    }

    public function getDepartmentAndNodeDepartmentIdByDepartmentId($department_id): array
    {
        $current_department = SysDepartmentModel::findFirst($department_id);
        $departments        = SysDepartmentModel::find([
            "conditions" => "( ancestry_v3 like :ancestry:  ) and deleted = 0 ",
            "bind"       => ["ancestry" => $current_department->ancestry_v3 . '/%'],
            'columns'    => 'id',
        ])->toArray();
        $departments[]      = ['id' => $department_id];
        return array_column($departments, 'id');
    }


    /**
     * 网点类型 迁移自bi
     * @return array
     */
    public function storeKind()
    {
        $data               = [];
        $store_category_map = SysStoreService::$category;
        foreach ($store_category_map as $k => $v) {
            $data[] = ['value' => $k, 'label' => self::$t->_($v)];
        }
        return $data;
    }

    /**
     * 网点所在区域 迁移自bi
     * @return array
     */
    public function storeGeographyCode()
    {
        $data = [];
        foreach (GlobalEnums::$areas as $k => $v) {
            $data[] = ['value' => $k, 'label' => $v];
        }
        return $data;
    }

    public function allStore()
    {
        $data = [];
        foreach ($this->getStoreListFromCache() as $item) {
            $data[] = ['value' => $item['id'], 'label' => $item['name']];
        }
        return $data;
    }

    /**
     * 拼接总部的网点
     * @return array
     */
    public function allStoreWithHeader()
    {
        $data = [];
//        $data[] = ['value' => -1, 'label' => GlobalEnums::HEAD_OFFICE];
        foreach ($this->getStoreListFromCache() as $item) {
            $data[] = ['value' => $item['id'], 'label' => $item['name']];
        }
        return $data;
    }

    /**
     * 大区
     * @return array
     */
    public function allRegion()
    {
        $data = [];
        foreach ($this->getRegionListFromCache() as $item) {
            $data[] = ['value' => $item['id'], 'label' => $item['name']];
        }
        return $data;
    }

    /**
     * 片区
     * @return array
     */
    public function allPiece()
    {
        $data = [];
        foreach ($this->getPieceListFromCache() as $item) {
            $data[] = ['value' => $item['id'], 'label' => $item['name']];
        }
        return $data;
    }

    /**
     * 审批状态
     * @return array
     */
    public function AuditStatus()
    {
        $data = [];
        foreach (Enums::$audit_status as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }

        return $data;
    }

    //

    /**
     * 处理资产状态常量
     * @return array
     */
    public function assetState()
    {
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_UNPROCESSED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_UNPROCESSED),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_PROCESSING,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_PROCESSING),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_PROCESSED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_PROCESSED),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_UN_DEDUCTED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_UN_DEDUCTED),
        ];
        return $data;
    }

    /**
     * 处理钱款状态常量
     * @return array
     */
    public function moneyState()
    {
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_UNPROCESSED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_UNPROCESSED),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_PROCESSING,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_PROCESSING),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_PROCESSED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_PROCESSED),
        ];
        return $data;
    }

    /**
     * 处理进度状态常量
     * @return array
     */
    public function remandState()
    {
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_UNPROCESSED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_UNPROCESSED),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_PROCESSING,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_PROCESSING),
        ];
        $data[] = [
            'value' => LeaveManagerService::ASSETS_STATE_PROCESSED,
            'label' => self::$t->_('assets_state_' . LeaveManagerService::ASSETS_STATE_PROCESSED),
        ];
        return $data;
    }

    /**
     * 离职状态
     * @return array
     */
    public function leaveState()
    {
        //$data[] = ['value' => 0, 'label' => 'All'];
        //$data[] = ['value' => 1, 'label' => self::$t->_('staff_state_1')];
        $data[] = ['value' => Enums::HRIS_WORKING_STATE_2, 'label' => self::$t->_('staff_state_2')];
        $data[] = ['value' => Enums::HRIS_WORKING_STATE_4, 'label' => self::$t->_('wait_leave_state_1')];
        return $data;
    }

    /**
     * 离职审批状态
     * @return array
     */
    public function approveState()
    {
        $data[] = [
            'value' => LeaveManagerService::APPROVAL_STATUS_WAIT,
            'label' => self::$t->_('by_state_' . LeaveManagerService::APPROVAL_STATUS_WAIT),
        ];
        $data[] = [
            'value' => LeaveManagerService::APPROVAL_STATUS_APPROVED,
            'label' => self::$t->_('by_state_' . LeaveManagerService::APPROVAL_STATUS_APPROVED),
        ];
        $data[] = [
            'value' => LeaveManagerService::APPROVAL_STATUS_DISMISSED,
            'label' => self::$t->_('by_state_' . LeaveManagerService::APPROVAL_STATUS_DISMISSED),
        ];
        $data[] = [
            'value' => LeaveManagerService::APPROVAL_STATUS_REVOKED,
            'label' => self::$t->_('by_state_' . LeaveManagerService::APPROVAL_STATUS_REVOKED),
        ];
        $data[] = [
            'value' => LeaveManagerService::APPROVAL_STATUS_TIME_OUT,
            'label' => self::$t->_('by_state_' . LeaveManagerService::APPROVAL_STATUS_TIME_OUT),
        ];
        return $data;
    }

    /**
     * 获取职位-更具OA关联体系获取
     * @param $params
     * @return array
     */
    public function searchJobTitleListRelation($params): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['relation' => HrJobDepartmentRelationModel::class]);
        $builder->columns('relation.job_id as job_id,job_title.job_name as job_name');
        $builder->join(HrJobTitleModel::class, 'relation.job_id = job_title.id', 'job_title');
        $builder->andWhere('relation.department_id = :department_id:', ['department_id' => $params['department_id']]);

        if (!empty($params['search_name'])) {
            $builder->andWhere('job_title.job_name LIKE :search_name:',
                ['search_name' => "%{$params['search_name']}%"]);
        }

        $builder->andWhere('job_title.status = :status:', ['status' => HrJobTitleModel::STATUS_1]);
        $builder->limit(20);
        $builder->orderBy('job_title.id ASC');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取职位-更具OA关联体系获取
     * @param $params
     * @return array
     */
    public function searchDepartmentRelationList($params): array
    {
        $returnData = [
            'JobTitleGrade'         => [],
            'getWorkingDayRestType' => [],
        ];

        $relationInfo = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind'       => [
                'department_id' => $params['department_id'],
                'job_id'        => $params['job_id'],
            ],
            'order'      => 'id DESC',
        ]);

        if (empty($relationInfo)) {
            return $returnData;
        }

        $relationInfo = $relationInfo->toArray();

        $jobTitleGrade = explode(',', $relationInfo['job_level'] ?? '');

        foreach (Enums::$job_level as $value) {
            if (in_array($value['id'], $jobTitleGrade)) {
                $returnData['JobTitleGrade'][] = ['value' => $value['id'], 'label' => $value['name']];
            }
        }

        $workingDay = explode(',', $relationInfo['working_day_rest_type'] ?? '');

        foreach (Enums::$working_day_rest_type as $key => $value) {
            if (in_array($key, $workingDay)) {
                $returnData['getWorkingDayRestType'][] = ['value' => $key, 'label' => self::$t->_($value)];
            }
        }

        return $returnData;
    }

    /**
     * 获取职位-更具OA关联体系获取
     * @param $params
     * @return array
     */
    public function rolesListRelation($params): array
    {
        $rolesList = HrJobTitleRoleModel::find([
            'columns'    => 'role_id',
            'conditions' => 'sys_depeartment_id = :department_id: and job_title_id = :job_title_id:',
            'bind'       => [
                'department_id' => $params['department_id'],
                'job_title_id'  => $params['job_id'],
            ],
        ])->toArray();

        if (empty($rolesList)) {
            return [];
        }

        //总部
        if ($params['store_id'] == '-1') {
            $type = RoleEnumsModel::TYPE_HEAD;
        } else {
            $type = RoleEnumsModel::TYPE_NETWORK;
        }

        $roleSelectList = RoleEnumsModel::find([
            'conditions' => 'type = :type:',
            'bind'       => ['type' => $type],
        ])->toArray();

        $roleSelectList = array_column($roleSelectList, 'role_id');

        $returnData = [];
        foreach ($rolesList as $role) {
            if (in_array($role['role_id'], $roleSelectList)) {
                $returnData[$role['role_id']] = [
                    'value' => $role['role_id'],
                    'name'  => self::$t->_('role_' . $role['role_id']),
                ];
            }
        }

        return $returnData;
    }


    /**
     * 离职来源状态
     * @return array
     */
    public function sourceState()
    {
        $data   = [];
        $data[] = ['value' => 5, 'label' => self::$t->_('font_leave_source_5')];
        $data[] = ['value' => 3, 'label' => self::$t->_('stay_away_from_work')]; //由'三天未出勤停职员工'=>'连续旷工'
        $data[] = ['value' => 6, 'label' => self::$t->_('font_leave_source_6')];
        $data[] = ['value' => 4, 'label' => self::$t->_('font_leave_source_4')];
        if (isCountry('TH')) {
            $data[] = ['value' => 7, 'label' => self::$t->_('font_leave_source_7')];//犯罪记录
        }
        if (isCountry(['PH', 'MY'])) {
            $data[] = ['value' => 8, 'label' => self::$t->_('leave_source_8')];//试用期未通过
        }
        $data[] = ['value' => -1, 'label' => self::$t->_('font_leave_source_-1')];
        $data[] = ['value' => 9, 'label' => self::$t->_('font_leave_source_9')];
        $data[] = ['value' => 10, 'label' => self::$t->_('font_leave_source_10')];
        if (isCountry(['PH', 'MY', 'TH'])) {
            $data[] = ['value' => 14, 'label' => self::$t->_('font_leave_source_14')];
        }

        $data[] = [
            'value' => LeaveManageListService::LEAVE_SOURCE_CONTRACT_EXPIRE,
            'label' => self::$t->_('font_leave_source_11'),
        ];
        $data[] = [
            'value' => LeaveManageListService::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT,
            'label' => self::$t->_('font_leave_source_13'),
        ];

        return $data;
    }

    public function areaList()
    {
        $data = [];
        foreach (Enums::$manage_areas as $key => $value) {
            $data[] = ['value' => $key, 'label' => $value];
        }
        return $data;
    }


    //搜索片区
    public function searchRegionList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('manage_piece.id,manage_piece.name');
        $builder->from(['manage_piece' => SysManagePieceModel::class]);
        if (!empty($params['region_ids'])) {
            $builder->andWhere('manage_piece.manage_region_id in ({region_id:array})',
                ['region_id' => is_array($params['region_ids']) ? $params['region_ids'] : [$params['region_ids']]]);
        }
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * tp3状态选项
     * @return array
     */
    public function tp3State()
    {
        $data[] = ['value' => 0, 'label' => 'All'];
        $data[] = ['value' => 1, 'label' => self::$t->_('tp3_state_1')];
        $data[] = ['value' => 2, 'label' => self::$t->_('tp3_state_2')];
        $data[] = ['value' => 3, 'label' => self::$t->_('tp3_state_3')];
        return $data;
    }

    /**
     * tp3状态选项
     * @return array
     */
    public function tp1State()
    {
        $data[] = ['value' => 0, 'label' => 'All'];
        $data[] = ['value' => 1, 'label' => self::$t->_('tp1_approve_state_1')];
        $data[] = ['value' => 2, 'label' => self::$t->_('tp1_approve_state_2')];
        $data[] = ['value' => 3, 'label' => self::$t->_('tp1_approve_state_3')];
        return $data;
    }

    /**
     * 获取所有班次信息列表
     */
    public function shiftInfo()
    {
        try {
            //所有班次信息
            $list = (new HrShiftService())->getList();

            $data = [];
            if (!empty($list)) {
                foreach ($list as $k => $v) {
                    $type   = strtolower($v['type']);
                    $data[] = ['value' => $v['id'], 'label' => self::$t[$type] . ' ' . $v['start'] . ' - ' . $v['end']];
                }
            }
            return $data;
        } catch (\Exception $e) {
            $this->logger->warning('hrShiftAction异常信息：' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    /**
     * 班次信息通过 type 分组
     * @param $params
     * @return array
     */
    public function shiftsToType($params = [])
    {
        $list   = (new HrShiftService())->getList($params);
        $result = [];
        foreach ($list as $oneData) {
            $result[$oneData['type']][] = [
                'start'  => $oneData['start'],
                'end'    => $oneData['end'],
                'id'     => $oneData['id'],
                'type'   => $oneData['type'],
                'markup' => '(' . self::$t->_('shift_' . strtolower($oneData['type'])) . ') ' . $oneData['start'] . '-' . $oneData['end'],
            ];
        }
        return $result;
    }

    /**
     * 获取所有班次信息列表：只展示时间区间
     * @return array
     */
    public function shiftTimeInfo()
    {
        //所有班次信息
        $list = (new HrShiftService())->getList(['is_all' => true]);

        $data = [];
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $data[] = ['value' => $v['id'], 'label' => $v['start'] . ' - ' . $v['end']];
            }
        }
        return $data;
    }


    /**
     * 工作制天数
     */
    public function weekWorkingDaysList()
    {
        $data = [];
        foreach (StaffEnums::$week_working_days as $key => $value) {
            $data[] = ['value' => $value, 'label' => $value];
        }
        return $data;
    }

    /**
     * 获取全部角色
     * @return array
     */
    public function getRoles(): array
    {
        $rpc = (new ApiClient('hris', '', 'role_list'));
        $rpc->setParams(['']);
        $result = $rpc->execute();

        //获取当前语言
        if (in_array(self::$language, ['zh-CN', 'zh'])) {
            $currentLang = 'zh';
        } elseif (self::$language == 'th') {
            $currentLang = 'th';
        } else {
            $currentLang = 'en';
        }
        $roleIdsArr = array_map(function ($v) use ($currentLang) {
            return [
                'key'   => $v['role_id'],
                'value' => $v['role_name_' . $currentLang] ?? '',
            ];
        }, $result);
        return $roleIdsArr ?? [];
    }

    /**
     * 获取全部角色，返回标准结构（label/value）
     * @return array
     */
    public function standardGetRoles(): array
    {
        $rpc = (new ApiClient('hris', '', 'role_list'));
        $rpc->setParams(['']);
        $result = $rpc->execute();

        //获取当前语言
        if (in_array(self::$language, ['zh-CN', 'zh'])) {
            $currentLang = 'zh';
        } elseif (self::$language == 'th') {
            $currentLang = 'th';
        } else {
            $currentLang = 'en';
        }
        return array_map(function ($v) use ($currentLang) {
            return [
                'value' => $v['role_id'],
                'label' => $v['role_name_' . $currentLang] ?? '',
            ];
        }, $result);
    }

    //工作天数&轮休规则
    public function getWorkingDayRestType()
    {
        $data = [];
        foreach (Enums::$working_day_rest_type as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 获取同步国家列表
     */
    public function SyncCountryList()
    {
        $data = [];
        foreach (Enums::$sync_country_list as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 获取部门职位关联的角色且区分 网点和总部角色
     * @param $params
     * @return array
     */
    public function getRelateRoles($params)
    {
        $rpc_data['department_id'] = $params['department_id'];
        $rpc_data['job_title_id']  = $params['job_title'];
        $rpc_data['sys_store_id']  = $params['sys_store_id'];
        $hris_rpc                  = new ApiClient('hris', '', 'department_job_title_role_by_store', self::$language);
        $hris_rpc->setParams([$rpc_data]);
        $res = $hris_rpc->execute();

        if (isset($res['code']) && $res['code'] == 1) {
            return $res['data'];
        }
        return [];
    }

    public function getContainerType()
    {
        $data = [];
        foreach (VanContainerModel::$typeList as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    //车厢审核类型 只有 1，2，3
    public function getContainerStateList()
    {
        $data = [];
        foreach (Enums::$audit_status as $key => $value) {
            if ($key == Enums::audit_status_4 || $key == Enums::audit_status_5) {
                continue;
            }
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }

    /**
     * 根据部门职位获取轮休规则
     * @param $params
     * @return array
     */
    public function getRestRule($params)
    {
        $work_rest_type = $job_level = [];
        $res            = (new HrJobTitleService())->getRoleJobDeptRelate($params);
        if ($res) {
            $working_day_rest_type = explode(',', $res['working_day_rest_type']);
            foreach (Enums::$working_day_rest_type as $key => $value) {
                if (!in_array($key, $working_day_rest_type)) {
                    continue;
                }
                $work_rest_type[] = ['value' => $key, 'label' => self::$t->_($value)];
            }

            $job_level_arr = explode(',', $res['job_level']);
            $jobLevel      = array_column(Enums::$job_level, null, 'id');
            foreach ($job_level_arr as $oneLevel) {
                if (!isset($jobLevel[$oneLevel])) {
                    continue;
                }
                $job_level[] = $jobLevel[$oneLevel];
            }
        }

        $result['work_rest_type'] = $work_rest_type;
        $result['job_level']      = $job_level;

        return $result;
    }

    /**
     * 获取离职类型-离职原因关系关系
     * @return array
     */
    public function getLeaveReason()
    {
        $rpc_data = [];
        $hris_rpc = new ApiClient('hris', '', 'get_leave_reason', self::$language);
        $hris_rpc->setParams([$rpc_data]);
        $res = $hris_rpc->execute();

        if (isset($res['code']) && $res['code'] == 1) {
            return $res['data'];
        }
        return [];
    }

    public function defaultRestDayDateMap()
    {
        $data = [];
        foreach (range(1, 7) as $item) {
            $data[] = ['name' => self::$t->_('default_rest_day_' . $item), 'value' => (string)$item];
        }
        return $data;
    }

    /**
     * 获取众包支付状态
     * @return array
     */
    public function getDriverCrowdsourcingPayStatus()
    {
        $data = [];
        foreach (HrDriverCrowdsourcingModel::$pay_status_list as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }

        return $data;
    }

    /**
     * 部门树
     * @return array
     */
    public function getDepartmentTreeList()
    {
        return (new SysDepartmentService())->getDeptTreeListFromCache();
    }

    /**
     * 根据传递的条件确认是否查询子部门
     * @param array $params
     * @return array
     */
    public static function getDepartmentConditionByParams(array $params): array
    {
        $all_department_ids = [];

        // 是否查询子部门，true 表示需要查询子部门，false 表示不需要查询子部门
        $is_sub_department = 1;
        if (isset($params['is_sub_department'])) {
            $is_sub_department = (int)$params['is_sub_department'];
        }

        // 获取部门id, 如果为999 并且is_sub_department = 1 表示查询所有部门，则直接返回空数据,则调用处则不再拼接 部门查询的条件
        $department_id = (isset($params['department']) && !empty($params['department'])) ? $params['department'] : 0;
        if (999 == $department_id && 1 == $is_sub_department) {
            return $all_department_ids;
        }

        // 追加当前部门id
        $all_department_ids = array_merge($all_department_ids, [$department_id]);
        if (0 < $department_id && 1 === $is_sub_department) {
            $sub_department_ids = (new SysDepartmentService())->getChildrenListByDepartmentIdFromCache($department_id,
                true);
            // 追加子部门id
            $all_department_ids = array_merge_recursive($all_department_ids, $sub_department_ids);
        }

        // 返回当前部门id和子部门id
        return array_values($all_department_ids);
    }

    /**
     * @description:获取打卡白名单状态
     * @author: L.J
     * @time: 2023/2/2 10:35
     */
    public function getAttendanceWhiteListState()
    {
        return (new AttendanceWhiteListService)->getState();
    }

    /**
     * 展示辅导员的职位
     * @return array[]
     */
    public function counselorShowJobTitle(): array
    {
        $job_title = \App\Modules\Th\library\Enums\enums::$job_title;
        $list      = [
            ['value' => 13, 'label' => 'Bike Courier'],
            ['value' => 37, 'label' => 'DC officer'],
            ['value' => 110, 'label' => 'Van Courier'],
        ];

        if (isCountry('th')) {
            $list[] = ['value' => $job_title['ev_courier'], 'label' => 'EV Courier'];
            $list[] = ['value' => $job_title['van_courier_project'], 'label' => 'Van Courier (Project)'];
            $list[] = ['value' => $job_title['tricycle_courier'], 'label' => 'Tricycle Courier'];
        }
        return $list;
    }

    /**
     * 排班建议 职位类型下拉
     * @return array[]
     */
    public function schedulingSuggestionPosition(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('scheduling_suggestion_position_1')],
            ['value' => 2, 'label' => static::$t->_('scheduling_suggestion_position_2')],
        ];
    }

    /**
     * 排班建议 职位类型下拉
     * @return array[]
     */
    public function jobTypeList(): array
    {
        $jobTypeList = WorkdaySettingDailyModel::$jobTypeMap;
        foreach ($jobTypeList as $key => $value) {
            $list[] = ['value' => $key, 'label' => static::$t->_($value)];
        }
        return $list;
    }

    /**
     * 一线员工薪资配置-薪资规则配置 薪资项目 枚举
     * @return array[]
     */
    public function frontLineStaffSalaryConfigSalaryItem(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('fsc_salary_item_type_1')],
            ['value' => 2, 'label' => static::$t->_('fsc_salary_item_type_2')],
            ['value' => 3, 'label' => static::$t->_('fsc_salary_item_type_3')],
            ['value' => 4, 'label' => static::$t->_('fsc_salary_item_type_4')],
            ['value' => 5, 'label' => static::$t->_('fsc_salary_item_type_5')],
            ['value' => 6, 'label' => static::$t->_('fsc_salary_item_type_6')],
            ['value' => 7, 'label' => static::$t->_('fsc_salary_item_type_7')],
        ];
    }

    /**
     * 日薪一线员工薪资配置-薪资规则配置 薪资项目 枚举
     * @return array[]
     */
    public function dailyFrontLineStaffSalaryConfigSalaryItem(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('fsc_salary_item_type_1')],
            ['value' => 2, 'label' => static::$t->_('fsc_salary_item_type_2')],
            ['value' => 3, 'label' => static::$t->_('fsc_salary_item_type_3')],
        ];
    }

    /**
     * 日薪一线员工薪资配置-薪资规则配置 薪资规则 枚举
     * @return array[]
     */
    public function dailyFrontLineStaffSalaryConfigSalaryRule(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('dfsc_salary_rule_type_1')],
            ['value' => 2, 'label' => static::$t->_('dfsc_salary_rule_type_2')],
        ];
    }

    /**
     * 一线员工薪资配置-薪资规则配置 薪资规则 枚举
     * @return array[]
     */
    public function frontLineStaffSalaryConfigSalaryRule(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('fsc_salary_rule_type_1')],
            ['value' => 2, 'label' => static::$t->_('fsc_salary_rule_type_2')],
            ['value' => 3, 'label' => static::$t->_('fsc_salary_rule_type_3')],
            ['value' => 4, 'label' => static::$t->_('fsc_salary_rule_type_4')],
            ['value' => 5, 'label' => static::$t->_('fsc_salary_rule_type_5')],
            ['value' => 6, 'label' => static::$t->_('fsc_salary_rule_type_6')],
            ['value' => 7, 'label' => static::$t->_('fsc_salary_rule_type_7')],
            ['value' => 8, 'label' => static::$t->_('fsc_salary_rule_type_8')],
        ];
    }

    /**
     * 一线员工薪资配置-薪资规则配置 状态 枚举
     * @return array[]
     */
    public function frontLineStaffSalaryConfigSalaryRuleState(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('fsc_state_1')],
            ['value' => 2, 'label' => static::$t->_('fsc_state_2')],
        ];
    }

    /**
     * 排班建议 职位详情
     * @return array[]
     */
    public function schedulingSuggestionPositionDetail(): array
    {
        $jobTitleMap = (new SchedulingSuggestionService())->getJobTitle();
        foreach ($jobTitleMap as $position => $jobTitleIds) {
            foreach ($jobTitleIds as $key => $jobTitleId) {
                $jobTitleMap[$position][$key] = $this->showJobTitleName($jobTitleId);
            }
        }
        return [
            ['value' => 1, 'include' => implode(',', $jobTitleMap[1])],
            ['value' => 2, 'include' => implode(',', $jobTitleMap[2])],
        ];
    }

    //个人代理转型状态
    public function changeStateEnum()
    {
        return [
            [
                'value' => HireTypeChangeInfoModel::TRANSITION_STATE_TO_BE_TRANSFORMED,
                'label' => static::$t->_('change_status_ing'),
            ],
            [
                'value' => HireTypeChangeInfoModel::TRANSITION_STATE_TRANSFORMED,
                'label' => static::$t->_('change_status_success'),
            ],
            [
                'value' => HireTypeChangeInfoModel::TRANSITION_STATE_FAILED,
                'label' => static::$t->_('change_status_fail'),
            ],
        ];
    }

    //轮休班次修改类型枚举 操作日志  类型：默认空，可多选，枚举值为：取消休息日、设置休息日、修改默认休息日、修改当日班次时间、修改未来班次时间
    public function shiftLogTypeEnum()
    {
        /**
         * work_shift_log_type_cancel_off  取消休息日
         * work_shift_log_type_add_off  增加休息日
         * work_shift_log_type_default_rest  修改默认休
         * work_shift_log_type_current_shift 修改当天班次
         * work_shift_log_type_preset_shift  修改未来班次
         */

        return [
            [
                'value' => HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF,
                'label' => static::$t->_('work_shift_log_type_cancel_off'),
            ],
            [
                'value' => HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF,
                'label' => static::$t->_('work_shift_log_type_add_off'),
            ],
            [
                'value' => HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST,
                'label' => static::$t->_('work_shift_log_type_default_rest'),
            ],
            [
                'value' => HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT,
                'label' => static::$t->_('work_shift_log_type_current_shift'),
            ],
            [
                'value' => HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT,
                'label' => static::$t->_('work_shift_log_type_preset_shift'),
            ],
        ];
    }

    /**
     * 工号开通原因
     * @return array
     */
    public function staffCreateReason()
    {
        $result = [];
        foreach (StaffOtherStaffRelationModel::$createReason as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }

    /**
     * 是否已有其他工号
     * @return array
     */
    public function isIssetOtherStaff()
    {
        $result = [];
        foreach (StaffOtherStaffRelationModel::$isIssetOtherStaff as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }

    /**
     * 其他工号归属国家
     * @return array
     */
    public function otherStaffIdBelongCountry()
    {
        $result = [];
        foreach (StaffOtherStaffRelationModel::$belong_country as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }

    /**
     * 同步原因
     * @return array
     */
    public function staffSyncReason()
    {
        $result = [];
        foreach (StaffInfoIntlModel::$syncReason as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }

    /**
     * 跟进处理状态
     * @return array
     */
    public function FollowStatus()
    {
        $result = [];
        foreach (CeoMailFollowModel::$follow_status as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }


    /**
     * 跟进处理结果
     * @return array
     */
    public function FollowResult()
    {
        $result = [];
        foreach (CeoMailFollowModel::$follow_result as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = static::$t->_($value);
            $result[]         = $oneData;
        }

        return $result;
    }

    /**
     * 跟进问题分类
     * @return array
     */
    public function FollowCategory()
    {
        $follow_category = range(1, 11);
        $result          = [];
        foreach ($follow_category as $key => $value) {
            $oneData['value'] = $value;
            $oneData['label'] = static::$t->_('follow_category_' . $value);
            $result[]         = $oneData;
        }

        return $result;
    }

    /**
     * 消息管理-网点类型 （多了HeadOffice）
     * @return array
     */
    public function messageStoreCategory(): array
    {
        return (new MessagesService())->getStoreCategory();
    }

    /**
     * 获取试用期目标设置状态
     * @return array
     */
    public function probationTargetSettingState(): array
    {
        $list = HrProbationTargetModel::$setting_state_list;
        unset($list[HrProbationTargetModel::SETTING_STATE_NOT_START]);

        $returnData = [];
        foreach ($list as $key => $value) {
            $returnData[] = [
                'label' => static::$t->_($value),
                'value' => (string)$key,
            ];
        }
        return $returnData;
    }

    /**
     * 获取试用期目标发送状态
     * @return array
     */
    public function probationTargetSendState(): array
    {
        $list       = HrProbationTargetModel::$send_state_list;
        $returnData = [];
        foreach ($list as $key => $value) {
            $returnData[] = [
                'label' => static::$t->_($value),
                'value' => (string)$key,
            ];
        }
        return $returnData;
    }

    /**
     * 获取试用期目标签字状态
     * @return array
     */
    public function probationTargetSignState(): array
    {
        $list       = HrProbationTargetModel::$sign_state_list;
        $returnData = [];
        foreach ($list as $key => $value) {
            $returnData[] = [
                'label' => static::$t->_($value),
                'value' => (string)$key,
            ];
        }
        return $returnData;
    }

    /**
     * 获取试用期TAB目标设置状态
     * @return array
     */
    public function probationTargetTabSettingState(): array
    {
        $list       = HrProbationTargetModel::$tab_setting_state_list;
        $returnData = [];
        foreach ($list as $key => $value) {
            $returnData[] = [
                'label' => static::$t->_($value),
                'value' => (string)$key,
            ];
        }
        return $returnData;
    }

    /**
     * 消息管理-搜索类型
     * @return array
     */
    public function messageSearchCategory(): array
    {
        $enum = [
            MessageEnums::ORGANIZATION_TYPE_DEPARTMENT,
            MessageEnums::ORGANIZATION_TYPE_STORE,
            MessageEnums::ORGANIZATION_TYPE_STAFF,
        ];
        foreach ($enum as $value) {
            $oneData['value'] = $value;
            $oneData['label'] = static::$t->_('message_search_category_' . $value);
            $result[]         = $oneData;
        }
        return $result;
    }


    /**
     * 试用期员工评估状态
     *  1“待发起”2“评估中”3“已完成”4“已超时”
     * @return array
     */
    public function probationaryPeriodAssessStatus(): array
    {
        $returnData = [];
        foreach (range(1, 4) as $value) {
            $returnData[] = [
                'value' => strval($value),
                'label' => static::$t->_('hr_probation_audit_status_' . $value),
            ];
        }
        return $returnData;
    }

    /**
     * 证书消息展示模版
     * @return array|string[]
     */
    public function certificateTplUrls(): array
    {
        $tplUrls    = (new MessagesService())->certificateTplUrls();
        $returnData = [];
        foreach ($tplUrls as $key => $value) {
            $returnData[] = [
                'value' => strval($key),
                'label' => $value,
            ];
        }
        return $returnData;
    }

    /**
     * 试用期员工评估结果
     * 评估结果 0 未通过  1 通过
     * @return array
     */
    public function probationaryPeriodStatus(): array
    {
        $returnData = [];
        foreach (range(0, 1) as $value) {
            $returnData[] = [
                'value' => strval($value),
                'label' => static::$t->_('hr_probation_status_' . $value),
            ];
        }
        return $returnData;
    }

    /**
     *网点是否考核车厢 1 考核 2 不考核
     * @return array[]
     */
    public function storeAssessCarriage(): array
    {
        return [
            [
                'value' => HrStoreSubsidiesCategoryModel::STORE_ASSESS_CARRIAGE_YES,
                'label' => static::$t->_('store_assess_carriage_1'),
            ],
            [
                'value' => HrStoreSubsidiesCategoryModel::STORE_ASSESS_CARRIAGE_NO,
                'label' => static::$t->_('store_assess_carriage_2'),
            ],
        ];
    }

    /**
     * @param string $content
     * @param int $company_type
     * @return string
     */
    public function getEmailSignature(string $content, int $company_type): string
    {
        if (isCountry('MY')) {
            $companyConfigInfo            = StaffPayrollCompanyInfoModel::findFirst([
                'columns'    => 'company_name, company_registration_no,business_registration_number',
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $company_type],
            ]);
            $companyConfigInfo            = empty($companyConfigInfo) ? [] : $companyConfigInfo->toArray();
            $company_name                 = $companyConfigInfo['company_name'] ?? '';
            $business_registration_number = !empty($companyConfigInfo['business_registration_number']) ? '(' . $companyConfigInfo['business_registration_number'] . ')' : '';
            $company_registration_no      = !empty($companyConfigInfo['company_registration_no']) ? 'Company Registration No. : ' . $companyConfigInfo['company_registration_no'] . $business_registration_number : '';
            $email_signature              = '<br/><br/><br/>' . $company_name . '<br/>' . $company_registration_no;
            $content                      .= $email_signature;
        }
        return $content;
    }

    //获取 hold类型，hold原因 枚举
    public function get_hold_enums_info($locale, $params)
    {
        self::setLanguage($locale['locale']);

        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $this->getHoldEnumsInfo()];
    }

    public function getHoldTypes()
    {
        $data = [];
        foreach (HoldStaffManageModel::$hold_type as $key=> $value) {
            $data[] = [
                "key" => strval($key),
                "value" => self::$t->_($value),
            ];
        }
        return $data;
    }

    public function getHoldEnumsInfo()
    {
        $data['hold_type_list']   = $this->getHoldTypes();
        $data['hold_reason_list'] = (new HoldManageService())->getHoldReason();
        return $data;
    }
    /**
     * @param $bank_id
     * @param $bank_no
     * @return true
     * @throws ValidationException
     */
    public function bankNoVerify($bank_id, $bank_no): bool
    {
        $bank_id   = trim($bank_id);
        $bank_no   = trim($bank_no);
        $bank_list = BanklistModel::findFirst([
            'conditions' => "bank_id = :bank_id:",
            'bind'       => [
                'bank_id' => $bank_id,
            ],
        ]);
        if (empty($bank_list)) {
            throw new ValidationException($this->getTranslation()->_('bank_no_err_4'));
        } elseif (empty($bank_no) || !preg_match("/^\d{1,30}$/", $bank_no)) {
            throw new ValidationException($this->getTranslation()->_('bank_no_err_1'));
        } else {
            $bank_no_len = strlen($bank_no);
            if (($bank_no_len < $bank_list->min_length || $bank_no_len > $bank_list->max_length) && $bank_list->min_length != $bank_list->max_length) {
                throw new ValidationException($this->getTranslation()->_('bank_no_err_3',
                    ['min_length' => $bank_list->min_length, 'max_length' => $bank_list->max_length]));
            } elseif (($bank_no_len < $bank_list->min_length || $bank_no_len > $bank_list->max_length) && $bank_list->min_length == $bank_list->max_length) {
                throw new ValidationException($this->getTranslation()->_('bank_no_err_2',
                    ['min_length' => $bank_list->min_length]));
            }
        }
        return true;
    }

    /**
     * 电子警告信，发送来源
     * @return array
     */
    public function warning_send_source()
    {
        $returnData = [];
        foreach (MessageWarningModel::$warning_send_source as $key => $value) {
            $returnData[] = [
                'value' => strval($key),
                'label' => static::$t->_($value),
            ];
        }
        return $returnData;
    }

    /**
     * 转正评估非一线分数枚举
     * @return array[]
     */
    public function probation_score_enumerate(): array
    {
        return [
            ['value' => 1, 'label' => 'C'],
            ['value' => 2, 'label' => 'B-'],
            ['value' => 3, 'label' => 'B'],
            ['value' => 4, 'label' => 'B+'],
            ['value' => 5, 'label' => 'A'],
        ];
    }

    /**
     * 合同公司枚举
     * @return array
     */
    public function getContractCompanyList()
    {
        $companyInfo = StaffPayrollCompanyInfoRepository::allData([], 'company_id,company_short_name');

        $companyInfoText = [];
        foreach ($companyInfo as $key => $value) {
            $companyInfoText[] = [
                'value' => strval($value['company_id']),
                'label' => $value['company_short_name'],
            ];
        }

        return $companyInfoText;
    }

    /**
     * 职位性质
     */
    public function positionType()
    {
        $data = [];
        foreach (HrJobDepartmentRelationModel::$position_type_list as $key => $value) {
            $data[] = ['value' => (string)$key, 'label' => static::$t->_($value)];
        }
        return $data;
    }
    /**
     * 插入附件
     * @param $biz_value
     * @param $oss_type
     * @param $file_path
     * @param string $file_name
     * @return bool|null
     * @throws \Exception
     */
    public  function insertFile($biz_value,$oss_type, $file_path, string $file_name= '')
    {
        if (empty($file_path)) {
            return false;
        }
        $urlInfo = parse_url($file_path);

        $model      = new SysAttachmentModel();
        $insertData = [
            'id'              => $this->generateUniqueId(),
            'oss_bucket_type' => $oss_type,
            'oss_bucket_key'  => $biz_value,
            'bucket_name'     => substr($urlInfo['host'], 0, strpos($urlInfo['host'], '.')),
            'object_key'      => trim($urlInfo['path'], '/'),
            'original_name'   => $file_name ?? null,
        ];
        return $model->create($insertData);
    }

    /**
     * 获取特殊无车厢名单-生效状态
     * @return array
     */
    public function getEffectiveStatus(): array
    {
        $returnData = [];
        foreach (FrontlineSpecialNonCarriageModel::$effectiveStatus as $key => $value) {
            $returnData[] = [
                'value' => strval($key),
                'label' => static::$t->_($value),
            ];
        }
        return $returnData;
    }
    /**
     * 大区片区 搜索框 联动 返回全部
     * @return mixed
     */
    public function region_piece()
    {
        return (new ManageRegionsService())->get_region_pieceFromCache();
    }

    /**
     * 提交状态
     * @return mixed
     */
    public function submitStatus()
    {
        $returnData = [];
        foreach (Enums::$submit_status as $key => $value) {
            $returnData[] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }
        return $returnData;
    }

    /**
     * 在职天数枚举
     * @return array
     */
    public function onJobDays()
    {
        $returnData = [];
        foreach (HrStaffInfoExtendModel::$onJobDaysText as $key => $value) {
            $returnData[] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }
        return $returnData;
    }

    public function all_ot_type(){
        $data = (new BllService())->getTypeBookOtList('otTypeBook');
        foreach ($data as $value) {
            $returnData[] = [
                'value' => (string)$value['code'],
                'label' => $value['msg'],
            ];
        }
        return $returnData;
    }

    //上牌地点-省/府
    public function licensePlateLocation($locale, $params)
    {
        self::setLanguage($locale['locale']);

        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $this->getLicensePlateLocation()];
    }

    //上牌地点-省/府
    public function getLicensePlateLocation()
    {
        $returnData = [];
        $list = SysProvinceRepository::getList([], ['name']);
        foreach ($list as $key => $value) {
            $returnData[] = $value['name'];
        }
        if(!empty($returnData)) {
            $returnData[] = 'เบตง';//特殊地点，不是省/府
        }
        return $returnData;
    }



}