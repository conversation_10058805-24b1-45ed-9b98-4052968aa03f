<?php
/**
 * Author: Bruce
 * Date  : 2025-04-28 11:47
 * Description:
 */

namespace App\Services;


use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\EnumSingleton;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\VehicleInfoModel;
use App\Models\backyard\VehicleInspectionModel;
use App\Repository\StaffInfoRepository;
use App\Repository\SysProvinceRepository;
use App\Repository\VanContainerRepository;
use App\Repository\VehicleInspectionRepository;

class VehicleInspectionService extends BaseService
{
    const LIMIT_DOWNLOAD_NUM = 300000;
    const IMPORT_NUM = 999;


    public function getCourierList($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $data['total'] = $this->getCourierCountQuery($params);
        $data['list']  = $this->getCourierListQuery($params);

        return $data;
    }

    /**
     * 获取条数
     * @param $params
     * @return int
     */
    public function getCourierCountQuery($params)
    {
        $builder = $this->modelsManager->createBuilder();

        $builder->from(['vi' => VehicleInspectionModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'vi.staff_info_id = hsi.staff_info_id',
            'hsi');
        $builder->columns('count(*) as count');

        $builder = $this->getCourierBuilderWhere($builder, $params);
        $total = $builder->getQuery()->getSingleResult()->toArray();

        return !empty($total) ? intval($total['count']) : 0;
    }

    /**
     * 获取列表数据
     * @param $params
     * @param $isFormat
     * @return array
     */
    public function getCourierListQuery($params, $isFormat = true)
    {
        $columns = [
            'vi.id',
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'hsi.state',
            'hsi.wait_leave_state',
            'hsi.hire_type',
            'vi.status',
            'vi.created_at',
            'vi.operator_id',
            'vi.operator_name',
            'vi.submit_time',
            'vi.plate_number',
            'vi.license_location',
            'vi.vehicle_img',
            'vi.mileage_img',
            'vi.vehicle_video',
            'vi.video_status',
            'vi.plate_status',
            'vi.mileage_status',
            'vi.carriage_type',
            'vi.audit_time',
            'vi.audit_staff_id',
            'vi.audit_staff_name',

            'vi.report_mileage_time',
            'vi.report_mileage_num',
            'vi.mileage_img',
            'vi.van_container_id',
            'vi.audit_mileage_num',
        ];

        $builder = $this->modelsManager->createBuilder();

        $builder->from(['vi' => VehicleInspectionModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'vi.staff_info_id = hsi.staff_info_id',
            'hsi');

        $builder = $this->getCourierBuilderWhere($builder, $params);

        $builder->columns($columns);

        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));

        $builder->orderBy('vi.id desc');
        $list = $builder->getQuery()->execute()->toArray();
        if ($list && $isFormat) {
            $list = $this->formatList($list);
        }

        return $list;
    }

    public function formatList($data)
    {
        $storeListToId = $jobTitleInfoToId = $list = [];
        $jobTitleId    = array_values(array_unique(array_column($data, 'job_title')));
        $sysStoreId    = array_values(array_unique(array_column($data, 'sys_store_id')));

        $storeList = (new SysStoreService())->getStoreListByIds($sysStoreId);

        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $jobTitleInfo = (new DepartmentService())->getJobList('', $jobTitleId, true);

        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $carriage_ids = array_values(array_unique(array_column($data, 'van_container_id')));
        $carriageInfo = VanContainerRepository::getList(['ids' => $carriage_ids], ['id', 'state', 'type', 'created_at']);
        $carriageInfoToId = empty($carriageInfo) ? [] : array_column($carriageInfo, NULL, 'id');

        $containerState = array_column((new SysService())->getContainerStateList(), 'label', 'value');

        foreach ($data as $value) {
            $oneData['id']            = $value['id'];
            $oneData['staff_info_id'] = $value['staff_info_id'];
            $oneData['name']          = $value['name'];

            $state = $value['state'];
            if ($value['state'] == HrStaffInfoModel::STATE_ON_JOB && $value['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
            }
            $oneData['state_text']       = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';
            $oneData['job_title']        = $value['job_title'];
            $oneData['job_title_name']   = $jobTitleInfoToId[$value['job_title']] ?? '';
            $oneData['store_id']         = $value['sys_store_id'];
            $oneData['store_name']       = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['store_name'] : '';
            $oneData['region_name']      = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['region_name'] : '';
            $oneData['piece_name']       = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['piece_name'] : '';
            $oneData['hire_type_text']   = self::$t->_('hire_type_' . $value['hire_type']);
            $oneData['created_at']       = show_time_zone($value['created_at']);
            $oneData['operator_name']    = '(' . $value['operator_id'] . ')' . (empty($value['operator_name']) ? '' : $value['operator_name']);
            $oneData['status_text']      = isset(Enums::$submit_status[$value['status']]) ? self::$t->_(Enums::$submit_status[$value['status']]) : '';
            $oneData['submit_time']      = empty($value['submit_time']) ? '' : $value['submit_time'];
            $oneData['plate_number']     = empty($value['plate_number']) ? '' : $value['plate_number'];
            $oneData['license_location'] = empty($value['license_location']) ? '' : $value['license_location'];
            $oneData['vehicle_img']      = empty($value['vehicle_img']) ? '' : $value['vehicle_img'];
            $oneData['mileage_img']      = empty($value['mileage_img']) ? '' : $value['mileage_img'];
            $oneData['vehicle_video']    = empty($value['vehicle_video']) ? '' : $value['vehicle_video'];

            $oneData['status']           = $value['status'];
            $oneData['video_status']     = $value['video_status'];
            $oneData['video_status_text']= isset(VehicleInspectionModel::$video_status_text[$value['video_status']]) ? self::$t->_(VehicleInspectionModel::$video_status_text[$value['video_status']]) : '';
            $oneData['plate_status']     = $value['plate_status'];
            $oneData['plate_status_text']= isset(VehicleInspectionModel::$plate_status_text[$value['plate_status']]) ? self::$t->_(VehicleInspectionModel::$plate_status_text[$value['plate_status']]) : '';
            $oneData['mileage_status']     = $value['mileage_status'];
            $oneData['mileage_status_text']= isset(VehicleInspectionModel::$mileage_status_text[$value['mileage_status']]) ? self::$t->_(VehicleInspectionModel::$mileage_status_text[$value['mileage_status']]) : '';
            $oneData['carriage_type']     = $value['carriage_type'];
            $oneData['carriage_type_text']= isset(VehicleInspectionModel::$carriage_type_text[$value['carriage_type']]) ? self::$t->_(VehicleInspectionModel::$carriage_type_text[$value['carriage_type']]) : '';//hcm 审核的
            $oneData['audit_time']        = !empty($value['audit_time']) ? $value['audit_time'] : '';
            $oneData['audit_staff_name']  = !empty($value['audit_staff_id']) ? '(' . $value['audit_staff_id'] . ')' . $value['audit_staff_name'] : '';

            $oneData['report_mileage_time']  = !empty($value['report_mileage_time']) ? $value['report_mileage_time'] : '';
            $oneData['report_mileage_num']  = !empty($value['report_mileage_num']) ? $value['report_mileage_num'] : '';
            $oneData['mileage_img']  = !empty($value['mileage_img']) ? $value['mileage_img'] : '';

            $carriageInfo = !empty($carriageInfoToId[$value['van_container_id']]) ? ($carriageInfoToId[$value['van_container_id']] ?? '') : [];
            $last_carriage_type = !empty($carriageInfoToId[$value['van_container_id']]) ? ($carriageInfoToId[$value['van_container_id']]['type'] ?? 0) : 0;

            $oneData['last_carriage_audit_time']    = !empty($carriageInfo['created_at']) ? show_time_zone($carriageInfo['created_at']) : '';
            $oneData['last_carriage_type']          = isset(VehicleInspectionModel::$carriage_type_text[$last_carriage_type]) ? self::$t->_(VehicleInspectionModel::$carriage_type_text[$last_carriage_type]) : '';//车厢记录审核的
            $oneData['last_carriage_audit_status']  = !empty($carriageInfo['state']) ? ($containerState[$carriageInfo['state']] ?? '') : '';//车厢记录审核实时状态

            $oneData['audit_mileage_num']  = !empty($value['audit_mileage_num']) ? $value['audit_mileage_num'] : '';

            $list[] = $oneData;
        }

        return $list;
    }

    /**
     * 构建查询
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getCourierBuilderWhere($builder, $params)
    {
        //员工id
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('hsi.staff_info_id = :staff_info_id:',
                ['staff_info_id' => $params['staff_info_id']]);
        }

        //在职状态
        if (!empty($params['state']) && is_array($params['state'])) {
            if (in_array(Enums::HRIS_WORKING_STATE_4, $params['state'])) {
                $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state=:state: AND hsi.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $params['state'],
                        'state'            => Enums::HRIS_WORKING_STATE_1,
                        'wait_leave_state' => Enums::WAIT_LEAVE_STATE,
                    ]);
            } elseif (!in_array(Enums::HRIS_WORKING_STATE_4,
                    $params['state']) && in_array(Enums::HRIS_WORKING_STATE_1, $params['state'])) {
                $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state != :wait_leave_state:",
                    ['states' => $params['state'], 'wait_leave_state' => Enums::WAIT_LEAVE_STATE]);
            } else {
                $builder->andWhere("hsi.state IN ({states:array})", ['states' => $params['state']]);
            }
        }

        //大区、片区
        $builder = $this->getWhereByRegionPiece($builder, $params);

        //网点
        if (!empty($params['sys_store_id']) && is_array($params['sys_store_id'])) {
            $builder->andWhere('hsi.sys_store_id in ({sys_store_id:array})',
                ['sys_store_id' => $params['sys_store_id']]);
        }

        //职位
        if (!empty($params['job_title_id']) && is_array($params['job_title_id'])) {
            $builder->andWhere('hsi.job_title in ({job_title:array})', ['job_title' => $params['job_title_id']]);
        }

        //导入日期
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $created_start_date = gmdate('Y-m-d H:s:i', strtotime($params['start_date'] . ' 00:00:00'));
            $created_end_date   = gmdate('Y-m-d H:s:i', strtotime($params['end_date'] . ' 23:59:59'));
            $builder->betweenWhere('vi.created_at', $created_start_date, $created_end_date);
        }

        //提交状态
        if (!empty($params['status'])) {
            $builder->andWhere("vi.status = :status:", ['status' => $params['status']]);
        }

        //车辆视频审核状态
        if (!empty($params['video_status'])) {
            $builder->andWhere("vi.video_status = :video_status:", ['video_status' => $params['video_status']]);
        }

        //车牌审核结果
        if (!empty($params['plate_status'])) {
            $builder->andWhere("vi.plate_status = :plate_status:", ['plate_status' => $params['plate_status']]);
        }

        //里程表审核结果
        if (!empty($params['mileage_status'])) {
            $builder->andWhere("vi.mileage_status = :mileage_status:", ['mileage_status' => $params['mileage_status']]);
        }

        //车厢审核结果
        if (!empty($params['carriage_type'])) {
            $builder->andWhere("vi.carriage_type = :carriage_type:", ['carriage_type' => $params['carriage_type']]);
        }

        return $builder;
    }

    /**
     * 大区、片区
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByRegionPiece($builder, $params)
    {
        //片区
        $store_ids = [];
        if (!empty($params['piece_id'])) {
            $piece_store_list    = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => ' state = :state: AND manage_piece = :manage_piece:',
                'bind'       => ['state' => SysStoreModel::STATE_1, 'manage_piece' => $params['piece_id']],
            ])->toArray();
            $store_ids           = array_column($piece_store_list, 'id');
            $params['region_id'] = '';// 如果选了片区，则不按大区筛选
        }
        //大区
        if (!empty($params['region_id'])) {
            $region_store_list = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => ' state = :state: AND manage_region = :manage_region:',
                'bind'       => ['state' => SysStoreModel::STATE_1, 'manage_region' => $params['region_id']],
            ])->toArray();
            $store_ids         = array_column($region_store_list, 'id');
        }

        if (!empty($store_ids)) {
            $builder->andWhere('hsi.sys_store_id IN ({store_ids:array})', ['store_ids' => $store_ids]);
        }

        return $builder;
    }

    /**
     * 导出
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function courierExport($params)
    {
        $count = $this->getCourierCountQuery($params);
        if ($count > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit', ['num' => self::LIMIT_DOWNLOAD_NUM]));
        }

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "vehicle_inspection" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "courier_export";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $headerData = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && in_array($headerData['From'], ['fbi'])? $headerData['From'] : '';

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        if (!empty($hcmExcelTaskId) && in_array($params['From'], ['fbi'])) {
            (new BllService())->addFbiExportMessage(
                $params['staff_id'],
                'hcm_vehicle_inspection_courier_export',
                $params['file_name'],
                $hcmExcelTaskId
            );
        }

        return $hcmExcelTaskId;
    }

    /**
     * 查看
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function courierCheck($params)
    {
        $info = VehicleInspectionRepository::getOne(['id' => $params['id']]);

        if (empty($info)) {
            throw new BusinessException('not found vehicle inspection ' . self::$t->_('data_error'));
        }

        $courierInfo = StaffInfoRepository::getInfoByStaffInfoId($info['staff_info_id'], true);

        $staffInfo['staff_info_id']    = $courierInfo['staff_info_id'];
        $staffInfo['name']             = $courierInfo['name'];
        $staffInfo['job_title_name']   = $this->showJobTitleName($courierInfo['job_title']);
        $staffInfo['store_name']       = $this->showStoreName($courierInfo['sys_store_id']);
        $staffInfo['hire_type_text']   = self::$t->_('hire_type_' . $courierInfo['hire_type']);
        $staffInfo['plate_number']     = empty($info['plate_number']) ? null : $info['plate_number'];
        $staffInfo['license_location'] = empty($info['license_location']) ? null : $info['license_location'];

        $submit_info = [];
        //本次提交信息
        if($info['status'] == Enums::SUBMIT_STATUS_COMPLETE) {
            $submit_info['submit_time']   = $info['submit_time'];
            $submit_info['vehicle_video'] = $info['vehicle_video'];
            $submit_info['mileage_num']   = $info['mileage_num'];
        }

        //最近一次里程汇报记录
        $report_info = [];
        if ($info['status'] == Enums::SUBMIT_STATUS_COMPLETE) {
            $report_info['report_mileage_time'] = $info['report_mileage_time'];
            $report_info['report_mileage_num']  = $info['report_mileage_num'];
            $report_info['mileage_img']         = $info['mileage_img'];
        }

        $containerState = array_column((new SysService())->getContainerStateList(), 'label', 'value');

        //最近一次车厢信息
        $carriage_info = [];
        if (!empty($info['status']) && $info['status'] == Enums::SUBMIT_STATUS_COMPLETE) {
            $containerInfo = empty($info['van_container_id']) ? [] : VanContainerRepository::getOne(['id' => $info['van_container_id']]);
            $containerInfoType = !empty($containerInfo['type']) ? $containerInfo['type'] : '';

            $carriage_info['last_carriage_audit_time']   = !empty($containerInfo['created_at']) ? show_time_zone($containerInfo['created_at']) : '';
            $carriage_info['last_carriage_type_text']    = isset(VehicleInspectionModel::$carriage_type_text[$containerInfoType]) ? self::$t->_(VehicleInspectionModel::$carriage_type_text[$containerInfoType]) : '';
            $carriage_info['last_carriage_audit_status'] = !empty($containerInfo['state']) ? ($containerState[$containerInfo['state']] ?? '') : '';
        }
        //车辆视频审核结果
        $audit_info = [];
        if (!empty($info['video_status']) && $info['video_status'] != VehicleInspectionModel::VIDEO_STATUS_PENDING) {
            $audit_info['video_status']      = $info['video_status'];
            $audit_info['plate_status']      = $info['plate_status'];
            $audit_info['audit_mileage_num'] = $info['audit_mileage_num'];
            $audit_info['mileage_status']    = $info['mileage_status'];
            $audit_info['carriage_type']     = $info['carriage_type'];
            $audit_info['audit_staff']       = !empty($info['audit_staff_id']) ? '(' . $info['audit_staff_id'] . ')' . $info['audit_staff_name'] : '';;
            $audit_info['audit_time']        = $info['audit_time'];
        }


        $data['submit_status'] = $info['status'];
        $data['staff_info']  = $staffInfo;
        $data['submit_info'] = empty($submit_info) ? null : $submit_info;
        $data['report_info'] = empty($report_info) ? null : $report_info;
        $data['carriage_info'] = empty($carriage_info) ? null : $carriage_info;
        $data['audit_info'] = empty($audit_info) ? null : $audit_info;

        return $data;
    }

    /**
     * 获取模板
     * @return mixed
     * @throws BusinessException
     */
    public function importTemplate()
    {
        $fileUrl = (new SettingEnvService())->getSetVal('vehicle_inspection_import_template');
        if (empty($fileUrl)) {
            throw new BusinessException(self::$t->_('data_error'));
        }
        $data['file_url'] = $fileUrl;

        return $data;
    }

    /**
     * 导入
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function courierImport($params)
    {
        $operatorId = $params['staff_id'];
        $importPath = $params['file_url'];

        $args['lang']      = $params['lang'];
        $args['file_name'] = $params['file_name'];
        $this->uploadCheck($importPath);

        (new AsyncImportTaskService())->insertTask($operatorId,
            AsyncImportTaskModel::COURIER_VEHICLE_INSPECTION, $importPath, $args);

        return true;
    }

    /**
     * 检验行数
     * @param $importPath
     * @throws BusinessException
     */
    public function uploadCheck($importPath)
    {
        $tmpDir      = sys_get_temp_dir();            // 获取系统的临时目录路径
        $fileName    = basename($importPath);         // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;     // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
            throw new BusinessException('System error');
        }

        $config       = ['path' => dirname($tmpFilePath)];
        $fileRealName = basename($tmpFilePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet()->setSkipRows(1);
        $excelData = $excel->getSheetData();

        $number = 0;
        foreach (static::yieldData()($excelData) as $item) {
            if (empty($item) || empty(array_filter($item)) || empty($item[0])) {
                continue;
            }
            ++$number;
        }
        if ($number = 0) {
            throw new BusinessException(static::$t->_('file_content_is_null'));
        }

        if ($number > self::IMPORT_NUM) {
            throw new BusinessException(static::$t->_('import_count_limit_error', ['number' => self::IMPORT_NUM]));
        }
        @unlink($tmpFilePath);
    }

    public function exportTask($params, $file_name)
    {
        //获取数据
        $list = [];
        $params['page_num'] = 1;
        $params['page_size'] = 500;
        while (true) {
            $data_tmp = $this->getCourierListQuery($params);
            if (empty($data_tmp)) {
                break;
            }
            $list = array_merge($list, $data_tmp);
            $params['page_num']++;
        }

        //设置语言
        $t = BaseService::getTranslation($params['lang']);

        $new_data = [];
        $header   = [
            $t['staff_info_id'], //工号
            $t['name'], //姓名
            $t['staff_state'], //在职状态
            $t['job_title'], //职位
            $t['store_area'],  //大区
            $t['store_district'],  //片区
            $t['sys_store_name'],  //所属网点
            $t['hire_type'],  //雇佣类型
            $t['import_vehicle_inspection_time'],  //导入稽查时间
            $t['import_operator'],  //导入操作人
            $t['submit_status'],  //提交状态
            $t['courier_submit_time'],  //快递员提交时间
            $t['staff_car_no'],  //车牌号
            $t['container_license_location'],  //上牌地点
//            $t['vehicle_img'],  //车辆照片
//            $t['mileage_img'],  //里程表照片
            $t['vehicle_video'],  //车辆视频
            $t['latest_mileage_reporting_time'],  //最近里程汇报时间（格式：YYYY-MM-DD HH:MM:SS)
            $t['latest_mileage_reporting_num'],  //最近里程表数字
            $t['latest_mileage_reporting_img'],  //最近里程表图片(格式：url)
            $t['latest_carriage_submit_time'],  //最近车厢提交时间
            $t['latest_carriage_status'],  //最近车厢状态
            $t['latest_carriage_audit_status'],  //最近车厢审核状态
            $t['video_status'],  //车辆视频审核状态
            $t['plate_status'],  //车牌审核结果
            $t['mileage_status'],  //里程表审核结果
            $t['audit_mileage_num'],  //里程表审核数字
            $t['carriage_type'],  //车厢审核结果
            $t['audit_time'],  //审核时间（格式：YYYY-MM-DD HH:MM:SS)
            $t['audit_people'],  //审核人（格式：（工号)姓名)
        ];

        foreach ($list as $key => $value) {
            $new_data[] = [
                $value['staff_info_id'],
                $value['name'],
                $value['state_text'],
                $value['job_title_name'],
                $value['region_name'],
                $value['piece_name'],
                $value['store_name'],
                $value['hire_type_text'],
                $value['created_at'],
                $value['operator_name'],
                $value['status_text'],
                $value['submit_time'],
                $value['plate_number'],
                $value['license_location'],
//                $value['vehicle_img'],
//                $value['mileage_img'],
                $value['vehicle_video'],
                $value['report_mileage_time'],
                $value['report_mileage_num'],
                $value['mileage_img'],
                $value['last_carriage_audit_time'],
                $value['last_carriage_type'],
                $value['last_carriage_audit_status'],
                $value['video_status_text'],
                $value['plate_status_text'],
                $value['mileage_status_text'],
                $value['audit_mileage_num'],
                $value['carriage_type_text'],
                $value['audit_time'],
                $value['audit_staff_name'],
            ];
        }

        return $this->exportExcel($header, $new_data, $file_name);
    }

    public function dealImportData($filePath, $operatorId, $fileName, $importId)
    {
        //获取Excel数据
        $excelData = $this->getExcelData($filePath,
            [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,//工号
            ]);
        //去重 保留行号大的数据
        $data = $fileData = $header = $insertData = [];
        foreach (static::yieldData()($excelData) as $key => $datum) {
            //表头
            if($key == 0) {
                $header = $datum;
                $header[1] = 'result';
                continue;
            }

            if (empty($datum) || empty(array_filter($datum))) {
                continue;
            }

            //去除多余空行
            if (empty($datum[0])) {
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);

        $vehicle_check_position = (new SettingEnvService())->getSetVal('vehicle_check_position', ',');
        if (!empty($data)) {
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError, $dataOne] = $this->dealItem($operatorId, $item, $vehicle_check_position);
                $fileData[]   = $item;
                //去重
                if(!empty($dataOne) && !isset($insertData[$dataOne['staff_info_id']])) {
                    $insertData[$dataOne['staff_info_id']] = $dataOne;
                }

                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        if(!empty($insertData)) {
            $staffService = new StaffService();
            $messagesService = new MessagesService();
            $category = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COURIER_VEHICLE_INSPECTION');

            $staffSupportStoreServer = new StaffSupportStoreServer();

            $db = $this->getDI()->get('db_backyard');
            foreach ($insertData as $oneData) {

                $db->insertAsDict('vehicle_inspection', $oneData);
                $id = $db->lastInsertId();

                //获取语言
                $lang    = $staffService->getAcceptLanguage($oneData['staff_info_id']);
                $t       = self::getTranslation($lang);
                $title   = $t->_('vehicle_inspection_message_title');//车辆信息采集
                $message_param = [
                    'id'              => time() . $oneData['staff_info_id'] . rand(1000000, 9999999),
                    'staff_users'     => [$oneData['staff_info_id']],
                    'message_title'   => $title,
                    'message_content' => $id,
                    'category'        => $category,
                ];
                $result = $messagesService->add_kit_message($message_param);

                if ($result[0] != 'ok') {
                    $this->logger->error(['VehicleInspectionTask-import-message-error' => ['params' => $message_param, 'res' => $result]]);
                }

                //主账号，查询子账号，如果有，则发给子账号。
                $supportInfo = $staffSupportStoreServer->getSupportOsStaffInfo($oneData['staff_info_id']);
                if(!empty($supportInfo)) {
                    $message_param = [
                        'id'              => time() . $supportInfo['sub_staff_info_id'] . rand(1000000, 9999999),
                        'staff_users'     => [$supportInfo['sub_staff_info_id']],
                        'message_title'   => $title,
                        'message_content' => $id,
                        'category'        => $category,
                    ];
                    $resultSub = $messagesService->add_kit_message($message_param);

                    if ($resultSub[0] != 'ok') {
                        $this->logger->error(['VehicleInspectionTask-import-message-error-sub' => ['params' => $message_param, 'res' => $result]]);
                    }
                }
            }
        }

        $excel_file = $this->exportExcel($header, $fileData, $fileName);
        $flashOss   = new FlashOss();
        $oss_path   = 'vehicle_inspection_courier' . '/' . $fileName;
        $flashOss->uploadFile($oss_path, $excel_file['data']);
        return ['url' => $oss_path, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    public function dealItem($operatorId, $item, $jobTitleConfig)
    {
        $data = [];
        try {
            $staffId = trim($item[0]);
            if(empty($staffId)) {
                throw new BusinessException(self::$t->_('wrong_staff_id'));//工号错误
            }
            $staffInfo = StaffInfoRepository::getInfoByStaffInfoId($staffId, true);
            //工号不存在，请重新输入
            if(empty($staffInfo)) {
                throw new BusinessException(self::$t->_('staff_info_id_err_1'));
            }

            //该工号不是编制员工，请重新输入
            if($staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
                throw new BusinessException(self::$t->_('staff_is_not_establishment'));
            }

            //该工号不是员工的主账号
            if($staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
                throw new BusinessException(self::$t->_('staff_is_sub'));
            }

            //工号已离职
            if ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN) {
                throw new BusinessException(self::$t->_('staff_leave'));
            }

            //工号的职位不是vehicle_check_position 快递员车辆稽查允许导入的职位id，提示：工号不属于可稽查的快递员职位
            if (!in_array($staffInfo['job_title'], $jobTitleConfig)) {
                throw new BusinessException(self::$t->_('staff_not_inspection_job'));
            }

            $where['staff_info_id'] = $staffId;
            $where['status'] = Enums::SUBMIT_STATUS_PENDING;
            $vehicleInspectionInfo = VehicleInspectionRepository::getOne($where);
            //该工号有待提交的稽查记录
            if(!empty($vehicleInspectionInfo)) {
                throw new BusinessException(self::$t->_('staff_inspection_pending'));
            }
            $operatorInfo = StaffInfoRepository::getInfoByStaffInfoId($operatorId, true);

            $data['staff_info_id']    = $item[0];
            $data['operator_id']      = $operatorId;
            $data['operator_name']    = $operatorInfo['name'];

            $item[1]   = 'success';
            $haveError = false;
        } catch (\Exception $e) {
            $haveError = true;
            $item[1]   = 'fail ' . $e->getMessage();
        }

        return [$item, $haveError, $data];
    }

    /**
     * 导入支援的时候，看看主账号，是否有待提交的任务。有则发给子账号消息
     * @param $staff_info_id
     * @param $sub_staff_info_id
     * @return bool
     */
    public function sendVehicleMessageToSub($staff_info_id, $sub_staff_info_id)
    {
        if(empty($staff_info_id) || empty($sub_staff_info_id)) {
            return false;
        }

        $where['staff_info_id'] = $staff_info_id;
        $where['status'] = Enums::SUBMIT_STATUS_PENDING;
        $vehicleInspectionInfo = VehicleInspectionRepository::getOne($where);
        if(empty($vehicleInspectionInfo)) {
            return false;
        }

        $staffService = new StaffService();
        $messagesService = new MessagesService();
        $category = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COURIER_VEHICLE_INSPECTION');

        //获取语言
        $lang    = $staffService->getAcceptLanguage($staff_info_id);
        $t       = self::getTranslation($lang);
        $title   = $t->_('vehicle_inspection_message_title');//车辆信息采集
        $message_param = [
            'id'              => time() . $sub_staff_info_id . rand(1000000, 9999999),
            'staff_users'     => [$sub_staff_info_id],
            'message_title'   => $title,
            'message_content' => $vehicleInspectionInfo['id'],
            'category'        => $category,
        ];

        $result = $messagesService->add_kit_message($message_param);

        if ($result[0] != 'ok') {
            $this->logger->error(['sendVehicleMessageToSub-message-error' => ['params' => $message_param, 'res' => $result]]);
            return false;
        }

        return true;
    }

    /**
     * 给支援子账号，发送消息
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function sendVehicleMessageToSubRpc($local, $params)
    {
        if (empty($params['staff_info_id'])) {
            throw new ValidationException("staff_info_id is not empty");

        }

        if (empty($params['sub_staff_info_id'])) {
            throw new ValidationException("sub_staff_info_id is not empty");

        }

        $res = $this->sendVehicleMessageToSub($params['staff_info_id'], $params['sub_staff_info_id']);
        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => $res,
        ];
    }

    /**
     * 枚举
     * @return mixed
     */
    public function getSysInfo()
    {
        $video_status = [];
        foreach (VehicleInspectionModel::$video_status_text as $key => $value) {
            $video_status[] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }

        $plate_status = [];
        foreach (VehicleInspectionModel::$plate_status_text as $key => $value) {
            $plate_status[] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }

        $mileage_status = [];
        foreach (VehicleInspectionModel::$mileage_status_text as $key => $value) {
            $mileage_status[] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }

        $carriage_type = [];
        foreach (VehicleInspectionModel::$carriage_type_text as $key => $value) {
            $carriage_type[] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }

        $data['video_status'] = $video_status;
        $data['plate_status'] = $plate_status;
        $data['mileage_status'] = $mileage_status;
        $data['carriage_type'] = $carriage_type;

        return $data;
    }

    /**
     * 审核提交
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function submit($params)
    {
        $info = VehicleInspectionRepository::getOne(['id' => $params['id']]);

        if (empty($info)) {
            throw new BusinessException('not found vehicle inspection ' . self::$t->_('data_error'));
        }

        if ($info['video_status'] != VehicleInspectionModel::VIDEO_STATUS_PENDING) {
            throw new BusinessException('DATA state is not submit! ' . self::$t->_('data_error'));
        }

        $update_data['video_status']      = $params['video_status'];

        if($params['video_status'] == VehicleInspectionModel::VIDEO_STATUS_PASS) {
            $update_data['plate_status']      = $params['plate_status'];
            $update_data['mileage_status']    = $params['mileage_status'];
            $update_data['carriage_type']     = $params['carriage_type'];
            $update_data['audit_mileage_num'] = $params['audit_mileage_num'];
        }
        $update_data['audit_staff_id']    = $params['staff_id'];
        $update_data['audit_staff_name']  = $params['staff_name'];
        $update_data['audit_time']        = date('Y-m-d H:i:s');

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        $update = $db->updateAsDict(
            'vehicle_inspection',
            $update_data,
            [
                "conditions" => 'id = ?',
                'bind'       => [$params['id']],
            ]
        );
        if(!$update) {
            $db->rollback();
            throw new ValidationException(self::$t->_('retry_later'));
        }

        if($params['video_status'] == VehicleInspectionModel::VIDEO_STATUS_REJECT) {
            $insertData['staff_info_id'] = $info['staff_info_id'];
            $insertData['operator_id']   = $params['staff_id'];
            $insertData['operator_name'] = $params['staff_name'];

            $insert_ret = $db->insertAsDict('vehicle_inspection', $insertData);
            if(!$insert_ret) {
                $db->rollback();
                throw new ValidationException(self::$t->_('retry_later'));
            }

            $id = $db->lastInsertId();

            $staffService = new StaffService();
            $messagesService = new MessagesService();
            $category = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COURIER_VEHICLE_INSPECTION');
            //获取语言
            $lang    = $staffService->getAcceptLanguage($info['staff_info_id']);
            $t       = self::getTranslation($lang);
            $title   = $t->_('vehicle_inspection_message_title');//车辆信息采集
            $message_param = [
                'id'              => time() . $info['staff_info_id'] . rand(1000000, 9999999),
                'staff_users'     => [$info['staff_info_id']],
                'message_title'   => $title,
                'message_content' => $id,
                'category'        => $category,
            ];
            $result = $messagesService->add_kit_message($message_param);

            if ($result[0] != 'ok') {
                $this->logger->error(['VehicleInspection-submit-message-error' => ['params' => $message_param, 'res' => $result]]);
                $db->rollback();
                throw new ValidationException(self::$t->_('retry_later'));
            }

            //主账号，查询子账号，如果有，则发给子账号。
            $staffSupportStoreServer = new StaffSupportStoreServer();
            $supportInfo = $staffSupportStoreServer->getSupportOsStaffInfo($info['staff_info_id']);
            if(!empty($supportInfo)) {
                $message_param = [
                    'id'              => time() . $supportInfo['sub_staff_info_id'] . rand(1000000, 9999999),
                    'staff_users'     => [$supportInfo['sub_staff_info_id']],
                    'message_title'   => $title,
                    'message_content' => $id,
                    'category'        => $category,
                ];
                $resultSub = $messagesService->add_kit_message($message_param);

                if ($resultSub[0] != 'ok') {
                    $this->logger->error(['VehicleInspection-submit-message-error-sub' => ['params' => $message_param, 'res' => $result]]);
                    $db->rollback();
                    throw new ValidationException(self::$t->_('retry_later'));
                }
            }
        }
        $db->commit();

        return true;
    }

    public function updateLicenseLocation()
    {
        $provinceInfo = SysProvinceRepository::getList([], ['name', 'code']);
        $provinceInfoToCode = array_column($provinceInfo, 'name', 'code');
        $provinceInfoToCode['TH10000'] = 'เบตง';

        $license_location_url = (new SettingEnvService())->getSetVal('vehicle_info_license_location_url');
        $tmpDir      = sys_get_temp_dir();            // 获取系统的临时目录路径
        $fileName    = basename($license_location_url);         // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;     // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($license_location_url))) {
            echo '无效文件' . PHP_EOL;
            return false;
        }

        $config       = ['path' => dirname($tmpFilePath)];
        $fileRealName = basename($tmpFilePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet()->setSkipRows(1);
        $excelData = $excel->getSheetData();

        foreach (static::yieldData()($excelData) as $item) {
            if (empty($item) || empty(array_filter($item)) || empty($item[0]) || empty($item[1])) {
                continue;
            }

            if (!isset($provinceInfoToCode[$item[1]])) {
                echo '无效code码：' . $item[0] . '-' . $item[1] . PHP_EOL;
                $this->logger->info(['updateLicenseLocation-error' => '无效code码:' . $item[0] . '-' . $item[1]]);
                continue;
            }

            $model = VehicleInfoModel::findFirst([
                'conditions' => 'uid = :staff_id:',
                'bind'       => ['staff_id' => $item[0]],
            ]);

            if(empty($model)) {
                $this->logger->info(['updateLicenseLocation-not-found-staff' => $item[0]]);
                continue;
            }
            $model->license_location = $provinceInfoToCode[$item[1]];
            $model->updated_at = $model->updated_at;
            $model->save();
        }

        echo 'success';
    }


}