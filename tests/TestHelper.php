#!/usr/bin/env php
<?php

use Phalcon\Cli\Console;

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

//为了引入env函数
require BASE_PATH . '/vendor/autoload.php';

ini_set('memory_limit', '-1');

/**
 * 加载环境变量
 */
$dotenv = new Dotenv\Dotenv(BASE_PATH);
$dotenv->load();
/**
 * Include Services
 */
include APP_PATH . '/config/services_cli.php';

// 设置运行环境变量
$runtime = env('runtime', 'dev');

define('RUNTIME', $runtime);
date_default_timezone_set(env('timeZoneGMT', 'Asia/Bangkok'));

/**
 * Get config service for use in inline setup below
 */
$config = $di->get("config");
/**
 * Include Autoloader
 */
include APP_PATH . '/config/loader.php';

$console = new Console($di);

$modules = require APP_PATH . '/config/modules.php';
/**
 * 国家模块
 */
$console->registerModules($modules);

//设置时区
$time_zone_GMT = $config->application->timeZoneGMT;
date_default_timezone_set($time_zone_GMT);

//让task里 可以使用 $this->console->handle
$di->setShared("console", $console);