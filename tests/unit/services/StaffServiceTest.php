<?php

namespace tests\unit\services;

use App\Models\backyard\HrStaffInfoModel;
use App\Services\StaffService;
use App\Library\Validation\ValidationException;

class StaffServiceTest extends UnitTestCase
{
    /**
     * @var StaffService
     */
    protected $service;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new StaffService();
    }

    /**
     * 测试校验企业邮箱 - 邮箱为空
     * @return void
     */
    public function testCheckEmailWithEmptyEmail()
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('email_is_error');

        $params = ['email' => ''];
        $this->service->checkEmail($params);
    }

    /**
     * 测试校验企业邮箱 - 邮箱未被使用
     * @return void
     */
    public function testCheckEmailWithUnusedEmail()
    {
        $params = ['email' => '<EMAIL>'];
        
        // Mock HrStaffInfoRepository::getHrStaffInfoByEmail
        $this->mockGetHrStaffInfoByEmail($params['email'], null);

        $result = $this->service->checkEmail($params);
        $this->assertEmpty($result);
    }

    /**
     * 测试校验企业邮箱 - 邮箱被在职员工使用
     * @return void
     */
    public function testCheckEmailWithUsedEmail()
    {
        $params = ['email' => '<EMAIL>'];
        $staffInfo = [
            'staff_info_id' => '12345',
            'name' => '张三',
            'state' => 1 // 在职状态
        ];
        
        // Mock HrStaffInfoRepository::getHrStaffInfoByEmail
        $this->mockGetHrStaffInfoByEmail($params['email'], $staffInfo);

        $result = $this->service->checkEmail($params);
        $this->assertNotEmpty($result);
        $this->assertTrue(strpos($result, $staffInfo['staff_info_id']) !== false);
        $this->assertTrue(strpos($result, $staffInfo['name']) !== false);
    }

    /**
     * Mock HrStaffInfoRepository::getHrStaffInfoByEmail
     * @param string $email
     * @param array|null $returnData
     */
    protected function mockGetHrStaffInfoByEmail($email, $returnData)
    {
        $model = $this->createMock(HrStaffInfoModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                'conditions' => 'email = :email: AND state != :state:',
                'bind' => [
                    'email' => $email,
                    'state' => 2 // 离职状态
                ]
            ])
            ->willReturn($returnData ? (object)$returnData : null);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        $this->service = null;
    }
} 