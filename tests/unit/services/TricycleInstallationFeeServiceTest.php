<?php

namespace tests\unit\services;

use App\Services\TricycleInstallationFeeService;

/**
 * TricycleInstallationFeeAllowanceService单元测试
 */
class TricycleInstallationFeeServiceTest extends UnitTestCase
{
    /**
     * @var TricycleInstallationFeeService
     */
    private $service;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new TricycleInstallationFeeService();
    }

    /**
     * 测试员工连续担任三轮车快递员职位的首次时间
     * @return void
     */
    public function testStaffsContinueFirst()
    {
        // 准备测试数据
        $findSolidMap = [
            1001 => [
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-15',
                ],
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-16',
                    'end_time'   => '2024-01-31',
                ],
            ],
            1002 => [
                [
                    'job_title'  => 300, // 非三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-10',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2024-01-11',
                    'end_time'   => '2024-01-20',
                ],
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-21',
                    'end_time'   => '2024-05-30',
                ],
            ],
            1003 => [
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-31',
                ],
            ],
            1004 => [
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-05',
                    'end_time'   => '2024-01-15',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2024-01-16',
                    'end_time'   => '2024-01-20',
                ],
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-21',
                    'end_time'   => '2024-01-31',
                ],
            ],
            1005 => [
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-10',
                ],
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-11',
                    'end_time'   => '2024-01-12',
                ],
                [
                    'job_title'  => 1000,
                    'start_time' => '2024-01-13',
                    'end_time'   => '2024-01-20',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2024-01-21',
                    'end_time'   => '2024-01-31',
                ],
            ],
        ];

        $startDate = '2024-01-01';
        $endDate   = '2024-01-31';

        // 执行测试
        $result = $this->service->staffsContinueFirst($findSolidMap, $startDate, $endDate);

        // 验证结果

        // 验证员工1001的结果 - 连续三轮车快递员职位
        $this->assertArrayHasKey(1001, $result);
        $this->assertEquals(['start' => '2024-01-01'], $result[1001]);

        // 验证员工1002的结果 - 在指定日期范围内首次担任三轮车快递员职位
        $this->assertArrayHasKey(1002, $result);
        $this->assertEquals(['start' => '2024-01-21'], $result[1002]);

        // 验证员工1003的结果 - 没有三轮车快递员职位
        $this->assertArrayNotHasKey(1003, $result);

        // 验证员工1004的结果 - 在指定日期范围内首次担任三轮车快递员职位，但不连续
        $this->assertArrayHasKey(1004, $result);
        $this->assertEquals(['start' => '2024-01-05'], $result[1004]);


        // 验证员工1005的结果 - 连续三轮车快递员职位，但在指定日期范围内不连续
        $this->assertArrayHasKey(1005, $result);
        $this->assertEquals(['start' => '2024-01-01'], $result[1005]);
    }

    /**
     * 测试计算从三轮车快递员日期往后倒推所选月份最后一天，职位不等于三轮车快递员的第一天
     * @return void
     */
    public function testCalTransferDay()
    {
        // 准备测试数据
        $staffSolidMap = [
            [
                'job_title'  => 1000, // 三轮车快递员职位
                'start_time' => '2024-01-01',
                'end_time'   => '2024-01-15',
            ],
            [
                'job_title'  => 2000, // 非三轮车快递员职位
                'start_time' => '2024-01-16',
                'end_time'   => '2024-01-31',
            ],
        ];

        $tricycleDay = '2024-01-01';                // 三轮车快递员日期
        $monthStart  = '2024-01-01';                // 月份开始日期
        $monthEnd    = '2024-01-31';                // 月份结束日期

        // 执行测试
        $result = $this->service->calTransferDay($staffSolidMap, $tricycleDay, $monthStart, $monthEnd);

        // 验证结果
        $this->assertEquals('2024-01-16', $result); // 应该返回非三轮车快递员职位的第一天

        // 测试没有找到非三轮车快递员职位的情况
        $staffSolidMap2 = [
            [
                'job_title'  => 1000, // 三轮车快递员职位
                'start_time' => '2024-01-01',
                'end_time'   => '2024-01-31',
            ],
        ];

        $result2 = $this->service->calTransferDay($staffSolidMap2, $tricycleDay, $monthStart, $monthEnd);
        $this->assertNull($result2);  // 应该返回null，因为没有找到非三轮车快递员职位

        // 测试三轮车快递员日期不在指定范围内的情况
        $tricycleDay2 = '2024-02-01'; // 三轮车快递员日期不在指定月份范围内
        $result3      = $this->service->calTransferDay($staffSolidMap, $tricycleDay2, $monthStart, $monthEnd);
        $this->assertNull($result3); // 应该返回null，因为三轮车快递员日期不在指定范围内
    }

    /**
     * 测试月份1号往前倒推连续Tricycle Courier职位的第一天
     * @return void
     */
    public function testAppointDayTricycleCourierIsFirstDay()
    {
        // 准备测试数据 - 按照结束时间倒序排列
        $findSolidMap = [
            1001 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2024-01-16',
                    'end_time'   => '2024-01-31',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-15',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-12',
                    'end_time'   => '2023-12-31',
                ],
            ],
            1003 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-31',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-31',
                ],
            ],
            1005 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2024-01-11',
                    'end_time'   => '2024-01-20',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-31',
                    'end_time'   => '2024-01-10',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位 不连续
                    'start_time' => '2023-12-20',
                    'end_time'   => '2023-12-28',
                ],
            ],
        ];

        $monthFirstDay = '2024-01-01'; // 月份第一天

        // 执行测试
        $result = $this->service->appointDayTricycleCourierIsFirstDay($findSolidMap, $monthFirstDay);


        // 验证员工1001的结果 - 连续三轮车快递员职位
        $this->assertArrayHasKey(1001, $result);
        $this->assertEquals('2023-12-12', $result[1001]['start']);

        // 验证员工1003的结果 - 整个月份都是三轮车快递员职位
        $this->assertArrayHasKey(1003, $result);
        $this->assertEquals('2023-12-01', $result[1003]['start']);

        // 验证员工1005的结果 - 连续三轮车快递员职位，但在指定日期范围内不连续
        $this->assertArrayHasKey(1005, $result);
        $this->assertEquals('2023-12-31', $result[1005]['start']);
    }

    /**
     * 测试指定日期是指定职位的员工
     * @return void
     */
    public function testAppointDayIsTricycleCourier()
    {
        // 准备测试数据
        $findSolidMap = [
            1001 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2024-01-31',
                ],
            ],
            1002 => [
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-15',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2024-01-16',
                    'end_time'   => '2024-01-31',
                ],
            ],
            1003 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-31',
                ],
            ],
            1004 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2024-01-01',
                    'end_time'   => '2024-01-31',
                ],
            ],
        ];

        $prevMonthLastDay = '2023-12-31';        // 上个月的最后一天
        $monthFirstDay    = '2024-01-01';        // 本月的第一天

        // 执行测试
        $result = $this->service->appointDayIsTricycleCourier($findSolidMap, $prevMonthLastDay, $monthFirstDay);

        // 验证结果
        $this->assertCount(2, $result);          // 应该有2个员工符合条件
        $this->assertContains(1001, $result);    // 员工1001在两个指定日期都是三轮车快递员职位
        $this->assertContains(1004, $result);    // 员工1004在两个指定日期都是三轮车快递员职位
        $this->assertNotContains(1002, $result); // 员工1002在上个月最后一天不是三轮车快递员职位
        $this->assertNotContains(1003, $result); // 员工1003在本月第一天不是三轮车快递员职位
    }

    /**
     * 测试排除从上月最后一天倒推至上月1号职位不能是三轮车>其他职位>三轮车的情况
     * @return void
     */
    public function testAppointRuleIsTricycleCourier()
    {
        // 准备测试数据 - 按照结束时间倒序排列
        $findSolidMap = [
            1001 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-11',
                    'end_time'   => '2023-12-15',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-10',
                ],
            ],
            1002 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-15',
                ],
            ],
            1003 => [
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-15',
                ],
            ],
            1004 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-11',
                    'end_time'   => '2023-12-15',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-10',
                ],
            ],
            // 新增跨月数据
            1005 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-26',
                    'end_time'   => '2024-01-05',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-25',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-15',
                ],
            ],
            // 新增更复杂的职位变化
            1006 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-26',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-21',
                    'end_time'   => '2023-12-25',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-20',
                ],
                [
                    'job_title'  => 2000, // 非三轮车快递员职位
                    'start_time' => '2023-12-11',
                    'end_time'   => '2023-12-15',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-10',
                ],
            ],
            // 新增连续三轮车职位
            1007 => [
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-26',
                    'end_time'   => '2023-12-31',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-21',
                    'end_time'   => '2023-12-25',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-16',
                    'end_time'   => '2023-12-20',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-11',
                    'end_time'   => '2023-12-15',
                ],
                [
                    'job_title'  => 1000, // 三轮车快递员职位
                    'start_time' => '2023-12-01',
                    'end_time'   => '2023-12-10',
                ],
            ],
        ];

        $monthFirstDay = '2023-12-01';    // 上个月第一天
        $prevMonthLastDay = '2023-12-31'; // 上个月最后一天

        // 执行测试
        $result = $this->service->appointRuleIsTricycleCourier($findSolidMap, $monthFirstDay, $prevMonthLastDay);

        // 验证结果
        $this->assertCount(3, $result); // 应该有2个员工被排除
        $this->assertContains(1001, $result); // 员工1001应该被排除，因为职位变化是三轮车>其他>三轮车
        $this->assertContains(1005, $result); // 员工1005应该被排除，因为职位变化是三轮车>其他>三轮车（跨月）
        $this->assertNotContains(1002, $result); // 员工1002不应该被排除，因为职位一直是三轮车
        $this->assertNotContains(1003, $result); // 员工1003不应该被排除，因为职位变化是其他>三轮车
        $this->assertNotContains(1004, $result); // 员工1004不应该被排除，因为职位变化是三轮车>其他>其他
        $this->assertContains(1006, $result); // 员工1006不应该被排除，因为职位变化是三轮车>其他>三轮车>其他>三轮车
        $this->assertNotContains(1007, $result); // 员工1007不应该被排除，因为职位一直是三轮车
    }

}