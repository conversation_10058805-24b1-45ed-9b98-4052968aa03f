<?php

namespace tests\unit\services;

use App\Models\backyard\HrOutsourcingOrderModel;
use App\Models\backyard\HrOutsourcingOrderDetailModel;
use App\Models\backyard\HrShift;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Services\HrStaffService;

class HrStaffServiceTest extends UnitTestCase
{
    /**
     * @var HrStaffService
     */
    protected $service;

    /**
     * @var array 预期的返回字段列表
     */
    protected $expected_keys = [
        'employment_date',
        'shift',
        'sys_store',
        'staff_name',
        'identity',
        'driver_license',
        'bank_no_name',
        'bank_no',
        'content_tel',
        'staff_shift',
        'supervisor_tel',
        'bottom_content'
    ];

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new HrStaffService();
    }

    /**
     * 测试数据提供者
     * @return array
     */
    public function orderDataProvider()
    {
        return [
            'normal_case' => [
                'serial_no' => 'TEST001',
                'identity' => '*********',
                'params' => [
                    'tel' => '**********',
                    'staff_shift' => '早班'
                ],
                'order_data' => [
                    'employment_date' => '2024-03-20',
                    'shift_id' => 1,
                    'store_id' => 100,
                    'job_id' => 1
                ],
                'shift_data' => [
                    'start' => '09:00',
                    'end' => '18:00'
                ],
                'store_data' => [
                    'name' => '测试网点'
                ],
                'order_detail_data' => [
                    'staff_name' => '张三',
                    'identity' => '*********',
                    'driver_license' => 'DL123456',
                    'bank_id' => 1,
                    'bank_no_name' => '张三',
                    'bank_no' => '****************'
                ],
                'supervisor_data' => [
                    'mobile' => '***********',
                    'mobile_company' => '***********'
                ],
                'expected_values' => [
                    'employment_date' => '2024-03-20',
                    'shift' => '09:00-18:00',
                    'sys_store' => '测试网点',
                    'staff_name' => '张三',
                    'identity' => '*********',
                    'driver_license' => 'DL123456',
                    'bank_no_name' => '张三',
                    'bank_no' => '****************',
                    'content_tel' => '**********',
                    'staff_shift' => '早班',
                    'supervisor_tel' => '***********'
                ]
            ],
            'special_chars_case' => [
                'serial_no' => 'TEST002',
                'identity' => '*********',
                'params' => [
                    'tel' => '**********',
                    'staff_shift' => '特殊班次@#$'
                ],
                'order_data' => [
                    'employment_date' => '2024-03-20',
                    'shift_id' => 1,
                    'store_id' => 100,
                    'job_id' => 1
                ],
                'shift_data' => [
                    'start' => '09:00',
                    'end' => '18:00'
                ],
                'store_data' => [
                    'name' => '测试网点@#$'
                ],
                'order_detail_data' => [
                    'staff_name' => '张三@#$',
                    'identity' => '*********',
                    'driver_license' => 'DL123456@#$',
                    'bank_id' => 1,
                    'bank_no_name' => '张三@#$',
                    'bank_no' => '****************'
                ],
                'supervisor_data' => [
                    'mobile' => '***********',
                    'mobile_company' => '***********'
                ],
                'expected_values' => [
                    'employment_date' => '2024-03-20',
                    'shift' => '09:00-18:00',
                    'sys_store' => '测试网点@#$',
                    'staff_name' => '张三@#$',
                    'identity' => '*********',
                    'driver_license' => 'DL123456@#$',
                    'bank_no_name' => '张三@#$',
                    'bank_no' => '****************',
                    'content_tel' => '**********',
                    'staff_shift' => '特殊班次@#$',
                    'supervisor_tel' => '***********'
                ]
            ]
        ];
    }

    /**
     * 测试获取外协工单详情 - 使用数据提供者
     * @dataProvider orderDataProvider
     * @return void
     */
    public function testOutSourceOrderInfoWithDataProvider($serial_no, $identity, $params, $order_data, $shift_data, $store_data, $order_detail_data, $supervisor_data, $expected_values)
    {
        // 参数验证
        $this->assertNotEmpty($serial_no, '工单号不能为空');
        $this->assertNotEmpty($identity, '身份证号不能为空');
        $this->assertTrue(is_array($params), '参数必须是数组');
        $this->assertArrayHasKey('tel', $params, '参数中必须包含电话号码');
        $this->assertArrayHasKey('staff_shift', $params, '参数中必须包含班次信息');

        // 模拟数据库查询
        $this->mockModels($serial_no, $identity, $order_data, $shift_data, $store_data, $order_detail_data, $supervisor_data);

        // 调用测试方法
        $result = $this->service->outSourceOrderInfo($serial_no, $identity, $params);

        // 验证返回值是数组
        $this->assertTrue(is_array($result), '返回值必须是数组');

        // 验证所有必需的键都存在
        foreach ($this->expected_keys as $key) {
            $this->assertArrayHasKey($key, $result, "返回数组中缺少键 {$key}");
        }

        // 验证所有预期值
        foreach ($expected_values as $key => $value) {
            $this->assertEquals($value, $result[$key], "键 {$key} 的值不匹配");
        }

        // 验证数据类型
        $this->assertTrue(is_string($result['employment_date']), '入职日期必须是字符串');
        $this->assertTrue(is_string($result['shift']), '班次必须是字符串');
        $this->assertTrue(is_string($result['sys_store']), '网点必须是字符串');
        $this->assertTrue(is_string($result['staff_name']), '姓名必须是字符串');
        $this->assertTrue(is_string($result['identity']), '身份证号必须是字符串');
        $this->assertRegExp('/^\d{4}-\d{2}-\d{2}$/', $result['employment_date'], '入职日期格式不正确');
    }

    /**
     * 测试获取外协工单详情 - 正常情况
     * @return void
     */
    public function testOutSourceOrderInfo()
    {
        // 准备测试数据
        $serial_no = "TEST001";
        $identity = "*********";
        $params = [
            'tel' => '**********',
            'staff_shift' => '早班'
        ];

        // 模拟订单数据
        $order_data = [
            'employment_date' => '2024-03-20',
            'shift_id' => 1,
            'store_id' => 100,
            'job_id' => 1
        ];

        // 模拟班次数据
        $shift_data = [
            'start' => '09:00',
            'end' => '18:00'
        ];

        // 模拟网点数据
        $store_data = [
            'name' => '测试网点'
        ];

        // 模拟订单详情数据
        $order_detail_data = [
            'staff_name' => '张三',
            'identity' => '*********',
            'driver_license' => 'DL123456',
            'bank_id' => 1,
            'bank_no_name' => '张三',
            'bank_no' => '****************'
        ];

        // 模拟主管信息
        $supervisor_data = [
            'mobile' => '***********',
            'mobile_company' => '***********'
        ];

        // 模拟数据库查询
        $this->mockModels($serial_no, $identity, $order_data, $shift_data, $store_data, $order_detail_data, $supervisor_data);

        // 调用测试方法
        $result = $this->service->outSourceOrderInfo($serial_no, $identity, $params);

        // 验证结果
        $this->assertArrayHasKey('employment_date', $result);
        $this->assertArrayHasKey('shift', $result);
        $this->assertArrayHasKey('sys_store', $result);
        $this->assertArrayHasKey('staff_name', $result);
        $this->assertArrayHasKey('identity', $result);
        $this->assertArrayHasKey('driver_license', $result);
        $this->assertArrayHasKey('bank_no_name', $result);
        $this->assertArrayHasKey('bank_no', $result);
        
        $this->assertEquals('2024-03-20', $result['employment_date']);
        $this->assertEquals('09:00-18:00', $result['shift']);
        $this->assertEquals('测试网点', $result['sys_store']);
        $this->assertEquals('张三', $result['staff_name']);
        $this->assertEquals('*********', $result['identity']);
        $this->assertEquals('DL123456', $result['driver_license']);
        $this->assertEquals('张三', $result['bank_no_name']);
        $this->assertEquals('****************', $result['bank_no']);
    }

    /**
     * 测试获取外协工单详情 - 工单不存在
     * @return void
     */
    public function testOutSourceOrderInfoWithNonExistentOrder()
    {
        // 准备测试数据
        $serial_no = "NONEXISTENT";
        $identity = "*********";
        $params = [
            'tel' => '**********',
            'staff_shift' => '早班'
        ];

        // 模拟订单不存在
        $this->mockOrderModel($serial_no, null);

        // 调用测试方法
        $result = $this->service->outSourceOrderInfo($serial_no, $identity, $params);

        // 验证结果
        $this->assertArrayHasKey('employment_date', $result);
        $this->assertArrayHasKey('shift', $result);
        $this->assertArrayHasKey('sys_store', $result);
        
        $this->assertEmpty($result['employment_date']);
        $this->assertEmpty($result['shift']);
        $this->assertEmpty($result['sys_store']);
    }

    /**
     * 测试获取外协工单详情 - 快递员工单
     * @return void
     */
    public function testOutSourceOrderInfoForCourier()
    {
        // 准备测试数据
        $serial_no = "TEST001";
        $identity = "*********";
        $params = [
            'tel' => '**********',
            'staff_shift' => '早班'
        ];

        // 模拟订单数据(快递员)
        $order_data = [
            'employment_date' => '2024-03-20',
            'shift_id' => 1,
            'store_id' => 100,
            'job_id' => 16 // 快递员职位ID
        ];

        // 模拟数据库查询
        $this->mockOrderModel($serial_no, $order_data);
        $this->mockSettingEnvModel('out_sourcing_courier', '16,17,18');

        // 调用测试方法
        $result = $this->service->outSourceOrderInfo($serial_no, $identity, $params);

        // 验证结果
        $this->assertArrayHasKey('bottom_content', $result);
        $this->assertContains('out_sourcing_sms_content_2', $result['bottom_content']);
    }

    /**
     * 模拟所有相关模型
     */
    protected function mockModels($serial_no, $identity, $order_data, $shift_data, $store_data, $order_detail_data, $supervisor_data)
    {
        $this->mockOrderModel($serial_no, $order_data);
        if ($order_data) {
            $this->mockShiftModel($order_data['shift_id'], $shift_data);
            $this->mockStoreModel($order_data['store_id'], $store_data);
            $this->mockStaffInfoModel($order_data['store_id'], $supervisor_data);
        }
        $this->mockOrderDetailModel($serial_no, $identity, $order_detail_data);
        $this->mockSettingEnvModel('out_sourcing_courier', '16,17,18');
    }

    /**
     * Mock HrOutsourcingOrderModel
     */
    protected function mockOrderModel($serial_no, $data)
    {
        $model = $this->createMock(HrOutsourcingOrderModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                "conditions" => " serial_no = :serial_no: ",
                "bind" => [
                    "serial_no" => $serial_no
                ]
            ])
            ->willReturn($data ? (object)$data : null);
    }

    /**
     * Mock HrShift
     */
    protected function mockShiftModel($shift_id, $data)
    {
        $model = $this->createMock(HrShift::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                "conditions" => "id = :id:",
                "bind" => ["id" => $shift_id]
            ])
            ->willReturn($data ? (object)$data : null);
    }

    /**
     * Mock SysStoreModel
     */
    protected function mockStoreModel($store_id, $data)
    {
        $model = $this->createMock(SysStoreModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                "conditions" => " id = :id: ",
                "bind" => ["id" => $store_id]
            ])
            ->willReturn($data ? (object)$data : null);
    }

    /**
     * Mock HrOutsourcingOrderDetailModel
     */
    protected function mockOrderDetailModel($serial_no, $identity, $data)
    {
        $model = $this->createMock(HrOutsourcingOrderDetailModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                "conditions" => " serial_no = :serial_no: and identity = :identity: ",
                "bind" => [
                    "serial_no" => $serial_no,
                    "identity" => $identity
                ]
            ])
            ->willReturn($data ? (object)$data : null);
    }

    /**
     * Mock HrStaffInfoModel
     */
    protected function mockStaffInfoModel($store_id, $data)
    {
        $model = $this->createMock(HrStaffInfoModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with([
                'columns' => "staff_info_id,name,job_title,sys_store_id,mobile,mobile_company",
                "conditions" => "sys_store_id = :store_id: and job_title = :job_title:",
                "bind" => ["store_id" => $store_id, 'job_title' => '16']
            ])
            ->willReturn($data ? (object)$data : null);
    }

    /**
     * Mock SettingEnvModel
     */
    protected function mockSettingEnvModel($code, $set_value)
    {
        $model = $this->createMock(SettingEnvModel::class);
        $model->expects($this->any())
            ->method('findFirst')
            ->with("code = 'out_sourcing_courier'")
            ->willReturn($set_value ? (object)['set_val' => $set_value] : null);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        $this->service = null;
    }
} 