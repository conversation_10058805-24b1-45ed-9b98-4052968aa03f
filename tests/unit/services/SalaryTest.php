<?php

namespace tests\unit\services;


use App\Modules\Id\Services\Gongzi2024Service;
use App\Modules\Id\Services\GongzicalService;
use unit\services\UnitTestCase;

class SalaryTest extends UnitTestCase
{
    public function testIDJpErConfig()
    {
        $service               = new GongzicalService();
        $service->gongzi_month = '2026-03';
        $config                = $service->findConfigByMonth($service->jp_erMap, $service->gongzi_month);
        $this->assertEquals(10547400, $config['judge']);
        $this->assertEquals(210948, $config['value']);
    }

    /**
     * 数据提供器：包含输入值和预期输出
     */
    public function roundDataProvider(): array
    {
        return [
            // 标准测试用例
            'Exactly integer'          => [20.00, 20],
            'Exactly integer 100'      => [110.00, 110],
            'Decimal exactly 50'       => [20.50, 20],
            'Decimal below 50'         => [20.4999, 20],
            'Decimal just over 50'     => [20.504, 21],
            'Decimal with third digit' => [20.505, 21],
            'Decimal clearly over 50'  => [20.51, 21],
            // 浮点精度边界测试
            'Float precision lower'    => [20.499999, 20],
            'Float precision higher'   => [20.500001, 21],
        ];
    }

    /**
     * @dataProvider roundDataProvider
     * @param float $input
     * @param int $expected
     */
    public function testCustomRound(float $input, int $expected): void
    {
        $service = new Gongzi2024Service();
        $this->assertEquals(
            $expected,
            $service->customRound($input),
            "Failed for input: $input"
        );
    }
}