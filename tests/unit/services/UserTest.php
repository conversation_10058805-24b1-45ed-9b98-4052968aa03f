<?php

namespace tests\unit\services;

use App\Modules\My\Services\StaffService;

class UserTest extends UnitTestCase
{
    public function setUp(): void
    {
        parent::setUp();
    }


    /**
     * 测试列表
     * @return void
     */
    public function testGetList()
    {
        $staffInfo = (new StaffService())->getHrStaffInfo(17245);
        $this->assertArrayHasKey('staff_info_id', $staffInfo);
    }

    /**
     * 测试列表
     * @return void
     */
    public function testGetListUser()
    {
        $staffInfo = (new StaffService())->getHrStaffInfo(22000);
        $this->assertArrayHasKey('staff_info_id', $staffInfo);
    }
}