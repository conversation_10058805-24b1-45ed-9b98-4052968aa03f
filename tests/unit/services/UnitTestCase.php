<?php

namespace tests\unit\services;

use App\Library\BaseService;
use Phalcon\Test\UnitTestCase as PhalconTestCase;


abstract class UnitTestCase extends PhalconTestCase
{
    private $_loaded = false;

    protected $userInfo = [];
    protected $locale   = [];

    public $lang = 'zh-CN'; // 语言变量


    public function setUp()
    {
        BaseService::setLanguage(getCountryDefaultLang());
        $this->_loaded = true;
    }

    public function __destruct()
    {
        if (!$this->_loaded) {

        }
    }

    public function tearDown()
    {
    }
}

